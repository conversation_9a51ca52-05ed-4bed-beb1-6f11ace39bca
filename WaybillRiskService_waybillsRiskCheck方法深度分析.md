# WaybillRiskService.waybillsRiskCheck() 方法深度分析

## 📋 1. 方法概述

### 1.1 方法签名
```java
public CommonResult waybillsRiskCheck(List<Long> waybillIds, String operatorName, Boolean capacityIsChanged, Long serialNumber)
```

### 1.2 方法功能
运单批量风控校验的核心方法，负责对多个运单进行风险检查，包括数据预处理、风险规则校验、结果处理和状态更新。

### 1.3 参数说明
- `waybillIds`: 待校验的运单ID列表
- `operatorName`: 操作人姓名
- `capacityIsChanged`: 运力是否发生变更
- `serialNumber`: 风控流水号，用于标识本次校验批次

## 📊 2. 方法调用层级结构

### 2.1 第一层级调用方法列表

| 序号 | 方法名 | 所属类 | 调用位置 | 功能描述 |
|------|--------|--------|----------|----------|
| 1 | `waybillRiskInfoMapper.selectWaybillRiskInfoByWaybillIds()` | WaybillRiskInfoMapper | 行602 | 查询运单风控信息 |
| 2 | `waybillMapper.selectWaybillByIds()` | WaybillMapper | 行613 | 批量查询运单信息 |
| 3 | `waybillCustomerRelationMapper.selectByWaybillIdsAndWaybillState()` | WaybillCustomerRelationMapper | 行616 | 查询已开票运单 |
| 4 | `restoreOriginalState()` | 当前类 | 行621,640,649,656 | 恢复运单原始风控状态 |
| 5 | `basicRiskRuleService.riskRulesVerify()` | BasicRiskRuleService | 行634 | 执行风险规则校验 |
| 6 | `dealWithWaybillRiskInfo()` | 当前类 | 行647 | 处理风控校验结果 |

### 2.2 第二层级嵌套调用方法列表

#### 2.2.1 restoreOriginalState() 方法内部调用
| 序号 | 方法名 | 功能描述 |
|------|--------|----------|
| 1 | `waybillRiskInfoMapper.selectWaybillRiskInfoByWaybillIds()` | 查询运单风控信息 |
| 2 | `waybillMapper.updateWaybillRiskGradeByWaybillIdsAndGrade()` | 更新运单风控等级 |

#### 2.2.2 dealWithWaybillRiskInfo() 方法内部调用
| 序号 | 方法名 | 功能描述 |
|------|--------|----------|
| 1 | `waybillRiskInfoMapper.selectWaybillRiskInfoByWaybillIds()` | 查询运单风控信息 |
| 2 | `forwarderRiskRuleMapper.selectByFreightForwarderIdOrderByDeepDesc()` | 查询网货风控规则 |
| 3 | `waybillRiskDetailMapper.selectListByWaybillIds()` | 查询风控详情 |
| 4 | `waybillRiskInfoMapper.selectWaybillRiskInfoByWaybillIdsAndIgnoreStates()` | 查询忽略状态风控信息 |
| 5 | `addRiskInfoAndDetailInfo()` | 添加风控信息和详情 |
| 6 | `waybillRiskInfoMapper.insertWaybillRiskInfos()` | 插入风控信息 |
| 7 | `waybillRiskDetailMapper.deleteByWaybillIds()` | 删除风控详情 |
| 8 | `waybillRiskDetailMapper.insertWaybillRiskDetails()` | 插入风控详情 |
| 9 | `waybillMapper.updateWaybillRiskGradeByWaybillIdsAndGrade()` | 更新运单风控等级 |
| 10 | `financeStatementMapper.selectFinanceStatementIdsByWaybillIds()` | 查询发票ID |
| 11 | `financeStatementMapper.getStatementIdAndRiskLevel()` | 获取发票风控等级 |
| 12 | `financeStatementMapper.batchUpdateFinanceStatement()` | 批量更新发票状态 |
| 13 | `riskHandleRecordsService.handleRiskReordLogs()` | 记录风控变更日志 |

#### 2.2.3 addRiskInfoAndDetailInfo() 方法内部调用
| 序号 | 方法名 | 功能描述 |
|------|--------|----------|
| 1 | `dealWithDetailInfo()` | 处理风控详情信息 |

#### 2.2.4 dealWithDetailInfo() 方法内部调用
| 序号 | 方法名 | 功能描述 |
|------|--------|----------|
| 1 | `dealIgnorRiskDetailWhenAddDetail()` | 处理忽略风控详情 |

### 2.3 第三层级及更深层调用

#### 2.3.1 basicRiskRuleService.riskRulesVerify() 内部调用链
这是一个外部服务调用，内部可能包含：
- 风险规则引擎调用
- 数据校验逻辑
- 第三方风控服务接口

#### 2.3.2 riskHandleRecordsService.handleRiskReordLogs() 内部调用链
- 日志记录相关的数据库操作
- 审计信息的处理

## 🔄 3. 完整方法调用关系图

```mermaid
graph TD
    A[waybillsRiskCheck] --> B[数据预处理阶段]
    A --> C[风险校验阶段]
    A --> D[结果处理阶段]
    
    B --> B1[selectWaybillRiskInfoByWaybillIds]
    B --> B2[selectWaybillByIds]
    B --> B3[selectByWaybillIdsAndWaybillState]
    B --> B4[数据过滤和分组]
    
    C --> C1[按网货平台分组]
    C --> C2[riskRulesVerify - 风险规则校验]
    C --> C3[异常处理]
    
    D --> D1[dealWithWaybillRiskInfo]
    D --> D2[restoreOriginalState]
    
    D1 --> D1A[selectWaybillRiskInfoByWaybillIds]
    D1 --> D1B[selectByFreightForwarderIdOrderByDeepDesc]
    D1 --> D1C[selectListByWaybillIds]
    D1 --> D1D[addRiskInfoAndDetailInfo]
    D1 --> D1E[数据库更新操作]
    
    D1D --> D1D1[dealWithDetailInfo]
    D1D1 --> D1D1A[dealIgnorRiskDetailWhenAddDetail]
    
    D2 --> D2A[selectWaybillRiskInfoByWaybillIds]
    D2 --> D2B[updateWaybillRiskGradeByWaybillIdsAndGrade]
    
    classDef main fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef stage fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef method fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef db fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    
    class A main
    class B,C,D stage
    class B4,C1,C3,D1D,D1D1 method
    class B1,B2,B3,C2,D1A,D1B,D1C,D1E,D2A,D2B,D1D1A db
```

## 📝 4. 下一步分析计划

接下来将逐个分析每个方法的详细执行逻辑：

1. **数据预处理阶段方法分析**
2. **风险校验核心方法分析**  
3. **结果处理方法分析**
4. **数据库操作方法分析**
5. **异常处理和状态恢复方法分析**

每个方法将包含：
- 方法签名和参数
- 执行逻辑分析
- 数据库操作详情
- 业务规则说明
- 异常处理机制
- 性能影响分析
