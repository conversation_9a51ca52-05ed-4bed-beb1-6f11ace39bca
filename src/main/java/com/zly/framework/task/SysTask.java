package com.zly.framework.task;

import com.alibaba.fastjson2.JSONObject;
import com.zly.common.exception.ServiceException;
import com.zly.common.utils.CommonUtil;
import com.zly.common.utils.DateUtils;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.StringUtils;
import com.zly.common.utils.TextUtil;
import com.zly.framework.web.domain.CommonResult;
import com.zly.project.carrier.CapacityProcessing.service.CapacityProcessingService;
import com.zly.project.carrier.carCaptain.service.CarCaptainInfoService;
import com.zly.project.carrier.driver.service.IDriverPortraitService;
import com.zly.project.consignor.contract.domain.CustomerContract;
import com.zly.project.consignor.contract.service.ICustomerContractService;
import com.zly.project.consignor.customer.service.ICustomerInfoService;
import com.zly.project.contract.domain.FrameworkContract;
import com.zly.project.contract.service.IFrameworkContractService;
import com.zly.project.customerForwarderContract.service.ICustomerForwarderContractService;
import com.zly.project.es.EsService;
import com.zly.project.es.waybill.service.EsWaybillService;
import com.zly.project.etc.service.EtcInoviceService;
import com.zly.project.finance.statement.service.IFinanceStatementBillService;
import com.zly.project.finance.statement.service.impl.FinanceStatementServiceImpl;
import com.zly.project.financeOperation.service.HuaxiaInvoicePushService;
import com.zly.project.freightforwarder.domain.FreightForwarderInfo;
import com.zly.project.freightforwarder.service.impl.FreightForwarderInfoServiceImpl;
import com.zly.project.group.mapper.WaybillCustomerRelationMapper;
import com.zly.project.group.service.IGroupFrameworkContractService;
import com.zly.project.hxb.service.IHxbCreditEnterpriseService;
import com.zly.project.increment.service.InsuranceService;
import com.zly.project.largeScreen.service.impl.LargeScreenValueServiceImpl;
import com.zly.project.makecode.service.IMakeCodeService;
import com.zly.project.operation.service.IOperationPlainService;
import com.zly.project.proxy.domain.ProxyInvoicesLog;
import com.zly.project.proxy.domain.req.chyProxy.QueryOrderAuditStatusReq;
import com.zly.project.proxy.domain.req.chyProxy.WaybillInvoiceStatusReq;
import com.zly.project.proxy.domain.res.cjhyProxy.WaybillAuditInfo;
import com.zly.project.proxy.domain.res.cjhyProxy.WaybillInvoiceStatusRes;
import com.zly.project.proxy.service.CjhyProxyInvoiceService;
import com.zly.project.proxy.service.ProxyInvoicesLogService;
import com.zly.project.proxy.service.ProxyInvoicesService;
import com.zly.project.riskManagement.service.FreightForwarderRiskRuleService;
import com.zly.project.srms.service.ISrmsWayBillService;
import com.zly.project.statistics.service.PayeeStatisticsService;
import com.zly.project.system.domain.FreightForwarderCapacityRelation;
import com.zly.project.system.mapper.FreightForwarderCapacityRelationMapper;
import com.zly.project.system.service.ISysConfigService;
import com.zly.project.taxUpload.service.YwkUploadService;
import com.zly.project.tenant.service.ITenantUserNoticeService;
import com.zly.project.transport.waybill.domain.DriverElectronicContract;
import com.zly.project.transport.waybill.domain.Waybill;
import com.zly.project.transport.waybill.domain.WaybillProxyInvoice;
import com.zly.project.transport.waybill.mapper.WaybillMapperEx;
import com.zly.project.transport.waybill.mapper.WaybillProxyInvoiceMapper;
import com.zly.project.transport.waybill.service.IWaybillService;
import com.zly.project.transport.waybill.service.PaymentRecordService;
import com.zly.project.transport.waybill.service.WaybillWxService;
import com.zly.project.transport.waybill.service.impl.DriverElectronicContractServiceImpl;
import com.zly.project.transport.waybill.service.impl.WaybillServiceImpl;
import com.zly.project.wlhy.model.UploginkWaybillQuery;
import com.zly.project.wlhy.service.WlhyUploadService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import javax.annotation.Resource;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.security.spec.InvalidKeySpecException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/***
 * 定时任务调度测试**
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Component("sysTask")
public class SysTask {

	private static final Logger log = LoggerFactory.getLogger(SysTask.class);

	@Resource
	private ICustomerInfoService customerInfoService;
	@Resource
	private IWaybillService waybillService;
	@Resource
	private IOperationPlainService operationPlainService;
	@Resource
	private IFrameworkContractService frameworkContractService;
	@Resource
	private ITenantUserNoticeService tenantUserNoticeService;
	@Resource
	private ICustomerContractService customerContractService;
	@Resource
	private ISrmsWayBillService srmsWayBillService;
	@Resource
	private WaybillWxService waybillWxService;
	@Resource
	private WaybillMapperEx waybillMapperEx;
	@Resource
	private IGroupFrameworkContractService groupFrameworkContractService;
	@Resource
	private DriverElectronicContractServiceImpl driverElectronicContractService;
	@Resource
	private InsuranceService insuranceService;
	@Resource
	private IHxbCreditEnterpriseService hxbCreditEnterpriseService;
	@Resource
	private HuaxiaInvoicePushService huaxiaInvoicePushService;
	@Resource
	private IFinanceStatementBillService financeStatementBillService;
	@Resource
	private WlhyUploadService wlhyUploadService;
	@Resource
	private FreightForwarderInfoServiceImpl freightForwarderInfoService;
	@Resource
	private YwkUploadService ywkUploadService;
	@Autowired
	private CarCaptainInfoService carCaptainInfoService;
	@Autowired
	private CapacityProcessingService capacityProcessingService;
	@Resource
	private IMakeCodeService makeCodeService;
	@Resource
	private FreightForwarderRiskRuleService freightForwarderRiskRuleService;
	@Value("${es.password}")
	private String esPassword;
	@Resource
	private EsService esService;
	@Resource
	WaybillCustomerRelationMapper waybillCustomerRelationMapper;
	@Resource
	private WaybillServiceImpl waybillServiceImpl;
	@Resource
	private EsWaybillService esWaybillService;
	@Resource
	private PaymentRecordService paymentRecordService;
	@Resource
	private ICustomerForwarderContractService customerForwarderContractService;
	@Resource
	private IDriverPortraitService driverPortraitService;
	@Resource
	private ISysConfigService iSysConfigService;
	@Resource
	private EtcInoviceService etcInoviceService;
	@Resource
	private CjhyProxyInvoiceService cjhyProxyInvoiceService;
	@Resource
	private WaybillProxyInvoiceMapper waybillProxyInvoiceMapper;
	@Resource
	private ProxyInvoicesLogService proxyInvoicesLogService;

	@Resource
	private PayeeStatisticsService payeeStatisticsService;
	@Resource
	private ProxyInvoicesService proxyInvoicesService;

	public void wxh() {
		log.info("王小珲执行无参方法");
	}

	public void zk(String params) {
		log.info("钟珂执行有参方法：" + params);
	}

	public void wzy(String s, Boolean b, Long l, Double d, Integer i) {
		log.info(StringUtils.format("王增亚执行多参方法： 字符串类型{}，布尔类型{}，长整型{}，浮点型{}，整形{}", s, b, l, d, i));
	}

	/**
	 * 将到期合同中客户的合同状态变更为'已到期'z
	 */
	public void customerInfoContractStateExpired() {
		log.info(DateUtils.getCurrentTime() + "----------------------------定时任务：将到期合同中客户的合同状态变更为'已到期'开始----------------------------");
		try {
			customerInfoService.customerContractStateExpired();
		} catch (Exception e) {
			log.info(DateUtils.getCurrentTime() + "----------------------------定时任务：未有需要更新的客户合同状态=----------------------------");
		}

		log.info(DateUtils.getCurrentTime() + "----------------------------定时任务：将到期合同中客户的合同状态变更为'已到期'结束----------------------------");
	}

	/**
	 * 运单GPS实时定位
	 */
	public void waybillPositioning() {
		log.info(DateUtils.getCurrentTime() + "============================运单GPS实时定位开始============================");
		List<Waybill> waybillList = waybillService.getGpsPositioningWaybillList();
		if (null != waybillList && !waybillList.isEmpty()) {
			for (Waybill waybill : waybillList) {
				if (StringUtils.isNull(waybill)) {
					log.info("【GPS定时任务】运单为空，定位失败，终止定位逻辑");
					continue;
				}
				try {
					waybillService.waybillPositioning(waybill.getId());
				} catch (Exception e) {
					log.error("【GPS定时任务】运单号：{}，运单定位处理异常{}", waybill.getShippingNoteNumber(), ServiceException.getExceptionInfo(e));
				}
			}
		}
		log.info(DateUtils.getCurrentTime() + "============================运单GPS实时定位结束============================");
	}

	/**
	 * 每天生成新的调度计划
	 */
	public void initOperationPlain() {
		log.info(DateUtils.getCurrentTime() + "============================每天生成新的调度计划开始============================");
		operationPlainService.initOperationPlainEveryDay();
		log.info(DateUtils.getCurrentTime() + "============================每天生成新的调度计划结束============================");
	}

	public void frameworkContractStateExpired() {
		log.info(DateUtils.getCurrentTime() + "----------------------------禁用到期 平台合约 开始----------------------------");
		// 1、禁用已到期项目合约 及 3个月(90天)内未有运单的项目
		frameworkContractService.frameworkContractStateExpired(90);
		// 2、查询项目合约列表，找出一星期后将过期的项目合约给予通知
		List<FrameworkContract> contractList = frameworkContractService.frameworkContractStateFromAWeekExpired();
		if (StringUtils.isNotEmpty(contractList)) {
			// 给予通知
			contractList.forEach(c -> tenantUserNoticeService.insertContractNotice(c.getContractName(), c.getEndTime(), c.getTenantId(), 1));
		}
		log.info(DateUtils.getCurrentTime() + "----------------------------禁用到期 平台合约 结束----------------------------");
	}

	public void customerContractStateExpired() {
		log.info(DateUtils.getCurrentTime() + "----------------------------禁用已到期 客户合约 开始----------------------------");
		// 禁用已到期客户合约
		customerContractService.customerContractStateExpired();
		// 查询客户合约列表，找出一星期后将过期的客户合约给予通知
		List<CustomerContract> customerContracts = customerContractService.customerContractStateFromAWeekExpired();
		if (StringUtils.isNotEmpty(customerContracts)) {
			// 给予通知
			customerContracts.forEach(c -> tenantUserNoticeService.insertContractNotice(c.getContractName(), c.getEndTime(), c.getTenantId(), 2));
		}
		log.info(DateUtils.getCurrentTime() + "----------------------------禁用已到期 客户合约 结束----------------------------");
	}

	/**
	 * 短驳系统创建运单定时任务
	 *
	 * @throws InvalidKeySpecException
	 * @throws NoSuchAlgorithmException
	 * @throws SignatureException
	 * @throws ParseException
	 * @throws InvalidKeyException
	 */
	public void getLocation() {
		log.info(DateUtils.getCurrentTime() + "----------------------------短驳系统创建运单开始----------------------------");
		srmsWayBillService.autoCreateWaybill();
		log.info(DateUtils.getCurrentTime() + "----------------------------短驳系统创建运单 结束----------------------------");
	}

	public void changeSubChainsByEffectTime() {
		log.info(DateUtils.getCurrentTime() + "----------------------------更新当天生效的项目转包链条 开始----------------------------");
		groupFrameworkContractService.changeSubChainsByEffectTime();
		log.info(DateUtils.getCurrentTime() + "----------------------------更新当天生效的项目转包链条 结束----------------------------");
	}

	/**
	 * 每天更新司机承运合同 以及委托付款合同
	 */
	public void changeDriverContract() {
		Date now = new Date();
		Date towMonAfter = DateUtils.addMonth(now, 2);
		// 查询生效的数据,变成即将失效
		log.info(DateUtils.getCurrentTime() + "----------------------------将生效合同修改为即将失效 开始----------------------------");
		List<Long> updateIds1 = new ArrayList<>();
		List<DriverElectronicContract> list1 = driverElectronicContractService.queryByState(1);
		for (DriverElectronicContract driverElectronicContract : list1) {
			if (DateUtils.compareDate(towMonAfter, driverElectronicContract.getLoseTime()) == 1) {
				updateIds1.add(driverElectronicContract.getId());
			}
		}
		if (updateIds1.size() > 0) {
			driverElectronicContractService.updateWillExpire(updateIds1);
		}

		// 查询即将失效的数据,变成失效
		log.info(DateUtils.getCurrentTime() + "----------------------------将即将失效合同改为失效 开始----------------------------");
		List<Long> updateIds2 = new ArrayList<>();
		List<DriverElectronicContract> list2 = driverElectronicContractService.queryByState(2);
		for (DriverElectronicContract driverElectronicContract : list2) {
			if (DateUtils.compareDate(now, driverElectronicContract.getLoseTime()) == 1) {
				updateIds2.add(driverElectronicContract.getId());
			}
		}
		// 查询补签数据,变成失效
		log.info(DateUtils.getCurrentTime() + "----------------------------将补签合同修改为失效 开始----------------------------");
		List<DriverElectronicContract> list3 = driverElectronicContractService.queryByState(3);
		for (DriverElectronicContract driverElectronicContract : list3) {
			if (DateUtils.compareDate(now, driverElectronicContract.getLoseTime()) == 1) {
				updateIds2.add(driverElectronicContract.getId());
			}
		}
		if (updateIds2.size() > 0) {
			driverElectronicContractService.updateExpire(updateIds2);
		}

		// 未生效变为生效
		log.info(DateUtils.getCurrentTime() + "----------------------------将未生效合同修改为生效 开始----------------------------");
		List<Long> updateIds3 = new ArrayList<>();
		List<DriverElectronicContract> list4 = driverElectronicContractService.queryByState(0);
		for (DriverElectronicContract driverElectronicContract : list4) {
			if (DateUtils.compareDate(now, driverElectronicContract.getTakeTime()) == 1) {
				updateIds3.add(driverElectronicContract.getId());
			}
		}
		if (updateIds3.size() > 0) {
			driverElectronicContractService.updateTake(updateIds3);
		}

		// 查询生效的数据,变成即将失效
		log.info(DateUtils.getCurrentTime() + "----------------------------将生效合同修改为即将失效 开始----------------------------");
		List<Long> updateIds4 = new ArrayList<>();
		List<FreightForwarderCapacityRelation> list5 = freightForwarderCapacityRelationMapper.queryByState(1);
		for (FreightForwarderCapacityRelation relation : list5) {
			if (DateUtils.compareDate(towMonAfter, DateUtils.addMonth(relation.getSignTime(), 12)) == 1) {
				updateIds4.add(relation.getId());
			}
		}
		if (updateIds4.size() > 0) {
			freightForwarderCapacityRelationMapper.updateWillExpire(updateIds4);
		}

		// 查询即将失效的数据,变成失效
		log.info(DateUtils.getCurrentTime() + "----------------------------将即将失效合同改为失效 开始----------------------------");
		List<Long> updateIds5 = new ArrayList<>();
		List<FreightForwarderCapacityRelation> list6 = freightForwarderCapacityRelationMapper.queryByState(2);
		for (FreightForwarderCapacityRelation relation : list6) {
			if (DateUtils.compareDate(now, DateUtils.addMonth(relation.getSignTime(), 12)) == 1) {
				updateIds5.add(relation.getId());
			}
		}
		// 查询补签数据,变成失效
		log.info(DateUtils.getCurrentTime() + "----------------------------将补签合同修改为失效 开始----------------------------");
		List<FreightForwarderCapacityRelation> list7 = freightForwarderCapacityRelationMapper.queryByState(3);
		for (FreightForwarderCapacityRelation relation : list7) {
			if (DateUtils.compareDate(now, DateUtils.addMonth(relation.getSignTime(), 12)) == 1) {
				updateIds5.add(relation.getId());
			}
		}
		if (updateIds5.size() > 0) {
			freightForwarderCapacityRelationMapper.updateExpire(updateIds5);
		}

		// 未生效变为生效
		log.info(DateUtils.getCurrentTime() + "----------------------------将未生效合同修改为生效 开始----------------------------");
		List<Long> updateIds6 = new ArrayList<>();
		List<FreightForwarderCapacityRelation> list8 = freightForwarderCapacityRelationMapper.queryByState(0);
		for (FreightForwarderCapacityRelation relation : list8) {
			if (DateUtils.compareDate(now, relation.getSignTime()) == 1) {
				updateIds6.add(relation.getId());
			}
		}
		if (updateIds6.size() > 0) {
			freightForwarderCapacityRelationMapper.updateTake(updateIds6);
		}
	}

	@Resource
	private FreightForwarderCapacityRelationMapper freightForwarderCapacityRelationMapper;

	/**
	 * 定时任务获取龙琨投保结果
	 */
	public void lkInsureResult() {
		log.info("----------------------------定时任务获取龙琨投保结果 开始----------------------------");
		insuranceService.lkInsureResultTask();
		log.info("----------------------------定时任务获取龙琨投保结果 结束----------------------------");
	}

	/**
	 * 定时任务同步授信过期状态
	 */
	public void creditExpired() {
		log.info("----------------------------华夏银行数贷通同步过期授信企业的状态 开始----------------------------");
		hxbCreditEnterpriseService.creditExpiredTask();
		log.info("----------------------------华夏银行数贷通同步过期授信企业的状态 结束----------------------------");
	}

	/**
	 * 定时任务--华夏推送失败的发票再次推送
	 */
	public void pushHxFailInvoice() {
		log.info("----------------------------华夏推送失败的发票再次推送 开始----------------------------");
		Integer num = huaxiaInvoicePushService.pushFailInvoiceToHuaxia();
		log.info("----------------------------华夏推送失败的发票再次推送 结束，共推送{}条----------------------------", num);
	}

	/**
	 * 定时任务--拉取尚未获取的百望云发票信息
	 */
	public void getBwyInvoiceInfos() {
		log.info("----------------------------拉取百望云发票信息 开始----------------------------");
		Integer num = financeStatementBillService.getBwyInvoiceInfos();
		log.info("----------------------------拉取百望云发票信息 结束，共拉取{}条----------------------------", num);
	}

	/**
	 * 定时任务--上传网络货运运单
	 */
	public void uplogink() {
		log.info("----------------------------上传网络货运运单 开始----------------------------");
		wlhyUploadService.uplogink(new UploginkWaybillQuery());
		log.info("----------------------------上传网络货运运单 结束----------------------------");
	}

	/**
	 * 定时任务--轨迹校验
	 */
	public void verifyTrajectory() {
		log.info("----------------------------轨迹校验 开始----------------------------");
		waybillService.verifyTrajectory();
		log.info("----------------------------轨迹校验 结束----------------------------");
	}

	/**
	 * 定时任务--网货获取项目信息
	 */
	public void projectInfo() {
		log.info("----------------------------网货获取项目信息 开始----------------------------");
		freightForwarderInfoService.getProjectInfo();
		log.info("----------------------------网货获取项目信息 结束----------------------------");
	}

	/**
	 * 定时任务--上报中五分钟后上报成功
	 */
	public void updateTaxPush() {
		log.info("----------------------------上报中五分钟后上报成功 开始----------------------------");
		ywkUploadService.updateTaxPush();
		log.info("----------------------------上报中五分钟后上报成功 结束----------------------------");
	}

	/**
	 * 定时任务--付款三天运单自动上报
	 */
	public void secondTaxUpload() {
		log.info("----------------------------付款三天运单自动上报 开始----------------------------");
		ywkUploadService.secondTaxUpload();
		log.info("----------------------------付款三天运单自动上报 结束----------------------------");
	}

	/**
	 * 定时任务--发送卸货到达短信
	 */
	public void sendUnloadMsg() {
		log.info("----------------------------发送卸货到达短信 开始----------------------------");
		waybillService.sendUnloadMsg();
		log.info("----------------------------发送卸货到达短信 结束----------------------------");
	}

	/**
	 * 定时任务--小程序认证信息10分钟自动通过
	 */
	public void carCaptainAutomaticReview() {
		log.info("----------------------------小程序车队长认证信息10分钟自动通过 开始----------------------------");
		carCaptainInfoService.carCaptainAutomaticReview();
		log.info("----------------------------小程序车队长认证信息10分钟自动通过 结束----------------------------");
	}

	/**
	 * 定时任务--检查证件过期状态
	 */
	public void checkDocumentExpirationStatus() {
		log.info("----------------------------检查证件过期状态 开始----------------------------");
		capacityProcessingService.checkDocumentExpirationStatus();
		log.info("----------------------------检查证件过期状态 结束----------------------------");
	}

	/**
	 * 定时任务--每5分钟更新货源表生效状态及货源表基础信息
	 */
	public void everyHourUpdateMakeCodeInfo() {
		log.info("----------------------------每小时更新货源表生效状态及货源表基础信息 开始----------------------------");
		makeCodeService.everyHourUpdateMakeCodeInfo();
		log.info("----------------------------每小时更新货源表生效状态及货源表基础信息 结束----------------------------");
	}

	/**
	 * 定时任务--小程序的订单，超过24小时未签约的，置为删除
	 */
	public void logicDeleteWxWaybillByStatusAndOrderTime() {
		log.info("---------------------------- 逻辑删除超过24小时未签约的小程序的订单 开始----------------------------");
		// 0、初始化需更新的运单id合集
		List<Long> expiredWaybillIds = new ArrayList<>();

		// 1、查询出所有接单超过24小时的小程序订单
		List<Waybill> waybills = waybillMapperEx.selectExpiredWaybillByOperationPlain();
		if (CommonUtil.isNullOrEmpty(waybills)) {
			return;
		}
		// 2、 三方货源相关运单（有调度计划），取调度计划时间判断和24小时判断的交集
		List<Long> makecodeWaybillIds = waybillMapperEx.selectWaybillStatusByToday();
		List<Long> operationPlainWaybillIds = waybills.stream().filter(w -> !w.getOperationPlainId().equals(0L)).map(Waybill::getId).collect(Collectors.toList());
		expiredWaybillIds.addAll(CollectionUtils.intersection(makecodeWaybillIds, operationPlainWaybillIds));

		// 3、大宗货源相关运单(无调度计划id)，直接放入待处理运单列表
		expiredWaybillIds.addAll(waybills.stream().filter(w -> w.getOperationPlainId().equals(0L)).map(Waybill::getId).collect(Collectors.toList()));

		// 4、将确认过期的运单逻辑删除
		int num = 0;
		if (StringUtils.isNotEmpty(expiredWaybillIds)) {
			num = waybillMapperEx.logicDeleteWxWaybills(expiredWaybillIds, "定时任务");
		}
		log.info("---------------------------- 逻辑删除超过24小时未签约的小程序的订单{}条， 结束----------------------------", num);
	}

	/**
	 * 查询到预计发车时间的运单,改为自动发车（目前仅善道的数据库有这个定时任务）
	 */
	public void waybillConfirmDeparture() {
		log.info(DateUtils.getCurrentTime() + "=============== =============针对善道数据库的运单进行一些特定的处理，开始============================");
		waybillService.todoConfirmDepartureWaybillList();
		// 善道数据库的运单到预计时间没有发车的运单自动发车
		// waybillServiceImpl.todoConfirmDepartureWaybillList();
		// 查询善道和4.0的运单状态不一样的，重新同步运单
		waybillServiceImpl.todoWaybillResynchronize();
		// 同步善道的es，保险期间，只有善道的服务器才能更新。
		// if (Arrays.asList("jssdzy").contains(SpringUtils.getActiveProfile()) && "Q7HASV#Ykaab2xqG+:z?*aV".equals(esPassword)) {
		// esService.delEsIndex("es_index_customer_waybill_info");
		// WaybillQueryReq queryReq = new WaybillQueryReq();
		// queryReq.setLimit(5000);
		// int total = waybillCustomerRelationMapper.countAll();
		// // 以运单关联表为维度查询，一共43277605条数据， 4328页
		// for (int i = 1; i < total / queryReq.getLimit() + 2; i++) {
		// queryReq.setPage(i);
		// List<ESCustomerWaybillInfo> infos = esService.searchMysqlWaybills(queryReq);
		// esWaybillService.addEsCustomerWaybillInfos(infos);
		// }
		// }
		log.info(DateUtils.getCurrentTime() + "============================针对善道数据库的运单进行一些特定的处理，结束============================");
		log.info(DateUtils.getCurrentTime() + "============================针对善道数据库的运单进行一些特定的处理，结束============================");
	}

	/**
	 * 定时任务--每天0点变更修改风控配置开始
	 */
	public void modifyFreightForwarderRiskRule() {
		log.info("---------------------------- 每天0点变更修改风控配置开始----------------------------");
		freightForwarderRiskRuleService.modifyFreightForwarderRiskRule();
		log.info("---------------------------- 每天0点变更修改风控配置结束----------------------------");
	}

	// 处理承运人附件
	@PostMapping(value = "/test")
	public void test() {
		changeDriverContract();
	}


	/**
	 * 定时任务--每年一月一日定时任务更新收款人500w
	 */
	public void statisticsPaymentRecordTask() {
		log.info("---------------------------- 每年一月一日定时任务更新收款人500w----------------------------");
		paymentRecordService.statisticsPaymentRecordTask();
		log.info("---------------------------- 每年一月一日定时任务更新收款人500w----------------------------");
	}

	/**
	 * 定时任务--每年一月一日定时任务更新收款人500w
	 */
	public void statisticsHomePageTask() {
		log.info("---------------------------- 每天晚上2点更新首页数据----------------------------");
		freightForwarderInfoService.statisticsHomePageTask();
		log.info("---------------------------- 每天晚上2点更新首页数据----------------------------");
	}

	/**
	 * 定时任务--符合风控条件，但未风控的运单，15分钟自动触发风控
	 */
	public void checkWaybillRisks() {
		log.info("----------------------------符合风控条件但未风控的运单，自动触发风控 开始----------------------------");
		freightForwarderRiskRuleService.checkForwarderWaybillRisks(null);
		log.info("----------------------------符合风控条件但未风控的运单，自动触发风控 结束----------------------------");
	}

	/**
	 * 定时任务--委托合同是否生效处理，根据结束时间修正是否生效状态
	 */
	public void checkEntrustmentContract() {
		log.info("----------------------------委托合同生效状态修正 开始----------------------------");
		customerForwarderContractService.checkEntrustmentContract();
		log.info("----------------------------委托合同生效状态修正 结束----------------------------");
	}

	/**
	 * 执行每日统计任务  每天凌晨1点执行
	 */
	public void executeStatisticsTask() {
		log.info("----------------------------统计收款人信息 开始----------------------------");
		// 检查payee_statistics表中是否存在数据
		if (payeeStatisticsService.hasStatisticsData()) {
			log.info("payee_statistics表中已存在数据，执行每日更新方法");
			payeeStatisticsService.executeStatisticsTask();
		} else {
			log.info("payee_statistics表中不存在数据，执行全量更新方法");
			payeeStatisticsService.executeFullStatistics();
		}
		log.info("----------------------------统计收款人信息 结束----------------------------");
	}

	/**
	 * 获取所有网络货运平台司机数据
	 */
	public void syncAllFreightForwarderDrivers()
	{
		long startTime = System.currentTimeMillis(); // 记录开始时间
		log.info("开始执行定时任务：获取所有网络货运平台司机数据");
		try {
			driverPortraitService.getAllFreightForwarderDrivers();
			long endTime = System.currentTimeMillis(); // 记录结束时间
			long duration = endTime - startTime; // 计算耗时
			log.info("定时任务执行成功：获取所有网络货运平台司机数据，耗时：{} ms", duration);
		} catch (Exception e) {
			long endTime = System.currentTimeMillis(); // 记录结束时间 (异常情况)
			long duration = endTime - startTime; // 计算耗时 (异常情况)
			log.error("定时任务执行失败：获取所有网络货运平台司机数据，耗时：{} ms", duration, e);
		}
	}

	@Resource
	private LargeScreenValueServiceImpl largeScreenValueService;

	/**
	 * 每日数据增加
	 */
	public void addDataEveryday() {
		try {
			largeScreenValueService.addDataEveryday();
			log.info("定时任务执行成功：每日数据增加");
		} catch (Exception e) {
			log.error("定时任务执行失败：每日数据增加", e);
		}
	}

	/**
	 * 拉取运单etc发票数据
	 */
	public void getEtcInvoice() {
		long startTime = System.currentTimeMillis(); // 记录开始时间
		log.info("开始执行定时任务：拉取运单etc发票数据");
		FreightForwarderInfo search = new FreightForwarderInfo();
		search.setName("江苏善道智运科技有限公司");
		List<FreightForwarderInfo> freightForwarderInfos = freightForwarderInfoService.selectFreightForwarderInfoList(search);
		if (CommonUtil.isNullOrEmpty(freightForwarderInfos)) {
			log.info("定时任务执行成功：没有需要拉取的网货平台数据");
			return;
		}
		Long forwarderId = freightForwarderInfos.get(0).getId();
		try {
			// 查询可开etc票的运单信息，其中的status被借用，实际为upetcInvoiceStatus的值
			List<Waybill> waybills = waybillMapperEx.selectEtcWaybill();
			for (Waybill waybill : waybills) {
				etcInoviceService.getEtcInvoice(waybill);
			}
			// etc未入网的直接将开票状态更新
			waybillMapperEx.updateETCInvoiceStatusByEtcStatus(forwarderId);
			long endTime = System.currentTimeMillis();
			long duration = endTime - startTime;
			log.info("定时任务执行成功：拉取运单etc发票数据，耗时：{} ms", duration);
		} catch (Exception e) {
			long endTime = System.currentTimeMillis();
			long duration = endTime - startTime;
			log.error("定时任务执行失败：拉取运单etc发票数据，耗时：{} ms", duration, e);
		}
	}


	/**
	 * 获取所有代开审批结果
	 */
	public void cjhyGetWaybillAuditStatus() {
		long startTime = System.currentTimeMillis(); // 记录开始时间
		log.info("开始执行定时任务：获取所有代开审批结果");
		try {
			// 查询所有申请中
			// List<String> shipperNoteNumbers = Arrays.asList("T2506171651187110033","T2506201417405570041");
			List<String> shipperNoteNumbers = waybillProxyInvoiceMapper.selectShipperNoteNumbers(4);
			if (shipperNoteNumbers == null || shipperNoteNumbers.size() == 0) {
				log.info("定时任务执行成功：未查询到审核中运单");
				return;
			}
			List<QueryOrderAuditStatusReq.WaybillInfo> waybillInfoList = new ArrayList<>();

			for (String shipperNoteNumber : shipperNoteNumbers) {
				QueryOrderAuditStatusReq.WaybillInfo waybillInfo = new QueryOrderAuditStatusReq.WaybillInfo();
				waybillInfo.setWaybillNo(shipperNoteNumber);
				waybillInfoList.add(waybillInfo);
			}

			QueryOrderAuditStatusReq query = new QueryOrderAuditStatusReq();
			query.setWaybillList(waybillInfoList);

			CommonResult<List<WaybillAuditInfo>> waybillAuditStatus = cjhyProxyInvoiceService.getWaybillAuditStatus(query);

			for (WaybillAuditInfo req : waybillAuditStatus.getData()) {
				Long waybillId = waybillMapperEx.selectIdByShippingNoteNumber(req.getWaybillNo());

				Waybill updateWaybill = new Waybill();
				updateWaybill.setId(waybillId);

				WaybillProxyInvoice updateWaybillProxyInvoice = new WaybillProxyInvoice();
				updateWaybillProxyInvoice.setWaybillId(waybillId);

				if ("1".equals(req.getStatus())) {
					updateWaybill.setProxyInvoiceState(6);
					updateWaybillProxyInvoice.setProxyInvoiceState(6);
				} else if ("2".equals(req.getStatus())) {
					updateWaybill.setProxyInvoiceState(5);
					updateWaybillProxyInvoice.setProxyInvoiceState(5);
					updateWaybillProxyInvoice.setRefuseReason(req.getAuditMsg());
				} else {
					updateWaybill.setProxyInvoiceState(4);
					updateWaybillProxyInvoice.setProxyInvoiceState(4);
				}

				waybillService.updateWaybill(updateWaybill);
				waybillProxyInvoiceMapper.updateWaybillProxyInvoiceByWaybillId(updateWaybillProxyInvoice);

				if (updateWaybill.getProxyInvoiceState() != 4) {
					ProxyInvoicesLog log = new ProxyInvoicesLog();
					log.setId(TextUtil.getTimeSequenceID(5));
					log.setShippingNoteNumber(req.getWaybillNo());
					log.setActionType(updateWaybill.getProxyInvoiceState());
					log.setParameter(JSONObject.toJSONString(req));
					log.setCreateBy(SecurityUtils.getNickname());
					log.setCreateTime(new Date());
					proxyInvoicesLogService.insterLog(log);
				}
			}

			log.info("定时任务执行成功：获取所有代开审批结果");
		} catch (Exception e) {
			log.error("定时任务执行失败：获取所有代开审批结果", e);
		}
	}

	/**
	 * 获取所有代开发票结果
	 */
	public void cjhyGetWaybillBill() {
		log.info("开始执行定时任务：获取所有代开发票结果");
		try {
			// 查询所有申请中
			// List<String> shipperNoteNumbers = Arrays.asList("T2506171651187110033","T2506201417405570041");
			List<String> shipperNoteNumbers = waybillProxyInvoiceMapper.selectShipperNoteNumbers(6);

			if (shipperNoteNumbers == null || shipperNoteNumbers.size() == 0){
				log.info("定时任务执行成功：未查询到运单");
				return;
			}

			List<WaybillInvoiceStatusReq.WaybillInfo> waybillInfoList = new ArrayList<>();

			for (String shipperNoteNumber : shipperNoteNumbers) {
				WaybillInvoiceStatusReq.WaybillInfo waybillInfo = new WaybillInvoiceStatusReq.WaybillInfo();
				waybillInfo.setWaybillNo(shipperNoteNumber);
				waybillInfoList.add(waybillInfo);
			}

			WaybillInvoiceStatusReq query = new WaybillInvoiceStatusReq();
			query.setWaybillList(waybillInfoList);

			CommonResult<List<WaybillInvoiceStatusRes>> waybillInvoiceStatus = cjhyProxyInvoiceService.getWaybillInvoiceStatus(query);

			for (WaybillInvoiceStatusRes req : waybillInvoiceStatus.getData()) {
				proxyInvoicesService.bill(req);

				ProxyInvoicesLog log = new ProxyInvoicesLog();
				log.setId(TextUtil.getTimeSequenceID(5));
				log.setShippingNoteNumber(req.getWaybillNo());
				log.setActionType(7);
				log.setParameter(JSONObject.toJSONString(req));
				log.setCreateBy(SecurityUtils.getNickname());
				log.setCreateTime(new Date());
				proxyInvoicesLogService.insterLog(log);
			}
			log.info("定时任务执行成功：获取所有代开发票结果");
		} catch (Exception e) {
			log.error("定时任务执行失败：获取所有代开发票结果", e);
		}
	}

	@Resource
	private FinanceStatementServiceImpl financeStatementService;

	/**
	 * 获取所有代开发票结果
	 */
	public void financeStatementPayQuery() {
		log.info("开始执行定时任务：查询申请开票付款中返回结果");

		financeStatementService.financeStatementPayQuery();
	}
}
