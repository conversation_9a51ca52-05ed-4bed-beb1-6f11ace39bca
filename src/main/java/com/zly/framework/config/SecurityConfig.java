package com.zly.framework.config;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.configurers.ExpressionUrlAuthorizationConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.logout.LogoutFilter;
import org.springframework.web.filter.CorsFilter;

import com.zly.framework.config.properties.PermitAllUrlProperties;
import com.zly.framework.config.spring.security.MyDaoAuthenticationProvider;
import com.zly.framework.security.filter.JwtAuthenticationTokenFilter;
import com.zly.framework.security.handle.AuthenticationEntryPointImpl;
import com.zly.framework.security.handle.LogoutSuccessHandlerImpl;
import com.zly.framework.security.service.MyUserDetailsService;

/**
 * spring security配置
 *
 * <AUTHOR>
 */
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
public class SecurityConfig extends WebSecurityConfigurerAdapter {
	/**
	 * 自定义用户认证逻辑
	 */
	@Autowired
	private MyUserDetailsService myUserDetailsService;

	/**
	 * 认证失败处理类
	 */
	@Autowired
	private AuthenticationEntryPointImpl unauthorizedHandler;

	/**
	 * 退出处理类
	 */
	@Autowired
	private LogoutSuccessHandlerImpl logoutSuccessHandler;

	/**
	 * token认证过滤器
	 */
	@Autowired
	private JwtAuthenticationTokenFilter authenticationTokenFilter;

	/**
	 * 跨域过滤器
	 */
	@Autowired
	private CorsFilter corsFilter;

	/**
	 * 允许匿名访问的地址
	 */
	@Autowired
	private PermitAllUrlProperties permitAllUrl;

	/**
	 * 解决 无法直接注入 AuthenticationManager
	 *
	 * @return
	 * @throws Exception
	 */
	@Bean
	@Override
	public AuthenticationManager authenticationManagerBean() throws Exception {
		return super.authenticationManagerBean();
	}

	/**
	 * anyRequest | 匹配所有请求路径 <br/>
	 * access | SpringEl表达式结果为true时可以访问 <br/>
	 * anonymous | 匿名可以访问 <br/>
	 * denyAll | 用户不能访问 <br/>
	 * fullyAuthenticated | 用户完全认证可以访问（非remember-me下自动登录） <br/>
	 * hasAnyAuthority | 如果有参数，参数表示权限，则其中任何一个权限可以访问<br/>
	 * hasAnyRole | 如果有参数，参数表示角色，则其中任何一个角色可以访问 <br/>
	 * hasAuthority | 如果有参数，参数表示权限，则其权限可以访问 <br/>
	 * hasIpAddress | 如果有参数，参数表示IP地址，如果用户IP和参数匹配，则可以访问 <br/>
	 * hasRole | 如果有参数，参数表示角色，则其角色可以访问 <br/>
	 * permitAll | 用户可以任意访问 <br/>
	 * rememberMe | 允许通过remember-me登录的用户访问 <br/>
	 * authenticated | 用户登录后可访问 <br/>
	 */
	@Override
	protected void configure(HttpSecurity httpSecurity) throws Exception {
		// 注解标记允许匿名访问的url
		ExpressionUrlAuthorizationConfigurer<HttpSecurity>.ExpressionInterceptUrlRegistry registry = httpSecurity.authorizeRequests();
		permitAllUrl.getUrls().forEach(url -> registry.antMatchers(url).permitAll());

		// 允许匿名访问的 uri 列表
		List<String> patterns = new ArrayList<>();
		patterns.add("/login");
		patterns.add("/tenant/login");
		patterns.add("/tenant/push/login");
		patterns.add("/group/login");
		patterns.add("/agent/login");
		patterns.add("/finance/login");
		patterns.add("/background/login");
		patterns.add("/register");
		patterns.add("/captchaImage");
		patterns.add("/vx/myCenter/vx/quicklogin/cargoowner");
		patterns.add("/vx/myCenter/vx/quicklogin");
		patterns.add("/vx/myCenter/bycode/selectOpendId");
		patterns.add("/vx/myCenter/bycode/customer/selectOpendId");
		patterns.add("/ali/notify");
		patterns.add("/websocket/**");
		patterns.add("/system/authorization/getSystemInfo");
		patterns.add("/app/myCenter/login/**");
		patterns.add("/system/personalize/list");
		patterns.add("/quickRecording/login");
		patterns.add("/quickRecording/login/byVcode");
		patterns.add("/dd/callback");
		patterns.add("/carrier/vehicle/handleCode/{id}");
		patterns.add("/common/export/types");
		patterns.add("/freightForwarder/info/domain");
		patterns.add("/freightForwarderCustomer/add");
		patterns.add("/common/uploadToCloud");
		patterns.add("/vx/myCenter/quicklogin/customer");
		patterns.add("/vx/myCenter/vx/testlogin/**");
		patterns.add("/whyzt/transportnotice");
		patterns.add("/make/code/handle/address");
		patterns.add("/tenant/user/tenantUser/messagesend");
		patterns.add("/tenant/user/getEnterpriseInfo/**");
		patterns.add("/system/user/getEnterpriseInfo/**");
		patterns.add("/finance/user/getEnterpriseInfo/**");
		patterns.add("/agent/user/getEnterpriseInfo/**");
		patterns.add("/vx/myCenter/quicklogin/getCustomerId");
		patterns.add("/tenant/user/customer/forgotPassword");
		patterns.add("/system/user/forgotPassword");
		patterns.add("/finance/user/forgotPassword");
		patterns.add("/agent/user/forgotPassword");
		patterns.add("/group/user/forgotPassword");
		patterns.add("/es/count/{type}");
		patterns.add("/sign-contract");
		patterns.add("/transport/waybill/makeElectronicContract");
		patterns.add("/sign/contract/**");
		patterns.add("/common/ocr/recognitionFree");
		patterns.add("/common/qiniu/getUploadToken");
		patterns.add("/tool/gen/importTable");
		patterns.add("/tool/gen/download/**");
		patterns.add("/consignor/customer/productAdd");
		patterns.add("/group/customer/reviewedList");
		patterns.add("/common/areas/getTreeChinaProvincesCitiesAreasListNoVerification");
		patterns.add("/common/resolveAddressIpLimit");
		patterns.add("/allSystem/onlineSynchronousData/disposeAddress");
		patterns.add("/ywk/callback");
		patterns.add("/shandao/synchronousData/full");
		patterns.add("/shandao/shanDaoBigScreen");
		patterns.add("/freightForwarderPreferences/info");
		patterns.add("/vx/actual/carrier/center/eidtPhoneNumberCheck");
		patterns.add("/vx/actual/carrier/center/editPhoneNumber");
		patterns.add("/vx/actual/carrier/center/verifyMobileAndSendCodeWithOutLogin");
		patterns.add("/driverFamily/detail/encode/{retaionIdStr}");
		patterns.add("/driverFamily/protocal/preview/partyB");
		patterns.add("/driverFamily/protocal/sign/partyB");
		patterns.add("/finance/config/info");
		patterns.add("/driverFamily/protocol/detail/{relationId}");
		patterns.add("/screen/name/**");
		patterns.add("/screen/value/waybillList/{limit}");
		patterns.add("/screen/value/login");
		patterns.add("/customer/config/info");
		patterns.add("/carrier/driver/portrait/driverPortraits");
		patterns.add("/proxy/needSignByOutSide/{waybillId}");
		patterns.add("/sign/contract/getWaybillIdByUrl/{url}");
		patterns.add("/proxy/getOutSideSignUrl/pc");
		patterns.add("/proxy/getTransportAgreeOutSideSignState/{waybillId}");
		patterns.add("/proxy/getProxyProtocolOutSideSignState/{identityCard}");
		//patterns.add("/capacityProcessing/**");
		// 支付结果的请求接口
		patterns.add("/pds/*");

		httpSecurity
				// CSRF禁用，因为不使用session
				.csrf().disable()
				// 认证失败处理类
				.exceptionHandling().authenticationEntryPoint(unauthorizedHandler).and()
				// 基于token，所以不需要session
				.sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and()
				// 过滤请求
				.authorizeRequests()
				// 对于登录login 注册register 验证码captchaImage 获取服务器硬件编码 允许匿名访问
				.antMatchers(patterns.toArray(new String[0])).permitAll()
				// 静态资源，可匿名访问
				.antMatchers(HttpMethod.GET, "/", "/*.html", "/**/*.html", "/**/*.css", "/**/*.js", "/profile/**").permitAll()
				// 内部接口可直接访问
				.antMatchers( "/swagger-ui.html", "/swagger-resources/**", "/webjars/**", "/*/api-docs", "/druid/**").permitAll()
				// 除上面外的所有请求全部需要鉴权认证
				.anyRequest().authenticated().and().headers().frameOptions().disable();
		// 添加Logout filter
		httpSecurity.logout().logoutUrl("/logout").logoutSuccessHandler(logoutSuccessHandler);
		// 添加JWT filter
		httpSecurity.addFilterBefore(authenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);
		// 添加CORS filter
		httpSecurity.addFilterBefore(corsFilter, JwtAuthenticationTokenFilter.class);
		httpSecurity.addFilterBefore(corsFilter, LogoutFilter.class);
	}

	/**
	 * 强散列哈希加密实现
	 */
	@Bean
	public BCryptPasswordEncoder bCryptPasswordEncoder() {
		return new BCryptPasswordEncoder();
	}

	// /**
	// * 身份认证接口
	// */
	// @Override
	// protected void configure(AuthenticationManagerBuilder auth) throws Exception {
	// auth.userDetailsService(userDetailsService).passwordEncoder(bCryptPasswordEncoder());
	// }

	/**
	 * 设置登录实际处理类，及密码加密方式
	 */
	@Bean(name = "myAuthenticationProvider")
	public AuthenticationProvider myAuthenticationProvider() {
		MyDaoAuthenticationProvider customAuthenticationProvider = new MyDaoAuthenticationProvider();
		customAuthenticationProvider.setUserDetailsService(myUserDetailsService);
		customAuthenticationProvider.setHideUserNotFoundExceptions(false);
		customAuthenticationProvider.setPasswordEncoder(bCryptPasswordEncoder());
		return customAuthenticationProvider;
	}

	/**
	 * 身份认证接口
	 */
	@Override
	protected void configure(AuthenticationManagerBuilder auth) {
		auth.authenticationProvider(myAuthenticationProvider());
	}

}
