package com.zly.framework.mq;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zly.common.utils.DateUtils;
import com.zly.framework.web.domain.CommonResult;
import com.zly.project.backgroundOperation.domain.req.SynchronousShanDaoReq;
import com.zly.project.backgroundOperation.service.FourDataHandlerService;
import com.zly.project.carrier.batchUpload.domain.BatchUploadOfDocumentsRecord;
import com.zly.project.carrier.batchUpload.domain.BatchUploadOfReceiptRecord;
import com.zly.project.carrier.batchUpload.domain.BatchUploadOfShippingAgreementsRecord;
import com.zly.project.carrier.batchUpload.domain.req.UploadDocumentsReq;
import com.zly.project.carrier.batchUpload.domain.req.UploadReceiptReq;
import com.zly.project.carrier.batchUpload.domain.req.UploadShippingAgreementsReq;
import com.zly.project.carrier.batchUpload.mapper.BatchUploadOfDocumentsPicRecordMapper;
import com.zly.project.carrier.batchUpload.mapper.BatchUploadOfDocumentsRecordMapper;
import com.zly.project.carrier.batchUpload.mapper.BatchUploadOfReceiptPicRecordMapper;
import com.zly.project.carrier.batchUpload.mapper.BatchUploadOfReceiptRecordMapper;
import com.zly.project.carrier.batchUpload.mapper.BatchUploadOfShippingAgreementsPicRecordMapper;
import com.zly.project.carrier.batchUpload.mapper.BatchUploadOfShippingAgreementsRecordMapper;
import com.zly.project.carrier.batchUpload.service.BatchUploadOfDocumentsService;
import com.zly.project.carrier.driver.domain.DriverTransportCapacity;
import com.zly.project.carrier.driver.service.IDriverTransportCapacityService;
import com.zly.project.common.domain.req.CustomerExportRecordReq;
import com.zly.project.common.domain.req.xiaoyudian.CanReUploadReq;
import com.zly.project.common.service.XiaoYuDianService;
import com.zly.project.common.service.impl.ExportRecordServiceImpl;
import com.zly.project.finance.dingDing.domain.CallBackInfo;
import com.zly.project.finance.dingDing.service.impl.DingDingService;
import com.zly.project.finance.statement.service.impl.FinanceStatementServiceImpl;
import com.zly.project.financeOperation.service.HuaXiaFinancingService;
import com.zly.project.financeOperation.service.HuaxiaRepaymentService;
import com.zly.project.hxb.entity.req.FinanceRepaymentNoticeReq;
import com.zly.project.hxb.entity.req.FinanceRepaymentWaybillNoticeReq;
import com.zly.project.hxb.entity.res.ApprovalResultNoticeRes;
import com.zly.project.hxb.entity.res.FinancePushResultRes;
import com.zly.project.hxb.service.IHxbCreditEnterpriseService;
import com.zly.project.miniprogram.domain.DriverInfo;
import com.zly.project.miniprogram.service.impl.DriverCardMsgSerivce;
import com.zly.project.proxy.domain.req.InvoiceStatusCallbackReq;
import com.zly.project.proxy.domain.req.OrderAuditCallbackReq;
import com.zly.project.proxy.service.ProxyInvoicesService;
import com.zly.project.riskManagement.domain.WaybillRiskMqInfo;
import com.zly.project.riskManagement.domain.dto.MqWaybillRiskValidateReq;
import com.zly.project.riskManagement.service.WaybillRiskMqInfoService;
import com.zly.project.riskManagement.service.WaybillRiskService;
import com.zly.project.taxUpload.domain.TaxUploadMqReq;
import com.zly.project.taxUpload.service.YwkUploadService;
import com.zly.project.tenant.domain.TenantUser;
import com.zly.project.tenant.service.ITenantUserNoticeService;
import com.zly.project.tenant.service.ITenantUserService;
import com.zly.project.transfer.service.ITmsDataTransferService;
import com.zly.project.transport.waybill.domain.Waybill;
import com.zly.project.transport.waybill.domain.WaybillProxyInvoice;
import com.zly.project.transport.waybill.domain.WaybillTrackAbnormal;
import com.zly.project.transport.waybill.domain.res.WaybillRes;
import com.zly.project.transport.waybill.mapper.WaybillMapperEx;
import com.zly.project.transport.waybill.mapper.WaybillProxyInvoiceMapper;
import com.zly.project.transport.waybill.service.impl.WaybillServiceImpl;
import com.zly.project.transport.waybill.service.impl.WaybillTrackAbnormalServiceImpl;
import com.zly.project.wlhy.model.WaybillUploginkInfo;
import com.zly.project.wlhy.service.WlhyUploadService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Component
public class RabbitMQReceiver {
	private static final Logger logger = LoggerFactory.getLogger(RabbitMQReceiver.class);
	@Resource
	BatchUploadOfDocumentsRecordMapper recordMapper;
	@Resource
	BatchUploadOfDocumentsPicRecordMapper picRecordMapper;
	@Resource
	ITenantUserService tenantUserService;
	@Resource
	ITenantUserNoticeService tenantUserNoticeService;
	@Resource
	DriverCardMsgSerivce driverCardMsgSerivce;
	@Resource
	ITmsDataTransferService tmsDataTransferService;
	@Resource
	IHxbCreditEnterpriseService hxbCreditEnterpriseService;
	@Resource
	private ExportRecordServiceImpl exportRecordService;
	@Resource
	private BatchUploadOfDocumentsService batchUploadOfDocumentsService;
	@Resource
	private HuaXiaFinancingService huaXiaFinancingService;
	@Resource
	private BatchUploadOfShippingAgreementsRecordMapper batchUploadOfShippingAgreementsRecordMapper;
	@Resource
	private BatchUploadOfShippingAgreementsPicRecordMapper batchUploadOfShippingAgreementsPicRecordMapper;
	@Resource
	private HuaxiaRepaymentService huaxiaRepaymentService;
	@Resource
	private BatchUploadOfReceiptRecordMapper batchUploadOfReceiptRecordMapper;
	@Resource
	private BatchUploadOfReceiptPicRecordMapper batchUploadOfReceiptPicRecordMapper;
	@Resource
	private WlhyUploadService wlhyUploadService;
	@Resource
	private WaybillTrackAbnormalServiceImpl waybillTrackAbnormalService;
	@Resource
	private WaybillMapperEx waybillMapperEx;
	@Resource
	private WaybillServiceImpl waybillService;
	@Resource
	private XiaoYuDianService xiaoYuDianService;
	@Resource
	private DingDingService dingDingService;
	@Resource
	private FourDataHandlerService fourDataHandlerService;
	@Resource
	private WaybillRiskService waybillRiskService;
	@Resource
	private IDriverTransportCapacityService driverTransportCapacityService;
	@Resource
	private WaybillRiskMqInfoService waybillRiskMqInfoService;

	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.exportMqName}", containerFactory = "rabbitListenerContainerFactoryForExport")
	// 单线程处理，暂无并发
	public void exportProcess(String data) {
		logger.info("export消费者收到消息，开始处理: {}", data);
		CustomerExportRecordReq recordReq = JSON.parseObject(data, CustomerExportRecordReq.class);
		exportRecordService.doExport(recordReq);
		logger.info("export消费者已处理完成: {}", data);
	}

	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.uploadMqName}")
	public void batchUploadOfDocumentsProcess(String data) {
		long start = System.currentTimeMillis();
		logger.info("运力证件批量上传--消费者收到消息，开始处理: {}", data);
		UploadDocumentsReq uploadDocumentsReq = JSON.parseObject(data, UploadDocumentsReq.class);
		if (uploadDocumentsReq == null || uploadDocumentsReq.getId() == null) {
			logger.error("运力证件批量上传任务id--不存在，终止处理！");
			logger.info("运力证件批量上传--消费者已处理完成，用时: {}，参数为：{}", System.currentTimeMillis() - start, data);
			return;
		}
		// 防止消息接收太快，前面数据库插入操作还未完成d
		BatchUploadOfDocumentsRecord record;
		do {
			try {
				// noinspection BusyWait
				Thread.sleep(3000);
			} catch (InterruptedException e) {
				throw new RuntimeException(e);
			}
			record = recordMapper.selectBatchUploadOfDocumentsRecordById(uploadDocumentsReq.getId());
		} while (record == null && System.currentTimeMillis() - start < 30000);

		if (record == null) {
			logger.error("运力证件批量上传任务--不存在，终止处理！");
			logger.info("运力证件批量上传--消费者已处理完成，用时: {}，参数为：{}", System.currentTimeMillis() - start, data);
			return;
		}
		try {
			batchUploadOfDocumentsService.doBatchRecognize(uploadDocumentsReq);
		} catch (Exception e) {
			// 失败时，仍修改识别状态
			picRecordMapper.updateStateAndMessageByRecordId(3, "无法识别，请联系管理员", record.getId()); // 图片记录状态置为识别失败
			record.setState(2); // 识别记录状态置为成功
			record.setSuccessNum(0);
			record.setFailNum(record.getUploadCount());
			record.setEndTime(DateUtils.getNowDate());
			recordMapper.updateBatchUploadOfDocumentsRecord(record);
		} finally {
			// 异步插入通知及发送socket消息
			BatchUploadOfDocumentsRecord finalRecord = record;
			new Thread(() -> {
				String formatter = "[证件批量上传]您于%s请求的%s批量识别已完成";
				String notice = String.format(formatter, DateUtils.getFormatDateTime(finalRecord.getCreateTime(), DateUtils.YYMMDD), finalRecord.getUploadFileName());
				List<TenantUser> users = Collections.singletonList(tenantUserService.selectUserById(finalRecord.getCreateUserId()));
				tenantUserNoticeService.createCommonNoticeAndSendWebSocket(notice, notice, users);
			}).start();
		}
		logger.info("运力证件批量上传--消费者已处理完成，用时: {}，参数为：{}", System.currentTimeMillis() - start, data);
	}

	// 接收司机名片推送
	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.driverCardName}")
	public void driverProcess(String data) {
		try {
			logger.info("司机名片消费者收到消息，开始处理: {}", data);
			DriverInfo recordReq = JSON.parseObject(data, DriverInfo.class);
			driverCardMsgSerivce.saveDriverCard(recordReq);
			logger.info("司机名片消费者已处理完成: {}", data);
		} catch (Exception e) {
			logger.error("处理司机名片推送发生异常：" + ExceptionUtils.getStackTrace(e));
		}
	}

	//处理司机运力完善信息 数据从司机名片过来
	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.driverTransportName}")
	public void handleDriverTransportCapacity(String message) {
		logger.info("接收到司机运力完善信息消息:{}", message);
		try {
			// 解析消息内容
			JSONObject jsonObject = JSON.parseObject(message);
			DriverTransportCapacity driverTransportCapacity = jsonObject.getObject("driverTransportCapacity", DriverTransportCapacity.class);
			String idCard = jsonObject.getString("idCard");
			if (driverTransportCapacity != null && StringUtils.isNotEmpty(idCard)) {
				// 根据身份证号查询现有运力数据
				DriverTransportCapacity existCapacity = driverTransportCapacityService.selectByIdCard(idCard);
				if (existCapacity != null) {
					DriverTransportCapacity newData = getUpdateDriver(driverTransportCapacity);
					newData.setId(existCapacity.getId());
					driverTransportCapacityService.updateDriverTransportCapacity(newData);
					logger.info("更新司机运力完善信息成功，司机身份证：{}", driverTransportCapacity.getIdCard());
				} else {
					DriverTransportCapacity insert = getUpdateDriver(driverTransportCapacity);
					driverTransportCapacityService.insertDriverTransportCapacity(insert,2);
					logger.info("新增司机运力完善信息成功，司机身份证：{}", driverTransportCapacity.getIdCard());
				}
			}
		} catch (Exception e) {
			logger.error("处理司机运力完善信息异常", e);
		}
	}

	/**
	 * 获取新的运力信息 数据司机名片
	 * @param driverTransportCapacity
	 * @return
	 */
	private DriverTransportCapacity getUpdateDriver(DriverTransportCapacity driverTransportCapacity) {
		DriverTransportCapacity newData = new DriverTransportCapacity();
		newData.setIdCard(driverTransportCapacity.getIdCard());
		newData.setSource(2);
		newData.setVehicleLength(driverTransportCapacity.getVehicleLength());
		newData.setVehicleType(driverTransportCapacity.getVehicleType());
		newData.setRouteInfo(driverTransportCapacity.getRouteInfo());
		newData.setRouteInfoDetail(driverTransportCapacity.getRouteInfoDetail());
		newData.setGoodsVolume(driverTransportCapacity.getGoodsVolume());
		newData.setGoodsWeight(driverTransportCapacity.getGoodsWeight());
		return newData;
	}

	// 接收司机名片上传回单
	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.driverCardWaybillReceiptName}")
	public void driverCradUplooadWaybillReceiptProcess(String data) {
		try {
			logger.info("司机名片——上传回单消费者收到消息，开始处理: {}", data);
			Long waybillId = Long.parseLong(data);
			driverCardMsgSerivce.dealDriverCardWaybillReceiptInfo(waybillId);
			logger.info("司机名片——上传回单消费者已处理完成: {}", data);
		} catch (Exception e) {
			logger.error("处理司机名片——上传回单推送发生异常：" + ExceptionUtils.getStackTrace(e));
		}
	}

	/**
	 * 华夏数贷通授信审核结果通知
	 *
	 * @param data
	 */
	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.customerApprovalName}")
	public void customerApprovalProcess(String data) {
		try {
			logger.info("华夏数贷通授信审核结果通知消费者收到消息，开始处理: {}", data);
			ApprovalResultNoticeRes notice = JSON.parseObject(data, ApprovalResultNoticeRes.class);
			hxbCreditEnterpriseService.creditResultFeedback(notice);
			logger.info("华夏数贷通授信审核结果通知消费者已处理完成: {}", data);
		} catch (Exception e) {
			logger.error("华夏数贷通授信审核结果通知消费者处理失败：{}", ExceptionUtils.getStackTrace(e));

		}
	}

	/**
	 * 华夏融资推送结果通知
	 *
	 * @param data
	 */
	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.financeHuaXiaPaymentResultName}")
	public void financeHuaXiaPaymentResult(String data) {
		try {
			logger.info("华夏融资推送结果通知结果通知消费者收到消息，开始处理: {}", data);
			FinancePushResultRes notice = JSON.parseObject(JSON.parseObject(data).getString("Data"), FinancePushResultRes.class);
			huaXiaFinancingService.financeHuaXiaPaymentResult(notice);
			logger.info("华夏融资推送结果通知结果通知消费者已处理完成: {}", data);
		} catch (Exception e) {
			logger.error("华夏融资推送结果通知结果处理失败：{}", ExceptionUtils.getStackTrace(e));

		}
	}

	// 批量上传运输协议 mq
	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.uploadShippingAgreementsMqName}")
	public void batchUploadOfShippingAgreementsProcess(String data) {
		long start = System.currentTimeMillis();
		logger.info("批量上传运输协议--消费者收到消息，开始处理: {}", data);
		UploadShippingAgreementsReq uploadShippingAgreementsReq = JSON.parseObject(data, UploadShippingAgreementsReq.class);
		if (uploadShippingAgreementsReq == null || uploadShippingAgreementsReq.getId() == null) {
			logger.error("批量上传运输协议id--不存在，终止处理！");
			logger.info("批量上传运输协议--消费者已处理完成，用时: {}，参数为：{}", System.currentTimeMillis() - start, data);
			return;
		}
		// 防止消息接收太快，前面数据库插入操作还未完成
		BatchUploadOfShippingAgreementsRecord record;
		do {
			try {
				// noinspection BusyWait
				Thread.sleep(3000);
			} catch (InterruptedException e) {
				throw new RuntimeException(e);
			}
			record = batchUploadOfShippingAgreementsRecordMapper.selectBatchUploadOfShippingAgreementsRecordById(uploadShippingAgreementsReq.getId());
		} while (record == null && System.currentTimeMillis() - start < 30000);
		if (record == null) {
			logger.error("批量上传运输协议任务--不存在，终止处理！");
			logger.info("批量上传运输协议--消费者已处理完成，用时: {}，参数为：{}", System.currentTimeMillis() - start, data);
			return;
		}
		try {
			batchUploadOfDocumentsService.doBatchShippingAgreements(uploadShippingAgreementsReq);
		} catch (Exception e) {
			// 失败时，仍修改识别状态
			batchUploadOfShippingAgreementsPicRecordMapper.updateStateAndMessageByRecordId(3L, "无法识别，请联系管理员", record.getId()); // 图片记录状态置为识别失败
			record.setState(2); // 识别记录状态置为成功
			record.setSuccessNum(0L);
			record.setFailNum(record.getUploadCount());
			record.setEndTime(DateUtils.getNowDate());
			batchUploadOfShippingAgreementsRecordMapper.updateBatchUploadOfShippingAgreementsRecord(record);
		}
	}

	/**
	 * 华夏还款结果通知
	 *
	 * @param data
	 */
	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.financeHuaXiaRepaymentResultRoutingName}")
	public void financeHuaXiaRepaymentResult(String data) {
		try {
			logger.info("华夏还款结果通知消费者收到消息，开始处理: {}", data);
			FinanceRepaymentNoticeReq notice = JSON.parseObject(JSON.parseObject(data).getString("Data"), FinanceRepaymentNoticeReq.class);
			huaxiaRepaymentService.dealWithRepaymentResult(notice);
			logger.info("华夏还款结果通知消费者已处理完成: {}", data);
		} catch (Exception e) {
			logger.error("华夏还款结果处理失败：{}", ExceptionUtils.getStackTrace(e));

		}
	}

	/**
	 * 华夏还款清分结果通知
	 *
	 * @param data
	 */
	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.financeHuaXiaRepaymentWaybillResultRoutingName}")
	public void financeHuaXiaRepaymentWaybillResult(String data) {
		try {
			logger.info("华夏还款清分结果通知消费者收到消息，开始处理: {}", data);
			FinanceRepaymentWaybillNoticeReq notice = JSON.parseObject(JSON.parseObject(data).getString("Data"), FinanceRepaymentWaybillNoticeReq.class);
			huaxiaRepaymentService.dealWithRepaymentWaybillResult(notice);
			logger.info("华夏还款清分结果通知消费者已处理完成: {}", data);
		} catch (Exception e) {
			logger.error("华夏还款清分结果处理失败：{}", ExceptionUtils.getStackTrace(e), e);
		}
	}

	// 批量上传运输协议 mq
	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.uploadReceiptMqName}")
	public void batchUploadOfReceiptProcess(String data) {
		long start = System.currentTimeMillis();
		logger.info("批量上传回单--消费者收到消息，开始处理: {}", data);
		UploadReceiptReq req = JSON.parseObject(data, UploadReceiptReq.class);
		if (req == null || req.getId() == null) {
			logger.error("批量上传回单id--不存在，终止处理！");
			logger.info("批量上传回单--消费者已处理完成，用时: {}，参数为：{}", System.currentTimeMillis() - start, data);
			return;
		}
		// 防止消息接收太快，前面数据库插入操作还未完成
		BatchUploadOfReceiptRecord record;
		do {
			try {
				// noinspection BusyWait
				Thread.sleep(3000);
			} catch (InterruptedException e) {
				throw new RuntimeException(e);
			}
			record = batchUploadOfReceiptRecordMapper.selectBatchUploadOfReceiptRecordById(req.getId());
		} while (record == null && System.currentTimeMillis() - start < 30000);
		if (record == null) {
			logger.error("批量上传回单任务--不存在，终止处理！");
			logger.info("批量上传回单--消费者已处理完成，用时: {}，参数为：{}", System.currentTimeMillis() - start, data);
			return;
		}
		try {
			batchUploadOfDocumentsService.doBatchReceipt(req);
		} catch (Exception e) {
			// 失败时，仍修改识别状态
			batchUploadOfReceiptPicRecordMapper.updateStateAndMessageByRecordId(3L, "无法识别，请联系管理员", record.getId()); // 图片记录状态置为识别失败
			record.setState(2); // 识别记录状态置为成功
			record.setSuccessNum(0L);
			record.setFailNum(record.getUploadCount());
			record.setEndTime(DateUtils.getNowDate());
			batchUploadOfReceiptRecordMapper.updateBatchUploadOfReceiptRecord(record);
		}
	}

	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.uploginkMqName}", containerFactory = "rabbitListenerContainerFactoryForUplogink")
	public void uploginkProcess(String data) {
		logger.info("uplogink消费者收到消息，开始处理: {}", data);
		try {
			WaybillUploginkInfo uploginkInfo = JSON.parseObject(data, WaybillUploginkInfo.class);
			wlhyUploadService.uploginkMQProcess(uploginkInfo);
			logger.info("uplogink消费者已处理完成: {}", data);
		} catch (Throwable e) {
			logger.error("uplogink消费者处理消息发生异常", e);
		}
	}

	// 3转4 mq
	// @RabbitHandler
	// @RabbitListener(queues = "${environment.config.mq.tmsThreeToFourQueueName}", containerFactory = "rabbitListenerContainerFactoryForUplogink")
	public void threeToFour(String data) {
		try {
			logger.info("3转4消费者收到消息，开始处理: {}", data);
			Long id = JSON.parseObject(data, Long.class);
			tmsDataTransferService.threeToFourWaybill(id);
			logger.info("3转4消费者已处理完成: {}", data);
		} catch (Exception e) {
			logger.error("3转4消费者处理失败：{}", ExceptionUtils.getStackTrace(e), e);
		}
	}

	// 小雨点运单退回
	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.lbfpCanReUploadName}", containerFactory = "rabbitListenerContainerFactoryForExport")
	public void lbfpCanReUpload(String data) {
		try {
			logger.info("小雨点运单退回消费者收到消息，开始处理: {}", data);
			CanReUploadReq canReUploadReq = JSON.parseObject(data, CanReUploadReq.class);
			xiaoYuDianService.lbfpCanReUpload(canReUploadReq);
			logger.info("小雨点运单退回消费者已处理完成: {}", data);
		} catch (Exception e) {
			logger.error("小雨点运单退回消费者处理失败：{}", ExceptionUtils.getStackTrace(e), e);
		}
	}

	// 小雨点账单校验
	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.lbfpNotifyBillName}", containerFactory = "rabbitListenerContainerFactoryForExport")
	public void lbfpNotifyBillUpload(String data) {
		try {
			logger.info("小雨点账单校验消费者收到消息，开始处理: {}", data);
			CanReUploadReq canReUploadReq = JSON.parseObject(data, CanReUploadReq.class);
			xiaoYuDianService.lbfpNotifyBillUpload(canReUploadReq);
			logger.info("小雨点账单校验消费者已处理完成: {}", data);
		} catch (Exception e) {
			logger.error("小雨点账单校验消费者处理失败：{}", ExceptionUtils.getStackTrace(e), e);
		}
	}

	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.verifyTrajectoryMqName}")
	public void verifyTrajectory(String data) {
		logger.info("verifyTrajectory消费者收到消息，开始处理: {}", data);
		List<Long> sucIds = new ArrayList<>();
		List<Long> errIds = new ArrayList<>();
		List<WaybillRes> waybills = JSON.parseArray(data, WaybillRes.class);
		List<WaybillTrackAbnormal> waybillTrackAbnormals = new ArrayList<>();
		for (WaybillRes waybill : waybills) {
			CommonResult<WaybillTrackAbnormal> result = waybillService.checkTrack(waybill, null, null, null);
			if (result.getData() == null) {
				sucIds.add(waybill.getId());
			} else {
				waybillTrackAbnormals.add(result.getData());
				errIds.add(waybill.getId());
			}
		}

		for (WaybillTrackAbnormal waybillTrackAbnormal : waybillTrackAbnormals) {
			waybillTrackAbnormalService.insertWaybillTrackAbnormal(waybillTrackAbnormal);
		}

		if (sucIds.size() > 0) {
			waybillMapperEx.updateVerifyTrajectoryState(sucIds, 2);
		}
		if (errIds.size() > 0) {
			waybillMapperEx.updateVerifyTrajectoryState(errIds, 1);
		}
		logger.info("verifyTrajectory消费者已处理完成: {}", data);
	}

	@Resource
	private YwkUploadService ywkUploadService;

	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.ywkUploadMqName}", containerFactory = "rabbitListenerContainerFactoryForExport")
	public void uploadTax(String data) {
		logger.info("uploadTax消费者收到消息，开始处理: {}", data);

		try {

			TaxUploadMqReq taxUploadMqReq = JSONObject.parseObject(data, TaxUploadMqReq.class);
			Long id = taxUploadMqReq.getStatementId() != null ? taxUploadMqReq.getStatementId() : taxUploadMqReq.getWaybill().getId();
			/*
			 * 上报税务接口类型
			 * <p>
			 * 1.第一次（起运）上报 2.运抵上报 3.第二次（支付）上报 4.修改起运信息 5.第三次（发票）上报
			 * 6.托运人合同上报 7.修改起运信息 + 运抵上报 8.第一次（起运）上报 + 运抵上报
			 */
			switch (taxUploadMqReq.getType()) {
			case 1:
				ywkUploadService.firstUpload(taxUploadMqReq.getWaybill().getId());
				break;
			case 2:
				ywkUploadService.arriveUpload(taxUploadMqReq.getWaybill().getId());
				break;
			case 3:
				ywkUploadService.secondUpload(taxUploadMqReq.getWaybill().getId());
				break;
			case 4:
				ywkUploadService.modifyFirstUploadField(taxUploadMqReq.getWaybill());
				break;
			case 5:
				ywkUploadService.invoiceUpload(taxUploadMqReq.getStatementId());
				break;
			case 6:
				ywkUploadService.contractUpload(taxUploadMqReq.getWaybill().getId());
				break;
			case 7:
				CommonResult<?> modifyFirstUploadResult = ywkUploadService.modifyFirstUploadField(taxUploadMqReq.getWaybill());
				if (CommonResult.isSuccess(modifyFirstUploadResult)) {
					ywkUploadService.arriveUpload(taxUploadMqReq.getWaybill().getId());
				}
				break;
			case 8:
				CommonResult<?> firstUploadResult = ywkUploadService.firstUpload(taxUploadMqReq.getWaybill().getId());
				if (CommonResult.isSuccess(firstUploadResult)) {
					ywkUploadService.arriveUpload(taxUploadMqReq.getWaybill().getId());
				}
				break;
			default:
				break;
			}
			logger.info("uploadTax消费者已处理完成: id：{}，type：{}，{}", id, taxUploadMqReq.getType(), data);
		} catch (Exception e) {
			logger.error("uploadTax消费者异常: {}", ExceptionUtils.getStackTrace(e), e);
		}
	}

	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.ddCallBackName}", containerFactory = "rabbitListenerContainerFactoryForExport")
	// 单线程处理，暂无并发
	public void ddCallBack(String data) {
		try {
			logger.info("钉钉回调消费者收到消息，开始处理: {}", data);
			CallBackInfo callBackInfo = JSON.parseObject(data, CallBackInfo.class);
			dingDingService.callback(callBackInfo.getMsg_signature(), callBackInfo.getTimeStamp(), callBackInfo.getNonce(), callBackInfo.getEncryptMsg());
			logger.info("钉钉回调消费者已处理完成: {}", data);
		} catch (Exception e) {
			logger.error("钉钉回调消费者处理失败：{}", ExceptionUtils.getStackTrace(e), e);
		}
	}

	// 处理4.0数据同步到善道
	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.fourToShanDaoName}")
	public void fourSynchronizationDataForShanDao(String data) {
		try {
			logger.info("处理4.0数据同步到善道消费者收到消息，开始处理: {}", data);
			SynchronousShanDaoReq req = JSON.parseObject(data, SynchronousShanDaoReq.class);
			fourDataHandlerService.synchronousFullData(req);
			logger.info("处理4.0数据同步到善道消费者已处理完成: {}", data);
		} catch (Exception e) {
			logger.error("处理4.0数据同步到善道——数据同步异常：{}" + ExceptionUtils.getStackTrace(e), e);
		}
	}

	// 处理运单风控校验
	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.waybillRiskValidateName}", containerFactory = "rabbitListenerContainerFactoryForExport")
	public void waybillRiskValidate(String data) {
		WaybillRiskMqInfo info = new WaybillRiskMqInfo();
		logger.info("处理运单风控校验 消费者收到消息，开始处理: {}", data);
		MqWaybillRiskValidateReq req = JSON.parseObject(data, MqWaybillRiskValidateReq.class);

		if (req.getSerialNumber() != null) {
			//插入数据库
			info.setId(req.getSerialNumber());
			info.setState(1);
			info.setReceiveTime(new Date());
			if (req.getSendTime() != null){
				info.setSendTime(req.getSendTime());
			}
			waybillRiskMqInfoService.updateWaybillRiskMqInfo(info);
		}
		try {
			waybillRiskService.waybillsRiskCheck(req.getWaybillIds(), req.getOperatorName(), req.getCapacityIsChanged(), req.getSerialNumber());
			if (req.getSerialNumber() != null) {
				//插入数据库
				info.setState(2);
				info.setEndTime(new Date());
				waybillRiskMqInfoService.updateWaybillRiskMqInfo(info);
			}
			logger.info("处理运单风控校验 消费者已处理完成: {}", data);
		} catch (Exception e) {
			waybillRiskService.restoreOriginalState(req.getWaybillIds());
			logger.error("处理运单风控校验 —— 消费者数据同步异常：{}" + ExceptionUtils.getStackTrace(e), e);

			if (req.getSerialNumber() != null) {
				//插入数据库
				info.setState(3);
				info.setEndTime(new Date());
				waybillRiskMqInfoService.updateWaybillRiskMqInfo(info);
			}
		}
	}

	@Resource
	private WaybillProxyInvoiceMapper waybillProxyInvoiceMapper;

	// 处理订单审核状态回调消息
	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.orderAuditCallbackName}")
	public void orderAuditCallback(String data) {
		try {
			logger.info("订单审核状态回调消费者收到消息: {}", data);
			JSONObject jsonObject = JSON.parseObject(data);
			OrderAuditCallbackReq req = jsonObject.getObject("orderAuditCallback", OrderAuditCallbackReq.class);
			if (req != null) {
				logger.info("处理订单审核状态回调，运单号：{}，审核状态：{}", req.getWaybillNo(), req.getStatus());
				// TODO: 实现具体的业务逻辑处理
				long waybillId = waybillMapperEx.selectIdByShippingNoteNumber(req.getWaybillNo());

				Waybill updateWaybill = new Waybill();
				updateWaybill.setId(waybillId);
				if ("1".equals(req.getStatus())) {
					updateWaybill.setProxyInvoiceState(6);
				} else if ("2".equals(req.getStatus())) {
					updateWaybill.setProxyInvoiceState(5);
				} else {
					updateWaybill.setProxyInvoiceState(4);
				}
				waybillService.updateWaybill(updateWaybill);

				if (StringUtils.isNotEmpty(req.getAuditMsg())) {
					WaybillProxyInvoice updateWaybillProxyInvoice = new WaybillProxyInvoice();
					updateWaybillProxyInvoice.setWaybillId(waybillId);
					updateWaybillProxyInvoice.setRefuseReason(req.getAuditMsg());
					waybillProxyInvoiceMapper.updateWaybillProxyInvoiceByWaybillId(updateWaybillProxyInvoice);
				}
			} else {
				logger.error("订单审核状态回调消息格式错误，无法解析orderAuditCallback数据");
			}
			logger.info("订单审核状态回调消费者处理完成");
		} catch (Exception e) {
			logger.error("处理订单审核状态回调消息异常: {}", ExceptionUtils.getStackTrace(e), e);
		}
	}

	@Resource
	private ProxyInvoicesService proxyInvoicesService;

	// 处理开票状态回调消息
	@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.invoiceStatusCallbackName}")
	public void invoiceStatusCallback(String data) {
		try {
			logger.info("开票状态回调消费者收到消息: {}", data);
			JSONObject jsonObject = JSON.parseObject(data);
			InvoiceStatusCallbackReq req = jsonObject.getObject("invoiceStatusCallback", InvoiceStatusCallbackReq.class);
			if (req != null) {
				logger.info("开票状态回调，运单号：{}，审核状态：{}", req.getWaybillNo(), req.getStatus());
				// TODO: 实现具体的业务逻辑处理
				proxyInvoicesService.bill(req);
			} else {
				logger.error("开票状态回调消息格式错误，无法解析invoiceStatusCallback数据");
			}
			logger.info("开票状态回调消费者处理完成");
		} catch (Exception e) {
			logger.error("处理开票状态回调消息异常: {}", ExceptionUtils.getStackTrace(e), e);
		}
	}

	@Resource
	private FinanceStatementServiceImpl financeStatementService;

	// 处理开票运输费支付状态
	/*@RabbitHandler
	@RabbitListener(queues = "${environment.config.mq.financeStatementPaymentName}")
	public void financeStatementPayment(String data) {
		try {
			logger.info("处理开票运输费支付状态回调消费者收到消息: {}", data);
			FinanceStatementCallBack callBack = JSON.parseObject(data, FinanceStatementCallBack.class);
			if (callBack != null) {
				CommonResult result = financeStatementService.updateFinanceStatementCallBack(callBack);
				if (!CommonResult.isSuccess(result)){
					logger.error("处理开票运输费支付状态回调消息格式错误，未查询到申请数据");
				}
			} else {
				logger.error("处理开票运输费支付状态回调消息格式错误，无法解析数据");
			}
			logger.info("处理开票运输费支付状态回调消费者处理完成");
		} catch (Exception e) {
			logger.error("处理开票运输费支付状态回调消息异常: {}", ExceptionUtils.getStackTrace(e), e);
		}
	}*/

}
