package com.zly.project.common.service.impl;

import com.zly.common.constant.BusinessConstants;
import com.zly.common.utils.DateUtils;
import com.zly.common.utils.StringUtils;
import com.zly.project.common.domain.CustomerAttachmentInfo;
import com.zly.project.common.mapper.CustomerAttachmentInfoMapper;
import com.zly.project.common.mapper.CustomerAttachmentInfoMapperEx;
import com.zly.project.common.service.ICustomerAttachmentInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 托运人附件信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-08
 */
@Service
public class CustomerAttachmentInfoServiceImpl implements ICustomerAttachmentInfoService {
	@Resource
	private CustomerAttachmentInfoMapper customerAttachmentInfoMapper;
	@Resource
	private CustomerAttachmentInfoMapperEx customerAttachmentInfoMapperEx;

	/**
	 * 查询托运人附件信息
	 *
	 * @param id
	 * 		托运人附件信息主键
	 * @return 托运人附件信息
	 */
	@Override
	public CustomerAttachmentInfo selectCustomerAttachmentInfoById(Long id) {
		return customerAttachmentInfoMapper.selectCustomerAttachmentInfoById(id);
	}

	/**
	 * 查询托运人附件信息列表
	 *
	 * @param customerAttachmentInfo
	 * 		托运人附件信息
	 * @return 托运人附件信息
	 */
	@Override
	public List<CustomerAttachmentInfo> selectCustomerAttachmentInfoList(CustomerAttachmentInfo customerAttachmentInfo) {
		return customerAttachmentInfoMapper.selectCustomerAttachmentInfoList(customerAttachmentInfo);
	}

	/**
	 * 新增托运人附件信息
	 *
	 * @param customerAttachmentInfo
	 * 		托运人附件信息
	 * @return 结果
	 */
	@Override
	public int insertCustomerAttachmentInfo(CustomerAttachmentInfo customerAttachmentInfo) {
		customerAttachmentInfo.setCreateTime(DateUtils.getNowDate());
		if (StringUtils.isNotEmpty(customerAttachmentInfo.getOriginalFileName())) {
			if (customerAttachmentInfo.getOriginalFileName().length() > 200) {
				String originalFileName = customerAttachmentInfo.getOriginalFileName();
				int lastIndexOfDot = originalFileName.lastIndexOf('.');
				String extension = originalFileName.substring(lastIndexOfDot);
				customerAttachmentInfo.setOriginalFileName(originalFileName.substring(0, 150) + extension);
			}
		}
		if (StringUtils.isNotEmpty(customerAttachmentInfo.getKeyValue())) {
			if (customerAttachmentInfo.getKeyValue().length() > 200) {
				int charsTo = 150;
				String keyValue = customerAttachmentInfo.getKeyValue();
				int stringLength = keyValue.length();
				int startIndex = Math.max(0, stringLength - charsTo); // 确保起始位置不小于0
				customerAttachmentInfo.setKeyValue(keyValue.substring(startIndex));
			}
		}
		return customerAttachmentInfoMapper.insertCustomerAttachmentInfo(customerAttachmentInfo);
	}

	/**
	 * 修改托运人附件信息
	 *
	 * @param customerAttachmentInfo
	 * 		托运人附件信息
	 * @return 结果
	 */
	@Override
	public int updateCustomerAttachmentInfo(CustomerAttachmentInfo customerAttachmentInfo) {
		customerAttachmentInfo.setUpdateTime(DateUtils.getNowDate());
		return customerAttachmentInfoMapper.updateCustomerAttachmentInfo(customerAttachmentInfo);
	}

	/**
	 * 批量删除托运人附件信息
	 *
	 * @param ids
	 * 		需要删除的托运人附件信息主键
	 * @return 结果
	 */
	@Override
	public int deleteCustomerAttachmentInfoByIds(Long[] ids) {
		return customerAttachmentInfoMapper.deleteCustomerAttachmentInfoByIds(ids);
	}

	/**
	 * 删除托运人附件信息信息
	 *
	 * @param id
	 * 		托运人附件信息主键
	 * @return 结果
	 */
	@Override
	public int deleteCustomerAttachmentInfoById(Long id) {
		return customerAttachmentInfoMapper.deleteCustomerAttachmentInfoById(id);
	}

	/**
	 * 查询托运人附件列表
	 *
	 * @param customerId
	 * 		托运人ID
	 * @return
	 */
	@Override
	public List<CustomerAttachmentInfo> customerFileList(Long customerId) {
		List<String> fileNames = new ArrayList<>();
		return selectCustomerFileList(customerId, fileNames);
	}

	/**
	 * 查询附件列表
	 *
	 * @param vehicleId
	 * 		关联ID
	 * @param fileNames
	 * 		文件名列表
	 * @return
	 */
	private List<CustomerAttachmentInfo> selectCustomerFileList(long vehicleId, List<String> fileNames) {
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("relationId", vehicleId);
		params.put("state", BusinessConstants.STATE_ENABLE);
		if (null != fileNames && !fileNames.isEmpty()) {
			params.put("fileName", fileNames);
		}
		List<CustomerAttachmentInfo> list = customerAttachmentInfoMapperEx.getCustomerAttachmentInfoList(params);
		if (null == list) {
			return new ArrayList<>();
		}
		return list;
	}

	/**
	 * 删除托运人的附件
	 *
	 * @param customerId
	 * @return
	 */
	@Override
	public int deleteCustomerFiles(Long customerId) {
		return customerAttachmentInfoMapperEx.deleteCustomerAttachmentInfoByRelaId(customerId);
	}
}
