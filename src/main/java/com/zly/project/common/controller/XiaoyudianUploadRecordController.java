package com.zly.project.common.controller;



import com.zly.framework.aspectj.lang.annotation.Log;
import com.zly.framework.aspectj.lang.enums.BusinessType;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.common.domain.XiaoyudianUploadRecord;
import com.zly.project.common.service.XiaoyudianUploadRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 * @ClassName: XiaoyudianUploadRecordController
 * @Description: 小雨点推送请求记录Controller层
 * @Author: CYS
 * @Date: 2023/6/14 16:22
 **/
@Api(tags = "金融服务系统-小雨点推送请求记录")
@RestController
@RequestMapping("/common/xiaoyudian")
public class XiaoyudianUploadRecordController extends BaseController {

	@Resource
	private XiaoyudianUploadRecordService xiaoyudianUploadRecordService;


	/**
	 * 查询客户信息列表
	 */
	@ApiOperation(value = "金融服务系统-小雨点推送请求记录-查询小雨点推送请求记录--v4.2.7--cys")
	@PostMapping("/list")
	public TableDataInfo list(@RequestBody XiaoyudianUploadRecord xiaoyudianUploadRecord) {
		startPage();
		List<XiaoyudianUploadRecord> list = xiaoyudianUploadRecordService.selectXiaoyudianUploadRecordList(xiaoyudianUploadRecord);
		return getDataTable(list);
	}


	/**
	 * 获取客户信息详细信息
	 */
	@ApiOperation(value = "金融服务系统-小雨点推送请求记录-获取小雨点推送请求记录详细信息--v4.2.7--cys")
	@GetMapping(value = "/{id}")
	public CommonResult<XiaoyudianUploadRecord> getInfo(@PathVariable("id") Long id) {
		XiaoyudianUploadRecord xiaoyudianUploadRecord = xiaoyudianUploadRecordService.selectXiaoyudianUploadRecordById(id);
		return CommonResult.success(xiaoyudianUploadRecord);
	}

	/**
	 * 新增客户信息
	 */
	@ApiOperation(value = "金融服务系统-小雨点推送请求记录-新增小雨点推送请求记录--v4.2.7--cys")
	@Log(title = "客户信息", businessType = BusinessType.INSERT)
	@PostMapping("/add")
	public CommonResult add(@RequestBody XiaoyudianUploadRecord xiaoyudianUploadRecord) {
		xiaoyudianUploadRecordService.insertXiaoyudianUploadRecord(xiaoyudianUploadRecord);
		return CommonResult.success();
	}

	/**
	 * 修改客户信息
	 */
	@ApiOperation(value = "金融服务系统-小雨点推送请求记录-修改小雨点推送请求记录信息--v4.2.7--cys")
	@Log(title = "客户信息", businessType = BusinessType.UPDATE)
	@PostMapping("/edit")
	public CommonResult edit(@RequestBody XiaoyudianUploadRecord xiaoyudianUploadRecord) {
		xiaoyudianUploadRecordService.updateXiaoyudianUploadRecord(xiaoyudianUploadRecord);
		return CommonResult.success();
	}

	/**
	 * 删除客户信息
	 */
	@ApiOperation(value = "金融服务系统-小雨点推送请求记录-删除小雨点推送请求记录信息--v4.2.7--cys")
	@Log(title = "客户信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public CommonResult remove(@PathVariable Long[] ids) {
		xiaoyudianUploadRecordService.deleteXiaoyudianUploadRecord(ids);
		return CommonResult.success();
	}
}
