package com.zly.project.common.mapper;

import java.util.List;

import com.zly.project.common.domain.CustomerAttachmentInfo;

/**
 * 托运人附件信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-08
 */
public interface CustomerAttachmentInfoMapper {
	/**
	 * 查询托运人附件信息
	 * 
	 * @param id
	 *            托运人附件信息主键
	 * @return 托运人附件信息
	 */
	CustomerAttachmentInfo selectCustomerAttachmentInfoById(Long id);

	/**
	 * 查询托运人附件信息列表
	 * 
	 * @param customerAttachmentInfo
	 *            托运人附件信息
	 * @return 托运人附件信息集合
	 */
	List<CustomerAttachmentInfo> selectCustomerAttachmentInfoList(CustomerAttachmentInfo customerAttachmentInfo);

	/**
	 * 新增托运人附件信息
	 * 
	 * @param customerAttachmentInfo
	 *            托运人附件信息
	 * @return 结果
	 */
	int insertCustomerAttachmentInfo(CustomerAttachmentInfo customerAttachmentInfo);

	/**
	 * 修改托运人附件信息
	 * 
	 * @param customerAttachmentInfo
	 *            托运人附件信息
	 * @return 结果
	 */
	int updateCustomerAttachmentInfo(CustomerAttachmentInfo customerAttachmentInfo);

	/**
	 * 删除托运人附件信息
	 * 
	 * @param id
	 *            托运人附件信息主键
	 * @return 结果
	 */
	int deleteCustomerAttachmentInfoById(Long id);

	/**
	 * 批量删除托运人附件信息
	 * 
	 * @param ids
	 *            需要删除的数据主键集合
	 * @return 结果
	 */
	int deleteCustomerAttachmentInfoByIds(Long[] ids);

    int deleteCustomerAttachmentInfoByRelationIds(List<Long> customerIds);

    List<CustomerAttachmentInfo> selectFileNameAndUrlListByRelationId(Long relationId);

	/**
	 * 查询托运人附件信息列表
	 *
	 * @param customerAttachmentInfo 托运人附件信息
	 * @return 托运人附件信息集合
	 */
	List<CustomerAttachmentInfo> selectCustomerAttachmentInfoListByFreightForwarderId();
}
