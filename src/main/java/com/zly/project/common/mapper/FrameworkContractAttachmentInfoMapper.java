package com.zly.project.common.mapper;

import java.util.List;

import com.zly.project.common.domain.FrameworkContractAttachmentInfo;
import com.zly.project.common.domain.FreightForwarderAttachmentInfo;

/**
 * 项目合约附件信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-08
 */
public interface FrameworkContractAttachmentInfoMapper {
	/**
	 * 查询项目合约附件信息
	 * 
	 * @param id
	 *            项目合约附件信息主键
	 * @return 项目合约附件信息
	 */
	FrameworkContractAttachmentInfo selectFrameworkContractAttachmentInfoById(Long id);

	/**
	 * 查询项目合约附件信息列表
	 * 
	 * @param frameworkContractAttachmentInfo
	 *            项目合约附件信息
	 * @return 项目合约附件信息集合
	 */
	List<FrameworkContractAttachmentInfo> selectFrameworkContractAttachmentInfoList(FrameworkContractAttachmentInfo frameworkContractAttachmentInfo);

	/**
	 * 新增项目合约附件信息
	 * 
	 * @param frameworkContractAttachmentInfo
	 *            项目合约附件信息
	 * @return 结果
	 */
	int insertFrameworkContractAttachmentInfo(FrameworkContractAttachmentInfo frameworkContractAttachmentInfo);

	/**
	 * 修改项目合约附件信息
	 * 
	 * @param frameworkContractAttachmentInfo
	 *            项目合约附件信息
	 * @return 结果
	 */
	int updateFrameworkContractAttachmentInfo(FrameworkContractAttachmentInfo frameworkContractAttachmentInfo);

	/**
	 * 删除项目合约附件信息
	 * 
	 * @param id
	 *            项目合约附件信息主键
	 * @return 结果
	 */
	int deleteFrameworkContractAttachmentInfoById(Long id);

	/**
	 * 批量删除项目合约附件信息
	 * 
	 * @param ids
	 *            需要删除的数据主键集合
	 * @return 结果
	 */
	int deleteFrameworkContractAttachmentInfoByIds(Long[] ids);

	int deleteFrameworkContractAttachmentInfoByRelationId(Long relationId);

	List<FrameworkContractAttachmentInfo> selectByRelationIds(List<Long> contractIds);

	int insertFrameworkContractAttachmentInfos(List<FrameworkContractAttachmentInfo> contractAttachmentInfos);

	List<FrameworkContractAttachmentInfo> selectFileNameAndUrlListByRelationId(Long relationId);
}
