package com.zly.project.common.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.zly.project.carrier.auxiliary.domain.AuxiliaryStaffAttachmentInfo;
import com.zly.project.common.domain.ActualCarrierAttachmentInfo;
import com.zly.project.common.domain.DriverAttachmentInfo;

public interface ActualCarrierAttachmentInfoMapper {
	/**
	 * 查询承运人附件信息
	 *
	 * @param id
	 *            承运人附件信息主键
	 * @return 承运人附件信息
	 */
	ActualCarrierAttachmentInfo selectActualCarrierAttachmentInfoById(Long id);

	/**
	 * 查询承运人附件信息列表
	 *
	 * @param attachmentInfo
	 *            承运人附件信息
	 * @return 承运人附件信息集合
	 */
	List<ActualCarrierAttachmentInfo> selectActualCarrierAttachmentInfoList(ActualCarrierAttachmentInfo attachmentInfo);

	/**
	 * 新增承运人附件信息
	 *
	 * @param attachmentInfo
	 *            承运人附件信息
	 * @return 结果
	 */
	int insertActualCarrierAttachmentInfo(ActualCarrierAttachmentInfo attachmentInfo);

	/**
	 * 修改承运人附件信息
	 *
	 * @param attachmentInfo
	 *            承运人附件信息
	 * @return 结果
	 */
	int updateActualCarrierAttachmentInfo(ActualCarrierAttachmentInfo attachmentInfo);

	/**
	 * 删除承运人附件信息
	 *
	 * @param id
	 *            承运人附件信息主键
	 * @return 结果
	 */
	int deleteActualCarrierAttachmentInfoById(Long id);

	/**
	 * 批量删除承运人附件信息
	 *
	 * @param ids
	 *            需要删除的数据主键集合
	 * @return 结果
	 */
	int deleteActualCarrierAttachmentInfoByIds(Long[] ids);

	List<ActualCarrierAttachmentInfo> selectCarrierAttachmentList(Map<String, Object> params);

	void updateActualCarrierAttachmentInfoId(Map<String, Object> map);

	int insertActualCarrierAttachmentInfos(List<ActualCarrierAttachmentInfo> attachmentInfos);

	List<Long> selectVehicleFileListByActualCarrierInfoIdAndFileName(List<Long> basicIntegrityActualCarrierInfoIds, String idCardA);

	List<ActualCarrierAttachmentInfo> queryFileByCarrierIds(List<Long> relationIds);

	// 查询属于收款人的承运人附件
	List<ActualCarrierAttachmentInfo> selectPayeeInfoPayeeNotAttachmentIssues(String identityCard, String payeeName);

	int deleteActualCarrierAttachmentInfoByCarrierId(Long actualCarrierId);

	void handleFile();

	List<ActualCarrierAttachmentInfo> selectCarrierAttachmentInfoByIds(List<Long> attachmentIds);

	List<ActualCarrierAttachmentInfo> selectIdsByRelationIdOne(Long carrierId);

	int deleteCarrierIdentityFiles(@Param("carrierId") Long carrierId, @Param("fileNames") List<String> fileNames);

	int logicDeleteCarrierIdentityFiles(@Param("carrierIds") List<Long> carrierIds, @Param("fileNames") List<String> fileNames);

	List<DriverAttachmentInfo> queryDriverFileByCarrierIds(List<Long> relationIds);

	List<DriverAttachmentInfo> selectDriverAttachmentListByIdCard(String identityCard);

	List<AuxiliaryStaffAttachmentInfo> selectAuxiliaryStaffAttachmentInfoList(Long auxiliaryStaffId);

	int modifyCarrierIdentityFilesState(@Param("carrierId") Long carrierId, @Param("fileNames") List<String> fileNames);

	void deleteCarrierFilesByIds(Long[] ids);

	List<ActualCarrierAttachmentInfo> selectByCarrierIds(List<Long> carrierIds);

    int deleteActualCarrierAttachmentInfoByRelationIds(List<Long> carrierIds);

	/**
	 * 将司机id作为附件的relationId返回
	 *
	 * @param driverIds
	 * @return
	 */
	List<ActualCarrierAttachmentInfo> selectJoinCarrierInfoByDriverIds(List<Long> driverIds);

	List<ActualCarrierAttachmentInfo> selectJoinCarrierInfoByAuxiliaryIds(List<Long> auxiliaryStaffIds);

	List<ActualCarrierAttachmentInfo> selectJoinCarrierInfoByCaptainIds(List<Long> carCaptainIds);

    List<ActualCarrierAttachmentInfo> selectFileNameAndUrlListByRelationId(Long relationId);
}
