package com.zly.project.tenant.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zly.common.entity.IUser;
import com.zly.common.xss.Xss;
import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.aspectj.lang.annotation.Excel.ColumnType;
import com.zly.framework.aspectj.lang.annotation.Excel.Type;
import com.zly.framework.aspectj.lang.annotation.Excels;
import com.zly.framework.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 租户的用户对象 Tenant_user
 *
 * <AUTHOR>
 */
public class TenantUser extends BaseEntity implements IUser {
	private static final long serialVersionUID = 1L;

	/** 用户ID */
	@Excel(name = "用户序号", cellType = ColumnType.NUMERIC, prompt = "用户编号")
	@ApiModelProperty("用户ID")
	private Long userId;

	/** 部门ID */
	@Excel(name = "部门编号", type = Type.IMPORT)
	@ApiModelProperty("部门ID")
	private Long deptId;

	/** 用户账号 */
	@Excel(name = "登录名称")
	@ApiModelProperty("用户账号")
	private String userName;

	/**
	 * 用户昵称
	 */
	@Excel(name = "用户名称")
	@ApiModelProperty("用户昵称")
	private String nickName;

	/**
	 * 用户邮箱
	 */
	@Excel(name = "用户邮箱")
	@ApiModelProperty("用户邮箱")
	private String email;

	// 上次输入邮箱
	private String lastEmail;

	/**
	 * 手机号码
	 */
	@Excel(name = "手机号码")
	@ApiModelProperty("手机号码")
	private String phonenumber;

	/**
	 * 用户性别
	 */
	@Excel(name = "用户性别", readConverterExp = "0=男,1=女,2=未知")
	@ApiModelProperty("用户性别")
	private String sex;

	/** 用户头像 */
	@ApiModelProperty("用户头像")
	@Excel(name = "用户性别", readConverterExp = "0=男,1=女,2=未知")
	private String avatar;

	/** 密码 */
	@ApiModelProperty("密码")
	@Excel(name = "密码")
	private String password;

	/** 密码 */
	@ApiModelProperty("密码")
	@Excel(name = "密码")
	private String pwd;

	/** 帐号状态（0正常 1停用） */
	@Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
	@ApiModelProperty("帐号状态（0正常 1停用）")
	private String status;

	/** 删除标志（0代表存在 2代表删除） */
	@ApiModelProperty("删除标志（0代表存在 2代表删除）")
	private String delFlag;

	/** 最后登录IP */
	@Excel(name = "最后登录IP", type = Type.EXPORT)
	@ApiModelProperty("最后登录IP")
	private String loginIp;

	/** 最后登录时间 */
	@Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Type.EXPORT)
	@ApiModelProperty("最后登录时间")
	private Date loginDate;

	/** 部门对象 */
	@Excels({ @Excel(name = "部门名称", targetAttr = "deptName", type = Type.EXPORT), @Excel(name = "部门负责人", targetAttr = "leader", type = Type.EXPORT) })
	@ApiModelProperty("部门对象")
	private TenantDept dept;

	/** 角色对象 */
	@ApiModelProperty("角色对象")
	private List<TenantRole> roles;

	/** 角色组 */
	@ApiModelProperty("角色组")
	private Long[] roleIds;

	/** 岗位组 */
	@ApiModelProperty("岗位组")
	private Long[] postIds;

	/** 角色ID */
	@ApiModelProperty("角色ID")
	private Long roleId;

	/** 租户ID */
	@ApiModelProperty("租户ID")
	private Long tenantId;

	/** 租户名称 */
	@ApiModelProperty("租户名称")
	private String tenantName;

	@ApiModelProperty("项目权限：0 指定项目 1 所有项目")
	private Integer defaultProject;

	@ApiModelProperty("复制运单附件方式  0.默认未设置 1.复制运力附件（司机,车辆,收款人） 2.复制所有附件（运力附件+收/发货附件+运输附件）")
	private Integer waybillCopyType;

	/**
	 * 项目组
	 */
	@ApiModelProperty("项目组")
	private List<Long> groupIds;

	// 是否可见全部货源(小程序)(0默认所有货源  1仅自己创建的货源)
	private Integer isAllMakeCode;

	public Long getTenantId() {
		return tenantId;
	}

	public void setTenantId(Long tenantId) {
		this.tenantId = tenantId;
	}

	public String getLastEmail() {
		return lastEmail;
	}

	public void setLastEmail(String lastEmail) {
		this.lastEmail = lastEmail;
	}

	public TenantUser() {

	}

	public TenantUser(Long userId) {
		this.userId = userId;
	}

	public TenantUser(Long tenantId, String status) {
		this.tenantId = tenantId;
		this.status = status;
	}

	public String getTenantName() {
		return tenantName;
	}

	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	// public boolean isAdmin() {
	// return isAdmin(this.userId);
	// }

	// public static boolean isAdmin(Long userId) {
	// return userId != null && 1L == userId;
	// }

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	@Xss(message = "用户昵称不能包含脚本字符")
	@Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	@Xss(message = "用户账号不能包含脚本字符")
	@NotBlank(message = "用户账号不能为空")
	@Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	@Email(message = "邮箱格式不正确")
	@Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	@Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
	public String getPhonenumber() {
		return phonenumber;
	}

	public void setPhonenumber(String phonenumber) {
		this.phonenumber = phonenumber;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getAvatar() {
		return avatar;
	}

	public void setAvatar(String avatar) {
		this.avatar = avatar;
	}

	@JsonIgnore
	@JsonProperty
	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	@JsonIgnore
	@JsonProperty
	public String getPwd() {
		return pwd;
	}

	public void setPwd(String pwd) {
		this.pwd = pwd;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getDelFlag() {
		return delFlag;
	}

	public void setDelFlag(String delFlag) {
		this.delFlag = delFlag;
	}

	public String getLoginIp() {
		return loginIp;
	}

	public void setLoginIp(String loginIp) {
		this.loginIp = loginIp;
	}

	public Date getLoginDate() {
		return loginDate;
	}

	public void setLoginDate(Date loginDate) {
		this.loginDate = loginDate;
	}

	public TenantDept getDept() {
		return dept;
	}

	public void setDept(TenantDept dept) {
		this.dept = dept;
	}

	public List<TenantRole> getRoles() {
		return roles;
	}

	public void setRoles(List<TenantRole> roles) {
		this.roles = roles;
	}

	public Long[] getRoleIds() {
		return roleIds;
	}

	public void setRoleIds(Long[] roleIds) {
		this.roleIds = roleIds;
	}

	public Long[] getPostIds() {
		return postIds;
	}

	public void setPostIds(Long[] postIds) {
		this.postIds = postIds;
	}

	public Long getRoleId() {
		return roleId;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	public Integer getDefaultProject() {
		return defaultProject;
	}

	public void setDefaultProject(Integer defaultProject) {
		this.defaultProject = defaultProject;
	}

	public List<Long> getGroupIds() {
		return groupIds;
	}

	public void setGroupIds(List<Long> groupIds) {
		this.groupIds = groupIds;
	}

	public Integer getWaybillCopyType() {
		return waybillCopyType;
	}

	public void setWaybillCopyType(Integer waybillCopyType) {
		this.waybillCopyType = waybillCopyType;
	}

	public Integer getIsAllMakeCode() {
		return isAllMakeCode;
	}

	public void setIsAllMakeCode(Integer isAllMakeCode) {
		this.isAllMakeCode = isAllMakeCode;
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("userId", getUserId()).append("deptId", getDeptId()).append("userName", getUserName()).append("nickName", getNickName())
				.append("email", getEmail()).append("phonenumber", getPhonenumber()).append("sex", getSex()).append("avatar", getAvatar()).append("password", getPassword())
				.append("status", getStatus()).append("delFlag", getDelFlag()).append("loginIp", getLoginIp()).append("loginDate", getLoginDate()).append("createBy", getCreateBy())
				.append("createTime", getCreateTime()).append("updateBy", getUpdateBy()).append("updateTime", getUpdateTime()).append("remark", getRemark()).append("dept", getDept())
				.append("tenantId", getTenantId()).toString();
	}
}
