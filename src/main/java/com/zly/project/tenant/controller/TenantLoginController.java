package com.zly.project.tenant.controller;

import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zly.common.constant.HttpStatus;
import com.zly.common.entity.LoginResult;
import com.zly.common.enums.ClientType;
import com.zly.common.exception.ServiceException;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.ServletUtils;
import com.zly.common.utils.ValidateUtils;
import com.zly.common.utils.VerifyPwUtils;
import com.zly.common.utils.ip.IPUtils;
import com.zly.framework.redis.RedisCache;
import com.zly.framework.security.LoginBody;
import com.zly.framework.security.service.SysLoginService;
import com.zly.framework.security.service.SysPermissionService;
import com.zly.framework.web.domain.CommonResult;
import com.zly.project.consignor.customer.domain.CustomerInfo;
import com.zly.project.consignor.customer.service.impl.CustomerInfoServiceImpl;
import com.zly.project.system.domain.vo.RouterVo;
import com.zly.project.system.service.impl.SysClientLogService;
import com.zly.project.tenant.domain.TenantMenu;
import com.zly.project.tenant.domain.TenantUser;
import com.zly.project.tenant.service.ITenantMenuService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 托运人端登录验证
 *
 * <AUTHOR>
 */
@Api(tags = "托运人端登录验证")
@RestController
@RequestMapping("/tenant")
public class TenantLoginController {
	@Resource
	private SysLoginService loginService;

	@Resource
	private ITenantMenuService menuService;

	@Resource
	private SysPermissionService permissionService;

	@Resource
	private SysClientLogService sysClientLogService;

	@Resource
	private CustomerInfoServiceImpl customerInfoService;

	@Resource
	private RedisCache redisCache;

	/**
	 * 登录方法 -- 加入弱密码校验
	 *
	 * @param loginBody
	 *            登录信息
	 * @return 结果
	 */
	@ApiOperation(value = "登录", notes = "登录")
	@PostMapping("/login")
	public LoginResult login(@RequestBody LoginBody loginBody, HttpServletRequest request) {
		if (!ValidateUtils.checkMobilePhone(loginBody.getUsername().split("_")[0])) {
			throw new ServiceException("格式错误，请输入正确的手机号");
		}
		// 如果跟善道项目相关的用户不是从善道系统登录的，记录下来(临时添加，过段时间要删除的)
		if (redisCache.hasKey("sduser")) {
			String sdUser = redisCache.getCacheObject("sduser");
			if (null != sdUser && sdUser.contains(loginBody.getUsername())) {
				String domain = request.getServerName();
				String accerr = redisCache.getCacheObject("sduser_accerr");
				String sddomain = redisCache.getCacheObject("sddomain");
				if (!domain.contains(sddomain)) {
					accerr = StringUtils.isBlank(accerr) ? loginBody.getUsername() : accerr + "," + loginBody.getUsername();
					String[] parts = accerr.split(",");
					Set<String> uniqueSet = new LinkedHashSet<>(Arrays.asList(parts));
					String result = String.join(",", uniqueSet);
					redisCache.setCacheObject("sduser_accerr", result);
				} else {
					if (StringUtils.isNotBlank(accerr)) {
						String result = Arrays.stream(accerr.split(",")).filter(s -> !s.equals(loginBody.getUsername())) // 精确匹配
								.collect(Collectors.joining(","));
						redisCache.setCacheObject("sduser_accerr", result);
					}
				}
			}
		}
		// 生成令牌
		String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(), loginBody.getUuid(), ClientType.SHIPPER);
		// 是否弱密码
		Boolean isWeekPW = VerifyPwUtils.isWeekPw(loginBody.getPassword());

		LoginResult loginResult = LoginResult.loginSuccess(token, isWeekPW);

		if (HttpStatus.SUCCESS == loginResult.getCode()) {
			sysClientLogService.insertLogLogin(loginResult.getToken(), IPUtils.getIPAddress(ServletUtils.getRequest()), ClientType.SHIPPER);
		}
		return loginResult;
	}

	/**
	 * 接口推送登录校验
	 *
	 * @param loginBody
	 *            登录信息
	 * @return 结果
	 */
	@ApiOperation(value = "接口推送登录校验", notes = "接口推送登录校验")
	@PostMapping("/push/login")
	public LoginResult pushLogin(@RequestBody LoginBody loginBody) {
		// if(!ValidateUtils.checkMobilePhone(loginBody.getUsername().split("_")[0])){
		// throw new ServiceException("格式错误，请输入正确的手机号");
		// }
		// 生成令牌
		String token = loginService.pushLogin(loginBody.getUsername(), loginBody.getPassword());
		// 是否弱密码
		// Boolean isWeekPW = VerifyPwUtils.isWeekPw(loginBody.getPassword());

		return LoginResult.loginSuccess(token, false);
	}

	/**
	 * 获取用户信息
	 *
	 * @return 用户信息
	 */
	@ApiOperation(value = "获取用户信息", notes = "获取用户信息")
	@GetMapping("/getInfo")
	public LoginResult getInfo() {
		TenantUser user = SecurityUtils.getLoginUser().getTenantUser();
		if (null == user) {
			return LoginResult.of(HttpStatus.UNAUTHORIZED, "登录失效");
		}
		// 角色集合
		Set<String> roles = permissionService.getRolePermission(user);
		if (roles.size() < 1) {
			throw new ServiceException("未查询到登录用户角色");
		}
		// 权限集合
		Set<String> permissions = permissionService.getMenuPermission(user);

		CustomerInfo customer = customerInfoService.selectCustomerInfoById(user.getTenantId());
		LoginResult loginResult = LoginResult.success();
		loginResult.setUser(user);
		loginResult.setRoles(roles);
		loginResult.setPermissions(permissions);
		loginResult.setTenantName(customer.getCustomerName());
		loginResult.setEnterpriseId(user.getTenantId());
		return loginResult;
	}

	/**
	 * 获取路由信息
	 *
	 * @return 路由信息
	 */
	@GetMapping("/getRouters")
	public CommonResult<List<RouterVo>> getRouters() {
		Long userId = SecurityUtils.getUserId();
		List<TenantMenu> menus = menuService.selectMenuTreeByUserId(userId);
		return CommonResult.success(menuService.buildMenus(menus));
	}
}
