package com.zly.project.tenant.mapper;

import java.util.List;

import com.zly.project.tenant.domain.TenantUser;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.zly.project.tenant.domain.TenantUserRole;

/**
 * 租户用户与角色关联表 数据层
 * 
 * <AUTHOR>
 */
@Repository
public interface TenantUserRoleMapper {
	/**
	 * 通过用户ID删除用户和角色关联
	 * 
	 * @param userId
	 *            用户ID
	 * @return 结果
	 */
	int deleteUserRoleByUserId(Long userId);

	/**
	 * 批量删除用户和角色关联
	 *
	 * @param ids
	 *            需要删除的数据ID
	 * @return 结果
	 */
	int deleteUserRole(Long[] ids);

	/**
	 * 通过角色ID查询角色使用数量
	 * 
	 * @param roleId
	 *            角色ID
	 * @return 结果
	 */
	int countUserRoleByRoleId(Long roleId);

	/**
	 * 批量新增用户角色信息
	 * 
	 * @param userRoleList
	 *            用户角色列表
	 * @return 结果
	 */
	int batchUserRole(List<TenantUserRole> userRoleList);

	/**
	 * 删除用户和角色关联信息
	 * 
	 * @param userRole
	 *            用户和角色关联信息
	 * @return 结果
	 */
	int deleteUserRoleInfo(TenantUserRole userRole);

	/**
	 * 批量取消授权用户角色
	 * 
	 * @param roleId
	 *            角色ID
	 * @param userIds
	 *            需要删除的用户数据ID
	 * @return 结果
	 */
	int deleteUserRoleInfos(@Param("roleId") Long roleId, @Param("userIds") Long[] userIds);

	/**
	 * 将管理员用户改为普通角色
	 * 
	 * @param tenantId
	 * @return
	 */
	int editRoleByRoleId(Long tenantId);

	List<Long> selectRoleIdsByUserId(Long userId);

	/**
	 * 根据权限id删除与之关联用户
	 * 
	 * @param roleId
	 * @return
	 */
	int deleteUserRoleByRoleId(Long roleId);

	List<Long> selectAdminByUserIds(Long[] userIds);

}
