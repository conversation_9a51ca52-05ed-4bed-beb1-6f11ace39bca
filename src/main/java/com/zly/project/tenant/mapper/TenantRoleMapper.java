package com.zly.project.tenant.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.zly.project.tenant.domain.TenantRole;
import com.zly.project.tenant.domain.req.TenantRoleReq;

/**
 * 角色表 数据层
 * 
 * <AUTHOR>
 */
@Repository
public interface TenantRoleMapper {
	/**
	 * 根据条件分页查询角色数据
	 *
	 * @param role 角色信息
	 * @return 角色数据集合信息
	 */
	public List<TenantRole> selectRoleList(TenantRole role);

	public List<TenantRole> selectRoleListEx(TenantRoleReq role);

	/**
	 * 根据用户ID查询角色
	 *
	 * @param userId 用户ID
	 * @return 角色列表
	 */
	public List<TenantRole> selectRolePermissionByUserId(Long userId, Long tenantId);

	/**
	 * 查询所有角色
	 * 
	 * @return 角色列表
	 */
	public List<TenantRole> selectRoleAll();

	/**
	 * 根据用户ID获取角色选择框列表
	 * 
	 * @param userId
	 *            用户ID
	 * @return 选中角色ID列表
	 */
	public List<Long> selectRoleListByUserId(Long userId);

	/**
	 * 通过角色ID查询角色
	 * 
	 * @param roleId
	 *            角色ID
	 * @return 角色对象信息
	 */
	public TenantRole selectRoleById(Long roleId);

	/**
	 * 根据用户ID查询角色
	 * 
	 * @param userName
	 *            用户名
	 * @return 角色列表
	 */
	public List<TenantRole> selectRolesByUserName(String userName);

	/**
	 * 校验角色名称是否唯一
	 * 
	 * @param roleName
	 *            角色名称
	 * @return 角色信息
	 */
	public TenantRole checkRoleNameUnique(@Param("roleName") String roleName,@Param("tenantId") Long tenantId);

	/**
	 * 校验角色权限是否唯一
	 * 
	 * @param roleKey
	 *            角色权限
	 * @return 角色信息
	 */
	public TenantRole checkRoleKeyUnique(String roleKey);

	/**
	 * 修改角色信息
	 * 
	 * @param role
	 *            角色信息
	 * @return 结果
	 */
	public int updateRole(TenantRole role);

	/**
	 * 新增角色信息
	 * 
	 * @param role
	 *            角色信息
	 * @return 结果
	 */
	public int insertRole(TenantRole role);

	/**
	 * 通过角色ID删除角色
	 * 
	 * @param roleId
	 *            角色ID
	 * @return 结果
	 */
	public int deleteRoleById(Long roleId);

	/**
	 * 批量删除角色信息
	 * 
	 * @param roleIds
	 *            需要删除的角色ID
	 * @return 结果
	 */
	public int deleteRoleByIds(Long[] roleIds);

	/**
	 * 设置用户默认角色
	 *
	 * @param userId
	 * @return
	 */
	public int insertDefaultRole(Long userId);

	List<TenantRole> selectRoleByPresetId(Long presetRoleId);

	List<TenantRole> selectRoleByUserId(Long userId);

	List<TenantRole> selectRoleListByIds(List<Long> roleIds);

	List<TenantRole> selectRoleListByCustomerIds(List<Long> customerIds);
}
