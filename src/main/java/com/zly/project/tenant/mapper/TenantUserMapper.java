package com.zly.project.tenant.mapper;

import com.zly.project.system.domain.ClickLoginRes;
import com.zly.project.tenant.domain.TenantUser;
import com.zly.project.tenant.domain.TenantUserEx;
import com.zly.project.tenant.domain.req.TenantUserReq;
import com.zly.project.tenant.domain.res.EnterpriseInfoRes;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 用户表 数据层
 *
 * <AUTHOR>
 */
@Repository
public interface TenantUserMapper {
	/**
	 * 根据条件分页查询用户列表
	 *
	 * @param user
	 *            用户信息
	 * @return 用户信息集合信息
	 */
	public List<TenantUser> selectUserList(TenantUser user);

	/**
	 * 根据条件分页查询已配用户角色列表
	 *
	 * @param user
	 *            用户信息
	 * @return 用户信息集合信息
	 */
	public List<TenantUser> selectAllocatedList(TenantUser user);

	/**
	 * 根据条件分页查询未分配用户角色列表
	 *
	 * @param user
	 *            用户信息
	 * @return 用户信息集合信息
	 */
	public List<TenantUser> selectUnallocatedList(TenantUser user);

	/**
	 * 通过用户名查询用户
	 *
	 * @param userName
	 *            用户名
	 * @return 用户对象信息
	 */
	public TenantUser selectUserByUserName(String userName);

	/**
	 * 通过用户ID查询用户
	 *
	 * @param userId
	 *            用户ID
	 * @return 用户对象信息
	 */
	public TenantUser selectUserById(Long userId);

	/**
	 * 新增用户信息
	 *
	 * @param user
	 *            用户信息
	 * @return 结果
	 */
	public int insertUser(TenantUser user);

	/**
	 * 修改用户信息
	 *
	 * @param user
	 *            用户信息
	 * @return 结果
	 */
	public int updateUser(TenantUser user);

	/**
	 * 修改用户头像
	 *
	 * @param userName
	 *            用户名
	 * @param avatar
	 *            头像地址
	 * @return 结果
	 */
	public int updateUserAvatar(@Param("userName") String userName, @Param("avatar") String avatar);

	/**
	 * 重置用户密码
	 *
	 * @param userName
	 *            用户名
	 * @param password
	 *            密码
	 * @return 结果
	 */
	public int resetUserPwd(@Param("userName") String userName, @Param("password") String password, @Param("pwd") String pwd);

	/**
	 * 通过用户ID删除用户
	 *
	 * @param userId
	 *            用户ID
	 * @return 结果
	 */
	public int deleteUserById(Long userId);

	/**
	 * 批量删除用户信息
	 *
	 * @param userIds
	 *            需要删除的用户ID
	 * @return 结果
	 */
	public int deleteUserByIds(Long[] userIds);

	/**
	 * 校验用户名称是否唯一
	 *
	 * @param userName
	 *            用户名称
	 * @return 结果
	 */
	public int checkUserNameUnique(String userName);

	/**
	 * 校验手机号码是否唯一
	 *
	 * @param phonenumber
	 *            手机号码
	 * @return 结果
	 */
	public TenantUser checkPhoneUnique(String phonenumber);

	/**
	 * 校验手机号码是否唯一，不含托运人id
	 *
	 * @param phonenumber
	 *            手机号码
	 * @return 结果
	 */
	public TenantUser checkPhoneAbsoluteUnique(String phonenumber);

	/**
	 * 校验手机号码是否唯一，包含托运人id
	 *
	 * @param phonenumber
	 *            手机号码
	 * @return 结果
	 */
	public TenantUser checkPhoneAndCustomerID(@Param("phonenumber") String phonenumber, @Param("tenantId") Long tenantId);

	/**
	 * 校验email是否唯一
	 *
	 * @param email
	 *            用户邮箱
	 * @return 结果
	 */
	public TenantUser checkEmailUnique(String email);

	/**
	 * 根据 TenantId 查询是否存在
	 *
	 * @param customerId
	 * @return
	 */
	TenantUser selectUserByTenantId(Long customerId);

	/**
	 * 根据 UserId 查询是否存在
	 *
	 * @param userId
	 * @return
	 */
	TenantUser selectUserByUserId(Long userId, Integer delFlag);

	/**
	 * 根据 phoneNumber 查询是否存在
	 *
	 * @param phoneNumber
	 * @return
	 */
	List<TenantUser> selectUserByPhoneNumber(String phoneNumber);

	List<TenantUser> selectAllUserList();

	/**
	 * 条件查询所有用户
	 *
	 * @param user
	 * @return
	 */
	List<TenantUser> getAllUserList(TenantUser user);

	List<TenantUser> selectUserListByTenantId(Long tenantId);

	List<TenantUser> selectAdminUsersByTenantId(Long tenantId);

	int countUser(TenantUserEx tenantUserEx);

	List<TenantUser> selectUsersByTenantId(Long tenantId);

	List<Long> selectHasAllProjectsUserByIds(List<Long> userIds);

	void updateUserDelFlag(@Param("customerId") Long customerId, @Param("userId") Long userId);

	List<Long> queryDelFlagUser(@Param("customerId") Long customerId, @Param("userId") Long userId);

	int checkNickNameUnique(TenantUserReq user);

	List<ClickLoginRes> queryUser(Map<String, Object> map);

	List<EnterpriseInfoRes> getEnterpriseInfo(String phone);

	void mergeShippers(Long fromId);

	List<TenantUser> selectTenantUserByCustomerIds(List<Long> customerIds);

	int updateUserStatusByUserIds(List<Long> shipTenantUserIds);

	List<TenantUser> selectUserByIds(List<Long> userIds);

	TenantUser selectUserAdminByCustomerId(Long customerId);

	List<TenantUser> selectUserListEx(TenantUserEx tenantUserEx);

	List<Long> selectHasAllProjectsUserByTenantId(Long customerId);

	int updateIsAllMakeCode(@Param("userId") Long userId, @Param("type") Integer type);

	void updateUserLastEmail(@Param("userId") Long userId, @Param("email") String email);

	String getLastEmail(Long userId);
}
