package com.zly.project.tenant.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.zly.project.tenant.domain.TenantMenu;

/**
 * 菜单表 数据层
 * 
 * <AUTHOR>
 */
@Repository
public interface TenantMenuMapper {
	/**
	 * 查询系统菜单列表
	 * 
	 * @param menu
	 *            菜单信息
	 * @return 菜单列表
	 */
	public List<TenantMenu> selectMenuList(TenantMenu menu);

	/**
	 * 根据用户所有权限
	 * 
	 * @return 权限列表
	 */
	public List<String> selectMenuPerms();

	/**
	 * 根据用户查询系统菜单列表
	 * 
	 * @param menu
	 *            菜单信息
	 * @return 菜单列表
	 */
	public List<TenantMenu> selectMenuListByUserId(TenantMenu menu);

	/**
	 * 根据用户ID查询权限
	 * 
	 * @param userId
	 *            用户ID
	 * @return 权限列表
	 */
	public List<String> selectMenuPermsByUserId(Long userId);

	/**
	 * 根据用户ID查询菜单
	 * 
	 * @return 菜单列表
	 */
	public List<TenantMenu> selectMenuTreeAll();

	/**
	 * 根据用户ID查询菜单
	 * 
	 * @param username
	 *            用户ID
	 * @return 菜单列表
	 */
	public List<TenantMenu> selectMenuTreeByUserId(Long userId);

	/**
	 * 根据角色ID查询菜单树信息
	 * 
	 * @param roleId
	 *            角色ID
	 * @param menuCheckStrictly
	 *            菜单树选择项是否关联显示
	 * @return 选中菜单列表
	 */
	public List<Long> selectMenuListByRoleId(@Param("roleId") Long roleId, @Param("menuCheckStrictly") boolean menuCheckStrictly);

	/**
	 * 根据菜单ID查询信息
	 * 
	 * @param menuId
	 *            菜单ID
	 * @return 菜单信息
	 */
	public TenantMenu selectMenuById(Long menuId);

	/**
	 * 是否存在菜单子节点
	 * 
	 * @param menuId
	 *            菜单ID
	 * @return 结果
	 */
	public int hasChildByMenuId(Long menuId);

	/**
	 * 新增菜单信息
	 * 
	 * @param menu
	 *            菜单信息
	 * @return 结果
	 */
	public int insertMenu(TenantMenu menu);

	/**
	 * 修改菜单信息
	 * 
	 * @param menu
	 *            菜单信息
	 * @return 结果
	 */
	public int updateMenu(TenantMenu menu);

	/**
	 * 删除菜单管理信息
	 * 
	 * @param menuId
	 *            菜单ID
	 * @return 结果
	 */
	public int deleteMenuById(Long menuId);

	/**
	 * 校验菜单名称是否唯一
	 * 
	 * @param menuName
	 *            菜单名称
	 * @param parentId
	 *            父菜单ID
	 * @return 结果
	 */
	public TenantMenu checkMenuNameUnique(@Param("menuName") String menuName, @Param("parentId") Long parentId);

	/**
	 * 根据 ParentId 反查 menu_id
	 * 
	 * @return
	 */
	public List<TenantMenu> selectMenuListByParentId();

	/**
	 * 获取所有按钮信息
	 */
	public List<TenantMenu> selectAllButtonList();
}
