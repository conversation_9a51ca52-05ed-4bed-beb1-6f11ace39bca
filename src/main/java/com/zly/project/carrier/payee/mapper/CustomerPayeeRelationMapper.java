package com.zly.project.carrier.payee.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.zly.project.carrier.payee.domain.CustomerPayeeRelation;
import com.zly.project.carrier.payee.domain.PayeeInfo;

/**
 * 货主收款账户关系Mapper接口
 *
 * <AUTHOR>
 * @date 2022-07-19
 */
public interface CustomerPayeeRelationMapper {
	/**
	 * 查询货主收款账户关系
	 *
	 * @param customerId
	 *            货主收款账户关系主键
	 * @return 货主收款账户关系
	 */
	CustomerPayeeRelation selectCustomerPayeeRelation(@Param("customerId") Long customerId, @Param("payeeId") Long payeeId);

	/**
	 * 查询货主收款账户关系列表
	 *
	 * @param customerPayeeRelation
	 *            货主收款账户关系
	 * @return 货主收款账户关系集合
	 */
	List<CustomerPayeeRelation> selectCustomerPayeeRelationList(CustomerPayeeRelation customerPayeeRelation);

	/**
	 * 新增货主收款账户关系
	 *
	 * @param customerPayeeRelation
	 *            货主收款账户关系
	 * @return 结果
	 */
	int insertCustomerPayeeRelation(CustomerPayeeRelation customerPayeeRelation);

	/**
	 * 删除货主收款账户关系
	 *
	 * @param customerId
	 *            托运人ID
	 * @param payeeId
	 *            收款人ID
	 * @return 结果
	 */
	int deleteCustomerPayeeRelation(@Param("customerId") Long customerId, @Param("payeeId") Long payeeId);

	PayeeInfo selectCustomerPayeeInfo(@Param("customerId") Long customerId, @Param("payeeId") Long payeeId);

	List<CustomerPayeeRelation> selectByPayeeIds(List<Long> payeeIds);

	List<CustomerPayeeRelation> selectByCustomerIdsAndPayeeIds(List<CustomerPayeeRelation> relations);

	int insertCustomerPayeeRelations(List<CustomerPayeeRelation> customerPayeeRelations);

	int deleteCustomerPayeeRelationByPayeeIds(List<Long> payeeIds);

	int deleteRepeatPayeeByBankCardNoAndId(String bankCardNo, Long payeeId);

	int updateNewPayeeIdByOldPayeeId(Long newPayeeId, Long oldPayeeId);

}
