package com.zly.project.carrier.payee.mapper;

import com.zly.project.carrier.payee.domain.ActualPayeeRelation;

import java.util.List;


/**
 * 承运人收款银行卡关联关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface ActualPayeeRelationMapper 
{
    /**
     * 查询承运人收款银行卡关联关系
     * 
     * @param actualPayeeRelationId 承运人收款银行卡关联关系主键
     * @return 承运人收款银行卡关联关系
     */
    public ActualPayeeRelation selectActualPayeeRelationByActualPayeeRelationId(Long actualPayeeRelationId);

    /**
     * 查询承运人收款银行卡关联关系列表
     * 
     * @param actualPayeeRelation 承运人收款银行卡关联关系
     * @return 承运人收款银行卡关联关系集合
     */
    public List<ActualPayeeRelation> selectActualPayeeRelationList(ActualPayeeRelation actualPayeeRelation);

    /**
     * 新增承运人收款银行卡关联关系
     * 
     * @param actualPayeeRelation 承运人收款银行卡关联关系
     * @return 结果
     */
    public int insertActualPayeeRelation(ActualPayeeRelation actualPayeeRelation);

    /**
     * 修改承运人收款银行卡关联关系
     * 
     * @param actualPayeeRelation 承运人收款银行卡关联关系
     * @return 结果
     */
    public int updateActualPayeeRelation(ActualPayeeRelation actualPayeeRelation);

    /**
     * 删除承运人收款银行卡关联关系
     * 
     * @param actualPayeeRelationId 承运人收款银行卡关联关系主键
     * @return 结果
     */
    public int deleteActualPayeeRelationByActualPayeeRelationId(Long actualPayeeRelationId);

    /**
     * 批量删除承运人收款银行卡关联关系
     * 
     * @param actualPayeeRelationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteActualPayeeRelationByActualPayeeRelationIds(Long[] actualPayeeRelationIds);
}
