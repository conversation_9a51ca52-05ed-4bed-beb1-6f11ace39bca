package com.zly.project.carrier.auxiliary.mapper;


import com.zly.project.carrier.auxiliary.domain.AuxiliaryStaffAttachmentInfo;

import java.util.List;

/**
 * 辅助员附件信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-27
 */
public interface AuxiliaryStaffAttachmentInfoMapper {
    /**
     * 查询辅助员附件信息
     *
     * @param id 辅助员附件信息主键
     * @return 辅助员附件信息
     */
    public AuxiliaryStaffAttachmentInfo selectAuxiliaryStaffAttachmentInfoById(Long id);

    /**
     * 查询辅助员附件信息列表
     *
     * @param auxiliaryStaffAttachmentInfo 辅助员附件信息
     * @return 辅助员附件信息集合
     */
    public List<AuxiliaryStaffAttachmentInfo> selectAuxiliaryStaffAttachmentInfoList(AuxiliaryStaffAttachmentInfo auxiliaryStaffAttachmentInfo);

    /**
     * 新增辅助员附件信息
     *
     * @param auxiliaryStaffAttachmentInfo 辅助员附件信息
     * @return 结果
     */
    public int insertAuxiliaryStaffAttachmentInfo(AuxiliaryStaffAttachmentInfo auxiliaryStaffAttachmentInfo);

    /**
     * 修改辅助员附件信息
     *
     * @param auxiliaryStaffAttachmentInfo 辅助员附件信息
     * @return 结果
     */
    public int updateAuxiliaryStaffAttachmentInfo(AuxiliaryStaffAttachmentInfo auxiliaryStaffAttachmentInfo);

    /**
     * 删除辅助员附件信息
     *
     * @param id 辅助员附件信息主键
     * @return 结果
     */
    public int deleteAuxiliaryStaffAttachmentInfoById(Long id);

    /**
     * 批量删除辅助员附件信息
     *
     * @param ids
     * 		需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAuxiliaryStaffAttachmentInfoByIds(Long[] ids);

    List<Long> selectIdsByRelationId(Long id);
}
