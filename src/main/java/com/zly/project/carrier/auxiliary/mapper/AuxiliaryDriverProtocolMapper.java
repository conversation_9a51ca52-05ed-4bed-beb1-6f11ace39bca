package com.zly.project.carrier.auxiliary.mapper;


import java.util.List;

import com.zly.project.carrier.auxiliary.domain.AuxiliaryDriverProtocol;

/**
 * 辅助员司机协议Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-27
 */
public interface AuxiliaryDriverProtocolMapper {
    /**
     * 查询辅助员司机协议
     *
     * @param id 辅助员司机协议主键
     * @return 辅助员司机协议
     */
    public AuxiliaryDriverProtocol selectAuxiliaryDriverProtocolById(Long id);

    /**
     * 查询辅助员司机协议列表
     *
     * @param auxiliaryDriverProtocol 辅助员司机协议
     * @return 辅助员司机协议集合
     */
    public List<AuxiliaryDriverProtocol> selectAuxiliaryDriverProtocolList(AuxiliaryDriverProtocol auxiliaryDriverProtocol);

    /**
     * 新增辅助员司机协议
     *
     * @param auxiliaryDriverProtocol 辅助员司机协议
     * @return 结果
     */
    public int insertAuxiliaryDriverProtocol(AuxiliaryDriverProtocol auxiliaryDriverProtocol);

    /**
     * 修改辅助员司机协议
     *
     * @param auxiliaryDriverProtocol 辅助员司机协议
     * @return 结果
     */
    public int updateAuxiliaryDriverProtocol(AuxiliaryDriverProtocol auxiliaryDriverProtocol);

    /**
     * 删除辅助员司机协议
     *
     * @param id 辅助员司机协议主键
     * @return 结果
     */
    public int deleteAuxiliaryDriverProtocolById(Long id);

    /**
     * 批量删除辅助员司机协议
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAuxiliaryDriverProtocolByIds(List<Long> ids);

	AuxiliaryDriverProtocol selectByAuxiliaryStaffIdAndDriverId(Long auxiliaryStaffId, Long driverId);

	int insertAuxiliaryDriverProtocolList(List<AuxiliaryDriverProtocol> insertAuxiliaryDriverProtocolList);

    List<AuxiliaryDriverProtocol> selectAuxiliaryDriverProtocolListByIdentityCards(List<String> identityCards);

    int deleteAuxiliaryDriverProtocolByIdentityCards(List<String> identityCards);
}
