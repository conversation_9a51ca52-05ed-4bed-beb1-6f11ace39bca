package com.zly.project.carrier.auxiliary.mapper;


import java.util.List;

import com.zly.project.carrier.auxiliary.domain.AuxiliaryDriverRelation;

/**
 * 辅助员司机关系Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-27
 */
public interface AuxiliaryDriverRelationMapper {
    /**
     * 查询辅助员司机关系
     *
     * @param auxiliaryStaffId 辅助员司机关系主键
     * @return 辅助员司机关系
     */
	public List<AuxiliaryDriverRelation> selectByAuxiliaryStaffId(Long auxiliaryStaffId);

    /**
     * 查询辅助员司机关系列表
     *
     * @param auxiliaryDriverRelation 辅助员司机关系
     * @return 辅助员司机关系集合
     */
    public List<AuxiliaryDriverRelation> selectAuxiliaryDriverRelationList(AuxiliaryDriverRelation auxiliaryDriverRelation);

    /**
     * 新增辅助员司机关系
     *
     * @param auxiliaryDriverRelation 辅助员司机关系
     * @return 结果
     */
    public int insertAuxiliaryDriverRelation(AuxiliaryDriverRelation auxiliaryDriverRelation);

    /**
     * 修改辅助员司机关系
     *
     * @param auxiliaryDriverRelation 辅助员司机关系
     * @return 结果
     */
    public int updateAuxiliaryDriverRelation(AuxiliaryDriverRelation auxiliaryDriverRelation);

    /**
     * 删除辅助员司机关系
     *
     * @param auxiliaryStaffId 辅助员司机关系主键
     * @return 结果
     */
    public int deleteAuxiliaryDriverRelationByAuxiliaryStaffId(Long auxiliaryStaffId);

	public int deleteByAuxiliaryStaffIdAndDriverId(Long auxiliaryStaffId, Long driverId);

    /**
     * 批量删除辅助员司机关系
     *
     * @param auxiliaryStaffIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAuxiliaryDriverRelationByAuxiliaryStaffIds(List<Long> auxiliaryStaffIds);

	AuxiliaryDriverRelation selectByAuxiliaryIdAndDriverId(Long auxiliaryStaffId, Long driverId, List<Integer> signingStates);

	List<AuxiliaryDriverRelation> selectByAuxiliaryStaffIdAndNotSelf(Long auxiliaryStaffId);

	int insertAuxiliaryDriverRelationList(List<AuxiliaryDriverRelation> insertAuxiliaryDriverRelationList);

    List<AuxiliaryDriverRelation> selectAuxiliaryDriverRelationListByIdentityCards(List<String> identityCards);
}
