package com.zly.project.carrier.vehicle.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.zly.project.carrier.vehicle.domain.CustomerVehicleRelation;

/**
 * 货主车辆关系Mapper接口
 *
 * <AUTHOR>
 * @date 2022-05-26
 */
public interface CustomerVehicleRelationMapper {
	/**
	 * 查询货主车辆关系
	 *
	 * @param customerId
	 *            货主车辆关系主键
	 * @param vehicleId
	 *            车辆ID
	 * @return 货主车辆关系
	 */
	CustomerVehicleRelation selectCustomerVehicleRelationByCustomerId(@Param("customerId") Long customerId, @Param("vehicleId") Long vehicleId);

	/**
	 * 查询货主车辆关系列表
	 *
	 * @param customerVehicleRelation
	 *            货主车辆关系
	 * @return 货主车辆关系集合
	 */
	List<CustomerVehicleRelation> selectCustomerVehicleRelationList(CustomerVehicleRelation customerVehicleRelation);

	/**
	 * 新增货主车辆关系
	 *
	 * @param customerVehicleRelation
	 *            货主车辆关系
	 * @return 结果
	 */
	int insertCustomerVehicleRelation(CustomerVehicleRelation customerVehicleRelation);

	/**
	 * 修改货主车辆关系
	 *
	 * @param customerVehicleRelation
	 *            货主车辆关系
	 * @return 结果
	 */
	int updateCustomerVehicleRelation(CustomerVehicleRelation customerVehicleRelation);

	/**
	 * 删除货主车辆关系
	 *
	 * @param customerId
	 *            货主车辆关系主键
	 * @return 结果
	 */
	int deleteCustomerVehicleRelationByCustomerId(@Param("customerId") Long customerId, @Param("vehicleId") Long vehicleId);

	/**
	 * 批量删除货主车辆关系
	 *
	 * @param customerIds
	 *            需要删除的数据主键集合
	 * @return 结果
	 */
	int deleteCustomerVehicleRelationByCustomerIds(Long[] customerIds);

	List<CustomerVehicleRelation> selectCustomerVehicleRelationListByIds(Map<String, Object> map);

    int updateVehicleByShipperIdAndVehicleID(Long customerId, Long oldId, Long newId);

    List<CustomerVehicleRelation> selectListLimit(@Param("offset")Integer offset, @Param("pageSize")Integer pageSize);

    int countAll();

	CustomerVehicleRelation selectCustomerVehicleRelationByVehicleNumber(@Param("customerId")Long customerId, @Param("vehicleNumber")String vehicleNumber);

	List<CustomerVehicleRelation> selectByCustomerIdsAndVehicleIds(List<CustomerVehicleRelation> relations);

	int insertCustomerVehicleRelations(List<CustomerVehicleRelation> customerVehicleRelations);

	int deleteCustomerVehicleRelationByVehicleIds(List<Long> vehicleIds);

	void deleteRepeatVehicleByNumberAndId(String vehicleNumber, Long vehicleId);

	void updateNewVehicleIdByOldVehicleId(Long newVehicleId, Long oldVehicleId);

}
