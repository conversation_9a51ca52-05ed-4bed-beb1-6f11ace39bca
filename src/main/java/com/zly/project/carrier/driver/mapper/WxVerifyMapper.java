package com.zly.project.carrier.driver.mapper;

import java.util.List;

import com.zly.project.carrier.driver.domain.Driver;
import com.zly.project.carrier.driver.domain.WxVerify;

/**
 * 司机信息验证Mapper接口
 *
 * <AUTHOR>
 * @date 2021-11-04
 */
public interface WxVerifyMapper
{
    /**
     * 查询司机信息验证
     *
     * @param driverId 司机信息验证主键
     * @return 司机信息验证
     */
    public WxVerify selectDriverVerifyByDriverId(Long driverId);

    /**
     * 查询司机信息验证列表
     *
     * @param wxVerify 司机信息验证
     * @return 司机信息验证集合
     */
    public List<WxVerify> selectDriverVerifyList(WxVerify wxVerify);

    /**
     * 新增司机信息验证
     *
     * @param wxVerify 司机信息验证
     * @return 结果
     */
    public int insertDriverVerify(WxVerify wxVerify);

    /**
     * 修改司机信息验证
     *
     * @param wxVerify 司机信息验证
     * @return 结果
     */
    public int updateDriverVerify(WxVerify wxVerify);

    /**
     * 删除司机信息验证
     *
     * @param driverId 司机信息验证主键
     * @return 结果
     */
    public int deleteDriverVerifyByDriverId(Long driverId);

    /**
     * 批量删除司机信息验证
     *
     * @param driverIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDriverVerifyByDriverIds(Long[] driverIds);

    void deleteByPhone(String telephone);
}
