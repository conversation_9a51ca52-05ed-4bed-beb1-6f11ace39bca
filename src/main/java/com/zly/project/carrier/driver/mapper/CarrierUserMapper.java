package com.zly.project.carrier.driver.mapper;

import java.util.List;

import com.zly.project.carrier.driver.domain.CarrierUser;

/**
 * 承运人用户Mapper接口
 *
 * <AUTHOR>
 * @date 2022-04-26
 */
public interface CarrierUserMapper
{
    /**
     * 查询承运人用户
     *
     * @param userId 承运人用户主键
     * @return 承运人用户
     */
    public CarrierUser selectCarrierUserByUserId(Long userId);

    /**
     * 查询承运人用户列表
     *
     * @param carrierUser 承运人用户
     * @return 承运人用户集合
     */
    public List<CarrierUser> selectCarrierUserList(CarrierUser carrierUser);

    /**
     * 新增承运人用户
     *
     * @param carrierUser 承运人用户
     * @return 结果
     */
    public int insertCarrierUser(CarrierUser carrierUser);

    /**
     * 修改承运人用户
     *
     * @param carrierUser 承运人用户
     * @return 结果
     */
    public int updateCarrierUser(CarrierUser carrierUser);

    /**
     * 删除承运人用户
     *
     * @param userId 承运人用户主键
     * @return 结果
     */
    public int deleteCarrierUserByUserId(Long userId);

    /**
     * 批量删除承运人用户
     *
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCarrierUserByUserIds(Long[] userIds);

    /**
     * 根据actual_id去actual_carrier_info（关联字段car_captain_id）查driver_id
     *
     * @param id 承运人用户主键
     * @return driver_id
     */
    public List<CarrierUser> selectDriverIdByCarCaptainId(Long id);

    Long selectDriverIdByCarrierId(Long actualCarrierId);

    Long selectCarrierIdUserByDriverId(Long driverId);

	Long selectCarrierUserIdUserByActualId(Long actualCarrierId);

	List<CarrierUser> selectCarrierUserListByNotExistsCarAuxiliary();

    List<CarrierUser> selectCarrierUserListByIdentityCards(List<String> identityCards);
}
