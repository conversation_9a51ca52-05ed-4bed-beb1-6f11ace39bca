package com.zly.project.carrier.driver.mapper;

import com.zly.project.carrier.driver.domain.DriverVehicleRelation;
import com.zly.project.carrier.vehicle.domain.res.VehicleRes;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 司机车辆关系Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-27
 */
public interface DriverVehicleRelationMapper {


    /**
     * 查询车队长司机关系列表
     *
     * @param driverVehicleRelation 车队长司机关系
     * @return 车队长司机关系集合
     */
    public List<DriverVehicleRelation> selectDriverVehicleRelationList(DriverVehicleRelation driverVehicleRelation);

    /**
     * 新增车队长司机关系
     *
     * @param driverVehicleRelation 车队长司机关系
     * @return 结果
     */
    public int insertDriverVehicleRelation(DriverVehicleRelation driverVehicleRelation);

    /**
     * 修改车队长司机关系
     *
     * @param driverVehicleRelation 车队长司机关系
     * @return 结果
     */
    public int updateDriverVehicleRelation(DriverVehicleRelation driverVehicleRelation);

	void insertDriverVehicleRelationList(List<DriverVehicleRelation> driverVehicleRelationInsertList);

    List<VehicleRes> selectCustomerDriverVehicleList(@Param("identityCard") String identityCard, @Param("customerId") Long customerId);

    int deleteDriverVehicleRelation(@Param("driverId") Long driverId, @Param("vehicleId") Long vehicleId);

    List<VehicleRes> selectFreightForwarderIdDriverVehicleList(@Param("identityCard") String identityCard, @Param("freightForwarderId") Long freightForwarderId, @Param("signType") Integer signType);

    List<VehicleRes> selectDriverVehicleList(@Param("identityCard") String identityCard, @Param("signType") Integer signType);
}
