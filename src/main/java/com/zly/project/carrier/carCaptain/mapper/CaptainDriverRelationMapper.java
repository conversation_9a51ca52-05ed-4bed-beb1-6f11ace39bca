package com.zly.project.carrier.carCaptain.mapper;


import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.zly.project.carrier.carCaptain.domain.CaptainDriverRelation;

/**
 * 车队长司机关系Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-27
 */
public interface CaptainDriverRelationMapper {
    /**
     * 查询车队长司机关系
     *
     * @param carCaptainId 车队长司机关系主键
     * @return 车队长司机关系
     */
    public CaptainDriverRelation selectCaptainDriverRelationByCarCaptainId(Long carCaptainId);

    /**
     * 查询车队长司机关系列表
     *
     * @param captainDriverRelation 车队长司机关系
     * @return 车队长司机关系集合
     */
    public List<CaptainDriverRelation> selectCaptainDriverRelationList(CaptainDriverRelation captainDriverRelation);

    /**
     * 新增车队长司机关系
     *
     * @param captainDriverRelation 车队长司机关系
     * @return 结果
     */
    public int insertCaptainDriverRelation(CaptainDriverRelation captainDriverRelation);

    /**
     * 修改车队长司机关系
     *
     * @param captainDriverRelation 车队长司机关系
     * @return 结果
     */
    public int updateCaptainDriverRelation(CaptainDriverRelation captainDriverRelation);

    /**
     * 删除车队长司机关系
     *
     * @param carCaptainId 车队长司机关系主键
     * @return 结果
     */
    public int deleteCaptainDriverRelationByCarCaptainId(Long carCaptainId);

    /**
     * 批量删除车队长司机关系
     *
     * @param carCaptainIds
     * 		需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCaptainDriverRelationByCarCaptainIds(List<Long> ids);

    int deleteCaptainDriverRelation(@Param("carCaptainId") Long carCaptainId, @Param("driverId") Long driverId);

    List<CaptainDriverRelation> checkDriverCaptainBinding(String phoneNumber);

	int insertCaptainDriverRelationList(List<CaptainDriverRelation> insertCaptainDriverRelationList);

    List<CaptainDriverRelation> selectCaptainDriverRelationListByIdentityCards(List<String> identityCards);
}
