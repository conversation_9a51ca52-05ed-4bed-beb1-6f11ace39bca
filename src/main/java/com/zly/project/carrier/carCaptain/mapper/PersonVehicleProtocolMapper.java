package com.zly.project.carrier.carCaptain.mapper;

import com.zly.project.carrier.carCaptain.domain.PersonVehicleProtocol;
import com.zly.project.carrier.vehicle.domain.res.VehicleRes;
import com.zly.project.transport.waybill.domain.req.VehicleProtocolReq;
import com.zly.project.transport.waybill.domain.res.VehicleProtocolRes;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 车队长车辆协议Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-27
 */
public interface PersonVehicleProtocolMapper {
    /**
     * 查询车队长车辆协议
     *
     * @param id 车队长车辆协议主键
     * @return 车队长车辆协议
     */
    public PersonVehicleProtocol selectPersonVehicleProtocolById(Long id);

    /**
     * 查询车队长车辆协议列表
     *
     * @param personVehicleProtocol 车队长车辆协议
     * @return 车队长车辆协议集合
     */
    public List<PersonVehicleProtocol> selectPersonVehicleProtocolList(PersonVehicleProtocol personVehicleProtocol);

    /**
     * 新增车队长车辆协议
     *
     * @param personVehicleProtocol 车队长车辆协议
     * @return 结果
     */
    public int insertPersonVehicleProtocol(PersonVehicleProtocol personVehicleProtocol);

    /**
     * 修改车队长车辆协议
     *
     * @param personVehicleProtocol 车队长车辆协议
     * @return 结果
     */
    public int updatePersonVehicleProtocol(PersonVehicleProtocol personVehicleProtocol);

    /**
     * 删除车队长车辆协议
     *
     * @param id 车队长车辆协议主键
     * @return 结果
     */
    public int deletePersonVehicleProtocolById(Long id);

    /**
     * 批量删除车队长车辆协议
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePersonVehicleProtocolByIds(Long[] ids);

	List<VehicleRes> selectCarPersonVehicleListByCarIdAndCustomerId(String idCard, Long customerId);

    int logicDeletePersonVehicleProtocol(@Param("identityCard")String identityCard, @Param("vehicleId")Long vehicleId, @Param("resource")int resource,@Param("signType") int signType);

    List<VehicleRes> selectCarPersonVehicleListByCarId(@Param("idCard") String idCard, @Param("signType") Integer signType);

	List<VehicleRes> selectCarPersonVehicleListByCarIdAndFreightForwardId(@Param("idCard") String idCard, @Param("freightForwarderId") Long freightForwarderId, @Param("signType") Integer signType);

	int updatePersonVehicleProtocolState(Long id);

	void insertPersonVehicleProtocolList(List<PersonVehicleProtocol> insertPersonVehicleProtocolList);

	void insertPersonVehicleProtocolUrlList(List<PersonVehicleProtocol> personVehicleProtocolUrlList);

	List<PersonVehicleProtocol> selectByList(List<PersonVehicleProtocol> list);

	List<VehicleProtocolRes> vehicleProtocolList(VehicleProtocolReq req);

	Integer vehicleProtocolCount(Long freightForwarderId);

	void deleteOldProtocol(PersonVehicleProtocol del);

	List<PersonVehicleProtocol> selectByIdCardAndVehicleNumber(String identityCard, String vehicleNumber);

}
