package com.zly.project.carrier.carCaptain.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.zly.project.carrier.carCaptain.domain.CarCaptainAttachmentInfo;

/**
 * 车队长附件信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-27
 */
public interface CarCaptainAttachmentInfoMapper {
    /**
     * 查询车队长附件信息
     *
     * @param id 车队长附件信息主键
     * @return 车队长附件信息
     */
    public CarCaptainAttachmentInfo selectCarCaptainAttachmentInfoById(Long id);

    /**
     * 查询车队长附件信息列表
     *
     * @param carCaptainAttachmentInfo 车队长附件信息
     * @return 车队长附件信息集合
     */
    public List<CarCaptainAttachmentInfo> selectCarCaptainAttachmentInfoList(CarCaptainAttachmentInfo carCaptainAttachmentInfo);

    /**
     * 新增车队长附件信息
     *
     * @param carCaptainAttachmentInfo 车队长附件信息
     * @return 结果
     */
    public int insertCarCaptainAttachmentInfo(CarCaptainAttachmentInfo carCaptainAttachmentInfo);

    /**
     * 修改车队长附件信息
     *
     * @param carCaptainAttachmentInfo 车队长附件信息
     * @return 结果
     */
    public int updateCarCaptainAttachmentInfo(CarCaptainAttachmentInfo carCaptainAttachmentInfo);

    /**
     * 删除车队长附件信息
     *
     * @param id 车队长附件信息主键
     * @return 结果
     */
    public int deleteCarCaptainAttachmentInfoById(Long id);

    /**
     * 批量删除车队长附件信息
     *
     * @param ids
     * 		需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCarCaptainAttachmentInfoByIds(Long[] ids);

    List<CarCaptainAttachmentInfo> selectCarCaptainAttachmentInfoListByCarCaptainId(Long carCaptainId);

    List<Long> selectIdsByRelationId(Long id);

    int deleteAttachmentInfoByRelationIdAndFileNames(@Param("relationId") Long relationId, @Param("fileNames")List<String> fileNames);

	int insertCarCaptainAttachmentInfoList(List<CarCaptainAttachmentInfo> insertCarCaptainAttachmentInfoList);

    int deleteCarCaptainAttachmentInfoByRelationIds(List<Long> captainIds);

    List<CarCaptainAttachmentInfo> selectFileNameAndUrlListByRelationId(Long relationId);
}
