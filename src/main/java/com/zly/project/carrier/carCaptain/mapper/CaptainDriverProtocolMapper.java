package com.zly.project.carrier.carCaptain.mapper;


import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.zly.project.carrier.carCaptain.domain.CaptainDriverProtocol;

/**
 * 车队长司机协议Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-27
 */
public interface CaptainDriverProtocolMapper {
    /**
     * 查询车队长司机协议
     *
     * @param id 车队长司机协议主键
     * @return 车队长司机协议
     */
    public CaptainDriverProtocol selectCaptainDriverProtocolById(Long id);

    /**
     * 查询车队长司机协议列表
     *
     * @param captainDriverProtocol 车队长司机协议
     * @return 车队长司机协议集合
     */
    public List<CaptainDriverProtocol> selectCaptainDriverProtocolList(CaptainDriverProtocol captainDriverProtocol);

    /**
     * 新增车队长司机协议
     *
     * @param captainDriverProtocol 车队长司机协议
     * @return 结果
     */
    public int insertCaptainDriverProtocol(CaptainDriverProtocol captainDriverProtocol);

    /**
     * 修改车队长司机协议
     *
     * @param captainDriverProtocol 车队长司机协议
     * @return 结果
     */
    public int updateCaptainDriverProtocol(CaptainDriverProtocol captainDriverProtocol);

    /**
     * 删除车队长司机协议
     *
     * @param id 车队长司机协议主键
     * @return 结果
     */
    public int deleteCaptainDriverProtocolById(Long id);

    /**
     * 批量删除车队长司机协议
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCaptainDriverProtocolByIds(Long[] ids);

    int logicDeleteCaptainDriverProtocol(@Param("carCaptainId") Long carCaptainId, @Param("driverId")Long driverId, @Param("resource")int resource);

	int insertCaptainDriverProtocolList(List<CaptainDriverProtocol> insertCaptainDriverProtocolList);

    List<CaptainDriverProtocol> selectCaptainDriverProtocolListByIdentityCards(List<String> identityCards);

    int deleteCaptainDriverProtocolByCaptainIds(List<Long> captainIds);
}
