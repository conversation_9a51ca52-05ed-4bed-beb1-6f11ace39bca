package com.zly.project.carrier.carCaptain.mapper;


import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.zly.project.carrier.carCaptain.domain.CaptainVehicleRelation;

/**
 * 车队长车辆关系Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-27
 */
public interface CaptainVehicleRelationMapper {
    /**
     * 查询车队长车辆关系
     *
     * @param carCaptainId 车队长车辆关系主键
     * @return 车队长车辆关系
     */
    public CaptainVehicleRelation selectCaptainVehicleRelationByCarCaptainId(Long carCaptainId);

    /**
     * 查询车队长车辆关系列表
     *
     * @param captainVehicleRelation 车队长车辆关系
     * @return 车队长车辆关系集合
     */
    public List<CaptainVehicleRelation> selectCaptainVehicleRelationList(CaptainVehicleRelation captainVehicleRelation);

    /**
     * 新增车队长车辆关系
     *
     * @param captainVehicleRelation 车队长车辆关系
     * @return 结果
     */
    public int insertCaptainVehicleRelation(CaptainVehicleRelation captainVehicleRelation);

    /**
     * 修改车队长车辆关系
     *
     * @param captainVehicleRelation 车队长车辆关系
     * @return 结果
     */
    public int updateCaptainVehicleRelation(CaptainVehicleRelation captainVehicleRelation);

    /**
     * 删除车队长车辆关系
     *
     * @param carCaptainId 车队长车辆关系主键
     * @return 结果
     */
    public int deleteCaptainVehicleRelationByCarCaptainId(Long carCaptainId);

    /**
     * 批量删除车队长车辆关系
     *
     * @param carCaptainIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCaptainVehicleRelationByCarCaptainIds(List<Long> carCaptainIds);

    int deleteCaptainVehicleRelation(@Param("carCaptainId") Long carCaptainId, @Param("vehicleId")Long vehicleId);

	int insertCaptainVehicleRelationList(List<CaptainVehicleRelation> insertCaptainVehicleRelationList);
}
