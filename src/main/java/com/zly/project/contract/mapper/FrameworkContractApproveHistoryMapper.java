package com.zly.project.contract.mapper;

import com.zly.project.contract.domain.FrameworkContractApproveHistory;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-09-19
 */
public interface FrameworkContractApproveHistoryMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public FrameworkContractApproveHistory selectFrameworkContractApproveHistoryById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param frameworkContractApproveHistory 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<FrameworkContractApproveHistory> selectFrameworkContractApproveHistoryList(FrameworkContractApproveHistory frameworkContractApproveHistory);

    /**
     * 新增【请填写功能名称】
     * 
     * @param frameworkContractApproveHistory 【请填写功能名称】
     * @return 结果
     */
    public int insertFrameworkContractApproveHistory(FrameworkContractApproveHistory frameworkContractApproveHistory);

    /**
     * 修改【请填写功能名称】
     * 
     * @param frameworkContractApproveHistory 【请填写功能名称】
     * @return 结果
     */
    public int updateFrameworkContractApproveHistory(FrameworkContractApproveHistory frameworkContractApproveHistory);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteFrameworkContractApproveHistoryById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFrameworkContractApproveHistoryByIds(Long[] ids);

    /**
     * 删除【根据合约id】
     *
     * @param ids 【请填写功能名称】合约id
     * @return 结果
     */
    public int deleteFrameworkContractApproveHistoryByContractIds(Long[] ids);
}
