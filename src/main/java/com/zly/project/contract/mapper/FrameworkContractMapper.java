package com.zly.project.contract.mapper;

import java.util.List;

import com.zly.project.contract.domain.*;
import org.apache.ibatis.annotations.Param;

import com.zly.project.agentOperation.domain.req.AgentContractQueryReq;
import com.zly.project.financeOperation.domain.res.FinanceContractQueryRes;

/**
 * 项目合约Mapper接口
 *
 * <AUTHOR>
 * @date 2022-06-07
 */
public interface FrameworkContractMapper {
	/**
	 * 查询项目合约
	 *
	 * @param id
	 *            项目合约主键
	 * @return 项目合约
	 */
	FrameworkContract selectFrameworkContractById(Long id);

	/**
	 * 查询项目合约列表
	 *
	 * @param frameworkContract
	 *            项目合约
	 * @return 项目合约集合
	 */
	List<FrameworkContract> selectFrameworkContractList(FrameworkContract frameworkContract);

	List<FrameworkContract> selectFrameworkContractSubcontractedList(FrameworkContract frameworkContract);

	/**
	 * 新增项目合约
	 *
	 * @param frameworkContract
	 *            项目合约
	 * @return 结果
	 */
	int insertFrameworkContract(FrameworkContract frameworkContract);

	/**
	 * 修改项目合约
	 *
	 * @param frameworkContract
	 *            项目合约
	 * @return 结果
	 */
	int updateFrameworkContract(FrameworkContract frameworkContract);

	/**
	 * 删除项目合约
	 *
	 * @param id
	 *            项目合约主键
	 * @return 结果
	 */
	int deleteFrameworkContractById(Long id);

	/**
	 * 批量删除项目合约
	 *
	 * @param ids
	 *            需要删除的数据主键集合
	 * @return 结果
	 */
	int deleteFrameworkContractByIds(Long[] ids);

	List<FrameworkContractRes> selectContractList(FrameworkContract frameworkContract);

	/**
	 * 一星期后将到期的平台合约
	 * 
	 * @return
	 */
	List<FrameworkContract> frameworkContractStateFromAWeekExpired();

	List<FrameworkContract> selectFrameworkContractByIds(List<Long> ids);

	List<FrameworkContract> selectAuthContractList(FrameworkContractReq contractReq);

	List<ContractSimpleRes> selectSearchValueByForwarderId(ContractSrarchReq req);

	List<FrameworkContractRes> selectContractListHasLimit(AgentContractQueryReq req);

	List<FinanceContractQueryRes> selectFinanceContractList(FrameworkContractReq req);

	List<FrameworkContractRes> selectAuthContractResList(FrameworkContractReq contractReq);

	List<FrameworkContractRes> selectAgentProjectList(AgentContractQueryReq req);

    Integer selectContractListTotal(FrameworkContractRes frameworkContract);

	Integer insertFrameworkContracts(List<FrameworkContract> contracts);

	List<FrameworkContract> selectForwarderContractList(FrameworkContractReq forwarderSearchReq);

	List<FrameworkContract> selectByIdsAndModelId(List<Long> contractIds, Long modelId);
}
