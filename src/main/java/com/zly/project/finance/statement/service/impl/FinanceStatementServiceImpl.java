package com.zly.project.finance.statement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zly.common.constant.BusinessConstants;
import com.zly.common.constant.FileNames;
import com.zly.common.constant.HttpStatus;
import com.zly.common.constant.TaxUploadConstants;
import com.zly.common.enums.ClientType;
import com.zly.common.utils.CodeUtil;
import com.zly.common.utils.CommonUtil;
import com.zly.common.utils.DateUtils;
import com.zly.common.utils.PageUtils;
import com.zly.common.utils.QiNiuUtil;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.StringUtils;
import com.zly.common.utils.TextUtil;
import com.zly.common.utils.bean.BeanUtils;
import com.zly.common.utils.poi.ExcelUtil;
import com.zly.framework.config.CustomConfig;
import com.zly.framework.security.LoginUser;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.project.backgroundOperation.domain.BusinessModel;
import com.zly.project.backgroundOperation.service.BusinessModelService;
import com.zly.project.backgroundOperation.service.FourDataOriginalService;
import com.zly.project.common.domain.SysAttachmentInfo;
import com.zly.project.common.service.ISysAttachmentInfoService;
import com.zly.project.consignor.customer.domain.CustomerInfo;
import com.zly.project.consignor.customer.mapper.CustomerInfoMapper;
import com.zly.project.consignor.customer.service.impl.CustomerInfoServiceImpl;
import com.zly.project.contract.domain.FrameworkContract;
import com.zly.project.contract.service.impl.FrameworkContractServiceImpl;
import com.zly.project.es.waybill.service.EsWaybillService;
import com.zly.project.finance.statement.domain.BillDetail;
import com.zly.project.finance.statement.domain.FinanceStatement;
import com.zly.project.finance.statement.domain.FinanceStatementBill;
import com.zly.project.finance.statement.domain.FinanceStatementEx;
import com.zly.project.finance.statement.domain.FinanceStatementWaybill;
import com.zly.project.finance.statement.domain.InvoiceHeader;
import com.zly.project.finance.statement.domain.InvoicingBillMsg;
import com.zly.project.finance.statement.domain.PaymentAttachmentInfo;
import com.zly.project.finance.statement.domain.req.FinanceStatementContractRelation;
import com.zly.project.finance.statement.domain.req.FinanceStatementRejectReq;
import com.zly.project.finance.statement.domain.req.FinanceStatementReq;
import com.zly.project.finance.statement.domain.req.FinanceStatementSearchReq;
import com.zly.project.finance.statement.domain.res.BillBusinessRes;
import com.zly.project.finance.statement.domain.res.BillDetailRes;
import com.zly.project.finance.statement.domain.res.FileApplyForInvoiceRes;
import com.zly.project.finance.statement.domain.res.FinancePlateFormRes;
import com.zly.project.finance.statement.domain.res.FinanceStatementApproveInfo;
import com.zly.project.finance.statement.domain.res.FinanceStatementPayQueryRes;
import com.zly.project.finance.statement.domain.res.FinanceStatementRes;
import com.zly.project.finance.statement.domain.res.StatementInfoRes;
import com.zly.project.finance.statement.mapper.FinanceStatementContractRelationMapper;
import com.zly.project.finance.statement.mapper.FinanceStatementMapper;
import com.zly.project.finance.statement.mapper.FinanceStatementMapperEx;
import com.zly.project.finance.statement.mapper.FinanceStatementWaybillMapper;
import com.zly.project.finance.statement.mapper.FinanceStatementWaybillMapperEx;
import com.zly.project.finance.statement.service.IFinanceStatementBillService;
import com.zly.project.finance.statement.service.IFinanceStatementService;
import com.zly.project.financeOperation.domain.res.BillMarkRecordRes;
import com.zly.project.financeOperation.domain.res.StatementRecordRes;
import com.zly.project.financeOperation.service.FinanceUserService;
import com.zly.project.freightforwarder.domain.FreightForwarderConfig;
import com.zly.project.freightforwarder.domain.FreightForwarderInfo;
import com.zly.project.freightforwarder.service.FreightForwarderConfigService;
import com.zly.project.freightforwarder.service.impl.FreightForwarderInfoServiceImpl;
import com.zly.project.group.domain.FrameworkContractSubchain;
import com.zly.project.group.domain.FrameworkContractSubchainEx;
import com.zly.project.group.domain.WaybillContractChain;
import com.zly.project.group.domain.WaybillCustomerRelation;
import com.zly.project.group.mapper.FrameworkContractSubchainMapper;
import com.zly.project.group.mapper.FrameworkContractSubchainMapperEx;
import com.zly.project.group.mapper.WaybillCustomerRelationMapper;
import com.zly.project.group.service.IWaybillCustomerService;
import com.zly.project.group.service.impl.FrameworkContractSubchainServiceImpl;
import com.zly.project.makecode.domain.MakeCode;
import com.zly.project.makecode.mapper.MakeCodeMapper;
import com.zly.project.projectGroup.service.ProjectAssociationUserService;
import com.zly.project.settlement.domain.request.huaxia.DownLoadReq;
import com.zly.project.settlement.service.huaxia.HuaXiaService;
import com.zly.project.statistics.domain.BillStatisticsInfo;
import com.zly.project.system.service.impl.SysClientLogService;
import com.zly.project.taxUpload.service.YwkUploadService;
import com.zly.project.tenant.service.impl.TenantUserServiceImpl;
import com.zly.project.transport.waybill.domain.Column;
import com.zly.project.transport.waybill.domain.Waybill;
import com.zly.project.transport.waybill.domain.WaybillSlave;
import com.zly.project.transport.waybill.domain.res.WaybillRes;
import com.zly.project.transport.waybill.mapper.WaybillMapper;
import com.zly.project.transport.waybill.mapper.WaybillMapperEx;
import com.zly.project.transport.waybill.service.IWaybillContractChainService;
import com.zly.project.transport.waybill.service.IWaybillService;
import com.zly.project.transport.waybill.service.IWaybillSlaveService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zly.common.enums.ClientType.FINANCE;
import static com.zly.common.enums.ClientType.PLATFORM;
import static com.zly.common.enums.ClientType.SHIPPER;

/**
 * 开票Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-11-22
 */
@Service
@Slf4j
public class FinanceStatementServiceImpl implements IFinanceStatementService {

	@Resource
	private FinanceStatementMapper financeStatementMapper;
	@Resource
	private FinanceStatementMapperEx financeStatementMapperEx;
	@Resource
	private IWaybillService waybillService;
	@Resource
	private EsWaybillService esWaybillService;
	@Resource
	private FinanceStatementWaybillMapper financeStatementWaybillMapper;
	@Resource
	private FinanceStatementWaybillMapperEx financeStatementWaybillMapperEx;
	@Resource
	private CustomerInfoMapper customerInfoMapper;
	@Resource
	private WaybillCustomerRelationMapper waybillCustomerRelationMapper;
	@Resource
	private IWaybillCustomerService waybillCustomerService;
	@Resource
	private IWaybillContractChainService waybillContractChainService;
	@Resource
	private FrameworkContractSubchainMapper contractSubchainMapper;
	@Resource
	private MakeCodeMapper makeCodeMapper;
	@Resource
	private IWaybillSlaveService waybillSlaveService;
	@Resource
	private ISysAttachmentInfoService sysAttachmentInfoService;
	@Resource
	private IFinanceStatementBillService financeStatementBillService;
	@Resource
	private FrameworkContractServiceImpl frameworkContractService;
	@Resource
	private SysClientLogService sysClientLogService;
	@Resource
	private WaybillMapperEx waybillMapperEx;

	@Resource
	private FinanceStatementContractRelationMapper financeStatementContractRelationMapper;
	@Resource
	private FrameworkContractSubchainMapperEx frameworkContractSubchainMapperEx;
	@Resource
	private CustomerInfoServiceImpl customerInfoService;
	@Resource
	private ProjectAssociationUserService projectAssociationUserService;
	@Resource
	private YwkUploadService ywkUploadService;
	@Resource
	private FourDataOriginalService fourDataOriginalService;
	@Autowired
	private WaybillMapper waybillMapper;
	@Resource
	private FinanceUserService financeUserService;
	@Resource
	private TenantUserServiceImpl tenantUserService;
	@Resource
	private FreightForwarderConfigService freightForwarderConfigService;
	@Resource
	private BusinessModelService businessModelService;

	@Value("${platform.pay.task.downloadUrl}")
	private String downloadUrl;
	@Value("${platform.pay.account.header.acc}")
	private String accountAcc;
	@Value("${platform.pay.account.header.pas}")
	private String accountPas;
	@Resource
	private CustomConfig customConfig;

	/**
	 * 查询开票
	 *
	 * @param id
	 * 		开票主键
	 * @return 开票
	 */
	@Override
	public FinanceStatement selectFinanceStatementById(Long id) {
		return financeStatementMapper.selectFinanceStatementById(id);
	}

	/**
	 * 查询开票列表
	 *
	 * @param financeStatement
	 * 		开票
	 * @return 开票
	 */
	@Override
	public List<FinanceStatement> selectFinanceStatementList(FinanceStatement financeStatement) {
		return financeStatementMapper.selectFinanceStatementList(financeStatement);
	}

	/**
	 * 新增开票
	 *
	 * @param financeStatement
	 * 		开票
	 * @return 结果
	 */
	@Override
	public int insertFinanceStatement(FinanceStatement financeStatement) {
		if (financeStatement.getStatementDirection() == null) {
			financeStatement.setStatementDirection(1); // 默认值为与系统内单位对账
		}
		fillBuyerAndSellerInfo(financeStatement);
		financeStatement.setCreateTime(DateUtils.getNowDate());
		return financeStatementMapper.insertFinanceStatement(financeStatement);
	}

	public void fillBuyerAndSellerInfo(FinanceStatement statement) {
		// 仅系统内单位对账开票需填充买卖方信息
		if (statement.getStatementDirection() == 2) {
			return;
		}
		// 买方信息
		Long buyerId = statement.getCustomerId();
		CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(buyerId);
		InvoiceHeader buyerInfo = JSON.parseObject(JSON.toJSONString(customerInfo), InvoiceHeader.class);
		statement.setBuyerInfo(JSON.toJSONString(buyerInfo));

		// 卖方信息
		InvoiceHeader sellerInfo = new InvoiceHeader();
		if (CommonUtil.isEmptyOrZero(statement.getAuditFreightForwarderId())) {
			Long sellerId = statement.getAuditCustomerId();
			customerInfo = customerInfoService.selectCustomerInfoById(sellerId);
			sellerInfo = JSON.parseObject(JSON.toJSONString(customerInfo), InvoiceHeader.class);
		} else {
			FreightForwarderInfo forwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(statement.getAuditFreightForwarderId());
			sellerInfo.setTaxNo(forwarderInfo.getSocialCreditCode());
			sellerInfo.setBillHead(forwarderInfo.getInvoiceHeader());
			sellerInfo.setBank(forwarderInfo.getBankName());
			sellerInfo.setTelephone(forwarderInfo.getPhone());
			sellerInfo.setBankAccount(forwarderInfo.getBankCard());
			sellerInfo.setUnitAddress(forwarderInfo.getAddress());
		}
		statement.setSellerInfo(JSON.toJSONString(sellerInfo));
	}

	@Override
	public FinanceStatementRes selectStatemenMoneyTotal(Map<String, Object> map) {
		if (map.containsKey("shippingNoteNumber")) {
			String shippingNoteNumber = (String) map.get("shippingNoteNumber");
			if (StringUtils.isNotEmpty(shippingNoteNumber)) {
				Long aLong = financeStatementWaybillMapper.selectStatementIdByShippingNoteNumber(shippingNoteNumber);
				map.put("statementId", aLong == null ? 0 : aLong);
			}
		}
		if (map.containsKey("auditFreightForwarderId")) {
			Long auditFreightForwarderId = (Long) map.get("auditFreightForwarderId");
			if (auditFreightForwarderId != null) {
				map.put("statementIds", financeStatementMapperEx.selectStatementIdList(auditFreightForwarderId, null));
			}
		}
		return financeStatementMapperEx.selectStatemenMoneyTotal(map);
	}

	/**
	 * 修改开票
	 *
	 * @param financeStatement
	 * 		开票
	 * @return 结果
	 */
	@Override
	public int updateFinanceStatement(FinanceStatement financeStatement) {
		financeStatement.setUpdateTime(DateUtils.getNowDate());
		return financeStatementMapper.updateFinanceStatement(financeStatement);
	}

	@Override
	public List<FinanceStatement> selectFinanceStatementByStatementNo(String statementNo) {
		return financeStatementMapper.selectFinanceStatementByStatementNo(statementNo);
	}

	@Override
	public StatementInfoRes statementInfoList(Long id) {
		// 根据 id 查询申请单号
		FinanceStatement financeStatement = financeStatementMapper.selectFinanceStatementById(id);
		if (financeStatement == null) {
			return new StatementInfoRes();
		}

		FinanceStatementWaybill financeStatementWaybillQuery = new FinanceStatementWaybill();
		financeStatementWaybillQuery.setStatementId(financeStatement.getId());
		List<FinanceStatementWaybill> financeStatementWaybills = financeStatementWaybillMapper.selectFinanceStatementWaybillList(financeStatementWaybillQuery);

		Map<Integer, Integer> riskCountMap = new HashMap<>();
		List<Waybill> waybillList = financeStatementWaybills.stream().map(financeStatementWaybill -> {
					Waybill waybillExist = waybillMapper.selectWaybillById(financeStatementWaybill.getWaybillId());
					if (waybillExist == null)
						return null;

					Waybill waybill = new Waybill();
					waybill.setId(waybillExist.getId());
					waybill.setShippingNoteNumber(waybillExist.getShippingNoteNumber());
					waybill.setFrameworkContractName(waybillExist.getFrameworkContractName());
					waybill.setRiskResultGrade(waybillExist.getRiskResultGrade());

					// 统计风险等级
					riskCountMap.merge(waybillExist.getRiskResultGrade(), 1, Integer::sum);

					return waybill;
				}).filter(Objects::nonNull) // 过滤掉空对象
				.collect(Collectors.toList());

		StatementInfoRes statementInfoRes = new StatementInfoRes();
		statementInfoRes.setWaybillList(waybillList);

		statementInfoRes.setHighRiskCount(riskCountMap.getOrDefault(1, 0));
		statementInfoRes.setMiddleRiskCount(riskCountMap.getOrDefault(2, 0));
		statementInfoRes.setLowRiskCount(riskCountMap.getOrDefault(3, 0));
		statementInfoRes.setNormalCount(riskCountMap.getOrDefault(4, 0));
		statementInfoRes.setNotYetCount(riskCountMap.getOrDefault(-1, 0));
		statementInfoRes.setOngoingCount(riskCountMap.getOrDefault(0, 0));

		return statementInfoRes;

	}

	/**
	 * 批量删除开票
	 *
	 * @param ids
	 * 		需要删除的开票主键
	 * @return 结果
	 */
	@Override
	public int deleteFinanceStatementByIds(Long[] ids) {
		return financeStatementMapper.deleteFinanceStatementByIds(ids);
	}

	/**
	 * 删除开票信息
	 *
	 * @param id
	 * 		开票主键
	 * @return 结果
	 */
	@Override
	public int deleteFinanceStatementById(Long id) {
		return financeStatementMapper.deleteFinanceStatementById(id);
	}

	@Resource
	private PaymentAttachmentInfoServiceImpl paymentAttachmentInfoService;

	/**
	 * 托运人按运单申请开票
	 *
	 * @param waybillIds
	 * @param customerInfo
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public AjaxResult statementApply(List<Long> waybillIds, CustomerInfo customerInfo, FinanceStatementReq req) {
		String billMsg = req.getInvoicingBillMsg();
		Integer isSales = req.getIsSales();
		if (req.getAuditType() == null){
			return AjaxResult.error("请选择审批模式");
		}

		if (StringUtils.isNotEmpty(req.getBillRemark())){
			if (req.getBillRemark().length() > 500){
				return AjaxResult.error("发票备注不能超过500字");
			}
		}

		List<Long> statementIds = financeStatementWaybillMapper.selectStatementIdByWaybillIds(waybillIds,customerInfo.getId());
		if (statementIds != null && statementIds.size() > 0){
			return AjaxResult.error("请勿重复申请");
		}

		// 1、基础信息查询
		List<WaybillRes> waybillList = selectStatementWaybillList(waybillIds);
		// 2、校验有效性，校验成功时返回项目转包链中该托运人的下一层（审核人信息）
		List<WaybillCustomerRelation> canApplyWaybills = waybillCustomerService.selectCanApplyInvoiceWaybills(customerInfo.getId(), waybillIds);
		AjaxResult verifyResult = verifyCanApplyInvoiceNew(waybillList, waybillIds, customerInfo.getId(), canApplyWaybills);
		if (verifyResult.isNotSuccess()) {
			return verifyResult;
		}
		waybillIds = waybillList.stream().map(w -> w.getId()).collect(Collectors.toList());
		Map<Long, WaybillRes> waybillResMap = waybillList.stream().collect(Collectors.toMap(WaybillRes::getId, Function.identity()));
		// 3、获取下一层开票审核人信息
		FrameworkContractSubchain subchain = (FrameworkContractSubchain) verifyResult.get("data");
		Long auditCustomerId = subchain.getCustomerId();
		Long auditFreightForwarderId = subchain.getIsLeaf() == 0 ? subchain.getFreightForwarderId() : 0;

		if (auditFreightForwarderId != 0) {
			FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(auditFreightForwarderId);
			if ("善道智运网络货运平台".equals(freightForwarderInfo.getName())) {
				if (StringUtils.isEmpty(req.getEmail())) {
					return AjaxResult.error("邮箱必填");
				}
				if (req.getProfitState() == 2) {
					if (StringUtils.isEmpty(req.getPaymentDate())) {
						return AjaxResult.error("运输费支付日期必填");
					}
					if (req.getList() == null || req.getList().size() == 0) {
						return AjaxResult.error("运输费支付凭证必填");
					}
				}
			}

			// 如果没有邮箱
			FreightForwarderConfig config = freightForwarderConfigService.selectFreightForwarderConfigById(auditFreightForwarderId);
			if (config.getIsEmail() == 1){
				req.setEmail(null);
			}
		}else {
			req.setEmail(null);
		}

		// 4、根据实际付款计算开票金额
		// 申请开票的总金额
		FileApplyForInvoiceRes res = new FileApplyForInvoiceRes();
		Map<Long, BigDecimal> rateMap = canApplyWaybills.stream().collect(Collectors.toMap(WaybillCustomerRelation::getWaybillId, a -> a.getRateToMe()));
		AjaxResult caculateFareResult = caculateFare(res, waybillList, rateMap);
		if (caculateFareResult.isNotSuccess()) {
			return caculateFareResult;
		}
		BigDecimal invoiceFare = res.getInvoiceFare();
		// 5、插入开票申请表
		Long statementId = TextUtil.getTimeSequenceID(5);
		int riskGrade = waybillList.stream().filter(f -> Arrays.asList(1, 2, 3, 4).contains(f.getRiskResultGrade())).mapToInt(WaybillRes::getRiskResultGrade).min() // 获取最小值
				.orElse(-1);
		// 原：没有项目限制，取一条运单的项目id插入
		// 现在：先票后款修改：支持多个项目，从运单集合里去重筛选出项目id并传入，项目id类型改成String

		FinanceStatement financeStatement = generateFinanceStatement(statementId, waybillList, customerInfo);
		financeStatement.setWaybillAmount(waybillIds.size());// 运单数量
		financeStatement.setReceiveFare(invoiceFare);// 开票应收总金额
		financeStatement.setInvoiceFare(invoiceFare);// 开票总金额
		financeStatement.setAuditCustomerId(auditCustomerId); // 审核的托运人ID
		financeStatement.setAuditFreightForwarderId(auditFreightForwarderId); // 审核的网络货运人ID
		financeStatement.setApplyBillMsg(billMsg);
		financeStatement.setIsSales(isSales);
		financeStatement.setRiskResultGrade(riskGrade);
		financeStatement.setBillRemark(req.getBillRemark());
		financeStatement.setState(auditFreightForwarderId == 0 ? 2 : 7);
		if (req.getAuditType() == 1){
			financeStatement.setState(2);
		}
		financeStatement.setEmail(req.getEmail());
		financeStatement.setAuditType(req.getAuditType());
		financeStatement.setSendDdState(req.getAuditType() == 0 ? 0 : 1);
		if (req.getPaymentType() != null) {
			financeStatement.setPaymentType(req.getPaymentType());
			if (req.getPaymentType() == 1) {
				financeStatement.setPayReturnState(0);
			}
		}

		// profitState 利润状态(0未支付(线上)  1已支付(线上)  2已支付(线下) 3未支付(线下) 4付款中 5线上支付失败)
		financeStatement.setProfitState(req.getProfitState());

		financeStatement.setPaymentDate(req.getPaymentDate());
		Long shipperId = SecurityUtils.getShipperId();
		CustomerInfo nextName = customerInfoMapper.selectCustomerInfoById(auditCustomerId);
		CustomerInfo name = customerInfoMapper.selectCustomerInfoById(shipperId);
		String salesName = name.getCustomerName() + "(" + nextName.getCustomerName() + ")销货清单";
		String transportationName = name.getCustomerName() + "(" + nextName.getCustomerName() + ")运输清单";
		financeStatement.setSalesName(salesName);
		financeStatement.setTransportationName(transportationName);

		// 插入附件表
		List<PaymentAttachmentInfo> list = req.getList();
		if (list != null && list.size() > 0) {
			for (PaymentAttachmentInfo paymentAttachmentInfo : list) {
				paymentAttachmentInfo.setId(TextUtil.getTimeSequenceID(5));
				paymentAttachmentInfo.setRelationId(financeStatement.getId());
				paymentAttachmentInfo.setCreateTime(new Date());
				paymentAttachmentInfo.setUpdateTime(new Date());
				paymentAttachmentInfo.setCreateBy(SecurityUtils.getNickname());
				paymentAttachmentInfo.setUpdateBy(SecurityUtils.getNickname());
				paymentAttachmentInfoService.insertPaymentAttachmentInfo(paymentAttachmentInfo);
			}
		}

		// 更新用户邮箱
		if (SHIPPER.equals(SecurityUtils.getClientType()) && StringUtils.isNotEmpty(req.getEmail())) {
			tenantUserService.updateUserLastEmail(SecurityUtils.getUserId(), req.getEmail());
		}
		if (FINANCE.equals(SecurityUtils.getClientType()) && StringUtils.isNotEmpty(req.getEmail())) {
			financeUserService.updateUserLastEmail(SecurityUtils.getUserId(), req.getEmail());
		}

		if (!StringUtils.isEmpty(billMsg)) {
			InvoicingBillMsg invoicingBillMsg = JSONObject.parseObject(billMsg, InvoicingBillMsg.class);
			financeStatement.setRemark(invoicingBillMsg.getRemark());
		}
		insertFinanceStatement(financeStatement);

		StringBuilder waybillCode = new StringBuilder();
		// 6、保存开票的运单清单信息并更新运单的开票用的应收运费
		List<FinanceStatementWaybill> statementWaybills = new ArrayList<>();
		List<Waybill> waybills = waybillMapper.selectWaybillByIds(waybillIds);
		for (Waybill waybill : waybills) {
			BigDecimal invoiceFareSingle = waybill.getActualFare().divide(BigDecimal.ONE.subtract(rateMap.get(waybill.getId())), RoundingMode.HALF_UP);
			statementWaybills.add(generateFinanceStatementWaybill(waybillResMap.get(waybill.getId()), statementId, invoiceFareSingle));
			// 7、运单的开票用的应收运费及结算状态重新赋值
			waybill.setStatementReceiveFare(invoiceFareSingle);
			waybill.setBillStatus(3);// 开票中
			waybillCode.append(waybill.getShippingNoteNumber());
		}
		// 运单表中开票应收运费、开票状态修改
		waybillMapper.updateWaybillStatementReceiveFareAndBillStatusByWaybillIds(waybills, 3, waybillIds);
		// 7、开票运单表数据插入
		financeStatementWaybillMapper.insertFinanceStatementWaybills(statementWaybills);
		// 8、更新运单托运人关联表状态--开票中
		waybillCustomerRelationMapper.updateWaybillStatementStatus(BusinessConstants.WAYBILL_INVOICE_STATE_INVOICING, waybillIds, customerInfo.getId());
		// 9、修改es中运单
		esWaybillService.updateEsWaybillInfos(waybills);
		List<WaybillCustomerRelation> relations = waybillCustomerRelationMapper.selectByWaybillIdsAndCustomerId(waybillIds, customerInfo.getId());
		for (WaybillCustomerRelation relation : relations) {
			relation.setWaybillState(BusinessConstants.WAYBILL_INVOICE_STATE_INVOICING);
		}
		esWaybillService.updateEsRelationInfos(relations);

		// 10、日志记录
		String actionName = "申请开票，请求参数：运单号：" + waybillCode + "，" + JSONUtil.toJsonStr(customerInfo);
		sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_INVOICING, BusinessConstants.ACTION_TYPE_ADD, actionName, financeStatement.getStatementNo());

		return AjaxResult.success("申请开票成功", statementId);
	}

	@Resource
	private HuaXiaService huaxiaservice;

	/**
	 * 托运人按运单申请开票
	 *
	 * @param waybillIds
	 * @param buyCustomerInfo
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public AjaxResult applyForInvoicing(List<Long> waybillIds, CustomerInfo buyCustomerInfo, FinanceStatementReq req, Boolean isDD) {

		List<String> errorMessages = new ArrayList<>();
		// 1、基础信息查询
		long l1 = System.currentTimeMillis();
		List<WaybillRes> waybillList = selectStatementWaybillList(waybillIds);
		long l2 = System.currentTimeMillis();

		if (StringUtils.isNotEmpty(req.getBillRemark())){
			if (req.getBillRemark().length() > 500){
				return AjaxResult.error("发票备注不能超过500字");
			}
		}

		log.info("通用申请开票查询运单用时" + String.valueOf(l2 - l1));
		// 2、校验有效性，校验成功时返回项目转包链中该托运人的下一层（审核人信息）
		List<WaybillCustomerRelation> canApplyWaybills = waybillCustomerService.selectCanApplyInvoiceWaybills(buyCustomerInfo.getId(), waybillIds);
		long l3 = System.currentTimeMillis();
		log.info("通用申请开票查询转包用时" + String.valueOf(l3 - l2));
		List<String> errorMessage = verifyApplyInvoice(waybillList, waybillIds, buyCustomerInfo.getId(), canApplyWaybills, req, errorMessages);
		long l4 = System.currentTimeMillis();
		log.info("通用申请开票校验用时" + String.valueOf(l4 - l3));
		if (!errorMessage.isEmpty()) {
			String errorMessageString = String.join(";", errorMessage);
			return AjaxResult.error(errorMessageString);
		}

		List<WaybillContractChain> chains = waybillContractChainService.findByIds(waybillIds);
		Long chainId = chains.get(0).getChainId();
		FrameworkContractSubchain lowerSubhain = getLowerSubhain(chainId, buyCustomerInfo.getId());
		Long auditCustomerId = req.getNextCustomerId();
		Long auditFreightForwarderId = lowerSubhain.getIsLeaf() == 0 ? lowerSubhain.getFreightForwarderId() : 0;

		if (auditFreightForwarderId != 0) {
			FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(auditFreightForwarderId);
			if ("善道智运网络货运平台".equals(freightForwarderInfo.getName())) {
				if (StringUtils.isEmpty(req.getEmail())) {
					return AjaxResult.error("邮箱必填");
				}
				if (req.getProfitState() == 2) {
					if (StringUtils.isEmpty(req.getPaymentDate())) {
						return AjaxResult.error("运输费支付日期必填");
					}
					if (req.getList() == null || req.getList().size() == 0) {
						return AjaxResult.error("运输费支付凭证必填");
					}
				}
			}
		}

		// 4、根据实际付款计算开票金额
		// 申请开票的总金额
		FileApplyForInvoiceRes res = new FileApplyForInvoiceRes();
		Map<Long, BigDecimal> rateMap = canApplyWaybills.stream().collect(Collectors.toMap(WaybillCustomerRelation::getWaybillId, a -> a.getRateToMe()));
		AjaxResult caculateFareResult = caculateFare(res, waybillList, rateMap);
		if (caculateFareResult.isNotSuccess()) {
			return caculateFareResult;
		}
		long l5 = System.currentTimeMillis();
		log.info("通用申请开票其他校验用时" + String.valueOf(l5 - l4));
		BigDecimal invoiceFare = res.getInvoiceFare();
		// 5、插入开票申请表
		Long statementId = TextUtil.getTimeSequenceID(5);
		int riskGrade = waybillList.stream().filter(f -> Arrays.asList(1, 2, 3, 4).contains(f.getRiskResultGrade())).mapToInt(WaybillRes::getRiskResultGrade).min() // 获取最小值
				.orElse(-1);
		// 原：没有项目限制，取一条运单的项目id插入
		// 现在：先票后款修改：支持多个项目，从运单集合里去重筛选出项目id并传入，项目id类型改成String
		FinanceStatement financeStatement = generateFinanceStatement(statementId, waybillList, buyCustomerInfo);
		financeStatement.setWaybillAmount(waybillIds.size());// 运单数量
		financeStatement.setReceiveFare(invoiceFare);// 开票应收总金额
		financeStatement.setInvoiceFare(invoiceFare);// 开票总金额
		financeStatement.setAuditCustomerId(auditCustomerId); // 审核的托运人ID
		financeStatement.setAuditFreightForwarderId(auditFreightForwarderId); // 审核的网络货运人ID
		financeStatement.setApplyBillMsg(req.getInvoicingBillMsg());
		financeStatement.setIsSales(req.getIsSales());
		financeStatement.setStatementSource(0);
		financeStatement.setBillRemark(req.getBillRemark());
		financeStatement.setAuditType(req.getAuditType());
		if (isDD) {
			financeStatement.setState(2);
			financeStatement.setSendDdState(2);
		} else {
			financeStatement.setState(auditFreightForwarderId == 0 ? 2 : 7);
			financeStatement.setSendDdState(req.getAuditType() == 0 ? 0 : 1);
		}
		if (req.getAuditType() == 1){
			financeStatement.setState(2);
		}
		financeStatement.setRiskResultGrade(riskGrade);
		financeStatement.setEmail(req.getEmail());
		if (req.getPaymentType() != null) {
			financeStatement.setPaymentType(req.getPaymentType());
			if (req.getPaymentType() == 1) {
				financeStatement.setPayReturnState(0);
			}
		}

		// profitState 利润状态(0未支付(线上)  1已支付(线上)  2已支付(线下) 3未支付(线下) 4付款中 5线上支付失败)
		financeStatement.setProfitState(req.getProfitState());

		financeStatement.setPaymentDate(req.getPaymentDate());
		Long shipperId = SecurityUtils.getShipperId();
		CustomerInfo nextName = customerInfoMapper.selectCustomerInfoById(auditCustomerId);
		CustomerInfo name = customerInfoMapper.selectCustomerInfoById(shipperId);
		String salesName = name.getCustomerName() + "(" + nextName.getCustomerName() + ")销货清单";
		String transportationName = name.getCustomerName() + "(" + nextName.getCustomerName() + ")运输清单";
		financeStatement.setSalesName(salesName);
		financeStatement.setTransportationName(transportationName);
		if (!StringUtils.isEmpty(req.getInvoicingBillMsg())) {
			InvoicingBillMsg invoicingBillMsg = JSONObject.parseObject(req.getInvoicingBillMsg(), InvoicingBillMsg.class);
			financeStatement.setRemark(invoicingBillMsg.getRemark());
		}
		insertFinanceStatement(financeStatement);

		List<Waybill> waybills = new ArrayList<>();

		// 插入附件表
		List<PaymentAttachmentInfo> fileList = req.getList();
		if (fileList != null && fileList.size() > 0) {
			for (PaymentAttachmentInfo paymentAttachmentInfo : fileList) {
				paymentAttachmentInfo.setId(TextUtil.getTimeSequenceID(5));
				paymentAttachmentInfo.setRelationId(statementId);
				paymentAttachmentInfo.setCreateTime(new Date());
				paymentAttachmentInfo.setUpdateTime(new Date());
				paymentAttachmentInfo.setCreateBy(SecurityUtils.getNickname());
				paymentAttachmentInfo.setUpdateBy(SecurityUtils.getNickname());
				paymentAttachmentInfoService.insertPaymentAttachmentInfo(paymentAttachmentInfo);
			}
		}

		// 更新用户邮箱
		if (SHIPPER.equals(SecurityUtils.getClientType()) && StringUtils.isNotEmpty(req.getEmail())) {
			tenantUserService.updateUserLastEmail(SecurityUtils.getUserId(), req.getEmail());
		}
		if (FINANCE.equals(SecurityUtils.getClientType()) && StringUtils.isNotEmpty(req.getEmail())) {
			financeUserService.updateUserLastEmail(SecurityUtils.getUserId(), req.getEmail());
		}

		StringBuilder waybillCode = new StringBuilder();
		// 6、保存开票的运单清单信息并更新运单的开票用的应收运费
		for (WaybillRes waybillRes : waybillList) {
			BigDecimal invoiceFareSingle = waybillRes.getActualFare().divide(BigDecimal.ONE.subtract(rateMap.get(waybillRes.getId())), RoundingMode.HALF_UP);
			FinanceStatementWaybill fsw = generateFinanceStatementWaybill(waybillRes, statementId, invoiceFareSingle);
			financeStatementWaybillMapper.insertFinanceStatementWaybill(fsw);
			// 7、更新运单的开票用的应收运费及结算状态
			Waybill _waybill = waybillService.selectWaybillById(waybillRes.getId());
			_waybill.setId(waybillRes.getId());
			_waybill.setStatementReceiveFare(invoiceFareSingle);
			_waybill.setBillStatus(3);// 开票中
			waybillService.updateWaybill(_waybill);
			// 修改es中运单
			esWaybillService.updateEsWaybillInfo(_waybill);

			waybillCode.append(waybillRes.getShippingNoteNumber());

			waybills.add(_waybill);
		}
		// 8、更新运单托运人关联表状态--开票中
		waybillCustomerRelationMapper.updateWaybillStatementStatus(BusinessConstants.WAYBILL_INVOICE_STATE_INVOICING, waybillIds, buyCustomerInfo.getId());

		List<WaybillCustomerRelation> list = new ArrayList<>();
		for (Long waybillId : waybillIds) {
			WaybillCustomerRelation query = new WaybillCustomerRelation();
			query.setWaybillId(waybillId);
			query.setCustomerId(buyCustomerInfo.getId());
			List<WaybillCustomerRelation> relations = waybillCustomerRelationMapper.selectWaybillCustomerRelationList(query);

			for (WaybillCustomerRelation relation : relations) {
				relation.setWaybillState(BusinessConstants.WAYBILL_INVOICE_STATE_INVOICING);
			}
			list.addAll(relations);
		}
		esWaybillService.updateEsRelationInfos(list);

		//同步善道4.0
		//waybillService.pushWaybillReachShanDao(waybillIds,13);

		String actionName = "申请开票：运单号：" + waybillCode + "，" + JSONUtil.toJsonStr(buyCustomerInfo);
		actionName += "，请求参数：" + JSONUtil.toJsonStr(req);
		sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_INVOICING, BusinessConstants.ACTION_TYPE_OTHER, actionName, financeStatement.getStatementNo());

		return AjaxResult.success("申请开票成功", statementId);
	}

	@Override
	@Transactional
	public AjaxResult applyForInvoicingBehalf(List<Long> waybillIds, CustomerInfo sellCustomerInfo, FinanceStatementReq req) {

		// 1、基础信息查询
		List<WaybillRes> waybillList = selectStatementWaybillList(waybillIds);
		// 2、校验有效性，校验成功时返回项目转包链中该托运人的下一层（审核人信息）
		List<String> errorMessages = new ArrayList<>();
		List<WaybillCustomerRelation> waybillCustomerRelations = waybillCustomerService.selectApplyInvoiceWaybills(req.getAboveCustomerId(), waybillIds);
		// List<WaybillCustomerRelation> canApplyWaybills = waybillCustomerService.selectCanApplyInvoiceWaybills(sellCustomerInfo.getId(), waybillIds);
		List<String> errorMessage = verifyApplyInvoiceBehalf(waybillList, waybillIds, sellCustomerInfo.getId(), waybillCustomerRelations, req, errorMessages);
		if (!errorMessage.isEmpty()) {
			return new AjaxResult(HttpStatus.ERROR_NEXT, "校验失败", errorMessage);
		}

		List<WaybillContractChain> chains = waybillContractChainService.findByIds(waybillIds);
		Long chainId = chains.get(0).getChainId();
		FrameworkContractSubchain lowerSubhain = getLowerSubhain(chainId, sellCustomerInfo.getId());
		Long auditCustomerId = sellCustomerInfo.getId();
		Long auditFreightForwarderId = lowerSubhain.getIsLeaf() == 0 ? lowerSubhain.getFreightForwarderId() : 0;
		// 4、根据实际付款计算开票金额
		// 申请开票的总金额
		FileApplyForInvoiceRes res = new FileApplyForInvoiceRes();
		Map<Long, BigDecimal> rateMap = waybillCustomerRelations.stream().collect(Collectors.toMap(WaybillCustomerRelation::getWaybillId, a -> a.getRateToMe()));
		AjaxResult caculateFareResult = caculateFareBehalf(res, waybillList, rateMap);
		if (caculateFareResult.isNotSuccess()) {
			return caculateFareResult;
		}
		BigDecimal invoiceFare = res.getInvoiceFare();
		// 5、插入开票申请表
		Long statementId = TextUtil.getTimeSequenceID(5);
		// 原：没有项目限制，取一条运单的项目id插入
		// 现在：先票后款修改：支持多个项目，从运单集合里去重筛选出项目id并传入，项目id类型改成String
		CustomerInfo buyCustomerInfo = customerInfoService.selectCustomerInfoById(req.getAboveCustomerId());
		FinanceStatement financeStatement = generateFinanceStatement(statementId, waybillList, buyCustomerInfo);
		financeStatement.setWaybillAmount(waybillIds.size());// 运单数量
		financeStatement.setReceiveFare(invoiceFare);// 开票应收总金额
		financeStatement.setInvoiceFare(invoiceFare);// 开票总金额
		financeStatement.setAuditCustomerId(auditCustomerId); // 审核的托运人ID
		financeStatement.setAuditTime(new Date());
		financeStatement.setAuditFreightForwarderId(0L); // 审核的网络货运人ID
		financeStatement.setApplyBillMsg(req.getInvoicingBillMsg());
		financeStatement.setIsSales(req.getIsSales());
		financeStatement.setAuditType(req.getAuditType());
		// 开票来源 0:开票申请 1：代客户开票
		financeStatement.setStatementSource(1);
		financeStatement.setState(3);// 审批通过
		Long shipperId = SecurityUtils.getShipperId();
		CustomerInfo nextName = customerInfoMapper.selectCustomerInfoById(auditCustomerId);
		CustomerInfo name = customerInfoMapper.selectCustomerInfoById(shipperId);
		String salesName = name.getCustomerName() + "(" + nextName.getCustomerName() + ")销货清单";
		String transportationName = name.getCustomerName() + "(" + nextName.getCustomerName() + ")运输清单";
		financeStatement.setAuditTime(new Date());
		financeStatement.setSalesName(salesName);
		financeStatement.setTransportationName(transportationName);
		if (!StringUtils.isEmpty(req.getInvoicingBillMsg())) {
			InvoicingBillMsg invoicingBillMsg = JSONObject.parseObject(req.getInvoicingBillMsg(), InvoicingBillMsg.class);
			financeStatement.setRemark(invoicingBillMsg.getRemark());
		}
		insertFinanceStatement(financeStatement);

		StringBuilder waybillCode = new StringBuilder();
		// 6、保存开票的运单清单信息并更新运单的开票用的应收运费
		for (WaybillRes waybillRes : waybillList) {
			BigDecimal invoiceFareSingle = waybillRes.getPayFare().divide(BigDecimal.ONE.subtract(rateMap.get(waybillRes.getId())), RoundingMode.HALF_UP);
			FinanceStatementWaybill fsw = generateFinanceStatementWaybill(waybillRes, statementId, invoiceFareSingle);
			financeStatementWaybillMapper.insertFinanceStatementWaybill(fsw);
			// 7、更新运单的开票用的应收运费及结算状态
			Waybill _waybill = waybillService.selectWaybillById(waybillRes.getId());
			_waybill.setId(waybillRes.getId());
			_waybill.setStatementReceiveFare(invoiceFareSingle);
			_waybill.setBillStatus(3);// 开票中
			waybillService.updateWaybill(_waybill);
			// 修改es中运单
			esWaybillService.updateEsWaybillInfo(_waybill);

			waybillCode.append(waybillRes.getShippingNoteNumber());
		}
		// 8、更新运单托运人关联表状态--开票中
		waybillCustomerRelationMapper.updateWaybillStatementStatus(BusinessConstants.WAYBILL_INVOICE_STATE_INVOICING, waybillIds, req.getAboveCustomerId());

		List<WaybillCustomerRelation> list = new ArrayList<>();
		for (Long waybillId : waybillIds) {
			WaybillCustomerRelation query = new WaybillCustomerRelation();
			query.setWaybillId(waybillId);
			query.setCustomerId(sellCustomerInfo.getId());
			List<WaybillCustomerRelation> relations = waybillCustomerRelationMapper.selectWaybillCustomerRelationList(query);

			for (WaybillCustomerRelation relation : relations) {
				relation.setWaybillState(BusinessConstants.WAYBILL_INVOICE_STATE_INVOICING);
			}
			list.addAll(relations);
		}
		esWaybillService.updateEsRelationInfos(list);

		String actionName = "申请开票，运单号：" + waybillCode + "，" + JSONUtil.toJsonStr(sellCustomerInfo);
		actionName += "，请求参数：" + JSONUtil.toJsonStr(req);
		sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_INVOICING, BusinessConstants.ACTION_TYPE_ADD, actionName, financeStatement.getStatementNo());

		return AjaxResult.success("申请开票成功", statementId);
	}

	private FinanceStatementWaybill generateFinanceStatementWaybill(WaybillRes waybillRes, Long statementId, BigDecimal invoiceFareSingle) {
		FinanceStatementWaybill fsw = new FinanceStatementWaybill();
		BeanUtils.copyProperties(waybillRes, fsw);
		fsw.setId(TextUtil.getTimeSequenceID(5));
		fsw.setStatementId(statementId);// 对账单ID
		fsw.setWaybillId(waybillRes.getId());// 运单ID
		fsw.setReceiveFare(invoiceFareSingle);
		fsw.setWaybillCreateTime(waybillRes.getCreateTime());// 运单创建时间
		fsw.setCreateBy(SecurityUtils.getNickname());// 创建人
		fsw.setCreateTime(DateUtils.getNowDate());// 创建时间
		return fsw;
	}

	private FinanceStatement generateFinanceStatement(Long statementId, List<WaybillRes> waybillList, CustomerInfo customerInfo) {
		WaybillRes waybillRes = waybillList.get(0);
		FinanceStatement financeStatement = new FinanceStatement();
		Date now = new Date();
		String nickname = SecurityUtils.getNickname();
		financeStatement.setId(statementId);
		financeStatement.setStatementNo(CodeUtil.createFinanceStatementCode());// 对账单号
		financeStatement.setCustomerId(customerInfo.getId());// 购买方id
		financeStatement.setCustomerName(customerInfo.getCustomerName());// 客户名称
		// 筛选出运单中的项目id，单条的话插入原来的申请表，多条需要插入关联表，原来申请表的项目id，置为0
		List<Long> contractIds = waybillList.stream().map(WaybillRes::getFrameworkContractId).distinct().collect(Collectors.toList());
		FinanceStatementContractRelation financeStatementContractRelation = new FinanceStatementContractRelation();
		if (contractIds.size() == 1) {
			financeStatement.setContractId(waybillRes.getFrameworkContractId());// 项目id
			financeStatement.setContractName(waybillRes.getFrameworkContractName());// 项目名称
			financeStatementContractRelation.setStatementId(statementId);
			financeStatementContractRelation.setContractId(waybillRes.getFrameworkContractId().toString());
			financeStatementContractRelationMapper.insertFinanceStatementContractRelation(financeStatementContractRelation);
		} else if (contractIds.size() > 1) {
			financeStatement.setContractId(0L);
			// 多个项目对应一个开票申请单
			for (Long contractId : contractIds) {
				financeStatementContractRelation.setStatementId(statementId);
				financeStatementContractRelation.setContractId(contractId.toString());
				// 插入关联表
				financeStatementContractRelationMapper.insertFinanceStatementContractRelation(financeStatementContractRelation);
			}
			List<String> frameworkContractName = waybillList.stream().map(WaybillRes::getFrameworkContractName).distinct().collect(Collectors.toList());
			String concatenatedNames = String.join(",", frameworkContractName);
			financeStatement.setContractName(concatenatedNames);
		}

		financeStatement.setState(BusinessConstants.FINANCE_STATEMENT_INVOICING);// 状态设为2:开票中
		financeStatement.setCreateBy(nickname);// 创建人
		financeStatement.setCreateUserId(SecurityUtils.getUserId());// 创建人
		financeStatement.setCreateTime(now);// 创建时间
		financeStatement.setUpdateBy(nickname);// 修改人
		financeStatement.setUpdateTime(now);// 修改时间
		return financeStatement;
	}

	// 当前链条上，托运人的下游
	public FrameworkContractSubchain getLowerSubhain(Long chainId, Long customerId) {
		FrameworkContractSubchain search = new FrameworkContractSubchain();
		search.setChainId(chainId);
		List<FrameworkContractSubchain> chains = contractSubchainMapper.selectFrameworkContractSubchainList(search);
		Map<Long, List<FrameworkContractSubchain>> customer = chains.stream().collect(Collectors.groupingBy(FrameworkContractSubchain::getCustomerId));
		Map<Integer, List<FrameworkContractSubchain>> deep = chains.stream().collect(Collectors.groupingBy(FrameworkContractSubchain::getDeep));
		FrameworkContractSubchain customerChain = customer.get(customerId).get(0);
		if (customerChain.getIsLeaf() == 0)
			return null;
		return deep.get(customerChain.getDeep() + 1).get(0);
	}

	public AjaxResult caculateFare(FileApplyForInvoiceRes res, List<WaybillRes> waybillList, Map<Long, BigDecimal> rateMap) {
		BigDecimal actualFare = BigDecimal.ZERO;
		BigDecimal invoiceFare = BigDecimal.ZERO;
		List<BigDecimal> list = new ArrayList<>();
		String rate = "";
		// 1、计算
		for (WaybillRes waybill : waybillList) {

			actualFare = actualFare.add(waybill.getPayFare());
			BigDecimal invoiceFareSingle = waybill.getPayFare().divide(BigDecimal.ONE.subtract(rateMap.get(waybill.getId())), RoundingMode.HALF_UP);
			invoiceFare = invoiceFare.add(invoiceFareSingle);

			if (!list.contains(rateMap.get(waybill.getId()))) {
				list.add(rateMap.get(waybill.getId()));
			}
		}
		invoiceFare = invoiceFare.setScale(2, RoundingMode.HALF_UP);
		actualFare = actualFare.setScale(2, RoundingMode.HALF_UP);
		// 2、校验开票总金额
		if (invoiceFare.compareTo(BigDecimal.ZERO) < 1) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "开票总金额为空");
		}
		// 开票总金额限制最大金额为 100 万
		// if (invoiceFare.compareTo(BusinessConstants.WAYBILL_STATEMENT_MAX_MONEY) >= 0) {
		// String limit = StringUtils.formatNumber(BusinessConstants.WAYBILL_STATEMENT_MAX_MONEY);
		// return AjaxResult.error(HttpStatus.BAD_REQUEST, "开票总金额不能超过 " + limit + " 元");
		// }
		res.setInvoiceFare(invoiceFare);
		res.setTotalCarriage(actualFare);
		res.setTotalServiceFee(invoiceFare.subtract(actualFare));
		res.setContractId(waybillList.get(0).getFrameworkContractId());
		for (BigDecimal bigDecimal : list) {
			String formattedValue = StringUtils.DecimalFormat(bigDecimal);
			rate = "," + formattedValue + "% ";
		}
		res.setRate(rate);
		return AjaxResult.success(res);
	}

	public AjaxResult caculateFareBehalf(FileApplyForInvoiceRes res, List<WaybillRes> waybillList, Map<Long, BigDecimal> rateMap) {
		BigDecimal totalFare = BigDecimal.ZERO;
		BigDecimal invoiceFare = BigDecimal.ZERO;
		// 1、计算
		for (WaybillRes waybill : waybillList) {
			totalFare = totalFare.add(waybill.getPayFare());
			BigDecimal invoiceFareSingle = waybill.getPayFare().divide(BigDecimal.ONE.subtract(rateMap.get(waybill.getId())), RoundingMode.HALF_UP);
			invoiceFare = invoiceFare.add(invoiceFareSingle);
		}
		invoiceFare = invoiceFare.setScale(2, RoundingMode.HALF_UP);
		totalFare = totalFare.setScale(2, RoundingMode.HALF_UP);
		// 2、校验开票总金额
		if (invoiceFare.compareTo(BigDecimal.ZERO) < 1) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "开票总金额为空");
		}
		res.setInvoiceFare(invoiceFare);// 总开票金额
		res.setTotalCarriage(totalFare);// 总运费
		res.setTotalServiceFee(invoiceFare.subtract(totalFare));// 总服务费
		return AjaxResult.success(res);
	}

	public AjaxResult verifyCanApplyInvoiceNew(List<WaybillRes> waybillList, List<Long> waybillIds, Long customerId, List<WaybillCustomerRelation> canApplyWaybills) {
		// 1、运单是否为空
		if (waybillList.isEmpty()) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "未查询到申请开票的运单信息");
		}
		// 2、获取该托运人这些运单是否可申请开票
		if (canApplyWaybills == null || canApplyWaybills.size() < waybillIds.size()) {
			if (CommonUtil.isNotNullOrEmpty(canApplyWaybills)) {
				List<Long> queryShippingNoteNumbers = canApplyWaybills.stream().filter(f -> !f.getWaybillState().equals(BusinessConstants.WAYBILL_INVOICE_STATE_CAN))
						.map(WaybillCustomerRelation::getWaybillId).distinct().collect(Collectors.toList());
				List<String> shippingNoteNumbers = waybillMapper.selectWaybillByIds(queryShippingNoteNumbers).stream().map(Waybill::getShippingNoteNumber).collect(Collectors.toList());
				if (!shippingNoteNumbers.isEmpty()) {
					return AjaxResult.error(HttpStatus.BAD_REQUEST, "存在不可申请开票的运单：" + StringUtils.join(shippingNoteNumbers, ","));
				}
			}
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "存在不可申请开票的运单");
		}
		/*for (WaybillCustomerRelation waybillRelation : canApplyWaybills) {
			if (!waybillRelation.getWaybillState().equals(BusinessConstants.WAYBILL_INVOICE_STATE_CAN)) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "存在不可申请开票的运单");
			}
		}*/
		// 3、运单税务上报状态校验——税务二次上报状态不正常 暂时去掉校验
		// List<WaybillRes> abnormalTaxWaybill = waybillList.stream()
		// .filter(w -> w.getIsTaxUpload() == 0 && (w.getTaxSecondUploadState() != TaxUploadConstants.UPLOAD_SUCCESS || w.getTaxSecondResState() != TaxUploadConstants.TAX_RES_NORMAL))
		// .collect(Collectors.toList());
		// if (CommonUtil.isNotNullOrEmpty(abnormalTaxWaybill)) {
		// return AjaxResult.error(HttpStatus.BAD_REQUEST, "存在未通过支付上报的运单，不允许申请开票");
		// }
		// 4、运单是否属于同一转包链
		Long nextCustomerId = null;
		List<WaybillContractChain> chains = waybillContractChainService.findByIds(waybillIds);
		Map<Long, List<WaybillContractChain>> chainIds = chains.stream().collect(Collectors.groupingBy(WaybillContractChain::getChainId));
		FrameworkContractSubchain lowerSubhain = new FrameworkContractSubchain();
		for (Long chainId : chainIds.keySet()) {
			// 5、运单转包链是否设置费率，即下一层是否有费率
			lowerSubhain = getLowerSubhain(chainId, customerId);
			nextCustomerId = lowerSubhain.getCustomerId();
			if (lowerSubhain == null) {
				return AjaxResult.error("处于转包链最后一层，无需申请开票");
			}
			for (WaybillCustomerRelation bill : canApplyWaybills) {
				if (bill.getRateToMe() == null || bill.getRateToMe().compareTo(BigDecimal.ZERO) == 0) {
					CustomerInfo auditCustomerInfo = customerInfoMapper.selectCustomerInfoById(nextCustomerId);
					return AjaxResult.error(HttpStatus.BAD_REQUEST, "转包费率尚未设置，请联系[ " + auditCustomerInfo.getCustomerName() + " ]设置费率后重新申请开票");
				}
			}
		}

		// 5、校验同一个销售方的运单是否存在上报、不上报
		// 销售方为网络货运人时，上报与不上报税务的运单不支持同时申请开票
		if (!CommonUtil.isEmptyOrZero(nextCustomerId)) {
			FreightForwarderInfo forwarderInfoSearch = new FreightForwarderInfo();
			forwarderInfoSearch.setCustomerId(nextCustomerId);
			List<FreightForwarderInfo> forwarderInfos = freightForwarderInfoService.selectFreightForwarderInfoList(forwarderInfoSearch);
			if (CommonUtil.isNotNullOrEmpty(forwarderInfos)) { // 下级销售方为网货人
				List<Integer> isTaxUpload = waybillList.stream().map(WaybillRes::getIsTaxUpload).distinct().collect(Collectors.toList());
				if (isTaxUpload.size() > 1) {
					return AjaxResult.error(HttpStatus.BAD_REQUEST, "上报与不上报税务的运单不支持同时申请开票");
				}
			}
		}

		// 6、申请开票单位校验
		Integer feeUnit = waybillList.get(0).getFeeUnit();
		for (WaybillRes waybill : waybillList) {
			if (!waybill.getFeeUnit().equals(feeUnit)) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "申请开票的运单货物单位必须一致");
			}
		}
		// 7、金融端项目modelId和选择的一致
		if (ClientType.FINANCE.equals(SecurityUtils.getClientType())) {
			FrameworkContract frameworkContract = frameworkContractService.selectFrameworkContractById(waybillList.get(0).getFrameworkContractId());
			if (SecurityUtils.getBusinessModelId() != frameworkContract.getBusinessModelId()) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "申请开票的运单需属于本次选择的业务模式");
			}
		}

		//8、校验是否含有高风险和中风险运单
		long count = waybillList.stream().map(WaybillRes::getRiskResultGrade).filter(grade -> grade != null && (grade == 1 || grade == 2 || grade == 0)).count();
		if (count > 0) {
			if (SecurityUtils.getClientType().equals(FINANCE)) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "申请的运单中，含有风险项，请联系托运人查询并修正运单后，再申请开票");
			} else {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "文件中含有风险运单，请先查询处理后再开票");
			}
		}
		return AjaxResult.success(lowerSubhain);

	}

	public List<String> verifyApplyInvoice(List<WaybillRes> waybillList, List<Long> waybillIds, Long customerId, List<WaybillCustomerRelation> ApplyWaybills, FinanceStatementReq req,
			List<String> errorMessages) {

		// 校验运单是否已经完成
		// 创建一个集合来存储未完成的运单号
		List<String> incompleteWaybillNumbers = new ArrayList<>();
		// 遍历运单列表
		for (WaybillRes waybillRes : waybillList) {
			if (2 != waybillRes.getSettleStatus()) {
				// 如果运单状态不是已结算，则将其运单号添加到未完成的运单号列表中
				incompleteWaybillNumbers.add(waybillRes.getShippingNoteNumber());
			}
		}
		// 检查是否有未完成的运单号
		if (!incompleteWaybillNumbers.isEmpty()) {
			// 构造包含未完成的运单号的错误信息
			String errorMessage = "存在未结算的运单：";
			for (String waybillNumber : incompleteWaybillNumbers) {
				errorMessage += waybillNumber + ", ";
			}
			errorMessage = errorMessage.substring(0, errorMessage.length() - 2); //

			// 返回包含错误信息的结果
			errorMessages.add(errorMessage + "请删除相关数据后重试");
		}

		// 通过传过来的运单数据，拿到项目id，根据项目id，查到购买方，跟传过来的购买方做判断
		List<Long> contractIds = waybillList.stream().map(WaybillRes::getFrameworkContractId).distinct().collect(Collectors.toList());
		List<Long> userAllContractIds = projectAssociationUserService.getUserAllContractIds(SecurityUtils.getUserId(), SecurityUtils.getDefaultProject());
		// 筛选出运单里项目 不在 有权限项目中的项目id
		List<Long> filteredContractIds = contractIds.stream().filter(contractId -> !userAllContractIds.contains(contractId)).collect(Collectors.toList());
		if (StringUtils.isNotEmpty(filteredContractIds)) {
			errorMessages.add("运单中存在用户没有项目权限的运单");
		}

		// 根据运单查询
		List<Long> nextCustomerIds = new ArrayList<>();
		List<WaybillContractChain> chains = waybillContractChainService.findByIds(waybillIds);
		List<String> contractChain = chains.stream().map(WaybillContractChain::getContractChain).distinct().collect(Collectors.toList());
		for (String s : contractChain) {
			List<FrameworkContractSubchain> lists = JSON.parseArray(s, FrameworkContractSubchain.class);
			for (FrameworkContractSubchain subchain : lists) {
				if (SecurityUtils.getShipperId().equals(subchain.getParentCustomerId())) {
					if (!nextCustomerIds.contains(subchain.getCustomerId())) {
						nextCustomerIds.add(subchain.getCustomerId());
					}
				}
			}
		}

		// 判断列表中是否只有一条数据，如果不是，返回错误
		Long nextCustomerId = null;
		if (nextCustomerIds.size() > 1) {
			errorMessages.add("所选运单中销售方必须是同一个");
		} else if (nextCustomerIds.size() == 0) {
			errorMessages.add("运单中销售方和所选销售方不一致");
		} else {
			if (req.getNextCustomerId() != null && !nextCustomerIds.get(0).equals(req.getNextCustomerId())) {
				errorMessages.add("运单中销售方和所选销售方不一致");
			} else {
				nextCustomerId = nextCustomerIds.get(0);
			}
		}

		Set<Long> frameworkContractIds = new HashSet<>();
		if (StringUtils.isNotEmpty(req.getFrameworkContractIds())) {
			for (String frameworkContractId : req.getFrameworkContractIds()) {
				frameworkContractIds.add(Long.parseLong(frameworkContractId));
			}
		}

		if (!frameworkContractIds.isEmpty()) {
			for (WaybillRes waybill : waybillList) {
				if (!frameworkContractIds.contains(waybill.getFrameworkContractId())) {
					// 如果不在集合中，直接返回错误信息
					errorMessages.add("存在所选项目外的运单，请修改需开票的项目或删除所选项目外的运单数据");
					break;
				}
			}
		}

		// 创建用于存储提示信息的StringBuilder对象
		StringBuilder errorMessageBuilder = new StringBuilder();
		StringBuilder submittedInvoiceWaybillsBuilder = new StringBuilder(); // 用于存储已提交开票申请的运单号
		StringBuilder alreadyInvoicedWaybillsBuilder = new StringBuilder(); // 用于存储进项票状态为已开票的运单号
		StringBuilder taxUploadFailWaybillsBuilder = new StringBuilder(); // 用于存储进项票状态为已开票的运单号

		// 创建用于存储状态为2的数据的列表（可以申请开票的运单）
		List<WaybillCustomerRelation> canApplyWaybills = new ArrayList<>();
		// 遍历 ApplyWaybills 列表
		Set<Integer> isTaxUploadSet = new HashSet<>();
		for (WaybillCustomerRelation applyWaybill : ApplyWaybills) {
			int waybillState = applyWaybill.getWaybillState();
			Waybill waybill = waybillService.selectWaybillById(applyWaybill.getWaybillId());
			// 根据运单状态进行判断
			if (waybillState == 2) {
				// 如果状态为2，将数据添加到状态为2的列表中
				canApplyWaybills.add(applyWaybill);
			} else if (waybillState == 3 || waybillState == 4) {
				String waybillNumber = waybill.getShippingNoteNumber();
				if (waybillState == 3) {
					submittedInvoiceWaybillsBuilder.append(waybillNumber).append(", ");
				} else {
					alreadyInvoicedWaybillsBuilder.append(waybillNumber).append(", ");
				}
			}
			isTaxUploadSet.add(waybill.getIsTaxUpload());
			if (waybill.getIsTaxUpload() == 0 && !ywkUploadService.uploadSummaryStatusIsSuccess(waybill, TaxUploadConstants.SECOND_UPLOAD)) {
				taxUploadFailWaybillsBuilder.append(waybill.getShippingNoteNumber()).append(", ");
			}
		}

		// 销售方为网络货运人时，上报与不上报税务的运单不支持同时申请开票
		if (!CommonUtil.isEmptyOrZero(nextCustomerId)) {
			FreightForwarderInfo forwarderInfoSearch = new FreightForwarderInfo();
			forwarderInfoSearch.setCustomerId(nextCustomerId);
			List<FreightForwarderInfo> forwarderInfos = freightForwarderInfoService.selectFreightForwarderInfoList(forwarderInfoSearch);
			if (CommonUtil.isNotNullOrEmpty(forwarderInfos)) { // 下级销售方为网货人
				if (isTaxUploadSet.size() > 1) {
					errorMessageBuilder.append("上报与不上报税务的运单不支持同时申请开票").append("，");
				}
			}
		}

		// 检查是否存在已提交开票申请的运单号，并将其添加到错误信息中
		String submittedInvoiceWaybills = submittedInvoiceWaybillsBuilder.toString().trim();
		if (!submittedInvoiceWaybills.isEmpty()) {
			errorMessageBuilder.append("存在已提交开票申请的运单：").append(submittedInvoiceWaybills.substring(0, submittedInvoiceWaybills.length() - 1)).append("，");
		}

		// 检查是否存在进项票状态为已开票的运单号，并将其添加到错误信息中
		String alreadyInvoicedWaybills = alreadyInvoicedWaybillsBuilder.toString().trim();
		if (!alreadyInvoicedWaybills.isEmpty()) {
			errorMessageBuilder.append("存在进项票状态为已开票的运单：").append(alreadyInvoicedWaybills.substring(0, alreadyInvoicedWaybills.length() - 1)).append("，");
		}

		// 检查是否存在税务上传失败的运单号，并将其添加到错误信息中 暂时去除该校验
		// String taxUploadFailWaybills = taxUploadFailWaybillsBuilder.toString().trim();
		// if (!taxUploadFailWaybills.isEmpty()) {
		// errorMessageBuilder.append("文件中含有未通过支付上报的运单，暂不支持开票：").append(taxUploadFailWaybills.substring(0, taxUploadFailWaybills.length() - 1)).append("，");
		// }

		// 移除最后一个逗号
		if (errorMessageBuilder.length() > 0) {
			errorMessageBuilder.deleteCharAt(errorMessageBuilder.length() - 1);
		}
		String errorMessage = errorMessageBuilder.toString();
		if (!errorMessage.isEmpty()) {
			errorMessages.add(errorMessage + "，请删除相关数据后重试");
		}

		// 7、金融端项目modelId和选择的一致
		if (ClientType.FINANCE.equals(SecurityUtils.getClientType())) {
			FrameworkContract frameworkContract = frameworkContractService.selectFrameworkContractById(waybillList.get(0).getFrameworkContractId());
			if (SecurityUtils.getBusinessModelId() != frameworkContract.getBusinessModelId()) {
				errorMessages.add("申请开票的运单需属于本次选择的业务模式");
			}
		}

		long count = waybillList.stream().map(WaybillRes::getRiskResultGrade).filter(grade -> grade != null && (grade == 1 || grade == 2 || grade == 0)).count();
		if (count > 0) {
			if (SecurityUtils.getClientType().equals(FINANCE)) {
				errorMessages.add("申请的运单中，含有风险项，请联系托运人查询并修正运单后，再申请开票");
			} else {
				errorMessages.add("文件中含有风险运单，请先查询处理后再开票");
			}
		}
		return errorMessages;
	}

	public List<String> verifyApplyInvoiceBehalf(List<WaybillRes> waybillList, List<Long> waybillIds, Long customerId, List<WaybillCustomerRelation> ApplyWaybills, FinanceStatementReq req,
			List<String> errorMessages) {

		// 校验运单是否已经完成
		// 创建一个集合来存储未完成的运单号
		List<String> incompleteWaybillNumbers = new ArrayList<>();
		// 遍历运单列表
		for (WaybillRes waybillRes : waybillList) {
			if (4 != waybillRes.getStatus()) {
				// 如果运单状态不是已结算，则将其运单号添加到未完成的运单号列表中
				incompleteWaybillNumbers.add(waybillRes.getShippingNoteNumber());
			}
		}
		// 检查是否有未完成的运单号
		if (!incompleteWaybillNumbers.isEmpty()) {
			// 构造包含未完成的运单号的错误信息
			String errorMessage = "存在未完成的运单：";
			for (String waybillNumber : incompleteWaybillNumbers) {
				errorMessage += waybillNumber + ", ";
			}
			errorMessage = errorMessage.substring(0, errorMessage.length() - 2); //

			// 返回包含错误信息的结果
			errorMessages.add(errorMessage + "请删除相关数据后重试");
		}

		// 通过传过来的运单数据，拿到项目id，根据项目id，查到购买方，跟传过来的购买方做判断
		List<Long> contractIds = waybillList.stream().map(WaybillRes::getFrameworkContractId).distinct().collect(Collectors.toList());
		List<Long> userAllContractIds = projectAssociationUserService.getUserAllContractIds(SecurityUtils.getUserId(), SecurityUtils.getDefaultProject());
		// 筛选出运单里项目 不在 有权限项目中的项目id
		List<Long> filteredContractIds = contractIds.stream().filter(contractId -> !userAllContractIds.contains(contractId)).collect(Collectors.toList());
		if (StringUtils.isNotEmpty(filteredContractIds)) {
			errorMessages.add("运单中存在用户没有项目权限的运单");
		}
		List<FrameworkContractSubchainEx> frameworkContractSubchainExes = frameworkContractSubchainMapperEx.selectContractSubchainListBehalf(0, contractIds, customerId);
		List<Long> aboveCustomerIdsNew = frameworkContractSubchainExes.stream().map(FrameworkContractSubchainEx::getParentCustomerId).distinct().collect(Collectors.toList());

		// 判断列表中是否只有一条数据，如果不是，返回错误
		if (CommonUtil.isNotNullOrEmpty(aboveCustomerIdsNew) && aboveCustomerIdsNew.size() != 1) {
			errorMessages.add("所选运单中购买方必须是同一个");
		}

		if (CommonUtil.isNotNullOrEmpty(aboveCustomerIdsNew) && aboveCustomerIdsNew.size() != 0) {
			Long aboveCustomerIdNew = aboveCustomerIdsNew.get(0);
			Long aboveCustomerIdLong = Long.parseLong(req.getAboveCustomerId().toString());
			if (!aboveCustomerIdNew.equals(aboveCustomerIdLong)) {
				errorMessages.add("运单中购买方和所选购买方不一致");
			}
		} else {
			errorMessages.add("运单中购买方和所选购买方不一致");
		}

		Set<Long> frameworkContractIds = new HashSet<>();
		if (StringUtils.isNotEmpty(req.getFrameworkContractIds())) {
			for (String frameworkContractId : req.getFrameworkContractIds()) {
				frameworkContractIds.add(Long.parseLong(frameworkContractId));
			}
		}

		if (!frameworkContractIds.isEmpty()) {
			for (WaybillRes waybill : waybillList) {
				if (!frameworkContractIds.contains(waybill.getFrameworkContractId())) {
					// 如果不在集合中，直接返回错误信息
					errorMessages.add("存在所选项目外的运单，请修改需开票的项目或删除所选项目外的运单数据");
					break;
				}
			}
		}

		// 创建用于存储提示信息的StringBuilder对象
		StringBuilder errorMessageBuilder = new StringBuilder();

		// 创建用于存储状态为2的数据的列表（可以申请开票的运单）
		List<WaybillCustomerRelation> canApplyWaybills = new ArrayList<>();
		// 遍历 ApplyWaybills 列表
		for (WaybillCustomerRelation applyWaybill : ApplyWaybills) {
			int waybillState = applyWaybill.getWaybillState();
			Waybill waybill = waybillService.selectWaybillById(applyWaybill.getWaybillId());
			// 根据运单状态进行判断
			if (waybillState == 2) {
				// 如果状态为2，将数据添加到状态为2的列表中
				canApplyWaybills.add(applyWaybill);
			} else if (waybillState == 3) {
				// 如果状态为3，提示存在已提交开票申请的运单
				errorMessageBuilder.append("存在已提交开票申请的运单：").append(waybill.getShippingNoteNumber()).append("，");
			} else if (waybillState == 4) {
				// 如果状态为4，提示存在进项票状态为已开票的运单
				errorMessageBuilder.append("存在销项票状态为已开票的运单：").append(waybill.getShippingNoteNumber()).append("，");
			}
			// if (waybill.getIsTaxUpload() == 0 && (waybill.getTaxSecondUploadState() != TaxUploadConstants.UPLOAD_SUCCESS || waybill.getTaxSecondResState() != TaxUploadConstants.TAX_RES_NORMAL)) {
			// errorMessageBuilder.append("文件中含有未通过支付上报的运单：").append(waybill.getShippingNoteNumber()).append("，");
			// }
		}

		// 移除最后一个逗号
		if (errorMessageBuilder.length() > 0) {
			errorMessageBuilder.deleteCharAt(errorMessageBuilder.length() - 1);
		}
		String errorMessage = errorMessageBuilder.toString();
		if (!errorMessage.isEmpty()) {
			errorMessages.add(errorMessage + "，请删除相关数据后重试");
		}

		// 7、金融端项目modelId和选择的一致
		if (ClientType.FINANCE.equals(SecurityUtils.getClientType())) {
			FrameworkContract frameworkContract = frameworkContractService.selectFrameworkContractById(waybillList.get(0).getFrameworkContractId());
			if (SecurityUtils.getBusinessModelId() != frameworkContract.getBusinessModelId()) {
				errorMessages.add("申请开票的运单需属于本次选择的业务模式");
			}
		}
		return errorMessages;
	}

	public AjaxResult verifyCanApplyInvoice(List<WaybillRes> waybillList, List<Long> waybillIds, Long customerId) {
		// 1、运单是否为空
		if (waybillList.isEmpty()) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "未查询到申请开票的运单信息");
		}
		// 2、获取该托运人这些运单是否可申请开票
		List<WaybillCustomerRelation> canApplyWaybills = waybillCustomerService.selectCanApplyInvoiceWaybills(customerId, waybillIds);
		if (canApplyWaybills == null || canApplyWaybills.size() < waybillIds.size()) {
			if (CommonUtil.isNotNullOrEmpty(canApplyWaybills)) {
				List<Long> queryShippingNoteNumbers = canApplyWaybills.stream().filter(f -> !f.getWaybillState().equals(BusinessConstants.WAYBILL_INVOICE_STATE_CAN))
						.map(WaybillCustomerRelation::getWaybillId).distinct().collect(Collectors.toList());
				List<String> shippingNoteNumbers = waybillMapper.selectWaybillByIds(queryShippingNoteNumbers).stream().map(Waybill::getShippingNoteNumber).collect(Collectors.toList());
				if (!shippingNoteNumbers.isEmpty()) {
					return AjaxResult.error(HttpStatus.BAD_REQUEST, "存在不可申请开票的运单：" + StringUtils.join(shippingNoteNumbers, ","));
				}
			}
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "存在不可申请开票的运单");
		}

		// 3、运单是否属于同一项目
		// Map<Long, List<WaybillRes>> collect = waybillList.stream().collect(Collectors.groupingBy(WaybillRes::getFrameworkContractId));
		// if (collect == null || collect.size() > 1) {
		// return AjaxResult.error(HttpStatus.BAD_REQUEST, "运单需属于同一个项目");
		// }
		// 4、运单是否属于同一转包链
		List<WaybillContractChain> chains = waybillContractChainService.findByIds(waybillIds);
		Map<Long, List<WaybillContractChain>> chainIds = chains.stream().collect(Collectors.groupingBy(WaybillContractChain::getChainId));
		// if (chainIds == null || chainIds.size() > 1) {
		// return AjaxResult.error(HttpStatus.BAD_REQUEST, "运单需属于同一个转包链");
		// }
		FrameworkContractSubchain lowerSubhain = new FrameworkContractSubchain();
		for (Long chainId : chainIds.keySet()) {
			// 5、运单转包链是否设置费率，即下一层是否有费率
			lowerSubhain = getLowerSubhain(chainId, customerId);
			if (lowerSubhain == null) {
				return AjaxResult.error("处于转包链最后一层，无需申请开票");
			}
			for (WaybillCustomerRelation bill : canApplyWaybills) {
				if (bill.getRateToMe() == null || bill.getRateToMe().compareTo(BigDecimal.ZERO) == 0) {
					CustomerInfo auditCustomerInfo = customerInfoMapper.selectCustomerInfoById(lowerSubhain.getCustomerId());
					return AjaxResult.error(HttpStatus.BAD_REQUEST, "转包费率尚未设置，请联系[ " + auditCustomerInfo.getCustomerName() + " ]设置费率后重新申请开票");
				}
			}
		}

		// 6、申请开票单位校验
		Integer feeUnit = waybillList.get(0).getFeeUnit();
		for (WaybillRes waybill : waybillList) {
			if (!waybill.getFeeUnit().equals(feeUnit)) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "申请开票的运单货物单位必须一致");
			}
		}
		// 7、金融端项目modelId和选择的一致
		if (ClientType.FINANCE.equals(SecurityUtils.getClientType())) {
			FrameworkContract frameworkContract = frameworkContractService.selectFrameworkContractById(waybillList.get(0).getFrameworkContractId());
			if (SecurityUtils.getBusinessModelId() != frameworkContract.getBusinessModelId()) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "申请开票的运单需属于本次选择的业务模式");
			}
		}
		return AjaxResult.success(lowerSubhain);

	}

	/**
	 * 开票管理---按金额申请_匹配合适的可开票运单
	 *
	 * @param
	 * @return
	 */
	@Override
	public AjaxResult matchWaybills(FinanceStatementReq req) {
		BeanUtils.beanAttributeValueTrim(req);// 去掉该对象中string字段前后空格
		if (StringUtils.isNull(req.getInvoiceFare()) || req.getInvoiceFare().compareTo(BigDecimal.ZERO) < 1) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "开票总金额为空");
		}
		Long customerId = SecurityUtils.getShipperId();
		if (null == customerId || customerId < 1L) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "货主信息为空");
		}
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(customerId);
		if (null == customerInfo) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "货主信息为空");
		}
		// 构建查询条件--已完成、已结算、未开票的运单
		Map<String, Object> map = new HashMap<>();
		map.put("customerId", customerId);
		// 匹配符合的运单，支持多个项目传入，如果为空，则说明不限制项目
		if (StringUtils.isNotEmpty(req.getFrameworkContractIds())) {
			map.put("frameworkContractIds", req.getFrameworkContractIds());
		}
		List<WaybillRes> waybillList = new ArrayList<>();
		if (SHIPPER.equals(SecurityUtils.getClientType())) {
			// 匹配运单需要匹配指定的下一级商户id，前端选项框选择完之后传nextCustomerId
			map.put("nextCustomerId", req.getNextCustomerId());
			waybillList = waybillMapperEx.waybillListForTenantStatement(map);
		} else if (FINANCE.equals(SecurityUtils.getClientType())) {
			map.put("BusinessModelId", SecurityUtils.getBusinessModelId());
			map.put("nextCustomerId", req.getNextCustomerId());
			waybillList = waybillMapperEx.waybillListForFinanceStatement(map);
		}
		if (CommonUtil.isNullOrEmpty(waybillList)) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "没有匹配到符合开票条件的运单");
		}
		for (WaybillRes w : waybillList) {
			if (null == w.getRate()) {
				w.setRate(BigDecimal.ZERO);
			}
			w.setInvoiceFare(w.getActualFare().divide(BigDecimal.ONE.subtract(w.getRate()), 2, RoundingMode.HALF_UP));
		}
		//过滤掉高风险和中风险的运单
		waybillList = waybillList.stream().filter(waybill -> {
			Integer grade = waybill.getRiskResultGrade();
			return grade == null || (grade != 1 && grade != 2 && grade != 0);
		}).collect(Collectors.toList());
		// 过滤掉上报失败/异常的运单
		// waybillList = waybillList.stream()
		// .filter(w -> w.getIsTaxUpload() == 1
		// || (w.getIsTaxUpload() == 0 && w.getTaxFirstUploadState() == TaxUploadConstants.UPLOAD_SUCCESS && w.getTaxFirstResState() == TaxUploadConstants.TAX_RES_NORMAL
		// && w.getTaxArriveUploadState() == TaxUploadConstants.UPLOAD_SUCCESS && w.getTaxArriveResState() == TaxUploadConstants.TAX_RES_NORMAL
		// && w.getTaxSecondUploadState() == TaxUploadConstants.UPLOAD_SUCCESS && w.getTaxSecondResState() == TaxUploadConstants.TAX_RES_NORMAL))
		// .collect(Collectors.toList());
		// 获取首条运单货物装载单位
		if (CommonUtil.isNullOrEmpty(waybillList)) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "没有匹配到合适的可开票运单");
		}
		Integer feeUnit = waybillList.get(0).getFeeUnit();

		BigDecimal currentTotalMoney = BigDecimal.ZERO;
		ArrayList<WaybillRes> waybillResList = combinationWaybill(waybillList, req.getInvoiceFare().doubleValue(), feeUnit);
		for (WaybillRes waybillRes : waybillResList) {
			currentTotalMoney = currentTotalMoney.add(waybillRes.getInvoiceFare());
		}

		if (!waybillResList.isEmpty()) {
			Map<String, Object> resMap = new HashMap<String, Object>();
			resMap.put("invoiceFare", req.getInvoiceFare());// 开票总金额
			resMap.put("waybillAmount", waybillResList.size());// 已匹配运单数
			resMap.put("receiveFare", currentTotalMoney);// 应收总金额
			resMap.put("waybillList", waybillResList);// 匹配的运单列表
			// 少一个运费总实付没返回,前端是从运单列表取值的
			return AjaxResult.success("匹配成功", resMap);
		} else {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "没有匹配到合适的可开票运单");
		}
	}

	/**
	 * 获取待对账运单列表
	 *
	 * @param waybillIds
	 * 		运单ID列表
	 * @return
	 */
	public List<WaybillRes> selectStatementWaybillList(List<Long> waybillIds) {
		if (null == waybillIds || waybillIds.isEmpty()) {
			return Collections.emptyList();
		}
		List<WaybillRes> list = new ArrayList<>();
		List<List<Long>> superIds = ListUtils.partition(waybillIds, 100);
		for (List<Long> subIds : superIds) {
			List<WaybillRes> subList = waybillService.selectStatementWaybillList(subIds);
			list.addAll(subList);
		}

		return list;
	}

	public List<WaybillRes> selectStatementWaybillList(List<Long> waybillIds, Long shipperId) {
		if (null == waybillIds || waybillIds.isEmpty()) {
			return Collections.emptyList();
		}
		List<WaybillRes> list = new ArrayList<>();
		List<List<Long>> superIds = ListUtils.partition(waybillIds, 100);
		for (List<Long> subIds : superIds) {
			List<WaybillRes> subList = waybillService.selectStatementWaybillList(subIds, shipperId);
			list.addAll(subList);
		}

		return list;
	}

	/**
	 * 开票申请单列表
	 *
	 * @param searchReq
	 * @return
	 */
	@Override
	public List<FinanceStatementRes> selectStatementList(FinanceStatementSearchReq searchReq) {
		if (CommonUtil.isNotNullOrEmpty(searchReq.getShippingNoteNumber())) {
			if (searchReq.getUsage() == 9 || searchReq.getUsage() == 2){
				List<Long> statementIds = financeStatementWaybillMapper.selectStatementIdsByShippingNoteNumber(searchReq.getShippingNoteNumber());
				searchReq.setStatementIds(statementIds);
			}else {
				Long lastStatementId = financeStatementWaybillMapper.selectStatementIdByShippingNoteNumber(searchReq.getShippingNoteNumber());
				searchReq.setStatementIds(Arrays.asList(lastStatementId));
			}
		}

		PageUtils.startPage();
		List<FinanceStatementRes> list = financeStatementMapperEx.selectStatementList(searchReq);
		List<Long> statementIds = list.stream().map(statement -> statement.getId()).collect(Collectors.toList());
		// 获取运单应付总金额
		List<FinanceStatementRes> farelist = CommonUtil.isNotNullOrEmpty(statementIds) ? financeStatementWaybillMapper.stasticWaybillFareByStatementIds(statementIds) : Collections.emptyList();
		Map<Long, FinanceStatementRes> fareMap = farelist.stream().collect(Collectors.toMap(FinanceStatementRes::getId, Function.identity()));

		// 根据申请单查询费率
		Map<Long, FinanceStatementRes> rateMap = null;
		if (!PLATFORM.equals(SecurityUtils.getClientType())){
			List<FinanceStatementRes> rateList = financeStatementMapperEx.selectStatementRate(statementIds, SecurityUtils.getShipperId());
			rateMap = rateList.stream().collect(Collectors.toMap(FinanceStatementRes::getId, Function.identity()));
		}

		for (FinanceStatementRes statementRes : list) {
			// 1、填充应收和利润金额
			BigDecimal receiveFare = fareMap.get(statementRes.getId()) == null ? BigDecimal.ZERO : fareMap.get(statementRes.getId()).getReceiveFare();
			statementRes.setReceiveFare(receiveFare);
			statementRes.setPayFare(receiveFare);
			statementRes.setProfitFare(statementRes.getInvoiceFare().subtract(receiveFare));
			if (rateMap != null){
				statementRes.setRate(rateMap.get(statementRes.getId()) == null ? "" : rateMap.get(statementRes.getId()).getRate());
			}

			// 2、已开票的添加附件
			if (statementRes.getMarkStatus() == 1) {
				List<FinanceStatementBill> fsbList = financeStatementBillService.selectFinanceStatementBillList(statementRes.getId());
				if (!StringUtils.isEmpty(fsbList)) {
					List<SysAttachmentInfo> files = sysAttachmentInfoService.selectFileList(fsbList.get(0).getId());
					statementRes.setSysAttachmentInfoList(files);
				}
			}

			if (statementRes.getProfitState() == 3){
				statementRes.setPaymentDate(null);
			}

			statementRes.setOperationAuditTime("0000-00-00 00:00:00".equals(statementRes.getOperationAuditTime()) ? null : statementRes.getOperationAuditTime());
			statementRes.setFinanceAuditTime("0000-00-00 00:00:00".equals(statementRes.getFinanceAuditTime()) ? null : statementRes.getFinanceAuditTime());
			statementRes.setPaymentDate("0000-00-00 00:00:00".equals(statementRes.getPaymentDate()) ? null : statementRes.getPaymentDate());

			// 查询运输费附件
			if (statementRes.getProfitState() == 2) {
				statementRes.setPaymentFileList(paymentAttachmentInfoService.selectPaymentAttachmentInfoByRelationId(statementRes.getId()));
			}
		}
		return list;
	}

	@Override
	public CommonResult<?> selectStatementListCount(Map<String, Object> map) {
		if (map.containsKey("shippingNoteNumber")) {
			String shippingNoteNumber = (String) map.get("shippingNoteNumber");
			if (StringUtils.isNotEmpty(shippingNoteNumber)) {
				Long aLong = financeStatementWaybillMapper.selectStatementIdByShippingNoteNumber(shippingNoteNumber);
				map.put("statementId", aLong == null ? 0 : aLong);
			}
		}
		if (map.containsKey("auditFreightForwarderId")) {
			Long auditFreightForwarderId = (Long) map.get("auditFreightForwarderId");
			if (auditFreightForwarderId != null) {
				map.put("statementIds", financeStatementMapperEx.selectStatementIdList(auditFreightForwarderId, null));
			}
		}

		if (map.containsKey("auditCustomerId")) {
			Long auditCustomerId = (Long) map.get("auditCustomerId");
			if (auditCustomerId != null) {
				map.put("statementIds", financeStatementMapperEx.selectStatementIdList(null, auditCustomerId));
			}
		}

		return CommonResult.success(financeStatementMapperEx.selectStatementListCount(map));

	}

	/**
	 * 查询开票列表
	 *
	 * @param req
	 * 		开票
	 * @return 开票集合
	 */
	@Override
	public List<FinanceStatementRes> financeStatementList(FinanceStatementReq req) {
		if (StringUtils.isNull(req)) {
			return new ArrayList<FinanceStatementRes>();
		}
		BeanUtils.beanAttributeValueTrim(req);// 去掉该对象中string字段前后空格
		if (StringUtils.isNotBlank(req.getCreateTimeStart())) {
			req.setCreateTimeStart(req.getCreateTimeStart() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(req.getCreateTimeEnd())) {
			req.setCreateTimeEnd(req.getCreateTimeEnd() + " 23:59:59");
		}
		return financeStatementMapperEx.financeStatementList(req);
	}

	/**
	 * 开票撤回
	 *
	 * @param id
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public AjaxResult revokeReconciliation(Long id) {
		if (StringUtils.isNull(id) || id < 1L) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "请选择要撤回的开票信息");
		}
		// 已经开票申请的账套不可撤回
		FinanceStatement financeStatement = financeStatementMapper.selectFinanceStatementById(id);
		if (StringUtils.isNull(financeStatement)) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "没有找到匹配的开票信息");
		}
		if (financeStatement.getState() > BusinessConstants.FINANCE_STATEMENT_RECONCILED) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "已经申请开票的账套不可撤回");
		}
		financeStatementMapper.deleteFinanceStatementById(id);// 删除开票信息
		// 删除开票清单信息
		FinanceStatementWaybill financeStatementWaybill = new FinanceStatementWaybill();
		financeStatementWaybill.setStatementId(id);
		List<FinanceStatementWaybill> financeStatementWaybillList = financeStatementWaybillMapper.selectFinanceStatementWaybillList(financeStatementWaybill);
		if (StringUtils.isNotNull(financeStatementWaybillList) && !financeStatementWaybillList.isEmpty()) {
			Long[] ids = new Long[financeStatementWaybillList.size()];
			int i = 0;
			for (FinanceStatementWaybill tmp : financeStatementWaybillList) {
				// 恢复该开票清单中运单的状态为4:已完成(未开票)
				Waybill waybill = waybillService.selectWaybillById(tmp.getWaybillId());
				waybill.setStatementReceiveFare(BigDecimal.ZERO);// 开票用的应收运费重置为0
				waybill.setStatus(BusinessConstants.WAYBILL_STATUS_COMPLETED);// 结算状态为4:已完成(未开票)
				waybillService.updateWaybill(waybill);
				// 修改es中运单
				esWaybillService.updateEsWaybillInfo(waybill);
				ids[i] = tmp.getId();
				i++;
			}
			financeStatementWaybillMapper.deleteFinanceStatementWaybillByIds(ids);
		}
		return AjaxResult.success(HttpStatus.SUCCESS, "撤回成功");
	}

	public static ArrayList<WaybillRes> combinationWaybill(List<WaybillRes> waybillList, double target, int feeUnit) {
		ArrayList<WaybillRes> res = new ArrayList<WaybillRes>();
		double lapse = 1000000;
		Random random = new Random();
		for (int i = 0; i < 100000; i++) {
			int numsize = random.nextInt(waybillList.size() + 1);
			ArrayList<WaybillRes> list = new ArrayList<WaybillRes>();
			HashMap<Integer, Integer> map = new HashMap<>();
			double thissum = 0;
			for (int j = 0; j < numsize; j++) {
				int anumberindex = random.nextInt(waybillList.size());
				while (map.containsKey(anumberindex)) {
					anumberindex = random.nextInt(waybillList.size());
				}
				map.put(anumberindex, 1);
				if (waybillList.get(anumberindex).getFeeUnit() == feeUnit) {
					list.add(waybillList.get(anumberindex));
					thissum += waybillList.get(anumberindex).getInvoiceFare().doubleValue();
				}
			}
			if (Math.abs(thissum - target) < lapse) {
				lapse = Math.abs(thissum - target);
				res = (ArrayList<WaybillRes>) list.clone();
			}
			if (lapse < 0.1)
				break;
		}
		return res;
	}

	public static List<WaybillRes> combinationWaybillNew(List<WaybillRes> waybillList, double target, int feeUnit) {
		if (CommonUtil.isNullOrEmpty(waybillList)) {
			return Collections.EMPTY_LIST;
		}
		double deviation = 0.1; // 组合允许的误差
		Integer tryTotal = 100000; // 尝试的总次数
		Random random = new Random();
		for (int tryCount = 0; tryCount < tryTotal; tryCount++) {
			ArrayList<WaybillRes> list = new ArrayList<WaybillRes>();
			HashMap<Integer, Integer> map = new HashMap<>();
			double thisSum = 0;
			// 随机取运单的总个数 在 1 ~ 运单长度 之间（包含首尾）
			int waybillpickNum = random.nextInt(waybillList.size()) + 1;
			for (int waybillCount = 0; waybillCount < waybillpickNum; waybillCount++) {
				int waybillIndex = random.nextInt(waybillList.size());
				while (map.containsKey(waybillIndex)) {
					waybillIndex = random.nextInt(waybillList.size());
				}
				map.put(waybillIndex, 1);
				if (waybillList.get(waybillIndex).getFeeUnit() == feeUnit) {
					list.add(waybillList.get(waybillIndex));
					thisSum += waybillList.get(waybillIndex).getInvoiceFare().doubleValue();
				}
				if (Math.abs(thisSum - target) < deviation) {
					return list;
				}
			}
		}
		return Collections.EMPTY_LIST;
	}

	public static void main(String[] args) {
		double target = 1.2;
		double[] params = { 0.02, 0.01 };
		ArrayList<Double> res = new ArrayList<>();
		Random random = new Random();
		for (int i = 0; i < 100000; i++) {
			double lapse = 1000000;
			res = new ArrayList<>();
			int numsize = random.nextInt(params.length) + 1;
			if (numsize == 0) {
				System.out.println(numsize);
			}
			double thissum = 0;
			HashMap<Integer, Integer> map = new HashMap<>();
			for (int j = 0; j < numsize; j++) {
				int anumberindex = random.nextInt(params.length);
				while (map.containsKey(anumberindex)) {
					anumberindex = random.nextInt(params.length);
				}
				System.out.println("尝试次数:" + (i + 1) + ",   j:" + j + ",   数组下标:" + anumberindex + ",   外层总长度:" + numsize);
				map.put(anumberindex, 1);
				thissum += params[anumberindex];
				res.add(params[anumberindex]);
				if (Math.abs(thissum - target) < lapse) {
					lapse = Math.abs(thissum - target);
				}
				if (lapse < 0.1) {
					System.out.println(res);
					return;
				}
			}
		}
		System.out.println(res);
	}

	/**
	 * 网络货运人端-开票撤消、审批
	 *
	 * @param req
	 * 		账单对象
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public AjaxResult invoiceApproval(FinanceStatementReq req) {
		// 0、校验
		List<Long> statementIds = req.getStatementIds();
		Integer state = req.getState();
		if (null == statementIds || statementIds.size() == 0) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "要操作的开票数据为空");
		}
		// state 状态(-1:已撤销 0:被驳回 1:已完成(待申请开票) 2:审批中 3:审批通过(未核销) 4:部分核销 5:已核销 6:发票登记驳回 7 运营审核中 8运营审核驳回 9运营审核通过 10财务审核驳回 11财务审核通过)
		List<Integer> stateList = new ArrayList<>();
		stateList.add(BusinessConstants.FINANCE_STATEMENT_CANCEL); // -1.已撤消
		stateList.add(BusinessConstants.FINANCE_STATEMENT_REJECT); // 0.被驳回
		stateList.add(BusinessConstants.FINANCE_STATEMENT_APPROVED); // 3.审批通过
		stateList.add(BusinessConstants.FINANCE_STATEMENT_REGISTER_REJECT); // 6.发票登记驳回
		stateList.add(7); // 7运营审核中
		stateList.add(8); // 8运营审核驳回
		stateList.add(9); // 9运营审核通过
		stateList.add(10); // 10财务审核驳回
		stateList.add(11); // 11财务审核通过
		if (!stateList.contains(state)) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "操作状态不匹配");
		}

		List<Long> waybillIdList = new ArrayList<>();

		// 1、基础数据准备并更新开票表：存放托运人-运单ID列表，key：托运人ID，value：运单ID列表
		Map<Long, List<Long>> customerWaybillIdsMap = new HashMap<>();
		List<FinanceStatement> list = new ArrayList<>();
		for (Long id : statementIds) {
			FinanceStatement statement = financeStatementMapper.selectFinanceStatementById(id);
			list.add(statement);
		}

		for (FinanceStatement statement : list) {
			if (state == 11 && !Arrays.asList(1,2).contains(statement.getProfitState())) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "该申请单服务费状态为“未支付”，请在“运输费标记”中上传支付凭证，或联系托运人上传支付凭证后重试");
			}

			// 运营审批
			if (state == 8 || state == 9) {
				statement.setOperationAuditTime(new Date());
				statement.setOperationAuditBy(SecurityUtils.getNickname());
			}

			if (state == 8) {
				statement.setOperationRejectReason(req.getOperationRejectReason());
				if (StringUtils.isEmpty(req.getOperationRejectReason())) {
					return AjaxResult.error(HttpStatus.BAD_REQUEST, "请填写驳回原因");
				}
			}

			// 财务审批
			if (state == 10 || state == 11) {
				statement.setFinanceAuditTime(new Date());
				statement.setFinanceAuditBy(SecurityUtils.getNickname());
			}

			if (state == 10) {
				statement.setFinanceRejectReason(req.getFinanceRejectReason());
				if (StringUtils.isEmpty(req.getFinanceRejectReason())) {
					return AjaxResult.error(HttpStatus.BAD_REQUEST, "请填写驳回原因");
				}
			}

			if (state == 11) {
				// 0未支付(线上)  1已支付(线上)  2已支付(线下) 3未支付(线下) 4付款中 5线上支付失败)
				if (!Arrays.asList(1,2).contains(statement.getProfitState())) {
					return AjaxResult.error(HttpStatus.BAD_REQUEST, "未支付成功的申请单无法审核通过");
				}
			}

			if (statement.getPaymentType() == 1) {
				if (statementIds.size() > 1) {
					if (0 == state || -1 == state || 6 == state || 8 == state || 10 == state) {
						throw new RuntimeException("线上支付的申请单无法批量驳回");
					}
				}
			}
		}

		for (FinanceStatement statement : list) {
			if (0 == state || -1 == state || 6 == state || 8 == state || 10 == state) {
				if (statement.getPaymentType() == 1 && !FINANCE.equals(SecurityUtils.getClientType())) {
					AjaxResult ajaxResult = feePayReturn(statement);
					if (ajaxResult.isNotSuccess()) {
						return ajaxResult;
					}
				}
			}
		}

		for (FinanceStatement statement : list) {

			List<Long> waybillIds = financeStatementWaybillMapper.selectListByStatementId(statement.getId());
			if (CommonUtil.isNotNullOrEmpty(waybillIds)) {
				List<Long> customerWaybillIds = customerWaybillIdsMap.get(statement.getCustomerId()) == null ? new ArrayList<>() : customerWaybillIdsMap.get(statement.getCustomerId());
				customerWaybillIds.addAll(waybillIds);
				customerWaybillIdsMap.put(statement.getCustomerId(), customerWaybillIds);
			}
			statement.setRejectReason(req.getRejectReason());
			statement.setState(state);
			statement.setAuditTime(DateUtils.getNowDate());
			statement.setUpdateBy(SecurityUtils.getNickname());
			financeStatementMapper.updateFinanceStatement(statement);
			waybillIdList.addAll(waybillIds);
		}

		// 3、驳回或撤销时，更新运单相关状态
		if (0 == state || -1 == state || 6 == state || 8 == state || 10 == state) {
			for (Long customerId : customerWaybillIdsMap.keySet()) {
				// 3.1更新运单关联表中开票状态为--可开票
				List<Long> waybillIds = customerWaybillIdsMap.get(customerId);
				waybillCustomerRelationMapper.updateWaybillStatementStatus(2, waybillIds, customerId);
				// 3.2更新运单表中开票状态--如任何环节都未在开票中，则更新为未开票0，否则仍为开票中，无需更新
				List<WaybillCustomerRelation> relations = waybillCustomerRelationMapper.selectRelationByWaybillIds(waybillIds);
				List<Long> notNeedChangeBillStateWaybillIds = relations.stream().filter(r -> !r.getCustomerId().equals(customerId) && r.getWaybillState() > 2).map(f -> f.getWaybillId()).distinct()
						.collect(Collectors.toList());
				List<Long> needChangeBillStateWaybillIds = waybillIds.stream().filter(id -> !notNeedChangeBillStateWaybillIds.contains(id)).collect(Collectors.toList());
				if (CommonUtil.isNotNullOrEmpty(needChangeBillStateWaybillIds)) {
					waybillMapperEx.updateWaybillBillStatusByIds(needChangeBillStateWaybillIds, 0);
				}
				// 3.3更新es运单的状态
				esWaybillService.updateEsRelationInfos(relations);
				waybillIdList.addAll(waybillIds);
			}
		}
		// 同步善道4.0
		// waybillService.pushWaybillReachShanDao(waybillIdList,0);

		//记录操作日志
		String actionName = "";
		Integer actionScene = 0;
		if (BusinessConstants.FINANCE_STATEMENT_CANCEL.equals(state)) {
			actionName = "发票撤销(开票id：" + StringUtils.join(statementIds, ",");
			actionScene = BusinessConstants.ACTION_SCENE_FINANCE_STATEMENT_CANCEL;
		} else if (BusinessConstants.FINANCE_STATEMENT_REJECT.equals(state)) {
			actionName = "发票驳回(开票id：" + StringUtils.join(statementIds, ",");
			actionScene = BusinessConstants.ACTION_SCENE_FINANCE_STATEMENT_REJECT;
		} else if (BusinessConstants.FINANCE_STATEMENT_APPROVED.equals(state)) {
			actionName = "发票审批通过(开票id：" + StringUtils.join(statementIds, ",");
			actionScene = BusinessConstants.ACTION_SCENE_FINANCE_STATEMENT_APPROVED;
		} else if (BusinessConstants.FINANCE_STATEMENT_REGISTER_REJECT.equals(state)) {
			actionName = "发票登记驳回(开票id：" + StringUtils.join(statementIds, ",");
			actionScene = BusinessConstants.ACTION_SCENE_FINANCE_STATEMENT_REGISTER_REJECT;
		} else if (state == 8) {
			actionName = "运营审批驳回(开票id：" + StringUtils.join(statementIds, ",");
			actionScene = BusinessConstants.ACTION_SCENE_FINANCE_STATEMENT_REGISTER_REJECT;
		} else if (state == 9) {
			actionName = "运营审批通过(开票id：" + StringUtils.join(statementIds, ",");
			actionScene = BusinessConstants.ACTION_SCENE_FINANCE_STATEMENT_APPROVED;
		} else if (state == 10) {
			actionName = "财务审批驳回(开票id：" + StringUtils.join(statementIds, ",");
			actionScene = BusinessConstants.ACTION_SCENE_FINANCE_STATEMENT_REGISTER_REJECT;
		} else if (state == 11) {
			actionName = "财务审批通过(开票id：" + StringUtils.join(statementIds, ",");
			actionScene = BusinessConstants.ACTION_SCENE_FINANCE_STATEMENT_APPROVED;
		}
		actionName += "，请求参数：" + JSONUtil.toJsonStr(req);
		sysClientLogService.insertLog(actionScene, BusinessConstants.ACTION_TYPE_OTHER, org.apache.commons.lang3.StringUtils.substring(actionName, 0, 5000), StringUtils.join(statementIds, ","));
		return AjaxResult.success("操作成功");
	}



	public void invoiceApprovalDD(Long[] statementIds, Integer state) {
		// 1、基础数据准备并更新开票表：存放托运人-运单ID列表，key：托运人ID，value：运单ID列表
		Map<Long, List<Long>> customerWaybillIdsMap = new HashMap<>();
		for (Long id : statementIds) {
			FinanceStatement statement = financeStatementMapper.selectFinanceStatementById(id);
			List<Long> waybillIds = financeStatementWaybillMapper.selectListByStatementId(id);
			if (CommonUtil.isNotNullOrEmpty(waybillIds)) {
				List<Long> customerWaybillIds = customerWaybillIdsMap.get(statement.getCustomerId()) == null ? new ArrayList<>() : customerWaybillIdsMap.get(statement.getCustomerId());
				customerWaybillIds.addAll(waybillIds);
				customerWaybillIdsMap.put(statement.getCustomerId(), customerWaybillIds);
			}
			statement.setRejectReason("");
			statement.setState(state);
			statement.setAuditTime(DateUtils.getNowDate());
			statement.setUpdateBy("钉钉用户");

			// 退款
			feePayReturn(statement);

			financeStatementMapper.updateFinanceStatement(statement);
		}
		// 3、驳回或撤销时，更新运单相关状态
		if (0 == state || -1 == state || 6 == state) {
			for (Long customerId : customerWaybillIdsMap.keySet()) {
				// 3.1更新运单关联表中开票状态为--可开票
				List<Long> waybillIds = customerWaybillIdsMap.get(customerId);
				waybillCustomerRelationMapper.updateWaybillStatementStatus(2, waybillIds, customerId);
				// 3.2更新运单表中开票状态--如任何环节都未在开票中，则更新为未开票0，否则仍为开票中，无需更新
				List<WaybillCustomerRelation> relations = waybillCustomerRelationMapper.selectRelationByWaybillIds(waybillIds);
				List<Long> notNeedChangeBillStateWaybillIds = relations.stream().filter(r -> r.getCustomerId() != customerId && r.getWaybillState() > 2).map(f -> f.getWaybillId()).distinct()
						.collect(Collectors.toList());
				List<Long> needChangeBillStateWaybillIds = waybillIds.stream().filter(id -> !notNeedChangeBillStateWaybillIds.contains(id)).collect(Collectors.toList());
				if (CommonUtil.isNotNullOrEmpty(needChangeBillStateWaybillIds)) {
					waybillMapperEx.updateWaybillBillStatusByIds(needChangeBillStateWaybillIds, 0);
				}
				// 3.3更新es运单的状态
				esWaybillService.updateEsRelationInfos(relations);
			}
		}
	}

	/**
	 * 托运人审批开票申请单
	 *
	 * @param auditStatus
	 * 		-1撤销, 0驳回, 3审批通过
	 * @param statementIds
	 * 		账单对象
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public AjaxResult shipperAudit(List<Long> statementIds, Integer auditStatus) {
		// 1、校验
		// state为-1撤销, 0驳回, 3审批通过
		if (null == statementIds || statementIds.size() == 0 || null == auditStatus || !Arrays.asList(-1, 0, 3).contains(auditStatus)) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "参数错误");
		}
		if (!ClientType.SHIPPER.equals(SecurityUtils.getClientType()) && !FINANCE.equals(SecurityUtils.getClientType())) {
			return AjaxResult.error(HttpStatus.FORBIDDEN, "托运人接口");
		}

		// 获取基础数据
		Map<Long, List<Long>> customerWaybillIdsMap = new HashMap<>();
		List<FinanceStatement> statementList = new ArrayList<>();
		for (Long statementId : statementIds) {
			FinanceStatement statement = financeStatementMapper.selectFinanceStatementById(statementId);
			statementList.add(statement);
			if (null == statement) {
				continue;
			}
			if (StringUtils.isNotBlank(statement.getInstanceId())) {
				return AjaxResult.error(HttpStatus.FORBIDDEN, "钉钉申请的开票，请前往钉钉操作撤回");
			}
			FinanceStatementWaybill fswQuery = new FinanceStatementWaybill();
			fswQuery.setStatementId(statementId);
			List<FinanceStatementWaybill> fsWaybillList = financeStatementWaybillMapper.selectFinanceStatementWaybillList(fswQuery);
			List<Long> customerWaybillIds = customerWaybillIdsMap.containsKey(statement.getCustomerId()) ? customerWaybillIdsMap.get(statement.getCustomerId()) : new ArrayList<>();
			if (CommonUtil.isNotNullOrEmpty(fsWaybillList)) {
				for (FinanceStatementWaybill fsw : fsWaybillList) {
					customerWaybillIds.add(fsw.getWaybillId());
				}
			}
			customerWaybillIdsMap.put(statement.getCustomerId(), customerWaybillIds);
		}

		if (statementIds.size() > 1) {
			if (-1 == auditStatus) {
				return AjaxResult.error("线上支付的申请单无法批量驳回");
			}
		}

		// 退款
		for (FinanceStatement statement : statementList) {
			if (auditStatus == -1 && statement.getPaymentType() == 1 && statement.getProfitState() == 1){
				AjaxResult ajaxResult = feePayReturn(statement);
				if (ajaxResult.isNotSuccess()) {
					return ajaxResult;
				}
			}
			FinanceStatement update = new FinanceStatement();
			update.setId(statement.getId());
			update.setPayReturnTime(statement.getPayReturnTime());
			update.setPayReturnState(statement.getPayReturnState());
			financeStatementMapper.updateFinanceStatement(update);
		}

		// 2、更新开票表中审批状态
		financeStatementMapperEx.invoiceApproval(statementIds.toArray(new Long[0]), auditStatus, DateUtils.getNowDate(), SecurityUtils.getNickname());

		// 3、驳回或撤销时，更新运单相关状态
		if (0 == auditStatus || -1 == auditStatus) {
			// 3.1、删除开票表中运单数据
			// financeStatementWaybillMapperEx.deleteStatementByStatementIds(statementIds);
			// 3.2、更新运单表开票相关状态
			for (Long customerId : customerWaybillIdsMap.keySet()) {
				List<Long> customerWaybillIds = customerWaybillIdsMap.get(customerId);
				if (null == customerWaybillIds || customerWaybillIds.isEmpty()) {
					continue;
				}
				// 3.2.1、更新运单关联表中开票状态为--可开票
				waybillCustomerRelationMapper.updateWaybillStatementStatus(2, customerWaybillIds, customerId);
				// 3.3.2、更新运单表开票状态--如任何环节都未在开票中，则更新为未开票0，否则仍为开票中，无需更新
				List<WaybillCustomerRelation> relations = waybillCustomerRelationMapper.selectRelationByWaybillIds(customerWaybillIds);
				List<Long> notNeedChangeBillStateWaybillIds = relations.stream().filter(r -> r.getCustomerId() != customerId && r.getWaybillState() > 2).map(f -> f.getWaybillId()).distinct()
						.collect(Collectors.toList());
				List<Long> needChangeBillStateWaybillIds = customerWaybillIds.stream().filter(id -> !notNeedChangeBillStateWaybillIds.contains(id)).collect(Collectors.toList());
				if (CommonUtil.isNotNullOrEmpty(needChangeBillStateWaybillIds)) {
					waybillMapperEx.updateWaybillBillStatusByIds(needChangeBillStateWaybillIds, 0);
				}
			}
		}
		// 4、更新es数据，重新查询，确保拿到的最新值
		List<Long> waybillIds = customerWaybillIdsMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
		List<WaybillCustomerRelation> relations = waybillCustomerRelationMapper.selectRelationByWaybillIds(waybillIds);
		esWaybillService.updateEsRelationInfos(relations);

		//同步善道4.0
		// waybillService.pushWaybillReachShanDao(waybillIds,0);

		//记录操作日志
		String actionName = "";
		Integer actionScene = 0;
		if (BusinessConstants.FINANCE_STATEMENT_CANCEL.equals(auditStatus)) {
			actionName = "发票撤销(开票id：" + StringUtils.join(statementIds, ",");
			actionScene = BusinessConstants.ACTION_SCENE_FINANCE_STATEMENT_CANCEL;
		} else if (BusinessConstants.FINANCE_STATEMENT_REJECT.equals(auditStatus)) {
			actionName = "发票驳回(开票id：" + StringUtils.join(statementIds, ",");
			actionScene = BusinessConstants.ACTION_SCENE_FINANCE_STATEMENT_REJECT;
		} else if (BusinessConstants.FINANCE_STATEMENT_APPROVED.equals(auditStatus)) {
			actionName = "发票审批通过(开票id：" + StringUtils.join(statementIds, ",");
			actionScene = BusinessConstants.ACTION_SCENE_FINANCE_STATEMENT_APPROVED;
		}
		actionName += "，请求参数：开票id：" + StringUtils.join(statementIds, ",") + "开票状态：" + auditStatus;
		sysClientLogService.insertLog(actionScene, BusinessConstants.ACTION_TYPE_OTHER, org.apache.commons.lang3.StringUtils.substring(actionName, 0, 5000), StringUtils.join(statementIds, ","));

		// 5、返回
		return AjaxResult.success("操作成功");
	}

	/**
	 * 利润标记
	 */
	@Override
	public AjaxResult profitSign(List<Long> ids, String remark) {
		if (null == ids || ids.size() == 0) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "要操作的运输费标记数据为空");
		}
		if (StringUtils.isBlank(remark)) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "运输费标记备注信息为空");
		}
		financeStatementMapperEx.profitSign(ids, remark);

		//记录操作日志
		String actionName = "销项发票登记利润标记(开票id：" + StringUtils.join(ids, ",");
		actionName += "，请求参数：开票id：" + StringUtils.join(ids, ",") + "备注信息:" + remark;
		sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_INVOICE, BusinessConstants.ACTION_TYPE_OTHER, org.apache.commons.lang3.StringUtils.substring(actionName, 0, 5000),
				StringUtils.join(ids, ","));
		return AjaxResult.success("操作成功");
	}

	/**
	 * 待审批的开票申请单数量
	 *
	 * @param usage
	 * 		使用途径（1.申请开票 2.开票审批）
	 * @return
	 */
	@Override
	public Long countStatementForAudit(int usage) {
		FinanceStatement req = new FinanceStatement();
		LoginUser loginUser = SecurityUtils.getLoginUser();
		switch (loginUser.getClientType()) {
		case SHIPPER:
			if (1 == usage) { // 1.申请开票
				req.setCustomerId(loginUser.getTenantUser().getTenantId());
			} else if (2 == usage) { // 2.开票审批
				req.setAuditCustomerId(loginUser.getTenantUser().getTenantId());
			}
			break;

		case PLATFORM:
			req.setAuditFreightForwarderId(loginUser.getUser().getUserId());
			break;
		}
		return financeStatementMapperEx.countStatementForAudit(req);
	}

	@Override
	public AjaxResult uploadFileForInvoiceBatchApply(Long customerId, List<String> waybillCodes, String nextCustomerId, Long[] frameworkContractIds) {
		List<String> errorMessages = new ArrayList<>();
		if (waybillCodes.size() > 2000) {
			errorMessages.add("导入的文件中每次最多支持2000条运单");
		}
		List<String> shippingNoteNumbers = waybillCodes.stream().distinct().collect(Collectors.toList());
		if (shippingNoteNumbers.size() < waybillCodes.size()) {
			errorMessages.add("存在重复的运单号，请确认后上传");
		}
		// 1、获取运单号关联的运单
		List<Waybill> waybills = CommonUtil.isNullOrEmpty(shippingNoteNumbers) ? new ArrayList<>() : waybillService.selectWaybillList(shippingNoteNumbers);

		// 判断是否没有找到任何一条运单
		if (waybills.isEmpty()) {
			errorMessages.add("文件中未含有系统中存在的运单，请确认后上传");
			return AjaxResult.errorNext(String.join(";", errorMessages));
		}
		// 创建一个集合来存储未找到的运单号
		List<String> notFoundWaybillNumbers = new ArrayList<>();
		// 遍历原始的运单号列表
		for (String shippingNoteNumber : shippingNoteNumbers) {
			boolean found = false; // 假设当前运单号未找到
			// 在查询结果中查找当前运单号
			for (Waybill waybill : waybills) {
				if (shippingNoteNumber.equals(waybill.getShippingNoteNumber())) {
					found = true; // 找到了当前运单号
					break;
				}
			}
			// 如果当前运单号未找到，则将其添加到未找到的列表中
			if (!found) {
				notFoundWaybillNumbers.add(shippingNoteNumber);
			}
		}
		// 判断是否有未找到的运单号，并进行相应的处理
		if (!notFoundWaybillNumbers.isEmpty()) {
			// 返回包含错误信息的结果
			errorMessages.add("当前商户下未查询到运单：" + notFoundWaybillNumbers + "请纠正数据后重试");
		}

		// 没有正确的运单直接返回错误,不做之后的校验
		if (!errorMessages.isEmpty()) {
			String errorMessageString = String.join(";", errorMessages);
			return AjaxResult.popError(errorMessageString);
		}

		List<Long> waybillIds = waybills.stream().map(Waybill::getId).collect(Collectors.toList());
		List<WaybillRes> waybillList = JSON.parseArray(JSON.toJSONString(waybills), WaybillRes.class);
		// 2、校验有效性，校验成功时返回项目转包链
		// 原来查询可以申请开票的运单号，现在查询所有的运单，取出每笔运单的状态，分别判断已开票和申请开票
		List<WaybillCustomerRelation> waybillCustomerRelations = waybillCustomerService.selectApplyInvoiceWaybills(customerId, waybillIds);
		List<WaybillCustomerRelation> canApplyWaybills = waybillCustomerService.selectCanApplyInvoiceWaybills(customerId, waybillIds);
		FinanceStatementReq req = new FinanceStatementReq();
		req.setNextCustomerId(Long.parseLong(nextCustomerId));
		if (frameworkContractIds != null) {
			List<String> frameworkContractIdStrings = Arrays.stream(frameworkContractIds).map(Object::toString).collect(Collectors.toList());
			req.setFrameworkContractIds(frameworkContractIdStrings);
		}
		List<String> errorMessage = verifyApplyInvoice(waybillList, waybillIds, customerId, waybillCustomerRelations, req, errorMessages);
		if (!errorMessage.isEmpty()) {
			String errorMessageString = String.join(";", errorMessage);
			return AjaxResult.errorNext(errorMessageString);
		}
		// 3、根据实际付款计算开票金额
		Map<Long, BigDecimal> rateMap = canApplyWaybills.stream().collect(Collectors.toMap(WaybillCustomerRelation::getWaybillId, a -> a.getRateToMe()));
		FileApplyForInvoiceRes res = new FileApplyForInvoiceRes();
		res.setWaybillId(waybillIds);
		res.setWaybillSize(waybillIds.size());
		CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(Long.parseLong(nextCustomerId));
		res.setCustomerName(customerInfo.getCustomerName());
		return caculateFare(res, waybillList, rateMap);
	}

	@Override
	public AjaxResult uploadFileForInvoiceBatchApplybehalf(Long customerId, List<String> waybillCodes, String aboveCustomerId, Long[] frameworkContractIds) {
		List<String> errorMessages = new ArrayList<>();
		if (waybillCodes.size() > 2000) {
			errorMessages.add("导入的文件中每次最多支持2000条运单");
		}
		List<String> shippingNoteNumbers = waybillCodes.stream().distinct().collect(Collectors.toList());
		if (shippingNoteNumbers.size() < waybillCodes.size()) {
			errorMessages.add("存在重复的运单号，请确认后上传");
		}
		// 1、获取运单号关联的运单
		List<Waybill> waybills = CommonUtil.isNullOrEmpty(shippingNoteNumbers) ? new ArrayList<>() : waybillService.selectWaybillList(shippingNoteNumbers);

		// 判断是否没有找到任何一条运单
		if (waybills.isEmpty()) {
			errorMessages.add("文件中未含有系统中存在的运单，请确认后上传");
			return AjaxResult.errorNext(String.join(";", errorMessages));
		}
		// 创建一个集合来存储未找到的运单号
		List<String> notFoundWaybillNumbers = new ArrayList<>();
		// 遍历原始的运单号列表
		for (String shippingNoteNumber : shippingNoteNumbers) {
			boolean found = false; // 假设当前运单号未找到
			// 在查询结果中查找当前运单号
			for (Waybill waybill : waybills) {
				if (shippingNoteNumber.equals(waybill.getShippingNoteNumber())) {
					found = true; // 找到了当前运单号
					break;
				}
			}
			// 如果当前运单号未找到，则将其添加到未找到的列表中
			if (!found) {
				notFoundWaybillNumbers.add(shippingNoteNumber);
			}
		}
		// 判断是否有未找到的运单号，并进行相应的处理
		if (!notFoundWaybillNumbers.isEmpty()) {
			// 返回包含错误信息的结果
			errorMessages.add("当前商户下未查询到运单：" + notFoundWaybillNumbers + "请纠正数据后重试");
		}
		List<Long> waybillIds = waybills.stream().map(Waybill::getId).collect(Collectors.toList());
		List<WaybillRes> waybillList = JSON.parseArray(JSON.toJSONString(waybills), WaybillRes.class);
		// 2、校验有效性，校验成功时返回项目转包链
		// 原来查询可以申请开票的运单号，现在查询所有的运单，取出每笔运单的状态，分别判断已开票和申请开票
		List<WaybillCustomerRelation> waybillCustomerRelations = waybillCustomerService.selectApplyInvoiceWaybills(Long.parseLong(aboveCustomerId), waybillIds);
		// List<WaybillCustomerRelation> canApplyWaybills = waybillCustomerService.selectCanApplyInvoiceWaybills(customerId, waybillIds);
		FinanceStatementReq req = new FinanceStatementReq();
		req.setAboveCustomerId(Long.parseLong(aboveCustomerId));
		List<String> frameworkContractIdStrings = Arrays.stream(frameworkContractIds).map(Object::toString) // 将 Long 转换为 String
				.collect(Collectors.toList());
		req.setFrameworkContractIds(frameworkContractIdStrings);
		List<String> errorMessage = verifyApplyInvoiceBehalf(waybillList, waybillIds, customerId, waybillCustomerRelations, req, errorMessages);
		if (!errorMessage.isEmpty()) {
			String errorMessageString = String.join(";", errorMessage);
			return AjaxResult.errorNext(errorMessageString);
		}
		// FrameworkContractSubchain subchain = (FrameworkContractSubchain) verifyResult.get("data");
		// 3、根据实际付款计算开票金额
		Map<Long, BigDecimal> rateMap = waybillCustomerRelations.stream().collect(Collectors.toMap(WaybillCustomerRelation::getWaybillId, a -> a.getRateToMe()));
		FileApplyForInvoiceRes res = new FileApplyForInvoiceRes();
		res.setWaybillId(waybillIds);
		res.setWaybillSize(waybillIds.size());
		// res.setContractId(subchain.getContractId());
		// 查询销售方名称，也就是当前登录人的customerId
		CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(Long.parseLong(aboveCustomerId));
		res.setCustomerName(customerInfo.getCustomerName());
		return caculateFareBehalf(res, waybillList, rateMap);
	}

	/**
	 * 查询开票
	 *
	 * @param id
	 * 		开票主键
	 * @return 开票
	 */
	@Override
	public FinanceStatementEx selectFinanceStatementInfoById(Long id) {
		FinanceStatement statement = financeStatementMapper.selectFinanceStatementById(id);
		FinanceStatementEx res = JSON.parseObject(JSON.toJSONString(statement), FinanceStatementEx.class);
		if (CommonUtil.isNotNullOrEmpty(statement.getBuyerInfo())) {
			InvoiceHeader buyerInfo = JSON.parseObject(statement.getBuyerInfo(), InvoiceHeader.class);
			BeanUtil.copyProperties(buyerInfo, res);
		}

		if (statement.getAuditFreightForwarderId() == 0){
			res.setSellName(customerInfoMapper.selectCustomerInfoById(statement.getAuditCustomerId()).getCustomerName());
		}else {
			res.setSellName(freightForwarderInfoService.selectFreightForwarderInfoById(statement.getAuditFreightForwarderId()).getName());
		}
		res.setFileList(selectAndDownloadByStatementId(id));
		res.setTotalActualFare(financeStatementMapper.selectActualFareByStatementId(id));

		res.setPaymentDate("0000-00-00 00:00:00".equals(res.getPaymentDate()) ? null : res.getPaymentDate());
		return res;
	}

	@Override
	public List<WaybillRes> waybillList(List<WaybillRes> waybillList, FrameworkContractSubchain subchain) {
		// 2、计算
		for (WaybillRes waybill : waybillList) {
			MakeCode mc = makeCodeMapper.selectMakeCodeById(waybill.getMakeCodeId());

			Integer unit = waybill.getFeeUnit();
			Integer supplyType = mc.getSupplyType();
			Integer waybillPaymentType = mc.getWaybillPaymentType();
			if (3 == unit) {
				waybill.setFeeAmount(BigDecimal.ONE);
			} else if (null != supplyType && null != waybillPaymentType) {
				// 货源类型（0：三方 1：大宗）
				if (1 == supplyType) {
					// 大宗货源，获取运费结算方式（0：按装货数量结算 1：按卸货数量结算 2：按最小值结算）
					WaybillSlave ws = waybillSlaveService.selectWaybillSlaveById(waybill.getId());
					if (0 == waybillPaymentType) {
						waybill.setFeeAmount(1 == unit ? ws.getLoadingWeight() : ws.getLoadingCube());
					} else if (1 == waybillPaymentType) {
						waybill.setFeeAmount(1 == unit ? ws.getUnloadWeight() : ws.getUnloadCube());
					} else if (2 == waybillPaymentType) {
						waybill.setFeeAmount(1 == unit ? NumberUtil.min(ws.getLoadingWeight(), ws.getUnloadWeight()) : NumberUtil.min(ws.getLoadingCube(), ws.getUnloadCube()));
					}
				}
				if (0 == supplyType) {
					// 三方货源 小程序接单的数量在slave表
					WaybillSlave ws = waybillSlaveService.selectWaybillSlaveById(waybill.getId());
					if (waybill.getFeeAmount().compareTo(BigDecimal.ZERO) == 0) {
						waybill.setFeeAmount(ws.getLoadingWeight() == null ? ws.getLoadingCube() : ws.getLoadingWeight());
					}
				}
			}
		}
		return waybillList;
	}

	public void shipperAuditDD(Long[] statementIds, Integer auditStatus) {

		Date curDate = new Date();

		// 存放托运人-运单ID列表，key：托运人ID，value：运单ID列表
		Map<Long, List<Long>> customerWaybillIdsMap = new HashMap<>();
		List<Long> waybillIds = new ArrayList<Long>();
		List<Long> statementIdList = new ArrayList<Long>();
		for (Long id : statementIds) {
			FinanceStatement fs = financeStatementMapper.selectFinanceStatementById(id);
			FinanceStatementWaybill fswQuery = new FinanceStatementWaybill();
			fswQuery.setStatementId(id);
			List<FinanceStatementWaybill> fswList = financeStatementWaybillMapper.selectFinanceStatementWaybillList(fswQuery);
			if (null != fswList && !fswList.isEmpty()) {
				for (FinanceStatementWaybill fsw : fswList) {
					waybillIds.add(fsw.getWaybillId());
				}
			}
			customerWaybillIdsMap.put(fs.getCustomerId(), waybillIds);
			statementIdList.add(id);
		}

		Integer waybillState = null;
		if (0 == auditStatus) {
			waybillState = 2;
		} else if (-1 == auditStatus) {
			waybillState = 2;
		}
		if (null != waybillState) {
			for (Map.Entry<Long, List<Long>> entry : customerWaybillIdsMap.entrySet()) {
				waybillCustomerRelationMapper.updateWaybillStatementStatus(waybillState, entry.getValue(), entry.getKey());

				List<WaybillCustomerRelation> list = new ArrayList<>();
				for (Long waybillId : waybillIds) {
					WaybillCustomerRelation query = new WaybillCustomerRelation();
					query.setWaybillId(waybillId);
					query.setCustomerId(entry.getKey());
					List<WaybillCustomerRelation> relations = waybillCustomerRelationMapper.selectWaybillCustomerRelationList(query);

					for (WaybillCustomerRelation relation : relations) {
						relation.setWaybillState(BusinessConstants.WAYBILL_INVOICE_STATE_INVOICING);
					}
					list.addAll(relations);
				}
				esWaybillService.updateEsRelationInfos(list);
			}
		}

		String userName = "钉钉用户";
		// 更新运单开票表审批状态
		financeStatementMapperEx.invoiceApproval(statementIds, auditStatus, curDate, userName);

		// 如果是驳回，要把申请开票的运单清空
		if (0 == auditStatus || -1 == auditStatus) {
			financeStatementWaybillMapperEx.deleteStatementByStatementIds(statementIdList);
		}
	}

	/**
	 * 获取签约主体托运人与上游客户对账记录
	 *
	 * @param map
	 * @return
	 */
	@Override
	public List<StatementRecordRes> selectUpstreamStatementList(Map<String, Object> map) {
		return financeStatementMapperEx.selectUpstreamStatementList(map);
	}

	/**
	 * 获取签约主体托运人与上游客户对账记录
	 *
	 * @param map
	 * @return
	 */
	@Override
	public List<BillMarkRecordRes> selectUpstreamMarkStatementList(Map<String, Object> map) {
		return financeStatementMapperEx.selectUpstreamMarkStatementList(map);
	}

	public List<WaybillRes> findWaybillByStatementId(Long statementId) {
		return financeStatementMapperEx.findWaybillByStatementId(statementId);
	}

	@Override
	public AjaxResult previewBillDetail(FinanceStatementReq req) {
		BillDetailRes billDetailRes = new BillDetailRes();
		List<Long> waybillIds = new ArrayList<>();
		String[] split = req.getWaybillIds().split(",");
		for (String s : split) {
			waybillIds.add(Long.parseLong(s));
		}
		// 1、基础信息查询
		List<WaybillRes> waybillList = selectStatementWaybillList(waybillIds);

		Integer type = req.getBillDetailType();
		Long shipperId = 0L;
		Long thisShipperId = 0L;
		Integer customerType = req.getCustomerType();
		List<WaybillCustomerRelation> canApplyWaybills = new ArrayList<>();
		if (customerType == 0) {
			shipperId = SecurityUtils.getShipperId();
			// 2、校验有效性，校验成功时返回项目转包链中该托运人的下一层（审核人信息）
			canApplyWaybills = waybillCustomerService.selectCanApplyInvoiceWaybills(shipperId, waybillIds);
		} else {
			if (ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
				Long freightForwarderId = SecurityUtils.getFreightForwarderId();
				FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(freightForwarderId);
				thisShipperId = freightForwarderInfo.getCustomerId();
			} else {
				thisShipperId = SecurityUtils.getShipperId();
			}
			Long frameworkContractId = waybillList.get(0).getFrameworkContractId();
			List<FrameworkContractSubchain> frameworkContractSubchains = frameworkContractSubchainService.selectFrameworkContractSubchainByContractId(frameworkContractId);
			for (FrameworkContractSubchain frameworkContractSubchain : frameworkContractSubchains) {
				if (thisShipperId.equals(frameworkContractSubchain.getCustomerId())) {
					shipperId = frameworkContractSubchain.getParentCustomerId();
				}
			}
			canApplyWaybills = waybillCustomerService.selectInApplyInvoiceWaybills(shipperId, waybillIds);
		}

		// 4、根据实际付款计算开票金额
		// 申请开票的总金额
		FileApplyForInvoiceRes res = new FileApplyForInvoiceRes();
		Map<Long, BigDecimal> rateMap = canApplyWaybills.stream().collect(Collectors.toMap(WaybillCustomerRelation::getWaybillId, a -> a.getRateToMe()));
		AjaxResult caculateFareResult = caculateFare(res, waybillList, rateMap);
		if (caculateFareResult.isNotSuccess()) {
			return caculateFareResult;
		}
		if (type == 1) {
			BillDetail detail = new BillDetail();
			BigDecimal totalPrice = res.getInvoiceFare();
			BigDecimal amount = BigDecimal.ZERO;
			detail.setProjectName("*运输服务*运费");// 项目名称
			detail.setUnit(waybillList.get(0).getFeeUnitString());// 单位
			detail.setTaxRate("9%"); // 税率
			detail.setMoney(totalPrice.divide(new BigDecimal("1.09"), 2, RoundingMode.HALF_UP));// 金额 税价合计/(1.09)
			detail.setTaxAmount(totalPrice.subtract(detail.getMoney()));// 税额 税价合计-金额
			for (WaybillRes waybillRes : waybillList) {
				amount = amount.add(waybillRes.getFeeAmount());
			}
			detail.setUnitPrice(detail.getMoney().divide(amount, 13, RoundingMode.HALF_UP));// 单价
			detail.setAmount(amount);// 数量
			billDetailRes.setList(Arrays.asList(detail));
			billDetailRes.setTotalMoney(detail.getMoney());
			billDetailRes.setTotalTaxAmount(totalPrice.subtract(detail.getMoney()));// 税额合计
			billDetailRes.setTotalPrice(totalPrice);// 税价合计
		} else if (type == 2) {
			List<BillDetail> list = new ArrayList<>();
			BigDecimal totalPrice = res.getInvoiceFare();
			billDetailRes.setTotalMoney(totalPrice.divide(new BigDecimal("1.09"), 2, RoundingMode.HALF_UP));// 金额合计
			billDetailRes.setTotalTaxAmount(totalPrice.subtract(billDetailRes.getTotalMoney()));// 税额合计
			billDetailRes.setTotalPrice(totalPrice);// 税价合计

			BigDecimal money = billDetailRes.getTotalMoney();
			BigDecimal taxAmount = billDetailRes.getTotalTaxAmount();
			List<WaybillRes> waybillResList = waybillMapperEx.queryListByIds(waybillIds, 4);
			for (int i = 0; i < waybillResList.size(); i++) {
				if (i != waybillResList.size() - 1) {
					BillDetail detail = new BillDetail();
					BigDecimal amount = waybillResList.get(i).getFeeAmount();
					detail.setProjectName("*运输服务*运费");// 项目名称
					detail.setUnit(waybillResList.get(0).getFeeUnitString());// 单位
					detail.setTaxRate("9%"); // 税率
					Long id = waybillResList.get(i).getId();
					BigDecimal bigDecimal = rateMap.get(id);
					BigDecimal actualFare = waybillResList.get(i).getPayFare();
					BigDecimal invoiceFareSingle = actualFare.divide(BigDecimal.ONE.subtract(bigDecimal), RoundingMode.HALF_UP);
					detail.setMoney(invoiceFareSingle.divide(new BigDecimal("1.09"), 2, RoundingMode.HALF_UP));// 金额 税价合计/(1.09)
					detail.setTaxAmount(invoiceFareSingle.subtract(detail.getMoney()));// 税额 税价合计-金额
					detail.setUnitPrice(detail.getMoney().divide(amount, 13, RoundingMode.HALF_UP));// 单价
					detail.setAmount(amount);// 数量list.add(detail);
					list.add(detail);

					// 总计减本次 给最后一条数据使用
					money = money.subtract(detail.getMoney());
					taxAmount = taxAmount.subtract(detail.getTaxAmount());
				} else {
					BillDetail detail = new BillDetail();
					BigDecimal amount = waybillResList.get(i).getFeeAmount();
					detail.setProjectName("*运输服务*运费");// 项目名称
					detail.setUnit(waybillResList.get(0).getFeeUnitString());// 单位
					detail.setTaxRate("9%"); // 税率
					detail.setMoney(money);// 金额 税价合计/(1.09)
					detail.setTaxAmount(taxAmount);// 税额 税价合计-金额
					detail.setUnitPrice(money.divide(amount, 13, RoundingMode.HALF_UP));// 单价
					detail.setAmount(amount);// 数量list.add(detail);
					list.add(detail);
				}
				billDetailRes.setList(list);
			}
		} else {
			List<BillDetail> list = new ArrayList<>();
			BigDecimal totalPrice = res.getInvoiceFare();
			billDetailRes.setTotalMoney(totalPrice.divide(new BigDecimal("1.09"), 2, RoundingMode.HALF_UP));// 金额合计
			billDetailRes.setTotalTaxAmount(totalPrice.subtract(billDetailRes.getTotalMoney()));// 税额合计
			billDetailRes.setTotalPrice(totalPrice);// 税价合计

			BigDecimal money = billDetailRes.getTotalMoney();
			BigDecimal taxAmount = billDetailRes.getTotalTaxAmount();
			for (int i = 0; i < waybillList.size(); i++) {
				if (i != waybillList.size() - 1) {
					BillDetail detail = new BillDetail();
					BigDecimal amount = waybillList.get(i).getFeeAmount();
					detail.setProjectName("*运输服务*运费");// 项目名称
					detail.setUnit(waybillList.get(0).getFeeUnitString());// 单位
					detail.setTaxRate("9%"); // 税率
					BigDecimal invoiceFareSingle = waybillList.get(i).getActualFare().divide(BigDecimal.ONE.subtract(rateMap.get(waybillList.get(i).getId())), RoundingMode.HALF_UP);
					detail.setMoney(invoiceFareSingle.divide(new BigDecimal("1.09"), 2, RoundingMode.HALF_UP));// 金额 税价合计/(1.09)
					detail.setTaxAmount(invoiceFareSingle.subtract(detail.getMoney()));// 税额 税价合计-金额
					detail.setUnitPrice(detail.getMoney().divide(amount, 13, RoundingMode.HALF_UP));// 单价
					detail.setAmount(amount);// 数量list.add(detail);
					list.add(detail);

					// 总计减本次 给最后一条数据使用
					money = money.subtract(detail.getMoney());
					taxAmount = taxAmount.subtract(detail.getTaxAmount());
				} else {
					BillDetail detail = new BillDetail();
					BigDecimal amount = waybillList.get(i).getFeeAmount();
					detail.setProjectName("*运输服务*运费");// 项目名称
					detail.setUnit(waybillList.get(0).getFeeUnitString());// 单位
					detail.setTaxRate("9%"); // 税率
					detail.setMoney(money);// 金额 税价合计/(1.09)
					detail.setTaxAmount(taxAmount);// 税额 税价合计-金额
					detail.setUnitPrice(money.divide(amount, 13, RoundingMode.HALF_UP));// 单价
					detail.setAmount(amount);// 数量list.add(detail);
					list.add(detail);
				}
			}
			billDetailRes.setList(list);
		}
		billDetailRes.setTotalPriceString(StringUtils.toChineseMoney(billDetailRes.getTotalPrice()));
		return AjaxResult.success(billDetailRes);
	}

	@Override
	public AjaxResult previewBillBusiness(FinanceStatementReq req) {
		List<BillBusinessRes> list = new ArrayList<>();
		List<Long> waybillIds = new ArrayList<>();
		String[] split = req.getWaybillIds().split(",");
		for (String s : split) {
			waybillIds.add(Long.parseLong(s));
		}
		Integer addressType = req.getBillBusinessAddressType();
		Integer type = req.getBillBusinessType();
		if (type == 2) {
			List<WaybillRes> waybillResList = waybillMapperEx.queryListByIdsGroupByAddress(waybillIds, addressType);
			for (WaybillRes waybill : waybillResList) {
				BillBusinessRes res = new BillBusinessRes();
				res.setGoodsName(waybill.getDescriptionOfGoods());
				res.setPlateNo(waybill.getVehicleNumber());
				if (addressType == 1) {
					res.setStartAddress(waybill.getAProvince());
					res.setEndAddress(waybill.getBProvince());
				} else if (addressType == 2) {
					res.setStartAddress(waybill.getAProvince() + waybill.getACity());
					res.setEndAddress(waybill.getBProvince() + waybill.getBCity());
				} else if (addressType == 3) {
					res.setStartAddress(waybill.getAProvince() + waybill.getACity() + waybill.getAArea());
					res.setEndAddress(waybill.getBProvince() + waybill.getBCity() + waybill.getBArea());
				} else if (addressType == 4) {
					res.setStartAddress(waybill.getAProvince() + waybill.getACity() + waybill.getAArea() + waybill.getPlaceOfLoading());
					res.setEndAddress(waybill.getBProvince() + waybill.getBCity() + waybill.getBArea() + waybill.getGoodsReceiptPlace());
				} else if (addressType == 5) {
					res.setStartAddress(waybill.getPlaceOfLoading());
					res.setEndAddress(waybill.getGoodsReceiptPlace());
				}
				list.add(res);
			}
		} else if (type == 3) {
			List<WaybillRes> waybillResList = waybillMapperEx.queryListByIdsGroupByAddress(waybillIds, 0);
			for (WaybillRes waybill : waybillResList) {
				BillBusinessRes res = new BillBusinessRes();
				res.setGoodsName(waybill.getDescriptionOfGoods());
				res.setPlateNo(waybill.getVehicleNumber());
				if (addressType == 1) {
					res.setStartAddress(waybill.getAProvince());
					res.setEndAddress(waybill.getBProvince());
				} else if (addressType == 2) {
					res.setStartAddress(waybill.getAProvince() + waybill.getACity());
					res.setEndAddress(waybill.getBProvince() + waybill.getBCity());
				} else if (addressType == 3) {
					res.setStartAddress(waybill.getAProvince() + waybill.getACity() + waybill.getAArea());
					res.setEndAddress(waybill.getBProvince() + waybill.getBCity() + waybill.getBArea());
				} else if (addressType == 4) {
					res.setStartAddress(waybill.getAProvince() + waybill.getACity() + waybill.getAArea() + waybill.getPlaceOfLoading());
					res.setEndAddress(waybill.getBProvince() + waybill.getBCity() + waybill.getBArea() + waybill.getGoodsReceiptPlace());
				} else if (addressType == 5) {
					res.setStartAddress(waybill.getPlaceOfLoading());
					res.setEndAddress(waybill.getGoodsReceiptPlace());
				}
				list.add(res);
			}
		}

		return AjaxResult.success(list);
	}

	@Override
	public void downloadTemplateFile(HttpServletResponse response, Integer type) {
		BufferedInputStream bis = null;
		BufferedOutputStream bos = null;
		try {
			// 下载的模板文件的名称
			String filename = "";
			if (type == 1) {
				URLEncoder.encode("发票信息明细导入模板" + BusinessConstants.IMPORT_BILL_DETAIL_VERSION + ".xlsx", "UTF-8");
			} else {
				URLEncoder.encode("特定业务信息导入模板" + BusinessConstants.IMPORT_BILL_BUSINESS_VERSION + ".xlsx", "UTF-8");
			}
			// 设置response参数，可以打开下载页面
			response.reset();
			response.setContentType("application/octet-stream; charset=UTF-8");
			response.setHeader("Access-Control-Allow-Origin", "*");
			// noinspection UastIncorrectHttpHeaderInspection
			response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
			response.setHeader("Content-Disposition", "attachment;filename=" + filename);// 下载文件的名称
			// 下载的文件源目录
			String filePath = "";
			if (type == 1) {
				filePath = "templates/billDetail" + BusinessConstants.IMPORT_BILL_DETAIL_VERSION + ".xlsx";
			} else {
				filePath = "templates/billBusiness" + BusinessConstants.IMPORT_BILL_BUSINESS_VERSION + ".xlsx";
			}
			ClassPathResource resource = new ClassPathResource(filePath);

			// bis = new BufferedInputStream(Files.newInputStream(file.toPath()));
			bis = new BufferedInputStream(resource.getInputStream());
			bos = new BufferedOutputStream(response.getOutputStream());
			byte[] buff = new byte[2048];
			int bytesRead;
			while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
				bos.write(buff, 0, bytesRead);
			}
			response.flushBuffer();
		} catch (final IOException e) {
			e.printStackTrace();
			log.error("下载发票导入模板出错，" + e.getMessage());
		} finally {
			IOUtils.closeQuietly(bis);
			IOUtils.closeQuietly(bos);
		}
	}

	@Resource
	private FrameworkContractSubchainServiceImpl frameworkContractSubchainService;
	@Resource
	private FreightForwarderInfoServiceImpl freightForwarderInfoService;

	@Override
	public AjaxResult importBillDetail(Workbook workbook, List<Long> waybillIds, Integer customerType) {
		// 1、基础信息查询
		List<WaybillRes> waybillList = selectStatementWaybillList(waybillIds);
		BillDetailRes billDetailRes = new BillDetailRes();
		Long shipperId = 0L;
		Long thisShipperId = 0L;
		if (customerType == 1) {
			if (ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
				Long freightForwarderId = SecurityUtils.getFreightForwarderId();
				FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(freightForwarderId);
				thisShipperId = freightForwarderInfo.getCustomerId();
			} else {
				thisShipperId = SecurityUtils.getShipperId();
			}
			Long frameworkContractId = waybillList.get(0).getFrameworkContractId();
			List<FrameworkContractSubchain> frameworkContractSubchains = frameworkContractSubchainService.selectFrameworkContractSubchainByContractId(frameworkContractId);
			for (FrameworkContractSubchain frameworkContractSubchain : frameworkContractSubchains) {
				if (thisShipperId.equals(frameworkContractSubchain.getCustomerId())) {
					shipperId = frameworkContractSubchain.getParentCustomerId();
				}
			}
			// 2、校验有效性，校验成功时返回项目转包链中该托运人的下一层（审核人信息）
			List<WaybillCustomerRelation> canApplyWaybills = waybillCustomerService.selectInApplyInvoiceWaybills(shipperId, waybillIds);

			// 3、运单是否属于同一转包链
			List<WaybillContractChain> chains = waybillContractChainService.findByIds(waybillIds);
			Map<Long, List<WaybillContractChain>> chainIds = chains.stream().collect(Collectors.groupingBy(WaybillContractChain::getChainId));

			Long chainId = chains.get(0).getChainId();
			// 4、运单转包链是否设置费率，即下一层是否有费率
			FrameworkContractSubchain subchain = getLowerSubhain(chainId, shipperId);

			// 5、根据实际付款计算开票金额
			// 申请开票的总金额
			FileApplyForInvoiceRes res = new FileApplyForInvoiceRes();
			Map<Long, BigDecimal> rateMap = canApplyWaybills.stream().collect(Collectors.toMap(WaybillCustomerRelation::getWaybillId, a -> a.getRateToMe()));
			AjaxResult caculateFareResult = caculateFare(res, waybillList, rateMap);
			if (caculateFareResult.isNotSuccess()) {
				return caculateFareResult;
			}

			List<List<String>> rows = ExcelUtil.readWorkbookInfo(workbook, 2, Column.count(), true);

			List<BillDetail> list = new ArrayList<>();
			BigDecimal importTotalPrice = BigDecimal.ZERO;
			BigDecimal importTotalMoney = BigDecimal.ZERO;
			BigDecimal importTotalTaxAmount = BigDecimal.ZERO;
			for (List<String> row : rows) {
				BillDetail detail = new BillDetail();
				detail.setProjectName(row.get(0));// 项目名称
				detail.setUnit(row.get(1));// 单位

				if (StringUtils.isEmpty(row.get(0))) {
					return AjaxResult.popError("导入模板中单位为空");
				} else {
					detail.setUnit(row.get(0));
				}
				try {
					detail.setAmount(new BigDecimal(row.get(1)));// 数量
				} catch (Exception e) {
					return AjaxResult.popError("导入模板中数量存在非数字类型字符");
				}
				try {
					BigDecimal total = new BigDecimal(row.get(2));
					detail.setMoney(total.divide(new BigDecimal("1.09"), 2, RoundingMode.HALF_UP));// 金额 税价合计/(1.09)
					importTotalPrice = importTotalPrice.add(total);
				} catch (Exception e) {
					e.printStackTrace();
					return AjaxResult.popError("导入模板中金额存在非数字类型字符");
				}
				detail.setProjectName("*运输服务*运费");
				detail.setTaxRate("9%");// 税率
				detail.setTaxAmount(new BigDecimal(row.get(2)).subtract(detail.getMoney()));// 合计 - 金额 = 税额
				detail.setUnitPrice(detail.getMoney().divide(detail.getAmount(), 13, RoundingMode.HALF_UP));// 金额 / 数量 = 单价
				list.add(detail);

				importTotalMoney = importTotalMoney.add(detail.getMoney());
				importTotalTaxAmount = importTotalTaxAmount.add(detail.getTaxAmount());
			}

			FileApplyForInvoiceRes resultData = (FileApplyForInvoiceRes) caculateFareResult.getData();
			BigDecimal totalPrice = resultData.getInvoiceFare();

			if (totalPrice.compareTo(importTotalPrice) != 0) {
				return AjaxResult.popError("导入税价合计与运单开票金额不一致");
			}

			billDetailRes.setList(list);
			billDetailRes.setTotalMoney(importTotalMoney);// 总金额
			billDetailRes.setTotalTaxAmount(importTotalTaxAmount);// 税额合计
			billDetailRes.setTotalPrice(totalPrice);// 税价合计
			billDetailRes.setTotalPriceString(StringUtils.toChineseMoney(totalPrice));

			return AjaxResult.success(billDetailRes);
		} else {
			shipperId = SecurityUtils.getShipperId();

			// 2、校验有效性，校验成功时返回项目转包链中该托运人的下一层（审核人信息）
			List<WaybillCustomerRelation> canApplyWaybills = waybillCustomerService.selectCanApplyInvoiceWaybills(shipperId, waybillIds);
			AjaxResult verifyResult = verifyCanApplyInvoiceNew(waybillList, waybillIds, shipperId, canApplyWaybills);
			if (verifyResult.isNotSuccess()) {
				return verifyResult;
			}
			// 3、获取下一层开票审核人信息
			FrameworkContractSubchain subchain = (FrameworkContractSubchain) verifyResult.get("data");
			Long auditCustomerId = subchain.getCustomerId();
			Long auditFreightForwarderId = subchain.getIsLeaf() == 0 ? subchain.getFreightForwarderId() : 0;
			// 4、根据实际付款计算开票金额
			// 申请开票的总金额
			FileApplyForInvoiceRes res = new FileApplyForInvoiceRes();
			Map<Long, BigDecimal> rateMap = canApplyWaybills.stream().collect(Collectors.toMap(WaybillCustomerRelation::getWaybillId, a -> a.getRateToMe()));
			AjaxResult caculateFareResult = caculateFare(res, waybillList, rateMap);
			if (caculateFareResult.isNotSuccess()) {
				return caculateFareResult;
			}

			List<List<String>> rows = ExcelUtil.readWorkbookInfo(workbook, 2, Column.count(), true);

			List<BillDetail> list = new ArrayList<>();
			BigDecimal importTotalPrice = BigDecimal.ZERO;
			BigDecimal importTotalMoney = BigDecimal.ZERO;
			BigDecimal importTotalTaxAmount = BigDecimal.ZERO;
			for (List<String> row : rows) {
				BillDetail detail = new BillDetail();
				detail.setProjectName(row.get(0));// 项目名称
				detail.setUnit(row.get(1));// 单位

				if (StringUtils.isEmpty(row.get(0))) {
					return AjaxResult.popError("导入模板中单位为空");
				} else {
					detail.setUnit(row.get(0));
				}
				try {
					detail.setAmount(new BigDecimal(row.get(1)));// 数量
				} catch (Exception e) {
					return AjaxResult.popError("导入模板中数量存在非数字类型字符");
				}
				try {
					BigDecimal total = new BigDecimal(row.get(2));
					detail.setMoney(total.divide(new BigDecimal("1.09"), 2, RoundingMode.HALF_UP));// 金额 税价合计/(1.09)
					importTotalPrice = importTotalPrice.add(total);
				} catch (Exception e) {
					e.printStackTrace();
					return AjaxResult.popError("导入模板中金额存在非数字类型字符");
				}
				detail.setProjectName("*运输服务*运费");
				detail.setTaxRate("9%");// 税率
				detail.setTaxAmount(new BigDecimal(row.get(2)).subtract(detail.getMoney()));// 合计 - 金额 = 税额
				detail.setUnitPrice(detail.getMoney().divide(detail.getAmount(), 13, RoundingMode.HALF_UP));// 金额 / 数量 = 单价
				list.add(detail);

				importTotalMoney = importTotalMoney.add(detail.getMoney());
				importTotalTaxAmount = importTotalTaxAmount.add(detail.getTaxAmount());
			}

			FileApplyForInvoiceRes resultData = (FileApplyForInvoiceRes) caculateFareResult.getData();
			BigDecimal totalPrice = resultData.getInvoiceFare();

			if (totalPrice.compareTo(importTotalPrice) != 0) {
				return AjaxResult.popError("导入税价合计与运单开票金额不一致");
			}

			billDetailRes.setList(list);
			billDetailRes.setTotalMoney(importTotalMoney);// 总金额
			billDetailRes.setTotalTaxAmount(importTotalTaxAmount);// 税额合计
			billDetailRes.setTotalPrice(totalPrice);// 税价合计
			billDetailRes.setTotalPriceString(StringUtils.toChineseMoney(totalPrice));

			return AjaxResult.success(billDetailRes);
		}
	}

	@Override
	public AjaxResult importBillBusiness(Workbook workbook) {
		List<BillBusinessRes> list = new ArrayList<>();
		List<List<String>> rows = ExcelUtil.readWorkbookInfo(workbook, 1, Column.count(), true);

		for (List<String> row : rows) {
			BillBusinessRes res = new BillBusinessRes();
			res.setType("公路运输");
			res.setPlateNo(row.get(0));
			res.setStartAddress(row.get(1));
			res.setEndAddress(row.get(2));
			res.setGoodsName(row.get(3));
			list.add(res);
		}

		return AjaxResult.success(list);
	}

	@Override
	@Transactional
	public CommonResult financeInvoiceApproval(FinanceStatementRejectReq req) {
		Long statementId = req.getId();
		FinanceStatement statement = financeStatementMapper.selectFinanceStatementById(statementId);
		if (statement.getMarkStatus() == 1) {
			return CommonResult.error("已开票的申请单不得驳回");
		}
		Long buyerCustomerId = statement.getCustomerId();
		// 1、更新开票表数据
		statement.setState(6);
		statement.setRejectReason(req.getRejectReason());
		statement.setUpdateBy(SecurityUtils.getNickname());
		statement.setUpdateTime(DateUtils.getNowDate());
		financeStatementMapper.updateFinanceStatement(statement);
		// 2、更新运单托运人表开票状态 -- 上一步状态（不可开票或可开票，根据是否付款完成来判断）
		List<Long> waybillIds = financeStatementWaybillMapper.selectListByStatementId(statementId);
		List<WaybillRes> waybills = waybillMapperEx.selectWaybillListByWaybillIds(waybillIds);
		List<Long> invoiceCanWaybillIds = new ArrayList<>();
		List<Long> invoiceCanNotWaybillIds = new ArrayList<>();
		waybills.forEach(waybill -> {
			if (waybill.getSettleStatus() == 2) { // 已结算，回到可开票状态
				invoiceCanWaybillIds.add(waybill.getId());
			} else { // 未结算，回到不可开票状态
				invoiceCanNotWaybillIds.add(waybill.getId());
			}
		});
		if (CommonUtil.isNotNullOrEmpty(invoiceCanWaybillIds)) {
			waybillCustomerRelationMapper.updateWaybillStatementStatus(2, invoiceCanWaybillIds, buyerCustomerId);
		}
		if (CommonUtil.isNotNullOrEmpty(invoiceCanNotWaybillIds)) {
			waybillCustomerRelationMapper.updateWaybillStatementStatus(1, invoiceCanNotWaybillIds, buyerCustomerId);
		}
		// 3、更新运单表开票状态--如任何环节都未在开票中，则更新为未开票0，否则仍为开票中，无需更新
		List<WaybillCustomerRelation> relations = waybillCustomerRelationMapper.selectRelationByWaybillIds(waybillIds);
		List<Long> notNeedChangeBillStateWaybillIds = relations.stream().filter(r -> r.getCustomerId() != buyerCustomerId && r.getWaybillState() > 2).map(f -> f.getWaybillId()).distinct()
				.collect(Collectors.toList());
		List<Long> needChangeBillStateWaybillIds = waybillIds.stream().filter(id -> !notNeedChangeBillStateWaybillIds.contains(id)).collect(Collectors.toList());
		if (CommonUtil.isNotNullOrEmpty(needChangeBillStateWaybillIds)) {
			waybillMapperEx.updateWaybillBillStatusByIds(needChangeBillStateWaybillIds, 0);
		}
		// 4、更新es数据，重新查询，确保是拿到的最新值
		relations = waybillCustomerRelationMapper.selectRelationByWaybillIds(waybillIds);
		esWaybillService.updateEsRelationInfos(relations);
		// 5、返回结果
		return CommonResult.success("驳回成功");
	}

	public List<BillStatisticsInfo> billStatisticsListByCustomerId(Map<String, Object> map) {
		return financeStatementMapperEx.billStatisticsListByCustomerId(map);
	}

	public List<BillStatisticsInfo> billStatisticsListByContractId(Map<String, Object> map) {
		return financeStatementMapperEx.billStatisticsListByContractId(map);
	}

	public List<BillStatisticsInfo> billStatisticsListByCustomerIdAll(Map<String, Object> map) {
		return financeStatementMapperEx.billStatisticsListByCustomerIdAll(map);
	}

	public List<BillStatisticsInfo> billStatisticsListByContractIdAll(Map<String, Object> map) {
		return financeStatementMapperEx.billStatisticsListByContractIdAll(map);
	}

	@Override
	public AjaxResult isPlatform(FinanceStatementReq req) {
		FinancePlateFormRes res = new FinancePlateFormRes();
		res.setIsSD(0);
		res.setIsWh(0);
		res.setIsNextWh(0);
		res.setIsEmail(1);

		List<Long> waybillIds = new ArrayList<>();
		for (String s : req.getWaybillIds().split(",")) {
			waybillIds.add(Long.parseLong(s.trim()));
		}

		Long customerId = SecurityUtils.getShipperId();
		CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(customerId);

		// 1、基础信息查询
		List<WaybillRes> waybillList = selectStatementWaybillList(waybillIds);
		WaybillRes waybillRes = waybillList.get(0);
		res.setBillRemark("起止地：" + waybillRes.getLoadingCity() + "-" + waybillRes.getUnloadCity() + "  货物名称：" + waybillRes.getDescriptionOfGoods() + " 车牌号：" + waybillRes.getVehicleNumber());

		List<Long> contractIds = waybillList.stream().map(w -> w.getFrameworkContractId()).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
		if (contractIds.size() == 1) {
			FrameworkContract frameworkContract = frameworkContractService.selectFrameworkContractById(contractIds.get(0));
			BusinessModel businessModel = businessModelService.selectBusinessModelById(frameworkContract.getBusinessModelId());
			if (!"网络货运模式".equals(businessModel.getModelName())) {
				res.setIsWh(1);
			}
		} else {
			res.setIsWh(1);
		}

		// 2、校验有效性，校验成功时返回项目转包链中该托运人的下一层（审核人信息）
		List<WaybillCustomerRelation> canApplyWaybills = waybillCustomerService.selectCanApplyInvoiceWaybills(customerInfo.getId(), waybillIds);
		AjaxResult verifyResult = verifyCanApplyInvoiceNew(waybillList, waybillIds, customerInfo.getId(), canApplyWaybills);
		if (verifyResult.isNotSuccess()) {
			return verifyResult;
		}

		// 3、获取下一层开票审核人信息
		FrameworkContractSubchain subchain = (FrameworkContractSubchain) verifyResult.get("data");
		Long auditFreightForwarderId = subchain.getIsLeaf() == 0 ? subchain.getFreightForwarderId() : 0;

		if (auditFreightForwarderId == 0) {
			res.setIsNextWh(1);
			res.setIsSD(1);
			res.setBillRemark(null);
		} else {
			FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(auditFreightForwarderId);
			if (!"江苏善道智运科技有限公司".equals(freightForwarderInfo.getName())) {
				res.setIsSD(1);
				res.setBillRemark(null);
			}
			FreightForwarderConfig config = freightForwarderConfigService.selectFreightForwarderConfigById(auditFreightForwarderId);
			if (config != null && config.getIsEmail() == 0) {
				res.setIsEmail(0);
			}
		}
		return AjaxResult.success(res);
	}

	@Override
	public AjaxResult isPlatformByFare(FinanceStatementReq req) {
		FinancePlateFormRes res = new FinancePlateFormRes();
		res.setIsSD(1);
		res.setIsWh(1);
		res.setIsNextWh(1);
		res.setIsEmail(1);
		res.setIsOneContract(1);

		if (req.getFrameworkContractId() != null) {
			res.setIsOneContract(0);
			FrameworkContract frameworkContract = frameworkContractService.selectFrameworkContractById(req.getFrameworkContractId());
			BusinessModel businessModel = businessModelService.selectBusinessModelById(frameworkContract.getBusinessModelId());
			if ("网络货运模式".equals(businessModel.getModelName())) {
				res.setIsWh(0);
			}
		}

		if (req.getFreightForwarderId() != null) {
			res.setIsNextWh(0);
			Long freightForwarderId = req.getFreightForwarderId();
			FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoByTenantId(freightForwarderId);
			if (freightForwarderInfo != null) {
				if ("江苏善道智运科技有限公司".equals(freightForwarderInfo.getName())) {
					res.setIsSD(0);
				}
				FreightForwarderConfig config = freightForwarderConfigService.selectFreightForwarderConfigById(freightForwarderInfo.getId());
				if (config != null && config.getIsEmail() == 0) {
					res.setIsEmail(0);
				}
			}
		}
		return AjaxResult.success(res);
	}

	@Override
	public CommonResult uploadPaymentFile(FinanceStatementReq req) {
		FinanceStatement financeStatement = financeStatementMapper.selectFinanceStatementById(req.getId());
		if (financeStatement == null) {
			return CommonResult.error("未查询到对账单");
		}
		if (financeStatement.getPaymentType() == 1) {
			return CommonResult.error("已线上支付，无需操作");
		}

		if (financeStatement.getAuditType() == 1 && !PLATFORM.equals(SecurityUtils.getClientType())) {
			return CommonResult.error("请联系平台财务，在平台端审核上传凭证");
		}

		if (financeStatement.getState() == 9 && !PLATFORM.equals(SecurityUtils.getClientType()) && financeStatement.getProfitState() == 2) {
			return CommonResult.error("财务审批中，若需要更新，请联系平台财务在平台端修改");
		}

		FinanceStatement update = new FinanceStatement();
		update.setProfitState(req.getProfitState());
		if (StringUtils.isNotEmpty(req.getEmail())) {
			update.setEmail(req.getEmail());
		}
		if (StringUtils.isNotEmpty(req.getRemark())) {
			update.setRemark(req.getRemark());
		}

		if (req.getProfitState() == null){
			update.setProfitState(2);
		}else {
			update.setProfitState(req.getProfitState());
		}

		update.setId(req.getId());
		update.setPaymentDate(req.getPaymentDate());
		update.setUpdateBy(SecurityUtils.getNickname());
		update.setUpdateTime(new Date());
		financeStatementMapper.updateFinanceStatement(update);

		paymentAttachmentInfoService.deletePaymentAttachmentInfoByRelationId(req.getId());

		for (PaymentAttachmentInfo paymentAttachmentInfo : req.getList()) {
			paymentAttachmentInfo.setId(TextUtil.getTimeSequenceID(5));
			paymentAttachmentInfo.setRelationId(req.getId());
			paymentAttachmentInfo.setCreateBy(SecurityUtils.getNickname());
			paymentAttachmentInfo.setCreateTime(new Date());
			paymentAttachmentInfo.setUpdateBy(SecurityUtils.getNickname());
			paymentAttachmentInfo.setUpdateTime(new Date());
			paymentAttachmentInfoService.insertPaymentAttachmentInfo(paymentAttachmentInfo);
		}

		return CommonResult.success();
	}

	@Override
	public CommonResult getLastEmail() {
		String email = "";
		if (SHIPPER.equals(SecurityUtils.getClientType())) {
			email = tenantUserService.getLastEmail(SecurityUtils.getUserId());
		}
		if (FINANCE.equals(SecurityUtils.getClientType())) {
			email = financeUserService.getLastEmail(SecurityUtils.getUserId());
		}
		return CommonResult.success(email);
	}

	@Override
	public CommonResult approveInfo(Long id) {
		FinanceStatementApproveInfo financeStatementApproveInfo = financeStatementMapper.approveInfo(id);
		if (financeStatementApproveInfo == null) {
			return CommonResult.error("未查询到申请单");
		}
		Date now = new Date();
		String datePoor = "";
		if ("0000-00-00 00:00:00".equals(financeStatementApproveInfo.getOperationAuditTime())) {
			datePoor = DateUtils.getDatePoorSimple(now, DateUtils.strToDate(financeStatementApproveInfo.getCreateTime()));
			financeStatementApproveInfo.setOperationWaitTime(datePoor);
			financeStatementApproveInfo.setOperationAuditTime(null);
		}
		if ("0000-00-00 00:00:00".equals(financeStatementApproveInfo.getFinanceAuditTime())) {
			if (StringUtils.isEmpty(datePoor)){
				datePoor = DateUtils.getDatePoorSimple(now, DateUtils.strToDate(financeStatementApproveInfo.getOperationAuditTime()));
				financeStatementApproveInfo.setFinanceWaitTime(datePoor);
			}
			financeStatementApproveInfo.setFinanceAuditTime("");
		}
		if ("0000-00-00 00:00:00".equals(financeStatementApproveInfo.getMarkTime())) {
			if (StringUtils.isEmpty(datePoor)) {
				datePoor = DateUtils.getDatePoorSimple(now, DateUtils.strToDate(financeStatementApproveInfo.getFinanceAuditTime()));
				financeStatementApproveInfo.setMarkWaitTime(datePoor);
			}
			financeStatementApproveInfo.setMarkTime("");
		}
		if ("0000-00-00 00:00:00".equals(financeStatementApproveInfo.getPaymentDate())) {
			financeStatementApproveInfo.setPaymentDate("");
		}
		if (financeStatementApproveInfo.getProfitState() == 3){
			financeStatementApproveInfo.setPaymentDate(null);
		}
		return CommonResult.success(financeStatementApproveInfo);
	}


	@Override
	public List<PaymentAttachmentInfo> selectAndDownloadByStatementId(Long statementId) {
		FinanceStatement statement = financeStatementMapper.selectFinanceStatementById(statementId);
		if (statement == null) {
			log.info("获取银行交易凭证失败：没有查询到付款记录！");
			return null;
		}
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(statement.getCustomerId());

		List<PaymentAttachmentInfo> tradeFlowFiles = paymentAttachmentInfoService.selectPaymentAttachmentInfoByRelationId(statementId);

		boolean hasPay = false;
		boolean hasReturnPay = false;

		// 付款
		if (statement.getProfitState() == 1){
			for (PaymentAttachmentInfo tradeFlowFile : tradeFlowFiles) {
				if (FileNames.TRADE_FLOW.equals(tradeFlowFile.getFileName())){
					hasPay = true;
				}
			}
		}

		// 退款
		if (statement.getPayReturnState() == 1){
			for (PaymentAttachmentInfo tradeFlowFile : tradeFlowFiles) {
				if (FileNames.RETURN_TRADE_FLOW.equals(tradeFlowFile.getFileName())){
					hasReturnPay = true;
				}
			}
		}

		DownLoadReq req = new DownLoadReq();
		req.setStatementId(statementId);
		req.setCallerFlowId(statement.getStatementNo());
		req.setIdNo(customerInfo.getCreditCode());
		req.setSourceType(BusinessConstants.SOURCE_PC_10);
		req.setUserType(BusinessConstants.USER_TYPE_CUSTOMER);
		req.setTranType(22);

		if (!hasPay) {
			// 如果该付款单对应支付凭证已经存在了，就不再去再取一遍了
			req.setIsPay(BusinessConstants.TRANSACTION_PAYMENT);
			this.toDoDownload(req, downloadUrl + "download/certificate/single");
		}

		if (!hasReturnPay) {
			// 如果该付款单对应支付凭证已经存在了，就不再去再取一遍了
			req.setIsPay(BusinessConstants.TRANSACTION_RECEIVE);
			this.toDoDownload(req, downloadUrl + "download/certificate/single");
		}

		return paymentAttachmentInfoService.selectPaymentAttachmentInfoByRelationId(statementId);
	}



	/**
	 * 执行下载操作
	 *
	 * @param req
	 * @param urlPath
	 * @return
	 */
	private String toDoDownload(DownLoadReq req, String urlPath) {

		String path = "";
		HttpURLConnection conn = null;
		try {
			URL url = new URL(urlPath);
			conn = (HttpURLConnection) url.openConnection();
			// post方式请求
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Charset", "UTF-8");// 设置字符编码
			conn.setRequestProperty("Accept-Encoding", "identity"); // 加上这句话解决问题
			conn.setRequestProperty("Content-Type", "application/json");
			// 添加头文件
			conn.setRequestProperty("acc", accountAcc);
			conn.setRequestProperty("pas", accountPas);
			// 传递参数
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.connect();
			DataOutputStream dataOutputStream = new DataOutputStream(conn.getOutputStream());
			dataOutputStream.writeBytes(JSONObject.toJSONString(req));
			dataOutputStream.flush();
			dataOutputStream.close();
			int length = conn.getContentLength();
			log.info("下载文件：" + url + ",fileSize=" + length);
			if (length > 0 && conn.getContentType().equals("text/html;charset=UTF-8")) {
				return path;
			}
			String filePath = UUID.randomUUID() + ".pdf";

			log.info("银行交易凭证，上传七牛云：" + req.getCallerFlowId());
			String fileKey = "pds/" + filePath;
			Boolean bool = QiNiuUtil.uploadInputStream(conn.getInputStream(), fileKey);
			log.info("银行交易凭证，上传七牛云返回：" + JSONObject.toJSONString(bool));
			PaymentAttachmentInfo record = new PaymentAttachmentInfo();
			record.setId(TextUtil.getTimeSequenceID(5));
			record.setRelationId(req.getStatementId());
			record.setCreateTime(new Date());
			record.setUpdateTime(record.getCreateTime());
			if (req.getIsPay() == BusinessConstants.TRANSACTION_PAYMENT){
				record.setFileName(FileNames.TRADE_FLOW);
			}
			if (req.getIsPay() == BusinessConstants.TRANSACTION_RECEIVE){
				record.setFileName(FileNames.RETURN_TRADE_FLOW);
			}
			record.setFileSize("300");
			record.setOriginalFileName(req.getCallerFlowId() + ".pdf");
			record.setFileType("pdf");
			record.setFileUrl(customConfig.getQiniuDomain() + fileKey);
			record.setKeyValue(fileKey);
			paymentAttachmentInfoService.insertPaymentAttachmentInfo(record);
			path = record.getFileUrl();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != conn) {
				conn.disconnect();
				conn = null;
			}
		}
		return path;
	}


	@Override
	public AjaxResult feePay(FinanceStatement statement, String password, BigDecimal totalPayFare) {
		// 加入付款
		BigDecimal bigDecimal = financeStatementMapper.selectActualFareByStatementId(statement.getId());
		BigDecimal payFare = statement.getInvoiceFare().subtract(bigDecimal);
		if (payFare.compareTo(totalPayFare) != 0){
			return AjaxResult.error("申请付款金额错误");
		}

		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(statement.getAuditFreightForwarderId());
		FrameworkContract frameworkContract = frameworkContractService.selectFrameworkContractById(statement.getContractId());
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(SecurityUtils.getShipperId());

		AjaxResult ajaxResult = huaxiaservice.feePay(customerInfo, frameworkContract, freightForwarderInfo, statement.getStatementNo(), String.valueOf(payFare), password);
		FinanceStatement update = new FinanceStatement();
		update.setId(statement.getId());
		update.setPaymentDate(DateUtils.getFormatDate(new Date()));
		//利润状态(0未支付(线上)  1已支付(线上)  2已支付(线下) 3未支付(线下) 4付款中 5线上支付失败)
		if (ajaxResult.isSuccess()) {
			update.setProfitState(1);
			financeStatementMapper.updateFinanceStatement(update);
			return AjaxResult.success("付款成功", statement.getId());
		} else {
			if (ajaxResult.getCode() == 500) {
				update.setProfitState(4);
				financeStatementMapper.updateFinanceStatement(update);
				return AjaxResult.error(ajaxResult.getMsg());
			}
			if (ajaxResult.getCode() == 602) {
				return AjaxResult.error("支付运输费失败：" + ajaxResult.getMsg());
			}
			return AjaxResult.success();
		}
	}

	public AjaxResult feePayReturn(FinanceStatement statement) {
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(statement.getCustomerId());
		AjaxResult ajaxResult = huaxiaservice.feePayReturn(customerInfo, statement.getStatementNo());
		if (ajaxResult.isSuccess()) {
			statement.setPayReturnState(1);
			statement.setPayReturnTime(new Date());
			return AjaxResult.success("退款成功");
		} else {
			if (ajaxResult.getCode() == 500) {
				statement.setPayReturnState(2);
				return AjaxResult.error(ajaxResult.getMsg());
			}
			if (ajaxResult.getCode() == 602) {
				statement.setPayReturnState(3);
				return AjaxResult.error("支付运输费失败：" + ajaxResult.getMsg());
			}
			return AjaxResult.success();
		}
	}

	public AjaxResult feePayReturnTest() {
		AjaxResult ajaxResult = huaxiaservice.feePayReturn(new CustomerInfo(),"FS2507042005006200001");
		FinanceStatement update = new FinanceStatement();
		update.setId(175162557619100001L);
		if (ajaxResult.isSuccess()) {
			update.setPayReturnState(1);
			financeStatementMapper.updateFinanceStatement(update);
			return AjaxResult.success("退款成功");
		} else {
			if (ajaxResult.getCode() == 500) {
				update.setPayReturnState(2);
				financeStatementMapper.updateFinanceStatement(update);
				return AjaxResult.error(ajaxResult.getMsg());
			}
			if (ajaxResult.getCode() == 602) {
				update.setPayReturnState(3);
				financeStatementMapper.updateFinanceStatement(update);
				return AjaxResult.error("支付运输费失败：" + ajaxResult.getMsg());
			}
			return AjaxResult.success();
		}
	}


	/*{"code": "200","data": {"statementNo": "FS2507031946172970003","state": 2,  1成功  2失败"resultMsg": "网络异常，请重新支付"},"msg": "查询成功"}*/

	public void financeStatementPayQuery() {

		// 每5分钟查询10分钟前记录
		// 查询付款中
		List<FinanceStatement> payList = financeStatementMapper.financeStatementPayQuery();
		for (FinanceStatement statement : payList) {
			if (StringUtils.isNotEmpty(statement.getStatementNo())) {
				CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(statement.getCustomerId());
				AjaxResult ajaxResult = huaxiaservice.queryFeePayResult(customerInfo, statement.getStatementNo(), false);

				if (ajaxResult.getCode() == HttpStatus.SUCCESS) {
					FinanceStatementPayQueryRes res = (FinanceStatementPayQueryRes) ajaxResult.getData();
					if (res != null) {
						if (res.getState() == 1) {
							statement.setProfitState(1);
						} else if (res.getState() == 2) {
							statement.setProfitState(5);
						}
						financeStatementMapper.updateFinanceStatement(statement);
					}
				}
			}

		}

		// 查询退款中
		List<FinanceStatement> payReturnList = financeStatementMapper.financeStatementPayReturnQuery();
		for (FinanceStatement statement : payReturnList) {
			if (StringUtils.isNotEmpty(statement.getStatementNo())) {
				CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(statement.getCustomerId());
				AjaxResult ajaxResult = huaxiaservice.queryFeePayResult(customerInfo, statement.getStatementNo(), true);

				if (ajaxResult.getCode() == HttpStatus.SUCCESS) {
					FinanceStatementPayQueryRes res = (FinanceStatementPayQueryRes) ajaxResult.getData();
					if (res != null) {
						if (res.getState() == 1) {
							statement.setPayReturnState(1);
						} else if (res.getState() == 2) {
							statement.setPayReturnState(3);
						}
						financeStatementMapper.updateFinanceStatement(statement);
					}
				}
			}
		}
	}
}
