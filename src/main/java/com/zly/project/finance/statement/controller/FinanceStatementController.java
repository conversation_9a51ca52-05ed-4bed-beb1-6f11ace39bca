package com.zly.project.finance.statement.controller;

import com.zly.common.constant.BusinessConstants;
import com.zly.common.constant.HttpStatus;
import com.zly.common.enums.ClientType;
import com.zly.common.utils.PageUtils;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.StringUtils;
import com.zly.common.utils.bean.BeanUtils;
import com.zly.common.utils.file.FileUtils;
import com.zly.common.utils.poi.ExcelUtil;
import com.zly.framework.aspectj.lang.annotation.Log;
import com.zly.framework.aspectj.lang.enums.BusinessType;
import com.zly.framework.redis.RedisCache;
import com.zly.framework.security.service.SysLoginService;
import com.zly.framework.task.SysTask;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.TableInfo;
import com.zly.project.consignor.customer.domain.CustomerInfo;
import com.zly.project.consignor.customer.service.ICustomerInfoService;
import com.zly.project.finance.statement.domain.FinanceStatement;
import com.zly.project.finance.statement.domain.FinanceStatementEx;
import com.zly.project.finance.statement.domain.PaymentAttachmentInfo;
import com.zly.project.finance.statement.domain.req.FinanceStatementRejectReq;
import com.zly.project.finance.statement.domain.req.FinanceStatementReq;
import com.zly.project.finance.statement.domain.req.FinanceStatementSearchReq;
import com.zly.project.finance.statement.domain.req.ProfitReq;
import com.zly.project.finance.statement.domain.res.FinanceStatementRes;
import com.zly.project.finance.statement.domain.res.StatementInfoRes;
import com.zly.project.finance.statement.service.IFinanceStatementService;
import com.zly.project.financeOperation.domain.FinanceUser;
import com.zly.project.system.domain.SysUser;
import com.zly.project.tenant.domain.TenantUser;
import com.zly.project.transport.waybill.domain.req.WaybillReq;
import com.zly.project.transport.waybill.domain.res.StatementMoneyRes;
import com.zly.project.transport.waybill.domain.res.WaybillRes;
import com.zly.project.transport.waybill.mapper.WaybillMapperEx;
import com.zly.project.transport.waybill.service.IWaybillService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zly.common.enums.ClientType.FINANCE;
import static com.zly.common.enums.ClientType.PLATFORM;
import static com.zly.common.enums.ClientType.SHIPPER;

/**
 * 对账Controller--平台PC端--发票审批--发票登记
 *
 * <AUTHOR>
 * @date 2021-11-22
 */
@Api(value = "平台发票管理--v4.3.17", tags = "平台发票管理--v4.3.17")
@RestController
@RequestMapping("/finance/statement")
public class FinanceStatementController extends BaseController {
	@Resource
	private IFinanceStatementService financeStatementService;
	@Resource
	private IWaybillService waybillService;
	@Resource
	private ICustomerInfoService customerInfoService;
	@Resource
	private RedisCache redisCache;
	@Resource
	private WaybillMapperEx waybillMapperEx;

	@PreAuthorize("@ss.hasPermi('finance:waybill:list')")
	@PostMapping("/waybillList")
	@ApiOperation("待申请开票运单列表--托运人、物流金融端--v4.5.1203")
	public TableInfo<WaybillRes> shipperWaybillList(@RequestBody WaybillReq waybillReq) {
		if (!SHIPPER.equals(SecurityUtils.getClientType()) && !FINANCE.equals(SecurityUtils.getClientType())) {
			return TableInfo.success(Collections.emptyList());
		}

		// 校验托运人项目分组权限
		this.addContractIds(waybillReq);
		// 托运人端开票申请只显示自己录单的 金融端看别人录入的运单
		if (SHIPPER.equals(SecurityUtils.getClientType())) {
			waybillReq.setIsRecorder(0);
		} else if (FINANCE.equals(SecurityUtils.getClientType())) {
			waybillReq.setBusinessModelId(SecurityUtils.getBusinessModelId());
			waybillReq.setIsRecorder(1);
		}

		List<WaybillRes> list = waybillService.waybillListForStatement(waybillReq);
		if (FINANCE.equals(SecurityUtils.getClientType())) {
			CommonResult<Integer> count = waybillService.waybillListForStatementCount(waybillReq);
			return new TableInfo<>(list, count.getData().longValue());
		}
		return TableInfo.success(list);
	}

	@PreAuthorize("@ss.hasPermi('finance:waybill:list')")
	@PostMapping("/waybillListCount")
	@ApiOperation("待申请开票运单列表条数统计--托运人、物流金融端")
	public CommonResult<?> shipperWaybillListCount(@RequestBody WaybillReq waybillReq) {
		if (!SHIPPER.equals(SecurityUtils.getClientType()) && !FINANCE.equals(SecurityUtils.getClientType())) {
			return CommonResult.success(0);
		}

		// 校验托运人项目分组权限
		this.addContractIds(waybillReq);
		// 托运人端开票申请只显示自己录单的 金融端看别人录入的运单
		if (SHIPPER.equals(SecurityUtils.getClientType())) {
			waybillReq.setIsRecorder(0);
		} else if (FINANCE.equals(SecurityUtils.getClientType())) {
			waybillReq.setBusinessModelId(SecurityUtils.getBusinessModelId());
			waybillReq.setIsRecorder(1);
		}
		waybillReq.setPageNum(null);
		waybillReq.setPageSize(null);
		return waybillService.waybillListForStatementCount(waybillReq);
	}

	@PreAuthorize("@ss.hasPermi('finance:waybill:list')")
	@GetMapping("/countStatementForAudit")
	@ApiOperation("开票管理的待审核的开票申请单数（我是审核的人）")
	public CommonResult<Number> countStatementForAudit() {
		// usage: 2.开票审批
		TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		FinanceStatementSearchReq searchReq = new FinanceStatementSearchReq();
		searchReq.setResource(SHIPPER.val());
		searchReq.setAuditCustomerId(tenantUser.getTenantId());
		searchReq.setState(2);
		List<FinanceStatementRes> list = financeStatementService.selectStatementList(searchReq);
		return CommonResult.success(list == null ? 0L : list.size());
	}

	/**
	 * 开票申请单列表（托运人提交待审批、托运人提交已审批、上游提交托运人待审批、上游提交托运人已审批、网络货运人待审批、网络货运人已审批、托运人发票登记列表、网络货运人发票登记列表）
	 *
	 * @param req
	 * @return
	 */
	@PreAuthorize("@ss.hasPermi('finance:waybill:list')")
	@PostMapping("/list")
	@ApiOperation(value = "开票申请单列表--v4.6.2", notes = "开票申请单列表（usage: 1.托运人提交待审批、2.托运人提交已审批、3.上游提交托运人待审批、4.上游提交托运人已审批、5.网络货运人待审批、6.网络货运人已审批、7.托运人发票登记列表、8.网络货运人发票登记列表）")
	public TableInfo<FinanceStatementRes> list(@RequestBody FinanceStatementReq req) {
		if (null == req || null == req.getUsage() || !Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10).contains(req.getUsage())) {
			return TableInfo.requestError("参数错误");
		}
		BeanUtils.beanAttributeValueTrim(req); // 去掉该对象中string字段前后空格
		addContractIds(req);
		FinanceStatementSearchReq searchReq = new FinanceStatementSearchReq();
		BeanUtils.copyProperties(req, searchReq);
		searchReq.setContractIds(req.getContractIds());
		searchReq.setUsage(req.getUsage());
		searchReq.setResource(SecurityUtils.getClientType().val());
		searchReq.setProfitState(req.getProfitState());
		searchReq.setOperationAuditBy(req.getOperationAuditBy());
		searchReq.setFinanceAuditBy(req.getFinanceAuditBy());
		searchReq.setBusinessModelId(searchReq.getResource().equals(FINANCE.val()) ? SecurityUtils.getBusinessModelId() : null);

		// usage 接口用途（1.申请人的待审批列表 2.申请人的已审批列表
		// 3.审批人的待审批列表 4.审批人的已审批列表 5.网络货运人的待审批列表 6.网络货运人的已审批列表）
		// state 状态(-1:已撤销 0:被驳回 1:已完成(待申请开票) 2:审批中 3:审批通过(未核销) 4:部分核销 5:已核销 6:发票登记驳回 7 运营审核中 8运营审核驳回 9运营审核通过 10财务审核驳回 11财务审核通过)
		switch (SecurityUtils.getClientType()) {
		case SHIPPER:
		case FINANCE:
			if (req.getUsage() <= 2) {
				searchReq.setCustomerId(SecurityUtils.getShipperId());
			} else {
				searchReq.setAuditCustomerId(SecurityUtils.getShipperId());
			}
			if (req.getUsage() == 1) {
				searchReq.setStateList(Arrays.asList(2, 7, 9));
			} else if (req.getUsage() == 2) {
				if (searchReq.getState() == null){
					searchReq.setStateList(Arrays.asList(-1, 0, 3, 6, 8, 10, 11));
				}else if (searchReq.getState() == 3){
					searchReq.setState(null);
					searchReq.setStateList(Arrays.asList(3, 11));
				}else if (searchReq.getState() == 0){
					searchReq.setState(null);
					searchReq.setStateList(Arrays.asList(0, 6, 8, 10));
				}
			} else if (3 == req.getUsage()) { // 自己审批的已审批列表
				searchReq.setState(2);
			} else if (4 == req.getUsage()) { // 自己审批的已审批列表
				searchReq.setStateList(Arrays.asList(0, 3));
			} else if (7 == req.getUsage()) {
				searchReq.setState(3);
			} else {
				return TableInfo.requestError("参数错误");
			}
			break;
		case PLATFORM:
			searchReq.setAuditFreightForwarderId(SecurityUtils.getFreightForwarderId());
			searchReq.setState(null);
			if (5 == req.getUsage()) { // 网络货运人的 待我审批 的 待审批列表
				searchReq.setStateList(Arrays.asList(2, 7));
			} else if (6 == req.getUsage()) { // 网络货运人的 我已审批 的 已审批列表
				searchReq.setStateList(Arrays.asList(3, 8, 9, 10, 11));
			} else if (8 == req.getUsage()) {
				searchReq.setState(9);
			} else if (9 == req.getUsage()) {
				searchReq.setStateList(Arrays.asList(6, 10));
			} else if (10 == req.getUsage()) {
				searchReq.setStateList(Arrays.asList(3, 11));
			}
			break;
		case GROUP:
			break;
		default:
			return TableInfo.forbidden("用户信息异常");
		}

		if (StringUtils.isNotBlank(searchReq.getCreateTimeStart())) {
			searchReq.setCreateTimeStart(searchReq.getCreateTimeStart() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(searchReq.getCreateTimeEnd())) {
			searchReq.setCreateTimeEnd(searchReq.getCreateTimeEnd() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(searchReq.getOperationAuditTimeStart())) {
			searchReq.setOperationAuditTimeStart(searchReq.getOperationAuditTimeStart() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(searchReq.getOperationAuditTimeEnd())) {
			searchReq.setOperationAuditTimeEnd(searchReq.getOperationAuditTimeEnd() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(searchReq.getFinanceAuditTimeStart())) {
			searchReq.setFinanceAuditTimeStart(searchReq.getFinanceAuditTimeStart() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(searchReq.getFinanceAuditTimeEnd())) {
			searchReq.setFinanceAuditTimeEnd(searchReq.getFinanceAuditTimeEnd() + " 23:59:59");
		}
		searchReq.setAuditEndTime(null);
		searchReq.setAuditStartTime(null);
		List<FinanceStatementRes> list = financeStatementService.selectStatementList(searchReq);
		return TableInfo.success(list);
	}

	/**
	 * 开票申请单列表合计（托运人提交待审批、托运人提交已审批、上游提交托运人待审批、上游提交托运人已审批、网络货运人待审批、网络货运人已审批、托运人发票登记列表、网络货运人发票登记列表）
	 *
	 * @param req
	 * @return
	 */
	@PreAuthorize("@ss.hasPermi('finance:waybill:list')")
	@PostMapping("/listCount")
	@ApiOperation(value = "开票申请单列表合计--v4.6.2", notes = "开票申请单列表（usage: 1.托运人提交待审批、2.托运人提交已审批、3.上游提交托运人待审批、4.上游提交托运人已审批、5.网络货运人待审批、6.网络货运人已审批、7.托运人发票登记列表、8.网络货运人发票登记列表）")
	public CommonResult<?> listCount(@RequestBody FinanceStatementReq req) {
		if (null == req || null == req.getUsage() || !Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8).contains(req.getUsage())) {
			return CommonResult.error("参数错误");
		}

		BeanUtils.beanAttributeValueTrim(req); // 去掉该对象中string字段前后空格
		req.setPageNum(null);
		req.setPageSize(null);
		addContractIds(req);
		Map<String, Object> map = new HashMap<>();

		// usage 接口用途（1.申请人的待审批列表 2.申请人的已审批列表 3.审批人的待审批列表 4.审批人的已审批列表 5.网络货运人的待审批列表 6.网络货运人的已审批列表）
		// state 状态(-1:已撤销 0:被驳回 1:已完成(待申请开票) 2:审批中 3:审批通过(未核销) 4:部分核销 5:已核销 6:发票登记驳回)
		// 1 2 3 4 7显示自己录入的运单申请
		switch (SecurityUtils.getClientType()) {
		case SHIPPER:
			TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
			map.put("resource", SHIPPER.val());
			if (req.getUsage() == 1) {
				map.put("customerId", tenantUser.getTenantId());
				map.put("stateList", Arrays.asList(2, 7, 9));
			} else if (req.getUsage() == 2) {
				map.put("customerId", tenantUser.getTenantId());
				// 已审批加上撤销数据
				map.put("stateList", Arrays.asList(-1, 0, 6, 8, 10, 11));
			} else if (3 == req.getUsage()) {
				map.put("auditCustomerId", tenantUser.getTenantId());
				map.put("state", 2);
			} else if (4 == req.getUsage()) {
				map.put("auditCustomerId", tenantUser.getTenantId());
				// 只要显示审批通过的，不显示驳回的
				// 23.9.20显示通过和驳回的数据
				map.put("stateList", Arrays.asList(0, 3));
				// map.put("state", 3);
			} else if (7 == req.getUsage()) {
				map.put("auditCustomerId", tenantUser.getTenantId());
				map.put("state", 3);
			} else {
				return CommonResult.error("参数错误");
			}
			map.put("contractIds", req.getContractIds());
			break;
		case PLATFORM:
			SysUser sysUser = SecurityUtils.getLoginUser().getUser();
			map.put("resource", PLATFORM.val());
			if (5 == req.getUsage()) {
				map.put("auditFreightForwarderId", sysUser.getFreightForwarderId());
				map.put("state", 2);
			} else if (6 == req.getUsage()) {
				map.put("auditFreightForwarderId", sysUser.getFreightForwarderId());
				// 只要显示审批通过的，不显示驳回的
				// 23.9.20显示通过和驳回的数据
				map.put("stateList", Arrays.asList(0, 3));
				// map.put("state", 3);
			} else if (8 == req.getUsage()) {
				map.put("auditFreightForwarderId", sysUser.getFreightForwarderId());
				map.put("state", 3);
			} else {
				return CommonResult.error("参数错误");
			}

			if (null != req.getCustomerId()) {
				map.put("customerId", req.getCustomerId());
			}
			break;
		case FINANCE:
			FinanceUser financeUser = SecurityUtils.getLoginUser().getFinanceUser();
			map.put("resource", ClientType.FINANCE.val());
			map.put("businessModelId", SecurityUtils.getBusinessModelId());
			if (req.getUsage() == 1) {
				map.put("customerId", SecurityUtils.getShipperId());
				map.put("stateList", Arrays.asList(2, 3, 7, 9));
			} else if (req.getUsage() == 2) {
				map.put("customerId", SecurityUtils.getShipperId());
				map.put("stateList", Arrays.asList(-1, 0, 6, 8, 10, 11));
			} else if (3 == req.getUsage()) {
				map.put("auditCustomerId", financeUser.getCustomerId());
				map.put("state", 2);
			} else if (4 == req.getUsage()) {
				map.put("auditCustomerId", financeUser.getCustomerId());
				// 只要显示审批通过的，不显示驳回的
				// 23.9.20显示通过和驳回的数据
				map.put("stateList", Arrays.asList(0, 3));
				// map.put("state", 3);
			} else if (7 == req.getUsage()) {
				map.put("auditCustomerId", financeUser.getCustomerId());
				map.put("state", 3);
			} else {
				return CommonResult.error("参数错误");
			}
			map.put("contractIds", req.getContractIds());
			break;
		case GROUP:
			break;
		default:
			return CommonResult.error("用户信息异常");
		}

		if (null != req.getFrameworkContractId()) {
			map.put("frameworkContractId", req.getFrameworkContractId());
		}
		//		if (StringUtils.isNotEmpty(req.getFrameworkContractIds())){
		//			map.put("frameworkContractIds",req.getFrameworkContractIds());
		//		}
		if (StringUtils.isNotBlank(req.getCreateTimeStart())) {
			map.put("createTimeStart", req.getCreateTimeStart() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(req.getCreateTimeEnd())) {
			map.put("createTimeEnd", req.getCreateTimeEnd() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(req.getStatementNo())) {
			map.put("statementNo", req.getStatementNo());
		}
		if (null != req.getMarkStatus()) {
			map.put("markStatus", req.getMarkStatus());
		}
		if (null != req.getBillHead()) {
			map.put("billHead", req.getBillHead());
		}
		if (StringUtils.isNotBlank(req.getAuditStartTime())) {
			map.put("auditStartTime", req.getAuditStartTime());
		}
		if (StringUtils.isNotBlank(req.getAuditEndTime())) {
			map.put("auditEndTime", req.getAuditEndTime());
		}
		if (StringUtils.isNotBlank(req.getShippingNoteNumber())) {
			map.put("shippingNoteNumber", req.getShippingNoteNumber());
		}
		if (StringUtils.isNotBlank(req.getInvoiceNumber())) {
			map.put("invoiceNumber", req.getInvoiceNumber());
		}
		if (StringUtils.isNotBlank(req.getInvoicePeriodStart())) {
			map.put("invoicePeriodStart", req.getInvoicePeriodStart());
		}
		if (StringUtils.isNotBlank(req.getInvoicePeriodEnd())) {
			map.put("invoicePeriodEnd", req.getInvoicePeriodEnd());
		}
		map.put("auditEndTime",null);
		map.put("auditStartTime",null);
		return financeStatementService.selectStatementListCount(map);
	}

	@PostMapping("/moneyTotal")
	@ApiOperation(value = "开票申请单列表金额汇总--v4.6.2", notes = "开票申请单列表金额汇总")
	public CommonResult<FinanceStatementRes> moneyTotal(@RequestBody FinanceStatementReq req) {
		if (null == req) {
			return CommonResult.requestError("参数错误");
		}
		BeanUtils.beanAttributeValueTrim(req); // 去掉该对象中string字段前后空格
		addContractIds(req);
		Map<String, Object> map = new HashMap<>();

		switch (SecurityUtils.getClientType()) {
		case SHIPPER:
			TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
			map.put("resource", SHIPPER.val());
			map.put("auditCustomerId", tenantUser.getTenantId());
			map.put("state", 3);
			map.put("contractIds", req.getContractIds());
			break;
		case PLATFORM:
			SysUser sysUser = SecurityUtils.getLoginUser().getUser();
			map.put("resource", PLATFORM.val());
			map.put("auditFreightForwarderId", sysUser.getFreightForwarderId());
			map.put("stateList", Arrays.asList(3 , 11));
			if (null != req.getCustomerId()) {
				map.put("customerId", req.getCustomerId());
			}
			break;
		case FINANCE:
			FinanceUser financeUser = SecurityUtils.getLoginUser().getFinanceUser();
			map.put("resource", ClientType.FINANCE.val());
			map.put("businessModelId", SecurityUtils.getBusinessModelId());
			map.put("auditCustomerId", financeUser.getCustomerId());
			map.put("state", 3);
			map.put("contractIds", req.getContractIds());
			break;
		default:
			return CommonResult.error("用户信息异常");
		}

		if (null != req.getFrameworkContractId()) {
			map.put("frameworkContractId", req.getFrameworkContractId());
		}
		if (StringUtils.isNotBlank(req.getCreateTimeStart())) {
			map.put("createTimeStart", req.getCreateTimeStart() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(req.getCreateTimeEnd())) {
			map.put("createTimeEnd", req.getCreateTimeEnd() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(req.getStatementNo())) {
			map.put("statementNo", req.getStatementNo());
		}
		if (null != req.getMarkStatus()) {
			map.put("markStatus", req.getMarkStatus());
		}
		if (null != req.getBillHead()) {
			map.put("billHead", req.getBillHead());
		}
		if (StringUtils.isNotBlank(req.getShippingNoteNumber())) {
			map.put("shippingNoteNumber", req.getShippingNoteNumber());
		}
		if (StringUtils.isNotBlank(req.getInvoicePeriodStart())) {
			map.put("invoicePeriodStart", req.getInvoicePeriodStart());
		}
		if (StringUtils.isNotBlank(req.getInvoicePeriodEnd())) {
			map.put("invoicePeriodEnd", req.getInvoicePeriodEnd());
		}
		if (StringUtils.isNotBlank(req.getInvoiceNumber())) {
			map.put("invoiceNumber", req.getInvoiceNumber());
		}
		if (StringUtils.isNotBlank(req.getOperationAuditTimeStart())) {
			map.put("operationAuditTimeStart", req.getOperationAuditTimeStart() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(req.getOperationAuditTimeEnd())) {
			map.put("operationAuditTimeEnd", req.getOperationAuditTimeEnd() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(req.getFinanceAuditTimeStart())) {
			map.put("financeAuditTimeStart", req.getFinanceAuditTimeStart() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(req.getFinanceAuditTimeEnd())) {
			map.put("financeAuditTimeEnd", req.getFinanceAuditTimeEnd() + " 23:59:59");
		}

		FinanceStatementRes financeStatementRes = financeStatementService.selectStatemenMoneyTotal(map);
		return CommonResult.success(financeStatementRes);

	}

	@ApiOperation(value = "导出对账列表", notes = "导出对账列表")
	@PreAuthorize("@ss.hasPermi('finance:statement:export')")
	@Log(title = "对账", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, FinanceStatement financeStatement) {
		List<FinanceStatement> list = financeStatementService.selectFinanceStatementList(financeStatement);
		ExcelUtil<FinanceStatement> util = new ExcelUtil<FinanceStatement>(FinanceStatement.class);
		util.exportExcel(response, list, "对账数据");
	}

	@ApiOperation(value = "获取对账详细信息-4.6.2", notes = "获取对账详细信息-4.6.2")
	@PreAuthorize("@ss.hasPermi('finance:waybill:list')")
	@GetMapping(value = "/{id}")
	public CommonResult<FinanceStatementEx> getInfo(@PathVariable("id") Long id) {
		return CommonResult.success(financeStatementService.selectFinanceStatementInfoById(id));
	}

	@ApiOperation(value = "新增对账", notes = "新增对账")
	@Log(title = "对账", businessType = BusinessType.INSERT)
	@PostMapping
	public CommonResult<Integer> add(@RequestBody FinanceStatement financeStatement) {
		return CommonResult.success(financeStatementService.insertFinanceStatement(financeStatement));
	}

	@ApiOperation(value = "修改对账", notes = "修改对账")
	@Log(title = "对账", businessType = BusinessType.UPDATE)
	@PutMapping
	public CommonResult<Integer> edit(@RequestBody FinanceStatement financeStatement) {
		return CommonResult.success(financeStatementService.updateFinanceStatement(financeStatement));
	}

	@ApiOperation(value = "删除对账", notes = "删除对账")
	@Log(title = "对账", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public CommonResult<Integer> remove(@PathVariable Long[] ids) {
		return CommonResult.success(financeStatementService.deleteFinanceStatementByIds(ids));
	}

	@Resource
	private SysLoginService sysLoginService;

	@ApiOperation(value = "票务管理--按运单申请4.6.2", notes = "按运单申请")
	@Log(title = "票务管理---按运单申请", businessType = BusinessType.MANUAL_RECONCILIATION)
	@PostMapping("/waybillManual")
	public AjaxResult statementApplyWaybill(@RequestBody FinanceStatementReq req) {
		Long customerId = SecurityUtils.getShipperId();
		if (customerId == null || customerId == 0L) {
			return AjaxResult.error("用户登录信息异常");
		}
		if (null == req || null == req.getWaybillIds()) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "参数错误");
		}
		CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(customerId);
		if (null == customerInfo) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "托运人信息为空");
		}
		List<Long> waybillIds = new ArrayList<>();
		for (String s : req.getWaybillIds().split(",")) {
			waybillIds.add(Long.parseLong(s.trim()));
		}
		if (waybillIds.isEmpty() || waybillIds.size() > 2000) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "申请开票的运单数量不可超过2000条");
		}

		// 加一个redis校验，防重复请求
		String key = "waybillManual_" + req.getWaybillIds();
		if (redisCache.hasKey(key)) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "请勿重复申请开票");
		} else {
			redisCache.setCacheObject(key, 1, 5, TimeUnit.MINUTES);
			AjaxResult res = financeStatementService.statementApply(waybillIds, customerInfo, req);
			redisCache.deleteObject(key);
			return res;
		}
	}

	@ApiOperation(value = "票务管理--申请附件", notes = "票务管理--申请附件")
	@PostMapping("/statementReApplyFare")
	public AjaxResult statementReApplyFare(@RequestBody FinanceStatementReq req) {

		String key = "waybillManual_payment_" + SecurityUtils.getShipperId();
		String lockKey = "waybillManual_payment_lock_" + SecurityUtils.getShipperId();
		if (redisCache.hasKey(lockKey)) {
			return AjaxResult.error("图形验证码错误次数太多，请10分钟以后再试");
		}
		AjaxResult codeResult = sysLoginService.validateCaptcha(req.getPicCode(), req.getUuid(), 8);
		if (!codeResult.isSuccess()) {
			if (redisCache.hasKey(key)) {
				Integer count = redisCache.getCacheObject(key);
				redisCache.setCacheObject(key, count + 1);
				if (count >= 4) {
					redisCache.setCacheObject(lockKey, 1, 10, TimeUnit.MINUTES);
					return codeResult;
				}
			} else {
				redisCache.setCacheObject(key, 1);
			}
			return codeResult;
		}
		redisCache.deleteObject(key);

		// 付款
		FinanceStatement statement = financeStatementService.selectFinanceStatementById(req.getId());
		if (statement.getProfitState() == 0 || statement.getPaymentType() == 5) {
			return financeStatementService.feePay(statement, req.getPassword(), req.getTotalPayFare());
		}

		return AjaxResult.error("只有未付款和付款失败的申请单可以重新付款");
	}

	@ApiOperation(value = "退款", notes = "退款")
	@PostMapping("/feePayReturn")
	public AjaxResult feePayReturn(@RequestBody FinanceStatementReq req) {
		// 付款
		FinanceStatement statement = financeStatementService.selectFinanceStatementById(req.getId());

		if (statement == null) {
			return AjaxResult.error("未查询到申请单");
		}
		if (statement.getPaymentType() != 1) {
			return AjaxResult.error("非线上付款的的申请单无需退回钱款");
		}
		if (statement.getProfitState() != 1) {
			return AjaxResult.error("未付款成功的申请单无法退款");
		}
		return financeStatementService.feePayReturn(statement);
	}

	@ApiOperation(value = "票务管理--按金额申请-4.6.2", notes = "按金额申请-4.6.2")
	@Log(title = "票务管理---按金额申请", businessType = BusinessType.MANUAL_RECONCILIATION)
	@PostMapping("/manual")
	public AjaxResult statementApply(@RequestBody FinanceStatementReq req) {
		Long customerId = SecurityUtils.getShipperId();
		if (customerId == null || customerId == 0L) {
			return AjaxResult.error("用户登录信息异常");
		}
		if (null == req || null == req.getWaybillIds()) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "参数错误");
		}
		CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(customerId);
		if (null == customerInfo) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "托运人信息为空");
		}
		List<Long> waybillIds = new ArrayList<>();
		for (String s : req.getWaybillIds().split(",")) {
			waybillIds.add(Long.parseLong(s.trim()));
		}
		if (waybillIds.isEmpty() || waybillIds.size() > 2000) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "申请开票的运单数量不可超过2000条");
		}

		// 加一个redis校验
		if (redisCache.hasKey(req.getWaybillIds())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "请勿重复申请开票");
		} else {
			// 查询出来的数据缓存在redis中，10s
			redisCache.setCacheObject(req.getWaybillIds(), 1, 10, TimeUnit.SECONDS);
			return financeStatementService.applyForInvoicing(waybillIds, customerInfo, req, false);
		}
	}

	@ApiOperation(value = "物流金融端代客户申请", notes = "物流金融端代客户申请")
	@Log(title = "物流金融端代客户申请", businessType = BusinessType.MANUAL_RECONCILIATION)
	@PostMapping("/manualBehalf")
	public AjaxResult statementApplyBehalf(@RequestBody FinanceStatementReq req) {
		Long customerId = SecurityUtils.getShipperId();
		if (customerId == null || customerId == 0L) {
			return AjaxResult.error("用户登录信息异常");
		}
		if (null == req || null == req.getWaybillIds()) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "参数错误");
		}
		CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(customerId);
		if (null == customerInfo) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "托运人信息为空");
		}
		List<Long> waybillIds = new ArrayList<>();
		for (String s : req.getWaybillIds().split(",")) {
			waybillIds.add(Long.parseLong(s.trim()));
		}
		if (waybillIds.isEmpty() || waybillIds.size() > 2000) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "申请开票的运单数量不可超过2000条");
		}

		// 加一个redis校验
		if (redisCache.hasKey(req.getWaybillIds())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "请勿重复申请开票");
		} else {

		}

		// 查询出来的数据缓存在redis中，10s
		redisCache.setCacheObject(req.getWaybillIds(), 1, 10, TimeUnit.SECONDS);

		return financeStatementService.applyForInvoicingBehalf(waybillIds, customerInfo, req);

	}

	@ApiOperation("开票管理--按金额申请")
	@Log(title = "开票管理---按金额申请", businessType = BusinessType.AUTOMATIC_RECONCILIATION)
	@PostMapping("/matchWaybills")
	public AjaxResult matchWaybills(@RequestBody FinanceStatementReq req) {
		return financeStatementService.matchWaybills(req);
	}

	@ApiOperation("撤回对账")
	@Log(title = "对账", businessType = BusinessType.REVOKE_RECONCILIATION)
	@PostMapping("/revokeReconciliation")
	public AjaxResult revokeReconciliation(@RequestParam Long id) {
		return financeStatementService.revokeReconciliation(id);
	}

	@ApiOperation("托运人端导入运单申请开票")
	@PreAuthorize("@ss.hasPermi('financial:finance:waybill:add')")
	@PostMapping("uploadFileForInvoiceBatchApply")
	public AjaxResult uploadFileForInvoiceBatchApply(@RequestParam(value = "upfile") MultipartFile file, String nextCustomerId, Long[] frameworkContractIds) {
		Long customerId = SecurityUtils.getShipperId();
		if (customerId == null || customerId == 0L) {
			return AjaxResult.errorNext("用户登录信息异常");
		}
		if (file == null || file.getSize() == 0) {
			return AjaxResult.errorNext("请选择要导入的文件");
		}
		List<String> waybillCodes = FileUtils.readLinesFromFile(file);
		if (waybillCodes == null || waybillCodes.size() == 0) {
			return AjaxResult.errorNext("导入文件中存在不符合要求的运单");
		}

		return financeStatementService.uploadFileForInvoiceBatchApply(customerId, waybillCodes, nextCustomerId.split(",")[0], frameworkContractIds);
	}

	@ApiOperation("物流金融端代客户开票--4.3.22--yusiyuan")
	@PreAuthorize("@ss.hasPermi('financial:finance:waybill:add')")
	@PostMapping("uploadFileForInvoiceBatchApplyBehalf")
	public AjaxResult uploadFileForInvoiceBatchApplyBehalf(@RequestParam(value = "upfile") MultipartFile file, String aboveCustomerId, Long[] frameworkContractIds) {
		Long customerId = SecurityUtils.getShipperId();
		if (customerId == null || customerId == 0L) {
			return AjaxResult.errorNext("用户登录信息异常");
		}
		if (file == null || file.getSize() == 0) {
			return AjaxResult.errorNext("请选择要导入的文件");
		}
		List<String> waybillCodes = FileUtils.readLinesFromFile(file);
		if (waybillCodes == null || waybillCodes.size() == 0) {
			return AjaxResult.errorNext("导入文件中存在不符合要求的运单");
		}

		return financeStatementService.uploadFileForInvoiceBatchApplybehalf(customerId, waybillCodes, aboveCustomerId, frameworkContractIds);
	}

	@ApiOperation("交易中心端-开票撤消、开票驳回-4.6.2")
	@Log(title = "开票撤消、审批", businessType = BusinessType.INVOICEAPPROVAL)
	@PostMapping("/transactionCenter/invoiceApproval")
	public AjaxResult invoiceApproval(@RequestBody FinanceStatementReq req) {
		return financeStatementService.invoiceApproval(req);
	}

	@ApiOperation("物流金融端--发票登记--开票驳回--v4.3.22--zxy")
	@Log(title = "开票撤消、审批", businessType = BusinessType.INVOICEAPPROVAL)
	@PostMapping("/finance/invoiceApproval")
	public CommonResult financeInvoiceApproval(@RequestBody @Validated FinanceStatementRejectReq req) {
		return financeStatementService.financeInvoiceApproval(req);
	}

	@ApiOperation("托运人、物流金融端——开票审批——state为-1撤销, 0驳回, 3审批通过")
	@Log(title = "托运人、物流金融端开票审批", businessType = BusinessType.INVOICEAPPROVAL)
	@PostMapping("/shipper/audit")
	public AjaxResult shipperAudit(@RequestParam String ids, @RequestParam Integer state) {
		logger.info("开票审批，请求参数：ids：{}，state：{}", ids, state);
		List<Long> statementIds = Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList());
		AjaxResult result = financeStatementService.shipperAudit(statementIds, state);
		logger.info("开票审批，审批结果：{}", result);
		return result;
	}

	@ApiOperation("交易中心端-发票登记-利润标记")
	@Log(title = "利润标记", businessType = BusinessType.INVOICEAPPROVAL)
	@PostMapping("/transactionCenter/profitSign")
	public AjaxResult profitSign(@RequestBody ProfitReq req) {

		String[] split = req.getIds().split(",");
		List<Long> longList = new ArrayList<>();
		for (String s : split) {
			longList.add(Long.parseLong(s));
		}
		return financeStatementService.profitSign(longList, req.getRemark());
	}

	/**
	 * 发票明细预览
	 *
	 * @param req
	 * 		type  1单条  2地址汇总  3全部
	 * 		账单对象
	 * @return
	 */
	@ApiOperation(value = "发票明细预览--v4.3.17", notes = "发票明细预览--v4.3.17")
	@PostMapping("/preview/bill/detail")
	public AjaxResult previewBillDetail(@RequestBody FinanceStatementReq req) {
		if (req.getCustomerType() == null) {
			return AjaxResult.error("请选择预览方类型");
		}
		return financeStatementService.previewBillDetail(req);
	}

	/**
	 * 发票业务预览
	 *
	 * @param req
	 * 		type  1单条  2地址汇总  3全部
	 * 		addresstype  1省  2省市  3省市区  4省市区详细地址
	 * 		账单对象
	 * @return
	 */
	@ApiOperation(value = "发票业务预览--v4.3.17", notes = "发票业务预览--v4.3.17")
	@PostMapping("/preview/bill/business")
	public AjaxResult previewBillBusiness(@RequestBody FinanceStatementReq req) {

		return financeStatementService.previewBillBusiness(req);
	}

	@ApiOperation(value = "发票明细导出--v4.3.17", notes = "发票明细导出--v4.3.17")
	@PostMapping("/bill/detail/export")
	public void billDetailExprot(HttpServletResponse response) {

		financeStatementService.downloadTemplateFile(response, 1);
	}

	@ApiOperation(value = "发票明细入--v4.3.17", notes = "发票明细导入--v4.3.17")
	@PostMapping("/bill/detail/import")
	public AjaxResult billDetailImprot(@RequestParam(value = "upfile") MultipartFile file, @RequestParam(value = "waybillIds") String waybillIds,
			@RequestParam(value = "customerType") Integer customerType) {
		try {
			// 导入支持的文件不能大于800K
			if (!FileUtils.checkFileSize(file.getSize(), 800, "K")) {
				return AjaxResult.requestError("文件大小不能超过800KB");
			}

			// 从读取的文件流中获取workbook
			InputStream in = file.getInputStream();
			ZipSecureFile.setMinInflateRatio(-1.0d);
			Workbook workbook = ExcelUtil.getWorkbook(in, file.getOriginalFilename());
			in.close();

			String sheetName = ExcelUtil.getSheetName(workbook, 0);
			if (!("发票明细" + BusinessConstants.IMPORT_BILL_DETAIL_VERSION).equals(sheetName)) {
				return AjaxResult.popError("导入文件版本不正确");
			}
			if (customerType == null) {
				return AjaxResult.error("请选择预览方类型");
			}

			List<Long> ids = new ArrayList<>();
			for (String s : waybillIds.split(",")) {
				ids.add(Long.parseLong(s.trim()));
			}

			return financeStatementService.importBillDetail(workbook, ids, customerType);

		} catch (Exception e) {
			e.printStackTrace();
			return AjaxResult.error("导入失败");
		}
	}

	@ApiOperation(value = "发票业务导出--v4.3.17", notes = "发票明细导出--v4.3.17")
	@PostMapping("/bill/business/export")
	public void billBusinessExprot(HttpServletResponse response) {

		financeStatementService.downloadTemplateFile(response, 2);
	}

	@ApiOperation(value = "发票业务导入--v4.3.17", notes = "发票业务导入--v4.3.17")
	@PostMapping("/bill/business/import")
	public AjaxResult billBusinessImprot(@RequestParam(value = "upfile") MultipartFile file) {
		try {
			// 导入支持的文件不能大于800K
			if (!FileUtils.checkFileSize(file.getSize(), 800, "K")) {
				return AjaxResult.requestError("文件大小不能超过800KB");
			}

			// 从读取的文件流中获取workbook
			InputStream in = file.getInputStream();
			ZipSecureFile.setMinInflateRatio(-1.0d);
			Workbook workbook = ExcelUtil.getWorkbook(in, file.getOriginalFilename());
			in.close();

			String sheetName = ExcelUtil.getSheetName(workbook, 0);
			if (!("特定业务" + BusinessConstants.IMPORT_BILL_BUSINESS_VERSION).equals(sheetName)) {
				return AjaxResult.popError("导入文件版本不正确");
			}

			return financeStatementService.importBillBusiness(workbook);

		} catch (Exception e) {
			e.printStackTrace();
			return AjaxResult.error("导入失败");
		}
	}

	@GetMapping(value = "/statementInfoList/{id}")
	@ApiOperation(value = "销项发票详情--v4.5.2--ysy", notes = "销项发票详情--v4.5.2--ysy")
	public CommonResult<StatementInfoRes> statementInfoList(@PathVariable Long id) {
		StatementInfoRes statementInfoRes = financeStatementService.statementInfoList(id);
		return CommonResult.success(statementInfoRes);
	}

	@PostMapping("/statementMoneyTotal")
	@ApiOperation(value = "待申请开票运单列表金额汇总--托运人、物流金融端---v4.5.6---ysy", notes = "销项发票详情--v4.5.6--ysy")
	public CommonResult<StatementMoneyRes> statementMoneyTotal(@RequestBody WaybillReq waybillReq) {
		// 校验托运人项目分组权限
		this.addContractIds(waybillReq);
		// 托运人端开票申请只显示自己录单的 金融端看别人录入的运单
		if (SHIPPER.equals(SecurityUtils.getClientType())) {
			waybillReq.setIsRecorder(0);
		} else if (FINANCE.equals(SecurityUtils.getClientType())) {
			waybillReq.setBusinessModelId(SecurityUtils.getBusinessModelId());
			waybillReq.setIsRecorder(1);
		}

		StatementMoneyRes statementMoneyRes = waybillService.statementMoneyTotal(waybillReq);
		return CommonResult.success(statementMoneyRes);
	}

	@PostMapping("/uploadPaymentFile")
	@ApiOperation(value = "上传支付凭证-4.6.2", notes = "上传支付凭证")
	public CommonResult uploadPaymentFile(@RequestBody FinanceStatementReq req) {

		// 金融端和托运人端只能已支付,前端不传profitState
		if (PLATFORM.equals(SecurityUtils.getClientType())){
			if (!Arrays.asList(2,3).contains(req.getProfitState())){
				return CommonResult.error("参数错误,请选择正确的支付状态");
			}
		}else {
			req.setProfitState(2);
		}

		return financeStatementService.uploadPaymentFile(req);
	}

	@PostMapping("/getLastEmail")
	@ApiOperation(value = "获取当前用户上一次输入的邮箱-4.6.2", notes = "获取当前用户上一次输入的邮箱")
	public CommonResult getLastEmail() {

		return financeStatementService.getLastEmail();
	}

	@PostMapping("/isPlatform")
	@ApiOperation(value = "下级开票商户信息-4.6.2", notes = "")
	public AjaxResult isPlatform(@RequestBody FinanceStatementReq req) {

		return financeStatementService.isPlatform(req);
	}

	@PostMapping("/isPlatformByFare")
	@ApiOperation(value = "下级开票商户信息-4.6.2", notes = "")
	public AjaxResult isPlatformByFare(@RequestBody FinanceStatementReq req) {

		return financeStatementService.isPlatformByFare(req);
	}

	@GetMapping("/approveInfo/{id}")
	@ApiOperation(value = "审批流程-4.6.2", notes = "")
	public CommonResult approveInfo(@PathVariable Long id) {

		return financeStatementService.approveInfo(id);
	}


	@GetMapping("/getTradeFlow/{id}")
	@ApiOperation(value = "获取支付凭证-4.6.2", notes = "")
	public CommonResult getTradeFlow(@PathVariable Long id) {
		List<PaymentAttachmentInfo> paymentAttachmentInfos = financeStatementService.selectAndDownloadByStatementId(id);
		return CommonResult.success(paymentAttachmentInfos);
	}


	@Resource
	private SysTask sysTask;


	@GetMapping("/test")
	public CommonResult approveInfo() {
		sysTask.financeStatementPayQuery();
		return CommonResult.success();
	}

}
