package com.zly.project.finance.statement.mapper;

import java.util.List;

import com.zly.project.finance.statement.domain.FinanceStatementBill;

/**
 * 开票发票登记Mapper接口
 *
 * <AUTHOR>
 * @date 2022-07-14
 */
public interface FinanceStatementBillMapper
{
    /**
     * 查询开票发票登记
     *
     * @param statementId 对账表主键
     * @return 开票发票登记
     */
    List<FinanceStatementBill> selectFinanceStatementBillByStatementId(Long statementId);

    /**
     * 查询开票发票登记列表
     *
     * @param financeStatementBill 开票发票登记
     * @return 开票发票登记集合
     */
    List<FinanceStatementBill> selectFinanceStatementBillList(FinanceStatementBill financeStatementBill);

    /**
     * 新增开票发票登记
     *
     * @param financeStatementBill 开票发票登记
     * @return 结果
     */
    int insertFinanceStatementBill(FinanceStatementBill financeStatementBill);

    /**
     * 修改开票发票登记
     *
     * @param financeStatementBill 开票发票登记
     * @return 结果
     */
    int updateFinanceStatementBill(FinanceStatementBill financeStatementBill);

    /**
     * 删除开票发票登记
     *
     * @param id 开票发票登记主键
     * @return 结果
     */
    int deleteFinanceStatementBillById(Long id);

	int logicDeleteFinanceStatementBillById(Long id);

    /**
     * 批量删除开票发票登记
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteFinanceStatementBillByIds(Long[] ids);

	int logicDeleteFinanceStatementBillByIds(List<Long> ids, String updateBy);

	List<FinanceStatementBill> selectFinanceStatementBillByStatementIds(List<Long> statementIds);

	int insertFinanceStatementBills(List<FinanceStatementBill> financeStatementBills);

}
