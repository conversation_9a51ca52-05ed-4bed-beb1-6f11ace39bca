package com.zly.project.finance.statement.mapper;

import com.zly.project.finance.statement.domain.FinanceStatementWaybill;
import com.zly.project.finance.statement.domain.res.FinanceStatementRes;

import java.util.List;
import java.util.Map;

/**
 * 对账清单Mapper接口
 *
 * <AUTHOR>
 * @date 2021-11-22
 */
public interface FinanceStatementWaybillMapper
{
    /**
     * 查询对账清单
     *
     * @param id 对账清单主键
     * @return 对账清单
     */
    public FinanceStatementWaybill selectFinanceStatementWaybillById(Long id);

    /**
     * 查询对账清单列表
     *
     * @param financeStatementWaybill 对账清单
     * @return 对账清单集合
     */
    public List<FinanceStatementWaybill> selectFinanceStatementWaybillList(FinanceStatementWaybill financeStatementWaybill);

    /**
     * 新增对账清单
     *
     * @param financeStatementWaybill 对账清单
     * @return 结果
     */
    public int insertFinanceStatementWaybill(FinanceStatementWaybill financeStatementWaybill);

    /**
     * 修改对账清单
     *
     * @param financeStatementWaybill 对账清单
     * @return 结果
     */
    public int updateFinanceStatementWaybill(FinanceStatementWaybill financeStatementWaybill);

    /**
     * 删除对账清单
     *
     * @param id 对账清单主键
     * @return 结果
     */
    public int deleteFinanceStatementWaybillById(Long id);

    /**
     * 批量删除对账清单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFinanceStatementWaybillByIds(Long[] ids);

	List<Long> selectFinanceStatementWaybillIdList(FinanceStatementWaybill financeStatementWaybill);

    /**
     * 查询华夏发票推送所需的运单列表
     *
     * @param statementId 对账单id
     * @return 对账清单集合
     */
    List<Map<String,String>> selectOrderListByStatementId(Long statementId);

    List<Long> selectListByStatementId(Long statementId);

	FinanceStatementWaybill selectFinanceStatementWaybillByShippingNoteNumber(String shippingNoteNumber);

	List<FinanceStatementWaybill> selectwaybillIdsByStatementNo(String statementNo);

	List<FinanceStatementWaybill> selectByWaybillIds(List<Long> waybillIds);

	Long selectStatementIdByShippingNoteNumber(String shippingNoteNumber);

	List<Long> selectStatementIdByWaybillIds(List<Long> waybillIds, Long customerId);

	int insertFinanceStatementWaybills(List<FinanceStatementWaybill> financeStatementWaybills);

	List<FinanceStatementRes> stasticWaybillFareByStatementIds(List<Long> statementIds);

	List<Long> selectStatementIdsByShippingNoteNumber(String shippingNoteNumber);
}
