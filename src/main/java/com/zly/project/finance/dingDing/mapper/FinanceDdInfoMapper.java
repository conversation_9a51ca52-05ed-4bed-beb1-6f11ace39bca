package com.zly.project.finance.dingDing.mapper;

import java.util.List;

import com.zly.project.finance.dingDing.domain.FinanceDdInfo;

/**
 * 钉钉开票申请信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-07
 */
public interface FinanceDdInfoMapper
{
    /**
     * 查询钉钉开票申请信息
     *
     * @param id 钉钉开票申请信息主键
     * @return 钉钉开票申请信息
     */
    public FinanceDdInfo selectFinanceDdInfoById(Long id);

    /**
     * 查询钉钉开票申请信息列表
     *
     * @param financeDdInfo 钉钉开票申请信息
     * @return 钉钉开票申请信息集合
     */
    public List<FinanceDdInfo> selectFinanceDdInfoList(FinanceDdInfo financeDdInfo);

    /**
     * 新增钉钉开票申请信息
     *
     * @param financeDdInfo 钉钉开票申请信息
     * @return 结果
     */
    public int insertFinanceDdInfo(FinanceDdInfo financeDdInfo);

    /**
     * 修改钉钉开票申请信息
     *
     * @param financeDdInfo 钉钉开票申请信息
     * @return 结果
     */
    public int updateFinanceDdInfo(FinanceDdInfo financeDdInfo);

    /**
     * 删除钉钉开票申请信息
     *
     * @param id 钉钉开票申请信息主键
     * @return 结果
     */
    public int deleteFinanceDdInfoById(Long id);

    /**
     * 批量删除钉钉开票申请信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFinanceDdInfoByIds(Long[] ids);

	List<FinanceDdInfo> queryByinstanceId(String processInstanceId);

	List<FinanceDdInfo> selectFinanceDdInfoByStatementIds(List<Long> statementIds);

	int insertFinanceDdInfos(List<FinanceDdInfo> financeDdInfos);

}
