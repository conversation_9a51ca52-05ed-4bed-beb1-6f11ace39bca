package com.zly.project.tool.swagger;

import javax.annotation.Resource;

import com.zly.framework.task.SysTask;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zly.framework.aspectj.lang.annotation.Anonymous;
import com.zly.framework.mq.RabbitMQReceiver;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.CommonResult;
import com.zly.project.common.domain.req.xiaoyudian.CanReUploadReq;
import com.zly.project.common.service.JuheService;
import com.zly.project.common.service.XiaoYuDianService;
import com.zly.project.etc.domain.WaybillEtcInvoiceRes;
import com.zly.project.etc.service.EtcInoviceService;
import com.zly.project.miniprogram.domain.DriverInfo;
import com.zly.project.miniprogram.service.impl.DriverCardMsgSerivce;
import com.zly.project.transport.waybill.domain.Waybill;
import com.zly.project.transport.waybill.service.impl.WaybillServiceImpl;

/**
 * swagger 测试调试专用接口
 *
 * <AUTHOR>
 */
@Api("测试调试专用接口")
@RestController
@RequestMapping("/test")
public class TestController extends BaseController {
	@Resource
	DriverCardMsgSerivce driverCardMsgSerivce;
	@Resource
	XiaoYuDianService xiaoYuDianService;
	@Resource
	WaybillServiceImpl waybillService;
	@Resource
	EtcInoviceService etcInoviceService;

	@Resource
	private RabbitMQReceiver rabbitMQReceiver;

	@Resource
	private JuheService juheService;

	@Resource
	SysTask sysTask;

	@Anonymous
	@ApiOperation("getSocialCreditCodeByName")
	@PostMapping("/getSocialCreditCodeByName")
	public CommonResult getSocialCreditCodeByName() {
		String socialCreditCodeByName = juheService.getSocialCreditCodeByName("江苏龙邦能源有限公司", "91320703MA221J7074");
		System.out.println(socialCreditCodeByName);
		return CommonResult.success();
	}

	@Anonymous
	@ApiOperation("测试导出excel")
	@PostMapping("/exportProcess")
	public CommonResult exportProcess() {
		String message = "{\"clientType\":2,\"createId\":171429603444000042,\"defaultProject\":1,\"freightForwarderId\":171429603429500039,\"recordId\":175066784683700001,\"requestParams\":\"{\\\"beginCreateTime\\\":\\\"2024-12-23 00:00:00 00:00:00\\\",\\\"endCreateTime\\\":\\\"2025-06-23 23:59:59 23:59:59\\\",\\\"freightForwarderId\\\":171429603429500039,\\\"isFleet\\\":-1,\\\"offSet\\\":0,\\\"pageNum\\\":1,\\\"pageSize\\\":10,\\\"proxyInvoiceState\\\":0,\\\"shippingNoteNumber\\\":\\\"T2504261625062163368\\\"}\",\"requestType\":5}";
		rabbitMQReceiver.exportProcess(message);
		return CommonResult.success();
	}

	@ApiOperation("模仿司机名片推送--处理数据")
	@PostMapping("/saveInfoFromDriverCard")
	public CommonResult saveInfoFromDriverCard(@RequestBody DriverInfo driverInfo) {
		driverCardMsgSerivce.saveDriverCard(driverInfo);
		return CommonResult.success();
	}

	@ApiOperation("模仿小雨点运单退单推送--处理数据")
	@PostMapping("/lbfpCanReUpload")
	public CommonResult lbfpCanReUpload(@RequestBody CanReUploadReq req) {
		xiaoYuDianService.lbfpCanReUpload(req);
		return CommonResult.success();
	}

	@ApiOperation("模仿小雨点账单校验推送--处理数据")
	@PostMapping("/lbfpNotifyBillUpload")
	public CommonResult lbfpNotifyBillUpload(@RequestBody CanReUploadReq req) {
		xiaoYuDianService.lbfpNotifyBillUpload(req);
		return CommonResult.success();
	}

	@ApiOperation("仅测试使用")
	@GetMapping("/justTest")
	public CommonResult justTest() {
		waybillService.waybillPositioning(173535799699400885L);
		return CommonResult.success();
	}

	@ApiOperation("仅测试使用定时任务，拉取运单etc发票数据")
	@GetMapping("/getEtcInvoice/{waybillId}")
	public CommonResult<WaybillEtcInvoiceRes> getEtcInvoice(@PathVariable Long waybillId) {
		Waybill waybill = waybillService.selectWaybillById(waybillId);
		WaybillEtcInvoiceRes etcInvoice = etcInoviceService.getEtcInvoice(waybill);
		return CommonResult.success(etcInvoice);
	}

	@ApiOperation("仅测试使用定时任务，拉取运单etc发票数据")
	@GetMapping("/getEtcInvoice")
	public CommonResult<WaybillEtcInvoiceRes> getEtcInvoiceAll() {
		sysTask.getEtcInvoice();
		return CommonResult.success();
	}

}
