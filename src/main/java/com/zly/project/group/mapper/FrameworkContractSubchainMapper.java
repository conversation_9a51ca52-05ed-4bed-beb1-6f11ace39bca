package com.zly.project.group.mapper;

import java.util.List;

import com.zly.project.group.domain.FrameworkContractSubchain;
import com.zly.project.group.domain.FrameworkContractSubchainEx;

/**
 * 项目合约转包关系明细Mapper接口
 *
 * <AUTHOR>
 * @date 2023-01-06
 */
public interface FrameworkContractSubchainMapper {
	/**
	 * 查询项目合约转包关系明细
	 *
	 * @param id
	 *            项目合约转包关系明细主键
	 * @return 项目合约转包关系明细
	 */
	FrameworkContractSubchain selectFrameworkContractSubchainById(Long id);

	/**
	 * 查询某个项目合约的转包关系
	 *
	 * @param contractId
	 *            项目合约ID
	 * @return 项目合约的转包关系
	 */
	List<FrameworkContractSubchain> selectFrameworkContractSubchainByContractId(Long contractId);

	/**
	 * 查询项目合约转包关系明细列表
	 *
	 * @param frameworkContractSubchain
	 *            项目合约转包关系明细
	 * @return 项目合约转包关系明细集合
	 */
	List<FrameworkContractSubchain> selectFrameworkContractSubchainList(FrameworkContractSubchain frameworkContractSubchain);

	/**
	 * 新增项目合约转包关系明细
	 *
	 * @param frameworkContractSubchain
	 *            项目合约转包关系明细
	 * @return 结果
	 */
	int insertFrameworkContractSubchain(FrameworkContractSubchain frameworkContractSubchain);

	/**
	 * 修改项目合约转包关系明细
	 *
	 * @param frameworkContractSubchain
	 *            项目合约转包关系明细
	 * @return 结果
	 */
	int updateFrameworkContractSubchain(FrameworkContractSubchain frameworkContractSubchain);

	/**
	 * 删除项目合约转包关系明细
	 *
	 * @param id
	 *            项目合约转包关系明细主键
	 * @return 结果
	 */
	int deleteFrameworkContractSubchainById(Long id);

	/**
	 * 批量删除项目合约转包关系明细
	 *
	 * @param ids
	 *            需要删除的数据主键集合
	 * @return 结果
	 */
	int deleteFrameworkContractSubchainByIds(Long[] ids);

	int deleteFrameworkContractSubchainByContractId(Long contractId);

	List<FrameworkContractSubchain> selectForwarderUpCustomerSubchainsByContractIds(List<Long> contractIds);

	FrameworkContractSubchain selectSubchainMaxDeepByContractId(Long id);

	List<FrameworkContractSubchain> selectByContractIds(List<Long> contractIds);

	int insertFrameworkContractSubchains(List<FrameworkContractSubchain> contractSubchains);

	List<FrameworkContractSubchainEx> selectCustomerNamesByContractIds(List<Long> contractIds);

	List<FrameworkContractSubchain> selectForwarderCustomerSubchainsByContractIds(List<Long> contractIds);
}
