package com.zly.project.consignor.consignee.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import javax.annotation.Resource;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zly.common.constant.BusinessConstants;
import com.zly.common.constant.HttpStatus;
import com.zly.common.enums.ClientType;
import com.zly.common.exception.ServiceException;
import com.zly.common.utils.*;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.common.service.IChinaProvincesCitiesAreasService;
import com.zly.project.consignor.consignee.domain.ConsigneeInfo;
import com.zly.project.consignor.consignee.domain.LocationModel;
import com.zly.project.consignor.consignee.domain.req.ConsigneeReq;
import com.zly.project.consignor.consignee.mapper.ConsigneeInfoMapper;
import com.zly.project.consignor.consignee.mapper.ConsigneeInfoMapperEx;
import com.zly.project.consignor.consignee.service.IConsigneeInfoService;
import com.zly.project.consignor.customer.domain.CustomerInfo;
import com.zly.project.consignor.customer.mapper.CustomerInfoMapper;
import com.zly.project.tenant.domain.TenantUser;

import lombok.extern.slf4j.Slf4j;

/**
 * 收货地址Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-10-22
 */
@Slf4j
@Service
public class ConsigneeInfoServiceImpl implements IConsigneeInfoService {

	@Resource
	private ConsigneeInfoMapper consigneeInfoMapper;
	@Resource
	private ConsigneeInfoMapperEx consigneeInfoMapperEx;
	@Resource
	private CustomerInfoMapper customerInfoMapper;
	@Resource
	private IChinaProvincesCitiesAreasService chinaProvincesCitiesAreasService;

	/**
	 * 查询收货地址
	 *
	 * @param id
	 * 		收货地址主键
	 * @return 收货地址
	 */
	@Override
	public ConsigneeInfo selectConsigneeInfoById(Long id) {
		return consigneeInfoMapper.selectConsigneeInfoById(id);
	}

	/**
	 * 查询收货地址列表
	 *
	 * @param consigneeInfo
	 * 		收货地址
	 * @return 收货地址
	 */
	@Override
	public List<ConsigneeInfo> selectConsigneeInfoList(ConsigneeInfo consigneeInfo) {
		return consigneeInfoMapper.selectConsigneeInfoList(consigneeInfo);
	}

	/**
	 * 新增收货地址
	 *
	 * @param consigneeInfo
	 * 		收货地址
	 * @return 结果
	 */
	@Override
	public int insertConsigneeInfo(ConsigneeInfo consigneeInfo) {
		try {
			if (consigneeInfo.getId() == null) {
				consigneeInfo.setId(TextUtil.getTimeSequenceID(5));
			}

			consigneeInfo.setCreateBy(SecurityUtils.getNickname());
			consigneeInfo.setCreateTime(DateUtils.getNowDate());
			// 设置托运人id和名称 物流金融服务也调用这个方法
			if (SecurityUtils.getClientType().equals(ClientType.PLATFORM)) {
				consigneeInfo.setCargoOwnerId(SecurityUtils.getFreightForwarderId());
				consigneeInfo.setCargoOwnerName(SecurityUtils.getNickname());
			} else if (!SecurityUtils.getClientType().equals(ClientType.FINANCE)) {
				TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
				CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(tenantUser.getTenantId());
				consigneeInfo.setCargoOwnerId(customerInfo.getId());
				consigneeInfo.setCargoOwnerName(customerInfo.getCustomerName());
			}

			if (CommonUtil.isNotNullOrEmpty(consigneeInfo.getLatitude()) && CommonUtil.isNotNullOrEmpty(consigneeInfo.getLongitude())) {

			} else {
				String preAddress = consigneeInfo.getProvince().equals(consigneeInfo.getCity()) ? "" : consigneeInfo.getProvince();
				LocationModel startlocationInfo = getLocationInfo(preAddress + consigneeInfo.getCity() + consigneeInfo.getArea() + consigneeInfo.getGoodsReceiptPlace());

				consigneeInfo.setLongitude(startlocationInfo.getLongitude());
				consigneeInfo.setLatitude(startlocationInfo.getLatitude());
			}
			return consigneeInfoMapper.insertConsigneeInfo(consigneeInfo);
		} catch (Exception ex) {
			log.error("新增地址错误:" + ExceptionUtils.getStackTrace(ex));
			return 0;
		}
	}

	@Override
	public Map<String, Object> insertConsigneeInfo2(ConsigneeInfo consigneeInfo) {
		Map<String, Object> map = new HashMap<>();
		try {
			if (consigneeInfo.getId() == null) {
				consigneeInfo.setId(TextUtil.getTimeSequenceID(5));
			}

			consigneeInfo.setCreateBy(SecurityUtils.getNickname());
			consigneeInfo.setCreateTime(DateUtils.getNowDate());
			// 设置托运人id和名称 物流金融服务也调用这个方法
			if (SecurityUtils.getClientType().equals(ClientType.PLATFORM)) {
				consigneeInfo.setCargoOwnerId(SecurityUtils.getFreightForwarderId());
				consigneeInfo.setCargoOwnerName(SecurityUtils.getNickname());
			} else if (!SecurityUtils.getClientType().equals(ClientType.FINANCE)) {
				TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
				CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(tenantUser.getTenantId());
				consigneeInfo.setCargoOwnerId(customerInfo.getId());
				consigneeInfo.setCargoOwnerName(customerInfo.getCustomerName());
			}

			if (CommonUtil.isNotNullOrEmpty(consigneeInfo.getLatitude()) && CommonUtil.isNotNullOrEmpty(consigneeInfo.getLongitude())) {

			} else {
				String preAddress = consigneeInfo.getProvince().equals(consigneeInfo.getCity()) ? "" : consigneeInfo.getProvince();
				LocationModel startlocationInfo = getLocationInfo(preAddress + consigneeInfo.getCity() + consigneeInfo.getArea() + consigneeInfo.getGoodsReceiptPlace());

				consigneeInfo.setLongitude(startlocationInfo.getLongitude());
				consigneeInfo.setLatitude(startlocationInfo.getLatitude());
			}
			map.put("latitude", consigneeInfo.getLatitude());
			map.put("longitude", consigneeInfo.getLongitude());
			int i = consigneeInfoMapper.insertConsigneeInfo(consigneeInfo);
			map.put("code", i);
			return map;
		} catch (Exception ex) {
			log.error("新增地址错误:" + ExceptionUtils.getStackTrace(ex));
			map.put("code", 0);
			return map;
		}
	}

	/**
	 * 修改收货地址
	 *
	 * @param consigneeInfo
	 * 		收货地址
	 * @return 结果
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public int updateConsigneeInfo(ConsigneeInfo consigneeInfo) {
		try {
			if (StringUtils.isNull(consigneeInfo) || StringUtils.isNull(consigneeInfo.getId())) {
				throw new RuntimeException("收获地址唯一标识为空");
			}

			TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
			// 原数据状态修改为无效
			ConsigneeInfo up = new ConsigneeInfo();
			up.setId(consigneeInfo.getId());
			up.setState(BusinessConstants.CONSIGNOR_INVALID);
			up.setUpdateBy(tenantUser.getNickName());
			up.setUpdateTime(DateUtils.getNowDate());
			consigneeInfoMapper.updateConsigneeInfo(up);
			// 新增一条数据
			return this.insertConsigneeInfo(consigneeInfo);

		} catch (Exception ex) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return 0;
		}
	}

	/**
	 * 批量删除收货地址
	 *
	 * @param ids
	 * 		需要删除的收货地址主键
	 * @return 结果
	 */
	@Override
	public int deleteConsigneeInfoByIds(Long[] ids) {
		return consigneeInfoMapper.deleteConsigneeInfoByIds(ids);
	}

	/**
	 * 删除收货地址信息
	 *
	 * @param id
	 * 		收货地址主键
	 * @return 结果
	 */
	@Override
	public int deleteConsigneeInfoById(Long id) {
		return consigneeInfoMapper.deleteConsigneeInfoById(id);
	}

	@Override
	public int deleteOwnerByIds(int delete, Long id) {
		return consigneeInfoMapperEx.deleteOwnerByIds(delete, id);
	}

	/**
	 * 查询自己的收货地址
	 *
	 * @param consigneeInfo
	 * @return
	 */
	@Override
	public List<ConsigneeInfo> selectOwnerConsigneeInfoList(ConsigneeInfo consigneeInfo) {
		TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		if (StringUtils.isNotNull(tenantUser)) {
			consigneeInfo.setCargoOwnerId(tenantUser.getTenantId());
			consigneeInfo.setCreateBy(tenantUser.getNickName());
		}
		PageUtils.startPage(false);
		consigneeInfo.setIsVisible(0);
		consigneeInfo.setCreateBy(tenantUser.getNickName());
		List<ConsigneeInfo> nickList = consigneeInfoMapperEx.selectOwnerConsigneeInfoList(consigneeInfo);
		return nickList;
	}

	@Override
	public List<ConsigneeInfo> selectOwnerConsigneeInfoListByContract(ConsigneeReq req) {
		return consigneeInfoMapperEx.selectOwnerConsigneeInfoListByContract(req);
	}

	/**
	 * 根据地址 换经纬度
	 *
	 * @param startAdress
	 * @return
	 */
	@Override
	public List<LocationModel> selectAdressLotLonDetail(String startAdress) {
		try {
			List<LocationModel> locationModelList = new ArrayList<>();
			LocationModel lal = new LocationModel();

			LocationModel startlocationInfo = getLocationInfo(startAdress);

			if (null != startlocationInfo) {
				lal.setLongitude(startlocationInfo.getLongitude());
				lal.setLatitude(startlocationInfo.getLatitude());
			}
			locationModelList.add(startlocationInfo);

			return locationModelList;
		} catch (Exception ex) {
			return null;
		}
	}

	/**
	 * 货主端 修改自己的收货地址
	 *
	 * @param consigneeInfo
	 * @return
	 */
	@Override
	public AjaxResult consignInfoUpdate(ConsigneeInfo consigneeInfo) {

		try {
			TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();

			if (StringUtils.isBlank(consigneeInfo.getConsignee())) {
				return AjaxResult.error("名称不能为空");
			}
			if (!CommonUtil.isNullOrEmpty(consigneeInfo.getContactPhone()) && !ValidateUtils.checkMobilePhone(consigneeInfo.getContactPhone())) {
				return AjaxResult.error("手机号格式错误，请重试");
			}
			consigneeInfo.setUpdateBy(tenantUser.getNickName());
			if (CommonUtil.isNullOrEmpty(consigneeInfo.getProvince()) || CommonUtil.isNullOrEmpty(consigneeInfo.getCity()) || CommonUtil.isNullOrEmpty(consigneeInfo.getArea())) {
				throw new ServiceException("省、市、区必须全部存在，请确认");
			}
			if ((consigneeInfo.getProvince() + consigneeInfo.getCity() + consigneeInfo.getArea()).equals(consigneeInfo.getGoodsReceiptPlace())) {
				throw new ServiceException("收货方信息中的详细地址信息不全，请填写具体地址");
			}
			String provinceCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consigneeInfo.getProvince(), "0");
			consigneeInfo.setProvinceCode(provinceCode);
			String cityCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consigneeInfo.getCity(), provinceCode);
			consigneeInfo.setCityCode(cityCode);
			String areaCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consigneeInfo.getArea(), cityCode);
			consigneeInfo.setAreaCode(areaCode);
			if (CommonUtil.isNotNullOrEmpty(consigneeInfo.getLatitude()) && CommonUtil.isNotNullOrEmpty(consigneeInfo.getLongitude())) {
				consigneeInfo.setLongitude(consigneeInfo.getLongitude());
				consigneeInfo.setLatitude(consigneeInfo.getLatitude());
			} else {
				LocationModel startlocationInfo = getLocationInfo(consigneeInfo.getProvince() + consigneeInfo.getCity() + consigneeInfo.getArea() + consigneeInfo.getGoodsReceiptPlace());
				if (startlocationInfo == null || "0".equals(startlocationInfo.getStatus())) {
					return AjaxResult.error("请输入正确的地址，当前地址没有找到经纬度");
				}
				consigneeInfo.setLongitude(startlocationInfo.getLongitude());
				consigneeInfo.setLatitude(startlocationInfo.getLatitude());
			}
			consigneeInfo.setContactPhone(consigneeInfo.getContactPhone() == null ? "" : consigneeInfo.getContactPhone());
			int success = consigneeInfoMapper.updateConsigneeInfo(consigneeInfo);
			if (success == 1) {
				return AjaxResult.success(HttpStatus.SUCCESS, "修改成功");
			}
			throw new RuntimeException("修改失败");
		} catch (Exception ex) {
			log.error(ex.toString());
			return AjaxResult.error(ex.getMessage(), ex.toString());
		}
	}

	/**
	 * 货主端 删除自己的收货地址
	 *
	 * @param consigneeInfo
	 * @return
	 */
	@Override
	public AjaxResult consignInfoDelete(ConsigneeInfo consigneeInfo) {
		try {
			if (consigneeInfo.getId() == null) {
				throw new RuntimeException("传参有误");
			}
			int success = consigneeInfoMapperEx.deleteOwnerByIds(-1, consigneeInfo.getId());
			if (success == 1) {
				return AjaxResult.success(HttpStatus.SUCCESS, "删除成功");
			}
			throw new RuntimeException("删除失败");
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex.toString());
			return AjaxResult.error(HttpStatus.ERROR, ex.getMessage());
		}
	}

	/**
	 * 货主端 新增自己的收货地址
	 *
	 * @return
	 */
	@Override
	public Long consigneeInfoAdd(ConsigneeInfo consigneeInfo) {
		TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(tenantUser.getTenantId());
		if (customerInfo == null) {
			throw new ServiceException("货主信息异常");
		}
		if (!CommonUtil.isNullOrEmpty(consigneeInfo.getContactPhone()) && !ValidateUtils.checkMobilePhone(consigneeInfo.getContactPhone())) {
			throw new ServiceException("手机号格式错误，请重试");
		}
		consigneeInfo.setCargoOwnerId(tenantUser.getTenantId());
		consigneeInfo.setCargoOwnerName(customerInfo.getCustomerName());
		Integer isVisible = consigneeInfo.getIsVisible();
		// 为校验地址是否存在，查询时仅查询需展示的数据
		consigneeInfo.setIsVisible(0);
		consigneeInfo.setCreateBy(SecurityUtils.getNickname());
		//		// 检查改地址是否已存在
		//		List<ConsigneeInfo> consigneeInfos = consigneeInfoMapper.selectConsigneeInfoList(consigneeInfo);
		//		if (!CommonUtil.isNullOrEmpty(consigneeInfos)) {
		//			throw new ServiceException("该地址已存在，请勿重复添加");
		//		}
		if (CommonUtil.isNullOrEmpty(consigneeInfo.getProvince()) || CommonUtil.isNullOrEmpty(consigneeInfo.getCity()) || CommonUtil.isNullOrEmpty(consigneeInfo.getArea())) {
			throw new ServiceException("省、市、区必须全部存在，请确认");
		}
		if ((consigneeInfo.getProvince() + consigneeInfo.getCity() + consigneeInfo.getArea()).equals(consigneeInfo.getGoodsReceiptPlace())) {
			throw new ServiceException("收货方信息中的详细地址信息不全，请填写具体地址");
		}
		String provinceCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consigneeInfo.getProvince(), "0");
		consigneeInfo.setProvinceCode(provinceCode);
		String cityCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consigneeInfo.getCity(), provinceCode);
		consigneeInfo.setCityCode(cityCode);
		String areaCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consigneeInfo.getArea(), cityCode);
		consigneeInfo.setAreaCode(areaCode);
		if (CommonUtil.isNotNullOrEmpty(areaCode) && CommonUtil.isNullOrEmpty(cityCode)) {
			consigneeInfo.setCityCode(chinaProvincesCitiesAreasService.getCityCodeByAreaCode(areaCode));
		}
		if (CommonUtil.isNotNullOrEmpty(consigneeInfo.getLatitude()) && CommonUtil.isNotNullOrEmpty(consigneeInfo.getLongitude())) {
			consigneeInfo.setLongitude(consigneeInfo.getLongitude());
			consigneeInfo.setLatitude(consigneeInfo.getLatitude());
		} else {
			LocationModel startlocationInfo = null;
			try {
				startlocationInfo = getLocationInfo(consigneeInfo.getProvince() + consigneeInfo.getCity() + consigneeInfo.getArea() + consigneeInfo.getGoodsReceiptPlace());
			} catch (Exception e) {
				throw new ServiceException("请输入正确的地址,当前地址没有找到经纬度");
			}
			if (startlocationInfo == null || "0".equals(startlocationInfo.getStatus())) {
				throw new ServiceException("请输入正确的地址,当前地址没有找到经纬度");
			}
			consigneeInfo.setLongitude(startlocationInfo.getLongitude());
			consigneeInfo.setLatitude(startlocationInfo.getLatitude());
		}
		consigneeInfo.setIsVisible(isVisible);
		consigneeInfo.setId(TextUtil.getTimeSequenceID(5));
		consigneeInfo.setCreateBy(tenantUser.getNickName());
		consigneeInfo.setCreateTime(DateUtils.getNowDate());
		consigneeInfoMapper.insertConsigneeInfo(consigneeInfo);
		return consigneeInfo.getId();

	}

	@Override
	public Long getConsigneeId(ConsigneeInfo consigneeInfo) {
		TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(tenantUser.getTenantId());
		if (customerInfo == null) {
			throw new ServiceException("货主信息异常");
		}
		if ((consigneeInfo.getProvince() + consigneeInfo.getCity() + consigneeInfo.getArea()).equals(consigneeInfo.getGoodsReceiptPlace())) {
			throw new ServiceException("收货方信息中的详细地址信息不全，请填写具体地址");
		}
		consigneeInfo.setCargoOwnerId(tenantUser.getTenantId());
		consigneeInfo.setCargoOwnerName(customerInfo.getCustomerName());
		consigneeInfo.setIsVisible(1);
		Integer isVisible = consigneeInfo.getIsVisible();
		ConsigneeInfo consigneeSearch = JSON.parseObject(JSON.toJSONString(consigneeInfo), ConsigneeInfo.class);
		// 查询是否存在时，不用包含省、市code和信用代码
		// consigneeSearch.setCityCode(null);
		// consigneeSearch.setProvinceCode(null);
		consigneeSearch.setConsigneeCode(null);
		// 检查改地址是否已存在，存在则返回对应id
		List<ConsigneeInfo> consigneeInfos = consigneeInfoMapper.selectConsigneeInfoList(consigneeSearch);
		if (!CommonUtil.isNullOrEmpty(consigneeInfos)) {
			ConsigneeInfo existConsigneeInfo = consigneeInfos.get(0);
			if (CommonUtil.isNotNullOrEmpty(consigneeInfo.getConsigneeCode()) && !existConsigneeInfo.getConsigneeCode().equals(consigneeInfo.getConsigneeCode())) {
				consigneeInfoMapper.updateConsigneeCode(existConsigneeInfo.getId(), consigneeInfo.getConsigneeCode());
			}
			return existConsigneeInfo.getId();
		}
		// 不存在则开始新增
		String provinceCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consigneeInfo.getProvince(), "0");
		consigneeInfo.setProvinceCode(provinceCode);
		String cityCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consigneeInfo.getCity(), provinceCode);
		consigneeInfo.setCityCode(cityCode);
		String areaCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consigneeInfo.getArea(), cityCode);
		consigneeInfo.setAreaCode(areaCode);
		if (CommonUtil.isNotNullOrEmpty(consigneeInfo.getLatitude()) && CommonUtil.isNotNullOrEmpty(consigneeInfo.getLongitude())) {

		} else {
			LocationModel startlocationInfo = null;
			try {
				startlocationInfo = getLocationInfo(consigneeInfo.getProvince() + consigneeInfo.getCity() + consigneeInfo.getArea() + consigneeInfo.getGoodsReceiptPlace());
			} catch (Exception e) {
				throw new ServiceException("请输入正确的地址,当前地址没有找到经纬度");
			}
			if (startlocationInfo == null || "0".equals(startlocationInfo.getStatus())) {
				throw new ServiceException("请输入正确的地址,当前地址没有找到经纬度");
			}
			consigneeInfo.setLongitude(startlocationInfo.getLongitude());
			consigneeInfo.setLatitude(startlocationInfo.getLatitude());
		}
		consigneeInfo.setIsVisible(isVisible);
		consigneeInfo.setId(TextUtil.getTimeSequenceID(5));
		consigneeInfo.setCreateBy(tenantUser.getNickName());
		consigneeInfo.setCreateTime(DateUtils.getNowDate());
		consigneeInfoMapper.insertConsigneeInfo(consigneeInfo);
		return consigneeInfo.getId();
	}

	@Override
	public void generateConsigneeByDetailAddress(ConsigneeInfo consigneeInfo, Map<String, String> areaMap) {
		// 1、根据详细地址，获取省市区
		Map<String, String> addressMap = CommonUtil.addressResolution(consigneeInfo.getGoodsReceiptPlace());
		consigneeInfo.setProvince(addressMap.get("province"));
		String provinceKey = consigneeInfo.getProvince() + "_" + 1 + "_" + "0";
		consigneeInfo.setProvinceCode(areaMap.get(provinceKey) == null ? CommonUtil.getAbnormaProvinceCode(consigneeInfo.getProvince(), areaMap) : areaMap.get(provinceKey));
		consigneeInfo.setCity(addressMap.get("city"));
		String cityKey = consigneeInfo.getCity() + "_" + 2 + "_" + consigneeInfo.getProvinceCode();
		consigneeInfo.setCityCode(areaMap.get(cityKey) == null ? "" : areaMap.get(cityKey));
		String countyKey = consigneeInfo.getArea() + "_" + 3 + "_" + consigneeInfo.getCityCode();
		consigneeInfo.setArea(addressMap.get("county"));
		consigneeInfo.setAreaCode(areaMap.get(countyKey) == null ? "" : areaMap.get(countyKey));
		if (CommonUtil.isNullOrEmpty(consigneeInfo.getProvinceCode())) {
			for (Map.Entry<String, String> stringStringEntry : addressMap.entrySet()) {
				if (!StringUtils.isEmpty(addressMap.get("province"))) {
					if (addressMap.get("province").length() > 1) {
						if (stringStringEntry.getKey().contains((addressMap.get("province").substring(0, 2)))) {
							consigneeInfo.setProvinceCode(stringStringEntry.getValue());
						}
					} else {
						if (stringStringEntry.getKey().contains((addressMap.get("province")))) {
							consigneeInfo.setProvinceCode(stringStringEntry.getValue());
						}
					}
				}
			}
		}
		if (CommonUtil.isNullOrEmpty(consigneeInfo.getCityCode())) {
			for (Map.Entry<String, String> stringStringEntry : addressMap.entrySet()) {
				if (!StringUtils.isEmpty(addressMap.get("city"))) {
					if (addressMap.get("city").length() > 2) {
						if (stringStringEntry.getKey().contains((addressMap.get("city").substring(0, 3)))) {
							consigneeInfo.setCityCode(stringStringEntry.getValue());
						}
					} else {
						if (stringStringEntry.getKey().contains((addressMap.get("city")))) {
							consigneeInfo.setCityCode(stringStringEntry.getValue());
						}
					}
				}
			}
		}
	}

	@Override
	public int insertConsignorInfoList(List<ConsigneeInfo> consigneeList) {
		AtomicInteger i = new AtomicInteger();
		consigneeList.forEach(l -> {
			i.addAndGet(consigneeInfoMapper.insertConsigneeInfo(l));
		});
		return i.get();
		/*return consigneeInfoMapperEx.insertList(consigneeList);*/
	}

	@Override
	public List<ConsigneeInfo> selectConsigneeInfoListByConsignees(Long customerId, List<String> consignees) {
		return consigneeInfoMapperEx.selectConsigneeInfoListByConsignees(customerId, consignees);
	}

	/**
	 * 获取位置信息
	 *
	 * @param startAdress
	 * @return
	 * @throws Exception
	 */
	public LocationModel getLocationInfo(String startAdress) throws Exception {
		if (StringUtils.isBlank(startAdress)) {
			return null;
		}
		String geocodeUrl = "https://restapi.amap.com/v5/place/text?key=b4ef1b65f0ca5eb684a70952d1c56210&output=json&keywords=" + StringUtils.deleteWhitespace(startAdress);
		String addressJson = HttpClientUtils.getHttp(geocodeUrl);
		if (TextUtil.isNull(addressJson)) {
			return null;
		}
		try {
			JSONObject dataJson = JSON.parseObject(addressJson);
			LocationModel locationModel = new LocationModel();
			locationModel.setStatus(dataJson.getString("status"));
			locationModel.setInfo(dataJson.getString("info"));
			locationModel.setInfocode(dataJson.getString("infocode"));
			// 返回值为 0 或 1
			// 0 表示请求失败；
			// 1 表示请求成功。
			if (!"1".equals(dataJson.getString("status"))) {
				return null;
			}
			JSONArray geocodes = dataJson.getJSONArray("pois");
			if (geocodes.size() != 0) {
				JSONObject locationInfo = geocodes.getJSONObject(0);
				locationModel.setProvince(locationInfo.getString("pname").replace("[]", ""));
				locationModel.setCity(locationInfo.getString("cityname").replace("[]", ""));
				if (locationInfo.containsKey("citycode")) {
					locationModel.setCitycode(locationInfo.getString("citycode").replace("[]", ""));
				}
				locationModel.setDistrict(locationInfo.getString("adname").replace("[]", ""));
				locationModel.setAdcode(locationInfo.getString("adcode").replace("[]", ""));
				// 获取经纬度（"location": "120.530373,31.912560"）
				String location = locationInfo.getString("location");
				if (StringUtils.isNotBlank(location)) {
					String[] l = location.split(",");
					locationModel.setLongitude(l[0]);
					if (l.length > 1) {
						locationModel.setLatitude(l[1]);
					}
				}
			}
			return locationModel;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public void mergeShippers(Long fromId, Long toId, String toName) {
		consigneeInfoMapperEx.mergeShippers(fromId, toId, toName);
	}

	@Override
	public List<ConsigneeInfo> selectConsigneeInfoListWithId(ConsigneeInfo consignee) {
		return consigneeInfoMapper.selectConsigneeInfoListWithId(consignee);
	}

	@Override
	public int selectOwnerConsigneeInfoListCount(ConsigneeInfo consigneeInfo) {
		TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		if (StringUtils.isNotNull(tenantUser)) {
			consigneeInfo.setCargoOwnerId(tenantUser.getTenantId());
			consigneeInfo.setCreateBy(tenantUser.getNickName());
		}
		consigneeInfo.setIsVisible(0);
		consigneeInfo.setCreateBy(tenantUser.getNickName());
		int count = consigneeInfoMapperEx.selectOwnerConsigneeInfoListCount(consigneeInfo);
		return count;
	}
}
