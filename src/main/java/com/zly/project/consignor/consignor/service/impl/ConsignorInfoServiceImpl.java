package com.zly.project.consignor.consignor.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import javax.annotation.Resource;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zly.common.constant.BusinessConstants;
import com.zly.common.constant.HttpStatus;
import com.zly.common.enums.ClientType;
import com.zly.common.exception.ServiceException;
import com.zly.common.utils.*;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.project.common.service.IChinaProvincesCitiesAreasService;
import com.zly.project.consignor.consignee.domain.LocationModel;
import com.zly.project.consignor.consignor.domain.ConsignorInfo;
import com.zly.project.consignor.consignor.domain.req.ConsignorReq;
import com.zly.project.consignor.consignor.mapper.ConsignorInfoMapper;
import com.zly.project.consignor.consignor.mapper.ConsignorInfoMapperEx;
import com.zly.project.consignor.consignor.service.IConsignorInfoService;
import com.zly.project.consignor.customer.domain.CustomerInfo;
import com.zly.project.consignor.customer.mapper.CustomerInfoMapper;
import com.zly.project.tenant.domain.TenantUser;

import lombok.extern.slf4j.Slf4j;

/**
 * 发货地址Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-10-22
 */
@Slf4j
@Service
public class ConsignorInfoServiceImpl implements IConsignorInfoService {
	@Resource
	private ConsignorInfoMapper consignorInfoMapper;
	@Resource
	private ConsignorInfoMapperEx consignorInfoMapperEx;
	@Resource
	private CustomerInfoMapper customerInfoMapper;
	@Resource
	private IChinaProvincesCitiesAreasService chinaProvincesCitiesAreasService;

	/**
	 * 查询发货地址
	 *
	 * @param id
	 * 		发货地址主键
	 * @return 发货地址
	 */
	@Override
	public ConsignorInfo selectConsignorInfoById(Long id) {
		return consignorInfoMapper.selectConsignorInfoById(id);
	}

	/**
	 * 查询发货地址列表
	 *
	 * @param consignorInfo
	 * 		发货地址
	 * @return 发货地址
	 */
	@Override
	public List<ConsignorInfo> selectConsignorInfoList(ConsignorInfo consignorInfo) {
		return consignorInfoMapper.selectConsignorInfoList(consignorInfo);
	}

	/**
	 * 新增发货地址
	 *
	 * @param consignorInfo
	 * 		发货地址
	 * @return 结果
	 */
	@Override
	public int insertConsignorInfo(ConsignorInfo consignorInfo) {
		try {
			if (consignorInfo.getId() == null) {
				consignorInfo.setId(TextUtil.getTimeSequenceID(5));
			}
			consignorInfo.setCreateBy(SecurityUtils.getNickname());
			consignorInfo.setCreateTime(DateUtils.getNowDate());
			// 设置托运人id和名称
			if (SecurityUtils.getClientType().equals(ClientType.PLATFORM)) {
				consignorInfo.setCargoOwnerId(SecurityUtils.getFreightForwarderId());
				consignorInfo.setCargoOwnerName(SecurityUtils.getNickname());
			} else if (!SecurityUtils.getClientType().equals(ClientType.FINANCE)) {
				TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
				CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(tenantUser.getTenantId());
				consignorInfo.setCargoOwnerId(customerInfo.getId());
				consignorInfo.setCargoOwnerName(customerInfo.getCustomerName());
			}
			if (CommonUtil.isNotNullOrEmpty(consignorInfo.getLatitude()) && CommonUtil.isNotNullOrEmpty(consignorInfo.getLongitude())) {

			} else {
				String preAddress = consignorInfo.getProvince().equals(consignorInfo.getCity()) ? "" : consignorInfo.getProvince();
				LocationModel startlocationInfo = getLocationInfo(preAddress + consignorInfo.getCity() + consignorInfo.getArea() + consignorInfo.getPlaceOfLoading());

				consignorInfo.setLongitude(startlocationInfo.getLongitude());
				consignorInfo.setLatitude(startlocationInfo.getLatitude());
			}

			return consignorInfoMapper.insertConsignorInfo(consignorInfo);
		} catch (Exception ex) {
			log.error("新增地址错误:" + ExceptionUtils.getStackTrace(ex));
			return 0;
		}
	}

	@Override
	public Map<String, Object> insertConsignorInfo2(ConsignorInfo consignorInfo) {
		Map<String, Object> map = new HashMap<>();
		try {
			if (consignorInfo.getId() == null) {
				consignorInfo.setId(TextUtil.getTimeSequenceID(5));
			}
			consignorInfo.setCreateBy(SecurityUtils.getNickname());
			consignorInfo.setCreateTime(DateUtils.getNowDate());
			// 设置托运人id和名称
			if (SecurityUtils.getClientType().equals(ClientType.PLATFORM)) {
				consignorInfo.setCargoOwnerId(SecurityUtils.getFreightForwarderId());
				consignorInfo.setCargoOwnerName(SecurityUtils.getNickname());
			} else if (!SecurityUtils.getClientType().equals(ClientType.FINANCE)) {
				TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
				CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(tenantUser.getTenantId());
				consignorInfo.setCargoOwnerId(customerInfo.getId());
				consignorInfo.setCargoOwnerName(customerInfo.getCustomerName());
			}
			if (CommonUtil.isNotNullOrEmpty(consignorInfo.getLatitude()) && CommonUtil.isNotNullOrEmpty(consignorInfo.getLongitude())) {

			} else {
				String preAddress = consignorInfo.getProvince().equals(consignorInfo.getCity()) ? "" : consignorInfo.getProvince();
				LocationModel startlocationInfo = getLocationInfo(preAddress + consignorInfo.getCity() + consignorInfo.getArea() + consignorInfo.getPlaceOfLoading());

				consignorInfo.setLongitude(startlocationInfo.getLongitude());
				consignorInfo.setLatitude(startlocationInfo.getLatitude());
			}
			int i = consignorInfoMapper.insertConsignorInfo(consignorInfo);
			map.put("code", i);
			map.put("longitude", consignorInfo.getLongitude());
			map.put("latitude", consignorInfo.getLatitude());
			return map;
		} catch (Exception ex) {
			log.error("新增地址错误:" + ExceptionUtils.getStackTrace(ex));
			map.put("code", 0);
			return map;
		}
	}

	/**
	 * 获取位置信息
	 *
	 * @param startAdress
	 * @return
	 * @throws Exception
	 */
	public LocationModel getLocationInfo(String startAdress) throws Exception {
		if (StringUtils.isBlank(startAdress)) {
			return null;
		}
		String geocodeUrl = "https://restapi.amap.com/v5/place/text?key=b4ef1b65f0ca5eb684a70952d1c56210&output=json&keywords=" + StringUtils.deleteWhitespace(startAdress);
		String addressJson = HttpClientUtils.getHttp(geocodeUrl);
		if (TextUtil.isNull(addressJson)) {
			return null;
		}
		try {
			JSONObject dataJson = JSON.parseObject(addressJson);
			LocationModel locationModel = new LocationModel();
			locationModel.setStatus(dataJson.getString("status"));
			locationModel.setInfo(dataJson.getString("info"));
			locationModel.setInfocode(dataJson.getString("infocode"));
			// 返回值为 0 或 1
			// 0 表示请求失败；
			// 1 表示请求成功。
			if (!"1".equals(dataJson.getString("status"))) {
				return null;
			}
			JSONArray geocodes = dataJson.getJSONArray("pois");
			if (geocodes.size() != 0) {
				JSONObject locationInfo = geocodes.getJSONObject(0);
				locationModel.setProvince(locationInfo.getString("pname").replace("[]", ""));
				locationModel.setCity(locationInfo.getString("cityname").replace("[]", ""));
				if (locationInfo.containsKey("citycode")) {
					locationModel.setCitycode(locationInfo.getString("citycode").replace("[]", ""));
				}
				locationModel.setDistrict(locationInfo.getString("adname").replace("[]", ""));
				locationModel.setAdcode(locationInfo.getString("adcode").replace("[]", ""));
				// 获取经纬度（"location": "120.530373,31.912560"）
				String location = locationInfo.getString("location");
				if (StringUtils.isNotBlank(location)) {
					String[] l = location.split(",");
					locationModel.setLongitude(l[0]);
					if (l.length > 1) {
						locationModel.setLatitude(l[1]);
					}
				}
			}
			return locationModel;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 修改发货地址
	 *
	 * @param consignorInfo
	 * 		发货地址
	 * @return 结果
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public int updateConsignorInfo(ConsignorInfo consignorInfo) {
		try {
			// SysUser sysUser = SecurityUtils.getLoginUser().getUser();
			if (StringUtils.isNull(consignorInfo) || StringUtils.isNull(consignorInfo.getId())) {
				throw new RuntimeException("缺少地址唯一标识符");
			}
			TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();

			ConsignorInfo up = new ConsignorInfo();
			up.setId(consignorInfo.getId());
			up.setUpdateBy(tenantUser.getNickName());
			up.setState(BusinessConstants.CONSIGNOR_INVALID);// 原数据状态修改为无效
			consignorInfoMapper.updateConsignorInfo(up);

			// 新增一条最新数据
			return this.insertConsignorInfo(consignorInfo);
		} catch (Exception ex) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return 0;
		}
	}

	/**
	 * 批量删除发货地址
	 *
	 * @param ids
	 * 		需要删除的发货地址主键
	 * @return 结果
	 */
	@Override
	public int deleteConsignorInfoByIds(Long[] ids) {
		return consignorInfoMapper.deleteConsignorInfoByIds(ids);
	}

	/**
	 * 删除发货地址信息
	 *
	 * @param id
	 * 		发货地址主键
	 * @return 结果
	 */
	@Override
	public int deleteConsignorInfoById(Long id) {
		return consignorInfoMapper.deleteConsignorInfoById(id);
	}

	@Override
	public int deleteOwnerById(Long id) {
		return consignorInfoMapperEx.deleteOwnerById(id);
	}

	@Override
	public List<ConsignorInfo> selectOwnerConsignorInfoList(ConsignorInfo consignorInfo) {
		TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		if (StringUtils.isNotNull(tenantUser)) {
			consignorInfo.setCargoOwnerId(tenantUser.getTenantId());
			consignorInfo.setCreateBy(tenantUser.getNickName());
		}
		PageUtils.startPage(false);
		consignorInfo.setIsVisible(0);
		consignorInfo.setCreateBy(tenantUser.getNickName());
		List<ConsignorInfo> nickList = consignorInfoMapperEx.selectOwnerConsignorInfoList(consignorInfo);
		return nickList;
	}

	@Override
	public List<ConsignorInfo> selectOwnerConsignorInfoListByContract(ConsignorReq req) {
		return consignorInfoMapperEx.selectOwnerConsignorInfoListByContract(req);
	}

	/**
	 * 货主端 修改自己的发货地址
	 *
	 * @return
	 */
	@Override
	public AjaxResult consignInfoUpdate(ConsignorInfo consignorInfo) {
		try {
			TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
			consignorInfo.setUpdateBy(tenantUser.getNickName());
			if (StringUtils.isBlank(consignorInfo.getConsignor())) {
				return AjaxResult.error("名称不能为空");
			}
			if (!CommonUtil.isNullOrEmpty(consignorInfo.getContactPhone()) && !ValidateUtils.checkMobilePhone(consignorInfo.getContactPhone())) {
				return AjaxResult.error("手机号格式错误");
			}

			if (CommonUtil.isNullOrEmpty(consignorInfo.getProvince()) || CommonUtil.isNullOrEmpty(consignorInfo.getCity()) || CommonUtil.isNullOrEmpty(consignorInfo.getArea())) {
				throw new ServiceException("省、市、区必须全部存在，请确认");
			}
			if ((consignorInfo.getProvince() + consignorInfo.getCity() + consignorInfo.getArea()).equals(consignorInfo.getPlaceOfLoading())) {
				throw new ServiceException("发货方信息中的详细地址信息不全，请填写具体地址");
			}
			String provinceCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consignorInfo.getProvince(), "0");
			consignorInfo.setProvinceCode(provinceCode);
			String cityCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consignorInfo.getCity(), provinceCode);
			consignorInfo.setCityCode(cityCode);
			String areaCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consignorInfo.getArea(), cityCode);
			consignorInfo.setAreaCode(areaCode);
			if (CommonUtil.isNotNullOrEmpty(consignorInfo.getLatitude()) && CommonUtil.isNotNullOrEmpty(consignorInfo.getLongitude())) {
				consignorInfo.setLongitude(consignorInfo.getLongitude());
				consignorInfo.setLatitude(consignorInfo.getLatitude());
			} else {
				LocationModel startlocationInfo = getLocationInfo(consignorInfo.getProvince() + consignorInfo.getCity() + consignorInfo.getArea() + consignorInfo.getPlaceOfLoading());
				if ("0".equals(startlocationInfo.getStatus())) {
					return AjaxResult.error("请输入正确的地址，当前地址没有找到经纬度");
				}
				consignorInfo.setLongitude(startlocationInfo.getLongitude());
				consignorInfo.setLatitude(startlocationInfo.getLatitude());
			}
			consignorInfo.setContactPhone(consignorInfo.getContactPhone() == null ? "" : consignorInfo.getContactPhone());
			int success = consignorInfoMapper.updateConsignorInfo(consignorInfo);
			if (success == 1) {
				return AjaxResult.success(HttpStatus.SUCCESS, "修改成功");
			}
			throw new RuntimeException("修改失败");
		} catch (Exception ex) {
			log.error(ex.toString());
			return AjaxResult.error(HttpStatus.ERROR, ex.getMessage());
		}
	}

	/**
	 * 货主端 删除自己的发货地址
	 *
	 * @return
	 */
	@Override
	public AjaxResult consignInfoDelete(ConsignorInfo consignorInfo) {
		try {
			TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();

			consignorInfo.setUpdateBy(tenantUser.getNickName());
			int success = consignorInfoMapperEx.deleteOwnerById(consignorInfo.getId());
			if (success == 1) {
				return AjaxResult.success(HttpStatus.SUCCESS, "删除成功");
			}
			throw new RuntimeException("删除失败");
		} catch (Exception ex) {
			log.error(ex.toString());
			return AjaxResult.error(HttpStatus.ERROR, ex.getMessage());
		}
	}

	/**
	 * 货主端 新增自己的发货地址
	 *
	 * @return
	 */
	@Override
	public Long consignorInfoAdd(ConsignorInfo consignorInfo) {
		TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(tenantUser.getTenantId());
		if (customerInfo == null)
			throw new ServiceException("货主异常");
		if (!CommonUtil.isNullOrEmpty(consignorInfo.getContactPhone()) && !ValidateUtils.checkMobilePhone(consignorInfo.getContactPhone())) {
			throw new ServiceException("手机号格式错误，请重试");
		}

		if (CommonUtil.isNullOrEmpty(consignorInfo.getProvince()) || CommonUtil.isNullOrEmpty(consignorInfo.getCity()) || CommonUtil.isNullOrEmpty(consignorInfo.getArea())) {
			throw new ServiceException("省、市、区必须全部存在，请确认");
		}
		if ((consignorInfo.getProvince() + consignorInfo.getCity() + consignorInfo.getArea()).equals(consignorInfo.getPlaceOfLoading())) {
			throw new ServiceException("发货方信息中的详细地址信息不全，请填写具体地址");
		}
		String provinceCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consignorInfo.getProvince(), "0");
		consignorInfo.setProvinceCode(provinceCode);
		String cityCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consignorInfo.getCity(), provinceCode);
		consignorInfo.setCityCode(cityCode);
		String areaCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consignorInfo.getArea(), cityCode);
		consignorInfo.setAreaCode(areaCode);
		if (CommonUtil.isNotNullOrEmpty(areaCode) && CommonUtil.isNullOrEmpty(cityCode)) {
			consignorInfo.setCityCode(chinaProvincesCitiesAreasService.getCityCodeByAreaCode(areaCode));
		}
		consignorInfo.setAreaCode(areaCode);
		consignorInfo.setCreateBy(tenantUser.getNickName());
		if (CommonUtil.isNotNullOrEmpty(consignorInfo.getLongitude()) && CommonUtil.isNotNullOrEmpty(consignorInfo.getLatitude())) {
			consignorInfo.setLongitude(consignorInfo.getLongitude());
			consignorInfo.setLatitude(consignorInfo.getLatitude());
		} else {
			LocationModel startlocationInfo = null;
			try {
				startlocationInfo = getLocationInfo(consignorInfo.getProvince() + consignorInfo.getCity() + consignorInfo.getArea() + consignorInfo.getPlaceOfLoading());
			} catch (Exception e) {
				throw new ServiceException("获取地址经纬度发生异常，请重试");
			}
			if (startlocationInfo == null || "0".equals(startlocationInfo.getStatus())) {
				throw new ServiceException("请输入正确的地址,当前地址没有找到经纬度");
			}
			consignorInfo.setLongitude(startlocationInfo.getLongitude());
			consignorInfo.setLatitude(startlocationInfo.getLatitude());
		}
		consignorInfo.setCargoOwnerId(tenantUser.getTenantId());
		consignorInfo.setCargoOwnerName(customerInfo.getCustomerName());
		Integer isVisible = consignorInfo.getIsVisible();
		consignorInfo.setIsVisible(0);
		// 检查该地址是否已存在，此时需查询同步的地址，不同步的不校验
		//		List<ConsignorInfo> consignorInfos = consignorInfoMapper.selectConsignorInfoList(consignorInfo);
		//		if (!CommonUtil.isNullOrEmpty(consignorInfos)) {
		//			throw new ServiceException("该地址已存在，请勿重复添加");
		//		}
		consignorInfo.setIsVisible(isVisible);
		consignorInfo.setId(TextUtil.getTimeSequenceID(5));
		consignorInfo.setCreateTime(DateUtils.getNowDate());
		consignorInfoMapper.insertConsignorInfo(consignorInfo);
		return consignorInfo.getId();
	}

	@Override
	public Long getConsignorId(ConsignorInfo consignorInfo) {
		TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(tenantUser.getTenantId());
		if (customerInfo == null) {
			throw new ServiceException("货主异常");
		}
		if ((consignorInfo.getProvince() + consignorInfo.getCity() + consignorInfo.getArea()).equals(consignorInfo.getPlaceOfLoading())) {
			throw new ServiceException("发货方信息中的详细地址信息不全，请填写具体地址");
		}
		consignorInfo.setCreateBy(tenantUser.getNickName());
		consignorInfo.setCargoOwnerId(tenantUser.getTenantId());
		consignorInfo.setCargoOwnerName(customerInfo.getCustomerName());
		consignorInfo.setIsVisible(1);
		Integer isVisible = consignorInfo.getIsVisible();
		ConsignorInfo consignorSearch = JSON.parseObject(JSON.toJSONString(consignorInfo), ConsignorInfo.class);
		// 查询是否存在时，不用包含省、市code
		// consignorSearch.setCityCode(null);
		// consignorSearch.setProvinceCode(null);
		// 检查改地址是否已存在，此时需查询同步的地址，不同步的不校验，如存在直接返回
		List<ConsignorInfo> consignorInfos = consignorInfoMapper.selectConsignorInfoList(consignorSearch);
		if (!CommonUtil.isNullOrEmpty(consignorInfos)) {
			return consignorInfos.get(0).getId();
		}
		String provinceCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consignorInfo.getProvince(), "0");
		consignorInfo.setProvinceCode(provinceCode);
		String cityCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consignorInfo.getCity(), provinceCode);
		consignorInfo.setCityCode(cityCode);
		String areaCode = chinaProvincesCitiesAreasService.getAreaCodeByAreaName(consignorInfo.getArea(), cityCode);
		consignorInfo.setAreaCode(areaCode);

		if (CommonUtil.isNotNullOrEmpty(consignorInfo.getLatitude()) && CommonUtil.isNotNullOrEmpty(consignorInfo.getLongitude())) {
			consignorInfo.setLongitude(consignorInfo.getLongitude());
			consignorInfo.setLatitude(consignorInfo.getLatitude());
		} else {
			LocationModel startlocationInfo = null;
			try {
				startlocationInfo = getLocationInfo(consignorInfo.getProvince() + consignorInfo.getCity() + consignorInfo.getArea() + consignorInfo.getPlaceOfLoading());
			} catch (Exception e) {
				throw new ServiceException("获取地址经纬度发生异常，请重试");
			}
			if (startlocationInfo == null || "0".equals(startlocationInfo.getStatus())) {
				throw new ServiceException("发货地址解析异常，请确认地址信息是否准确");
			}
			consignorInfo.setLongitude(startlocationInfo.getLongitude());
			consignorInfo.setLatitude(startlocationInfo.getLatitude());
		}
		consignorInfo.setIsVisible(isVisible);
		consignorInfo.setId(TextUtil.getTimeSequenceID(5));
		consignorInfo.setCreateTime(DateUtils.getNowDate());
		consignorInfoMapper.insertConsignorInfo(consignorInfo);
		return consignorInfo.getId();
	}

	@Override
	public int insertConsignorInfoList(List<ConsignorInfo> consignorList) {
		AtomicInteger i = new AtomicInteger();
		consignorList.forEach(l -> {
			i.addAndGet(consignorInfoMapper.insertConsignorInfo(l));
		});
		return i.get();
		// return consignorInfoMapperEx.insertList(consignorList);
	}

	@Override
	public void generateConsignorByDetailAddress(ConsignorInfo consignorInfo, Map<String, String> areaMap) {
		// 1、根据详细地址，获取省市区
		Map<String, String> addressMap = CommonUtil.addressResolution(consignorInfo.getPlaceOfLoading());

		String provinceKey = consignorInfo.getProvince() + "_" + 1 + "_" + "0";
		consignorInfo.setProvince(addressMap.get("province"));
		consignorInfo.setProvinceCode(areaMap.get(provinceKey) == null ? CommonUtil.getAbnormaProvinceCode(consignorInfo.getProvince(), areaMap) : areaMap.get(provinceKey));
		String cityKey = consignorInfo.getCity() + "_" + 2 + "_" + consignorInfo.getProvinceCode();
		consignorInfo.setCity(addressMap.get("city"));
		consignorInfo.setCityCode(areaMap.get(cityKey) == null ? "" : areaMap.get(cityKey));
		String countyKey = consignorInfo.getArea() + "_" + 3 + "_" + consignorInfo.getCityCode();
		consignorInfo.setArea(addressMap.get("county"));
		consignorInfo.setAreaCode(areaMap.get(countyKey) == null ? "" : areaMap.get(countyKey));
		if (consignorInfo.getProvinceCode() == null) {
			for (Map.Entry<String, String> stringStringEntry : addressMap.entrySet()) {
				if (!StringUtils.isEmpty(addressMap.get("province"))) {
					if (addressMap.get("province").length() > 1) {
						if (stringStringEntry.getKey().contains((addressMap.get("province").substring(0, 2)))) {
							consignorInfo.setProvinceCode(stringStringEntry.getValue());
						}
					} else {
						if (stringStringEntry.getKey().contains((addressMap.get("province")))) {
							consignorInfo.setProvinceCode(stringStringEntry.getValue());
						}
					}
				}
			}
		}
		if (consignorInfo.getCityCode() == null) {
			for (Map.Entry<String, String> stringStringEntry : addressMap.entrySet()) {
				if (!StringUtils.isEmpty(addressMap.get("city"))) {
					if (addressMap.get("city").length() > 2) {
						if (stringStringEntry.getKey().contains((addressMap.get("city").substring(0, 3)))) {
							consignorInfo.setCityCode(stringStringEntry.getValue());
						}
					} else {
						if (stringStringEntry.getKey().contains((addressMap.get("city")))) {
							consignorInfo.setCityCode(stringStringEntry.getValue());
						}
					}
				}
			}
		}
	}

	@Override
	public List<ConsignorInfo> selectConsignorInfoListByConsignors(Long customerId, List<String> consignors) {
		return consignorInfoMapperEx.selectConsignorInfoListByConsignors(customerId, consignors);
	}

	public void mergeShippers(Long fromId, Long toId, String toName) {
		consignorInfoMapperEx.mergeShippers(fromId, toId, toName);
	}

	@Override
	public List<ConsignorInfo> selectConsignorInfoListWithId(ConsignorInfo consignor) {
		return consignorInfoMapper.selectConsignorInfoListWithId(consignor);
	}

	@Override
	public int selectOwnerConsignorInfoListCount(ConsignorInfo consignorInfo) {
		TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		if (StringUtils.isNotNull(tenantUser)) {
			consignorInfo.setCargoOwnerId(tenantUser.getTenantId());
			consignorInfo.setCreateBy(tenantUser.getNickName());
		}
		consignorInfo.setIsVisible(0);
		consignorInfo.setCreateBy(tenantUser.getNickName());
		int count = consignorInfoMapperEx.selectOwnerConsignorInfoListCount(consignorInfo);
		return count;
	}
}
