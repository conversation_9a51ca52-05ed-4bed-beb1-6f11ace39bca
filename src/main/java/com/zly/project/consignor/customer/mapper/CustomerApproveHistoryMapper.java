package com.zly.project.consignor.customer.mapper;

import com.zly.project.consignor.customer.domain.CustomerApproveHistory;

import java.util.List;

public interface CustomerApproveHistoryMapper {
    /**
     * 查询货主端审核历史
     *
     * @param id 货主端审核历史主键
     * @return 货主端审核历史
     */
    public CustomerApproveHistory selectCustomerApproveHistoryById(Long id);

    /**
     * 查询货主端审核历史列表
     *
     * @param customerApproveHistory 货主端审核历史
     * @return 货主端审核历史集合
     */
    public List<CustomerApproveHistory> selectCustomerApproveHistoryList(CustomerApproveHistory customerApproveHistory);

    /**
     * 新增货主端审核历史
     *
     * @param customerApproveHistory 货主端审核历史
     * @return 结果
     */
    public int insertCustomerApproveHistory(CustomerApproveHistory customerApproveHistory);

    /**
     * 修改货主端审核历史
     *
     * @param customerApproveHistory 货主端审核历史
     * @return 结果
     */
    public int updateCustomerApproveHistory(CustomerApproveHistory customerApproveHistory);

    /**
     * 删除货主端审核历史
     *
     * @param id 货主端审核历史主键
     * @return 结果
     */
    public int deleteCustomerApproveHistoryById(Long id);

    /**
     * 批量删除货主端审核历史
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerApproveHistoryByIds(Long[] ids);
}
