package com.zly.project.consignor.customer.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.zly.project.consignor.customer.domain.CustomerDriverRelation;

/**
 * 货主司机关系Mapper接口
 *
 * <AUTHOR>
 * @date 2022-04-29
 */
public interface CustomerDriverRelationMapper {
	/**
	 * 查询货主司机关系
	 *
	 * @param customerId
	 *            货主司机关系主键
	 * @param driverId
	 *            司机ID
	 * @return 货主司机关系
	 */
	CustomerDriverRelation selectCustomerDriverRelationByCustomerId(Long customerId, Long driverId);

	/**
	 * 查询货主司机关系列表
	 *
	 * @param customerDriverRelation
	 *            货主司机关系
	 * @return 货主司机关系集合
	 */
	List<CustomerDriverRelation> selectCustomerDriverRelationList(CustomerDriverRelation customerDriverRelation);

	/**
	 * 新增货主司机关系
	 *
	 * @param customerDriverRelation
	 *            货主司机关系
	 * @return 结果
	 */
	int insertCustomerDriverRelation(CustomerDriverRelation customerDriverRelation);

	/**
	 * 修改货主司机关系
	 *
	 * @param customerDriverRelation
	 *            货主司机关系
	 * @return 结果
	 */
	int updateCustomerDriverRelation(CustomerDriverRelation customerDriverRelation);

	/**
	 * 删除货主司机关系
	 *
	 * @param customerId
	 *            货主司机关系主键
	 * @return 结果
	 */
	int deleteCustomerDriverRelationByCustomerId(@Param("customerId") Long customerId, @Param("driverId") Long driverId);

	/**
	 * 批量删除货主司机关系
	 *
	 * @param customerIds
	 *            需要删除的数据主键集合
	 * @return 结果
	 */
	int deleteCustomerDriverRelationByCustomerIds(Long[] customerIds);

	List<CustomerDriverRelation> selectCustomerDriverRelationListByIds(Map<String, Object> map);

	int updateDriverByShipperIdAndOldId(@Param("customerId")Long customerId, @Param("oldId")Long oldId, @Param("newId")Long newId);

	int updateDriverByNewIdAndOldId( @Param("newId")Long newId, @Param("oldId")Long oldId);


	List<CustomerDriverRelation> selectListLimit(@Param("offset")Integer offset, @Param("pageSize")Integer pageSize);

    int countAll();

	CustomerDriverRelation selectCustomerDriverRelationByDrivingLicense(@Param("customerId")Long customerId,@Param("drivingLicense") String drivingLicense);

	List<CustomerDriverRelation> selectCustomerDriverRelationByDrivingLicenseWithoutCustomerId(@Param("drivingLicense") String drivingLicense);

	void updateDriverByNewIdAndOldDrivingLicense(@Param("id")Long id,@Param("driverIds") List<Long> driverIds);

	List<Long> checkCustomerDriverRelationTelephone(@Param("customerIds") List<Long> customerIds, @Param("telephone") String telephone);

	List<CustomerDriverRelation> selectByCustomerIdsAndDriverIds(List<CustomerDriverRelation> relations);

	int insertCustomerDriverRelations(List<CustomerDriverRelation> customerDriverRelations);

	int deleteCustomerDriverRelationByDriverIds(List<Long> driverIds);

	int deleteRepeatDriverByDrivingLicenseAndId(String drivingLicense, Long driverId);

	int updateNewDriverIdByOldDriverId(Long newDriverId, Long oldDriverId);
}
