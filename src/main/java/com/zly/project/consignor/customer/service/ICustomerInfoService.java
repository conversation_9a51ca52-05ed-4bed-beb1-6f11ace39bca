package com.zly.project.consignor.customer.service;

import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.project.consignor.customer.domain.CustomerInfo;
import com.zly.project.consignor.customer.domain.CustomerInfoEx;
import com.zly.project.consignor.customer.domain.CustomerVerify;
import com.zly.project.consignor.customer.domain.CustomerVerifyEx;
import com.zly.project.consignor.customer.domain.req.CustomerAdminConfigReq;
import com.zly.project.consignor.customer.domain.req.CustomerInfoReq;
import com.zly.project.consignor.customer.domain.res.CustomerAdminConfigRes;
import com.zly.project.consignor.customer.domain.res.OptionCustomerRes;
import com.zly.project.miniprogram.domain.CustomerInfoDetail;
import com.zly.project.tenant.domain.TenantUser;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 客户信息Service接口
 *
 * <AUTHOR>
 * @date 2021-10-19
 */
public interface ICustomerInfoService {
	/**
	 * 查询客户信息
	 *
	 * @param id
	 * 		客户信息主键
	 * @return 客户信息
	 */
	CustomerInfo selectCustomerInfoById(Long id);

	/**
	 * 校验客户信息
	 *
	 * @param customerInfo
	 * 		客户信息
	 * @return 客户信息
	 */
	CustomerInfo validateCustomerInfo(CustomerInfo customerInfo);

	/**
	 * 查询客户信息列表
	 *
	 * @param customerInfo
	 * 		客户信息
	 * @return 客户信息集合
	 */
	List<CustomerInfo> selectCustomerInfoList(CustomerInfo customerInfo);

	/**
	 * 查询客户信息列表(根据审核状态及时间排序)
	 *
	 * @param customerInfo
	 * @return
	 */
	List<CustomerInfoEx> getCustomerInfoList(CustomerInfoEx customerInfo);

	/**
	 * 启用/禁用
	 *
	 * @param id
	 * @return
	 */
	AjaxResult startOrStop(Long id);

	/**
	 * 新增客户信息
	 *
	 * @param customerInfo
	 * 		客户信息
	 * @return 结果
	 */
	int insertCustomerInfo(CustomerInfo customerInfo);

	/**
	 * 修改客户信息
	 *
	 * @param customerInfo
	 * 		客户信息
	 * @return 结果
	 */
	int updateCustomerInfo(CustomerInfo customerInfo);

	/**
	 * 批量删除客户信息
	 *
	 * @param ids
	 * 		需要删除的客户信息主键集合
	 * @return 结果
	 */
	int deleteCustomerInfoByIds(Long[] ids);

	/**
	 * 删除客户信息信息
	 *
	 * @param id
	 * 		客户信息主键
	 * @return 结果
	 */
	int deleteCustomerInfoById(Long id);

	// /**
	// * 查询客户信息列表
	// *
	// * @param customerInfo
	// * 客户信息
	// * @return 客户信息集合
	// */
	// public List<CustomerInfoRes> customerInfoList(CustomerInfo customerInfo);

	/**
	 * 新增客户信息
	 *
	 * @param req
	 * 		客户信息
	 * @return 结果
	 */
	AjaxResult addCustomerInfo(CustomerInfoReq req);

	/**
	 * 新增托运人信息
	 *
	 * @param req
	 * 		托运人信息
	 * @param initPassword
	 * 		自定义该托运人管理员用户的初始密码，如果传null，将指定默认的初始密码
	 * @return 结果
	 */
	AjaxResult addCustomerInfo(CustomerInfoReq req, String initPassword);

	/**
	 * 查询客户详情
	 *
	 * @param id
	 * 		客户信息主键
	 * @return 客户信息
	 */
	AjaxResult getCustomerById(Long id);

	/**
	 * 编辑客户信息
	 *
	 * @param req
	 * 		客户信息
	 * @return 结果
	 */
	AjaxResult editCustomerInfo(CustomerInfoReq req);

	/**
	 * 将到期合同中客户的合同状态变更为'已到期'
	 *
	 * @return 结果
	 */
	AjaxResult customerContractStateExpired();

	/**
	 * 新增货主信息验证
	 *
	 * @param customerVerifyEx
	 * 		货主信息验证
	 * @return
	 */
	CustomerVerify getOrInsertCustomerVerify(CustomerVerifyEx customerVerifyEx);

	/**
	 * 审核
	 *
	 * @param req
	 * @return
	 */
	AjaxResult approveCustomer(CustomerInfoReq req);

	/**
	 * 配置
	 *
	 * @param req
	 * @return
	 */
	AjaxResult adminConfig(CustomerAdminConfigReq req);

	/**
	 * 管理员信息
	 *
	 * @param id
	 * @return
	 */
	CustomerAdminConfigRes adminInfo(Long id);

	/**
	 * 企业认证货主端
	 *
	 * @param customerInfoReq
	 * @return
	 */
	AjaxResult insertIdentity(CustomerInfoReq customerInfoReq);

	/**
	 * 企业认证货主端 后 插入的一套数据逻辑
	 *
	 * @param customerInfoReq
	 * 		托运人信息
	 * @param initPassword
	 * 		自定义的初始密码，如果传null，将指定默认的初始密码
	 * @return
	 */
	AjaxResult insertTenantUserInfo(CustomerInfoReq customerInfoReq, String initPassword);

	AjaxResult insertFinanceUserInfo(CustomerInfoReq customerInfoReq, String initPassword);

	AjaxResult insertTenantUserInfoGroup(CustomerInfoReq customerInfoReq, Long tenantUserId);

	AjaxResult insertTenantUserInfoRole(CustomerInfoReq customerInfoReq, Long tenantUserId, Integer clientType);

	/**
	 * 审核 企业货主端信息
	 *
	 * @param customerInfoReq
	 * @return
	 */
	AjaxResult approveIdentity(CustomerInfoReq customerInfoReq);

	/**
	 * 获取货主端 当前的审核信息 也就是详情信息
	 *
	 * @return
	 */
	AjaxResult customerInfoDetail();

	/**
	 * 货主PC端 企业管理详情
	 *
	 * @return
	 */
	AjaxResult selectCustomerInfoPCById();

	/**
	 * 货主PC端 修改企业管理
	 *
	 * @return
	 */
	AjaxResult editCustomerInfoSlave(CustomerInfoDetail customerInfoDetail);

	/**
	 * 集团版托运人详情
	 *
	 * @return
	 */
	AjaxResult findById(Long id);

	/**
	 * 集团版托运人注册详情
	 *
	 * @return
	 */
	AjaxResult enrollFindById(Long id);

	/**
	 * 集团版托运人列表
	 *
	 * @return
	 */
	List<CustomerInfoEx> getGroupCustomerInfoList(CustomerInfoEx customerInfo);

	/**
	 * 集团版托运人启用禁用
	 *
	 * @return
	 */
	AjaxResult setCustomerInfoState(CustomerInfo info);

	/**
	 * 批量查询客户列表信息
	 *
	 * @param customerIds
	 * @return
	 */
	List<CustomerInfo> selectCustomerList(List<Long> customerIds);

	CommonResult<String> findNameById(String id);

	TenantUser getCustomerUser(CustomerVerifyEx customerVerifyEx);

	List<CustomerInfo> selectCustomerInfoListByFreightForwarderId(CustomerInfo customerInfo);

	List<CustomerInfoEx> getSocialCodeCustomerInfoList();

	List<OptionCustomerRes> optionCustomerList(String customerName, Long freightForwarderId);

	List<CustomerInfo> selectWaybillCustomerInfoListByFreightForwarderId(CustomerInfo customerInfo);

	List<CustomerInfo> selectCustomerInfoDetail(CustomerInfo customerInfo);

	// 产品中心注册托运人接口
	AjaxResult productAddCustomerInfo(CustomerInfoReq req);

	// 集团业务端审核托运人
	AjaxResult approveIdentityByGroup(CustomerInfoReq customerInfoReq);

	List<CustomerInfoEx> getReviewedGroupCustomerInfoList(CustomerInfoEx customerInfo, HttpServletRequest request);

	AjaxResult addCustomerInfoByForwarder(CustomerInfoReq customerInfoReq);

	List<CustomerInfo> selectTaxCustomerInfoList(CustomerInfo customerInfo);

	AjaxResult getCustomerBankCard(Long id);

	AjaxResult getCustomerAccount(Long id);
}
