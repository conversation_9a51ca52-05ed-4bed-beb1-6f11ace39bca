package com.zly.project.consignor.customer.domain.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.project.common.domain.CustomerAttachmentInfo;
import com.zly.project.common.domain.FileModel;
import com.zly.project.customerForwarderContract.domain.CustomerForwarderContractAttachmentInfo;
import com.zly.project.customerForwarderContract.domain.req.CustomerForwarderContractReq;
import com.zly.project.freightforwarder.domain.FreightForwarderInfoEx;
import com.zly.project.settlement.domain.CustomerAccount;
import com.zly.project.settlement.domain.CustomerBankCard;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 客户信息对象 customer_info
 *
 * <AUTHOR>
 * @date 2021-10-19
 */
@Data
public class CustomerInfoRes {

	/** id */
	private Long id;

	/** 客户名称 */
	private String customerName;

	/** 合同状态（1：已签约，2：已到期） */
	private Integer contractState;

	/** 联系人 */
	private String contact;

	/** 联系电话 */
	private String contactPhone;

	/** 客户收款编号 */
	private String accountNo;

	/** 银行联行号 */
	private String bankCode;

	/** 银行名称 */
	private String bankName;

	/** 统一信用代码 */
	private String creditCode;

	/** 法人 */
	private String legal;

	/** 法人身份证 */
	private String legalIdNo;

	/** 客户地址 */
	private String address;

	/** 备注 */
	private String remark;

	/** 发票抬头 */
	private String billHead;

	/** 纳税人识别号 */
	private String taxNo;

	/** 单位地址 */
	private String unitAddress;

	/** 电话号码 */
	private String telephone;

	/** 开户银行 */
	private String bank;

	/** 银行账户 */
	private String bankAccount;

	/** 创建时间 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	/** 客户id */
	private Long customerId;

	private List<CustomerInfoGradeRes> customerInfoGradeResList;

	/** 附件信息 */
	private List<FileModel> attachment;

	private Map<String, List<CustomerAttachmentInfo>> protocolAttachment;

	private List<FreightForwarderInfoEx> freightForwarderInfos;

	private String scopeSum;

	private List<Long> services;

	@Excel(name = "省")
	@ApiModelProperty("省")
	private String province;
	/** 市区编码 */
	@Excel(name = "市")
	@ApiModelProperty("市")
	private String city;
	/** 省编码 */
	@Excel(name = "省编码")
	@ApiModelProperty("省编码")
	private String provinceCode;

	/** 市区编码 */
	@Excel(name = "市编码")
	@ApiModelProperty("市编码")
	private String cityCode;

	/** 区编码 */
	@Excel(name = "区编码")
	@ApiModelProperty("区编码")
	private String areaCode;

	/** 区 */
	@Excel(name = "区")
	@ApiModelProperty("区")
	private String area;

	@ApiModelProperty("信用等级")
	private BigDecimal creditRating;

	@ApiModelProperty("委托合同")
	List<CustomerForwarderContractReq> customerForwarderContractList;

	// 委托合同附件信息
	@ApiModelProperty(value = "委托合同附件信息")
	private List<CustomerForwarderContractAttachmentInfo> customerForwarderContractAttachmentInfoList;

	//平台账户信息
	private List<CustomerAccount> customerAccount;
	//对公账户信息
	private List<CustomerBankCard> customerBankCard;

	public void setCustomerInfoGradeResList(List<CustomerInfoGradeRes> customerInfoGradeResList) {
		this.customerInfoGradeResList = customerInfoGradeResList;
	}
}
