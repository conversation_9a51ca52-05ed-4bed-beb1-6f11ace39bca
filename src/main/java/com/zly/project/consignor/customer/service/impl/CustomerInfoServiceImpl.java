package com.zly.project.consignor.customer.service.impl;

import cn.hutool.json.JSONUtil;
import com.zly.common.constant.*;
import com.zly.common.enums.ClientType;
import com.zly.common.enums.CustomerServiceEnum;
import com.zly.common.exception.ServiceException;
import com.zly.common.utils.CodeUtil;
import com.zly.common.utils.CommonUtil;
import com.zly.common.utils.DateUtils;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.StringUtils;
import com.zly.common.utils.TextUtil;
import com.zly.common.utils.ValidateUtils;
import com.zly.common.utils.VerifyPwUtils;
import com.zly.common.utils.bean.BeanUtils;
import com.zly.common.utils.sign.VxUtils;
import com.zly.framework.config.CustomConfig;
import com.zly.framework.redis.RedisCache;
import com.zly.framework.security.LoginUser;
import com.zly.framework.security.service.TokenService;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.project.applets.evaluate.domain.CustomerAppraise;
import com.zly.project.applets.evaluate.mapper.CustomerAppraiseMapper;
import com.zly.project.backgroundOperation.service.BusinessModelService;
import com.zly.project.backgroundOperation.service.OnlineSynchronousDataService;
import com.zly.project.carrier.payee.domain.PayBank;
import com.zly.project.carrier.payee.mapper.PayBankMapper;
import com.zly.project.carrier.payee.service.IPtmCertificateCheckService;
import com.zly.project.common.domain.CustomerAttachmentInfo;
import com.zly.project.common.domain.FileModel;
import com.zly.project.common.domain.FreightForwarderAttachmentInfo;
import com.zly.project.common.service.ICustomerAttachmentInfoService;
import com.zly.project.common.service.JuheService;
import com.zly.project.consignor.contract.domain.CustomerContract;
import com.zly.project.consignor.contract.mapper.CustomerContractMapperEx;
import com.zly.project.consignor.customer.domain.*;
import com.zly.project.consignor.customer.domain.req.CustomerAdminConfigReq;
import com.zly.project.consignor.customer.domain.req.CustomerInfoReq;
import com.zly.project.consignor.customer.domain.res.CustomerAdminConfigRes;
import com.zly.project.consignor.customer.domain.res.CustomerInfoGradeRes;
import com.zly.project.consignor.customer.domain.res.CustomerInfoRes;
import com.zly.project.consignor.customer.domain.res.OptionCustomerRes;
import com.zly.project.consignor.customer.mapper.*;
import com.zly.project.consignor.customer.service.ICustomerInfoService;
import com.zly.project.contract.service.impl.FrameworkContractServiceImpl;
import com.zly.project.customerForwarderContract.domain.CustomerForwarderContract;
import com.zly.project.customerForwarderContract.domain.CustomerForwarderContractAttachmentInfo;
import com.zly.project.customerForwarderContract.domain.req.CustomerForwarderContractReq;
import com.zly.project.customerForwarderContract.mapper.CustomerForwarderContractAttachmentInfoMapper;
import com.zly.project.customerForwarderContract.mapper.CustomerForwarderContractMapper;
import com.zly.project.finance.statement.service.impl.FinanceStatementBillServiceImpl;
import com.zly.project.financeOperation.domain.*;
import com.zly.project.financeOperation.domain.req.FinanceUserReq;
import com.zly.project.financeOperation.mapper.*;
import com.zly.project.freightforwarder.domain.FreightForwarderInfo;
import com.zly.project.freightforwarder.domain.FreightForwarderInfoEx;
import com.zly.project.freightforwarder.mapper.FreightForwarderInfoMapper;
import com.zly.project.group.domain.PresetRoles;
import com.zly.project.group.domain.PresetRolesMenu;
import com.zly.project.group.service.IPresetRolesMenuService;
import com.zly.project.group.service.IPresetRolesService;
import com.zly.project.group.service.ShipCustomerInfoService;
import com.zly.project.group.service.impl.GroupFrameworkContractServiceImpl;
import com.zly.project.miniprogram.domain.CustomerInfoDetail;
import com.zly.project.miniprogram.domain.VxService;
import com.zly.project.projectGroup.service.ProjectGroupService;
import com.zly.project.projectGroup.service.ProjectGroupUserRelationService;
import com.zly.project.service.customerservice.domain.CustomerServiceInfo;
import com.zly.project.service.customerservice.mapper.CustomerServiceInfoMapper;
import com.zly.project.service.customerservice.mapper.CustomerServiceInfoMapperEx;
import com.zly.project.service.servicehome.domain.SystemService;
import com.zly.project.settlement.domain.CustomerAccount;
import com.zly.project.settlement.domain.CustomerBankCard;
import com.zly.project.settlement.domain.CustomerBankFlow;
import com.zly.project.settlement.domain.request.ali.AccOpenReq;
import com.zly.project.settlement.domain.request.ali.FixcardCreateReq;
import com.zly.project.settlement.mapper.CustomerAccountMapper;
import com.zly.project.settlement.mapper.CustomerBankCardMapper;
import com.zly.project.settlement.mapper.CustomerBankFlowMapperEx;
import com.zly.project.settlement.service.ICustomerAccountService;
import com.zly.project.settlement.service.ali.AliBankService;
import com.zly.project.settlement.service.ali.ShipAliBankService;
import com.zly.project.settlement.service.huaxia.HuaXiaService;
import com.zly.project.settlement.service.huaxia.ShipHuaXiaService;
import com.zly.project.system.domain.vo.CustomerVo;
import com.zly.project.system.service.impl.SysClientLogService;
import com.zly.project.tenant.domain.TenantDept;
import com.zly.project.tenant.domain.TenantMenu;
import com.zly.project.tenant.domain.TenantPost;
import com.zly.project.tenant.domain.TenantRole;
import com.zly.project.tenant.domain.TenantRoleDept;
import com.zly.project.tenant.domain.TenantRoleMenu;
import com.zly.project.tenant.domain.TenantUser;
import com.zly.project.tenant.domain.TenantUserEx;
import com.zly.project.tenant.domain.TenantUserPost;
import com.zly.project.tenant.domain.TenantUserRole;
import com.zly.project.tenant.mapper.TenantDeptMapper;
import com.zly.project.tenant.mapper.TenantMenuMapper;
import com.zly.project.tenant.mapper.TenantPostMapper;
import com.zly.project.tenant.mapper.TenantRoleDeptMapper;
import com.zly.project.tenant.mapper.TenantRoleMapper;
import com.zly.project.tenant.mapper.TenantRoleMenuMapper;
import com.zly.project.tenant.mapper.TenantUserMapper;
import com.zly.project.tenant.mapper.TenantUserMapperEx;
import com.zly.project.tenant.mapper.TenantUserPostMapper;
import com.zly.project.tenant.mapper.TenantUserRoleMapper;
import com.zly.project.transport.waybill.domain.FreightForwarderCustomerRelation;
import com.zly.project.transport.waybill.service.IFreightForwarderCustomerRelationService;
import com.zly.project.transport.waybill.service.IWaybillService;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.zly.common.constant.BusinessConstants.APPROVE_STATEAPING;
import static com.zly.common.constant.BusinessConstants.CUSTOMER_CREATE_BY_OUT;

/**
 * 客户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-10-19
 */
@Slf4j
@Service
public class CustomerInfoServiceImpl implements ICustomerInfoService {

	@Resource
	private CustomerInfoMapper customerInfoMapper;
	@Resource
	private CustomerInfoSlaveMapper customerInfoSlaveMapper;
	@Resource
	private CustomerInfoMapperEx customerInfoMapperEx;
	@Resource
	private CustomerApproveHistoryMapperEx customerApproveHistoryMapperEx;
	@Resource
	private CustomerApproveHistoryMapper customerApproveHistoryMapper;
	@Resource
	private CustomerVerifyMapper customerVerifyMapper;
	@Resource
	private CustomerBankFlowMapperEx customerBankFlowMapperEx;
	@Resource
	private CustomerContractMapperEx customerContractMapperEx;
	@Resource
	private IWaybillService waybillService;
	@Resource
	private RedisCache redisCache;
	@Resource
	private TenantUserMapper tenantUserMapper;
	@Resource
	private FinanceUserMapper financeUserMapper;
	@Resource
	private TokenService tokenService;
	@Resource
	private TenantDeptMapper tenantDeptMapper;
	@Resource
	private TenantPostMapper tenantPostMapper;
	@Resource
	private TenantRoleMapper tenantRoleMapper;
	@Resource
	private TenantRoleDeptMapper tenantRoleDeptMapper;
	@Resource
	private TenantRoleMenuMapper tenantRoleMenuMapper;
	@Resource
	private TenantMenuMapper tenantMenuMapper;
	@Resource
	private TenantUserMapperEx tenantUserMapperEx;
	@Resource
	private TenantUserRoleMapper tenantUserRoleMapper;
	@Resource
	private TenantUserPostMapper tenantUserPostMapper;
	@Resource
	private CustomerServiceInfoMapper customerServiceInfoMapper;
	@Resource
	private CustomerServiceInfoMapperEx customerServiceInfoMapperEx;
	@Resource
	private FreightForwarderInfoMapper freightForwarderInfoMapper;
	@Resource
	private IFreightForwarderCustomerRelationService forwarderCustomerRelationService;
	@Resource
	private FinanceStatementBillServiceImpl financeStatementBillService;
	@Resource
	private ICustomerAttachmentInfoService customerAttachmentInfoService;
	@Resource
	private IPresetRolesService iPresetRolesService;
	@Resource
	private IPresetRolesMenuService iPresetRolesMenuService;
	@Resource
	private JuheService juheService;
	@Resource
	private FinanceRoleMapper financeRoleMapper;
	@Resource
	private FinanceUserRoleMapper financeUserRoleMapper;
	@Resource
	private FinanceMenuMapper financeMenuMapper;
	@Resource
	private FinanceRoleMenuMapper financeRoleMenuMapper;
	@Resource
	private ProjectGroupService projectGroupService;
	@Resource
	private ProjectGroupUserRelationService projectGroupUserRelationService;
	@Resource
	private FinanceModelMenuMapper financeModelMenuMapper;
	@Resource
	private FrameworkContractServiceImpl frameworkContractService;
	@Resource
	private CustomerAccountMapper customerAccountMapper;
	@Resource
	private CustomConfig customConfig;
	@Resource
	private FrameworkContractServiceImpl contractService;
	@Resource
	private BusinessModelService businessModelService;
	@Resource
	private GroupFrameworkContractServiceImpl groupFrameworkContractService;
	@Resource
	private ShipCustomerInfoService shipCustomerInfoService;
	@Resource
	private IPtmCertificateCheckService iPtmCertificateCheckService;
	@Resource
	private SysClientLogService sysClientLogService;
	@Resource
	private CustomerAppraiseMapper customerAppraiseMapper;
	@Resource
	private CustomerForwarderContractMapper customerForwarderContractMapper;
	@Resource
	private CustomerForwarderContractAttachmentInfoMapper customerForwarderContractAttachmentInfoMapper;
	@Autowired
	private AliBankService aliBankService;
	@Autowired
	private ShipAliBankService shipAliBankService;
	@Autowired
	private ShipHuaXiaService shipHuaXiaService;

	@Value("${platform.payment.channel}")
	private String paymentChannel;// 平台支付通道
	@Resource
	private CustomerBankCardMapper customerBankCardMapper;
	@Autowired
	private PayBankMapper payBankMapper;

	@Resource
	private ICustomerAccountService customerAccountService;

	/**
	 * 查询客户信息(会判断基本状态，基本有效性)
	 *
	 * @param id
	 * 		客户信息主键
	 * @return 客户信息
	 */
	@Override
	public CustomerInfo selectCustomerInfoById(Long id) {
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(id);
		if (null == customerInfo) {
			throw new RuntimeException("托运人企业信息不存在");
		}
		if (!customerInfo.getApproveState().equals(BusinessConstants.APPROVE_STATEAP) && !customerInfo.getApproveState().equals(BusinessConstants.APPROVE_OPEN)) {
			throw new RuntimeException("托运人审核未完成，请等待审核完成后再操作");
		}
		if (BusinessConstants.STATE_DELETE == customerInfo.getState()) {
			log.info("托运人企业：{}，已被删除", customerInfo.getCustomerName());
			throw new ServiceException("托运人企业 " + customerInfo.getCustomerName() + " 已被删除，请确认");
		} else if (BusinessConstants.STATE_DISABLE == customerInfo.getState()) {
			log.info("托运人企业：{}，已停用", customerInfo.getCustomerName());
			throw new ServiceException("托运人企业 " + customerInfo.getCustomerName() + " 已停用，请确认");
		} else if (BusinessConstants.STATE_ENABLE != customerInfo.getState()) {
			log.info("托运人企业：{}，已失效", customerInfo.getCustomerName());
			throw new ServiceException("托运人企业 " + customerInfo.getCustomerName() + " 已失效，请确认");
		}
		return customerInfo;
	}

	/**
	 * 校验客户信息(会判断基本状态，基本有效性)
	 *
	 * @param customerInfo
	 * 		客户信息
	 * @return 客户信息
	 */
	@Override
	public CustomerInfo validateCustomerInfo(CustomerInfo customerInfo) {
		if (null == customerInfo) {
			throw new RuntimeException("托运人企业信息不存在");
		}
		if (!customerInfo.getApproveState().equals(BusinessConstants.APPROVE_STATEAP) && !customerInfo.getApproveState().equals(BusinessConstants.APPROVE_OPEN)) {
			throw new RuntimeException("托运人审核未完成，请等待审核完成后再操作");
		}
		if (BusinessConstants.STATE_DELETE == customerInfo.getState()) {
			log.info("托运人企业：{}，已被删除", customerInfo.getCustomerName());
			throw new ServiceException("托运人企业 " + customerInfo.getCustomerName() + " 已被删除，请确认");
		} else if (BusinessConstants.STATE_DISABLE == customerInfo.getState()) {
			log.info("托运人企业：{}，已停用", customerInfo.getCustomerName());
			throw new ServiceException("托运人企业 " + customerInfo.getCustomerName() + " 已停用，请确认");
		} else if (BusinessConstants.STATE_ENABLE != customerInfo.getState()) {
			log.info("托运人企业：{}，已失效", customerInfo.getCustomerName());
			throw new ServiceException("托运人企业 " + customerInfo.getCustomerName() + " 已失效，请确认");
		}
		return customerInfo;
	}

	/**
	 * 查询客户信息列表
	 *
	 * @param customerInfo
	 * 		客户信息
	 * @return 客户信息
	 */
	@Override
	public List<CustomerInfo> selectCustomerInfoList(CustomerInfo customerInfo) {
		return customerInfoMapper.selectCustomerInfoList(customerInfo);
	}

	/**
	 * 查询客户信息列表(根据审核状态及时间排序)
	 *
	 * @param customerInfo
	 * @return
	 */
	@Override
	public List<CustomerInfoEx> getCustomerInfoList(CustomerInfoEx customerInfo) {
		List<CustomerInfoEx> customerInfoList = customerInfoMapperEx.getCustomerInfoList(customerInfo);
		for (CustomerInfoEx customerInfoEx : customerInfoList) {
			List<CustomerBankCard> customerBankCards = customerBankCardMapper.selectCustomerBankCardListByCustomerId(customerInfoEx.getId());
			customerInfoEx.setCustomerBankCardCount(customerBankCards.size());
			int x = 0;
			for (CustomerBankCard customerBankCard : customerBankCards) {
				Integer state = customerBankCard.getState();
				//判断是否存在审核中对公账户
				if (1 == state) {
					x = 1;
					break;
				}
			}
			customerInfoEx.setCustomerBankCardState(x);
			String customerAccount = customerInfoEx.getCustomerAccount();
			if ("0".equals(customerAccount)) {
				List<CustomerAccount> customerAccounts = customerAccountMapper.selectCustomerAccountListByCreditCode(customerInfoEx.getCreditCode());
				if (customerAccounts.isEmpty()) {
					customerInfoEx.setCustomerAccount("——");
					customerInfoEx.setCustomerAccountState(-1);
				} else {
					customerInfoEx.setCustomerAccount(customerAccounts.get(0).getAccountNo());
					customerInfoEx.setCustomerAccountState(1);
				}
			} else {
				customerInfoEx.setCustomerAccountState(1);
			}

		}
		return customerInfoList;
	}

	/**
	 * 启用/禁用
	 *
	 * @param id
	 * @return
	 */
	@Override
	public AjaxResult startOrStop(Long id) {
		if (StringUtils.isNull(id)) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "客户标识为空");
		}

		// 查询项目合约信息
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(id);
		if (StringUtils.isNull(customerInfo)) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "没有找到对应客户信息");
		}
		Integer state = customerInfo.getState() == 0 ? 1 : 0;// 状态 0：启用 1；禁用 = > 将启用设置为禁用 禁用设置为启用
		customerInfo.setState(state);
		// 启用/禁用项目合约
		int success = updateCustomerInfo(customerInfo);
		if (success == 0) {
			return AjaxResult.error("启用/禁用失败");
		}

		return AjaxResult.success(HttpStatus.SUCCESS, BusinessConstants.SUCCESS_DESC);
	}

	/**
	 * 新增客户信息
	 *
	 * @param customerInfo
	 * 		客户信息
	 * @return 结果
	 */
	@Override
	public int insertCustomerInfo(CustomerInfo customerInfo) {
		customerInfo.setCreateTime(DateUtils.getNowDate());
		return customerInfoMapper.insertCustomerInfo(customerInfo);
	}

	/**
	 * 修改客户信息
	 *
	 * @param customerInfo
	 * 		客户信息
	 * @return 结果
	 */
	@Override
	public int updateCustomerInfo(CustomerInfo customerInfo) {
		customerInfo.setUpdateTime(DateUtils.getNowDate());
		return customerInfoMapper.updateCustomerInfo(customerInfo);
	}

	/**
	 * 批量删除客户信息
	 *
	 * @param ids
	 * 		需要删除的客户信息主键
	 * @return 结果
	 */
	@Override
	public int deleteCustomerInfoByIds(Long[] ids) {
		return customerInfoMapper.deleteCustomerInfoByIds(ids);
	}

	/**
	 * 删除客户信息信息
	 *
	 * @param id
	 * 		客户信息主键
	 * @return 结果
	 */
	@Override
	public int deleteCustomerInfoById(Long id) {
		return customerInfoMapper.deleteCustomerInfoById(id);
	}

	@Override
	public AjaxResult addCustomerInfo(CustomerInfoReq req) {
		if (req.getContactPhone() != null && req.getContactPhone().length() > 11) {
			return AjaxResult.error("联系方式错误,请输入正确的联系方式");
		}

		// 随机生成12位大小写加数字密码
		return addCustomerInfo(req, OnlineSynchronousDataService.generateMixedCasePassword());
	}

	/**
	 * 新增托运人信息
	 *
	 * @param req
	 * 		托运人信息
	 * @param initPassword
	 * 		自定义该托运人管理员用户的初始密码，如果传null，将指定默认的初始密码
	 * @return
	 */
	@Override
	public AjaxResult addCustomerInfo(CustomerInfoReq req, String initPassword) {
		AjaxResult createResult = createCustomerInfo(req, initPassword);
		if (createResult.isNotSuccess()) {
			return createResult;
		}
		// 外部创建，到此结束
		if (CUSTOMER_CREATE_BY_OUT.equals(req.getResourceType())) {
			return createResult;
		}
		CustomerInfo customerInfo = (CustomerInfo) createResult.getData();
		//托运人新增成功后开户操作
		boolean openAccountSuccess = false;
		try {
			//开户信息请求体
			FixcardCreateReq request = new FixcardCreateReq();
			request.setBusinessType(0);
			request.setContact(customerInfo.getContact());
			request.setContactMobile(customerInfo.getContactPhone());
			request.setIdCode(customerInfo.getCreditCode());
			request.setName(customerInfo.getCustomerName());
			AjaxResult ajaxResult = customerAccountService.openCorporateAccount(request);
			openAccountSuccess = ajaxResult.isSuccess() ? true : false;
		} catch (Exception e) {

		}
		// 删除redis缓存
		redisCache.deleteObject(CacheConstants.NETWORK_CUSTOMER_INFO + customerInfo.getCreditCode());
		return openAccountSuccess ? AjaxResult.success("注册成功") : AjaxResult.success("托运人注册成功，但平台账户开户失败。请前往【托运人详情】页面，重新进行开户操作");
	}

	/**
	 * 创建托运人基础信息
	 *
	 * @param req
	 * @param initPassword
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult createCustomerInfo(CustomerInfoReq req, String initPassword) {
		// 校验必填数据
		AjaxResult result = null;
		ClientType clientType = CUSTOMER_CREATE_BY_OUT.equals(req.getResourceType()) ? ClientType.PLATFORM : SecurityUtils.getClientType();
		String nickName = CUSTOMER_CREATE_BY_OUT.equals(req.getResourceType()) ? "外部用户" : SecurityUtils.getNickname();
		if (ClientType.PLATFORM.equals(clientType) || ClientType.AGENT.equals(clientType)) {
			result = verify(req);
		} else if (ClientType.GROUP.equals(clientType)) {
			result = groupVerify(req);
		}
		if (null == result || HttpStatus.BAD_REQUEST == result.getCode() || !result.isSuccess()) {
			return result;
		}
		Date curDate = DateUtils.getNowDate();
		CustomerInfo customerInfo = new CustomerInfo();
		BeanUtils.copyProperties(req, customerInfo);
		// 保存客户信息
		Long customerId = StringUtils.getOrDefault(req.getId(), TextUtil.generateId());
		Long freightForwarderId = req.getFreightForwarderId();
		customerInfo.setId(customerId);
		customerInfo.setCustomerCode(CodeUtil.generateCustomerCode());// 客户编号
		customerInfo.setCreateBy(nickName);// 创建人
		customerInfo.setCreateTime(curDate);// 创建时间
		// 托运人不为网货人时，网货id字段为0
		customerInfo.setFreightForwarderId(0L);

		if (CUSTOMER_CREATE_BY_OUT.equals(req.getResourceType())) {
			customerInfo.setResourceType(CUSTOMER_CREATE_BY_OUT);
			customerInfo.setApproveState(APPROVE_STATEAPING);
			customerInfo.setGroupType(BusinessConstants.CUSTOMER_OUT_GROUP);
		} else if (ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
			freightForwarderId = SecurityUtils.getLoginUser().getUser().getFreightForwarderId();
			customerInfo.setResourceType(BusinessConstants.CUSTOMER_CREATE_BY_PLATFROM);
			customerInfo.setApproveState(BusinessConstants.APPROVE_STATEAP);
			customerInfo.setGroupType(BusinessConstants.CUSTOMER_OUT_GROUP);
		} else if (ClientType.GROUP.equals(SecurityUtils.getClientType())) {
			customerInfo.setResourceType(BusinessConstants.CUSTOMER_CREATE_BY_GROUP);
			customerInfo.setApproveState(BusinessConstants.APPROVE_STATEAP);
			customerInfo.setGroupType(BusinessConstants.CUSTOMER_IN_GROUP);
		} else if (ClientType.AGENT.equals(SecurityUtils.getClientType())) {
			customerInfo.setResourceType(BusinessConstants.CUSTOMER_CREATE_BY_AGENT);
			customerInfo.setApproveState(BusinessConstants.APPROVE_STATEAP);
			customerInfo.setGroupType(BusinessConstants.CUSTOMER_IN_GROUP);
		}

		CustomerInfo customerInfoQuery = new CustomerInfo();
		customerInfoQuery.setCreditCode(req.getCreditCode());
		customerInfoQuery.setCustomerName(req.getCustomerName());
		List<CustomerInfo> list = customerInfoMapper.selectCustomerInfoListByCreditCodeOrCustomerName(customerInfoQuery);

		if (StringUtils.isEmpty(list)) {
			// 只有客户信息表没有数据的时候才新增
			customerInfoMapper.insertCustomerInfo(customerInfo);
			if (!ClientType.GROUP.equals(clientType)) {
				FreightForwarderCustomerRelation forwarderCustomerRelation = new FreightForwarderCustomerRelation();
				forwarderCustomerRelation.setFreightForwarderId(freightForwarderId);
				forwarderCustomerRelation.setCustomerId(customerId);
				forwarderCustomerRelation.setCreateBy(nickName);
				forwarderCustomerRelation.setCreateTime(DateUtils.getNowDate());
				forwarderCustomerRelationService.insertFreightForwarderCustomerRelation(forwarderCustomerRelation);
			}

			CustomerInfoReq customerInfoReq = new CustomerInfoReq();
			BeanUtils.copyProperties(customerInfo, customerInfoReq);
			// 初始化一个管理员账号
			this.insertTenantUserInfo(customerInfoReq, initPassword);
			// 初始化金融服务管理员账号
			this.insertFinanceUserInfo(customerInfoReq, initPassword);

		} else {
			// 客户信息表存在数据，允许继续添加，但只绑定关联关系,并且数据允许变更
			// 保存网络货运人关联关系
			if (ClientType.GROUP.equals(clientType)) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "托运人已存在，请勿重复添加");
			}
			Long customerIdExist = list.get(0).getId();
			// 客户信息表 社会统一信用代码相同的只可能存在一条数据所以直接取列表第一条，取出id
			// customerIdExist:数据库里存在的客户id
			// 查询关联关系表，如果已经有关联关系了，返回错误
			FreightForwarderCustomerRelation freightForwarderCustomerRelation = new FreightForwarderCustomerRelation();
			freightForwarderCustomerRelation.setCustomerId(customerIdExist);
			freightForwarderCustomerRelation.setFreightForwarderId(freightForwarderId);
			List<FreightForwarderCustomerRelation> freightForwarderCustomerRelations = forwarderCustomerRelationService.selectFreightForwarderCustomerRelationList(freightForwarderCustomerRelation);
			if (freightForwarderCustomerRelations.size() != 0) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "托运人已存在，请勿重复添加");
			}
			customerInfo.setId(customerIdExist);
			customerInfo.setApproveState(list.get(0).getApproveState());
			customerInfoMapper.updateCustomerInfo(customerInfo);

			FreightForwarderCustomerRelation forwarderCustomerRelation = new FreightForwarderCustomerRelation();
			forwarderCustomerRelation.setFreightForwarderId(freightForwarderId);
			forwarderCustomerRelation.setCustomerId(customerIdExist);
			forwarderCustomerRelation.setCreateBy(nickName);
			forwarderCustomerRelation.setCreateTime(DateUtils.getNowDate());
			forwarderCustomerRelationService.insertFreightForwarderCustomerRelation(forwarderCustomerRelation);

			// 先查出之前所有的附件
			List<CustomerAttachmentInfo> customerFiles = customerAttachmentInfoService.customerFileList(customerIdExist);
			List<CustomerAttachmentInfo> oldAgreementFiles = new ArrayList<>();
			for (CustomerAttachmentInfo customerFile : customerFiles) {
				if (customerFile.getFileName().equals("平台协议") && !customerFile.getFreightForwarderId().equals(SecurityUtils.getFreightForwarderId())) {
					oldAgreementFiles.add(customerFile);
				}
			}
			// 删除先前的附件信息
			customerAttachmentInfoService.deleteCustomerFiles(customerIdExist);
			for (CustomerAttachmentInfo oldAgreementFile : oldAgreementFiles) {
				customerAttachmentInfoService.insertCustomerAttachmentInfo(oldAgreementFile);
			}

			// 删除先前的服务信息
			customerServiceInfoMapper.deleteCustomerServiceInfoByCustomerId(customerIdExist);
		}

		// 保存附件信息
		List<CustomerAttachmentInfo> attachmentList = req.getAttachment();
		if (null != attachmentList && !attachmentList.isEmpty()) {
			for (CustomerAttachmentInfo customerFile : attachmentList) {
				customerFile.setId(TextUtil.getTimeSequenceID(5));
				customerFile.setRelationId(customerInfo.getId());// 客户附件关联客户id
				customerFile.setCreateBy(nickName);// 创建人
				customerFile.setCreateTime(curDate);// 创建时间
				customerFile.setUpdateBy(nickName);// 修改人
				customerFile.setUpdateTime(curDate);// 修改时间
				if (customerFile.getFileName().equals("平台协议")) {
					if (ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
						customerFile.setFreightForwarderId(SecurityUtils.getFreightForwarderId());
					} else if (ClientType.GROUP.equals(SecurityUtils.getClientType())) {
						customerFile.setFreightForwarderId(customerFile.getFreightForwarderId());
					}
				}
				customerAttachmentInfoService.insertCustomerAttachmentInfo(customerFile);
			}
		}

		// 开通增值服务
		List<Long> serviceIds = req.getServiceIds() == null ? new ArrayList<>() : req.getServiceIds();
		// 若参数中未有增值服务，基础服务为默认项
		if (serviceIds.isEmpty()) {
			serviceIds.add(Long.parseLong(CustomerServiceEnum.BASIS.getCode()));
		}
		// 如果选择了定位服务，则将车辆最新位置查询的服务作为定位服务项
		for (Long serviceId : serviceIds) {
			if (SystemService.LOCATE.equals(serviceId)) {
				if (!serviceIds.contains(SystemService.Locate.REALTIME.val())) {
					serviceIds.add(SystemService.Locate.REALTIME.val());
					break;
				}
			}
		}
		for (Long serviceId : serviceIds) {
			CustomerServiceInfo serviceInfo = new CustomerServiceInfo();
			Long customerServiceId = TextUtil.getTimeSequenceID(5);
			serviceInfo.setId(customerServiceId);
			serviceInfo.setCustomerId(customerInfo.getId());
			serviceInfo.setCustomerName(customerInfo.getCustomerName());
			serviceInfo.setOpenState(1);
			serviceInfo.setCustomerServiceId(serviceId);
			serviceInfo.setCreateBy(nickName);// 创建人
			serviceInfo.setCreateTime(curDate);// 创建时间
			customerServiceInfoMapper.insertCustomerServiceInfo(serviceInfo);
		}

		// 记录操作日志
		String actionName = "新增托运人(托运人id：" + customerInfo.getId() + "，托运人名称：" + customerInfo.getCustomerName() + ")";
		actionName += "，请求参数：" + JSONUtil.toJsonStr(req);
		sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_CUSTOMER_INFO, BusinessConstants.ACTION_TYPE_ADD, org.apache.commons.lang3.StringUtils.substring(actionName, 0, 5000),
				customerInfo.getCreditCode());
		return AjaxResult.success(customerInfo);
	}

	/**
	 * 查询托运人详情
	 */
	@Override
	public AjaxResult getCustomerById(Long customerId) {
		if (null == customerId || customerId == 0L) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "客户标识为空");
		}
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(customerId);
		if (null == customerInfo) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "没有找到对应的客户信息");
		}
		CustomerInfoRes res = new CustomerInfoRes();
		BeanUtils.copyProperties(customerInfo, res);
		// 根据id关联查询出未删除的附件信息
		List<CustomerAttachmentInfo> customerFiles = customerAttachmentInfoService.customerFileList(customerId);
		if (!customerFiles.isEmpty()) {
			customerFiles.removeIf(file -> "平台协议".equals(file.getFileName()));
		}

		// 1.单独查询平台协议
		CustomerAttachmentInfo customerAttachmentInfoQuery = new CustomerAttachmentInfo();
		customerAttachmentInfoQuery.setRelationId(customerInfo.getId());
		customerAttachmentInfoQuery.setFileName("平台协议");
		customerAttachmentInfoQuery.setFreightForwarderId(SecurityUtils.getFreightForwarderId());
		List<CustomerAttachmentInfo> customerAttachmentInfos = customerAttachmentInfoService.selectCustomerAttachmentInfoList(customerAttachmentInfoQuery);

		List<CustomerAttachmentInfo> allAttachments = new ArrayList<>();
		allAttachments.addAll(customerFiles);
		allAttachments.addAll(customerAttachmentInfos);

		res.setAttachment(allAttachments.stream().map(FileModel::of).collect(Collectors.toList()));

		// 查询委托合同
		CustomerForwarderContract customerForwarderContract = new CustomerForwarderContract();
		customerForwarderContract.setCustomerId(customerId);
		customerForwarderContract.setState(1);// 只展示生效的委托合同
		customerForwarderContract.setFreightForwarderId(SecurityUtils.getFreightForwarderId());
		List<CustomerForwarderContractReq> customerForwarderContractReqList = customerForwarderContractMapper.selectContractList(customerForwarderContract);
		List<CustomerForwarderContractAttachmentInfo> customerForwarderContractAttachmentInfoList = new ArrayList<>();
		// 查询合同附件
		for (CustomerForwarderContractReq customerForwarderContractReq : customerForwarderContractReqList) {
			CustomerForwarderContractAttachmentInfo customerForwarderContractAttachmentInfo = new CustomerForwarderContractAttachmentInfo();
			customerForwarderContractAttachmentInfo.setRelationId(customerForwarderContractReq.getId());
			List<CustomerForwarderContractAttachmentInfo> customerForwarderContractAttachmentInfos = customerForwarderContractAttachmentInfoMapper.selectCustomerForwarderContractAttachmentInfoList(
					customerForwarderContractAttachmentInfo);
			customerForwarderContractAttachmentInfoList.addAll(customerForwarderContractAttachmentInfos);
		}
		res.setCustomerForwarderContractAttachmentInfoList(customerForwarderContractAttachmentInfoList);

		List<CustomerInfoGradeRes> customerInfoGradeResList = customerInfoMapperEx.selectCustomerGrage(customerId);
		double _score = 0.0;
		double _sum = 0.0;
		for (CustomerInfoGradeRes gradeRes : customerInfoGradeResList) {
			_score += gradeRes.getGrade() * gradeRes.getGradeSum();
			_sum += gradeRes.getGradeSum();
		}

		double percentNumber = (5 + _score) / (1 + _sum);
		res.setScopeSum(new DecimalFormat("######0.00").format(percentNumber));
		res.setCustomerInfoGradeResList(customerInfoGradeResList);

		List<Long> serviceIds = customerServiceInfoMapperEx.getServiceIdByCustomerInfoId(customerId);
		res.setServices(serviceIds);

		// 查询托运人的信用评价等级
		List<CustomerAppraise> customerAppraises = customerAppraiseMapper.selectCreditRating(customerId);
		if (StringUtils.isNotEmpty(customerAppraises)) {
			// 过滤掉不为空的，剩下的数据都是评价该托运人的
			customerAppraises.removeIf(appraise -> appraise.getGradeJson() != null && !appraise.getGradeJson().isEmpty());
			if (StringUtils.isNotEmpty(customerAppraises)) {
				BigDecimal totalGrade = customerAppraises.stream().map(appraise -> appraise.getGrade() == null ? BigDecimal.ZERO : BigDecimal.valueOf(appraise.getGrade())) // Integer 转 BigDecimal
						.reduce(BigDecimal.ZERO, BigDecimal::add);

				BigDecimal averageGrade = customerAppraises.isEmpty() ? BigDecimal.ZERO : totalGrade.divide(BigDecimal.valueOf(customerAppraises.size()), 2, BigDecimal.ROUND_HALF_UP);

				res.setCreditRating(averageGrade);
			}
			res.setCreditRating(new BigDecimal("5"));
		} else {
			res.setCreditRating(new BigDecimal("5"));
		}
		//平台账户信息
		List<CustomerAccount> customerAccounts = customerAccountMapper.selectCustomerAccountListByCreditCode(customerInfo.getCreditCode());
		res.setCustomerAccount(customerAccounts);
		//对公账户信息
		List<CustomerBankCard> customerBankCards = customerBankCardMapper.selectCustomerBankCardListByCustomerId(customerId);
		//bug7342调用钟珂接口更新审核中账户状态
		if (customerBankCards != null && !customerBankCards.isEmpty()) {
			AjaxResult ajaxResult = huaXiaService.queryCustomerBankState(customerInfo.getCreditCode());
			List<Map<String, Object>> list = Collections.emptyList();
			if (ajaxResult.getCode() == 200 && ajaxResult.getData() != null) {
				list = (List<Map<String, Object>>) ajaxResult.getData();
			}
			for (CustomerBankCard customerBankCard1 : customerBankCards) {
				Integer state = customerBankCard1.getState();
				if (state == 1) {
					for (Map<String, Object> map : list) {
						String account = String.valueOf(map.get("settleAccount"));
						if (customerBankCard1.getBankCardNo().equals(account)) {
							String isOuath = String.valueOf(map.get("isOuath"));
							String signState = String.valueOf(map.get("signState"));
							System.out.println("isOuath:" + isOuath);
							System.out.println("signState:" + signState);
							if ("2".equals(signState)) {
								customerBankCard1.setState(0);
								customerBankCardMapper.updateCustomerBankCard(customerBankCard1);
							}
						}
					}
				}
				String bankCode = customerBankCard1.getBankCode();
				PayBank payBank = new PayBank();
				payBank.setBankCode(bankCode);
				List<PayBank> payBanks = payBankMapper.selectPayBankList(payBank);
				if (StringUtils.isNotEmpty(payBanks)) {
					customerBankCard1.setBankID(payBanks.get(0).getId());
				}
			}
		}
		res.setCustomerBankCard(customerBankCards);
		return AjaxResult.success(res);
	}

	/**
	 * 编辑客户信息
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public AjaxResult editCustomerInfo(CustomerInfoReq req) {
		if (null == req || null == req.getId()) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "数据对象为空");
		}
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(req.getId());
		if (null == customerInfo) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "没有找到对应的客户信息");
		}
		String oldKey = customerInfo.getCreditCode();
		// 校验必填数据
		AjaxResult result = null;
		if (ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
			result = verify(req);
		} else if (ClientType.GROUP.equals(SecurityUtils.getClientType())) {
			result = groupVerifyUpdate(req);
		}
		if (null == result) {
			return AjaxResult.error(HttpStatus.FORBIDDEN, "您没有权限操作该接口");
		}
		if (HttpStatus.BAD_REQUEST == result.getCode()) {
			return result;
		}
		String customerName = customerInfo.getCustomerName();
		Date curDate = DateUtils.getNowDate();
		BeanUtils.copyProperties(req, customerInfo);
		// 修改客户信息
		customerInfo.setUpdateBy(SecurityUtils.getNickname());// 修改人
		customerInfo.setUpdateTime(curDate);// 修改时间
		customerInfoMapper.updateCustomerInfo(customerInfo);

		if (ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
			// 先查出之前所有的附件
			List<CustomerAttachmentInfo> customerFiles = customerAttachmentInfoService.customerFileList(req.getId());
			List<CustomerAttachmentInfo> oldAgreementFiles = new ArrayList<>();
			for (CustomerAttachmentInfo customerFile : customerFiles) {
				if (customerFile.getFileName().equals("平台协议") && !customerFile.getFreightForwarderId().equals(SecurityUtils.getFreightForwarderId())) {
					oldAgreementFiles.add(customerFile);
				}
			}

			// 先删除先前的附件信息再保存
			customerAttachmentInfoService.deleteCustomerFiles(req.getId());

			List<CustomerAttachmentInfo> attachmentList = req.getAttachment();
			if (null != attachmentList && !attachmentList.isEmpty()) {
				for (CustomerAttachmentInfo customerFile : attachmentList) {
					customerFile.setId(TextUtil.getTimeSequenceID(5));
					customerFile.setRelationId(req.getId());// 关联id
					customerFile.setCreateBy(SecurityUtils.getNickname());// 创建人
					customerFile.setCreateTime(curDate);// 创建时间
					customerFile.setUpdateBy(SecurityUtils.getNickname());// 修改人
					customerFile.setUpdateTime(curDate);// 修改时间
					if (customerFile.getFileName().equals("平台协议")) {
						if (ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
							customerFile.setFreightForwarderId(SecurityUtils.getFreightForwarderId());
						} else if (ClientType.GROUP.equals(SecurityUtils.getClientType())) {
							customerFile.setFreightForwarderId(customerFile.getFreightForwarderId());
						}
					}
					customerAttachmentInfoService.insertCustomerAttachmentInfo(customerFile);
				}
			}
			for (CustomerAttachmentInfo oldAgreementFile : oldAgreementFiles) {
				customerAttachmentInfoService.insertCustomerAttachmentInfo(oldAgreementFile);
			}
		} else if (ClientType.GROUP.equals(SecurityUtils.getClientType())) {
			// 先删除先前的附件信息再保存
			customerAttachmentInfoService.deleteCustomerFiles(req.getId());

			List<CustomerAttachmentInfo> attachmentList = req.getAttachment();
			if (null != attachmentList && !attachmentList.isEmpty()) {
				for (CustomerAttachmentInfo customerFile : attachmentList) {
					customerFile.setId(TextUtil.getTimeSequenceID(5));
					customerFile.setRelationId(req.getId());// 关联id
					customerFile.setCreateBy(SecurityUtils.getNickname());// 创建人
					customerFile.setCreateTime(curDate);// 创建时间
					customerFile.setUpdateBy(SecurityUtils.getNickname());// 修改人
					customerFile.setUpdateTime(curDate);// 修改时间
					if (customerFile.getFileName().equals("平台协议")) {
						customerFile.setFreightForwarderId(customerFile.getFreightForwarderId());
					}
					customerAttachmentInfoService.insertCustomerAttachmentInfo(customerFile);
				}
			}
		}

		// 如果客户名称发生了修改，要同步下运单表,合同表,结算表 数据
		if (!customerName.equals(req.getCustomerName())) {
			// 运单表
			int c = waybillService.editCustomerInfoOfWaybillByCustomerId(req.getId(), req.getCustomerName());
			log.info("更新托运人信息，同步修改运单数 {} 条", c);

			// 合同表
			CustomerContract customerContract = new CustomerContract();
			customerContract.setCustomerId(req.getId());
			customerContract.setCustomerName(req.getCustomerName());
			customerContractMapperEx.editCustomerContract(customerContract);

			// 结算表
			CustomerBankFlow customerBankFlow = new CustomerBankFlow();
			customerBankFlow.setCustomerId(req.getId());
			customerBankFlow.setCustomerName(req.getCustomerName());
			customerBankFlowMapperEx.editCustomerBankFlow(customerBankFlow);
		}

		// 关联网络货运人,只增不减
		if (req.getFreightForwarderIds() != null) {
			List<Long> list = forwarderCustomerRelationService.selectForwarderIdsByCustomerId(req.getId());
			List<Long> diffrent5 = financeStatementBillService.getDiffrent5(req.getFreightForwarderIds(), list);

			FreightForwarderCustomerRelation relation = new FreightForwarderCustomerRelation();
			relation.setCustomerId(req.getId());
			relation.setCreateTime(new Date());
			relation.setUpdateTime(new Date());
			relation.setCreateBy(SecurityUtils.getNickname());
			relation.setUpdateBy(SecurityUtils.getNickname());

			for (Long aLong : diffrent5) {
				relation.setFreightForwarderId(aLong);
				forwarderCustomerRelationService.insertFreightForwarderCustomerRelation(relation);
			}
		}

		// 增值服务修改
		if (req.getServiceIds() != null) {
			// 先修改所有为失效
			CustomerServiceInfo serviceInfoSearch = new CustomerServiceInfo();
			serviceInfoSearch.setCustomerId(req.getId());
			serviceInfoSearch.setOpenState(0);
			customerServiceInfoMapperEx.updateCustomerServiceInfoOpenState(serviceInfoSearch);
			// 查询托运人所有服务
			List<CustomerServiceInfo> customerServiceInfos = customerServiceInfoMapper.selectCustomerServiceInfoList(serviceInfoSearch);
			for (Long serviceId : req.getServiceIds()) {
				boolean exist = false;
				for (CustomerServiceInfo serviceInfo : customerServiceInfos) {
					if (serviceId.equals(serviceInfo.getCustomerServiceId())) {
						exist = true;
						break;
					}
				}
				// 没有则进行新增
				if (!exist) {
					CustomerServiceInfo serviceInfoAdd = new CustomerServiceInfo();
					Long customerServiceId = TextUtil.getTimeSequenceID(5);
					serviceInfoAdd.setId(customerServiceId);
					serviceInfoAdd.setCustomerId(req.getId());
					serviceInfoAdd.setCustomerName(customerInfo.getCustomerName());
					serviceInfoAdd.setOpenState(1);
					serviceInfoAdd.setCustomerServiceId(serviceId);
					serviceInfoAdd.setUpdateBy(SecurityUtils.getNickname());// 创建人
					serviceInfoAdd.setUpdateTime(curDate);// 创建时间
					customerServiceInfoMapper.insertCustomerServiceInfo(serviceInfoAdd);
				} else {
					// 存在修改为开通
					serviceInfoSearch.setCustomerId(req.getId());
					serviceInfoSearch.setOpenState(1);
					serviceInfoSearch.setCustomerServiceId(serviceId);
					customerServiceInfoMapperEx.updateCustomerServiceInfoOpenState(serviceInfoSearch);
				}
			}

		}
		// 删除缓存中公司信息
		redisCache.deleteObject(CacheConstants.NETWORK_CUSTOMER_INFO + oldKey);

		// 记录操作日志
		String actionName = "编辑托运人(托运人id：" + customerInfo.getId() + "，托运人名称：" + customerInfo.getCustomerName() + ")";
		actionName += "，请求参数：" + JSONUtil.toJsonStr(req);
		sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_CUSTOMER_INFO, BusinessConstants.ACTION_TYPE_UPDATE, org.apache.commons.lang3.StringUtils.substring(actionName, 0, 5000),
				customerInfo.getCreditCode(), Collections.singletonList(customerInfo.getId()));
		return result;
	}

	/**
	 * 数据校验
	 *
	 * @param req
	 * @return
	 */
	private AjaxResult productVerify(CustomerInfoReq req) {
		if (null == req) {
			return AjaxResult.error(HttpStatus.ERROR, "数据对象为空");
		}
		BeanUtils.beanAttributeValueTrim(req);// 去掉该对象中string字段前后空格
		if (StringUtils.isBlank(req.getCustomerName())) {
			return AjaxResult.error(HttpStatus.ERROR, "客户名称为空");
		}
		if (StringUtils.isBlank(req.getCreditCode())) {
			return AjaxResult.error(HttpStatus.ERROR, "统一社会信用代码为空");
		}
		if (StringUtils.isBlank(req.getContact())) {
			return AjaxResult.error(HttpStatus.ERROR, "联系人为空");
		}
		if (!ValidateUtils.checkMobilePhone(req.getContactPhone())) {
			return AjaxResult.error(HttpStatus.ERROR, "请填写正确的联系方式");
		}
		if (!ValidateUtils.checkMobilePhone(req.getTelephone()) && !ValidateUtils.checkTelephone(req.getTelephone())) {
			return AjaxResult.error(HttpStatus.ERROR, "请填写正确的电话号码");
		}
		if (CUSTOMER_CREATE_BY_OUT.equals(req.getResourceType())) {
			if (CommonUtil.isEmptyOrZero(req.getFreightForwarderId())) {
				return AjaxResult.error(HttpStatus.ERROR, "网络货运人为空");
			}
		} else {
			// 校验开票信息
			if (StringUtils.isBlank(req.getBillHead())) {
				return AjaxResult.error(HttpStatus.ERROR, "发票抬头为空");
			}
			if (StringUtils.isBlank(req.getTaxNo())) {
				return AjaxResult.error(HttpStatus.ERROR, "纳税人识别号为空");
			}
		}
		if (StringUtils.isBlank(req.getTaxNo())) {
			return AjaxResult.error(HttpStatus.ERROR, "纳税人识别号为空");
		}
		if (StringUtils.isBlank(req.getUnitAddress())) {
			return AjaxResult.error(HttpStatus.ERROR, "单位地址为空");
		}
		if (req.getRemark() != null && req.getRemark().length() > 255) {
			return AjaxResult.error(HttpStatus.ERROR, "备注不能大于255个字");
		}
		// 新增时，查询手机号是否已被注册
		/*if (null != req.getId()) {
			TenantUserEx user = new TenantUserEx();
			user.setUserName(req.getContactPhone());
			user.setPhonenumber(req.getContactPhone());
			user.setDelFlag(String.valueOf(BusinessConstants.STATE_ENABLE));
			List<TenantUser> userList = tenantUserMapper.getAllUserList(user);
			if (StringUtils.isNotEmpty(userList)) {
				TenantUser userDb = userList.get(0);
				CustomerInfo customerInfoDb = customerInfoMapper.selectCustomerInfoById(userDb.getTenantId());
				if (customerInfoDb == null) {
					return AjaxResult.error(HttpStatus.BAD_REQUEST, " 该手机号已被用户注册，请更换联系方式");
				}
				return AjaxResult.error(HttpStatus.BAD_REQUEST, req.getContactPhone() + " 用户已被 " + customerInfoDb.getCustomerName() + " 注册，请更换联系方式");
			}
		}*/
		// 银行卡号去除空格
		if (req.getBankAccount() != null) {
			req.setBankAccount(req.getBankAccount().replace(" ", ""));
		}
		// 4.4.1新增法人身份证，姓名，附件，必填和二要素校验
		if (StringUtils.isBlank(req.getLegal())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "法人为空");
		}
		if (StringUtils.isBlank(req.getLegalIdNo())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "法人身份证为空");
		}
		// 法人姓名身份证二要素校验
		CommonResult<String> stringCommonResult = iPtmCertificateCheckService.realNameAuthentication(req.getLegal(), req.getLegalIdNo());
		if (CommonResult.isNotSuccess(stringCommonResult)) {
			return AjaxResult.error(HttpStatus.ERROR, "实名认证未通过");
		}
		// 附件必填校验：
		List<CustomerAttachmentInfo> attachment = req.getAttachment();
		if (attachment.isEmpty()) {
			return AjaxResult.error("附件信息不能为空");
		}

		Boolean hasIDA = false;
		Boolean hasIDB = false;
		for (CustomerAttachmentInfo customerAttachmentInfo : attachment) {
			if (customerAttachmentInfo.getFileName().equals(FileNames.JP_ID_CARD_A)) {
				hasIDA = true;
			}
			if (customerAttachmentInfo.getFileName().equals(FileNames.JP_ID_CARD_B)) {
				hasIDB = true;
			}
		}
		if (!hasIDA) {
			return AjaxResult.error("法人身份证（人像面）不能为空");
		}
		if (!hasIDB) {
			return AjaxResult.error("法人身份证（国徽面）不能为空");
		}

		return AjaxResult.success(HttpStatus.SUCCESS, BusinessConstants.SUCCESS_DESC);
	}

	/**
	 * 数据校验
	 *
	 * @param req
	 * @return
	 */
	private AjaxResult groupVerify(CustomerInfoReq req) {
		if (null == req) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "数据对象为空");
		}
		BeanUtils.beanAttributeValueTrim(req);// 去掉该对象中string字段前后空格
		if (StringUtils.isBlank(req.getCustomerName())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "客户名称为空");
		}
		if (StringUtils.isBlank(req.getCreditCode())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "统一社会信用代码为空");
		}
		if (StringUtils.isBlank(req.getContact())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "联系人为空");
		}
		if (StringUtils.isBlank(req.getContactPhone())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "联系方式为空");
		}
		// 联系方式去空格
		req.setContactPhone(req.getContactPhone().replace(" ", ""));
		// 校验开票信息
		if (StringUtils.isBlank(req.getBillHead())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "发票抬头为空");
		}
		if (StringUtils.isBlank(req.getTaxNo())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "纳税人识别号为空");
		}
		if (StringUtils.isBlank(req.getUnitAddress())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "单位地址为空");
		}
		if (StringUtils.isBlank(req.getTelephone())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "电话号码为空");
		}
		// 电话号码去空格
		req.setTelephone(req.getTelephone().replace(" ", ""));
		// 按客户名称查询有没有重复的
		CustomerInfo customerInfo = new CustomerInfo();
		customerInfo.setCustomerName(req.getCustomerName());
		List<CustomerInfo> list = customerInfoMapperEx.selectCustomerInfoList(customerInfo);
		if (null != list && !list.isEmpty()) {
			// 新增时
			if (null == req.getId()) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "托运人客户名称已存在");
			} else {
				// 编辑时
				if (req.getId().longValue() != list.get(0).getId().longValue()) {
					return AjaxResult.error(HttpStatus.BAD_REQUEST, "托运人客户名称已存在");
				}
			}
		}

		// 查询手机号是否已被注册
		/*TenantUserEx userSearch = new TenantUserEx();
		userSearch.setUserName(req.getTenantUser().getUserName());
		userSearch.setPhonenumber(req.getTenantUser().getUserName());
		userSearch.setDelFlag(String.valueOf(BusinessConstants.STATE_ENABLE));
		List<TenantUser> userList = tenantUserMapper.getAllUserList(userSearch);
		if (StringUtils.isNotEmpty(userList)) {
			TenantUser userDb = userList.get(0);
			CustomerInfo customerInfoDb = customerInfoMapper.selectCustomerInfoById(userDb.getTenantId());
			if (customerInfoDb == null) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, " 该手机号已被用户注册,请更换联系方式");
			}
			return AjaxResult.error(HttpStatus.BAD_REQUEST, req.getTenantUser().getUserName() + " 用户已被 " + customerInfoDb.getCustomerName() + " 注册,请更换联系方式");
		}*/
		// 调用聚合数据，查询公司名称、统一社会信用代码是否对应
		String creditCodeByName = juheService.getSocialCreditCodeByName(req.getCustomerName(), req.getCreditCode());
		if (!creditCodeByName.equals(req.getCreditCode())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "托运人名称、统一社会信用代码不匹配，请确认");
		}
		// 4.4.1新增法人身份证，姓名，附件，必填和二要素校验
		if (StringUtils.isBlank(req.getLegal())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "法人为空");
		}
		if (StringUtils.isBlank(req.getLegalIdNo())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "法人身份证为空");
		}
		// 法人姓名身份证二要素校验
		CommonResult<String> stringCommonResult = iPtmCertificateCheckService.realNameAuthentication(req.getLegal(), req.getLegalIdNo());
		if (CommonResult.isNotSuccess(stringCommonResult)) {
			return AjaxResult.error(HttpStatus.ERROR, "实名认证未通过");
		}
		// 附件必填校验：
		List<CustomerAttachmentInfo> attachment = req.getAttachment();
		if (attachment.isEmpty()) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "附件信息不能为空");
		}

		Boolean hasIDA = false;
		Boolean hasIDB = false;
		for (CustomerAttachmentInfo customerAttachmentInfo : attachment) {
			if (customerAttachmentInfo.getFileName().equals(FileNames.JP_ID_CARD_A)) {
				hasIDA = true;
			}
			if (customerAttachmentInfo.getFileName().equals(FileNames.JP_ID_CARD_B)) {
				hasIDB = true;
			}
		}
		if (!hasIDA) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "法人身份证（人像面）不能为空");
		}
		if (!hasIDB) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "法人身份证（国徽面）不能为空");
		}
		return AjaxResult.success(HttpStatus.SUCCESS, BusinessConstants.SUCCESS_DESC);
	}

	/**
	 * 数据校验
	 *
	 * @param req
	 * @return
	 */
	private AjaxResult groupVerifyUpdate(CustomerInfoReq req) {
		if (null == req) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "数据对象为空");
		}
		BeanUtils.beanAttributeValueTrim(req);// 去掉该对象中string字段前后空格
		if (StringUtils.isBlank(req.getCustomerName())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "客户名称为空");
		}
		if (StringUtils.isBlank(req.getCreditCode())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "统一社会信用代码为空");
		}
		if (StringUtils.isBlank(req.getContact())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "联系人为空");
		}
		if (StringUtils.isBlank(req.getContactPhone())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "联系方式为空");
		}
		// 校验开票信息
		if (StringUtils.isBlank(req.getBillHead())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "发票抬头为空");
		}
		if (StringUtils.isBlank(req.getTaxNo())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "纳税人识别号为空");
		}
		if (StringUtils.isBlank(req.getUnitAddress())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "单位地址为空");
		}
		if (StringUtils.isBlank(req.getTelephone())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "电话号码为空");
		}
		// 4.4.1新增法人身份证，姓名，附件，必填和二要素校验
		if (StringUtils.isBlank(req.getLegal())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "法人为空");
		}
		if (StringUtils.isBlank(req.getLegalIdNo())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "法人身份证为空");
		}
		// 法人姓名身份证二要素校验
		CommonResult<String> stringCommonResult = iPtmCertificateCheckService.realNameAuthentication(req.getLegal(), req.getLegalIdNo());
		if (CommonResult.isNotSuccess(stringCommonResult)) {
			return AjaxResult.error(HttpStatus.ERROR, "实名认证未通过");
		}
		// 附件必填校验：
		List<CustomerAttachmentInfo> attachment = req.getAttachment();
		if (attachment.isEmpty()) {
			return AjaxResult.error("附件信息不能为空");
		}

		Boolean hasIDA = false;
		Boolean hasIDB = false;
		for (CustomerAttachmentInfo customerAttachmentInfo : attachment) {
			if (customerAttachmentInfo.getFileName().equals(FileNames.JP_ID_CARD_A)) {
				hasIDA = true;
			}
			if (customerAttachmentInfo.getFileName().equals(FileNames.JP_ID_CARD_B)) {
				hasIDB = true;
			}
		}
		if (!hasIDA) {
			return AjaxResult.error("法人身份证（人像面）不能为空");
		}
		if (!hasIDB) {
			return AjaxResult.error("法人身份证（国徽面）不能为空");
		}
		return AjaxResult.success(HttpStatus.SUCCESS, BusinessConstants.SUCCESS_DESC);
	}

	/**
	 * 将到期合同中客户的合同状态变更为'已到期'
	 *
	 * @return 结果
	 */
	@Override
	public AjaxResult customerContractStateExpired() {
		customerInfoMapperEx.customerContractStateExpired();
		return AjaxResult.success(HttpStatus.SUCCESS, BusinessConstants.SUCCESS_DESC);
	}

	/**
	 * 新增货主信息验证
	 *
	 * @param customerVerifyEx
	 * 		货主信息验证表
	 * @return
	 */

	@Override
	@Transactional
	public CustomerVerify getOrInsertCustomerVerify(CustomerVerifyEx customerVerifyEx) {
		TenantUser tenantUser;
		VxService service = VxUtils.decrtptVxService(null, customerVerifyEx);
		CustomerVerify verify = customerVerifyEx.getCustomerVerify();

		// 获取手机号，通过手机号 查询货主端登录信息表是否存在
		CustomerVerify customerVerify = new CustomerVerify();
		customerVerify.setIphone(service.getPhoneNumber());
		List<CustomerVerify> customerVerifyList = customerVerifyMapper.selectCustomerVerifyList(customerVerify);
		// 小程序端托运人校验信息表，存在数据时填充托运人信息就直接登录
		if (!CommonUtil.isNullOrEmpty(customerVerifyList)) {
			customerVerify = customerVerifyList.get(0);
		} else {
			// 如果不存在就开始注册模式，注册时，要先去查询是否在PC端开启了这个手机号的权限
			tenantUser = tenantUserMapper.checkPhoneAbsoluteUnique(service.getPhoneNumber());
			if (tenantUser == null) {
				// 托运人用户不存在时，自动注册为托运人用户
				tenantUser = addTenantUser(service, verify);
			}
			customerVerify = addCustomerVerify(tenantUser, service, verify);
		}
		return customerVerify;
	}

	private CustomerVerify addCustomerVerify(TenantUser tenantUser, VxService service, CustomerVerify verify) {
		CustomerVerify customerVerify = new CustomerVerify();
		customerVerify.setCustomerId(tenantUser.getUserId());
		customerVerify.setIphone(service.getPhoneNumber());
		customerVerify.setOpenId(verify.getOpenId());
		customerVerify.setWxHeadImg(verify.getWxHeadImg());
		customerVerify.setWxSex(verify.getWxSex());
		customerVerify.setDeviceType(verify.getDeviceType());
		customerVerify.setWxNickName(verify.getWxNickName());
		customerVerifyMapper.insertCustomerVerify(customerVerify);
		return customerVerify;
	}

	private TenantUser addTenantUser(VxService service, CustomerVerify verify) {
		String password = SecurityUtils.encryptPassword(UserConstants.INITIAL_PASSWORD, false);
		TenantUser tenantUser = new TenantUser();
		tenantUser.setUserId(TextUtil.getTimeSequenceID(5));
		tenantUser.setDeptId(0L);
		tenantUser.setUserName(service.getPhoneNumber());
		tenantUser.setPassword(password);
		tenantUser.setPwd(SecurityUtils.encryptPassword(tenantUser.getUserName() + password, true));
		tenantUser.setNickName(verify.getWxNickName());
		tenantUser.setPhonenumber(service.getPhoneNumber());
		tenantUser.setCreateBy(verify.getWxNickName());
		tenantUser.setUpdateBy(verify.getWxNickName());
		tenantUserMapper.insertUser(tenantUser);
		return tenantUser;
	}

	/**
	 * 托运人审核
	 *
	 * @param req
	 * @return
	 */
	@Override
	public AjaxResult approveCustomer(CustomerInfoReq req) {

		if (StringUtils.isNull(req)) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "数据对象为空");
		} else if (null == req.getId()) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "客户标识为空");
		} else if (StringUtils.isNull(req.getApproveState()) || 0 == req.getApproveState()) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "审核状态为空");
		}
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(req.getId());
		if (null == customerInfo) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "没有找到对应的客户信息");
		}

		// 修改客户信息审核状态 2：审核通过 3：审核驳回
		customerInfo = new CustomerInfo();
		BeanUtils.copyProperties(req, customerInfo);
		customerInfo.setState(BusinessConstants.STATE_ENABLE);
		customerInfoMapper.updateCustomerInfo(customerInfo);
		return AjaxResult.success(HttpStatus.SUCCESS, BusinessConstants.SUCCESS_DESC);
	}

	/**
	 * 托运人配置
	 *
	 * @param req
	 * @return
	 */
	@Override
	@Transactional
	public AjaxResult adminConfig(CustomerAdminConfigReq req) {
		try {

			if (StringUtils.isNull(req)) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "数据对象为空");
			} else if (StringUtils.isNull(req.getCustomerId())) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "客户标识为空");
			} else if (StringUtils.isEmpty(req.getUserName())) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "用户名称为空");
			} else if (StringUtils.isEmpty(req.getPhoneNumber())) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "用户账号为空");
			}
			if (StringUtils.isEmpty(req.getPassword()) || !VerifyPwUtils.validatePassword(req.getPassword())) {
				return AjaxResult.error("密码太过简单，不支持纯数字，建议数字+大小写字母组合,密码位数介于8-16位之间");
			}

			String password = SecurityUtils.encryptPassword(req.getPassword(), false);
			req.setPhoneNumber(req.getPhoneNumber().trim());
			TenantUserEx user = new TenantUserEx();
			user.setUserName(req.getPhoneNumber() + "_" + req.getCustomerId());
			List<TenantUser> userList = tenantUserMapper.getAllUserList(user);

			Long userId;
			if (StringUtils.isEmpty(userList)) {
				// 用户不存在，新增用户
				TenantUser tenantUser = new TenantUser();
				tenantUser.setUserId(TextUtil.getTimeSequenceID(5));
				tenantUser.setTenantId(req.getCustomerId());

				tenantUser.setUserName(req.getPhoneNumber() + "_" + req.getCustomerId());
				tenantUser.setPassword(password);
				tenantUser.setPwd(SecurityUtils.encryptPassword(tenantUser.getUserName() + password, true));
				tenantUser.setNickName(req.getUserName());
				tenantUser.setPhonenumber(req.getPhoneNumber());
				tenantUser.setStatus(UserConstants.NORMAL);
				tenantUser.setCreateBy(BusinessConstants.ROBOT);
				tenantUser.setDefaultProject(1);
				if (StringUtils.isNotEmpty(req.getEmail())) {
					tenantUser.setEmail(req.getEmail());
				}

				// 部门信息
				TenantDept dept = new TenantDept();
				dept.setTenantId(req.getCustomerId());
				dept.setParentId(0L);
				List<TenantDept> deptList = tenantDeptMapper.selectDeptList(dept);
				Long deptId = 0L;
				if (StringUtils.isNotEmpty(deptList)) {
					deptId = deptList.get(0).getDeptId();
				}
				tenantUser.setDeptId(deptId);
				int success = tenantUserMapper.insertUser(tenantUser);
				if (success <= 0) {
					return AjaxResult.error(HttpStatus.BAD_REQUEST, "用户新增失败");
				}
				userId = tenantUser.getUserId();
			} else {
				TenantUser userDb = userList.get(0);
				userId = userDb.getUserId();

				if (StringUtils.isNotEmpty(req.getEmail())) {
					userDb.setEmail(req.getEmail());
				}
				// 若该用户已经是其他企业的用户则已存在
				if (userDb.getTenantId() > 0 && !userDb.getTenantId().equals(req.getCustomerId())) {
					CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(userDb.getTenantId());
					return AjaxResult.error(HttpStatus.BAD_REQUEST, req.getPhoneNumber() + " 用户已被 " + customerInfo.getCustomerName() + " 注册");
				}
				if (userDb.getTenantId() <= 0) {
					userDb.setTenantId(req.getCustomerId());
				}
				// 禁用的用户重新启用
				if (userDb.getStatus().equals(String.valueOf(BusinessConstants.STATE_DISABLE))) {
					userDb.setStatus(String.valueOf(BusinessConstants.STATE_ENABLE));
				}
				if (userDb.getDelFlag().equals(String.valueOf(BusinessConstants.TENANT_DELETE))) {
					// 删除的用户也重新启用
					userDb.setDelFlag(String.valueOf(BusinessConstants.STATE_ENABLE));
				}
				userDb.setNickName(req.getUserName());
				userDb.setPassword(password);
				userDb.setPwd(SecurityUtils.encryptPassword(userDb.getUserName() + password, true));
				userDb.setCreateBy(BusinessConstants.ROBOT);
				tenantUserMapper.updateUser(userDb);
			}

			// 查询公司管理员角色信息,若不存在管理员，则初始化管理员信息
			TenantRole db = new TenantRole();
			db.setRoleKey("admin");
			db.setRoleName("管理员");
			db.setTenantId(req.getCustomerId());
			List<TenantRole> roleList = tenantRoleMapper.selectRoleList(db);

			if (StringUtils.isEmpty(roleList)) {
				CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(req.getCustomerId());
				CustomerInfoReq customerInfoReq = new CustomerInfoReq();
				BeanUtils.copyProperties(customerInfo, customerInfoReq);
				insertTenantUserInfoRole(customerInfoReq, userId, 0);
			} else {
				// 若公司管理员存在
				// 将公司原管理员改为删除状态
				tenantUserMapper.updateUserDelFlag(req.getCustomerId(), userId);
				// 新增当前用户为管理员（先删除当前用户原信息，再新增）
				tenantUserRoleMapper.deleteUserRoleByUserId(userId);
				TenantUserRole ur = new TenantUserRole();
				ur.setUserId(userId);
				ur.setRoleId(roleList.get(0).getRoleId());
				List<TenantUserRole> urList = new ArrayList<>();
				urList.add(ur);
				tenantUserRoleMapper.batchUserRole(urList);

			}

			// 处理金融服务管理员
			Long financeId;
			FinanceUserReq financeUserReq = new FinanceUserReq();
			financeUserReq.setUserName(req.getPhoneNumber() + "_" + req.getCustomerId());
			List<FinanceUser> financeUsers = financeUserMapper.selectUserList(financeUserReq);
			if (StringUtils.isEmpty(financeUsers)) {
				// 用户不存在，新增用户
				FinanceUser financeUser = new FinanceUser();
				financeUser.setUserId(TextUtil.getTimeSequenceID(5));
				financeUser.setCustomerId(req.getCustomerId());

				financeUser.setUserName(req.getPhoneNumber() + "_" + req.getCustomerId());
				financeUser.setPassword(password);
				financeUser.setPwd(SecurityUtils.encryptPassword(financeUser.getUserName() + password, true));
				financeUser.setNickName(req.getUserName());
				financeUser.setPhonenumber(req.getPhoneNumber());
				financeUser.setStatus(UserConstants.NORMAL);
				financeUser.setCreateBy(BusinessConstants.ROBOT);
				financeUser.setDefaultProject(1);
				if (StringUtils.isNotEmpty(req.getEmail())) {
					financeUser.setEmail(req.getEmail());
				}
				financeUserMapper.insertUser(financeUser);
				financeId = financeUser.getUserId();
			} else {
				FinanceUser financeUser = financeUsers.get(0);
				financeUser.setDefaultProject(1);
				if (StringUtils.isNotEmpty(req.getEmail())) {
					financeUser.setEmail(req.getEmail());
				}
				// 禁用的用户重新启用
				if (financeUser.getStatus().equals(String.valueOf(BusinessConstants.STATE_DISABLE))) {
					financeUser.setStatus(String.valueOf(BusinessConstants.STATE_ENABLE));
				}
				financeUser.setNickName(req.getUserName());
				financeUser.setPassword(password);
				financeUser.setPwd(SecurityUtils.encryptPassword(financeUser.getUserName() + password, true));
				financeUserMapper.updateUser(financeUser);
				financeId = financeUser.getUserId();
			}

			FinanceRole _financeRole = new FinanceRole();
			_financeRole.setRoleKey("admin");
			_financeRole.setCustomerId(req.getCustomerId());
			List<FinanceRole> financeRoles = financeRoleMapper.selectRoleList(_financeRole);
			if (StringUtils.isEmpty(financeRoles)) {
				// 角色数据
				FinanceRole financeRole = new FinanceRole();
				Long financeRoleId = TextUtil.getTimeSequenceID(5);
				financeRole.setRoleId(financeRoleId);
				financeRole.setRoleName("管理员");
				financeRole.setRoleKey("admin");
				financeRole.setStatus("0");
				financeRole.setRoleSort("1");
				financeRole.setCreateBy(BusinessConstants.ROBOT);
				financeRole.setUpdateBy(BusinessConstants.ROBOT);
				financeRole.setCreateTime(DateUtils.getNowDate());
				financeRole.setUpdateTime(DateUtils.getNowDate());
				financeRole.setCustomerId(req.getCustomerId());
				financeRoleMapper.insertRole(financeRole);

				// 用户和角色
				List<FinanceUserRole> financeUserRoles = new ArrayList<>();
				FinanceUserRole financeUserRole = new FinanceUserRole();
				financeUserRole.setUserId(financeId);
				financeUserRole.setRoleId(financeRoleId);
				financeUserRoles.add(financeUserRole);
				financeUserRoleMapper.batchUserRole(financeUserRoles);

				// 角色与菜单关系数据
				List<FinanceModelMenu> financeModelMenus = financeModelMenuMapper.selectModelMenuList(null);
				List<FinanceRoleMenu> financeRoleMenu = new ArrayList<>();
				for (FinanceModelMenu financeMenu : financeModelMenus) {
					FinanceRoleMenu _financeRoleMenu = new FinanceRoleMenu();
					_financeRoleMenu.setRoleId(financeRoleId);
					_financeRoleMenu.setMenuId(financeMenu.getId());
					financeRoleMenu.add(_financeRoleMenu);
				}
				financeRoleMenuMapper.batchRoleMenu(financeRoleMenu);
			} else {
				// 把原管理员修改为删除状态
				financeUserMapper.updateUserDelFlag(req.getCustomerId(), financeId);
				// 删除角色用户关联
				financeUserRoleMapper.deleteUserRoleByRoleId(financeRoles.get(0).getRoleId());
				List<FinanceUserRole> userRoleList = new ArrayList<>();
				FinanceUserRole _financeUserRole = new FinanceUserRole();
				_financeUserRole.setRoleId(financeRoles.get(0).getRoleId());
				_financeUserRole.setUserId(financeId);
				userRoleList.add(_financeUserRole);
				financeUserRoleMapper.batchUserRole(userRoleList);
			}

			// 查询所有失效用户,清楚token
			List<Long> financeUserList = financeUserMapper.queryDelFlagUser(req.getCustomerId(), financeId);
			List<Long> tenantUserList = tenantUserMapper.queryDelFlagUser(req.getCustomerId(), userId);

			Collection<String> key = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
			for (String s : key) {
				LoginUser loginUser = redisCache.getCacheObject(s);
				for (Long aLong : tenantUserList) {
					if (loginUser != null && loginUser.getUserId() != null && aLong.equals(loginUser.getUserId())) {
						redisCache.deleteObject(s);
					}
				}
				for (Long aLong : financeUserList) {
					if (loginUser != null && loginUser.getUserId() != null && aLong.equals(loginUser.getUserId())) {
						redisCache.deleteObject(s);
					}
				}
			}

			return AjaxResult.success(HttpStatus.SUCCESS, BusinessConstants.SUCCESS_DESC);
		} catch (Exception ex) {
			ex.printStackTrace();
			log.error(ex.toString());
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return AjaxResult.error(HttpStatus.BAD_REQUEST, ex.toString());
		}

	}

	/**
	 * 公司管理员信息
	 *
	 * @param id
	 * @return
	 */
	@Override
	public CustomerAdminConfigRes adminInfo(Long id) {

		TenantRole db = new TenantRole();
		db.setRoleKey("admin");
		db.setTenantId(id);
		db.setStatus(UserConstants.NORMAL);
		// 管理员用户信息
		TenantUser user = tenantUserMapperEx.selectUserByRole(db);
		CustomerAdminConfigRes res = new CustomerAdminConfigRes();
		if (StringUtils.isNotNull(user)) {
			res.setCustomerId(id);
			res.setPhoneNumber(user.getPhonenumber());
			res.setUserName(user.getNickName());
			res.setEmail(user.getEmail());
		}

		return res;
	}

	/**
	 * 企业认证货主端
	 *
	 * @param customerInfoReq
	 * @return
	 */
	@Transactional
	@Override
	public AjaxResult insertIdentity(CustomerInfoReq customerInfoReq) {
		try {
			LoginUser loginUserFinal = SecurityUtils.getLoginUser();
			List<CustomerAttachmentInfo> customerFiles = customerInfoReq.getAttachment();
			if (customerFiles == null || customerFiles.size() == 0) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "请先上传附件");
			}

			// 传入参数实体类 转移到 插入实体类
			CustomerInfo customerInfo = new CustomerInfo();
			BeanUtils.copyProperties(customerInfoReq, customerInfo);

			CustomerInfo _customerInfo = new CustomerInfo();
			_customerInfo.setCreditCode(customerInfo.getCreditCode());
			List<CustomerInfo> customerInfoList = customerInfoMapper.selectCustomerInfoList(_customerInfo);

			// 如果数据有值 直接返回 无法重复注册
			if (customerInfoList != null && customerInfoList.size() != 0) {
				return AjaxResult.error(HttpStatus.ERROR, "该企业已存在,请让管理员分配账号");
			}
			// 插入信息
			customerInfo.setId(TextUtil.getTimeSequenceID(5));
			customerInfo.setCustomerCode(CodeUtil.generateCustomerCode());
			customerInfo.setApproveState(1); // 审核状态 1 审核中 2 审核成功 3 审核驳回
			// 这里的把id传到req中，是为了在插入模板数据时，获取企业id
			customerInfoReq.setId(customerInfo.getId());

			int success = customerInfoMapper.insertCustomerInfo(customerInfo);
			if (success == 0) {
				throw new RuntimeException("插入失败");
			}
			// 保存企业基础服务
			CustomerServiceInfo serviceInfo = new CustomerServiceInfo();
			Long customerServiceId = TextUtil.getTimeSequenceID(5);
			serviceInfo.setId(customerServiceId);
			serviceInfo.setCustomerId(customerInfo.getId());
			serviceInfo.setCustomerName(customerInfo.getCustomerName());
			serviceInfo.setOpenState(1);
			serviceInfo.setCustomerServiceId(Long.parseLong(CustomerServiceEnum.BASIS.getCode()));
			serviceInfo.setCreateBy(SecurityUtils.getNickname());// 创建人
			serviceInfo.setCreateTime(DateUtils.getNowDate());// 创建时间
			serviceInfo.setUpdateBy(SecurityUtils.getNickname());// 修改人
			serviceInfo.setUpdateTime(DateUtils.getNowDate());// 修改时间
			customerServiceInfoMapper.insertCustomerServiceInfo(serviceInfo);

			TenantUser tenant = new TenantUser();
			tenant.setTenantId(customerInfo.getId());
			tenant.setUserId(SecurityUtils.getUserId());
			success = tenantUserMapper.updateUser(tenant);
			if (success == 0) {
				throw new RuntimeException("信息绑定失败");
			}
			// 刷新登录信息中的 tenantId
			LoginUser loginUser = new LoginUser();
			loginUser.setToken(loginUserFinal.getToken());
			loginUser.setLoginTime(System.currentTimeMillis());
			loginUser.setUserId(customerVerifyMapper.selectCustomerVerifyByCustomerId(loginUserFinal.getUserId()).getCustomerId());
			loginUser.setTenantUser(tenantUserMapper.selectUserByUserId(loginUserFinal.getUserId(), null));
			loginUser.getTenantUser().setTenantId(customerInfo.getId());
			loginUser.setDeptId(loginUser.getTenantUser().getDeptId());
			loginUser.setClientType(ClientType.SHIPPER);

			tokenService.refreshToken(loginUser);

			// 插入附件信息
			for (CustomerAttachmentInfo customerFile : customerFiles) {
				customerFile.setId(TextUtil.getTimeSequenceID(5));
				customerFile.setRelationId(loginUser.getTenantUser().getTenantId());
				customerFile.setCreateBy(loginUser.getNickname());
				customerFile.setCreateTime(DateUtils.getNowDate());
				customerFile.setUpdateBy(loginUser.getNickname());// 修改人
				customerFile.setUpdateTime(DateUtils.getNowDate());// 修改时间
				customerAttachmentInfoService.insertCustomerAttachmentInfo(customerFile);
			}

			this.insertTenantUserInfoRole(customerInfoReq, SecurityUtils.getUserId(), 0);
			return AjaxResult.success(HttpStatus.SUCCESS, "提交认证成功");

			// 本来想双重校验的，后来得知前端请求
			// OcrRecognitionReq ocrRecognitionReq = new OcrRecognitionReq();
			// ocrRecognitionReq.setPicType(7);
			// ocrRecognitionReq.setUrl(attachment.get(0).getFileUrl());
			// AjaxResult businessLicense = ocrController.certificationBusinessLicense(ocrRecognitionReq);
			//
			// int code = (Integer) businessLicense.get("code");
			// String msg = (String) businessLicense.get("msg");
			// if (code != 200 && !"操作成功".equals(msg)){
			// throw new RuntimeException("营业执照识别失败");
			// }
			//
			// OcrRecognitionRes ocrRecognitionRes = (OcrRecognitionRes) businessLicense.get("data");
			// // 一致标志
			// boolean consistent = false;
			// // 处理社会统一信用代码空格问题
			// customerInfo.setCreditCode(customerInfo.getCreditCode().replace(" ",""));
			// // 公司名称
			// consistent = ocrRecognitionRes.getName().equals(customerInfo.getCustomerName());
			// // 法人
			// consistent = ocrRecognitionRes.getLegalRepresentative().equals(customerInfo.getLegal());
			// // 社会统一信用代码
			// consistent = ocrRecognitionRes.getRegistrationNumber().equals(customerInfo.getCreditCode());
			//
			// if (!consistent){
			// throw new RuntimeException("企业信息有误请重新填写");
			// }
		} catch (Exception ex) {
			log.error(ex.toString());
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "认证发生异常");
		}
	}

	/**
	 * 企业认证货主端 后 插入的一套数据逻辑
	 *
	 * @param customerInfoReq
	 * @param initPassword
	 * 		自定义的初始密码，如果传null，将指定默认的初始密码
	 * @return
	 */
	@Transactional
	@Override
	public AjaxResult insertTenantUserInfo(CustomerInfoReq customerInfoReq, String initPassword) {
		LoginUser loginUserFinal = SecurityUtils.getLoginUser();

		String createBy = BusinessConstants.ROBOT;
		if (StringUtils.isNotNull(loginUserFinal.getTenantUser())) {
			createBy = loginUserFinal.getTenantUser().getUserName();
		}
		// 如果传入的初始密码传null，将指定默认的初始密码
		if (null == initPassword) {
			initPassword = UserConstants.INITIAL_PASSWORD;
		}

		// 部门数据
		TenantDept tenantDept = new TenantDept();
		// 顶级部门为 0
		tenantDept.setParentId(0L);
		tenantDept.setDeptName(customerInfoReq.getCustomerName());
		tenantDept.setLeader(customerInfoReq.getCustomerName());
		tenantDept.setPhone(customerInfoReq.getContactPhone());
		tenantDept.setTenantId(customerInfoReq.getId());
		tenantDept.setCreateTime(DateUtils.getNowDate());
		tenantDept.setUpdateTime(DateUtils.getNowDate());
		tenantDeptMapper.insertDept(tenantDept);
		// 获取的插入的部门id
		Long deptId = tenantDept.getDeptId();

		String password = SecurityUtils.encryptPassword(initPassword, false);
		// 初始化创建一个托运人
		Long tenantUserId = TextUtil.getTimeSequenceID(5);
		TenantUser tenantUser = new TenantUser();
		tenantUser.setCreateBy(BusinessConstants.ROBOT);
		tenantUser.setTenantId(customerInfoReq.getId());
		tenantUser.setUserName(customerInfoReq.getContactPhone() + "_" + tenantUser.getTenantId());
		tenantUser.setPassword(password);// 默认初始密码
		tenantUser.setPwd(SecurityUtils.encryptPassword(tenantUser.getUserName() + password, true));
		tenantUser.setUserId(tenantUserId);
		tenantUser.setNickName(customerInfoReq.getContact());
		tenantUser.setPhonenumber(customerInfoReq.getContactPhone());
		tenantUser.setDeptId(deptId);
		tenantUser.setDefaultProject(1);
		tenantUserMapper.insertUser(tenantUser);

		// 岗位数据
		TenantPost tenantPost = new TenantPost();
		tenantPost.setPostCode("admin");
		tenantPost.setPostName("管理员");
		tenantPost.setStatus("0");
		tenantPost.setPostSort("1");
		tenantPost.setCreateBy(createBy);
		tenantPost.setUpdateBy(createBy);
		tenantPost.setTenantId(customerInfoReq.getId());
		tenantPost.setCreateTime(DateUtils.getNowDate());
		tenantPost.setUpdateTime(DateUtils.getNowDate());
		tenantPostMapper.insertPost(tenantPost);
		Long postId = tenantPost.getPostId();

		// 角色数据
		TenantRole tenantRole = new TenantRole();
		tenantRole.setRoleName("管理员");
		tenantRole.setRoleKey("admin");
		tenantRole.setStatus("0");
		tenantRole.setRoleSort("1");
		tenantRole.setCreateBy(BusinessConstants.ROBOT);
		tenantRole.setUpdateBy(BusinessConstants.ROBOT);
		tenantRole.setCreateTime(DateUtils.getNowDate());
		tenantRole.setUpdateTime(DateUtils.getNowDate());
		tenantRole.setTenantId(customerInfoReq.getId());
		tenantRoleMapper.insertRole(tenantRole);
		Long roleId = tenantRole.getRoleId();
		// 预设其他角色
		setTenantRole(customerInfoReq, tenantUserId);

		// 用户和岗位
		List<TenantUserPost> userPostList = new ArrayList<>();
		TenantUserPost tenantUserPost = new TenantUserPost();
		tenantUserPost.setUserId(tenantUserId);
		tenantUserPost.setPostId(postId);
		userPostList.add(tenantUserPost);
		tenantUserPostMapper.batchUserPost(userPostList);

		// 用户和角色
		List<TenantUserRole> userRoleList = new ArrayList<>();
		TenantUserRole tenantUserRole = new TenantUserRole();
		tenantUserRole.setUserId(tenantUserId);
		tenantUserRole.setRoleId(roleId);
		userRoleList.add(tenantUserRole);
		tenantUserRoleMapper.batchUserRole(userRoleList);

		// 角色与部门关系数据
		List<TenantRoleDept> tenantRoleDept = new ArrayList<>();
		TenantRoleDept _tenantRoleDept = new TenantRoleDept();
		_tenantRoleDept.setRoleId(roleId);
		_tenantRoleDept.setDeptId(deptId);
		tenantRoleDept.add(_tenantRoleDept);
		tenantRoleDeptMapper.batchRoleDept(tenantRoleDept);

		// 角色与菜单关系数据 暂时全部
		List<TenantMenu> tenantMenus = tenantMenuMapper.selectMenuList(null);
		List<TenantRoleMenu> tenantRoleMenu = new ArrayList<>();
		for (TenantMenu tenantMenu : tenantMenus) {
			TenantRoleMenu _tenantRoleMenu = new TenantRoleMenu();
			_tenantRoleMenu.setRoleId(roleId);
			_tenantRoleMenu.setMenuId(tenantMenu.getMenuId());
			tenantRoleMenu.add(_tenantRoleMenu);
		}
		tenantRoleMenuMapper.batchRoleMenu(tenantRoleMenu);

		return AjaxResult.success();
	}

	@Transactional
	@Override
	public AjaxResult insertFinanceUserInfo(CustomerInfoReq customerInfoReq, String initPassword) {
		LoginUser loginUserFinal = SecurityUtils.getLoginUser();

		String createBy = BusinessConstants.ROBOT;
		if (StringUtils.isNotNull(loginUserFinal.getTenantUser())) {
			createBy = loginUserFinal.getTenantUser().getUserName();
		}
		// 如果传入的初始密码传null，将指定默认的初始密码
		if (null == initPassword) {
			initPassword = UserConstants.INITIAL_PASSWORD;
		}

		String password = SecurityUtils.encryptPassword(initPassword, false);
		// 初始化创建一个金融服务用户
		Long financeUserId = TextUtil.getTimeSequenceID(5);
		FinanceUser financeUser = new FinanceUser();
		financeUser.setCreateBy(BusinessConstants.ROBOT);
		financeUser.setCustomerId(customerInfoReq.getId());
		financeUser.setUserName(customerInfoReq.getContactPhone() + "_" + customerInfoReq.getId());
		financeUser.setPassword(password);// 默认初始密码
		financeUser.setPwd(SecurityUtils.encryptPassword(financeUser.getUserName() + password, true));
		financeUser.setUserId(financeUserId);
		financeUser.setNickName(customerInfoReq.getContact());
		financeUser.setPhonenumber(customerInfoReq.getContactPhone());
		financeUser.setDefaultProject(1);
		financeUserMapper.insertUser(financeUser);

		// 角色数据
		FinanceRole financeRole = new FinanceRole();
		Long financeRoleId = TextUtil.getTimeSequenceID(5);
		financeRole.setRoleId(financeRoleId);
		financeRole.setRoleName("管理员");
		financeRole.setRoleKey("admin");
		financeRole.setStatus("0");
		financeRole.setRoleSort("1");
		financeRole.setCreateBy(BusinessConstants.ROBOT);
		financeRole.setUpdateBy(BusinessConstants.ROBOT);
		financeRole.setCreateTime(DateUtils.getNowDate());
		financeRole.setUpdateTime(DateUtils.getNowDate());
		financeRole.setCustomerId(customerInfoReq.getId());
		financeRoleMapper.insertRole(financeRole);
		// 内置预设角色
		setFinanceRole(customerInfoReq, financeUserId);

		// 用户和角色
		List<FinanceUserRole> userRoleList = new ArrayList<>();
		FinanceUserRole financeUserRole = new FinanceUserRole();
		financeUserRole.setUserId(financeUserId);
		financeUserRole.setRoleId(financeRoleId);
		userRoleList.add(financeUserRole);
		financeUserRoleMapper.batchUserRole(userRoleList);

		// 角色与菜单关系数据
		// List<FinanceMenu> financeMenus = financeMenuMapper.selectMenuList(null);
		List<FinanceModelMenu> financeModelMenus = financeModelMenuMapper.selectModelMenuList(null);
		List<FinanceRoleMenu> financeRoleMenu = new ArrayList<>();
		for (FinanceModelMenu financeMenu : financeModelMenus) {
			FinanceRoleMenu _financeRoleMenu = new FinanceRoleMenu();
			_financeRoleMenu.setRoleId(financeRoleId);
			_financeRoleMenu.setMenuId(financeMenu.getId());
			financeRoleMenu.add(_financeRoleMenu);
		}
		financeRoleMenuMapper.batchRoleMenu(financeRoleMenu);

		return AjaxResult.success();
	}

	private void setTenantRole(CustomerInfoReq customerInfoReq, Long tenantUserId) {
		List<PresetRoles> presetRoles = iPresetRolesService.selectPresetRolesList(ClientType.SHIPPER.val());
		// List<TenantUserRole> tenantUserRole = new ArrayList<>();
		for (int i = 0; i < presetRoles.size(); i++) {
			PresetRoles presetRole = presetRoles.get(i);

			// 新增预设角色信息,随机生成权限字符
			TenantRole tenantRole1 = new TenantRole();
			Long roleId = TextUtil.getTimeSequenceID(5);
			tenantRole1.setRoleId(roleId);
			tenantRole1.setRoleName(presetRole.getRoleName());
			tenantRole1.setRoleKey(RandomStringUtils.randomAlphanumeric(8));
			tenantRole1.setRoleSort(String.valueOf(i + 2));
			tenantRole1.setStatus(presetRole.getStatus());
			tenantRole1.setCreateBy(BusinessConstants.ROBOT);
			tenantRole1.setUpdateBy(BusinessConstants.ROBOT);
			tenantRole1.setCreateTime(DateUtils.getNowDate());
			tenantRole1.setUpdateTime(DateUtils.getNowDate());
			tenantRole1.setTenantId(customerInfoReq.getId());
			tenantRole1.setPresetRoleId(presetRole.getRoleId());
			tenantRole1.setRemark(StringUtils.getOrDefault(presetRole.getRemark(), "系统预置角色"));
			tenantRoleMapper.insertRole(tenantRole1);
			/*if (ClientType.SHIPPER.equals(presetRole.getClientType())){
				tenantRoleMapper.insertRole(tenantRole1);
			}*/
			// 设置角色菜单
			setTenanMenu(presetRole.getRoleId(), tenantRole1.getRoleId());
			// tenantUserRole.add(new TenantUserRole(roleId,tenantUserId));
		}
		// 设置内置角色用户关联
		// tenantUserRoleMapper.batchUserRole(tenantUserRole);

		/*String[] roleName = new String[] { "运营", "业务员", "建单员", "财务", "出纳", "开票申请", "系统管理", "小程序托运人端" };
		List<String> roleList = new ArrayList<>(Arrays.asList(roleName));
		for (int i = 0; i < roleList.size(); i++) {
			TenantRole tenantRole = new TenantRole();
			tenantRole.setRoleName(roleList.get(i));
			tenantRole.setRoleKey("common");
			tenantRole.setStatus("0");
			tenantRole.setRoleSort("1");
			tenantRole.setCreateBy(createBy);
			tenantRole.setUpdateBy(createBy);
			tenantRole.setCreateTime(DateUtils.getNowDate());
			tenantRole.setUpdateTime(DateUtils.getNowDate());
			tenantRole.setTenantId(customerInfoReq.getId());
			tenantRoleMapper.insertRole(tenantRole);
			Long roleId = tenantRole.getRoleId();
			setTenanMenu(roleId, i);
		}*/
	}

	// 给不同角色配置菜单
	// 给角色配置菜单
	private void setTenanMenu(Long presetRoleId, Long RoleId) {
		List<TenantRoleMenu> tenantRoleMenu = new ArrayList<>();
		List<PresetRolesMenu> menuList = iPresetRolesMenuService.getByPresetRolesId(presetRoleId);
		for (PresetRolesMenu presetRolesMenu : menuList) {
			TenantRoleMenu _tenantRoleMenu = new TenantRoleMenu();
			_tenantRoleMenu.setRoleId(RoleId);
			_tenantRoleMenu.setMenuId(presetRolesMenu.getMenuId());
			tenantRoleMenu.add(_tenantRoleMenu);
		}
		for (TenantRoleMenu roleMenu : tenantRoleMenu) {
			if (roleMenu.getMenuId() == null) {
				roleMenu.setMenuId(0L);
			}
			if (roleMenu.getRoleId() == null) {
				roleMenu.setRoleId(0L);
			}
		}
		if (!tenantRoleMenu.isEmpty()) {
			tenantRoleMenuMapper.batchRoleMenu(tenantRoleMenu);
		}

		/*List<Long> tenantMenusIds = new ArrayList<>();
		List<TenantRoleMenu> tenantRoleMenu = new ArrayList<>();
		switch (i) {
		case 0: // 运营岗位对应菜单按钮
			tenantMenusIds.addAll(Arrays.asList(YUNYING_MENU));
			break;
		case 1: // 业务员对应菜单按钮
			tenantMenusIds.addAll(Arrays.asList(BUSINESS_MENU));
			break;
		case 2: // 建单员对应菜单按钮
			tenantMenusIds.addAll(Arrays.asList(CREATER_MENU));
			break;
		case 3: // 财务对应菜单按钮
			tenantMenusIds.addAll(Arrays.asList(FINANCE_MENU));
			break;
		case 4: // 出纳
			tenantMenusIds.addAll(Arrays.asList(RECEIVE_PAY_MENU));
			break;
		case 5: // 开票申请
			tenantMenusIds.addAll(Arrays.asList(INVOICE_MENU));
			break;
		case 6: // 系统管理
		default:
			tenantMenusIds.addAll(Arrays.asList(SYSTEM_MENU));
			break;
		}*/

	}

	/**
	 * 企业认证货主端 后 插入的一套数据逻辑
	 *
	 * @param customerInfoReq
	 * @return
	 */
	@Transactional
	@Override
	public AjaxResult insertTenantUserInfoGroup(CustomerInfoReq customerInfoReq, Long tenantUserId) {
		LoginUser loginUserFinal = SecurityUtils.getLoginUser();

		String createBy = BusinessConstants.ROBOT;
		if (StringUtils.isNotNull(loginUserFinal.getTenantUser())) {
			createBy = loginUserFinal.getTenantUser().getUserName();
		}

		// 部门数据
		TenantDept tenantDept = new TenantDept();
		// 顶级部门为 0
		tenantDept.setParentId(0L);
		tenantDept.setDeptName(customerInfoReq.getCustomerName());
		tenantDept.setLeader(customerInfoReq.getCustomerName());
		tenantDept.setPhone(customerInfoReq.getTenantUser().getUserName());
		tenantDept.setTenantId(customerInfoReq.getId());
		tenantDept.setCreateTime(DateUtils.getNowDate());
		tenantDept.setUpdateTime(DateUtils.getNowDate());
		tenantDeptMapper.insertDept(tenantDept);
		// 获取的插入的部门id
		Long deptId = tenantDept.getDeptId();

		// 初始化创建一个托运人
		if (tenantUserId == null || tenantUserId <= 0L) {
			String password = SecurityUtils.encryptPassword(UserConstants.INITIAL_PASSWORD, false);
			TenantUser tenantUser = new TenantUser();
			tenantUser.setCreateBy(BusinessConstants.ROBOT);
			tenantUser.setTenantId(customerInfoReq.getId());
			tenantUser.setUserName(customerInfoReq.getTenantUser().getUserName());
			tenantUser.setPassword(password);// 默认初始密码
			tenantUser.setPwd(SecurityUtils.encryptPassword(tenantUser.getUserName() + password, true));
			tenantUserId = TextUtil.getTimeSequenceID(5);
			tenantUser.setUserId(tenantUserId);
			tenantUser.setNickName(customerInfoReq.getTenantUser().getUserName());
			tenantUser.setPhonenumber(customerInfoReq.getTenantUser().getUserName());
			tenantUser.setDeptId(deptId);
			tenantUser.setDefaultProject(1);
			tenantUserMapper.insertUser(tenantUser);
		}

		// 岗位数据
		TenantPost tenantPost = new TenantPost();
		tenantPost.setPostCode("admin");
		tenantPost.setPostName("管理员");
		tenantPost.setStatus("0");
		tenantPost.setPostSort("1");
		tenantPost.setCreateBy(createBy);
		tenantPost.setUpdateBy(createBy);
		tenantPost.setTenantId(customerInfoReq.getId());
		tenantPost.setCreateTime(DateUtils.getNowDate());
		tenantPost.setUpdateTime(DateUtils.getNowDate());
		tenantPostMapper.insertPost(tenantPost);
		Long postId = tenantPost.getPostId();

		// 角色数据
		TenantRole tenantRole = new TenantRole();
		tenantRole.setRoleName("管理员");
		tenantRole.setRoleKey("admin");
		tenantRole.setStatus("0");
		tenantRole.setRoleSort("1");
		tenantRole.setCreateBy(createBy);
		tenantRole.setUpdateBy(createBy);
		tenantRole.setCreateTime(DateUtils.getNowDate());
		tenantRole.setUpdateTime(DateUtils.getNowDate());
		tenantRole.setTenantId(customerInfoReq.getId());
		tenantRoleMapper.insertRole(tenantRole);
		Long roleId = tenantRole.getRoleId();
		// 预设其他角色
		// setTenantRole(customerInfoReq, createBy);
		// 用户和岗位
		List<TenantUserPost> userPostList = new ArrayList<>();
		TenantUserPost tenantUserPost = new TenantUserPost();
		tenantUserPost.setUserId(tenantUserId);
		tenantUserPost.setPostId(postId);
		userPostList.add(tenantUserPost);
		tenantUserPostMapper.batchUserPost(userPostList);

		// 用户和角色
		List<TenantUserRole> userRoleList = new ArrayList<>();
		TenantUserRole tenantUserRole = new TenantUserRole();
		tenantUserRole.setUserId(tenantUserId);
		tenantUserRole.setRoleId(roleId);
		userRoleList.add(tenantUserRole);
		tenantUserRoleMapper.batchUserRole(userRoleList);

		// 角色与部门关系数据
		List<TenantRoleDept> tenantRoleDept = new ArrayList<>();
		TenantRoleDept _tenantRoleDept = new TenantRoleDept();
		_tenantRoleDept.setRoleId(roleId);
		_tenantRoleDept.setDeptId(deptId);
		tenantRoleDept.add(_tenantRoleDept);
		tenantRoleDeptMapper.batchRoleDept(tenantRoleDept);

		// 角色与菜单关系数据 暂时全部
		List<TenantMenu> tenantMenus = tenantMenuMapper.selectMenuList(null);
		List<TenantRoleMenu> tenantRoleMenu = new ArrayList<>();
		for (TenantMenu tenantMenu : tenantMenus) {
			TenantRoleMenu _tenantRoleMenu = new TenantRoleMenu();
			_tenantRoleMenu.setRoleId(roleId);
			_tenantRoleMenu.setMenuId(tenantMenu.getMenuId());
			tenantRoleMenu.add(_tenantRoleMenu);
		}
		tenantRoleMenuMapper.batchRoleMenu(tenantRoleMenu);

		return AjaxResult.success();
	}

	@Override
	public AjaxResult insertTenantUserInfoRole(CustomerInfoReq customerInfoReq, Long tenantUserId, Integer clientType) {
		LoginUser loginUserFinal = SecurityUtils.getLoginUser();

		String createBy = BusinessConstants.ROBOT;
		if (StringUtils.isNotNull(loginUserFinal.getTenantUser())) {
			createBy = loginUserFinal.getTenantUser().getUserName();
		}

		// 部门数据
		TenantDept tenantDept = new TenantDept();
		// 顶级部门为 0
		tenantDept.setParentId(0L);
		tenantDept.setDeptName(customerInfoReq.getCustomerName());
		tenantDept.setLeader(customerInfoReq.getCustomerName());
		tenantDept.setPhone(customerInfoReq.getContactPhone());
		tenantDept.setTenantId(customerInfoReq.getId());
		tenantDept.setCreateTime(DateUtils.getNowDate());
		tenantDept.setUpdateTime(DateUtils.getNowDate());
		tenantDeptMapper.insertDept(tenantDept);
		// 获取的插入的部门id
		Long deptId = tenantDept.getDeptId();

		// 初始化创建一个托运人
		if (tenantUserId == null || tenantUserId <= 0L) {
			String password = SecurityUtils.encryptPassword(UserConstants.INITIAL_PASSWORD, false);
			TenantUser tenantUser = new TenantUser();
			tenantUser.setCreateBy(BusinessConstants.ROBOT);
			tenantUser.setTenantId(customerInfoReq.getId());
			tenantUser.setUserName(customerInfoReq.getContactPhone());
			tenantUser.setPassword(password);// 默认初始密码
			tenantUser.setPwd(SecurityUtils.encryptPassword(tenantUser.getUserName() + password, true));
			tenantUserId = TextUtil.getTimeSequenceID(5);
			tenantUser.setUserId(tenantUserId);
			tenantUser.setNickName(customerInfoReq.getContact());
			tenantUser.setPhonenumber(customerInfoReq.getContactPhone());
			tenantUser.setDeptId(deptId);
			tenantUser.setDefaultProject(1);
			tenantUserMapper.insertUser(tenantUser);
		}

		// 岗位数据
		TenantPost tenantPost = new TenantPost();
		tenantPost.setPostCode("admin");
		tenantPost.setPostName("管理员");
		tenantPost.setStatus("0");
		tenantPost.setPostSort("1");
		tenantPost.setCreateBy(createBy);
		tenantPost.setUpdateBy(createBy);
		tenantPost.setTenantId(customerInfoReq.getId());
		tenantPost.setCreateTime(DateUtils.getNowDate());
		tenantPost.setUpdateTime(DateUtils.getNowDate());
		tenantPostMapper.insertPost(tenantPost);
		Long postId = tenantPost.getPostId();

		// 角色数据
		TenantRole tenantRole = new TenantRole();
		tenantRole.setRoleName("管理员");
		tenantRole.setRoleKey("admin");
		tenantRole.setStatus("0");
		tenantRole.setRoleSort("1");
		tenantRole.setCreateBy(createBy);
		tenantRole.setUpdateBy(createBy);
		tenantRole.setCreateTime(DateUtils.getNowDate());
		tenantRole.setUpdateTime(DateUtils.getNowDate());
		tenantRole.setTenantId(customerInfoReq.getId());
		tenantRoleMapper.insertRole(tenantRole);
		Long roleId = tenantRole.getRoleId();

		// 预设测试角色
		// TenantRole preTenantRole = new TenantRole();
		// preTenantRole.setRoleName("测试");
		// preTenantRole.setRoleKey("test");
		// preTenantRole.setStatus("0");
		// preTenantRole.setRoleSort("1");
		// preTenantRole.setCreateBy(createBy);
		// preTenantRole.setUpdateBy(createBy);
		// preTenantRole.setCreateTime(DateUtils.getNowDate());
		// preTenantRole.setUpdateTime(DateUtils.getNowDate());
		// preTenantRole.setTenantId(customerInfoReq.getId());
		// tenantRoleMapper.insertRole(preTenantRole);
		// Long presetRoleId = tenantRole.getRoleId();
		// setTenantRole(customerInfoReq, createBy);

		// 用户和岗位
		List<TenantUserPost> userPostList = new ArrayList<>();
		TenantUserPost tenantUserPost = new TenantUserPost();
		tenantUserPost.setUserId(tenantUserId);
		tenantUserPost.setPostId(postId);
		userPostList.add(tenantUserPost);
		tenantUserPostMapper.batchUserPost(userPostList);

		// 用户和角色
		List<TenantUserRole> userRoleList = new ArrayList<>();
		TenantUserRole tenantUserRole = new TenantUserRole();
		tenantUserRole.setUserId(tenantUserId);
		tenantUserRole.setRoleId(roleId);
		userRoleList.add(tenantUserRole);
		tenantUserRoleMapper.batchUserRole(userRoleList);

		// 预设测试角色
		// List<PresetRoles> rolesList = iPresetRolesService.listByClientTypeAndRoleName(clientType, "测试");
		// PresetRoles presetRoles = rolesList.get(0);
		// List<TenantUserRole> presetUserRoleList = new ArrayList<>();
		// TenantUserRole PresetTenantUserRole = new TenantUserRole();
		// PresetTenantUserRole.setUserId(tenantUserId);
		// PresetTenantUserRole.setRoleId(presetRoles.getRoleId());
		// presetUserRoleList.add(PresetTenantUserRole);
		// tenantUserRoleMapper.batchUserRole(presetUserRoleList);

		// 角色与部门关系数据
		List<TenantRoleDept> tenantRoleDept = new ArrayList<>();
		TenantRoleDept _tenantRoleDept = new TenantRoleDept();
		_tenantRoleDept.setRoleId(roleId);
		_tenantRoleDept.setDeptId(deptId);
		tenantRoleDept.add(_tenantRoleDept);
		tenantRoleDeptMapper.batchRoleDept(tenantRoleDept);

		/*// 角色与菜单关系数据 暂时全部

		PresetRolesMenuReq presetRolesMenuReq = new PresetRolesMenuReq();
		List<Long> listMenuIds = tenantMenus.stream().map(TenantMenu::getMenuId).collect(Collectors.toList());
		presetRolesMenuReq.setPresetRolesId(presetRoles.getRoleId());
		presetRolesMenuReq.setListMenuIds(listMenuIds);
		iPresetRolesMenuService.insertByPresetRolesId(presetRolesMenuReq);
		*/
		List<TenantMenu> tenantMenus = tenantMenuMapper.selectMenuList(null);
		List<TenantRoleMenu> tenantRoleMenu = new ArrayList<>();
		for (TenantMenu tenantMenu : tenantMenus) {
			TenantRoleMenu _tenantRoleMenu = new TenantRoleMenu();
			_tenantRoleMenu.setRoleId(roleId);
			_tenantRoleMenu.setMenuId(tenantMenu.getMenuId());
			tenantRoleMenu.add(_tenantRoleMenu);
		}
		tenantRoleMenuMapper.batchRoleMenu(tenantRoleMenu);

		return AjaxResult.success();

	}

	/**
	 * 审核 企业货主端信息
	 *
	 * @param customerInfoReq
	 * @return
	 */
	@Override
	public AjaxResult approveIdentity(CustomerInfoReq customerInfoReq) {
		LoginUser loginUser = SecurityUtils.getLoginUser();
		// 控制数据库修改数据是否成功
		int success;
		try {
			if (customerInfoReq.getApproveState() == null) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "请选择审核类型");
			}
			// 审核成功的话 直接简单粗暴 修改审核状态
			if (customerInfoReq.getApproveState() == 1) {
				customerInfoReq.setId(loginUser.getUserId());
				success = customerInfoMapperEx.updateCustomerInfo(customerInfoReq);
				if (success == 1)
					return AjaxResult.success(HttpStatus.SUCCESS, "审核通过成功");
				else
					return AjaxResult.success(HttpStatus.SUCCESS, "审核通过失败");
			}

			// 如果拒绝审核的话 修改货主表状态，以及插入审核历史记录表
			if (customerInfoReq.getApproveState() == 2) {
				success = customerInfoMapperEx.updateCustomerInfo(customerInfoReq);

				// 如果审核失败 直接返回错误，不牵扯其他子表，以防主子表数据不统一
				if (success == 0) {
					return AjaxResult.error(HttpStatus.BAD_REQUEST, "拒绝审核失败");
				}
				// 构造历史表实体类
				CustomerApproveHistory customerApproveHistory = new CustomerApproveHistory();
				customerApproveHistory.setId(TextUtil.getTimeSequenceID(5));
				customerApproveHistory.setCustomerId(customerInfoReq.getId());
				customerApproveHistory.setRejectReason(customerInfoReq.getApproveReason());
				customerApproveHistory.setRejectTime(DateUtils.getNowDate());
				customerApproveHistory.setCreateBy(loginUser.getUsername());
				customerApproveHistory.setUpdateBy(loginUser.getUsername());
				customerApproveHistoryMapper.insertCustomerApproveHistory(customerApproveHistory);
				return AjaxResult.success(HttpStatus.SUCCESS, "拒绝审核成功");
			}

			// 其他所有的状态一切拦截，不做任何处理！ 后续安全性可以考虑并发攻击，这里暂时不考虑

			return AjaxResult.success(HttpStatus.SUCCESS, "审核成功");
		} catch (Exception ex) {
			log.error(ex.toString());
			return AjaxResult.error(HttpStatus.BAD_REQUEST, ex.toString());
		}
	}

	/**
	 * 获取货主端 当前的审核信息 也就是详情信息
	 *
	 * @return
	 */
	@Override
	public AjaxResult customerInfoDetail() {
		try {
			CustomerInfoDetail customerInfoDetail = new CustomerInfoDetail();
			// 这里取的数据有可能是老的数据
			TenantUser _tenantUser = SecurityUtils.getLoginUser().getTenantUser();

			if (_tenantUser == null || _tenantUser.getTenantId() == null || _tenantUser.getTenantId() == 0)
				return AjaxResult.success("数据为空");

			// 刷新最新的数据
			TenantUser tenantUser = tenantUserMapper.selectUserByTenantId(_tenantUser.getTenantId());
			if (tenantUser == null)
				return AjaxResult.success("数据为空");

			// 反查审核状态
			CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(tenantUser.getTenantId());

			// 查询当前的审核历史记录
			CustomerApproveHistory customerApproveHistory = new CustomerApproveHistory();
			customerApproveHistory.setCustomerId(tenantUser.getTenantId());
			customerApproveHistory = customerApproveHistoryMapperEx.selectCustomerApproveHistoryByNew(customerApproveHistory);
			customerInfoDetail.setCustomerApproveHistory(customerApproveHistory);

			List<CustomerAttachmentInfo> customerFiles = customerAttachmentInfoService.customerFileList(customerInfo.getId());

			customerInfoDetail.setPhoneNumber(tenantUser.getPhonenumber());
			customerInfoDetail.setCustomerInfo(customerInfo);
			customerInfoDetail.setSysAttachmentInfoList(customerFiles);
			customerInfoDetail.setIsAllMakeCode(_tenantUser.getIsAllMakeCode());

			return AjaxResult.success(customerInfoDetail);
		} catch (Exception ex) {
			log.error(ex.toString());
			return AjaxResult.error(HttpStatus.ERROR, ex.toString());
		}
	}

	/**
	 * 货主PC端 企业管理详情
	 *
	 * @return
	 */
	@Override
	public AjaxResult selectCustomerInfoPCById() {
		CustomerInfoDetail customerInfoDetail = new CustomerInfoDetail();

		Long shipperId = SecurityUtils.getShipperId();
		if (shipperId == null || shipperId == 0) {
			return AjaxResult.error("租户信息有误");
		}
		// 企业详情
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(shipperId);
		customerInfoDetail.setCustomerInfo(customerInfo);
		// 企业附件
		List<CustomerAttachmentInfo> customerFiles = customerAttachmentInfoService.customerFileList(customerInfo.getId());
		customerInfoDetail.setSysAttachmentInfoList(customerFiles);
		// 合同信息
		CustomerInfoSlave customerInfoSlave = new CustomerInfoSlave();
		customerInfoSlave.setCustomerId(customerInfo.getId());
		List<CustomerInfoSlave> infoSlaves = customerInfoSlaveMapper.selectCustomerInfoSlaveList(customerInfoSlave);
		if (!infoSlaves.isEmpty()) {
			customerInfoDetail.setCustomerInfoSlave(infoSlaves.get(0));
		}

		return AjaxResult.success(customerInfoDetail);
	}

	@Resource
	private HuaXiaService huaXiaService;

	/**
	 * 货主PC端---企业管理修改
	 *
	 * @return
	 */
	@Override
	@Transactional
	public AjaxResult editCustomerInfoSlave(CustomerInfoDetail customerInfoDetail) {
		try {

			TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
			CustomerInfo customerInfo = customerInfoDetail.getCustomerInfo();
			CustomerInfoSlave customerInfoSlave = customerInfoDetail.getCustomerInfoSlave();
			List<CustomerAttachmentInfo> customerFiles = customerInfoDetail.getSysAttachmentInfoList();

			if (customerInfo.getId() == null || customerInfo.getId() == 0) {
				return AjaxResult.error("id为空,请重试");
			}
			if (StringUtils.isEmpty(customerInfo.getCustomerName())) {
				return AjaxResult.error("企业名称为空,请重试");
			}
			if (StringUtils.isEmpty(customerInfo.getCreditCode())) {
				return AjaxResult.error("社会统一信用代码为空,请重试");
			}
			CustomerInfo customerInfoData = customerInfoMapper.selectCustomerInfoById(customerInfo.getId());
			if (null == customerInfoData) {
				return AjaxResult.error("未查询到当前企业信息,请重试");
			}
			// 已开户客户不可修改社会统一信用代码
			if (4 == customerInfoData.getApproveState() && (!customerInfo.getCreditCode().equals(customerInfoData.getCreditCode()) || !customerInfo.getCustomerName()
					.equals(customerInfoData.getCustomerName()))) {
				return AjaxResult.error("托运人名称、社会统一信用代码不可编辑");
			}
			// 4.4.1新增法人身份证，姓名，附件，必填和二要素校验
			if (StringUtils.isBlank(customerInfo.getLegal())) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "法人为空");
			}
			if (StringUtils.isBlank(customerInfo.getLegalIdNo())) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "法人身份证为空");
			}
			// 法人姓名身份证二要素校验
			CommonResult<String> stringCommonResult = iPtmCertificateCheckService.realNameAuthentication(customerInfo.getLegal(), customerInfo.getLegalIdNo());
			if (CommonResult.isNotSuccess(stringCommonResult)) {
				return AjaxResult.error(HttpStatus.ERROR, "实名认证未通过");
			}
			// 附件必填校验：
			if (customerFiles.isEmpty()) {
				return AjaxResult.error("附件信息不能为空");
			}

			Boolean hasIDA = false;
			Boolean hasIDB = false;
			for (CustomerAttachmentInfo customerAttachmentInfo : customerFiles) {
				if (customerAttachmentInfo.getFileName().equals(FileNames.JP_ID_CARD_A)) {
					hasIDA = true;
				}
				if (customerAttachmentInfo.getFileName().equals(FileNames.JP_ID_CARD_B)) {
					hasIDB = true;
				}
			}
			if (!hasIDA) {
				return AjaxResult.error("法人身份证（人像面）不能为空");
			}
			if (!hasIDB) {
				return AjaxResult.error("法人身份证（国徽面）不能为空");
			}

			// 社会统一信用代码校验
			String code = juheService.getSocialCreditCodeByName(customerInfo.getCustomerName(), "");
			// 校验社会统一信用代码是否正确
			if (customerInfo.getCreditCode().equals(code)) {
				CustomerInfo _customerInfo = new CustomerInfo();
				_customerInfo.setCreditCode(code);
				// 查询库中是否有相同社会统一信用代码
				List<CustomerInfo> customerInfos = customerInfoMapper.selectCustomerInfoList(_customerInfo);
				if (StringUtils.isEmpty(customerInfos) || customerInfos.stream().anyMatch(f -> Objects.equals(f.getId(), customerInfo.getId()))) {
					// 修改公司信息
					customerInfoMapperEx.updateCustomerInfoEx(customerInfo);
					// 开户//没有开户信息才去开户
					CustomerAccount customerAccount = customerAccountMapper.selectCustomerAccountById(customerInfo.getId());
					if (customerAccount == null && StringUtils.isNotBlank(customerInfo.getCreditCode()) && StringUtils.isNotBlank(customerInfo.getCustomerName()) && StringUtils.isNotBlank(
							customerInfo.getContact()) && StringUtils.isNotBlank(customerInfo.getContactPhone()) && 4 != customerInfoData.getApproveState()) {
						AccOpenReq accOpenReq = new AccOpenReq();
						accOpenReq.setIdCode(customerInfo.getCreditCode()); // 开户证件号码
						accOpenReq.setName(customerInfo.getCustomerName()); // 开户名称
						accOpenReq.setContact(customerInfo.getContact()); // 开户联系人
						huaXiaService.openCustomerInfoAccount(accOpenReq);
					}
				} else {
					return AjaxResult.error("托运人已存在,不可重复添加,请联系技术咨询详情");
				}
			} else {
				return AjaxResult.error("企业信息不存在,请核实并正确填写");
			}

			customerAttachmentInfoService.deleteCustomerFiles(customerInfo.getId());
			for (CustomerAttachmentInfo customerFile : customerFiles) {
				customerFile.setId(TextUtil.getTimeSequenceID(5));
				customerFile.setRelationId(customerInfo.getId());
				customerFile.setCreateBy(tenantUser.getUserName());
				customerFile.setCreateTime(DateUtils.getNowDate());
				customerFile.setUpdateBy(tenantUser.getUserName());
				customerFile.setUpdateTime(DateUtils.getNowDate());
				customerAttachmentInfoService.insertCustomerAttachmentInfo(customerFile);
			}

			return AjaxResult.success();
		} catch (Exception ex) {
			log.error(ExceptionUtils.getStackTrace(ex));
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return AjaxResult.error(ex.getMessage());
		}
	}

	@Override
	public AjaxResult findById(Long id) {
		CustomerInfoEx customerInfo = customerInfoMapperEx.findById(id);

		// 根据id关联查询出未删除的附件信息
		List<CustomerAttachmentInfo> customerFiles = customerAttachmentInfoService.customerFileList(customerInfo.getId());
		if (!customerFiles.isEmpty()) {
			customerFiles.removeIf(file -> "平台协议".equals(file.getFileName()));
			customerInfo.setAttachment(customerFiles);
		}

		// 1.查询委托合同
		CustomerForwarderContract customerForwarderContract = new CustomerForwarderContract();
		customerForwarderContract.setCustomerId(id);
		customerForwarderContract.setState(1);// 只展示生效的委托合同
		customerForwarderContract.setFreightForwarderId(SecurityUtils.getFreightForwarderId());
		List<CustomerForwarderContractReq> customerForwarderContractReqList = customerForwarderContractMapper.selectContractList(customerForwarderContract);
		// 查询合同附件
		for (CustomerForwarderContractReq customerForwarderContractReq : customerForwarderContractReqList) {
			CustomerForwarderContractAttachmentInfo customerForwarderContractAttachmentInfo = new CustomerForwarderContractAttachmentInfo();
			customerForwarderContractAttachmentInfo.setRelationId(customerForwarderContractReq.getId());
			List<CustomerForwarderContractAttachmentInfo> customerForwarderContractAttachmentInfos = customerForwarderContractAttachmentInfoMapper.selectCustomerForwarderContractAttachmentInfoList(
					customerForwarderContractAttachmentInfo);
			customerForwarderContractReq.setCustomerForwarderContractAttachmentInfoList(customerForwarderContractAttachmentInfos);
		}

		if (!StringUtils.isEmpty(customerForwarderContractReqList)) {

			// 按 freightForwarderId 归类附件
			Map<Long, List<FreightForwarderAttachmentInfo>> groupedAttachments = new HashMap<>();
			for (CustomerForwarderContractReq customerForwarderContractReq : customerForwarderContractReqList) {
				List<FreightForwarderAttachmentInfo> freightForwarderAttachmentInfos = new ArrayList<>();
				FreightForwarderAttachmentInfo freightAttachment = new FreightForwarderAttachmentInfo();
				List<CustomerForwarderContractAttachmentInfo> customerForwarderContractAttachmentInfoList = customerForwarderContractReq.getCustomerForwarderContractAttachmentInfoList();
				if (!StringUtils.isEmpty(customerForwarderContractAttachmentInfoList)) {
					List<FreightForwarderAttachmentInfo> list = groupedAttachments.getOrDefault(customerForwarderContractReq.getFreightForwarderId(), new ArrayList<>());
					for (CustomerForwarderContractAttachmentInfo forwarderContractAttachmentInfo : customerForwarderContractAttachmentInfoList) {
						freightAttachment.setId(customerForwarderContractReq.getId());
						freightAttachment.setFileName(forwarderContractAttachmentInfo.getFileName());
						freightAttachment.setFileUrl(forwarderContractAttachmentInfo.getFileUrl());
						freightAttachment.setOriginalFileName(forwarderContractAttachmentInfo.getOriginalFileName());
						freightAttachment.setFileSize(forwarderContractAttachmentInfo.getFileSize());
						freightAttachment.setFileType(forwarderContractAttachmentInfo.getFileType());
						freightAttachment.setKeyValue(forwarderContractAttachmentInfo.getKeyValue());
						freightAttachment.setFreightForwarderId(customerForwarderContractReq.getFreightForwarderId());
						list.add(freightAttachment);
					}
					groupedAttachments.put(customerForwarderContractReq.getFreightForwarderId(), list);
				}
			}

			// 3. 查询网货信息组装数据
			List<FreightForwarderInfoEx> freightForwarderInfos = new ArrayList<>();
			for (Map.Entry<Long, List<FreightForwarderAttachmentInfo>> entry : groupedAttachments.entrySet()) {
				Long freightForwarderId = entry.getKey();
				List<FreightForwarderAttachmentInfo> attachments = entry.getValue();

				// 查询网货信息
				FreightForwarderInfo freightForwarderInfo = freightForwarderInfoMapper.selectFreightForwarderInfoById(freightForwarderId);
				if (freightForwarderInfo != null) {
					FreightForwarderInfoEx freightForwarderInfoEx = new FreightForwarderInfoEx();
					freightForwarderInfoEx.setId(freightForwarderInfo.getId());
					freightForwarderInfoEx.setName(freightForwarderInfo.getName());
					freightForwarderInfoEx.setAttachment(attachments);
					freightForwarderInfos.add(freightForwarderInfoEx);
				}
			}
			if (!StringUtils.isEmpty(freightForwarderInfos)) {
				customerInfo.setFreightForwarderInfos(freightForwarderInfos);
			}
		}

		// 服务id
		List<Long> serviceIds = customerServiceInfoMapperEx.getServiceIdByCustomerInfoId(customerInfo.getId());
		customerInfo.setServiceIds(serviceIds);

		// 托运人关联的网络货运人
		List<Long> list = forwarderCustomerRelationService.selectForwarderIdsByCustomerId(id);
		if (null != list && !list.isEmpty()) {
			List<FreightForwarderInfo> freightForwarderInfos = freightForwarderInfoMapper.selectFreightForwarderInfoByIds(list);
			customerInfo.setList(freightForwarderInfos);
		}
		customerInfo.setUserName(customerInfo.getUserName().split("_")[0]);
		return AjaxResult.success(customerInfo);
	}

	@Override
	public AjaxResult enrollFindById(Long id) {
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(id);
		CustomerInfoEx customerInfoEx = new CustomerInfoEx();
		BeanUtils.copyProperties(customerInfo, customerInfoEx);
		// 附件信息
		List<CustomerAttachmentInfo> customerFiles = customerAttachmentInfoService.customerFileList(customerInfo.getId());
		customerInfoEx.setAttachment(customerFiles);

		// 服务id
		List<Long> serviceIds = customerServiceInfoMapperEx.getServiceIdByCustomerInfoId(customerInfo.getId());
		customerInfoEx.setServiceIds(serviceIds);

		// 托运人关联的网络货运人
		List<Long> list = forwarderCustomerRelationService.selectForwarderIdsByCustomerId(id);
		if (null != list && !list.isEmpty()) {
			List<FreightForwarderInfo> freightForwarderInfos = freightForwarderInfoMapper.selectFreightForwarderInfoByIds(list);
			customerInfoEx.setList(freightForwarderInfos);
		}
		return AjaxResult.success(customerInfoEx);
	}

	@Override
	public List<CustomerInfoEx> getGroupCustomerInfoList(CustomerInfoEx customerInfo) {
		return customerInfoMapperEx.getGroupCustomerInfoList(customerInfo);
	}

	@Override
	public List<CustomerInfoEx> getSocialCodeCustomerInfoList() {
		return customerInfoMapperEx.getSocialCodeCustomerInfoList();
	}

	@Override
	public List<OptionCustomerRes> optionCustomerList(String customerName, Long freightForwarderId) {
		return customerInfoMapperEx.optionCustomerList(customerName, freightForwarderId);
	}

	@Override
	public List<CustomerInfo> selectWaybillCustomerInfoListByFreightForwarderId(CustomerInfo customerInfo) {
		return customerInfoMapperEx.selectWaybillCustomerInfoListByFreightForwarderId(customerInfo);
	}

	@Override
	public List<CustomerInfo> selectCustomerInfoDetail(CustomerInfo customerInfo) {
		return customerInfoMapper.selectCustomerInfoList(customerInfo);
	}

	@Override
	public AjaxResult setCustomerInfoState(CustomerInfo info) {
		return AjaxResult.success(customerInfoMapper.updateCustomerInfo(info));
	}

	/**
	 * 批量查询客户列表信息
	 *
	 * @param customerIds
	 * @return
	 */
	@Override
	public List<CustomerInfo> selectCustomerList(List<Long> customerIds) {
		if (null == customerIds || customerIds.isEmpty()) {
			return Collections.emptyList();
		}
		return customerInfoMapperEx.selectCustomerList(customerIds);
	}

	@Override
	public CommonResult<String> findNameById(String id) {

		String nextName = frameworkContractService.getNextName(Long.valueOf(id), SecurityUtils.getClientType());

		return CommonResult.success(nextName);
	}

	@Override
	public TenantUser getCustomerUser(CustomerVerifyEx customerVerifyEx) {
		TenantUser tenantUser;
		VxService service = VxUtils.decrtptVxService(null, customerVerifyEx);
		if (service == null || service.getPhoneNumber() == null) {
			throw new ServiceException("未获取到手机号，请尝试再次登录");
		}
		tenantUser = tenantUserMapper.checkPhoneAndCustomerID(service.getPhoneNumber(), customerVerifyEx.getTenantId());
		if (null == tenantUser || 0 == tenantUser.getTenantId()) {
			return null;
		}

		// 如果跟善道项目相关的用户不是从善道系统登录的，记录下来(临时添加，过段时间要删除的)
		if (redisCache.hasKey("sduser")) {
			String sdUser = redisCache.getCacheObject("sduser");
			String userName = service.getPhoneNumber() + "_" + customerVerifyEx.getTenantId();
			if (null != sdUser && sdUser.contains(userName)) {
				HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
				String domain = request.getServerName();
				String accerr = redisCache.getCacheObject("sduser_accerr");
				String sddomain = redisCache.getCacheObject("sddomain");
				if (!domain.contains(sddomain)) {
					accerr = StringUtils.isBlank(accerr) ? userName : accerr + "," + userName;
					String[] parts = accerr.split(",");
					Set<String> uniqueSet = new LinkedHashSet<>(Arrays.asList(parts));
					String result = String.join(",", uniqueSet);
					redisCache.setCacheObject("sduser_accerr", result);
				} else {
					if (StringUtils.isNotBlank(accerr)) {
						String result = Arrays.stream(accerr.split(",")).filter(s -> !s.equals(userName)) // 精确匹配
								.collect(Collectors.joining(","));
						redisCache.setCacheObject("sduser_accerr", result);
					}
				}
			}
		}

		// 获取手机号，通过手机号 查询货主端登录信息表是否存在
		CustomerVerify customerVerify = new CustomerVerify();
		customerVerify.setIphone(service.getPhoneNumber());
		customerVerify.setCustomerId(customerVerifyEx.getTenantId());
		List<CustomerVerify> customerVerifyList = customerVerifyMapper.selectCustomerVerifyList(customerVerify);
		// 小程序端托运人校验信息表，如果不存在就开始注册模式
		if (CommonUtil.isNullOrEmpty(customerVerifyList)) {
			addCustomerVerify(tenantUser, service, customerVerifyEx.getCustomerVerify());
		}
		return tenantUser;
	}

	@Override
	public List<CustomerInfo> selectCustomerInfoListByFreightForwarderId(CustomerInfo customerInfo) {
		return customerInfoMapperEx.selectCustomerInfoListByFreightForwarderId(customerInfo);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult productAddCustomerInfo(CustomerInfoReq req) {
		// 校验必填数据
		AjaxResult result = null;
		// 设置1L是为了过校验
		req.setFreightForwarderId(1L);
		ClientType clientType = CUSTOMER_CREATE_BY_OUT.equals(req.getResourceType()) ? ClientType.PLATFORM : SecurityUtils.getClientType();
		String nickName = CUSTOMER_CREATE_BY_OUT.equals(req.getResourceType()) ? "外部用户" : SecurityUtils.getNickname();
		if (ClientType.PLATFORM.equals(clientType) || ClientType.AGENT.equals(clientType)) {
			result = productVerify(req);
		} else if (ClientType.GROUP.equals(clientType)) {
			result = productVerify(req);
		}
		if (null == result || HttpStatus.ERROR == result.getCode()) {
			return result;
		}
		Date curDate = DateUtils.getNowDate();
		CustomerInfo customerInfo = new CustomerInfo();
		BeanUtils.copyProperties(req, customerInfo);
		// 保存客户信息
		Long customerId = StringUtils.getOrDefault(req.getId(), TextUtil.generateId());
		customerInfo.setId(customerId);
		customerInfo.setCustomerCode(CodeUtil.generateCustomerCode());// 客户编号
		customerInfo.setCreateBy(nickName);// 创建人
		customerInfo.setCreateTime(curDate);// 创建时间
		// 托运人不为网货人时，网货id字段为0
		customerInfo.setFreightForwarderId(0L);
		customerInfo.setResourceType(CUSTOMER_CREATE_BY_OUT);
		customerInfo.setApproveState(APPROVE_STATEAPING);
		customerInfo.setGroupType(BusinessConstants.CUSTOMER_OUT_GROUP);

		CustomerInfo customerInfoQuery = new CustomerInfo();
		customerInfoQuery.setCreditCode(req.getCreditCode());
		List<CustomerInfo> list = customerInfoMapper.selectCustomerInfoList(customerInfoQuery);
		if (StringUtils.isEmpty(list)) {
			// 只有客户信息表没有数据的时候才新增
			customerInfoMapper.insertCustomerInfo(customerInfo);
			// 保存附件信息
			List<CustomerAttachmentInfo> attachmentList = req.getAttachment();
			if (null != attachmentList && !attachmentList.isEmpty()) {
				for (CustomerAttachmentInfo customerFile : attachmentList) {
					customerFile.setId(TextUtil.getTimeSequenceID(5));
					customerFile.setRelationId(customerId);// 客户附件关联客户id
					customerFile.setCreateBy(nickName);// 创建人
					customerFile.setCreateTime(curDate);// 创建时间
					customerFile.setUpdateBy(nickName);// 修改人
					customerFile.setUpdateTime(curDate);// 修改时间
					customerAttachmentInfoService.insertCustomerAttachmentInfo(customerFile);
				}
			}
			// 不存在客户信息，首次新增时添加关联关系
			// if (!ClientType.GROUP.equals(clientType)) {
			// FreightForwarderCustomerRelation forwarderCustomerRelation = new FreightForwarderCustomerRelation();
			// forwarderCustomerRelation.setFreightForwarderId(freightForwarderId);
			// forwarderCustomerRelation.setCustomerId(customerId);
			// forwarderCustomerRelation.setCreateBy(nickName);
			// forwarderCustomerRelation.setCreateTime(DateUtils.getNowDate());
			// forwarderCustomerRelationService.insertFreightForwarderCustomerRelation(forwarderCustomerRelation);
			// }

			// 保存企业增值服务服务
			{
				List<Long> serviceIds = req.getServiceIds() == null ? new ArrayList<>() : req.getServiceIds();
				// 若参数中未有增值服务，基础服务为默认项
				if (serviceIds.size() == 0) {
					serviceIds.add(Long.parseLong(CustomerServiceEnum.BASIS.getCode()));
				}
				// 如果选择了定位服务，则将车辆最新位置查询的服务作为定位服务项
				for (Long serviceId : serviceIds) {
					if (SystemService.LOCATE.equals(serviceId)) {
						if (!serviceIds.contains(SystemService.Locate.REALTIME.val())) {
							serviceIds.add(SystemService.Locate.REALTIME.val());
							break;
						}
					}
				}
				for (Long serviceId : serviceIds) {
					CustomerServiceInfo serviceInfo = new CustomerServiceInfo();
					Long customerServiceId = TextUtil.getTimeSequenceID(5);
					serviceInfo.setId(customerServiceId);
					serviceInfo.setCustomerId(customerId);
					serviceInfo.setCustomerName(customerInfo.getCustomerName());
					serviceInfo.setOpenState(1);
					serviceInfo.setCustomerServiceId(serviceId);
					serviceInfo.setCreateBy(nickName);// 创建人
					serviceInfo.setCreateTime(curDate);// 创建时间
					customerServiceInfoMapper.insertCustomerServiceInfo(serviceInfo);
				}
			}
		} else {
			if (list.get(0).getApproveState() == 1) {
				return AjaxResult.error(HttpStatus.POP_ERROR, "您已提交过注册申请，无需重复提交");
			} else if (list.get(0).getApproveState() == 3) {
				CustomerInfo customerInfo1 = list.get(0);
				Long id = customerInfo1.getId();
				BeanUtils.copyProperties(req, customerInfo1);
				customerInfo1.setApproveState(1);
				customerInfo1.setId(id);
				customerInfo1.setFreightForwarderId(0L);
				// CustomerInfo customerInfo1 = list.get(0);
				// Long id = customerInfo1.getId();
				// customerInfo1.setCustomerName(req.getCustomerName());
				// customerInfo1.setCreditCode(req.getCreditCode());
				// customerInfo1.setContact(req.getContact());
				// customerInfo1.setLegal(req.getLegal());
				// customerInfo1.setContactPhone(req.getContactPhone());
				// customerInfo1.setTelephone(req.getTelephone());
				// customerInfo1.setUnitAddress(req.getUnitAddress());
				// customerInfo1.setBank(req.getBank());
				// customerInfo1.setBankAccount(req.getBankAccount());
				//// BeanUtils.copyProperties(req, customerInfo1);
				// customerInfo1.setApproveState(1);
				// customerInfo1.setId(id);
				// customerInfo1.setFreightForwarderId(0L);
				customerInfoMapper.updateCustomerInfoByProduct(customerInfo1);
				// customerInfoMapper.updateCustomerInfo(customerInfo1);
				return AjaxResult.error(HttpStatus.POP_ERROR, "您的申请已提交，请耐心等到审核结果！");
			} else {
				return AjaxResult.error(HttpStatus.POP_ERROR, "您已跟车联签约，无需重复注册，可直接联系业务员");
			}
			// 客户信息表存在数据，允许继续添加，但只绑定关联关系,并且数据允许变更
			// 保存网络货运人关联关系
			// if (!ClientType.GROUP.equals(clientType)) {
			// //客户信息表 社会统一信用代码相同的只可能存在一条数据所以直接取列表第一条，取出id
			// //customerIdExist:数据库里存在的客户id
			// Long customerIdExist = list.get(0).getId();
			// //查询关联关系表，如果已经有关联关系了，返回错误
			// FreightForwarderCustomerRelation freightForwarderCustomerRelation = new FreightForwarderCustomerRelation();
			// freightForwarderCustomerRelation.setCustomerId(customerIdExist);
			// freightForwarderCustomerRelation.setFreightForwarderId(freightForwarderId);
			// List<FreightForwarderCustomerRelation> freightForwarderCustomerRelations = forwarderCustomerRelationService.selectFreightForwarderCustomerRelationList(freightForwarderCustomerRelation);
			// if (freightForwarderCustomerRelations.size() == 0){
			//// customerInfo.setId(customerIdExist);
			//// customerInfoMapper.updateCustomerInfo(customerInfo);
			////
			//// FreightForwarderCustomerRelation forwarderCustomerRelation = new FreightForwarderCustomerRelation();
			//// forwarderCustomerRelation.setFreightForwarderId(freightForwarderId);
			//// forwarderCustomerRelation.setCustomerId(customerIdExist);
			//// forwarderCustomerRelation.setCreateBy(nickName);
			//// forwarderCustomerRelation.setCreateTime(DateUtils.getNowDate());
			//// forwarderCustomerRelationService.insertFreightForwarderCustomerRelation(forwarderCustomerRelation);
			// }else{

			// }
			// }
		}

		// 外部创建，到此结束
		if (CUSTOMER_CREATE_BY_OUT.equals(req.getResourceType())) {
			return AjaxResult.success("新增托运人成功", customerId);
		}

		// 删除redis缓存
		redisCache.deleteObject(CacheConstants.NETWORK_CUSTOMER_INFO + customerInfo.getCreditCode());
		return AjaxResult.success("新增托运人成功", customerId);
	}

	/**
	 * 数据校验
	 *
	 * @param req
	 * @return
	 */
	private AjaxResult verify(CustomerInfoReq req) {
		if (null == req) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "数据对象为空");
		}
		BeanUtils.beanAttributeValueTrim(req);// 去掉该对象中string字段前后空格
		if (StringUtils.isBlank(req.getCustomerName())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "客户名称为空");
		}
		if (StringUtils.isBlank(req.getCreditCode())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "统一社会信用代码为空");
		}
		if (StringUtils.isBlank(req.getContact())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "联系人为空");
		}
		// 联系方式去空格
		req.setContactPhone(req.getContactPhone().replace(" ", ""));
		if (StringUtils.isBlank(req.getContactPhone())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "联系方式为空");
		}
		if (CUSTOMER_CREATE_BY_OUT.equals(req.getResourceType())) {
			if (CommonUtil.isEmptyOrZero(req.getFreightForwarderId())) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "网络货运人为空");
			}
		} else {
			// 校验开票信息
			if (StringUtils.isBlank(req.getBillHead())) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "发票抬头为空");
			}
			if (StringUtils.isBlank(req.getTaxNo())) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "纳税人识别号为空");
			}
		}
		if (StringUtils.isBlank(req.getTaxNo())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "纳税人识别号为空");
		}
		if (StringUtils.isBlank(req.getUnitAddress())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "单位地址为空");
		}
		if (StringUtils.isBlank(req.getTelephone())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "电话号码为空");
		}
		// 电话号去空格
		req.setTelephone(req.getTelephone().replace(" ", ""));

		// 校验第一位是否为0 为0则是电话不是则是手机号
		if (!req.getTelephone().startsWith("0")) {
			if (!ValidateUtils.checkMobilePhone(req.getTelephone())) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "请填写正确的电话号码");
			}
		} else {
			if (!ValidateUtils.checkTelephone(req.getTelephone())) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "请填写正确的电话号码");
			}
		}

		if (req.getRemark() != null && req.getRemark().length() > 255) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "备注不能大于255个字");
		}
		// 此处已去掉合同信息校验

		// 按统一社会信用代码查询有没有重复的
		CustomerInfo customerInfo = new CustomerInfo();
		customerInfo.setCreditCode(req.getCreditCode());
		List<CustomerInfo> list = customerInfoMapper.selectCustomerInfoList(customerInfo);
		if (null != list && !list.isEmpty()) {
			// 新增时
			if (null == req.getId()) {
				// 不同税源地添加同一个公司要能添加成功，不校验统一社会信用代码是否重复，在校验外添加关联关系
				// return AjaxResult.error(HttpStatus.BAD_REQUEST, "统一社会信用代码已存在");
			} else {
				// 编辑时
				if (req.getId().longValue() != list.get(0).getId().longValue()) {
					return AjaxResult.error(HttpStatus.BAD_REQUEST, "统一社会信用代码已存在");
				}
			}
		}
		// 按客户名称查询有没有重复的
		customerInfo = new CustomerInfo();
		customerInfo.setCustomerName(req.getCustomerName());
		list = customerInfoMapperEx.selectCustomerInfoList(customerInfo);
		if (null != list && !list.isEmpty()) {
			// 新增时
			if (null == req.getId()) {
				// return AjaxResult.error(HttpStatus.BAD_REQUEST, "客户名称已存在");
			} else {
				// 编辑时
				if (req.getId().longValue() != list.get(0).getId().longValue()) {
					return AjaxResult.error(HttpStatus.BAD_REQUEST, "客户名称已存在");
				}
			}
		}

		// 新增时，查询手机号是否已被注册
		/*if (null != req.getId()) {
			TenantUserEx user = new TenantUserEx();
			user.setUserName(req.getContactPhone());
			user.setPhonenumber(req.getContactPhone());
			user.setDelFlag(String.valueOf(BusinessConstants.STATE_ENABLE));
			List<TenantUser> userList = tenantUserMapper.getAllUserList(user);
			if (StringUtils.isNotEmpty(userList)) {
				TenantUser userDb = userList.get(0);
				CustomerInfo customerInfoDb = customerInfoMapper.selectCustomerInfoById(userDb.getTenantId());
				if (customerInfoDb == null) {
					return AjaxResult.error(HttpStatus.BAD_REQUEST, " 该手机号已被用户注册，请更换联系方式");
				}
				return AjaxResult.error(HttpStatus.BAD_REQUEST, req.getContactPhone() + " 用户已被 " + customerInfoDb.getCustomerName() + " 注册，请更换联系方式");
			}
		}*/
		// 银行卡号去除空格
		if (req.getBankAccount() != null) {
			req.setBankAccount(req.getBankAccount().replace(" ", ""));
		}
		// 调用聚合数据，查询公司名称、统一社会信用代码是否对应
		String creditCodeByName = juheService.getSocialCreditCodeByName(req.getCustomerName(), req.getCreditCode());
		if (StringUtils.isEmpty(creditCodeByName) || !creditCodeByName.equals(req.getCreditCode())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "托运人名称、统一社会信用代码不匹配，请确认");
		}
		// 4.4.1新增法人身份证，姓名，附件，必填和二要素校验
		if (StringUtils.isBlank(req.getLegal())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "法人为空");
		}
		if (StringUtils.isBlank(req.getLegalIdNo())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "法人身份证为空");
		}
		// 法人姓名身份证二要素校验
		CommonResult<String> stringCommonResult = iPtmCertificateCheckService.realNameAuthentication(req.getLegal(), req.getLegalIdNo());
		if (CommonResult.isNotSuccess(stringCommonResult)) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "实名认证未通过");
		}
		// 附件必填校验：
		List<CustomerAttachmentInfo> attachment = req.getAttachment();
		if (attachment.isEmpty()) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "附件信息不能为空");
		}

		Boolean hasIDA = false;
		Boolean hasIDB = false;
		for (CustomerAttachmentInfo customerAttachmentInfo : attachment) {
			if (customerAttachmentInfo.getFileName().equals(FileNames.JP_ID_CARD_A)) {
				hasIDA = true;
			}
			if (customerAttachmentInfo.getFileName().equals(FileNames.JP_ID_CARD_B)) {
				hasIDB = true;
			}
		}
		if (!hasIDA) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "法人身份证（人像面）不能为空");
		}
		if (!hasIDB) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "法人身份证（国徽面）不能为空");
		}

		return AjaxResult.success(HttpStatus.SUCCESS, BusinessConstants.SUCCESS_DESC);
	}

	// 集团业务端审核托运人
	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult approveIdentityByGroup(CustomerInfoReq customerInfoReq) {
		// 控制数据库修改数据是否成功
		int success;
		try {
			CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(customerInfoReq.getId());
			CustomerInfo customerInfoEx = new CustomerInfo();
			BeanUtils.copyProperties(customerInfo, customerInfoEx);
			customerInfo.setApproveState(customerInfoReq.getApproveState());
			Long freightForwarderId = customerInfoReq.getFreightForwarderId();
			BeanUtils.copyProperties(customerInfo, customerInfoReq);
			if (customerInfoReq.getId() == null) {
				return AjaxResult.error(HttpStatus.ERROR, "请选择托运人");
			}
			if (customerInfoReq.getApproveState() == null) {
				return AjaxResult.error(HttpStatus.ERROR, "请选择审核类型");
			}
			if (StringUtils.isEmpty(customerInfoReq.getCustomerName())) {
				return AjaxResult.error(HttpStatus.ERROR, "公司名称为空");
			}
			if (StringUtils.isEmpty(customerInfoReq.getCreditCode())) {
				return AjaxResult.error(HttpStatus.ERROR, "公司统一社会信用代码为空");
			}
			if (!ValidateUtils.checkUnifiedSocialCreditCode(customerInfoReq.getCreditCode())) {
				return AjaxResult.error("托运人工商信息校验未通过");
			}
			if (!ValidateUtils.checkMobilePhone(customerInfoReq.getContactPhone())) {
				return AjaxResult.error(HttpStatus.ERROR, "请填写正确的联系方式");
			}
			// log.info("托运人工商信息校验开始，公司名称：{}，社会统一信用代码：{}", customerInfoReq.getCustomerName(), customerInfoReq.getCreditCode());
			// 调用聚合数据，查询公司名称、统一社会信用代码是否对应
			// String creditCodeByName = juheService.getSocialCreditCodeByName(customerInfoReq.getCustomerName());
			// log.info("托运人工商信息校验结束，结果：{}", creditCodeByName);
			// if (!creditCodeByName.equals(customerInfoReq.getCreditCode())) {
			// return AjaxResult.error("托运人名称、统一社会信用代码不匹配，请确认");
			// }
			// 审核成功的话 直接简单粗暴 修改审核状态
			if (customerInfoReq.getApproveState() == 2) {
				if (customerInfoReq.getFreightForwarderId() == null) {
					return AjaxResult.error(HttpStatus.ERROR, "请选择网络货运人");
				}
				if (2 == customerInfoEx.getApproveState() || 4 == customerInfoEx.getApproveState()) {
					return AjaxResult.error(HttpStatus.ERROR, "该托运人已经审核通过，请勿重复审核");
				}
				success = customerInfoMapperEx.updateCustomerInfo(customerInfoReq);
				if (success == 1) {
					// 随机生成12位大小写加数字密码
					String initPassword = OnlineSynchronousDataService.generateMixedCasePassword();
					// 初始化一个管理员账号
					this.insertTenantUserInfo(customerInfoReq, initPassword);
					// 初始化金融服务管理员账号
					this.insertFinanceUserInfo(customerInfoReq, initPassword);
					FreightForwarderCustomerRelation forwarderCustomerRelation = new FreightForwarderCustomerRelation();
					forwarderCustomerRelation.setFreightForwarderId(freightForwarderId);
					forwarderCustomerRelation.setCustomerId(customerInfoReq.getId());
					List<FreightForwarderCustomerRelation> freightForwarderCustomerRelations = forwarderCustomerRelationService.selectFreightForwarderCustomerRelationList(forwarderCustomerRelation);
					if (freightForwarderCustomerRelations.isEmpty()) {
						forwarderCustomerRelation.setCreateBy(SecurityUtils.getUsername());
						forwarderCustomerRelation.setCreateTime(DateUtils.getNowDate());
						forwarderCustomerRelationService.insertFreightForwarderCustomerRelation(forwarderCustomerRelation);
					}
					// 构造历史表实体类
					CustomerApproveHistory customerApproveHistory = new CustomerApproveHistory();
					customerApproveHistory.setId(TextUtil.getTimeSequenceID(5));
					customerApproveHistory.setCustomerId(customerInfoReq.getId());
					customerApproveHistory.setRejectReason(customerInfoReq.getApproveReason());
					customerApproveHistory.setRejectTime(DateUtils.getNowDate());
					customerApproveHistory.setCreateBy(SecurityUtils.getUsername());
					customerApproveHistory.setUpdateBy(SecurityUtils.getUsername());
					customerApproveHistoryMapper.insertCustomerApproveHistory(customerApproveHistory);
					return AjaxResult.success(HttpStatus.SUCCESS, "审核通过成功");
				} else {
					return AjaxResult.success(HttpStatus.SUCCESS, "审核通过失败");
				}
			}
			// 如果拒绝审核的话 修改货主表状态，以及插入审核历史记录表
			if (customerInfoReq.getApproveState() == 3) {
				if (2 == customerInfoEx.getApproveState() || 4 == customerInfoEx.getApproveState()) {
					return AjaxResult.error(HttpStatus.ERROR, "该托运人已经审核通过，请勿重复审核");
				}
				success = customerInfoMapperEx.updateCustomerInfo(customerInfoReq);
				// 如果审核失败 直接返回错误，不牵扯其他子表，以防主子表数据不统一
				if (success == 0) {
					return AjaxResult.error(HttpStatus.ERROR, "拒绝审核失败");
				}
				// 构造历史表实体类
				CustomerApproveHistory customerApproveHistory = new CustomerApproveHistory();
				customerApproveHistory.setId(TextUtil.getTimeSequenceID(5));
				customerApproveHistory.setCustomerId(customerInfoReq.getId());
				customerApproveHistory.setRejectReason(customerInfoReq.getApproveReason());
				customerApproveHistory.setRejectTime(DateUtils.getNowDate());
				customerApproveHistory.setCreateBy(SecurityUtils.getUsername());
				customerApproveHistory.setUpdateBy(SecurityUtils.getUsername());
				customerApproveHistoryMapper.insertCustomerApproveHistory(customerApproveHistory);
				return AjaxResult.success(HttpStatus.SUCCESS, "拒绝审核成功");
			}
			// 其他所有的状态一切拦截，不做任何处理！ 后续安全性可以考虑并发攻击，这里暂时不考虑
			return AjaxResult.success(HttpStatus.SUCCESS, "审核成功");
		} catch (Exception ex) {
			log.error(ex.toString());
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			return AjaxResult.error(HttpStatus.ERROR, ex.toString());
		}
	}

	@Override
	public List<CustomerInfoEx> getReviewedGroupCustomerInfoList(CustomerInfoEx customerInfo, HttpServletRequest request) {
		if (null != request.getHeader("Authorization")) {
			customerInfo.setResourceType(4);
		}
		List<CustomerInfoEx> customerInfoList = customerInfoMapperEx.getReviewedGroupCustomerInfoList(customerInfo);
		// List<CustomerInfoEx> customerInfoList = customerInfoMapperEx.getGroupCustomerInfoList(customerInfo);
		for (CustomerInfoEx customerInfoEx : customerInfoList) {
			if (customerInfoEx.getApproveState() == 3) {
				CustomerApproveHistory customerApproveHistory = new CustomerApproveHistory();
				customerApproveHistory.setCustomerId(customerInfoEx.getId());
				List<CustomerApproveHistory> customerApproveHistories = customerApproveHistoryMapper.selectCustomerApproveHistoryList(customerApproveHistory);
				customerInfoEx.setCustomerApproveHistory(customerApproveHistories);
			}
		}
		return customerInfoList;
	}

	// 内置金融物流服务角色
	private void setFinanceRole(CustomerInfoReq customerInfoReq, Long financeUserId) {
		List<PresetRoles> presetRoles = iPresetRolesService.selectPresetRolesList(ClientType.FINANCE.val());

		AtomicInteger i = new AtomicInteger(1);
		// List<FinanceUserRole> financeUserRoles = new ArrayList<FinanceUserRole>();
		presetRoles.forEach(l -> {
			// 生成内置角色
			FinanceRole financeRole = new FinanceRole();
			Long financeRoleId = TextUtil.getTimeSequenceID(5);
			financeRole.setRoleId(financeRoleId);
			financeRole.setRoleName(l.getRoleName());
			financeRole.setRoleKey(RandomStringUtils.randomAlphanumeric(8));
			financeRole.setStatus("0");
			financeRole.setRoleSort(String.valueOf(i.getAndIncrement()));
			financeRole.setCreateBy(BusinessConstants.ROBOT);
			financeRole.setUpdateBy(BusinessConstants.ROBOT);
			financeRole.setCreateTime(DateUtils.getNowDate());
			financeRole.setUpdateTime(DateUtils.getNowDate());
			financeRole.setCustomerId(customerInfoReq.getId());
			financeRole.setPresetRoleId(l.getRoleId());
			financeRole.setRemark(StringUtils.getOrDefault(l.getRemark(), "系统预置角色"));
			financeRoleMapper.insertRole(financeRole);
			// 设置内置角色菜单
			List<PresetRolesMenu> presetMenus = iPresetRolesMenuService.getByPresetRolesId(l.getRoleId());
			List<FinanceRoleMenu> roleMenus = presetMenus.stream().map(m -> new FinanceRoleMenu(financeRoleId, m.getMenuId())).collect(Collectors.toList());
			financeRoleMenuMapper.batchRoleMenu(roleMenus);

			// financeUserRoles.add(new FinanceUserRole(financeUserId, financeRoleId));
		});
		// 绑定当前创建用户与角色关系
		// financeUserRoleMapper.batchUserRole(financeUserRoles);

	}

	public List<CustomerVo> getCustomer() {
		return customerInfoMapper.getCustomer();
	}

	public static boolean isObjectNotEmpty(Object obj) throws IllegalAccessException {
		// 使用反射获取对象的所有字段
		Field[] fields = obj.getClass().getDeclaredFields();

		// 遍历每个字段
		for (Field field : fields) {
			field.setAccessible(true); // 设置字段可访问

			// 获取字段值
			Object value = field.get(obj);

			// 如果存在任何一个字段为 null，则返回 false
			if (value != null) {
				return true;
			}
		}

		return false;
	}

	public List<CustomerInfo> selectCustomerServiceInfoByCustomerIds(List<Long> customerIds) {
		return customerInfoMapperEx.selectCustomerServiceInfoByCustomerIds(customerIds);
	}

	@Override
	public AjaxResult addCustomerInfoByForwarder(CustomerInfoReq req) {
		ClientType clientType = CUSTOMER_CREATE_BY_OUT.equals(req.getResourceType()) ? ClientType.PLATFORM : SecurityUtils.getClientType();
		String nickName = CUSTOMER_CREATE_BY_OUT.equals(req.getResourceType()) ? "外部用户" : SecurityUtils.getNickname();
		String initPassword = OnlineSynchronousDataService.generateMixedCasePassword();
		Date curDate = DateUtils.getNowDate();
		CustomerInfo customerInfo = new CustomerInfo();
		BeanUtils.copyProperties(req, customerInfo);
		// 保存客户信息
		Long customerId = StringUtils.getOrDefault(req.getId(), TextUtil.generateId());
		Long freightForwarderId = req.getFreightForwarderId();
		customerInfo.setId(customerId);
		customerInfo.setCustomerCode(CodeUtil.generateCustomerCode());// 客户编号
		customerInfo.setCreateBy(nickName);// 创建人
		customerInfo.setCreateTime(curDate);// 创建时间
		// 托运人不为网货人时，网货id字段为0
		customerInfo.setFreightForwarderId(0L);

		if (CUSTOMER_CREATE_BY_OUT.equals(req.getResourceType())) {
			customerInfo.setResourceType(CUSTOMER_CREATE_BY_OUT);
			customerInfo.setApproveState(APPROVE_STATEAPING);
			customerInfo.setGroupType(BusinessConstants.CUSTOMER_OUT_GROUP);
		} else if (ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
			freightForwarderId = SecurityUtils.getLoginUser().getUser().getFreightForwarderId();
			customerInfo.setResourceType(BusinessConstants.CUSTOMER_CREATE_BY_PLATFROM);
			customerInfo.setApproveState(BusinessConstants.APPROVE_STATEAP);
			customerInfo.setGroupType(BusinessConstants.CUSTOMER_OUT_GROUP);
		} else if (ClientType.GROUP.equals(SecurityUtils.getClientType())) {
			// 此时集团端创建的为网络货运人对应的托运人，网货id字段有值
			customerInfo.setFreightForwarderId(freightForwarderId);
			customerInfo.setResourceType(BusinessConstants.CUSTOMER_CREATE_BY_GROUP);
			customerInfo.setApproveState(BusinessConstants.APPROVE_STATEAP);
			customerInfo.setGroupType(BusinessConstants.CUSTOMER_IN_GROUP);
		} else if (ClientType.AGENT.equals(SecurityUtils.getClientType())) {
			customerInfo.setResourceType(BusinessConstants.CUSTOMER_CREATE_BY_AGENT);
			customerInfo.setApproveState(BusinessConstants.APPROVE_STATEAP);
			customerInfo.setGroupType(BusinessConstants.CUSTOMER_IN_GROUP);
		}

		CustomerInfo customerInfoQuery = new CustomerInfo();
		customerInfoQuery.setCreditCode(req.getCreditCode());
		List<CustomerInfo> list = customerInfoMapper.selectCustomerInfoList(customerInfoQuery);
		if (StringUtils.isEmpty(list)) {
			// 只有客户信息表没有数据的时候才新增
			customerInfoMapper.insertCustomerInfo(customerInfo);
			// 保存附件信息
			List<CustomerAttachmentInfo> attachmentList = req.getAttachment();
			if (null != attachmentList && !attachmentList.isEmpty()) {
				for (CustomerAttachmentInfo customerFile : attachmentList) {
					customerFile.setId(TextUtil.getTimeSequenceID(5));
					customerFile.setRelationId(customerId);// 客户附件关联客户id
					customerFile.setCreateBy(nickName);// 创建人
					customerFile.setCreateTime(curDate);// 创建时间
					customerFile.setUpdateBy(nickName);// 修改人
					customerFile.setUpdateTime(curDate);// 修改时间
					customerAttachmentInfoService.insertCustomerAttachmentInfo(customerFile);
				}
			}
			// 不存在客户信息，首次新增时添加关联关系
			if (!ClientType.GROUP.equals(clientType)) {
				FreightForwarderCustomerRelation forwarderCustomerRelation = new FreightForwarderCustomerRelation();
				forwarderCustomerRelation.setFreightForwarderId(freightForwarderId);
				forwarderCustomerRelation.setCustomerId(customerId);
				forwarderCustomerRelation.setCreateBy(nickName);
				forwarderCustomerRelation.setCreateTime(DateUtils.getNowDate());
				forwarderCustomerRelationService.insertFreightForwarderCustomerRelation(forwarderCustomerRelation);
			}

			CustomerInfoReq customerInfoReq = new CustomerInfoReq();
			BeanUtils.copyProperties(customerInfo, customerInfoReq);
			// 初始化一个管理员账号
			this.insertTenantUserInfo(customerInfoReq, initPassword);
			// 初始化金融服务管理员账号
			this.insertFinanceUserInfo(customerInfoReq, initPassword);

			// 保存企业增值服务服务
			{
				List<Long> serviceIds = req.getServiceIds() == null ? new ArrayList<>() : req.getServiceIds();
				// 若参数中未有增值服务，基础服务为默认项
				if (serviceIds.size() == 0) {
					serviceIds.add(Long.parseLong(CustomerServiceEnum.BASIS.getCode()));
				}
				// 如果选择了定位服务，则将车辆最新位置查询的服务作为定位服务项
				for (Long serviceId : serviceIds) {
					if (SystemService.LOCATE.equals(serviceId)) {
						if (!serviceIds.contains(SystemService.Locate.REALTIME.val())) {
							serviceIds.add(SystemService.Locate.REALTIME.val());
							break;
						}
					}
				}
				for (Long serviceId : serviceIds) {
					CustomerServiceInfo serviceInfo = new CustomerServiceInfo();
					Long customerServiceId = TextUtil.getTimeSequenceID(5);
					serviceInfo.setId(customerServiceId);
					serviceInfo.setCustomerId(customerId);
					serviceInfo.setCustomerName(customerInfo.getCustomerName());
					serviceInfo.setOpenState(1);
					serviceInfo.setCustomerServiceId(serviceId);
					serviceInfo.setCreateBy(nickName);// 创建人
					serviceInfo.setCreateTime(curDate);// 创建时间
					customerServiceInfoMapper.insertCustomerServiceInfo(serviceInfo);
				}
			}
		} else {
			// 客户信息表存在数据，允许继续添加，但只绑定关联关系,并且数据允许变更
			// 保存网络货运人关联关系
			if (!ClientType.GROUP.equals(clientType)) {
				// 客户信息表 社会统一信用代码相同的只可能存在一条数据所以直接取列表第一条，取出id
				// customerIdExist:数据库里存在的客户id
				Long customerIdExist = list.get(0).getId();
				// 查询关联关系表，如果已经有关联关系了，返回错误
				FreightForwarderCustomerRelation freightForwarderCustomerRelation = new FreightForwarderCustomerRelation();
				freightForwarderCustomerRelation.setCustomerId(customerIdExist);
				freightForwarderCustomerRelation.setFreightForwarderId(freightForwarderId);
				List<FreightForwarderCustomerRelation> freightForwarderCustomerRelations = forwarderCustomerRelationService.selectFreightForwarderCustomerRelationList(
						freightForwarderCustomerRelation);
				if (freightForwarderCustomerRelations.size() == 0) {
					customerInfo.setId(customerIdExist);
					customerInfo.setApproveState(list.get(0).getApproveState());
					customerInfoMapper.updateCustomerInfo(customerInfo);

					FreightForwarderCustomerRelation forwarderCustomerRelation = new FreightForwarderCustomerRelation();
					forwarderCustomerRelation.setFreightForwarderId(freightForwarderId);
					forwarderCustomerRelation.setCustomerId(customerIdExist);
					forwarderCustomerRelation.setCreateBy(nickName);
					forwarderCustomerRelation.setCreateTime(DateUtils.getNowDate());
					forwarderCustomerRelationService.insertFreightForwarderCustomerRelation(forwarderCustomerRelation);
				} else {
					return AjaxResult.error(HttpStatus.BAD_REQUEST, "托运人已存在，请勿重复添加");
				}
			}
		}

		// 外部创建，到此结束
		if (CUSTOMER_CREATE_BY_OUT.equals(req.getResourceType())) {
			return AjaxResult.success("新增托运人成功", customerId);
		}

		// 删除redis缓存
		redisCache.deleteObject(CacheConstants.NETWORK_CUSTOMER_INFO + customerInfo.getCreditCode());
		return AjaxResult.success("新增托运人成功", customerId);
	}

	@Override
	public List<CustomerInfo> selectTaxCustomerInfoList(CustomerInfo customerInfo) {
		return customerInfoMapperEx.selectTaxCustomerInfoList(customerInfo);
	}

	@Override
	public AjaxResult getCustomerBankCard(Long id) {
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(id);
		//对公账户信息
		List<CustomerBankCard> customerBankCards = customerBankCardMapper.selectCustomerBankCardListByCustomerId(id);
		//bug7342调用钟珂接口更新审核中账户状态
		if (customerBankCards != null && !customerBankCards.isEmpty()) {
			AjaxResult ajaxResult = huaXiaService.queryCustomerBankState(customerInfo.getCreditCode());
			List<Map<String, Object>> list = Collections.emptyList();
			if (ajaxResult.getCode() == 200 && ajaxResult.getData() != null) {
				list = (List<Map<String, Object>>) ajaxResult.getData();
			}
			for (CustomerBankCard customerBankCard1 : customerBankCards) {
				Integer state = customerBankCard1.getState();
				if (state == 1) {
					for (Map<String, Object> map : list) {
						String account = String.valueOf(map.get("settleAccount"));
						if (customerBankCard1.getBankCardNo().equals(account)) {
							String isOuath = String.valueOf(map.get("isOuath"));
							String signState = String.valueOf(map.get("signState"));
							System.out.println("isOuath:" + isOuath);
							System.out.println("signState:" + signState);
							if ("2".equals(signState)) {
								customerBankCard1.setState(0);
								customerBankCardMapper.updateCustomerBankCard(customerBankCard1);
							}

						}
					}
				}
				String bankCode = customerBankCard1.getBankCode();
				PayBank payBank = new PayBank();
				payBank.setBankCode(bankCode);
				List<PayBank> payBanks = payBankMapper.selectPayBankList(payBank);
				if (StringUtils.isNotEmpty(payBanks)) {
					customerBankCard1.setBankID(payBanks.get(0).getId());
				}
			}
		}
		AjaxResult ajaxResult = new AjaxResult();
		ajaxResult.setCode(200);
		ajaxResult.setData(customerBankCards);
		ajaxResult.setMsg("查询成功");
		return ajaxResult;
	}

	@Override
	public AjaxResult getCustomerAccount(Long id) {
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(id);
		List<CustomerAccount> customerAccounts = customerAccountMapper.selectCustomerAccountListByCreditCode(customerInfo.getCreditCode());
		AjaxResult ajaxResult = new AjaxResult();
		ajaxResult.setCode(200);
		ajaxResult.setData(customerAccounts);
		ajaxResult.setMsg("查询成功");
		return ajaxResult;
	}
}
