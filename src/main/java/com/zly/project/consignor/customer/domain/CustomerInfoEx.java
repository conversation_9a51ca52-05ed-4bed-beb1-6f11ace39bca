package com.zly.project.consignor.customer.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.project.common.domain.CustomerAttachmentInfo;
import com.zly.project.freightforwarder.domain.FreightForwarderInfo;
import com.zly.project.freightforwarder.domain.FreightForwarderInfoEx;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 托运人对象 customer_info
 *
 * <AUTHOR>
 * @date 2021-11-22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerInfoEx extends CustomerInfo {
	@ApiModelProperty(value = "用户名")
	private String userName;

	@ApiModelProperty(value = "用户id")
	private String userId;

	/** 附件信息 */
	@ApiModelProperty(value = "附件信息")
	private List<CustomerAttachmentInfo> attachment;

	@ApiModelProperty(value = "服务id")
	private List<Long> serviceIds;

	@ApiModelProperty(value = "服务名称")
	private String serviceName;

	@ApiModelProperty(value = "是否网络货运人0是1否")
	private Integer isPlatform = 1;

	@ApiModelProperty(value = "托运人关系表")
	private List<FreightForwarderInfo> list;

	@ApiModelProperty(value = "业务模式id")
	private Long businessModelId;

	@ApiModelProperty(value = "客户信息创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	@ApiModelProperty(value = "业务类型0：公路系统 1：水运系统 2：公路系统+水运系统")
	private Integer businessType;
	@ApiModelProperty(value = "系统类型0：公路系统 1：水运系统")
	private Integer systemType;
	@ApiModelProperty(value = "审核历史记录")
	private List<CustomerApproveHistory> customerApproveHistory;

	@ApiModelProperty(value = "合同状态（-1 未上传  1：正常已上报   2：已过期）")
	private Integer customerForwarderContractState;

	private List<FreightForwarderInfoEx> freightForwarderInfos;

	@ApiModelProperty(value = "平台账户状态（-1 未开通  1 已开通）")
	private Integer customerAccountState;

	@ApiModelProperty(value = "平台账户交易号码")
	private String customerAccount;

	@ApiModelProperty(value = "是否存在审核中对公账户（0不存在  1 存在）")
	private Integer customerBankCardState;

	@ApiModelProperty(value = "对公账户数量")
	private Integer customerBankCardCount;

}
