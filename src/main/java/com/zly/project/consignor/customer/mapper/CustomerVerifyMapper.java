package com.zly.project.consignor.customer.mapper;

import java.util.List;
import com.zly.project.consignor.customer.domain.CustomerVerify;

/**
 * 货主信息验证 Mapper 接口
 *
 * <AUTHOR>
 * @date 2022-04-16
 */
public interface CustomerVerifyMapper
{
    /**
     * 查询货主信息验证
     *
     * @param customerId 货主信息验证主键
     * @return 货主信息验证
     */
    public CustomerVerify selectCustomerVerifyByCustomerId(Long customerId);

    /**
     * 查询货主信息验证列表
     *
     * @param customerVerify 货主信息验证
     * @return 货主信息验证集合
     */
    public List<CustomerVerify> selectCustomerVerifyList(CustomerVerify customerVerify);

    /**
     * 新增货主信息验证
     *
     * @param customerVerify 货主信息验证
     * @return 结果
     */
    public int insertCustomerVerify(CustomerVerify customerVerify);

    /**
     * 修改货主信息验证
     *
     * @param customerVerify 货主信息验证
     * @return 结果
     */
    public int updateCustomerVerify(CustomerVerify customerVerify);

    /**
     * 删除货主信息验证
     *
     * @param customerId 货主信息验证主键
     * @return 结果
     */
    public int deleteCustomerVerifyByCustomerId(Long customerId);

    /**
     * 批量删除货主信息验证
     *
     * @param customerIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerVerifyByCustomerIds(Long[] customerIds);
}