package com.zly.project.consignor.customer.mapper;

import com.zly.project.consignor.customer.domain.CustomerCaptainRelation;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 货主车队长关系Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-27
 */
public interface CustomerCaptainRelationMapper {
    /**
     * 查询货主车队长关系
     *
     * @param customerId 货主车队长关系主键
     * @return 货主车队长关系
     */
    public CustomerCaptainRelation selectCustomerCaptainRelationByCustomerId(Long customerId);

    /**
     * 查询货主车队长关系列表
     *
     * @param customerCaptainRelation 货主车队长关系
     * @return 货主车队长关系集合
     */
    public List<CustomerCaptainRelation> selectCustomerCaptainRelationList(CustomerCaptainRelation customerCaptainRelation);

    /**
     * 新增货主车队长关系
     *
     * @param customerCaptainRelation 货主车队长关系
     * @return 结果
     */
    public int insertCustomerCaptainRelation(CustomerCaptainRelation customerCaptainRelation);

    /**
     * 修改货主车队长关系
     *
     * @param customerCaptainRelation 货主车队长关系
     * @return 结果
     */
    public int updateCustomerCaptainRelation(CustomerCaptainRelation customerCaptainRelation);

    /**
     * 删除货主车队长关系
     *
     * @param customerId 货主车队长关系主键
     * @return 结果
     */
    public int deleteCustomerCaptainRelationByCustomerId(Long customerId);

    /**
     * 批量删除货主车队长关系
     *
     * @param customerIds
     * 		需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerCaptainRelationByCustomerIds(Long[] customerIds);

    int insertCustomerCaptainRelationList(List<CustomerCaptainRelation> insertCustomerCaptainRelationList);

    void updateCustomerCaptainRelationPhone(Map<String, Object> map);

    List<CustomerCaptainRelation> selectCustomerCaptainRelationListByIdentityCards(List<String> identityCards);

    int deleteCustomerCaptainRelationByCaptainIds(List<Long> captainIds);
}
