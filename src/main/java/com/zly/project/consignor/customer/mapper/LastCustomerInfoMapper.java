package com.zly.project.consignor.customer.mapper;


import java.util.List;

import com.zly.project.consignor.customer.domain.LastCustomerInfo;

/**
 * 客户信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-06-20
 */
public interface LastCustomerInfoMapper {
    /**
     * 查询客户信息
     *
     * @param id 客户信息主键
     * @return 客户信息
     */
    public LastCustomerInfo selectLastCustomerInfoById(Long id);

    /**
     * 查询客户信息列表
     *
     * @param lastCustomerInfo 客户信息
     * @return 客户信息集合
     */
    public List<LastCustomerInfo> selectLastCustomerInfoList(LastCustomerInfo lastCustomerInfo);

    /**
     * 新增客户信息
     *
     * @param lastCustomerInfo 客户信息
     * @return 结果
     */
    public int insertLastCustomerInfo(LastCustomerInfo lastCustomerInfo);

    /**
     * 修改客户信息
     *
     * @param lastCustomerInfo 客户信息
     * @return 结果
     */
    public int updateLastCustomerInfo(LastCustomerInfo lastCustomerInfo);

    /**
     * 删除客户信息
     *
     * @param id 客户信息主键
     * @return 结果
     */
    public int deleteLastCustomerInfoById(Long id);

    /**
     * 批量删除客户信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLastCustomerInfoByIds(Long[] ids);

	int checkCustomerExistence(LastCustomerInfo lastCustomerInfo);

	List<LastCustomerInfo> selectByCustomerIds(List<Long> customerIds);

}
