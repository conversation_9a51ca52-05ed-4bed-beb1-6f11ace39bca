package com.zly.project.consignor.customer.mapper;

import com.zly.project.consignor.customer.domain.CustomerInfo;
import com.zly.project.system.domain.vo.CustomerVo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 客户信息Mapper接口
 *
 * <AUTHOR>
 * @date 2021-10-20
 */
@Repository
public interface CustomerInfoMapper {
	/**
	 * 查询客户信息
	 *
	 * @param id
	 * 		客户信息主键
	 * @return 客户信息
	 */
	public CustomerInfo selectCustomerInfoById(Long id);

	/**
	 * 查询客户信息列表
	 *
	 * @param customerInfo
	 * 		客户信息
	 * @return 客户信息集合
	 */
	public List<CustomerInfo> selectCustomerInfoList(CustomerInfo customerInfo);

	/**
	 * 查询客户信息列表(根据审核状态及时间排序)
	 *
	 * @param customerInfo
	 * @return
	 */
	public List<CustomerInfo> getCustomerInfoList(CustomerInfo customerInfo);

	/**
	 * 新增客户信息
	 *
	 * @param customerInfo
	 * 		客户信息
	 * @return 结果
	 */
	public int insertCustomerInfo(CustomerInfo customerInfo);

	/**
	 * 修改客户信息
	 *
	 * @param customerInfo
	 * 		客户信息
	 * @return 结果
	 */
	public int updateCustomerInfo(CustomerInfo customerInfo);

	/**
	 * 删除客户信息
	 *
	 * @param id
	 * 		客户信息主键
	 * @return 结果
	 */
	public int deleteCustomerInfoById(Long id);

	/**
	 * 批量删除客户信息
	 *
	 * @param ids
	 * 		需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteCustomerInfoByIds(Long[] ids);

	List<CustomerVo> getCustomer();

	// 数据迁移用查询条件 托运人名称不模糊查询
	List<CustomerInfo> selectTransferCustomerInfoList(CustomerInfo customerInfo);

	List<CustomerInfo> selectCustomerInfoByIds(List<Long> ids);

	int updateCustomerInfoByProduct(CustomerInfo customerInfo1);

	List<CustomerInfo> selectCustomerInfoListByCreditCodeOrCustomerName(CustomerInfo customerInfoQuery);
}
