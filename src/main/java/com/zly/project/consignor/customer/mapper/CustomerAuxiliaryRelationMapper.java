package com.zly.project.consignor.customer.mapper;


import java.util.List;
import java.util.Map;

import com.zly.project.consignor.customer.domain.CustomerAuxiliaryRelation;

/**
 * 货主车辅助员关系Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-27
 */
public interface CustomerAuxiliaryRelationMapper {
    /**
     * 查询货主车辅助员关系
     *
     * @param customerId 货主车辅助员关系主键
     * @return 货主车辅助员关系
     */
    public CustomerAuxiliaryRelation selectCustomerAuxiliaryRelationByCustomerId(Long customerId);

    /**
     * 查询货主车辅助员关系列表
     *
     * @param customerAuxiliaryRelation 货主车辅助员关系
     * @return 货主车辅助员关系集合
     */
    List<CustomerAuxiliaryRelation> selectCustomerAuxiliaryRelationList(CustomerAuxiliaryRelation customerAuxiliaryRelation);

    /**
     * 新增货主车辅助员关系
     *
     * @param customerAuxiliaryRelation 货主车辅助员关系
     * @return 结果
     */
    public int insertCustomerAuxiliaryRelation(CustomerAuxiliaryRelation customerAuxiliaryRelation);

    /**
     * 修改货主车辅助员关系
     *
     * @param customerAuxiliaryRelation 货主车辅助员关系
     * @return 结果
     */
    public int updateCustomerAuxiliaryRelation(CustomerAuxiliaryRelation customerAuxiliaryRelation);

    /**
     * 删除货主车辅助员关系
     *
     * @param customerId 货主车辅助员关系主键
     * @return 结果
     */
    public int deleteCustomerAuxiliaryRelationByCustomerId(Long customerId);

    /**
     * 批量删除货主车辅助员关系
     *
     * @param customerIds
     * 		需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerAuxiliaryRelationByCustomerIds(Long[] customerIds);

    int insertCustomerAuxiliaryRelationList(List<CustomerAuxiliaryRelation> insertCustomerAuxiliaryRelationList);

    void updateCustomerAuxiliaryRelationPhone(Map<String, Object> map);

    List<CustomerAuxiliaryRelation> selectCustomerAuxiliaryRelationListByIdentityCard(List<String> identityCards);

    int deleteCustomerAuxiliaryRelationByAuxiliaryStaffIds(List<Long> ids);

	int deleteRepeatAuxiliaryByIdentityCardAndId(String identityCard, Long auxiliaryId);

	void updateNewAuxiliaryIdByOldAuxiliaryId(Long newAuxiliaryId, Long oldAuxiliaryId);

}
