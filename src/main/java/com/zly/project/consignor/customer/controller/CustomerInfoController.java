package com.zly.project.consignor.customer.controller;

import com.zly.common.constant.BusinessConstants;
import com.zly.common.constant.HttpStatus;
import com.zly.common.constant.UserConstants;
import com.zly.common.enums.ClientType;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.ServletUtils;
import com.zly.common.utils.StringUtils;
import com.zly.common.utils.TextUtil;
import com.zly.common.utils.bean.BeanUtils;
import com.zly.common.utils.ip.IPUtils;
import com.zly.common.utils.poi.ExcelUtil;
import com.zly.framework.aspectj.lang.annotation.Log;
import com.zly.framework.aspectj.lang.enums.BusinessType;
import com.zly.framework.redis.RedisCache;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.consignor.customer.domain.CustomerInfo;
import com.zly.project.consignor.customer.domain.CustomerInfoEx;
import com.zly.project.consignor.customer.domain.req.CustomerAdminConfigReq;
import com.zly.project.consignor.customer.domain.req.CustomerInfoReq;
import com.zly.project.consignor.customer.domain.res.OptionCustomerRes;
import com.zly.project.consignor.customer.service.ICustomerInfoService;
import com.zly.project.freightforwarder.domain.FreightForwarderInfo;
import com.zly.project.freightforwarder.service.impl.FreightForwarderInfoServiceImpl;
import com.zly.project.miniprogram.domain.CustomerInfoDetail;
import com.zly.project.system.domain.SysUser;
import com.zly.project.system.service.impl.SysClientLogService;
import com.zly.project.tenant.domain.TenantUser;
import com.zly.project.transport.waybill.domain.ConfirmReceiptInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 托运人信息
 *
 * <AUTHOR>
 * @date 2021-10-19
 */
@Api(tags = "托运人信息")
@Slf4j
@RestController
@RequestMapping("/consignor/customer")
public class CustomerInfoController extends BaseController {

	@Autowired
	private ICustomerInfoService customerInfoService;
	@Resource
	private FreightForwarderInfoServiceImpl freightForwarderInfoService;
	@Resource
	private RedisCache redis;
	@Resource
	private SysClientLogService sysClientLogService;

	/**
	 * 查询托运人信息列表
	 */
	@ApiOperation(value = "查询托运人用户名", notes = "查询托运人用户名")
	@GetMapping("/optionCustomer/list")
	public CommonResult optionCustomerList(String customerName) {
		List<OptionCustomerRes> list = customerInfoService.optionCustomerList(customerName, SecurityUtils.getLoginUser().getUser().getFreightForwarderId());
		return CommonResult.success(list);
	}

	/**
	 * 查询托运人信息列表
	 */
	@ApiOperation(value = "查询托运人信息列表", notes = "查询托运人信息列表")
	@PostMapping("/list")
	public TableDataInfo list(@RequestBody CustomerInfoEx customerInfo) {
		startPage();
		BeanUtils.beanAttributeValueTrim(customerInfo);// 去掉该对象中string字段前后空格
		// customerInfo.setState(BusinessConstants.STATE_ENABLE);
		customerInfo.setFreightForwarderId(SecurityUtils.getLoginUser().getUser().getFreightForwarderId());
		List<CustomerInfoEx> list = customerInfoService.getCustomerInfoList(customerInfo);
		return getDataTable(list);
	}

	/**
	 * 托运人列表
	 *
	 * @return
	 */
	@ApiOperation(value = "托运人列表", notes = "托运人列表")
	@GetMapping("/platform/list")
	public CommonResult<List<CustomerInfo>> getAllCustomer(String customerName, Integer type) {
		Long freightForwarderId = SecurityUtils.getFreightForwarderId();
		CustomerInfo customerInfo = new CustomerInfo();
		customerInfo.setState(BusinessConstants.STATE_ENABLE);
		customerInfo.setFreightForwarderId(null == freightForwarderId ? 0 : freightForwarderId);
		customerInfo.setCustomerName(customerName);
		List<CustomerInfo> resList = customerInfoService.selectCustomerInfoListByFreightForwarderId(customerInfo);
		return CommonResult.success(resList);

	}

	/**
	 * 查询运单托运人列表
	 *
	 * @return
	 */
	@ApiOperation(value = "查询运单托运人列表", notes = "查询运单托运人列表")
	@GetMapping("/platform/waybillList")
	public CommonResult<List<CustomerInfo>> getAllWaybillCustomer(String customerName) {
		Long freightForwarderId = SecurityUtils.getFreightForwarderId();
		CustomerInfo customerInfo = new CustomerInfo();
		customerInfo.setState(BusinessConstants.STATE_ENABLE);
		customerInfo.setFreightForwarderId(null == freightForwarderId ? 0 : freightForwarderId);
		customerInfo.setCustomerName(customerName);
		// List<CustomerInfo> resList = customerInfoService.selectTaxCustomerInfoList(customerInfo);
		List<CustomerInfo> resList = customerInfoService.selectWaybillCustomerInfoListByFreightForwarderId(customerInfo);
		return CommonResult.success(resList);

	}

	/**
	 * 查询运单托运人列表
	 *
	 * @return
	 */
	@ApiOperation(value = "查询运单托运人列表", notes = "查询运单托运人列表")
	@GetMapping("/platform/taxCustomerInfoListByForwarder")
	public CommonResult<List<CustomerInfo>> taxCustomerInfoListByForwarder(String customerName) {
		Long freightForwarderId = SecurityUtils.getFreightForwarderId();
		CustomerInfo customerInfo = new CustomerInfo();
		customerInfo.setState(BusinessConstants.STATE_ENABLE);
		customerInfo.setFreightForwarderId(freightForwarderId);
		customerInfo.setCustomerName(customerName);
		List<CustomerInfo> resList = customerInfoService.selectTaxCustomerInfoList(customerInfo);
		// List<CustomerInfo> resList = customerInfoService.selectWaybillCustomerInfoListByFreightForwarderId(customerInfo);
		return CommonResult.success(resList);
	}

	/**
	 * 启用/禁用
	 *
	 * @param id
	 * @return
	 */
	// @PreAuthorize("@ss.hasPermi('customer:contract:switch')")
	@ApiOperation(value = "启用/禁用", notes = "启用/禁用")
	@GetMapping("/startOrStop/{id}")
	public AjaxResult startOrStop(@PathVariable("id") Long id) {
		return customerInfoService.startOrStop(id);
	}

	/**
	 * 待审批托运人信息列表
	 */
	@ApiOperation(value = "待审批托运人信息列表", notes = "待审批托运人信息列表")
	@PreAuthorize("@ss.hasPermi('consignor:customer:approve:list')")
	@PostMapping("/approve/list")
	public TableDataInfo approveList(@RequestBody CustomerInfo customerInfo) {
		SysUser sysUser = SecurityUtils.getLoginUser().getUser();
		customerInfo.setFreightForwarderId(null == sysUser ? 0 : sysUser.getFreightForwarderId());
		startPage();
		customerInfo.setApproveState(UserConstants.APPROVE_ING);
		List<CustomerInfo> list = customerInfoService.selectCustomerInfoList(customerInfo);
		return getDataTable(list);
	}

	/**
	 * 导出托运人信息列表
	 */
	@ApiOperation(value = "导出托运人信息列表", notes = "导出托运人信息列表")
	@PreAuthorize("@ss.hasPermi('consignor:customer:export')")
	@Log(title = "托运人信息", businessType = BusinessType.EXPORT)
	@GetMapping("/export")
	public AjaxResult export(CustomerInfo customerInfo) {
		SysUser sysUser = SecurityUtils.getLoginUser().getUser();
		customerInfo.setFreightForwarderId(null == sysUser ? 0 : sysUser.getFreightForwarderId());
		List<CustomerInfo> list = customerInfoService.selectCustomerInfoList(customerInfo);
		ExcelUtil<CustomerInfo> util = new ExcelUtil<CustomerInfo>(CustomerInfo.class);
		return util.exportExcel(list, "托运人信息数据");
	}

	/**
	 * 获取托运人信息详细信息
	 */
	@ApiOperation(value = "获取托运人信息详细信息", notes = "获取托运人信息详细信息")
	@PreAuthorize("@ss.hasPermi('consignor:customer:query')")
	@GetMapping(value = "/info/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		return customerInfoService.getCustomerById(id);
	}

	@ApiOperation(value = "获取托运人对公账户信息", notes = "获取托运人对公账户信息")
	@PreAuthorize("@ss.hasPermi('consignor:customer:query')")
	@GetMapping(value = "/customerBankCard/{id}")
	public AjaxResult getCustomerBankCard(@PathVariable("id") Long id) {
		return customerInfoService.getCustomerBankCard(id);
	}

	@ApiOperation(value = "获取托运人平台账户信息", notes = "获取托运人对公账户信息")
	@PreAuthorize("@ss.hasPermi('consignor:customer:query')")
	@GetMapping(value = "/customerAccount/{id}")
	public AjaxResult getCustomerAccount(@PathVariable("id") Long id) {
		return customerInfoService.getCustomerAccount(id);
	}

	@ApiOperation(value = "获取托运人的联系人 -- v4.1 -- zk", notes = "获取托运人的联系人 -- v4.1 -- zk")
	@PreAuthorize("@ss.hasPermi('operation:mycenter:query')")
	@GetMapping(value = "/contactInfo")
	public AjaxResult contactInformation() {
		CustomerInfo res = new CustomerInfo();
		if (ClientType.SHIPPER.equals(SecurityUtils.getClientType()) || ClientType.FINANCE.equals(SecurityUtils.getClientType())) {
			CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(SecurityUtils.getShipperId());
			res.setContact(TextUtil.getStarString2(customerInfo.getContact(), 1, 0));
			res.setContactPhone(TextUtil.getStarString2(customerInfo.getContactPhone(), 3, 3));
		} else if (ClientType.AGENT.equals(SecurityUtils.getClientType()) || ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
			FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(SecurityUtils.getFreightForwarderId());
			res.setContact(TextUtil.getStarString2(freightForwarderInfo.getContact(), 1, 0));
			res.setContactPhone(TextUtil.getStarString2(freightForwarderInfo.getContactPhone(), 3, 3));
		} else {
			return AjaxResult.error("没有权限访问");
		}
		return AjaxResult.success(res);
	}

	/**
	 * 审核结果
	 */
	@ApiOperation(value = "审核结果", notes = "审核结果")
	@PreAuthorize("@ss.hasPermi('consignor:customer:approve')")
	@PostMapping("/approve")
	public AjaxResult approveCustomer(@RequestBody CustomerInfoReq req) {
		return customerInfoService.approveCustomer(req);
	}

	/**
	 * 新增托运人信息
	 */
	@ApiOperation(value = "新增托运人信息", notes = "新增托运人信息")
	@PreAuthorize("@ss.hasPermi('consignor:customer:add')")
	@Log(title = "托运人信息", businessType = BusinessType.INSERT)
	@PostMapping(value = "/add")
	public AjaxResult add(@RequestBody CustomerInfoReq req) {
		return customerInfoService.addCustomerInfo(req);
	}

	/**
	 * 修改托运人信息
	 */
	@ApiOperation(value = "修改托运人信息", notes = "修改托运人信息")
	@PreAuthorize("@ss.hasPermi('consignor:customer:edit')")
	@Log(title = "托运人信息", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/edit")
	public AjaxResult edit(@RequestBody CustomerInfoReq req) {
		return customerInfoService.editCustomerInfo(req);
	}

	/**
	 * 删除托运人信息
	 */
	@ApiOperation(value = "删除托运人信息", notes = "删除托运人信息")
	@PreAuthorize("@ss.hasPermi('consignor:customer:remove')")
	@Log(title = "托运人信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult remove(@PathVariable Long[] ids) {
		return toAjax(customerInfoService.deleteCustomerInfoByIds(ids));
	}

	/**
	 * 托运人配置
	 */
	@ApiOperation(value = "托运人配置", notes = "托运人配置")
	@PreAuthorize("@ss.hasPermi('consignor:customer:adminConfig')")
	@PostMapping("/adminConfig")
	public AjaxResult adminConfig(@RequestBody CustomerAdminConfigReq req) {

		return customerInfoService.adminConfig(req);
	}

	/**
	 * 托运人管理员信息
	 *
	 * @return
	 */
	@ApiOperation(value = "托运人管理员信息", notes = "托运人管理员信息")
	@GetMapping("/adminInfo/{id}")
	public AjaxResult adminInfo(@PathVariable("id") Long id) {
		if (StringUtils.isNull(id)) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "托运人标识为空");
		}
		return AjaxResult.success(customerInfoService.adminInfo(id));
	}

	/**
	 * 货主PC端---企业管理详情
	 *
	 * @return
	 */
	@ApiOperation(value = "货主PC端---企业管理详情", notes = "货主PC端---企业管理详情")
	@PreAuthorize("@ss.hasPermi('operation:mycenter:query')")
	@GetMapping("/customer/info")
	public AjaxResult customerInfo() {
		return customerInfoService.selectCustomerInfoPCById();
	}

	/**
	 * 货主PC端---企业管理修改
	 *
	 * @return
	 */
	@ApiOperation(value = "货主PC端---企业管理修改", notes = "货主PC端---企业管理修改")
	@PreAuthorize("@ss.hasPermi('operation:mycenter:edit')")
	@PostMapping("/customer/edit")
	public AjaxResult customerEdit(@Valid @RequestBody CustomerInfoDetail customerInfoDetail) {
		return customerInfoService.editCustomerInfoSlave(customerInfoDetail);
	}

	/**
	 * 校验托运人信息是否完整
	 */
	@ApiOperation(value = "校验托运人信息是否完整--4.3.13", notes = "校验托运人信息是否完整--4.3.13")
	@PostMapping("/customer/verifyCustomerInfoIsComplete")
	public CommonResult verifyCustomerInfoIsComplete() {
		TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		if (tenantUser == null) {
			return CommonResult.error("请重新登录!");
		}
		CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(tenantUser.getTenantId());
		if (null == customerInfo) {
			return CommonResult.error("未查询到当前托运人信息!");
		}
		if (StringUtils.isEmpty(customerInfo.getCustomerName())) {
			return CommonResult.success("托运人名称为空!");
		}
		if (StringUtils.isEmpty(customerInfo.getCreditCode())) {
			return CommonResult.success("社会统一信用代码为空!");
		}
		if (StringUtils.isEmpty(customerInfo.getContactPhone())) {
			return CommonResult.success("联系方式为空!");
		}
		if (StringUtils.isEmpty(customerInfo.getContact())) {
			return CommonResult.success("联系人为空!");
		}
		if (StringUtils.isEmpty(customerInfo.getBillHead())) {
			return CommonResult.success("发票抬头为空!");
		}
		if (StringUtils.isEmpty(customerInfo.getTaxNo())) {
			return CommonResult.success("纳税人识别号为空!");
		}
		if (StringUtils.isEmpty(customerInfo.getUnitAddress())) {
			return CommonResult.success("地址为空!");
		}
		if (StringUtils.isEmpty(customerInfo.getTelephone())) {
			return CommonResult.success("电话为空!");
		}
		if (StringUtils.isEmpty(customerInfo.getBank())) {
			return CommonResult.success("开户行为空!");
		}
		if (StringUtils.isEmpty(customerInfo.getBankAccount())) {
			return CommonResult.success("开户行账号为空!");
		}
		return CommonResult.success();
	}

	/**
	 * 外部新增托运人信息
	 */
	@ApiOperation(value = "产品中心新增托运人信息--4.3.25版本--yxq", notes = "产品中心新增托运人信息--4.3.25版本--yxq")
	@PostMapping(value = "/productAdd")
	public AjaxResult productAdd(@RequestBody CustomerInfoReq req) {
		String ip = IPUtils.getIPAddress(ServletUtils.getRequest());
		log.info("ip地址：" + ip + "正在访问产品中心注册托运人");
		if (!redis.canAccess(ip, 10, 60)) {
			return AjaxResult.requestError("操作频繁，请稍后再试！");
		}
		req.setResourceType(BusinessConstants.CUSTOMER_CREATE_BY_OUT);
		// return customerInfoService.addCustomerInfo(req);
		// if (req.getFreightForwarderId() == null) {
		// return AjaxResult.error("请选择网络货运人");
		// }
		// req.setFreightForwarderId(req.getFreightForwarderId());
		// 2、新增托运人
		return customerInfoService.productAddCustomerInfo(req);
	}

	/**
	 * 修改强制回单状态
	 */
	@ApiOperation(value = "修改强制回单状态--4.4.16--shw", notes = "修改强制回单状态--4.4.16--shw")
	@PostMapping("/updateConfirmReceiptState/{type}")
	@Transactional
	public CommonResult<ConfirmReceiptInfo> updateConfirmReceiptState(@PathVariable("type") Integer type) {
		CustomerInfo info = new CustomerInfo();
		info.setId(SecurityUtils.getShipperId());
		info.setConfirmReceiptState(type);
		customerInfoService.updateCustomerInfo(info);

		CustomerInfo customerInfo = Optional.ofNullable(customerInfoService.selectCustomerInfoById(SecurityUtils.getShipperId())).orElse(new CustomerInfo());
		//记录操作日志
		String actionName = "托运人修改强制回单状态(托运人id：" + SecurityUtils.getShipperId() + "，托运人名称：" + customerInfo.getCustomerName() + ")";
		actionName += "，请求参数：" + type;
		sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_SYS_PARAMETER, BusinessConstants.ACTION_TYPE_UPDATE, org.apache.commons.lang3.StringUtils.substring(actionName, 0, 5000),
				customerInfo.getCreditCode(), Collections.singletonList(customerInfo.getId()));
		return CommonResult.success();
	}

	/**
	 * 修改强制回单状态
	 */
	@ApiOperation(value = "强制回单状态--4.4.16--shw", notes = "强制回单状态--4.4.16--shw")
	@GetMapping("/ConfirmReceiptState")
	public CommonResult<Integer> ConfirmReceiptState() {
		CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(SecurityUtils.getShipperId());
		return CommonResult.success(customerInfo.getConfirmReceiptState());
	}

	/**
	 * 修改强制回单状态
	 */
	@ApiOperation(value = "司机修改回单状态--4.5.5--shw", notes = "司机修改回单状态--4.5.5--shw")
	@GetMapping("/driverReceiptState")
	public CommonResult<Integer> driverReceiptState() {
		CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(SecurityUtils.getShipperId());
		return CommonResult.success(customerInfo.getDriverReceiptState());
	}

	/**
	 * 修改强制回单状态
	 */
	@ApiOperation(value = "修改司机回单状态--4.5.5--shw", notes = "修改司机回单状态--4.5.5--shw")
	@PostMapping("/updateDriverReceiptState/{type}")
	@Transactional
	public CommonResult<ConfirmReceiptInfo> updateDriverReceiptState(@PathVariable("type") Integer type) {
		CustomerInfo info = new CustomerInfo();
		info.setId(SecurityUtils.getShipperId());
		info.setDriverReceiptState(type);
		customerInfoService.updateCustomerInfo(info);

		CustomerInfo customerInfo = Optional.ofNullable(customerInfoService.selectCustomerInfoById(SecurityUtils.getShipperId())).orElse(new CustomerInfo());
		//记录操作日志
		String actionName = "托运人允许司机修改回单(托运人id：" + SecurityUtils.getShipperId() + "，托运人名称：" + customerInfo.getCustomerName() + ")";
		actionName += "，请求参数：" + type;
		sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_SYS_PARAMETER, BusinessConstants.ACTION_TYPE_UPDATE, org.apache.commons.lang3.StringUtils.substring(actionName, 0, 5000),
				customerInfo.getCreditCode(), Collections.singletonList(customerInfo.getId()));
		return CommonResult.success();
	}
}
