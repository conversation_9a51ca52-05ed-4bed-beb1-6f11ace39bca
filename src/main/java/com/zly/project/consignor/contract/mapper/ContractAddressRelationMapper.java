package com.zly.project.consignor.contract.mapper;

import com.zly.project.consignor.contract.domain.ContractAddressRelation;

import java.util.List;

/**
 * 项目地址关联Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-15
 */
public interface ContractAddressRelationMapper
{
    /**
     * 查询项目地址关联
     *
     * @param addressId 项目地址关联主键
     * @return 项目地址关联
     */
    public ContractAddressRelation selectContractAddressRelationByAddressId(Long addressId);

    /**
     * 查询项目地址关联列表
     *
     * @param contractAddressRelation 项目地址关联
     * @return 项目地址关联集合
     */
    public List<ContractAddressRelation> selectContractAddressRelationList(ContractAddressRelation contractAddressRelation);

    /**
     * 新增项目地址关联
     *
     * @param contractAddressRelation 项目地址关联
     * @return 结果
     */
    public int insertContractAddressRelation(ContractAddressRelation contractAddressRelation);

    /**
     * 修改项目地址关联
     *
     * @param contractAddressRelation 项目地址关联
     * @return 结果
     */
    public int updateContractAddressRelation(ContractAddressRelation contractAddressRelation);

    /**
     * 删除项目地址关联
     *
     * @param addressId 项目地址关联主键
     * @return 结果
     */
    public int deleteContractAddressRelationByAddressId(Long addressId);

    /**
     * 批量删除项目地址关联
     *
     * @param addressIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteContractAddressRelationByAddressIds(Long[] addressIds);
}
