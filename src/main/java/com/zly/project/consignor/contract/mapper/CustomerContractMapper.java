package com.zly.project.consignor.contract.mapper;

import java.util.List;

import com.zly.project.consignor.contract.domain.CustomerContract;

/**
 * 客户合同Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-10-19
 */
public interface CustomerContractMapper 
{
    /**
     * 查询客户合同
     * 
     * @param id 客户合同主键
     * @return 客户合同
     */
    public CustomerContract selectCustomerContractById(Long id);

    /**
     * 查询客户合同列表
     * 
     * @param customerContract 客户合同
     * @return 客户合同集合
     */
    public List<CustomerContract> selectCustomerContractList(CustomerContract customerContract);

    /**
     * 新增客户合同
     * 
     * @param customerContract 客户合同
     * @return 结果
     */
    public int insertCustomerContract(CustomerContract customerContract);

    /**
     * 修改客户合同
     * 
     * @param customerContract 客户合同
     * @return 结果
     */
    public int updateCustomerContract(CustomerContract customerContract);

    /**
     * 删除客户合同
     * 
     * @param id 客户合同主键
     * @return 结果
     */
    public int deleteCustomerContractById(Long id);

    /**
     * 批量删除客户合同
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerContractByIds(Long[] ids);

    /**
     * 一星期后即将到期的客户合约
     * @return
     */
    public List<CustomerContract> customerContractStateFromAWeekExpired();

	public List<CustomerContract> selectCustomerContractBycustomerIds(List<Long> customerIds);
}
