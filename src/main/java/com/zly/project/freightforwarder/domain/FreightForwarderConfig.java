package com.zly.project.freightforwarder.domain;

import com.zly.framework.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FreightForwarderConfig extends BaseEntity {

	@ApiModelProperty(value = "网络货运人id", required = false)
	private Long freightForwarderId;

	@ApiModelProperty(value = "网络货运人个性化名称", required = true)
	private String freightForwarderName;

	@ApiModelProperty(value = "网络货运登录页logo", required = true)
	private String freightForwarderLoginLogo;

	@ApiModelProperty(value = "网络货运人logo", required = true)
	private String freightForwarderLogo;

	@ApiModelProperty(value = "网络货运导航栏小logo", required = true)
	private String freightForwarderMiniLogo;

	@ApiModelProperty(value = "网络货运平台域名", required = true)
	private String freightForwarderDomain;

	@ApiModelProperty("是否人工审核证照 0 需要人工审核 1自动审核")
	private Integer isAuditFile;

	@ApiModelProperty("是否人工审核运单 0 需要人工审核 1自动审核")
	private Integer isAuditWaybill;

	@ApiModelProperty("短码")
	private String shortCode;

	@ApiModelProperty("icp编号")
	private String icpCode;

	@ApiModelProperty("车辆是否限制营运性质：0 不限制 、1 仅营运")
	private Integer vehicleUseCharacter;

	@ApiModelProperty("是否支持亲属收款 0 支持 1 不支持")
	private Integer familyReceivePayment;

	@ApiModelProperty("允许业务车牌(0不限制   1黄牌)")
	private Integer supportColors;

	@ApiModelProperty("双轨迹校验(0关闭  1开启)")
	private Integer towTrajectoryVerification;

	@ApiModelProperty("是否发邮件 0 需要 1不需要")
	private Integer isEmail;

	@ApiModelProperty("邮箱")
	private String email;

	@ApiModelProperty("密码")
	private String password;

}
