package com.zly.project.freightforwarder.controller;

import com.alibaba.fastjson2.JSON;
import com.zly.common.utils.CommonUtil;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.poi.ExcelUtil;
import com.zly.framework.aspectj.lang.annotation.Log;
import com.zly.framework.aspectj.lang.enums.BusinessType;
import com.zly.framework.security.service.PermissionService;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.carrier.auxiliary.domain.AuxiliaryStaffInfo;
import com.zly.project.carrier.driver.domain.Driver;
import com.zly.project.carrier.payee.domain.PayeeInfo;
import com.zly.project.consignor.customer.service.ICustomerDriverRelationService;
import com.zly.project.freightforwarder.domain.DriverCarCaptainInfo;
import com.zly.project.freightforwarder.domain.FreightForwarderInfo;
import com.zly.project.freightforwarder.domain.req.FreightForwarderInfoAdd;
import com.zly.project.freightforwarder.domain.req.WaybillSetUpReq;
import com.zly.project.freightforwarder.mapper.ForwarderDomainRelationMapper;
import com.zly.project.freightforwarder.service.IFreightForwarderInfoService;
import com.zly.project.freightforwarder.service.impl.FreightForwarderAuxiliaryRelationServiceImpl;
import com.zly.project.freightforwarder.service.impl.FreightForwarderCarCaptainRelationServiceImpl;
import com.zly.project.system.domain.req.WhitelistContractReq;
import com.zly.project.system.service.FreightForwarderContractWhitelistService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 网络货运人管理（税源地）Controller
 *
 * <AUTHOR>
 * @date 2022-12-27
 */
@Api(tags = "网络货运人管理")
@RestController
@RequestMapping("/freightForwarder/info")
public class FreightForwarderInfoController extends BaseController {
	@Resource
	private IFreightForwarderInfoService freightForwarderInfoService;
	@Resource
	private ForwarderDomainRelationMapper forwarderDomainRelationMapper;
	@Resource
	private FreightForwarderContractWhitelistService forwarderContractWhitelistService;
	@Resource
	private ICustomerDriverRelationService customerDriverRelationService;
	@Resource
	private PermissionService permissionService;
	@Resource
	private FreightForwarderCarCaptainRelationServiceImpl freightForwarderCarCaptainRelationService;

	/**
	 * 查询网络货运人信息（税源地）列表
	 */
	@ApiOperation(value = "查询网络货运人信息（税源地）列表", notes = "查询网络货运人信息（税源地）列表")
	@GetMapping("/list")
	public TableDataInfo list(FreightForwarderInfo freightForwarderInfo) {
		startPage();
		List<FreightForwarderInfo> list = freightForwarderInfoService.selectFreightForwarderInfoList(freightForwarderInfo);
		TableDataInfo res = getDataTable(list);
		if (CommonUtil.isNotNullOrEmpty(list)) {
			FreightForwarderInfo shandao = list.stream().filter(f -> f.getName().equals("江苏善道智运科技有限公司")).findFirst().orElse(null);
			if (shandao != null) {
				list.remove(shandao);
				res.setTotal(res.getTotal() - 1);
			}
		}
		return res;
	}

	/**
	 * 导出网络货运人信息（税源地）列表
	 */
	@ApiOperation(value = "导出网络货运人信息（税源地）列表", notes = "导出网络货运人信息（税源地）列表")
	@PreAuthorize("@ss.hasPermi('freightForwarder:info:export')")
	@Log(title = "网络货运人信息（税源地）", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, FreightForwarderInfo freightForwarderInfo) {
		List<FreightForwarderInfo> list = freightForwarderInfoService.selectFreightForwarderInfoList(freightForwarderInfo);
		ExcelUtil<FreightForwarderInfo> util = new ExcelUtil<FreightForwarderInfo>(FreightForwarderInfo.class);
		util.exportExcel(response, list, "网络货运人信息（税源地）数据");
	}

	/**
	 * 获取网络货运人信息（税源地）详细信息
	 */
	@ApiOperation(value = "获取网络货运人信息（税源地）详细信息", notes = "获取网络货运人信息（税源地）详细信息")
	@PreAuthorize("@ss.hasPermi('freightForwarder:info:query')")
	@GetMapping(value = "/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		return AjaxResult.success(freightForwarderInfoService.selectFreightForwarderInfoById(id));
	}

	/**
	 * 新增网络货运人信息（税源地）
	 *
	 * @param freightForwarderInfo
	 * @deprecated 这个接口不知道是干嘛用的，先弃用，<br/>
	 *             接口从 [POST]/freightForwarder/info 变更为 [POST]/freightForwarder/info/add，需要再放开
	 */
	@ApiOperation(value = "新增网络货运人信息（税源地）", notes = "新增网络货运人信息（税源地）")
	@PreAuthorize("@ss.hasPermi('freightForwarder:info:add')")
	@Log(title = "网络货运人信息（税源地）", businessType = BusinessType.INSERT)
	@Deprecated
	@PostMapping(value = "/add")
	public AjaxResult add(@RequestBody FreightForwarderInfoAdd freightForwarderInfo) {
		return freightForwarderInfoService.insertFreightForwarderInfo(freightForwarderInfo);
	}

	/**
	 * 修改网络货运人信息（税源地）
	 */
	@PreAuthorize("@ss.hasPermi('freightForwarder:info:edit')")
	@Log(title = "修改网络货运人信息（税源地）", businessType = BusinessType.UPDATE)
	@PutMapping
	public AjaxResult edit(@RequestBody FreightForwarderInfo freightForwarderInfo) {
		return toAjax(freightForwarderInfoService.updateFreightForwarderInfo(freightForwarderInfo));
	}

	/**
	 * 删除网络货运人信息（税源地）
	 */
	@ApiOperation(value = "删除网络货运人信息（税源地）", notes = "删除网络货运人信息（税源地）")
	@PreAuthorize("@ss.hasPermi('freightForwarder:info:remove')")
	@Log(title = "网络货运人信息（税源地）", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult remove(@PathVariable Long[] ids) {
		return toAjax(freightForwarderInfoService.deleteFreightForwarderInfoByIds(ids));
	}

	/**
	 * 根据域名查询网络货运人id
	 */
	@ApiOperation(value = "根据域名获取网络货运人id", notes = "根据域名获取网络货运人id")
	@GetMapping("/domain")
	public CommonResult<Long> getForwarderInfo(String domain) {
		Long forwarderId = forwarderDomainRelationMapper.selectFreightForwarderId(domain);
		if (forwarderId == null)
			return CommonResult.error("找不到对应的网络货运人，请联系管理员");
		return CommonResult.success(forwarderId);
	}

	/**
	 * 承运人收款设置
	 */
	@ApiOperation(value = "承运人收款设置", notes = "承运人收款设置")
	@PostMapping("/paymentSetting")
	public CommonResult paymentSetting(@RequestBody FreightForwarderInfoAdd info){
		FreightForwarderInfo freightForwarderInfo = new FreightForwarderInfo();
		freightForwarderInfo.setId(SecurityUtils.getFreightForwarderId());
		freightForwarderInfo.setLimitAmount(new BigDecimal(info.getLimitAmount()));
		freightForwarderInfoService.updateFreightForwarderInfo(freightForwarderInfo);
		return CommonResult.success();
	}

	@ApiOperation(value = "托运人关联的税源地 -v4.3.18--yxq", notes = "托运人关联的税源地")
	@GetMapping("/shipperWithFreightForwarder")
	public CommonResult shipperWithFreightForwarder(){
		Long shipperId = SecurityUtils.getShipperId();
		CommonResult freightForwarderInfoCommonResult = freightForwarderInfoService.shipperWithFreightForwarder(shipperId);
		return freightForwarderInfoCommonResult;
	}

	@ApiOperation(value = "首页信息", notes = "首页信息")
	@PostMapping("/getHomePageInfo")
	public CommonResult getHomePageInfo() {
		if (!permissionService.sysHasPermi("sys:page:contract")) {
			return CommonResult.success(null, "没有权限");
		}
		return freightForwarderInfoService.getHomePageInfo();
	}

	@ApiOperation(value = "手动更新首页信息", notes = "手动更新首页信息")
	@PostMapping("/updateHomePageInfo")
	public CommonResult updateHomePageInfo() {
		freightForwarderInfoService.getProjectInfo();
		return CommonResult.success();
	}

	/**
	 * 网络货运人收款人列表 *
	 *
	 * @return
	 */
	@ApiOperation("网络货运人收款人列表 -v4.4.3--yxq")
	@GetMapping(value = "/forwarderPayeeList")
	public CommonResult<List<PayeeInfo>> forwarderPayeeList(String license, String keyword) {
		return freightForwarderInfoService.forwarderPayeeList(license, keyword);
	}

	@ApiOperation("网络货运人运单导入开关设置 -v4.4.8--zxy")
	@PostMapping(value = "/updateWaybillImportState")
	public CommonResult updateWaybillImportState(@RequestBody FreightForwarderInfo req) {
		if (req == null || req.getWaybillIsImport() == null) {
			return CommonResult.error("参数错误，运单导入开关设置失败");
		}
		FreightForwarderInfo info = new FreightForwarderInfo();
		info.setId(SecurityUtils.getFreightForwarderId());
		info.setWaybillIsImport(req.getWaybillIsImport());
		freightForwarderInfoService.updateFreightForwarderInfo(info);
		return CommonResult.success("运单导入开关设置成功");
	}

	@ApiOperation("网络货运人运单设置 -v4.4.8--zxy")
	@PostMapping(value = "/waybillSetUp")
	public CommonResult waybillSetUp(@RequestBody WaybillSetUpReq req) {
		// 1、项目白名单设置
		WhitelistContractReq whitelistContractReq = JSON.parseObject(JSON.toJSONString(req), WhitelistContractReq.class);
		CommonResult res = forwarderContractWhitelistService.saveContractWhitelistSetting(whitelistContractReq);
		if (CommonResult.isNotSuccess(res)) {
			return res;
		}
		// 2、运单导入开关设置
		if (req == null || req.getWaybillIsImport() == null) {
			return CommonResult.error("是否导入为空，运单设置失败");
		}
		FreightForwarderInfo info = new FreightForwarderInfo();
		info.setId(SecurityUtils.getFreightForwarderId());
		info.setWaybillIsImport(req.getWaybillIsImport());
		freightForwarderInfoService.updateFreightForwarderInfo(info);
		return CommonResult.success("运单设置成功");
	}

	/**
	 * 网络货运司机列表
	 *
	 * @param keyword
	 *            关键词（司机名称、手机号）
	 * @return
	 */
	@ApiOperation("网络货运司机列表")
	@GetMapping(value = "/freightForwarderDriverList")
	public CommonResult<List<Driver>> freightForwarderDriverList(String keyword) {
		List<Driver> driverList = customerDriverRelationService.freightForwarderDriverList(keyword);
		return CommonResult.success(driverList);
	}

	@Resource
	private FreightForwarderAuxiliaryRelationServiceImpl freightForwarderAuxiliaryRelationService;

	/**
	 * 网络货运辅助员列表
	 *
	 * @param keyword
	 * 		关键词（辅助员姓名,身份证）
	 * @return
	 */
	@ApiOperation("网络货运辅助员列表")
	@GetMapping(value = "/freightForwarderAuxiliaryStaffInfoList")
	public CommonResult<List<AuxiliaryStaffInfo>> freightForwarderAuxiliaryStaffInfoList(String keyword) {
		List<AuxiliaryStaffInfo> auxiliaryStaffInfoList = freightForwarderAuxiliaryRelationService.freightForwarderAuxiliaryStaffInfoList(keyword);
		return CommonResult.success(auxiliaryStaffInfoList);
	}

	/**
	 * 网络货运司机车队长列表
	 *
	 * @param keyword
	 * 		关键词（辅助员姓名,身份证）
	 * @return
	 */
	@ApiOperation("网络货运司机车队长列表")
	@GetMapping(value = "/freightForwarderDriverCarCaptainList")
	public CommonResult<List<DriverCarCaptainInfo>> freightForwarderDriverCarCaptainList(String keyword) {
		List<DriverCarCaptainInfo> driverCarCaptainInfoList = freightForwarderCarCaptainRelationService.freightForwarderDriverCarCaptainList(keyword);
		return CommonResult.success(driverCarCaptainInfoList);
	}
}
