package com.zly.project.riskManagement.mapper;

import com.zly.project.riskManagement.domain.WaybillRiskHandler;
import com.zly.project.riskManagement.domain.WaybillRiskQuery;
import com.zly.project.riskManagement.domain.res.WaybillRiskQueryRes;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 网络货运平台风控处理记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-21
 */
public interface WaybillRiskHandlerMapper {
    /**
     * 查询网络货运平台风控处理记录
     *
     * @param id 网络货运平台风控处理记录主键
     * @return 网络货运平台风控处理记录
     */
    public WaybillRiskHandler selectWaybillRiskHandlerById(Long id);

    /**
     * 查询网络货运平台风控处理记录列表
     *
     * @param waybillRiskHandler 网络货运平台风控处理记录
     * @return 网络货运平台风控处理记录集合
     */
    public List<WaybillRiskHandler> selectWaybillRiskHandlerList(WaybillRiskHandler waybillRiskHandler);

    /**
     * 新增网络货运平台风控处理记录
     *
     * @param waybillRiskHandler 网络货运平台风控处理记录
     * @return 结果
     */
    public int insertWaybillRiskHandler(WaybillRiskHandler waybillRiskHandler);

    /**
     * 修改网络货运平台风控处理记录
     *
     * @param waybillRiskHandler 网络货运平台风控处理记录
     * @return 结果
     */
    public int updateWaybillRiskHandler(WaybillRiskHandler waybillRiskHandler);

    /**
     * 删除网络货运平台风控处理记录
     *
     * @param id 网络货运平台风控处理记录主键
     * @return 结果
     */
    public int deleteWaybillRiskHandlerById(Long id);

    /**
     * 批量删除网络货运平台风控处理记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWaybillRiskHandlerByIds(Long[] ids);

    int batchInsertWaybillRiskHandler(List<WaybillRiskHandler> handlers);

    List<WaybillRiskQueryRes> selectWaybillRiskHandlerProcessedList(@Param("query") WaybillRiskQuery query);
}
