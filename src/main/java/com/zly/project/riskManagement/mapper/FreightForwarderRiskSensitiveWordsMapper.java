package com.zly.project.riskManagement.mapper;

import com.zly.project.riskManagement.domain.FreightForwarderRiskSensitiveWords;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 网络货运平台风控敏感词Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
public interface FreightForwarderRiskSensitiveWordsMapper {
    /**
     * 查询网络货运平台风控敏感词
     *
     * @param id 网络货运平台风控敏感词主键
     * @return 网络货运平台风控敏感词
     */
    public FreightForwarderRiskSensitiveWords selectFreightForarderRiskSensitiveWordsById(Long id);

    /**
     * 查询网络货运平台风控敏感词列表
     *
     * @param freightForarderRiskSensitiveWords 网络货运平台风控敏感词
     * @return 网络货运平台风控敏感词集合
     */
    public List<FreightForwarderRiskSensitiveWords> selectFreightForarderRiskSensitiveWordsList(FreightForwarderRiskSensitiveWords freightForarderRiskSensitiveWords);

    /**
     * 新增网络货运平台风控敏感词
     *
     * @param freightForarderRiskSensitiveWords 网络货运平台风控敏感词
     * @return 结果
     */
    public int insertFreightForarderRiskSensitiveWords(FreightForwarderRiskSensitiveWords freightForarderRiskSensitiveWords);

    /**
     * 修改网络货运平台风控敏感词
     *
     * @param freightForarderRiskSensitiveWords 网络货运平台风控敏感词
     * @return 结果
     */
    public int updateFreightForarderRiskSensitiveWords(FreightForwarderRiskSensitiveWords freightForarderRiskSensitiveWords);

    /**
     * 删除网络货运平台风控敏感词
     *
     * @param id 网络货运平台风控敏感词主键
     * @return 结果
     */
    public int deleteFreightForarderRiskSensitiveWordsById(Long id);

    /**
     * 批量删除网络货运平台风控敏感词
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFreightForarderRiskSensitiveWordsByIds(Long[] ids);

    int batchInsertRiskSensitiveWords(List<FreightForwarderRiskSensitiveWords> sensitiveWords);

    List<FreightForwarderRiskSensitiveWords> selectFreightForwarderRiskSensitiveWordsByEffectiveTime();

    int deleteFreightForwarderRiskSensitiveWordsByFreightForwarderIds(List<Long> sensitiveWordFreightForwarderIds);

    int updateSensitiveWordsStates(@Param("sensitiveWordIds")List<Long> sensitiveWordIds, @Param("state")Integer state);

    List<FreightForwarderRiskSensitiveWords> selectFreightForwarderRiskSensitiveWordsByRiskAndFreightForwarderId(@Param("riskId")Long riskId, @Param("freightForwarderId")Long freightForwarderId);

    List<FreightForwarderRiskSensitiveWords> selectRiskSensitiveWordsByRiskAndSensitiveWords(@Param("riskId")Long riskId, @Param("sensitiveWords")List<String> sensitiveWords, @Param("freightForwarderId")Long freightForwarderId);
}
