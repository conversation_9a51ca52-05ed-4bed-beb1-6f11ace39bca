package com.zly.project.financeOperation.mapper;

import com.zly.project.financeOperation.domain.FinanceUser;
import com.zly.project.financeOperation.domain.req.FinanceUserReq;
import com.zly.project.system.domain.ClickLoginRes;
import com.zly.project.tenant.domain.res.EnterpriseInfoRes;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 用户表 数据层
 *
 * <AUTHOR>
 */
public interface FinanceUserMapper {
	/**
	 * 根据条件分页查询用户列表
	 *
	 * @param sysUser
	 *            用户信息
	 * @return 用户信息集合信息
	 */
	List<FinanceUser> selectUserList(FinanceUserReq sysUser);

	/**
	 * 通过用户名查询用户
	 *
	 * @param userName
	 *            用户名
	 * @return 用户对象信息
	 */
	FinanceUser selectUserByUserName(String userName);

	/**
	 * 通过用户ID查询用户
	 *
	 * @param userId
	 *            用户ID
	 * @return 用户对象信息
	 */
	FinanceUser selectUserById(Long userId);

	/**
	 * 新增用户信息
	 *
	 * @param user
	 *            用户信息
	 * @return 结果
	 */
	int insertUser(FinanceUser user);

	/**
	 * 修改用户信息
	 *
	 * @param user
	 *            用户信息
	 * @return 结果
	 */
	int updateUser(FinanceUser user);

	/**
	 * 修改用户头像
	 *
	 * @param userName
	 *            用户名
	 * @param avatar
	 *            头像地址
	 * @return 结果
	 */
	int updateUserAvatar(@Param("userName") String userName, @Param("avatar") String avatar);

	/**
	 * 通过用户ID删除用户
	 *
	 * @param userId
	 *            用户ID
	 * @return 结果
	 */
	int deleteUserById(Long userId);

	/**
	 * 批量删除用户信息
	 *
	 * @param userIds
	 *            需要删除的用户ID
	 * @return 结果
	 */
	int deleteUserByIds(Long[] userIds);

	/**
	 * 校验用户名称是否唯一
	 *
	 * @param userName
	 *            用户名称
	 * @return 结果
	 */
	int checkUserNameUnique(String userName);

	/**
	 * 校验手机号码是否唯一
	 *
	 * @param phonenumber
	 *            手机号码
	 * @return 结果
	 */
	FinanceUser checkPhoneUnique(String phonenumber);

	/**
	 * 校验email是否唯一
	 *
	 * @param email
	 *            用户邮箱
	 * @return 结果
	 */
	FinanceUser checkEmailUnique(String email);

	int resetUserPwd(@Param("userName") String userName, @Param("password") String password, @Param("pwd") String pwd);

	List<FinanceUser> selectUserListByCustomerId(Long customerId);

	List<FinanceUser> selectAdminUsersByCustomerId(Long customerId);

	int updateUserDelFlag(@Param("customerId") Long customerId, @Param("userId") Long userId);

	List<Long> queryDelFlagUser(@Param("customerId") Long customerId, @Param("userId") Long userId);

	List<ClickLoginRes> queryUser(Map<String, Object> map);

	List<EnterpriseInfoRes> getEnterpriseInfo(String phone);

	List<FinanceUser> selectFinanceUserList();

	void updateUserLastEmail(@Param("userId") Long userId, @Param("email") String email);

	String getLastEmail(Long userId);
}
