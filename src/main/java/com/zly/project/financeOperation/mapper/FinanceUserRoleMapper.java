package com.zly.project.financeOperation.mapper;

import com.zly.project.financeOperation.domain.FinanceUserRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户与角色关联表 数据层
 *
 * <AUTHOR>
 */
public interface FinanceUserRoleMapper {

	/**
	 * 获取用户关联的角色ID
	 *
	 * @param userId
	 *            用户ID
	 * @return
	 */
	List<Long> selectUserRoleIds(Long userId);

	/**
	 * 获取角色ID关联的用户
	 *
	 * @param roleId
	 *            角色ID
	 * @return
	 */
	List<Long> selectRoleRelatedUserIds(Long roleId);

	/**
	 * 通过用户ID删除用户和角色关联
	 *
	 * @param userId
	 *            用户ID
	 * @return 结果
	 */
	int deleteUserRoleByUserId(Long userId);

	/**
	 * 通过用户ID删除用户和角色关联
	 *
	 * @param roleId
	 *            角色ID
	 * @return 结果
	 */
	int deleteUserRoleByRoleId(Long roleId);

	/**
	 * 批量删除用户和角色关联
	 *
	 * @param userIds
	 *            需要删除的数据ID
	 * @return 结果
	 */
	int deleteUserRoleByUserIds(Long[] userIds);

	/**
	 * 通过角色ID查询角色使用数量
	 *
	 * @param roleId
	 *            角色ID
	 * @return 结果
	 */
	int countUserRoleByRoleId(Long roleId);

	/**
	 * 批量新增用户角色信息
	 *
	 * @param userRoleList
	 *            用户角色列表
	 * @return 结果
	 */
	int batchUserRole(List<FinanceUserRole> userRoleList);

	/**
	 * 删除用户和角色关联信息
	 *
	 * @param userRole
	 *            用户和角色关联信息
	 * @return 结果
	 */
	int deleteUserRoleInfo(FinanceUserRole userRole);

	/**
	 * 批量取消授权用户角色
	 *
	 * @param roleId
	 *            角色ID
	 * @param userIds
	 *            需要删除的用户数据ID
	 * @return 结果
	 */
	int deleteUserRoleInfos(@Param("roleId") Long roleId, @Param("userIds") Long[] userIds);

	List<Long> selectRoleIdsByUserId(Long userId);

	List<Long> selectAdminByUserIds(Long[] userIds);
}
