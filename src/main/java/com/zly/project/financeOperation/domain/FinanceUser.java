package com.zly.project.financeOperation.domain;

import com.zly.common.entity.IUser;
import com.zly.common.xss.Xss;
import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * @ClassName: FinanceUser
 * @Description: 用户信息
 * @Author: CYS
 * @Date: 2023/6/12 9:44
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class FinanceUser extends BaseEntity implements IUser {

	private static final long serialVersionUID = 1L;

	/** 用户ID */
	@ApiModelProperty("用户ID")
	private Long userId;

	@ApiModelProperty("网络货运人ID（目前为0）")
	private Long FreightForwarderId;

	/** 托运人ID */
	@ApiModelProperty("托运人ID")
	private Long customerId;

	/** 用户账号 */
	@ApiModelProperty("用户账号")
	private String userName;

	/**
	 * 用户昵称
	 */
	@ApiModelProperty("用户昵称")
	private String nickName;

	/**
	 * 用户邮箱
	 */
	@ApiModelProperty("用户邮箱")
	private String email;

	// 上次输入邮箱
	private String lastEmail;

	/**
	 * 手机号码
	 */
	@ApiModelProperty("手机号码")
	private String phonenumber;

	/**
	 * 用户性别
	 */
	@Excel(name = "用户性别", readConverterExp = "0=男,1=女,2=未知")
	@ApiModelProperty("用户性别")
	private String sex;

	/** 用户头像 */
	@ApiModelProperty("用户头像")
	private String avatar;

	/** 密码 */
	@ApiModelProperty("密码")
	private String password;

	/** 密码 */
	@ApiModelProperty("密码")
	@Excel(name = "密码")
	private String pwd;

	/** 帐号状态（0正常 1停用） */
	@Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
	@ApiModelProperty("帐号状态（0正常 1停用）")
	private String status;

	/** 删除标志（0代表存在 2代表删除） */
	@ApiModelProperty("删除标志（0代表存在 2代表删除）")
	private String delFlag;

	/** 最后登录IP */
	@Excel(name = "最后登录IP", type = Excel.Type.EXPORT)
	@ApiModelProperty("最后登录IP")
	private String loginIp;

	/** 最后登录时间 */
	@Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Excel.Type.EXPORT)
	@ApiModelProperty("最后登录时间")
	private Date loginDate;

	@ApiModelProperty("项目权限：0 指定项目 1 所有项目")
	private Integer defaultProject;

	public FinanceUser() {

	}

	public FinanceUser(Long userId) {
		this.userId = userId;
	}

	public boolean isAdmin() {
		return isAdmin(this.userId);
	}

	public static boolean isAdmin(Long userId) {
		return userId != null && 1L == userId;
	}

	@Xss(message = "用户昵称不能包含脚本字符")
	@Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	@Xss(message = "用户账号不能包含脚本字符")
	@NotBlank(message = "用户账号不能为空")
	@Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	@Email(message = "邮箱格式不正确")
	@Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	@Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
	public String getPhonenumber() {
		return phonenumber;
	}
}
