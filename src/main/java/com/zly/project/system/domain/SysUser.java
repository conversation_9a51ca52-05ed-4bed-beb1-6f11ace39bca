package com.zly.project.system.domain;

import java.util.Date;
import java.util.List;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import com.zly.common.entity.IUser;
import com.zly.common.xss.Xss;
import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.aspectj.lang.annotation.Excel.ColumnType;
import com.zly.framework.aspectj.lang.annotation.Excel.Type;
import com.zly.framework.aspectj.lang.annotation.Excels;
import com.zly.framework.web.domain.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysUser extends BaseEntity implements IUser {
	private static final long serialVersionUID = 1270350624738910518L;

	@ApiModelProperty("用户ID")
	private Long userId;

	@ApiModelProperty("网络货运人ID")
	private Long freightForwarderId;

	@ApiModelProperty("部门ID")
	private Long deptId;

	@ApiModelProperty("用户账号")
	private String userName;

	@ApiModelProperty("用户昵称")
	private String nickName;

	@ApiModelProperty("用户邮箱")
	private String email;

	@ApiModelProperty("手机号码")
	private String phonenumber;

	@ApiModelProperty("用户性别")
	private String sex;

	@ApiModelProperty("用户头像")
	private String avatar;

	@ApiModelProperty("密码")
	private String password;

	@ApiModelProperty("密码")
	private String pwd;

	@ApiModelProperty("帐号状态（0正常 1停用）")
	private String status;

	@ApiModelProperty("删除标志（0代表存在 2代表删除）")
	private String delFlag;

	@Excel(name = "最后登录IP", type = Type.EXPORT)
	@ApiModelProperty("最后登录IP")
	private String loginIp;

	@Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Type.EXPORT)
	@ApiModelProperty("最后登录时间")
	private Date loginDate;

	@Excels({ @Excel(name = "部门名称", targetAttr = "deptName", type = Type.EXPORT), @Excel(name = "部门负责人", targetAttr = "leader", type = Type.EXPORT) })
	@ApiModelProperty("部门对象")
	private SysDept dept;

	@ApiModelProperty("角色对象")
	private List<SysRole> roles;

	@ApiModelProperty("角色组")
	private Long[] roleIds;

	@ApiModelProperty("岗位组")
	private Long[] postIds;

	@ApiModelProperty("角色ID")
	private Long roleId;

	@ApiModelProperty("项目权限ID")
	private List<Long> contractId;

	@ApiModelProperty("项目权限：0 指定项目 1 所有项目")
	private Integer defaultProject;

	public SysUser() {

	}

	public SysUser(Long userId) {
		this.userId = userId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public boolean isAdmin() {
		return isAdmin(this.userId);
	}

	public static boolean isAdmin(Long userId) {
		return userId != null && 1L == userId;
	}

	@Xss(message = "用户昵称不能包含脚本字符")
	@Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	@Xss(message = "用户账号不能包含脚本字符")
	@NotBlank(message = "用户账号不能为空")
	@Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	@Email(message = "邮箱格式不正确")
	@Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	@Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
	public String getPhonenumber() {
		return phonenumber;
	}

}
