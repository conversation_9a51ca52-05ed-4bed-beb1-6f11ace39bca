package com.zly.project.system.mapper;

import java.util.List;

import com.zly.project.system.domain.SysCost;
import com.zly.project.system.domain.SysCostChargeCustomer;

import javax.annotation.Resource;

/**
 * 查询所有的收费商户
 * 
 * <AUTHOR>
 * @date 2022-01-12
 */
@Resource
public interface SysCostChargeCustomerMapper {

	/**
	 * 查询所有的收费商户
	 * @return
	 */
	public List<SysCostChargeCustomer> selectSysCostChargeCustomerList();

}
