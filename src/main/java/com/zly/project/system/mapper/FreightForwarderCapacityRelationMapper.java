package com.zly.project.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.zly.project.carrier.driver.domain.res.DriverRes;
import com.zly.project.system.domain.FreightForwarderCapacityRelation;
import com.zly.project.system.domain.res.FreightForwarderCapacityRelationRes;
import com.zly.project.transport.waybill.domain.req.EntrustedProtocolReq;
import com.zly.project.transport.waybill.domain.res.EntrustedProtocolRes;

/**
 * 网络货运人车队长关联Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
public interface FreightForwarderCapacityRelationMapper {
    /**
     * 查询网络货运人车队长关联
     *
     * @param freightForwarderId 网络货运人车队长关联主键
     * @return 网络货运人车队长关联
     */
    public FreightForwarderCapacityRelation selectFreightForwarderCapacityRelationById(Long freightForwarderId);

    /**
     * 查询网络货运人车队长关联列表
     *
     * @param freightForwarderCapacityRelation 网络货运人车队长关联
     * @return 网络货运人车队长关联集合
     */
    public List<FreightForwarderCapacityRelation> selectFreightForwarderCapacityRelationList(FreightForwarderCapacityRelation freightForwarderCapacityRelation);

    /**
     * 新增网络货运人车队长关联
     *
     * @param freightForwarderCapacityRelation 网络货运人车队长关联
     * @return 结果
     */
    public int insertFreightForwarderCapacityRelation(FreightForwarderCapacityRelation freightForwarderCapacityRelation);

    /**
     * 修改网络货运人车队长关联
     *
     * @param freightForwarderCapacityRelation 网络货运人车队长关联
     * @return 结果
     */
    public int updateFreightForwarderCapacityRelation(FreightForwarderCapacityRelation freightForwarderCapacityRelation);

    /**
     * 删除网络货运人车队长关联
     *
     * @param freightForwarderId 网络货运人车队长关联主键
     * @return 结果
     */
    public int deleteFreightForwarderCapacityRelationById(Long freightForwarderId);

	/**
	 * 批量删除网络货运人车队长关联
	 *
	 * @param freightForwarderIds
	 * 		需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteFreightForwarderCapacityRelationByIds(Long[] freightForwarderIds);

	List<FreightForwarderCapacityRelation> selectFreightNameByCapacityIdCardAndCustomerId(String idCard, Long customerId, Integer type);

	List<FreightForwarderCapacityRelation> selectByCapacityIdCardAndForwarderId(String idCard, Long freightForwarderId, Integer type, Integer agreementState);

	List<FreightForwarderCapacityRelation> selectFreightNameByCapacityIdCardAndFreightForwarderId(String idCard, Long freightForwarderId, Integer type);

	List<FreightForwarderCapacityRelation> selectFreightNameByCapacityIdCard(@Param("idCard") String idCard, @Param("type") Integer type);

	// 查询司机代开协议
	List<FreightForwarderCapacityRelationRes> selectFreightForwarderCapacityRelationResListByIdcard(String identityCard);

	List<DriverRes> selectFreightNameByCapacityVehicleAndCustomerId(Long vehicleId, Long customerId);

	List<DriverRes> selectFreightNameByCapacityVehicleAndFreightForwarderId(@Param("vehicleId") Long vehicleId, @Param("freightForwarderId") Long freightForwarderId,
			@Param("signType") Integer signType);

	List<DriverRes> selectFreightNameByCapacityVehicle(@Param("vehicleId") Long vehicleId, @Param("signType") Integer signType);

	int insertFreightForwarderCapacityRelations(List<FreightForwarderCapacityRelationRes> freightForwarderCapacityRelationRes);

	List<FreightForwarderCapacityRelation> queryByState(int state);

	FreightForwarderCapacityRelation selectEffectiveContract(@Param("identityCard") String identityCard, @Param("freightForwarderId") Long freightForwarderId);

	FreightForwarderCapacityRelation selectEffectiveProxyContractByResource(@Param("identityCard") String identityCard, @Param("freightForwarderId") Long freightForwarderId,
			@Param("resource") Integer resource);

	void updateTake(List<Long> ids);

	void updateWillExpire(List<Long> ids);

	void updateExpire(List<Long> ids);

	List<FreightForwarderCapacityRelation> selectByIdcardsAndForwarderId(List<String> idcards, Long freightForwarderId);

	List<EntrustedProtocolRes> entrustedProtocolList(EntrustedProtocolReq req);

	Integer entrustedProtocolCount(Long freightForwarderId);

	void auditCapacityRelation(Long freightForwarderId);

	List<String> auditIdentityCards(Long freightForwarderId);
}
