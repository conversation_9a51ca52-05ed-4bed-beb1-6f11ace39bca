package com.zly.project.service.customerservice.mapper;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.zly.project.service.customerservice.domain.ContractServiceInfo;

/**
 * 项目合约服务管理Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-10
 */
@Repository
public interface ContractServiceInfoMapper
{
    /**
     * 查询客户服务管理
     *
     * @param id 客户服务管理主键
     * @return 客户服务管理
     */
    public ContractServiceInfo selectContractServiceInfoById(Long id);

    /**
     * 查询客户服务管理列表
     *
     * @param ContractServiceInfo 客户服务管理
     * @return 客户服务管理集合
     */
    public List<ContractServiceInfo> selectContractServiceInfoList(ContractServiceInfo ContractServiceInfo);

    /**
     * 新增客户服务管理
     *
     * @param ContractServiceInfo 客户服务管理
     * @return 结果
     */
    public int insertContractServiceInfo(ContractServiceInfo ContractServiceInfo);

    /**
     * 修改客户服务管理
     *
     * @param ContractServiceInfo 客户服务管理
     * @return 结果
     */
    public int updateContractServiceInfo(ContractServiceInfo ContractServiceInfo);

    /**
     * 删除客户服务管理
     *
     * @param id 客户服务管理主键
     * @return 结果
     */
    public int deleteContractServiceInfoById(Long id);

    /**
     * 批量删除客户服务管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteContractServiceInfoByIds(Long[] ids);

	List<ContractServiceInfo> selectByContractIds(List<Long> contractIds);

	void insertContractServiceInfos(List<ContractServiceInfo> contractServiceInfos);

	List<ContractServiceInfo> selectByContractIdsAndServiceIds(List<Long> contractIds, List<Long> serviceIds);

}
