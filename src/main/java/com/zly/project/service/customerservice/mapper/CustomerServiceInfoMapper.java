package com.zly.project.service.customerservice.mapper;

import com.zly.project.service.customerservice.domain.CustomerServiceInfo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 客户服务管理Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-10
 */
@Repository
public interface CustomerServiceInfoMapper {
	/**
	 * 查询客户服务管理
	 *
	 * @param id
	 * 		客户服务管理主键
	 * @return 客户服务管理
	 */
	CustomerServiceInfo selectCustomerServiceInfoById(Long id);

	/**
	 * 查询客户服务管理列表
	 *
	 * @param customerServiceInfo
	 * 		客户服务管理
	 * @return 客户服务管理集合
	 */
	List<CustomerServiceInfo> selectCustomerServiceInfoList(CustomerServiceInfo customerServiceInfo);

	/**
	 * 新增客户服务管理
	 *
	 * @param customerServiceInfo
	 * 		客户服务管理
	 * @return 结果
	 */
	int insertCustomerServiceInfo(CustomerServiceInfo customerServiceInfo);

	/**
	 * 修改客户服务管理
	 *
	 * @param customerServiceInfo
	 * 		客户服务管理
	 * @return 结果
	 */
	int updateCustomerServiceInfo(CustomerServiceInfo customerServiceInfo);

	/**
	 * 删除客户服务管理
	 *
	 * @param id
	 * 		客户服务管理主键
	 * @return 结果
	 */
	int deleteCustomerServiceInfoById(Long id);

	/**
	 * 批量删除客户服务管理
	 *
	 * @param ids
	 * 		需要删除的数据主键集合
	 * @return 结果
	 */
	int deleteCustomerServiceInfoByIds(Long[] ids);

	List<CustomerServiceInfo> selectByCustomerIds(List<Long> customerIds);

	int deleteCustomerServiceInfoByCustomerId(Long customerId);
}
