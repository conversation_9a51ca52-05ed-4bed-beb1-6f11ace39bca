package com.zly.project.agentOperation.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.zly.project.agentOperation.domain.AgentUser;
import com.zly.project.system.domain.ClickLoginRes;
import com.zly.project.tenant.domain.res.EnterpriseInfoRes;

/**
 * 用户表 数据层
 *
 * <AUTHOR>
 */
public interface AgentUserMapper {
	/**
	 * 根据条件分页查询用户列表
	 *
	 * @param sysUser
	 *            用户信息
	 * @return 用户信息集合信息
	 */
	List<AgentUser> selectUserList(AgentUser sysUser);

	/**
	 * 通过用户名查询用户
	 *
	 * @param userName
	 *            用户名
	 * @return 用户对象信息
	 */
	AgentUser selectUserByUserName(String userName);

	/**
	 * 通过用户ID查询用户
	 *
	 * @param userId
	 *            用户ID
	 * @return 用户对象信息
	 */
	AgentUser selectUserById(Long userId);

	/**
	 * 新增用户信息
	 *
	 * @param user
	 *            用户信息
	 * @return 结果
	 */
	int insertUser(AgentUser user);

	/**
	 * 修改用户信息
	 *
	 * @param user
	 *            用户信息
	 * @return 结果
	 */
	int updateUser(AgentUser user);

	/**
	 * 修改用户头像
	 *
	 * @param userName
	 *            用户名
	 * @param avatar
	 *            头像地址
	 * @return 结果
	 */
	int updateUserAvatar(@Param("userName") String userName, @Param("avatar") String avatar);

	/**
	 * 通过用户ID删除用户
	 *
	 * @param userId
	 *            用户ID
	 * @return 结果
	 */
	int deleteUserById(Long userId);

	/**
	 * 批量删除用户信息
	 *
	 * @param userIds
	 *            需要删除的用户ID
	 * @return 结果
	 */
	int deleteUserByIds(Long[] userIds);

	/**
	 * 校验用户名称是否唯一
	 *
	 * @param userName
	 *            用户名称
	 * @return 结果
	 */
	int checkUserNameUnique(String userName);

	/**
	 * 校验手机号码是否唯一
	 *
	 * @param phonenumber
	 *            手机号码
	 * @return 结果
	 */
	AgentUser checkPhoneUnique(String phonenumber);

	/**
	 * 校验email是否唯一
	 *
	 * @param email
	 *            用户邮箱
	 * @return 结果
	 */
	AgentUser checkEmailUnique(String email);

	List<Long> selectHasAllProjectsUserByIds(List<Long> userIds);

	List<AgentUser> selectAdminUsersByFreightForwarderId(Long freightForwarderId);

	public int resetUserPwd(@Param("userName") String userName, @Param("password") String password, @Param("pwd") String pwd);

	List<ClickLoginRes> queryUser(Map<String, Object> map);

	List<EnterpriseInfoRes> getEnterpriseInfo(String phone);

	List<AgentUser> selectAgentUserList();

	List<AgentUser> selectUserByIds(List<Long> userIds);

	List<AgentUser> selectAgentUserListByFreightForwarderId(Long freightForwardId);

	int updateAgentUserStatusByUserIds(List<Long> shipAgentUserIds);
}
