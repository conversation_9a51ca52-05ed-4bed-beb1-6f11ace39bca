package com.zly.project.agentOperation.mapper;

import java.util.List;

import com.zly.project.agentOperation.domain.AgentRole;
import com.zly.project.agentOperation.domain.AgentUserRoleInfo;
import com.zly.project.agentOperation.domain.req.AgentRoleReq;

/**
 * 角色表 数据层
 *
 * <AUTHOR>
 */
public interface AgentRoleMapper {
	/**
	 * 根据条件分页查询角色数据
	 *
	 * @param role
	 *            角色信息
	 * @return 角色数据集合信息
	 */
	List<AgentRole> selectRoleList(AgentRole role);

	/**
	 * 根据用户ID查询角色
	 *
	 * @param userId
	 *            用户ID
	 * @param freightForwarderId
	 * @return 角色列表
	 */
	List<AgentRole> selectRolePermissionByUserId(Long userId, long freightForwarderId);

	/**
	 * 查询所有角色
	 *
	 * @return 角色列表
	 */
	List<AgentRole> selectRoleAll(Long freightForwarderId);

	/**
	 * 根据用户ID获取角色选择框列表
	 *
	 * @param userId
	 *            用户ID
	 * @return 选中角色ID列表
	 */
	List<Long> selectRoleIdsByUserId(Long userId);

	/**
	 * 通过角色ID查询角色
	 *
	 * @param roleId
	 *            角色ID
	 * @return 角色对象信息
	 */
	AgentRole selectRoleById(Long roleId);

	/**
	 * 根据用户ID查询角色
	 *
	 * @param userName
	 *            用户名
	 * @return 角色列表
	 */
	List<AgentRole> selectRolesByUserName(String userName);

	/**
	 * 根据用户ID查询角色
	 *
	 * @param userId
	 *            用户ID
	 * @return 角色列表
	 */
	List<AgentRole> selectUserRoleList(Long userId);

	/**
	 * 批量获取用户角色列表信息
	 *
	 * @param userIds
	 *            用户ID列表
	 * @return 角色列表
	 */
	List<AgentUserRoleInfo> selectUsersRoleList(List<Long> userIds);

	/**
	 * 校验角色名称是否唯一
	 *
	 * @param role
	 *            角色名称
	 * @return 角色信息
	 */
	AgentRole checkRoleNameUnique(AgentRole role);

	/**
	 * 校验角色权限是否唯一
	 *
	 * @param role
	 *            角色权限
	 * @return 角色信息
	 */
	AgentRole checkRoleKeyUnique(AgentRole role);

	/**
	 * 修改角色信息
	 *
	 * @param role
	 *            角色信息
	 * @return 结果
	 */
	int updateRole(AgentRole role);

	/**
	 * 新增角色信息
	 *
	 * @param role
	 *            角色信息
	 * @return 结果
	 */
	int insertRole(AgentRole role);

	/**
	 * 通过角色ID删除角色
	 *
	 * @param roleId
	 *            角色ID
	 * @return 结果
	 */
	int deleteRoleById(Long roleId);

	/**
	 * 批量删除角色信息
	 *
	 * @param roleIds
	 *            需要删除的角色ID
	 * @return 结果
	 */
	int deleteRoleByIds(Long[] roleIds);

	List<AgentRole> selectRoleByPresetId(Long presetRoleId);

	List<AgentRole> selectRoleListByAgentRoleReq(AgentRoleReq roleReq);
}
