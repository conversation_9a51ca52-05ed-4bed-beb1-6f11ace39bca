package com.zly.project.agentOperation.mapper;

import java.util.List;

import com.zly.project.agentOperation.domain.AgentMenu;
import com.zly.project.agentOperation.domain.AgentRoleMenu;

/**
 * 角色与菜单关联表 数据层
 *
 * <AUTHOR>
 */
public interface AgentRoleMenuMapper {
	/**
	 * 查询菜单使用数量
	 *
	 * @param menuId
	 *            菜单ID
	 * @return 结果
	 */
	int checkMenuExistRole(Long menuId);

	/**
	 * 通过角色ID删除角色和菜单关联
	 *
	 * @param roleId
	 *            角色ID
	 * @return 结果
	 */
	int deleteRoleMenuByRoleId(Long roleId);

	/**
	 * 批量删除角色菜单关联信息
	 *
	 * @param ids
	 *            需要删除的数据ID
	 * @return 结果
	 */
	int deleteRoleMenu(Long[] ids);

	/**
	 * 批量新增角色菜单信息
	 *
	 * @param roleMenuList
	 *            角色菜单列表
	 * @return 结果
	 */
	int batchRoleMenu(List<AgentRoleMenu> roleMenuList);

	/**
	 * 获取角色的菜单ID
	 *
	 * @param roleId
	 *            角色ID
	 * @return 结果
	 */
	List<Long> selectRoleMenuIds(long roleId);

	/**
	 * 获取角色的菜单列表
	 *
	 * @param roleId
	 *            角色ID
	 * @return 结果
	 */
	List<AgentMenu> selectRoleMenuList(long roleId);

	List<AgentMenu> selectRoleParentMenuList(long roleId);

	List<AgentMenu> selectRoleMenuListAdmin();

	List<AgentMenu> selectRoleParentMenuListAdmin();

    List<AgentRoleMenu> selectRoleParentMenuByRoleIds(List<Long> agentRoleIds);
}
