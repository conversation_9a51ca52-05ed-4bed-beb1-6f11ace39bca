package com.zly.project.agentOperation.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.zly.project.agentOperation.domain.AgentMenu;

/**
 * 菜单表 数据层
 *
 * <AUTHOR>
 */
public interface AgentMenuMapper {
	/**
	 * 查询系统菜单列表
	 *
	 * @param menu
	 *            菜单信息
	 * @return 菜单列表
	 */
	List<AgentMenu> selectMenuList(AgentMenu menu);

	/**
	 * 根据用户所有权限
	 *
	 * @return 权限列表
	 */
	List<String> selectMenuPerms();

	/**
	 * 根据用户查询系统菜单列表
	 *
	 * @param menu
	 *            菜单信息
	 * @return 菜单列表
	 */
	List<AgentMenu> selectMenuListByUserId(AgentMenu menu);

	/**
	 * 根据用户ID查询权限
	 *
	 * @param userId
	 *            用户ID
	 * @return 权限列表
	 */
	List<String> selectMenuPermsByUserId(Long userId);

	/**
	 * 根据角色ID查询权限
	 *
	 * @param roleId
	 *            角色ID
	 * @return 权限列表
	 */
	List<String> selectMenuPermsByRoleId(Long roleId);

	/**
	 * 根据用户ID查询菜单
	 *
	 * @return 菜单列表
	 */
	List<AgentMenu> selectMenuTreeAll();

	/**
	 * 根据用户ID查询菜单
	 *
	 * @param userId
	 *            用户ID
	 * @return 菜单列表
	 */
	List<AgentMenu> selectMenuTreeByUserId(Long userId);

	/**
	 * 根据角色ID查询菜单树信息
	 *
	 * @param roleId
	 *            角色ID
	 * @param menuCheckStrictly
	 *            菜单树选择项是否关联显示(页面上的开关名叫“父子联动”)
	 * @return 选中菜单列表
	 */
	List<Long> selectMenuIdsByRoleId(@Param("roleId") Long roleId, @Param("menuCheckStrictly") boolean menuCheckStrictly);

	/**
	 * 根据菜单ID查询信息
	 *
	 * @param menuId
	 *            菜单ID
	 * @return 菜单信息
	 */
	AgentMenu selectMenuById(Long menuId);

	/**
	 * 是否存在菜单子节点
	 *
	 * @param menuId
	 *            菜单ID
	 * @return 结果
	 */
	int hasChildByMenuId(Long menuId);

	/**
	 * 新增菜单信息
	 *
	 * @param menu
	 *            菜单信息
	 * @return 结果
	 */
	int insertMenu(AgentMenu menu);

	/**
	 * 修改菜单信息
	 *
	 * @param menu
	 *            菜单信息
	 * @return 结果
	 */
	int updateMenu(AgentMenu menu);

	/**
	 * 删除菜单管理信息
	 *
	 * @param menuId
	 *            菜单ID
	 * @return 结果
	 */
	int deleteMenuById(Long menuId);

	/**
	 * 校验菜单名称是否唯一
	 *
	 * @param menuName
	 *            菜单名称
	 * @param parentId
	 *            父菜单ID
	 * @return 结果
	 */
	AgentMenu checkMenuNameUnique(@Param("menuName") String menuName, @Param("parentId") Long parentId);

	/**
	 * 根据角色查询相对应菜单
	 * @param roleId
	 * @return
	 */
	List<AgentMenu> selectMenusByRoleId(Long roleId);

}
