package com.zly.project.customerForwarderContract.mapper;

import java.util.List;

import com.zly.project.customerForwarderContract.domain.CustomerForwarderContract;
import com.zly.project.customerForwarderContract.domain.req.CustomerForwarderContractReq;

/**
 * 委托合同Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
public interface CustomerForwarderContractMapper {
	/**
	 * 查询委托合同
	 *
	 * @param id
	 *            委托合同主键
	 * @return 委托合同
	 */
	public CustomerForwarderContract selectCustomerForwarderContractById(Long id);

	/**
	 * 查询委托合同列表
	 *
	 * @param customerForwarderContract
	 *            委托合同
	 * @return 委托合同集合
	 */
	public List<CustomerForwarderContract> selectCustomerForwarderContractList(CustomerForwarderContract customerForwarderContract);

	/**
	 * 新增委托合同
	 *
	 * @param customerForwarderContract
	 *            委托合同
	 * @return 结果
	 */
	public int insertCustomerForwarderContract(CustomerForwarderContract customerForwarderContract);

	/**
	 * 修改委托合同
	 *
	 * @param customerForwarderContract
	 *            委托合同
	 * @return 结果
	 */
	public int updateCustomerForwarderContract(CustomerForwarderContract customerForwarderContract);

	/**
	 * 删除委托合同
	 *
	 * @param id
	 *            委托合同主键
	 * @return 结果
	 */
	public int deleteCustomerForwarderContractById(Long id);

	/**
	 * 批量删除委托合同
	 *
	 * @param ids
	 *            需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteCustomerForwarderContractByIds(Long[] ids);

	CustomerForwarderContract selectContractByCustomerAndState(Long customerId, int state, Long freightForwarderId);

	CustomerForwarderContract selectCustomerForwarderContractByContractCode(String contractNumber);

	// List<CustomerForwarderContractReq> selectCustomerForwarderContractReqList(CustomerForwarderContract customerForwarderContract);

	List<CustomerForwarderContract> selectContractListByFile(CustomerForwarderContract customerForwarderContract);

	List<CustomerForwarderContract> selectContractByCustomerIdsAndForwarderId(List<Long> customerIds, Long forwarderId);

	List<CustomerForwarderContractReq> selectContractList(CustomerForwarderContract customerForwarderContract);

	List<CustomerForwarderContractReq> selectContractListByTime(CustomerForwarderContract customerForwarderContract);

}
