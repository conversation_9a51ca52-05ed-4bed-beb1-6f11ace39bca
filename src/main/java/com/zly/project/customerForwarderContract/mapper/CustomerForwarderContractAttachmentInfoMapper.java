package com.zly.project.customerForwarderContract.mapper;

import java.util.List;

import com.zly.project.customerForwarderContract.domain.CustomerForwarderContractAttachmentInfo;

/**
 * 委托合同附件信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
public interface CustomerForwarderContractAttachmentInfoMapper {
	/**
	 * 查询委托合同附件信息
	 *
	 * @param id
	 *            委托合同附件信息主键
	 * @return 委托合同附件信息
	 */
	public CustomerForwarderContractAttachmentInfo selectCustomerForwarderContractAttachmentInfoById(Long id);

	/**
	 * 查询委托合同附件信息列表
	 *
	 * @param customerForwarderContractAttachmentInfo
	 *            委托合同附件信息
	 * @return 委托合同附件信息集合
	 */
	public List<CustomerForwarderContractAttachmentInfo> selectCustomerForwarderContractAttachmentInfoList(CustomerForwarderContractAttachmentInfo customerForwarderContractAttachmentInfo);

	/**
	 * 新增委托合同附件信息
	 *
	 * @param customerForwarderContractAttachmentInfo
	 *            委托合同附件信息
	 * @return 结果
	 */
	public int insertCustomerForwarderContractAttachmentInfo(CustomerForwarderContractAttachmentInfo customerForwarderContractAttachmentInfo);

	/**
	 * 修改委托合同附件信息
	 *
	 * @param customerForwarderContractAttachmentInfo
	 *            委托合同附件信息
	 * @return 结果
	 */
	public int updateCustomerForwarderContractAttachmentInfo(CustomerForwarderContractAttachmentInfo customerForwarderContractAttachmentInfo);

	/**
	 * 删除委托合同附件信息
	 *
	 * @param id
	 *            委托合同附件信息主键
	 * @return 结果
	 */
	public int deleteCustomerForwarderContractAttachmentInfoById(Long id);

	/**
	 * 批量删除委托合同附件信息
	 *
	 * @param ids
	 *            需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteCustomerForwarderContractAttachmentInfoByIds(Long[] ids);

	public List<CustomerForwarderContractAttachmentInfo> selectByRelationIds(List<Long> relationIds);

	int deleteCustomerForwarderContractAttachmentInfoByRelationIds(List<Long> forwarderContractIds);

    List<CustomerForwarderContractAttachmentInfo> selectFileNameAndUrlListByRelationId(Long relationId);
}
