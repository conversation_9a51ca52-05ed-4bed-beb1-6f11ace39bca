package com.zly.project.transport.waybill.mapper;

import com.zly.project.carrier.driver.domain.Driver;
import com.zly.project.carrier.driver.domain.dto.VehicleInfoDTO;
import com.zly.project.financeOperation.domain.req.AuditReq;
import com.zly.project.financeOperation.domain.req.ProjectOptionReq;
import com.zly.project.financeOperation.domain.req.RiskControllerReq;
import com.zly.project.financeOperation.domain.res.AuditRes;
import com.zly.project.financeOperation.domain.res.FinancingWaybillRes;
import com.zly.project.financeOperation.domain.res.ProjectOptionRes;
import com.zly.project.financeOperation.domain.res.RiskControllerRes;
import com.zly.project.financeOperation.domain.res.StatementWaybillInfo;
import com.zly.project.freightforwarder.domain.FrameworkContractInfo;
import com.zly.project.miniprogram.domain.PaymentFareInfo;
import com.zly.project.miniprogram.domain.res.WxWaybillRes;
import com.zly.project.operation.domain.req.OperationPlainReq;
import com.zly.project.settlement.domain.PayApply;
import com.zly.project.settlement.domain.request.PayApplyQueryReq;
import com.zly.project.settlement.domain.request.TradeFlowQueryReq;
import com.zly.project.statistics.domain.*;
import com.zly.project.taxUpload.domain.TaxUploadWaybillInfo;
import com.zly.project.taxUpload.domain.req.AbnormalWaybillQueryReq;
import com.zly.project.taxUpload.domain.res.AbnormalWaybillQueryRes;
import com.zly.project.transfer.domain.req.TransferReq;
import com.zly.project.transport.waybill.domain.ConfirmReceiptInfo;
import com.zly.project.transport.waybill.domain.Waybill;
import com.zly.project.transport.waybill.domain.WaybillExx;
import com.zly.project.transport.waybill.domain.WaybillFare;
import com.zly.project.transport.waybill.domain.dto.DriverRouteStatisticsDTO;
import com.zly.project.transport.waybill.domain.req.OrderQuery;
import com.zly.project.transport.waybill.domain.req.WaybillPolicyRecordReq;
import com.zly.project.transport.waybill.domain.req.WaybillQuery;
import com.zly.project.transport.waybill.domain.req.WaybillReq;
import com.zly.project.transport.waybill.domain.res.StatementMoneyRes;
import com.zly.project.transport.waybill.domain.res.WaybillAuditListRes;
import com.zly.project.transport.waybill.domain.res.WaybillPolicyRecordRes;
import com.zly.project.transport.waybill.domain.res.WaybillProxyInvoiceRes;
import com.zly.project.transport.waybill.domain.res.WaybillRes;
import com.zly.project.transport.waybill.domain.res.WaybillTableRes;
import com.zly.project.transport.waybill.domain.res.WaybillTableResOfFreightForwarder;
import com.zly.project.wlhy.model.UploginkWaybillQuery;
import com.zly.project.wlhy.model.UploginkWaybillRes;
import com.zly.project.wlhy.model.WaybillUploginkInfo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface WaybillMapperEx {

	/**
	 * 查询运单列表
	 *
	 * @param customerId
	 * @param contractId
	 * @return
	 */
	List<WaybillRes> matchWaybillList(long customerId, Long contractId);

	/**
	 * 查询运单列表
	 *
	 * @param waybillReq
	 * @return
	 */
	List<WaybillRes> waybillList(WaybillReq waybillReq);

	/**
	 * 查询运单列表
	 *
	 * @param waybillReq
	 * @return
	 */
	Long countWaybillForStatement(WaybillReq waybillReq);

	/**
	 * 批量删除
	 *
	 * @param ids      需要删除的数据ID
	 * @param updateBy
	 * @return 结果
	 */
	int removeWaybillByIds(@Param("ids") Long[] ids, @Param("updateBy") String updateBy);

	/**
	 * 查询各状态的运单数量
	 *
	 * @param status
	 * @return
	 */
	Integer getStatusCount(@Param("status") Integer status);

	Integer getTransportStatus(@Param("makeCodeId") Long makeCodeId, @Param("status") Integer status);

	PaymentFareInfo getPaymentStatus(@Param("makeCodeId") Long makeCodeId);

	/**
	 * 查询未付款的运单
	 *
	 * @param waybill
	 * @return
	 */
	List<WaybillRes> selectUnSettlementWaybillList(WaybillExx waybill);

	/**
	 * 查询合并付款的运单
	 *
	 * @param waybill
	 * @return
	 */
	List<Waybill> selectMergeSettlementWaybillList(WaybillExx waybill);

	/**
	 * 托运人我录的运单列表
	 *
	 * @param waybillReq
	 * @return
	 */
	List<WaybillTableRes> selectShipperWaybillList(WaybillQuery waybillReq);

	List<WaybillTableRes> selectShipperWaybillListSP(WaybillQuery waybillReq);

	/**
	 * 微信小程序承运人端运单列表
	 *
	 * @param waybillReq
	 * @return
	 */
	List<WxWaybillRes> wxCarrierWaybillList(WaybillReq waybillReq);

	/**
	 * 微信小程序托运人端运单列表
	 *
	 * @param waybillReq
	 * @return
	 */
	List<WxWaybillRes> wxShipperWaybillList(WaybillReq waybillReq);

	/**
	 * 查询 当前司机的 累计票数
	 *
	 * @param driverId
	 * @return
	 */
	int selectSumWayBill(Long driverId);

	/**
	 * 查询 当前司机的 当月票数
	 *
	 * @param driverId
	 *            司机编号
	 * @param startTime
	 *            开始时间
	 * @param endTime
	 *            结束时间
	 * @return
	 */
	int selectMonthWayBill(@Param("driverId") Long driverId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

	/**
	 * 根据运单号更新运单未付款和结算状态
	 *
	 * @param ids
	 * @return
	 */
	int updateWaybillByIds(@Param("array") Long[] ids, @Param("payTime") Date payTime);

	/**
	 * 批量提交
	 *
	 * @param ids
	 *            需要提交的数据ID
	 * @return 结果
	 */
	int submitWaybillByIds(@Param("ids") Long[] ids, @Param("userName") String userName);

	/**
	 * 修改运单
	 *
	 * @param waybill
	 *            运单
	 * @return 结果
	 */
	int editWaybill(Waybill waybill);

	/**
	 * 获取一批运单信息
	 *
	 * @param ids
	 * @return
	 */
	List<WaybillRes> getWaybillByIds(@Param("ids") List<Long> ids);

	/**
	 * 获取一批运单的应收运费总额
	 *
	 * @param ids
	 * @return
	 */
	BigDecimal getReceiveFareSumByWaybillIds(@Param("ids") List<Long> ids);

	/**
	 * 获取一批运单的单位信息
	 *
	 * @param ids
	 * @return
	 */
	List<Integer> getFeeUnitListByWaybillIds(@Param("ids") List<Long> ids);

	/**
	 * 查询要GPS实时定位的运单
	 *
	 * @return
	 */
	List<Waybill> getGpsPositioningWaybillList();

	/**
	 * 查询网络货运运单列表
	 *
	 * @param waybillQuery
	 * @return
	 */
	List<UploginkWaybillRes> uploginkWaybillList(UploginkWaybillQuery waybillQuery);

	/**
	 * 查询待上传的网络货运运单列表（上传服务使用）
	 *
	 * @param waybillQuery
	 * @return
	 */
	List<WaybillUploginkInfo> selectWaybillUploginkList(UploginkWaybillQuery waybillQuery);

	/**
	 * 查询上传成功的网络货运运单列表（补传资金流水单用）【非江苏省的运单】
	 *
	 * @param waybillQuery
	 * @return
	 */
	List<WaybillUploginkInfo> selectWaybillUploginkSuccessList(UploginkWaybillQuery waybillQuery);

	/**
	 * 查询司机或车辆进行中（未开始或运输中）的运单数量
	 *
	 * @param waybill
	 * @return 运单数量
	 */
	int countWaybillOnTransport(Waybill waybill);

	/**
	 * 根据司机id 查询运费总计
	 *
	 * @param driverId
	 * @return
	 */
	BigDecimal selectSumReceiveMoney(Long driverId);

	/**
	 * 根据司机id 查询待收款总计
	 *
	 * @param driverId
	 * @return
	 */
	BigDecimal selectWaitReceiveMoney(Long driverId);

	/**
	 * 根据司机id 查询已收款总计
	 *
	 * @param driverId
	 * @return
	 */
	BigDecimal selectOkReceiveMoney(Long driverId);

	/**
	 * 查询上一次接单的承运人
	 *
	 * @param makeCodeId
	 *            货源id
	 * @param carrierIdList
	 *            承运人集合
	 * @return
	 */
	Long getLatestCarrierId(@Param("makeCodeId") Long makeCodeId, @Param("carrierIdList") List<Long> carrierIdList);

	/**
	 * 申请付款运单列表
	 *
	 * @param waybillReq
	 * @return
	 */
	List<WaybillRes> tradeApplyWaybillList(WaybillReq waybillReq);

	List<WaybillRes> wxTradeApplyWaybillList(WaybillReq waybillReq);

	/**
	 * 需对其进行付款审核的运单列表
	 *
	 * @param waybillReq
	 * @return
	 */
	List<WaybillRes> tradeNeedApplyWaybillList(WaybillReq waybillReq);

	/**
	 * 检查运单的申请付款状态
	 *
	 * @param ids
	 *            运单ID集合
	 * @return 结果
	 */
	int tradeApplyVerify(@Param("ids") Long[] ids);

	/**
	 * 确认付款运单列表
	 *
	 * @param applyQuery
	 * @return
	 */
	List<PayApply> prePaymentApplyList(PayApplyQueryReq applyQuery);

	/**
	 * 更改运单结算状态
	 *
	 * @param waybillReq
	 * @return
	 */
	int updateWaybillSettleStatus(WaybillReq waybillReq);

	/**
	 * 根据运单id更新项目合约信息
	 *
	 * @param ids
	 * @return
	 */
	int updateWaybillFrameContractByIds(@Param("array") List<Long> ids, @Param("contractId") Long contractId, @Param("contractCode") String contractCode, @Param("contractName") String contractName);

	List<WaybillTableResOfFreightForwarder> freightForwarderWaybillList(WaybillReq waybillReq);

	List<WaybillRes> selectSubcontractWaybillList(WaybillReq waybillReq);

	List<WaybillRes> selectWaybillListByWaybillIds(@Param("array") List<Long> waybillIds);

	/**
	 * 物流金融端的待申请开票列表
	 *
	 * @param map
	 * @return
	 */
	List<WaybillRes> waybillListForFinanceStatement(Map<String, Object> map);
	List<WaybillRes> waybillListFinanceStatement(Map<String, Object> map);

	/**
	 * 托运人端的待申请开票列表
	 *
	 * @param map
	 * @return
	 */
	List<WaybillRes> waybillListForTenantStatement(Map<String, Object> map);

	List<WaybillRes> selectStatementWaybillList(@Param("array") List<Long> waybillIds);

	List<WaybillRes> selectStatementWaybillListByShipperId(@Param("array") List<Long> waybillIds, @Param("shipperId") Long shipperId);

	/**
	 * 根据运单号批量查询运单信息
	 *
	 * @param shippingNoteNumbers
	 * @return
	 */
	List<Waybill> selectWaybillList(@Param("shippingNoteNumbers") List<String> shippingNoteNumbers);

	/**
	 * 批量插入运单表
	 *
	 * @param waybillList
	 *            待插入运单列表
	 * @return
	 */
	int insertWaybillList(@Param("list") List<Waybill> waybillList);

	Integer getAllWaybillCount(Map<String, Object> map);

	Integer getMonthWaybills(Map<String, Object> map);

	int getDayWaybills(Map<String, Object> map);

	List<Long> selectUnArriveWaybillByDriverid(Long driverId);

	List<WxWaybillRes> wxWaybillFinishList(WaybillReq waybillReq);

	/**
	 * 更改运单状态
	 *
	 * @param waybillReq
	 * @return
	 */
	int updateWaybillStatus(WaybillReq waybillReq);

	/**
	 * 定时任务未确认费用，且时间以超过预计装货时间23.59的运单，设置状态为删除
	 */
	int updateWayBillStatusByToday(@Param("waybillIds") List<Long> waybillIds);

	List<Long> selectWaybillStatusByToday();

	int updateWayBillActualCarrierByCarrierIds(@Param("carrierIds") List<Long> carrierIds, @Param("carrierId") Long carrierId, @Param("name") String name, @Param("phone") String phone);

	List<Long> selectWayBillActualCarrierByCarrierIds(@Param("carrierIds") List<Long> carrierIds);

	int updateDriverInfoByDriverIds(List<Long> driverIds, Driver driver);

	List<Long> selectDriverInfoByDriverIds(@Param("driverIds") List<Long> driverIds);

	/**
	 * 金融服务系统签约主体托运人运单列表
	 *
	 * @param map
	 * @return
	 */
	List<FinancingWaybillRes> selectSignorWaybillList(Map<String, Object> map);

	/**
	 * 金融服务系统待对账运单列表
	 *
	 * @param map
	 * @return
	 */
	List<StatementWaybillInfo> selectPreStatementWaybillList(Map<String, Object> map);

	List<AuditRes> tradeCheckWaybillList(AuditReq req);

	List<RiskControllerRes> riskWaitList(RiskControllerReq req);

	List<RiskControllerRes> riskRefuseList(RiskControllerReq req);

	List<RiskControllerRes> pushList(RiskControllerReq req);

	List<RiskControllerRes> pushSuccessList(RiskControllerReq req);

	Integer batchUpdateDriverInfoIsComplete(@Param("driverId") Long driverId, @Param("infoIsComplete") Integer infoIsComplete);

	Integer batchUpdateVehicleInfoIsComplete(@Param("vehicleId") Long vehicleId, @Param("infoIsComplete") Integer infoIsComplete);

	Integer batchUpdateActualCarrierInfoIsComplete(@Param("actualCarrierIds") List<Long> actualCarrierIds, @Param("infoIsComplete") Integer infoIsComplete);

	Integer batchUpdateWaybillInfoIsComplete(@Param("waybillIds") List<Long> waybillIds, @Param("infoIsComplete") Integer infoIsComplete);

	Long countShipperWaybillList(WaybillQuery waybillQuery);

	List<Integer> selectFeeUnit(List<Long> waybillIds);

	Long countForwarderWaybills(WaybillReq waybillReq);

	List<Long> selectWaybillByContractId(Long contractId);

	int updateWaybillByContractId(Long contractId);

	List<ProjectOptionRes> riskWaitByNameList(ProjectOptionReq req);

	List<ProjectOptionRes> riskRefuseByNameList(ProjectOptionReq req);

	List<ProjectOptionRes> pushByNameList(ProjectOptionReq req);

	List<ProjectOptionRes> pushSuccessByNameList(ProjectOptionReq req);

	List<ProjectOptionRes> selectSignorWaybillByNameList(ProjectOptionReq req);

	List<ProjectOptionRes> tradeApplyWaybillByNameList(ProjectOptionReq req);

	List<ProjectOptionRes> tradeNeedApplyWaybillByNameList(ProjectOptionReq req);

	List<ProjectOptionRes> selectUpstreamStatementByNameList(ProjectOptionReq req);

	List<ProjectOptionRes> selectPreStatementWaybillByNameList(ProjectOptionReq req);

	List<ProjectOptionRes> selectUpstreamMarkStatementByNameList(ProjectOptionReq req);

	List<ProjectOptionRes> selectAuthContractByNameList(ProjectOptionReq req);

	List<ProjectOptionRes> selectShipperWaybillByNameList(ProjectOptionReq req);

	List<ProjectOptionRes> selectFinanceContractByNameList(ProjectOptionReq req);

	List<ProjectOptionRes> waybillListForStatementByName(ProjectOptionReq req);

	List<ProjectOptionRes> selectStatementByNameList(ProjectOptionReq req);

	List<ProjectOptionRes> prePaymentApplyByNameList(ProjectOptionReq req);

	List<ProjectOptionRes> queryCustomerFlowByNameList(ProjectOptionReq req);

	List<ProjectOptionRes> selectHasAccountContractByNameList(ProjectOptionReq req);

	/**
	 * 对外服务查询运单列表
	 *
	 * @param map
	 * @return
	 */
	List<WaybillRes> selectAppWaybillList(Map<String, Object> map);

	/**
	 * 对外服务查询运单总条数
	 *
	 * @param map
	 * @return
	 */
	Long selectAppWaybillCount(Map<String, Object> map);

	void mergeShippers(@Param("fromId")Long fromId,@Param("toId") Long toId,@Param("toName") String toName);

    List<WaybillReq> selectLastYearUnpaidFare();

	List<WaybillRes> selectParentCustomerNameByWaybillIds(List<Long> waybillIds);

	List<WaybillReq> selectLastYearTradeApply();

    BigDecimal statisticsUnpaidFareByContractId(@Param("contractId")Long contractId,@Param("customerId") Long customerId);

	BigDecimal paymentReviewTotalAmountByContractId(WaybillReq waybillReq);

	BigDecimal statisticsApplyMoneyByContractId(TradeFlowQueryReq req);

	WaybillTableRes selectShipperWaybillListSum(WaybillQuery req);

	FinancingWaybillRes selectSignorWaybillSum(Map<String, Object> map);

	/**
	 * 查询运单列表(ops)
	 *
	 * @param waybillReq
	 * @return
	 */
	List <WaybillTableRes> OpsWaybillList(WaybillReq waybillReq);


	List<WaybillReq> selectWayBillByCarrierIds(List<Long> ids);

	List<WaybillReq> selectWayBillByVehicleIds(List<Long> ids);

	List<WaybillReq> selectWayBillByDriverIds(List<Long> ids);

	List<String> selectPendingPaymentCustomerName(List<Long> ids);

	int batchModifyWaybillReceiptStatus(List<Long> ids);

	Integer queryUnPaidWaybillByChooseId(Map<String, Long> map);

	int batchModifyWaybillConsignorInfo(@Param("ids") List<Long> ids,@Param("consignorId") Long consignorId);

	int batchModifyWaybillConsigneeInfo(@Param("ids")List<Long> ids, @Param("consigneeId")Long consigneeId);

    int batchModifyWaybillMakeCode(@Param("ids")List<Long> ids, @Param("makeCodeId")Long makeCodeId);

	List<RiskControllerRes> pushOverruleList(RiskControllerReq req);
	List<WaybillRes> queryListByIdsGroupByAddress(@Param("waybillIds")List<Long> waybillIds,@Param("addressType") Integer addressType);

	List<WaybillRes> queryListByIds(@Param("waybillIds")List<Long> waybillIds,@Param("addressType") Integer addressType);

	List<PayApply> prePaymentApplyListWithElectronicContractState(PayApplyQueryReq applyQuery);

	List<WaybillRes> tradeApplyWaybillListWithElectronicContractState(WaybillReq waybillReq);

	List<RiskControllerRes> shuDaiTongPushList(RiskControllerReq req);

	List<Long> selectWaybillByReceiptMonetIsZeroList();

	int disposeReceiveFareData(List<Long> ids);

	int updateHsiangHsienWaybillByIds(List<Long> ids);

	Integer updateWaybillContractByContractId(Long contractId, String contractName, String contractCode);

	Integer updateWaybillBillStatusByIds(List<Long> ids, int billStatus);

	List<StatementWaybillInfo> selectHXPreStatementWaybillList(Map<String, Object> map);

	List<WaybillPolicyRecordRes> getCustomerWaybillPolicyRecordList(WaybillPolicyRecordReq req);

	void waybillSupplementData(List<Long> ids);

	List<Long> selectSettledWaybillId(@Param("start") String start, @Param("end") String end);

	List<Long> selectOtherWaybillId(@Param("start") String start, @Param("end") String end);

	List<Long> selectUnSettlementWaybillIds(Long customerId);

	void handleCarrier();

	void handleDriver();

	void handleVehicle();

	void handlePayeeInfo();

	void handleWaybill(List<Long> ids);

	int countWaybillOnTransporting(Waybill vehicleQuery);

	List<Waybill> selectWaybillListByIds(List<Long> ids);

	List<Long> selectWaybillIdByCustomer(Long customerId);

	List<WaybillRes> getWaybillPayInfo(WaybillReq waybillReq);

	List<WaybillRes> selectUnCheckWaybillIds(List<String> list);

	void updateVerifyTrajectoryState(List<Long> ids, Integer state);

	WaybillRes selectWaybillById(Long waybillId);

	List<Long> selectWithoutUnloadTime();

	void updateUnloadTime(@Param("id") Long id, @Param("utc") Date utc);

	List<Waybill> queryUnArriveWaybillByVehicleId(@Param("vehicleId") Long vehicleId, @Param("customerId") Long customerId);

	List<Waybill> queryUnArriveWaybillByDriverid(@Param("driverId") Long driverId, @Param("customerId") Long customerId);

	int batchUpdateDriverInfoIsCompleteByWaybillIds(List<Long> waybillIds, int driverInfoIsComplete);

	int batchUpdateVehicleInfoIsCompleteByWaybillIds(List<Long> waybillIds, int vehicleInfoIsComplete);

	List<Waybill> selectHandForwarderCarrierRelation(Long freightForwarderId,TransferReq req);

	List<Waybill> selectHandForwarderDriverRelation(Long freightForwarderId,TransferReq req);

	List<Waybill> selectHandForwarderVehicleRelation(Long freightForwarderId);

	List<FrameworkContractInfo> queryPayMoneyByTimeAndContractIds(Map<String, Object> map);

	BigDecimal queryPayMoneyByfreightForwarderId(Map<String, Object> map);

	List<Waybill> selectHandForwarderVehicleRelation(Long freightForwarderId,TransferReq req);

	WaybillTableRes freightForwarderWaybillListSum(WaybillReq waybillReq);

	List<Waybill> selectHandForwarderCapacityRelation(TransferReq req);

	BigDecimal queryBillMoneyByfreightForwarderId(Map<String, Object> map);

	BigDecimal queryWaitBillMoneyByfreightForwarderId(Map<String, Object> map);

	BigDecimal queryProfitByfreightForwarderId(Map<String, Object> map);

	List<Waybill> selectWaybillListByFreightForwarderIdAndCreateTime(@Param("freightForwarderId") Long freightForwarderId, @Param("payeeInfoIds") List<Long> payeeInfoIds);

	TaxUploadWaybillInfo findTaxUpload1WaybillInfo(Long waybillId);

	TaxUploadWaybillInfo findTaxUploadArriveWaybillInfo(Long waybillId);

	TaxUploadWaybillInfo findTaxUpload2WaybillInfo(Long waybillId);

	List<AbnormalWaybillQueryRes> getCustomerAbnormalWaybill(AbnormalWaybillQueryReq req);

	List<AbnormalWaybillQueryRes> getForwarderAbnormalWaybill(AbnormalWaybillQueryReq req);

	List<Long> selectNeedUpdateArriverTaxBill();

	void updateNeedUpdateArriverTaxBill(List<Long> ids);

	List<Long> selectNeedSecondTaxUploadBill();

	List<Long> selectNeedUpdateSecondTaxBill();

	void updateNeedUpdateSecondTaxBill(List<Long> ids);

	List<Long> selectNeedUpdateThirdTaxBill();

	void updateNeedUpdateThirdTaxBill(List<Long> ids);

	Waybill selectWaybillByWaybillCode(String waybillCode);

	Integer wxCarrierWaybillListCount(WaybillReq waybillReq);

	List<WaybillRes> requestedTradeApplyWaybillList(WaybillReq waybillReq);

	void updateFeeUnitByMakeCodeId(@Param("makeCodeId") Long id, @Param("waybillType") Integer waybillType);

	List<Waybill> selectUnloadBill();

	List<WxWaybillRes> getInsureWaybill(WaybillReq waybillReq);

	Integer selectCountToBeConfirmed(WaybillReq waybill);

	Integer selectInsureWaybillCount(WaybillReq waybillReq);

	Integer selectInTransitTrackingCount(WaybillReq waybillReq);

	Integer selectApplyForPaymentCount(WaybillReq waybillReq);

	List<StatementWaybillInfo> selectShippingNoteNumberList(List<String> shippingNoteNumbers);

	List<StatementWaybillInfo> selectWaybillReconciled(Set<String> shippingNoteNumbers, String statementNo);

	List<WxWaybillRes> getConfirmed(OperationPlainReq req);

	WaybillPolicyRecordRes getCustomerWaybillPolicyRecordListSum(WaybillPolicyRecordReq req);

	Integer selectUnDeleteWaybills(Long contractId);

	BigDecimal waybillFareSumByWaybillId(Long waybillId);

	List<WaybillFare> waybillFareSumByWaybillIds(List<Long> waybillIds);

	List<Waybill> selectNewWaybillList(UploginkWaybillQuery waybillQuery);

	ConfirmReceiptInfo confirmReceiptInfo(Long id);
	List<Waybill> seleVehicleByIdCardFromMini(List<String> drivingLicense, int type);

	List<Waybill> selectWaybillByDriverIdCardFromMini(List<String> idCardList, int type);

	/**
	 *
	 * @param vehicleId
	 * @param identityCard
	 * @param idCardType
	 *            身份证类型 1 车队长 2 司机
	 * @param statusList
	 * @return
	 */
	List<Waybill> selectWaybillByVehicleIdAndIdCardFromMini(Long vehicleId, String identityCard, Integer idCardType, List<Integer> statusList);

	List<Waybill> selectContractNameListByVehicleId(Long vehicleId, Long customerId);

	List<Waybill> selectContractNameList(Long vehicleId);

	int updateCarCaptainWaybill(List<Long> waybillIdCollect, Long captainInfoId, String captainName, String captainContactPhone, String captainIdentityCard);

	int updateAuxiliaryWaybill(List<Long> waybillIdCollect, Long auxiliaryStaffInfoId, String auxiliaryStaffName, String auxiliaryStaffIdentityCard, String auxiliaryStaffContactPhone);

	List<WxWaybillRes> selectWaybillListAll(WaybillReq query);

	Integer wxTreatConfirmCount(WaybillReq waybillReq);

	Integer wxOnlineSigningCount(WaybillReq waybillReq);

	Integer wxPendingPaymentCount(WaybillReq waybillReq);

	Integer wxLoadingCount(WaybillReq waybillReq);

	Integer wxUnloadingCount(WaybillReq waybillReq);

	List<WxWaybillRes> wxCarrierWaybillListNew(WaybillReq waybillReq);

	List<Waybill> selectGenerateOwnerDeclaration();

	List<Long> selectWaybillByActualCarrierIdentityCard(String idCard);

	List<String> selectWaybillByAnonymously(List<String> idCardList);

	List<Waybill> selectSD1111Waybill();

	List<Waybill> selectLoadTimeAllList();

	List<Waybill> selectExpiredWaybillByOperationPlain();

	int logicDeleteWxWaybills(List<Long> waybillIds, String updateBy);

	List<Waybill> selectGenerateOwnerDeclarationByWaybillIds(List<Long> waybillIds);

	void updateRiskResultGradeByIds(List<Long> ids);

	List<Long> getCompletedNotInvoicedWaybillIds(List<Long> ids);

    List<Long> selectWaybillIdByFreightForwarderIds(List<Long> freightForwarderIds);

	/**
	 * 查询司机运输最早的运单的创建时间
	 * @param forwarderId
	 * @param driverId
	 * @return
	 */
	Waybill selectWayBillByDriverIdForMinTime(Long forwarderId, Long driverId);

	/**
	 * 查询车辆运输最早的运单的创建时间
	 * @param forwarderId
	 * @param vehicleId
	 * @return
	 */
	Waybill selectWayBillByVehicleForMinTime(Long forwarderId, Long vehicleId);

	/**
	 * 查询到了预计发车时间但是还未发车的运单
	 *
	 * @return
	 */
	List<Waybill> getConfirmDepartureWaybillList();

	/**
	 * 根据运单id更改运单运输状态，改为3
	 *
	 * @param waybillReq
	 * @return
	 */
	int updateWaybillStatusForConfirmDeparture(WaybillReq waybillReq);

	/**
	 * 查询善道和4.0状态不一样的运单（-1，4）
	 * @return
	 */
	List<Waybill> selectWaybillListForInconsistentStatus(String shandaoDBName, String fourDBName);

	List<Long> selectNotCheckWaybillIdByForwarderIds(Long forwarderId);

	List<Long> selectWaybillStatusByWaybillIds(@Param("waybillIds") List<Long> waybillIds);

	StatementMoneyRes tenantStatementMoneyTotal(Map<String, Object> map);

	StatementMoneyRes FinanceStatementMoneyTotal(Map<String, Object> map);

	WaybillCount selectWaybillByToday(Long freightForwarderId, String today);

	WaybillCount selectSettlementWaybillByToday(Long freightForwarderId, String today);

	WaybillCount selectWaybillByMonth(Long freightForwarderId, String month);

	WaybillCount selectSettlementWaybillByMonth(Long freightForwarderId, String month);

	RealTimeWaybill realTimeWaybillCount(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("freightForwarderId") Long id);

	List<PayeeTypeStatistics> payeeTypeStatistics(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("freightForwarderId") Long id);

	List<WaybillSource> waybillSourceCount(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("freightForwarderId") Long id);

	DataReportingCount dataReportingCount(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("freightForwarderId") Long id);

	List<PayeeStatisticsInfo> payeeStatisticsList(Map<String, Object> map);

	List<HaulwayStatisticsInfo> haulwayStatisticsList(Map<String, Object> map);

	List<ContractStatisticsInfo> contractStatisticsListByContractId(Map<String, Object> map);

	List<BillStatisticsInfo> billStatisticsListByCustomerIds(Map<String, Object> map);

	List<BillStatisticsInfo> billStatisticsListByContractIds(Map<String, Object> map);

	Integer waybillListForTenantStatementCount(Map<String, Object> map);

	Integer waybillListFinanceStatementCount(Map<String, Object> map);

	List<ContractStatisticsInfo> contractStatisticsListByCustomerId(Map<String, Object> map);

	List<PayeeStatisticsCountInfo> payeeStatisticsCount(Map<String, Object> map);

	List<String> payeeStatisticsYearCount(Long freightForwarderId);

	List<WaybillProxyInvoiceRes> proxyInvoiceWaybillList(WaybillReq waybillReq);

	WaybillProxyInvoiceRes proxyInvoiceWaybillListSum(WaybillReq waybillReq);

	int batchModifyArtificialAuditStateByWaybillIds(@Param("waybillIds") List<Long> waybillIds, @Param("artificialAuditState") Integer artificialAuditState);

	List<WaybillAuditListRes> freightForwarderWaybillAuditList(WaybillReq waybillReq);

	Integer freightForwarderWaybillAuditCount(Long freightForwarderId);

	int countWaybill(Waybill waybill);

	int logicDeleteWaybillsByMakeCodeId(Long makecodeId, Long customerId, List<Integer> statusList);

	List<WaybillTableRes> selectShipperOrderList(OrderQuery orderQuery);

	int selectShipperOrderListCount(OrderQuery orderQuery);

	List<WaybillReq> selectDriverFamilyLastYearUnpaidFare();

	List<WaybillReq> selectDriverFamilyLastYearTradeApply();


	/**
	 * 获取司机长跑路线信息
	 *
	 * @param params 查询参数，包含：
	 *               - driverIds: 司机ID集合
	 *               - startTime: 开始时间（可选）
	 *               - endTime: 结束时间（可选）
	 * @return 司机路线统计信息列表
	 */
	List<DriverRouteStatisticsDTO> getDriverRouteStatistics(Map<String, Object> params);

	/**
     * 批量查询司机运单相关的车辆信息
     * @param params 包含driverIds和startTime的参数Map
     * @return 车辆信息列表
     */
    List<VehicleInfoDTO> getDriverWaybillVehicles(Map<String, Object> params);


	/**
	 * 查询最近一段时间内满足特定条件的运单关联的去重司机ID列表
	 * @param sinceDate 指定的起始日期 (例如，一周前)
	 * @return 满足条件的去重司机ID列表
	 */
	List<Long> selectRecentDistinctDriverIds(@Param("sinceDate") Date sinceDate);

	/**
	 * 分页查询近期运单中的司机ID（去重）
	 *
	 * @param date 查询日期范围
	 * @param offset 偏移量
	 * @param limit 每页大小
	 * @return 司机ID列表
	 */
	List<Long> selectRecentDistinctDriverIdsByPage(@Param("date") Date date, @Param("offset") int offset, @Param("limit") int limit);
	Integer queryWaybillCountByCustomerIdAndTime(String dateStartString, String dateEndString, Long customerId);

	BigDecimal queryWaybillFareByCustomerIdAndTime(String dateStartString, String dateEndString, Long customerId);

	Long selectSignorWaybillListCount(Map<String, Object> map);

	boolean waybillIsLocate(List<Long> ids);

	Integer getWaybillStatus(Long id);

	List<Waybill> selectEtcWaybill();

	List<WaybillProxyInvoiceRes> waitMatchmakingProxyInvoiceWaybillList(WaybillReq waybillReq);

	List<WaybillProxyInvoiceRes> matchmakingProxyInvoiceWaybillList(WaybillReq waybillReq);

	long selectIdByShippingNoteNumber(String waybillNo);

	int selectVehicleCount(Long driverId);

	void updateProxyStateByShipperNoteNumbers(List<String> shippingNoteNumbers);

	BigDecimal getNoTaxFare(WaybillReq waybillReq);

	int updateETCInvoiceStatusByEtcStatus(Long forwarderId);

}
