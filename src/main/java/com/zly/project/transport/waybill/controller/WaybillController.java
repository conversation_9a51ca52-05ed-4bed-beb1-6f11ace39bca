package com.zly.project.transport.waybill.controller;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.zly.project.riskManagement.service.WaybillRiskService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSON;
import com.zly.common.constant.*;
import com.zly.common.enums.ClientType;
import com.zly.common.enums.ExportEnum;
import com.zly.common.utils.*;
import com.zly.common.utils.bean.BeanUtils;
import com.zly.common.utils.poi.ExcelUtil;
import com.zly.framework.aspectj.lang.annotation.Log;
import com.zly.framework.aspectj.lang.annotation.RateLimiter;
import com.zly.framework.aspectj.lang.enums.BusinessType;
import com.zly.framework.aspectj.lang.enums.LimitType;
import com.zly.framework.interceptor.annotation.RepeatSubmit;
import com.zly.framework.redis.RedisCache;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.framework.web.page.TableInfo;
import com.zly.project.common.domain.AmapLocationModel;
import com.zly.project.common.service.ExportService;
import com.zly.project.common.service.impl.AmapServiceImpl;
import com.zly.project.financeOperation.domain.FinanceUser;
import com.zly.project.freightforwarder.domain.FreightForwarderInfo;
import com.zly.project.freightforwarder.service.IFreightForwarderInfoService;
import com.zly.project.group.service.IWaybillCustomerService;
import com.zly.project.settlement.domain.CustomerTradeApply;
import com.zly.project.settlement.domain.PayApply;
import com.zly.project.settlement.domain.request.PayApplyQueryReq;
import com.zly.project.settlement.service.ICustomerTradeApplyService;
import com.zly.project.system.domain.SysDictData;
import com.zly.project.system.service.ISysDictTypeService;
import com.zly.project.tenant.domain.TenantUser;
import com.zly.project.tenant.service.ITenantUserService;
import com.zly.project.transport.waybill.domain.ConfirmReceiptInfo;
import com.zly.project.transport.waybill.domain.Waybill;
import com.zly.project.transport.waybill.domain.WaybillExportFile;
import com.zly.project.transport.waybill.domain.WaybillPolicyInfo;
import com.zly.project.transport.waybill.domain.req.*;
import com.zly.project.transport.waybill.domain.res.*;
import com.zly.project.transport.waybill.mapper.WaybillExportFileMapper;
import com.zly.project.transport.waybill.service.IWaybillService;
import com.zly.project.transport.waybill.service.WaybillElectronicContractService;
import com.zly.project.transport.waybill.service.WaybillPolicyService;
import com.zly.project.transport.waybill.service.WaybillTradeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 运单管理
 *
 * <AUTHOR>
 * @date 2021-10-29
 */
@Api(tags = "运单管理")
@RestController
@RequestMapping("/transport/waybill")
public class WaybillController extends BaseController {

	@Resource
	private IWaybillService waybillService;
	@Resource
	private ISysDictTypeService dictTypeService;
	@Resource
	private ICustomerTradeApplyService customerTradeApplyService;
	@Resource
	private IFreightForwarderInfoService freightForwarderInfoService;
	@Resource
	private ExportService exportService;
	@Resource
	private IWaybillCustomerService waybillCustomerService;
	@Resource
	private AmapServiceImpl amapService;
	@Resource
	private RedisCache redisCache;
	@Resource
	private ITenantUserService tenantUserService;

	@Resource
	private HuaXiaUtils huaXiaUtils;
	@Resource
	private WaybillTradeService waybillTradeService;
	@Resource
	private WaybillElectronicContractService waybillElectronicContractService;
	@Resource
	private WaybillPolicyService waybillPolicyService;
	@Resource
	private WaybillExportFileMapper waybillExportFileMapper;
	@Resource
	private WaybillRiskService waybillRiskService;

	/**
	 * 获取运输协议单号
	 *
	 * @return
	 */
	@ApiOperation(value = "获取运输协议单号", notes = "获取运输协议单号")
	@GetMapping(value = "/originalDocumentNumber")
	public AjaxResult generateOriginalDocumentNumber() {
		return AjaxResult.success("操作成功", CodeUtil.generateOriginalDocumentNumber());
	}

	/**
	 * 获取订单号
	 */
	@ApiOperation(value = "获取订单号", notes = "获取订单号")
	@PreAuthorize("@ss.hasPermi('transport:waybill:add')")
	@GetMapping(value = "/generateOrderNo")
	public AjaxResult getOrderNo() {
		return AjaxResult.success("操作成功", CodeUtil.generateOrderCode());
	}

	/**
	 * 获取数据字典业务类型
	 */
	@ApiOperation(value = "获取数据字典业务类型", notes = "获取数据字典业务类型")
	@PreAuthorize("@ss.hasPermi('transport:waybill:add')")
	@GetMapping(value = "/getWaybillBusinessType")
	public AjaxResult getWaybillBusinessType() {
		List<SysDictData> data = dictTypeService.selectDictDataByType(DictType.BUSINESS_TYPE);
		if (StringUtils.isNull(data)) {
			data = new ArrayList<SysDictData>();
		}
		return AjaxResult.success("操作成功", data);
	}

	/**
	 * 获取数据字典货物类别
	 */
	@ApiOperation(value = "获取数据字典货物类别", notes = "获取数据字典货物类别")
	// @PreAuthorize("@ss.hasPermi('transport:waybill:add')")
	@GetMapping(value = "/getWaybillCargoType")
	public AjaxResult getWaybillCargoType() {
		List<SysDictData> data = dictTypeService.selectDictDataByType(DictType.CARGO_TYPE);
		if (StringUtils.isNull(data)) {
			data = new ArrayList<SysDictData>();
		}
		return AjaxResult.success("操作成功", data);
	}

	/**
	 * 获取数据字典包装类型
	 */
	@ApiOperation(value = "获取数据字典包装类型", notes = "获取数据字典包装类型")
	// @PreAuthorize("@ss.hasPermi('transport:waybill:add')")
	@GetMapping(value = "/getWaybillPackingType")
	public AjaxResult getWaybillPackingType() {
		List<SysDictData> data = dictTypeService.selectDictDataByType(DictType.PACKING_TYPE);
		if (StringUtils.isNull(data)) {
			data = new ArrayList<SysDictData>();
		}
		return AjaxResult.success("操作成功", data);
	}

	/**
	 * 待申请开票的运单数量
	 */
	@ApiOperation(value = "开票管理的待申请条数", notes = "开票管理的待申请条数")
	@PreAuthorize("@ss.hasPermi('financial:finance:list')")
	@GetMapping("/countWaybillForStatement")
	public CommonResult<Long> countWaybillForStatement() {
		return CommonResult.success(0L, "查询成功");
	}

	/**
	 * 待审核的开票申请单数量（我是申请的人且我是录单人）
	 */
	@ApiOperation(value = "开票管理的待审核的开票申请单数", notes = "开票管理的待审核的开票申请单数")
	@PreAuthorize("@ss.hasPermi('financial:finance:list')")
	@GetMapping("/countStatementForAudit")
	public CommonResult<Long> countStatementForAudit() {
		return CommonResult.success(0L, "查询成功");
	}

	/**
	 * 根据货主货源、调度计划、查询运单列表，只返回承运人、司机车辆、运单状态、费用等信息
	 */
	@ApiOperation(value = "开票管理的运单列表", notes = "开票管理的运单列表")
	@PreAuthorize("@ss.hasPermi('financial:finance:list')")
	@PostMapping("/list")
	public TableDataInfo list(@RequestBody WaybillReq waybillReq) {
		PageUtils.startPage();
		List<WaybillRes> list = waybillService.waybillList(waybillReq);
		return getDataTable(list);
	}

	/**
	 * 网络货运人端-运单查询-列表
	 *
	 * @param waybillReq
	 * @return
	 */
	@ApiOperation(value = "网络货运人端运单查询列表---4.5.6X", notes = "网络货运人端运单查询列表---4.5.6X")
	@PostMapping("/list/query")
	public TableInfo<WaybillTableResOfFreightForwarder> freightForwarderWaybillList(@RequestBody WaybillReq waybillReq, HttpServletRequest request) {
		if (!ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
			return TableInfo.forbidden("网络货运人端功能，您没有权限使用该接口");
		}
		BeanUtils.beanAttributeValueTrim(waybillReq);// 去掉该对象中string字段前后空格
		if (StringUtils.isNotNull(waybillReq.getStatus()) && waybillReq.getStatus() == BusinessConstants.WAYBILL_STATUS_DELETE) {
			return TableInfo.requestError("参数错误");
		}
		Date start = DateUtils.strToDate(waybillReq.getBeginCreateTime());
		Date end = DateUtils.strToDate(waybillReq.getEndCreateTime());
		if (null == start || null == end || DateUtils.addYears(start, 1).before(end)) {
			return TableInfo.requestError("请指定运单创建时间，跨度不能超过1年");
		}
		Long freightForwarderId = SecurityUtils.getFreightForwarderId();
		if (null == freightForwarderId) {
			return TableInfo.error("登录用户错误");
		}
		// 获取登录的网络货运人信息
		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(freightForwarderId);
		if (freightForwarderInfo == null || freightForwarderInfo.getCustomerId() == null || freightForwarderInfo.getCustomerId() == 0) {
			return TableInfo.error("登录用户错误");
		}
		waybillReq.setFreightForwarderId(freightForwarderId);
		addContractIds(waybillReq);
		TableInfo<WaybillTableResOfFreightForwarder> list = waybillService.selectFreightForwarderWaybillList(waybillReq, request);
		return list;
	}

	/**
	 * 网络货运人端-运单查询-应付总运费、实付总运费、应收上游的金额统计接口
	 *
	 * @param waybillReq
	 * @return
	 */
	@ApiOperation(value = "网络货运人端运单金额统计接口 --4.4.3 --yxq", notes = "网络货运人端运单金额统计接口 --4.4.3 --yxq")
	@PostMapping("/list/querySum")
	public AjaxResult freightForwarderWaybillListSum(@RequestBody WaybillReq waybillReq, HttpServletRequest request) {
		if (!ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
			return AjaxResult.error("网络货运人端功能，您没有权限使用该接口");
		}
		BeanUtils.beanAttributeValueTrim(waybillReq);// 去掉该对象中string字段前后空格
		if (StringUtils.isNotNull(waybillReq.getStatus()) && waybillReq.getStatus() == BusinessConstants.WAYBILL_STATUS_DELETE) {
			return AjaxResult.error("参数错误");
		}
		Date start = DateUtils.strToDate(waybillReq.getBeginCreateTime());
		Date end = DateUtils.strToDate(waybillReq.getEndCreateTime());
		if (null == start || null == end || DateUtils.addYears(start, 1).before(end)) {
			return AjaxResult.error("请指定运单创建时间，跨度不能超过1年");
		}
		Long freightForwarderId = SecurityUtils.getFreightForwarderId();
		if (null == freightForwarderId) {
			return AjaxResult.error("登录用户错误");
		}
		// 获取登录的网络货运人信息
		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(freightForwarderId);
		if (freightForwarderInfo == null || freightForwarderInfo.getCustomerId() == null || freightForwarderInfo.getCustomerId() == 0) {
			return AjaxResult.error("登录用户错误");
		}
		waybillReq.setFreightForwarderId(freightForwarderId);
		waybillReq.setPageNum(null);
		waybillReq.setPageSize(null);
		addContractIds(waybillReq);
		return waybillService.selectFreightForwarderWaybillSum(waybillReq);
	}

	/**
	 * 网络货运人端-运单查询-应付总运费、实付总运费、应收上游的金额统计接口
	 *
	 * @param waybillReq
	 * @return
	 */
	@ApiOperation(value = "网络货运人端运单金额统计接口 --4.4.3 --yxq", notes = "网络货运人端运单金额统计接口 --4.4.3 --yxq")
	@PostMapping("/proxyInvoiceWaybillListSum")
	public CommonResult<?> proxyInvoiceWaybillListSum(@RequestBody WaybillReq waybillReq) {
		if (!ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
			return CommonResult.error("网络货运人端功能，您没有权限使用该接口");
		}
		BeanUtils.beanAttributeValueTrim(waybillReq);// 去掉该对象中string字段前后空格
		if (StringUtils.isNotNull(waybillReq.getStatus()) && waybillReq.getStatus() == BusinessConstants.WAYBILL_STATUS_DELETE) {
			return CommonResult.error("参数错误");
		}
		Date start = DateUtils.strToDate(waybillReq.getBeginCreateTime());
		Date end = DateUtils.strToDate(waybillReq.getEndCreateTime());
		if (null == start || null == end || DateUtils.addYears(start, 1).before(end)) {
			return CommonResult.error("请指定运单创建时间，跨度不能超过1年");
		}
		Long freightForwarderId = SecurityUtils.getFreightForwarderId();
		if (null == freightForwarderId) {
			return CommonResult.error("登录用户错误");
		}
		// 获取登录的网络货运人信息
		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(freightForwarderId);
		if (freightForwarderInfo == null || freightForwarderInfo.getCustomerId() == null || freightForwarderInfo.getCustomerId() == 0) {
			return CommonResult.error("登录用户错误");
		}
		waybillReq.setFreightForwarderId(freightForwarderId);
		waybillReq.setPageNum(null);
		waybillReq.setPageSize(null);
		return waybillService.selectProxyInvoiceWaybillListSum(waybillReq);
	}

	/**
	 * 网络货运人端-税务代开运单
	 *
	 * @param waybillReq
	 * @return
	 */
	@ApiOperation(value = "网络货运人端-税务代开运单---4.5.6X", notes = "网络货运人端-税务代开运单---4.5.6X")
	@PostMapping("/proxyInvoiceWaybillList")
	public TableInfo<WaybillProxyInvoiceRes> proxyInvoiceWaybillList(@RequestBody WaybillReq waybillReq) {
		if (!ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
			return TableInfo.forbidden("网络货运人端功能，您没有权限使用该接口");
		}
		BeanUtils.beanAttributeValueTrim(waybillReq);// 去掉该对象中string字段前后空格
		if (StringUtils.isNotNull(waybillReq.getStatus()) && waybillReq.getStatus() == BusinessConstants.WAYBILL_STATUS_DELETE) {
			return TableInfo.requestError("参数错误");
		}
		Date start = DateUtils.strToDate(waybillReq.getBeginCreateTime());
		Date end = DateUtils.strToDate(waybillReq.getEndCreateTime());
		if (null == start || null == end || DateUtils.addYears(start, 1).before(end)) {
			return TableInfo.requestError("请指定运单创建时间，跨度不能超过1年");
		}
		Long freightForwarderId = SecurityUtils.getFreightForwarderId();
		if (null == freightForwarderId) {
			return TableInfo.error("登录用户错误");
		}
		// 获取登录的网络货运人信息
		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(freightForwarderId);
		if (freightForwarderInfo == null || freightForwarderInfo.getCustomerId() == null || freightForwarderInfo.getCustomerId() == 0) {
			return TableInfo.error("登录用户错误");
		}
		waybillReq.setFreightForwarderId(freightForwarderId);

		startPage();
		List<WaybillProxyInvoiceRes> resList = waybillService.proxyInvoiceWaybillList(waybillReq);
		return getTableData(resList);
	}

	/**
	 * 网络货运人端-申请、待审请、撤销税务代开运单
	 *
	 * @param req
	 * @return
	 */
	@ApiOperation(value = "网络货运人端-申请、待审请、撤销税务代开运单---4.5.6X", notes = "网络货运人端-申请、待审请、撤销税务代开运单---4.5.6X")
	@PostMapping("/settingProxyInvoice")
	public CommonResult settingProxyInvoice(@RequestBody WaybillProxyInvoiceReq req) {
		if (!ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
			return CommonResult.error("网络货运人端功能，您没有权限使用该接口");
		}

		Long freightForwarderId = SecurityUtils.getFreightForwarderId();
		if (null == freightForwarderId) {
			return CommonResult.error("登录用户错误");
		}
		// 获取登录的网络货运人信息
		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(freightForwarderId);
		if (freightForwarderInfo == null || freightForwarderInfo.getCustomerId() == null || freightForwarderInfo.getCustomerId() == 0) {
			return CommonResult.error("登录用户错误");
		}

		return waybillService.settingProxyInvoice(req);
	}

	/**
	 * 网络货运人端运单导出
	 *
	 * @param waybillReq
	 */
	@ApiOperation(value = "网络货运人端运单查询列表导出", notes = "网络货运人端运单查询列表导出")
	@PostMapping("/freightForwarder/waybillExport")
	@RepeatSubmit(interval = 120000, message = "运单导出中,请勿重复操作")
	public CommonResult<?> freightForwarderWaybillExport(@RequestBody WaybillReq waybillReq) {
		Date start = DateUtils.strToDate(waybillReq.getBeginCreateTime());
		Date end = DateUtils.strToDate(waybillReq.getEndCreateTime());
		if (null == start || null == end || DateUtils.addYears(start, 1).before(end)) {
			return CommonResult.requestError("请指定运单创建时间，跨度不能超过1年");
		}
		waybillReq.setFreightForwarderId(SecurityUtils.getFreightForwarderId());
		if (StringUtils.isNotBlank(waybillReq.getBeginOrderCreateTime())) {
			waybillReq.setBeginOrderCreateTime(waybillReq.getBeginOrderCreateTime() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(waybillReq.getEndOrderCreateTime())) {
			waybillReq.setEndOrderCreateTime(waybillReq.getEndOrderCreateTime() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillReq.getBeginCreateTime())) {
			waybillReq.setBeginCreateTime(waybillReq.getBeginCreateTime() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(waybillReq.getEndCreateTime())) {
			waybillReq.setEndCreateTime(waybillReq.getEndCreateTime() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillReq.getBeginPayTime())) {
			waybillReq.setBeginPayTime(waybillReq.getBeginPayTime() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(waybillReq.getEndPayTime())) {
			waybillReq.setEndPayTime(waybillReq.getEndPayTime() + " 23:59:59");
		}
		return exportService.commonExport(ExportEnum.PLATFORM_WAYBILL.getCode(), JSON.toJSONString(waybillReq), ClientType.PLATFORM.val());
	}

	/**
	 * 网络货运人端税务代开运单
	 *
	 * @param waybillReq
	 */
	@ApiOperation(value = "网络货运人端税务代开运单导出---4.5.6X", notes = "网络货运人端税务代开运单导出---4.5.6X")
	@PostMapping("/freightForwarder/proxyInvoiceWaybillExport")
	public CommonResult<?> proxyInvoiceWaybillExport(@RequestBody WaybillReq waybillReq) {
		Date start = DateUtils.strToDate(waybillReq.getBeginCreateTime());
		Date end = DateUtils.strToDate(waybillReq.getEndCreateTime());
		if (null == start || null == end || DateUtils.addYears(start, 1).before(end)) {
			return CommonResult.requestError("请指定运单创建时间，跨度不能超过1年");
		}
		waybillReq.setFreightForwarderId(SecurityUtils.getFreightForwarderId());
		return exportService.commonExport(ExportEnum.PLATFORM_PROXY_INVOICE_WAYBILL.getCode(), JSON.toJSONString(waybillReq), ClientType.PLATFORM.val());
	}

	/**
	 * 查询运单列表
	 */
	@ApiOperation(value = "查询运单列表", notes = "查询运单列表")
	@PreAuthorize("@ss.hasPermi('transport:waybill:list')")
	@GetMapping("/count")
	public AjaxResult count() {
		// 查询待处理总数
		Integer pendingCount = waybillService.getStatusCount(BusinessConstants.WAYBILL_STATUS_WAYBILL);
		// 查询已签约总数
		Integer signedCount = waybillService.getStatusCount(BusinessConstants.WAYBILL_STATUS_LEASE);
		// 查询进行中总数
		Integer underwayCount = waybillService.getStatusCount(BusinessConstants.WAYBILL_STATUS_IN_TRANSIT);
		// 查询已完成总数
		Integer finishedCount = waybillService.getStatusCount(BusinessConstants.WAYBILL_STATUS_COMPLETED);
		HashMap<String, Integer> map = new HashMap<String, Integer>();
		map.put("pendingCount", pendingCount);
		map.put("signedCount", signedCount);
		map.put("underwayCount", underwayCount);
		map.put("finishedCount", finishedCount);
		return AjaxResult.success("查询成功", map);
	}

	/**
	 * 导出运单列表
	 */
	@ApiOperation(value = "导出运单列表", notes = "导出运单列表")
	@PreAuthorize("@ss.hasPermi('transport:waybill:export')")
	@Log(title = "运单", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(WaybillReq waybillReq, HttpServletResponse response) {
		List<WaybillRes> list = waybillService.waybillList(waybillReq);
		List<WaybillOrderRes> orderResList = new ArrayList<WaybillOrderRes>();
		if (null != waybillReq.getStatus() && waybillReq.getStatus() == BusinessConstants.WAYBILL_STATUS_ORDER) {
			if (null != list && !list.isEmpty()) {
				for (WaybillRes waybillRes : list) {
					WaybillOrderRes waybillOrderRes = new WaybillOrderRes();
					BeanUtils.copyProperties(waybillRes, waybillOrderRes);
					orderResList.add(waybillOrderRes);
				}
			}
			ExcelUtil<WaybillOrderRes> util = new ExcelUtil<WaybillOrderRes>(WaybillOrderRes.class);
			util.exportExcel(response, orderResList, "订单数据");
		} else {
			ExcelUtil<WaybillRes> util = new ExcelUtil<WaybillRes>(WaybillRes.class);
			util.exportExcel(response, list, "运单数据");
		}
	}

	/**
	 * 获取运单详细信息
	 */
	@ApiOperation(value = "获取运单详细信息--v4.5.0.2--shw", notes = "获取运单详细信息，增加货源是否可以修改字段makeCodeUpdate")
	@PreAuthorize("@ss.hasPermi('transport:waybill:query')")
	@GetMapping(value = "/{id}")
	public CommonResult<WaybillRes> waybillDetail(@PathVariable("id") Long id) {
		return waybillService.getWaybillById(id);
	}

	@ApiOperation(value = "获取运运费结算细信息--v4.6.2--jb", notes = "运单详情界面运费结算模块")
	@PreAuthorize("@ss.hasPermi('transport:waybill:query')")
	@GetMapping(value = "/pay/{id}")
	public CommonResult<WaybillRes> waybillPay(@PathVariable("id") Long id) {
		return waybillService.getWaybillPayById(id);
	}

	@ApiOperation(value = "获取运单开票信息--v4.6.2--jb", notes = "运单详情界面开票信息模块")
	@PreAuthorize("@ss.hasPermi('transport:waybill:query')")
	@GetMapping(value = "/invoice/{id}")
	public CommonResult<WaybillRes> waybillInvoice(@PathVariable("id") Long id) {
		return waybillService.getWaybillInvoiceById(id);
	}

	@ApiOperation(value = "获取运单影像信息--v4.6.2--jb", notes = "运单详情界面影像素材模块")
	@PreAuthorize("@ss.hasPermi('transport:waybill:query')")
	@GetMapping(value = "/image/{id}")
	public CommonResult<WaybillRes> waybillImage(@PathVariable("id") Long id) {
		return waybillService.getWaybillImageById(id);
	}

	@ApiOperation(value = "获取运单签约信息--v4.6.2--jb", notes = "运单详情界面签约协议模块")
	@PreAuthorize("@ss.hasPermi('transport:waybill:query')")
	@GetMapping(value = "/agreement/{id}")
	public CommonResult<WaybillRes> waybillAgreement(@PathVariable("id") Long id) {
		return waybillService.getWaybillAgreementById(id);
	}

	/**
	 * 获取运单相关状态(运单编辑控制使用)
	 */
	@ApiOperation(value = "获取运单相关状态--v4.3--wzy", notes = "获取运单相关状态，用以控制运单的编辑")
	@PreAuthorize("@ss.hasPermi('transport:waybill:query')")
	@GetMapping(value = "/getWaybillStatus")
	public CommonResult<WaybillEditableInfo> getWaybillStatus(Long id) {
		return waybillService.getWaybillStatusInfo(id);
	}

	/**
	 * 托运人新增运单
	 *
	 * @param req
	 * @return
	 */
	@ApiOperation(value = "托运人新增运单--v4.5.0.2--shw", notes = "托运人新增运单")
	@Log(title = "运单", businessType = BusinessType.INSERT)
	@PostMapping(value = "/shipper/add")
	public CommonResult shipperAddWaybill(@RequestBody @Validated WaybillUpdateRequest req) {
		logger.info("托运人新增运单，请求参数：{}", JSON.toJSONString(req));
		if (StringUtils.isNotEmpty(req.getUuid())) {
			String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(req.getUuid(), "");
			String captcha = redisCache.getCacheObject(verifyKey);
			if (captcha == null) {
				return CommonResult.error("验证码已失效");
			}
			if (!req.getPicCode().equalsIgnoreCase(captcha)) {
				return CommonResult.error("验证码错误");
			}
		}

		String redisKey = Constants.PC_CACHE + "waybill_shipper_" + SecurityUtils.getUserId();
		if (redisCache.hasKey(redisKey)) {
			return CommonResult.error("新建运单请求已提交，目前处理中，请勿重复提交");
		}
		// 信息到redis中，保留10s,避免重复提交
		redisCache.setCacheObject(redisKey, 1, 10, TimeUnit.SECONDS);

		if (!ClientType.SHIPPER.equals(SecurityUtils.getClientType())) {
			return CommonResult.error("托运人新增运单功能，您无权访问");
		}
		CommonResult commonResult = waybillService.shipperAddWaybill(req);
		// 解锁
		redisCache.deleteObject(redisKey);
		return commonResult;
	}

	/**
	 * 托运人编辑运单
	 *
	 * @deprecated 该接口已废弃 <br/>
	 *             替换接口为 [POST]/customer/waybill/editWaybillInfo/{type} <br/>
	 *             具体请查看方法：{@link CustomerWaybillController#editWaybillInfo(Integer, WaybillUpdateRequest)}
	 *
	 * @param req
	 * @return
	 */
	@ApiOperation("托运人编辑运单--已废弃")
	@Log(title = "运单", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/shipper/update")
	public AjaxResult shipperUpdateWaybill(@RequestBody WaybillUpdateRequest req) {
		if (!ClientType.SHIPPER.equals(SecurityUtils.getClientType())) {
			return AjaxResult.error(HttpStatus.FORBIDDEN, "托运人编辑运单功能，您无权访问");
		}
		return AjaxResult.success();
		// return waybillService.shipperUpdateWaybill(req);
	}

	/**
	 * 修改运单
	 */
	@ApiOperation(value = "修改运单", notes = "修改运单")
	@PreAuthorize("@ss.hasPermi('transport:waybill:edit')")
	@Log(title = "运单", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/edit")
	public CommonResult edit(@RequestBody WaybillReq req) {
		return null;
	}

	/**
	 * 删除运单
	 */
	@ApiOperation(value = "删除运单", notes = "删除运单")
	@PreAuthorize("@ss.hasPermi('transport:waybill:remove')")
	@Log(title = "运单", businessType = BusinessType.DELETE)
	@PostMapping("/remove")
	public AjaxResult remove(@RequestParam Long[] ids) {
		logger.info("托运人删除运单，请求参数：{}", JSON.toJSONString(ids));
		return waybillService.removeWaybillByIds(ids);
	}

	/**
	 * 提交运单
	 */
	@ApiOperation(value = "提交运单", notes = "提交运单")
	@PreAuthorize("@ss.hasPermi('transport:waybill:add')")
	@Log(title = "运单", businessType = BusinessType.SUBMIT)
	@PostMapping("/submit")
	public AjaxResult submit(@RequestParam Long[] ids) {
		return waybillService.submitWaybillByIds(ids);
	}

	/**
	 * 获取运单物流详情
	 */
	@ApiOperation(value = "获取运单物流详情", notes = "获取运单物流详情")
	@PreAuthorize("@ss.hasPermi('transport:waybill:query')")
	@GetMapping(value = "/logistics/{id}")
	public AjaxResult getWaybillLogisticsDetails(@PathVariable("id") Long id, HttpServletRequest request) {
		if (request.getRequestURL().toString().contains(BusinessConstants.SHAN_DAO_API_URL)) {
			Waybill waybill = waybillService.selectWaybillById(id);
			if (waybill.getCreateTime().compareTo(DateUtils.strToDate("2024-11-11 00:00:00")) > 0) {
				return waybillService.getWaybillLogisticsDetailsSD(id, 0);
			}
		}
		return waybillService.getWaybillLogisticsDetails(id, 0);
	}

	/**
	 * 托运人端我录的运单
	 */
	@ApiOperation(value = "我录的运单--v4.5.0.2--shw", notes = "我录的运单，指托运人自己录的运单,增加列表返回")
	@PreAuthorize("@ss.hasPermi('transport:waybill:list')")
	@PostMapping("/customer/waybillList")
	public TableInfo<WaybillTableRes> shipperWaybillList(@RequestBody WaybillQuery waybillQuery, HttpServletRequest request) {
		logger.info("托运人运单列表，请求参数：" + JSON.toJSONString(waybillQuery));
		if (waybillQuery == null) {
			return TableInfo.requestError("参数错误");
		}
		if (!ClientType.SHIPPER.equals(SecurityUtils.getClientType())) {
			return TableInfo.forbidden("托运人端功能，您没有权限使用该接口");
		}
		waybillQuery.setCustomerId(SecurityUtils.getShipperId());
		waybillQuery.setPageSize(Integer.parseInt(request.getParameterMap().get("pageSize")[0]));
		waybillQuery.setOffSet((Integer.parseInt(request.getParameterMap().get("pageNum")[0]) - 1) * waybillQuery.getPageSize());
		waybillQuery.setPageNum(null); // 防止分页插件自动分页

		// 校验托运人项目分组权限
		this.addContractIds(waybillQuery);

		return waybillService.selectShipperWaybillPageList(waybillQuery);

	}

	/**
	 * 托运人端我录的运单合计
	 */
	@ApiOperation(value = "我录的运单合计--v4.5.0.2--shw", notes = "我录的运单合计--4.3.15---shw")
	@PostMapping("/customer/waybillListSum")
	public AjaxResult shipperWaybillListSum(@RequestBody WaybillQuery waybillQuery, HttpServletRequest request) {
		logger.info("托运人运单列表，请求参数：" + JSON.toJSONString(waybillQuery));
		if (waybillQuery == null) {
			return AjaxResult.error("参数错误");
		}
		if (!ClientType.SHIPPER.equals(SecurityUtils.getClientType())) {
			return AjaxResult.error("托运人端功能，您没有权限使用该接口");
		}
		waybillQuery.setCustomerId(SecurityUtils.getShipperId());

		// 校验托运人项目分组权限
		this.addContractIds(waybillQuery);

		return waybillService.selectShipperWaybillSum(waybillQuery);

	}

	/**
	 * 托运人端我录的运单合计
	 */
	@ApiOperation(value = "运单数量合计--4.4.16---shw", notes = "运单数量合计--4.4.16---shw")
	@PostMapping("/customer/waybillListCount")
	public AjaxResult shipperWaybillListCount() {
		WaybillQuery waybillQuery = new WaybillQuery();
		if (!ClientType.SHIPPER.equals(SecurityUtils.getClientType())) {
			return AjaxResult.error("托运人端功能，您没有权限使用该接口");
		}
		waybillQuery.setCustomerId(SecurityUtils.getShipperId());

		// 校验托运人项目分组权限
		this.addContractIds(waybillQuery);

		return waybillService.shipperWaybillListCount(waybillQuery);

	}

	/**
	 * 托运人运单导出
	 *
	 * @param waybillQuery
	 */
	@ApiOperation(value = "自营运单导出", notes = "自营运单导出")
	@PreAuthorize("@ss.hasPermi('transport:waybill:list:export')")
	@PostMapping("/customer/waybillExport")
	@RepeatSubmit(interval = 120000, message = "运单导出中,请勿重复操作")
	public CommonResult<?> shipperWaybillExport(@RequestBody WaybillQuery waybillQuery) {
		// 暂时去掉导出时，创建时间跨度一年的限制
		// Date start = DateUtils.strToDate(waybillQuery.getCreateTimeStart());
		// Date end = DateUtils.strToDate(waybillQuery.getCreateTimeEnd());
		// if (null == start || null == end || DateUtils.addYears(start, 1).before(end)) {
		// return CommonResult.requestError("请指定运单创建时间，跨度不能超过1年");
		// }

		// 校验托运人项目分组权限
		this.addContractIds(waybillQuery);
		if (null != waybillQuery.getExportType() && 2 == waybillQuery.getExportType()) {
			// 将自定义导出的字段存入到mysql
			List<String> exportNameList = waybillQuery.getExportNameList();
			String exportNameListJson = JSON.toJSONString(exportNameList);
			if (StringUtils.isBlank(exportNameListJson)) {
				return CommonResult.error("请指定要导出的字段");
			}
			WaybillExportFile waaybillExport = waybillExportFileMapper.selectWaybillExportFileByUserId(SecurityUtils.getUserId(), 3, 2);
			if (ObjectUtils.isNotEmpty(waaybillExport)) {
				waybillExportFileMapper.updateExportWordsByUserId(exportNameListJson, SecurityUtils.getUserId(), 3, 2);
			} else {
				WaybillExportFile newWaybillExport = new WaybillExportFile();
				newWaybillExport.setId(TextUtil.getTimeSequenceID(5));
				newWaybillExport.setUserId(SecurityUtils.getUserId());
				newWaybillExport.setExportWords(exportNameListJson);
				newWaybillExport.setCreateTime(DateUtils.getNowDate());
				newWaybillExport.setUpdateTime(DateUtils.getNowDate());
				newWaybillExport.setExportType(2);
				newWaybillExport.setClientType(3);
				waybillExportFileMapper.insertWaybillExportFile(newWaybillExport);
			}
		}
		return exportService.commonExport(ExportEnum.SHIPPER_WAYBILL.getCode(), JSON.toJSONString(waybillQuery), ClientType.SHIPPER.val());
	}

	@ApiOperation(value = "托运人运单查询导出字段", notes = "托运人运单查询导出字段")
	@GetMapping("/customer/queryExportFileName")
	public CommonResult<WaybillExportFile> shipperSubcontractWaybillList() {
		return CommonResult.success(waybillExportFileMapper.selectWaybillExportFileByUserId(SecurityUtils.getUserId(), 3, 2));
	}

	@ApiOperation(value = "托运人运单自定义字段", notes = "托运人运单自定义字段")
	@GetMapping("/customer/queryExportName")
	public CommonResult<Map> queryExportName() {
		Map<String, String> map = exportService.queryExportName(WaybillTableRes.class);
		return CommonResult.success(map);
	}

	/**
	 * 托运人显示的转包运单（非自己录单）
	 *
	 * @param waybillReq
	 * @return
	 */
	@ApiOperation(value = "转包运单", notes = "托运人显示的其他托运人转包过来的运单")
	@PreAuthorize("@ss.hasPermi('transport:waybill:list')")
	@PostMapping("/customer/subcontractWaybillList")
	public TableDataInfo shipperSubcontractWaybillList(@RequestBody WaybillReq waybillReq) {
		if (null == waybillReq) {
			return getDataTable(HttpStatus.BAD_REQUEST, "参数错误");
		}
		if (!ClientType.SHIPPER.equals(SecurityUtils.getClientType())) {
			return getDataTable(HttpStatus.FORBIDDEN, "托运人端功能，您没有权限使用该接口");
		}
		TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		if (null == tenantUser) {
			return getDataTable(HttpStatus.UNAUTHORIZED, "登录失效");
		}
		waybillReq.setCustomerId(tenantUser.getTenantId());

		PageUtils.startPage();
		List<WaybillRes> list = waybillService.selectSubcontractWaybillList(waybillReq);
		return getDataTable(list);
	}

	/**
	 * 获取运单保单信息
	 *
	 * @param waybillId
	 * @return
	 */
	@ApiOperation(value = "获取运单保单信息", notes = "获取运单保单信息")
	@GetMapping("/shipper/waybillPolicyInfo")
	public CommonResult<WaybillPolicyInfo> waybillPolicyInfo(Long waybillId) {
		return waybillPolicyService.waybillPolicyInfo(waybillId);
	}

	/**
	 * 获取运单保单保价金额
	 *
	 * @param waybillId
	 * @return
	 */
	@ApiOperation(value = "获取运单投保金额", notes = "获取运单投保金额")
	@Log(title = "获取运单投保金额", businessType = BusinessType.INSURANCE_APPLY)
	@GetMapping("/shipper/getPolicyFare")
	public CommonResult<? extends WaybillPolicyInfo> shipperGetPolicyFare(Long waybillId, BigDecimal goodsValue) {
		logger.info("托运人端获取运单投保金额，请求参数：waybillId={}, goodsValue={}", waybillId, goodsValue);
		return waybillPolicyService.getPolicyFare(waybillId, goodsValue);
	}

	/**
	 * 运单投保
	 *
	 * @param insureReq
	 *            投保信息（waybillId：运单ID、goodsValue：货物价值）
	 * @return
	 */
	@ApiOperation(value = "运单投保", notes = "运单投保")
	@Log(title = "运单投保", businessType = BusinessType.INSURANCE_APPLY)
	@PostMapping("/shipper/insuranceApply")
	public CommonResult<WaybillPolicyInfo> shipperInsuranceApply(@RequestBody WaybillInsureReq insureReq) {
		logger.info("托运人端运单投保，请求参数：{}", JSON.toJSONString(insureReq));
		if (!ClientType.SHIPPER.equals(SecurityUtils.getClientType())) {
			return CommonResult.error("您不是托运人用户，没有操作运单投保权限");
		}
		return waybillPolicyService.shipperInsuranceApply(insureReq);
	}

	/**
	 * 运单确认收货(可批量)
	 *
	 * @param waybillReq
	 *            运单ID集合
	 * @return
	 */
	@ApiOperation(value = "运单确认收货", notes = "运单确认收货")
	@Log(title = "运单确认收货", businessType = BusinessType.CONFIRM_ARRIVE)
	@PostMapping("/shipper/confirmArrive")
	public CommonResult<Object> shipperConfirmArrive(@RequestBody WaybillReq waybillReq) {
		logger.info("托运人端运单确认收货，请求参数：{}", JSON.toJSONString(waybillReq));
		if (!ClientType.SHIPPER.equals(SecurityUtils.getClientType())) {
			logger.info("clientType不匹配：{}", SecurityUtils.getClientType().toString());
			return CommonResult.error("您不是托运人用户，没有操作运单确认收货权限");
		}
		if (null == waybillReq.getWaybillIds() || waybillReq.getWaybillIds().isEmpty()) {
			return CommonResult.requestError("请选择要确认收货的运单");
		}
		return waybillService.shipperConfirmArrive(waybillReq);
	}

	/**
	 * 要申请付款、付款审核的运单列表
	 */
	@ApiOperation(value = "申请付款、付款审核的运单列表--v4.5.0.2--shw", notes = "申请付款、付款审核的运单列表")
	@PreAuthorize("@ss.hasPermi('feesettle:payapply:customer:list')")
	@PostMapping("/customer/tradeApplyWaybillList")
	public TableDataInfo tradeApplyWaybillList(@RequestBody WaybillReq waybillReq) {
		if (null == waybillReq || null == waybillReq.getPayFlag() || (waybillReq.getPayFlag() != 0 && waybillReq.getPayFlag() != 1 && waybillReq.getPayFlag() != 2)) {
			return getDataTable(new ArrayList<WaybillRes>());
		}
		// 校验托运人项目分组权限
		this.addContractIds(waybillReq);

		startPage();

		List<WaybillRes> list = waybillTradeService.tradeApplyWaybillList(waybillReq);

		return getDataTable(list);
	}

	/**
	 * 导出申请付款（0）、付款审核（1）的运单列表
	 */
	@ApiOperation(value = "导出申请付款（0）、付款审核（1）的运单列表", notes = "导出申请付款（0）、付款审核（1）的运单列表--v4.5.1203")
	@PreAuthorize("@ss.hasPermi('feesettle:payapply:customer:export')")
	@PostMapping("/customer/tradeApplyWaybillList/export")
	public void tradeApplyWaybillListExport(@RequestBody WaybillReq waybillReq, HttpServletResponse response) {
		List<WaybillRes> list = waybillTradeService.tradeApplyWaybillList(waybillReq);

		// 校验托运人项目分组权限
		// this.addProjectGroup(waybillReq);
		this.addContractIds(waybillReq);
		if (StringUtils.isNotEmpty(list)) {
			// 0:申请付款
			if (StringUtils.isNull(waybillReq.getPayFlag()) || waybillReq.getPayFlag() == 0) {
				List<WaybillCustomerRes> res = new ArrayList<>();
				list.forEach(waybillRes -> {
					WaybillCustomerRes customerRes = new WaybillCustomerRes();
					BeanUtils.copyProperties(waybillRes, customerRes);
					res.add(customerRes);
				});

				ExcelUtil<WaybillCustomerRes> util = new ExcelUtil<WaybillCustomerRes>(WaybillCustomerRes.class);
				util.exportExcel(response, res, "申请付款");
			} else {
				// 1:付款审核
				List<WaybillPaymentReviewRes> reviewResList = new ArrayList<>();
				list.forEach(waybillRes -> {
					WaybillPaymentReviewRes reviewRes = new WaybillPaymentReviewRes();
					BeanUtils.copyProperties(waybillRes, reviewRes);
					reviewResList.add(reviewRes);
				});

				ExcelUtil<WaybillPaymentReviewRes> util = new ExcelUtil<WaybillPaymentReviewRes>(WaybillPaymentReviewRes.class);
				util.exportExcel(response, reviewResList, "付款审核");
			}
		}
	}

	/**
	 * 运单申请付款的记录
	 */
	@ApiOperation(value = "运单申请付款的记录", notes = "运单申请付款的记录")
	@PreAuthorize("@ss.hasPermi('feesettle:payapply:customer:list')")
	@GetMapping(value = "/customer/tradeApplyList")
	public AjaxResult tradeApplyList(@RequestParam Long waybillId) {
		List<CustomerTradeApply> list = null;
		if (null != waybillId && waybillId > 0) {
			CustomerTradeApply customerTradeApply = new CustomerTradeApply();
			customerTradeApply.setWaybillId(waybillId);
			PageUtils.startPage();
			list = customerTradeApplyService.selectCustomerTradeApplyList(customerTradeApply);
			for (CustomerTradeApply tradeApply : list) {
				TenantUser tenantUser = new TenantUser();
				tenantUser.setUserName(tradeApply.getAuditBy());
				List<TenantUser> tenantUsers = tenantUserService.selectTenanUserList(tenantUser);
				if (StringUtils.isNotEmpty(tenantUsers)) {
					tradeApply.setAuditBy(tenantUsers.get(0).getNickName());
				}

			}
		}
		return new AjaxResult(HttpStatus.SUCCESS, BusinessConstants.SUCCESS_DESC, list);
	}

	/**
	 * 申请付款
	 */
	@ApiOperation(value = "申请付款", notes = "申请付款")
	@PreAuthorize("@ss.hasPermi('feesettle:payapply:customer:add')")
	@Log(title = "申请付款", businessType = BusinessType.TRADEAPPLY)
	@PostMapping("/customer/tradeApply")
	public CommonResult tradeApply(@RequestParam Long[] waybillIds, @RequestParam BigDecimal applyFare) {
		if (null == waybillIds || 0 == waybillIds.length) {
			throw new RuntimeException("申请付款运单id为空");
		}

		String value = UUID.randomUUID().toString();
		List<String> keys = new ArrayList<>();
		// 可能会是多笔运单
		for (int i = 0; i < waybillIds.length; i++) {
			String key = "TRADE_APPLY_LOCK_" + waybillIds[i];
			keys.add(key);
		}
		try {
			keys.forEach(l -> {
				if (!redisCache.lock(l, value, 300)) { // 300秒锁定
					throw new RuntimeException("申请付款失败，同样的请求正在处理，请稍后再操作！");
				}
			});
			return waybillTradeService.tradeApply(waybillIds, applyFare);
		} finally {
			keys.forEach(l -> {
				redisCache.unlock(l, value);
			});

		}
	}

	/**
	 * 申请付款
	 */
	@ApiOperation(value = "申请付款", notes = "申请付款")
	@Log(title = "申请付款", businessType = BusinessType.TRADEAPPLY)
	@PostMapping("/customer/tradeApplyForAPI")
	public CommonResult tradeApplyForAPI(@RequestBody WaybillReq req) {
		if (null == req.getWaybillIds() || 0 == req.getWaybillIds().size()) {
			throw new RuntimeException("申请付款运单id为空");
		}
		Long[] waybillIds = req.getWaybillIds().toArray(new Long[0]);
		String value = UUID.randomUUID().toString();
		List<String> keys = new ArrayList<>();
		// 可能会是多笔运单
		for (int i = 0; i < waybillIds.length; i++) {
			String key = "TRADE_APPLY_LOCK_" + waybillIds[i];
			keys.add(key);
		}
		try {
			keys.forEach(l -> {
				if (!redisCache.lock(l, value, 300)) { // 300秒锁定
					throw new RuntimeException("申请付款失败，同样的请求正在处理，请稍后再操作！");
				}
			});
			return waybillTradeService.tradeApplyForAPI(waybillIds, req.getApplyFare());
		} finally {
			keys.forEach(l -> {
				redisCache.unlock(l, value);
			});
		}
	}

	/**
	 * 付款申请的风险提示<br/>
	 * 判断运单里的五证（司机身份证、驾驶证、司机从业资格证、行驶证、车辆道路运输证）及字段是否齐全<br/>
	 * 承运车辆大于4.5吨，判断从业资格证和道路运输证是否上传，字段是否完善（从业资格证不可以000000000000结尾（如320481000000000000）、道路运输证不可以000000结尾（如320481000000））
	 *
	 * @param waybillIds
	 * @return
	 */
	@ApiOperation(value = "付款申请的风险提示", notes = "付款申请的风险提示，判断运单里的五证（司机身份证、驾驶证、司机从业资格证、行驶证、车辆道路运输证）及字段是否齐全")
	@PreAuthorize("@ss.hasPermi('feesettle:payreview:customer:approveok')")
	@PostMapping("/customer/tradeApplyRisk")
	public CommonResult tradeApplyRisk(@RequestParam Long[] waybillIds) {
		return waybillTradeService.tradeApplyRisk(waybillIds);
	}

	/**
	 * 付款审核通过的风险提示
	 */
	@ApiOperation(value = "付款审核通过的风险提示", notes = "付款审核通过的风险提示")
	@PreAuthorize("@ss.hasPermi('feesettle:payreview:customer:approveok')")
	@PostMapping("/customer/tradeRisk")
	public AjaxResult tradeRisk(@RequestParam Long[] waybillIds) {
		return waybillTradeService.tradeRisk(waybillIds);
	}

	/**
	 * 付款审核
	 *
	 * @param waybillIds
	 * 		运单ID
	 * @param auditStatus
	 * 		审核状态(1:审核通过 2:审核不通过)
	 * @param remark
	 * 		驳回原因
	 * @return
	 */
	@ApiOperation(value = "查询运单列付款审核表", notes = "付款审核")
	@PreAuthorize("@ss.hasPermi('feesettle:payreview:customer:audi')")
	@Log(title = "付款审核", businessType = BusinessType.TRADEAUDIT)
	@PostMapping("/customer/tradeAudit")
	public AjaxResult tradeAudit(@RequestParam Long[] waybillIds, @RequestParam Integer auditStatus, @RequestParam(required = false) String remark) {
		return waybillTradeService.tradeAudit(waybillIds, auditStatus, remark);
	}

	/**
	 * 付款审核API专用
	 *
	 * @param req
	 * @return
	 */
	@ApiOperation(value = "查询运单列付款审核表", notes = "付款审核")
	@PreAuthorize("@ss.hasPermi('feesettle:payreview:customer:audi')")
	@Log(title = "付款审核", businessType = BusinessType.TRADEAUDIT)
	@PostMapping("/customer/tradeAuditForAPI")
	public AjaxResult tradeAuditForAPI(@RequestBody WaybillReq req) {
		Long[] waybillIds = req.getWaybillIds().toArray(new Long[0]);
		return waybillTradeService.tradeAudit(waybillIds, req.getAuditStatus(), req.getAuditRemark());
	}

	@ApiOperation(value = "申请付款发送运输合同--v4.3.14--shw", notes = "申请付款发送运输合同--v4.3.14--shw")
	@PostMapping("/customer/sendMsg")
	public AjaxResult sendMsg(@RequestParam Long[] waybillIds) {
		// return AjaxResult.popError("该功能暂未开放!");
		return waybillElectronicContractService.sendMsg(waybillIds);
	}

	/**
	 * 确认付款的运单列表
	 */
	@ApiOperation(value = "确认付款运单列表查询", notes = "确认付款运单列表查询")
	@PreAuthorize("@ss.hasPermi('feesettle:payconfirm:customer:list')")
	@PostMapping("/customer/obligationsWaybillList")
	public TableInfo<PayApply> obligationsWaybillList(@RequestBody PayApplyQueryReq applyQuery) {
		// 校验托运人项目分组权限
		this.addContractIds(applyQuery);
		PageUtils.startPage();
		List<PayApply> list = waybillTradeService.prePaymentApplyList(applyQuery);
		return getTableData(list);
	}

	@ApiOperation(value = "确认付款的运单列表导出", notes = "确认付款的运单列表导出")
	@PreAuthorize("@ss.hasPermi('feesettle:payconfirm:customer:export')")
	@PostMapping("/customer/obligationsWaybillList/export")
	public CommonResult<?> obligationsWaybillListExport(@RequestBody WaybillReq waybillReq) {
		// 校验托运人项目分组权限
		// this.addProjectGroup(waybillReq);
		this.addContractIds(waybillReq);
		TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		waybillReq.setCustomerId(tenantUser.getTenantId());
		return exportService.commonExport(ExportEnum.PAY_CONFIRM.getCode(), JSON.toJSONString(waybillReq), ClientType.SHIPPER.val());
	}

	@ApiOperation(value = "付款审核的运单列表导出", notes = "付款审核的运单列表导出")
	@PreAuthorize("@ss.hasPermi('feesettle:payAudit:customer:export')")
	@PostMapping("/customer/payAuditWaybillList/export")
	public CommonResult<?> payAuditWaybillListExport(@RequestBody WaybillReq waybillReq) {
		// 校验托运人项目分组权限
		// this.addProjectGroup(waybillReq);
		this.addContractIds(waybillReq);
		// 判断当前操作是哪个端
		if (SecurityUtils.getClientType().equals(ClientType.SHIPPER)) {
			TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
			waybillReq.setCustomerId(tenantUser.getTenantId());
		} else {
			FinanceUser financeUser = SecurityUtils.getLoginUser().getFinanceUser();
			Long customerId = financeUser.getCustomerId();
			long businessModelId = SecurityUtils.getBusinessModelId();
			waybillReq.setCustomerId(customerId);
			waybillReq.setBusinessModelId(businessModelId);
		}

		return exportService.commonExport(ExportEnum.PAY_AUDIT.getCode(), JSON.toJSONString(waybillReq), SecurityUtils.getClientType().val());
	}

	/**
	 * 付款审核
	 *
	 * @param waybillIds
	 * 		运单ID
	 * @param remark
	 * 		驳回原因
	 * @return
	 */
	@ApiOperation(value = "付款驳回", notes = "确认付款页面，驳回审核通过的申请单")
	@PreAuthorize("@ss.hasPermi('feesettle:payreview:customer:audi')")
	@Log(title = "付款驳回", businessType = BusinessType.TRADEAUDIT)
	@PostMapping("/customer/tradeRevoke")
	public AjaxResult tradeRevoke(@RequestParam Long[] waybillIds, @RequestParam String remark) {
		return waybillTradeService.tradeRevoke(waybillIds, remark);
	}

	@PostMapping("/customer/canTradeRevoke")
	public AjaxResult canTradeRevoke(@RequestParam Long[] waybillIds) {
		return waybillTradeService.canTradeRevoke(waybillIds);
	}

	@ApiOperation(value = "补全轨迹", notes = "补全轨迹")
	@GetMapping("/customer/obligationsWaybillList/completeTrajectory/{id}")
	public CommonResult getCompleteTrajectory(@PathVariable Long id) {

		return waybillService.getCompleteTrajectory(id);
	}

	@ApiOperation(value = "补全轨迹", notes = "补全轨迹")
	@PostMapping("/customer/obligationsWaybillList/completeTrajectory")
	public CommonResult getCompleteTrajectoryByTime(@RequestBody GpsTimeReq req) {
		CommonResult res = waybillService.getCompleteTrajectoryByTime(req);
		if (CommonResult.isSuccess(res)) {
			waybillRiskService.waybillRiskCheckById(req.getId(), SecurityUtils.getNickname(), false);
		}
		return res;
	}

	@ApiOperation(value = "补全轨迹获取默认时间", notes = "补全轨迹获取默认时间")
	@PostMapping("/customer/obligationsWaybillList/completeTrajectory/getTime")
	public CommonResult getCompleteTrajectoryTime(@RequestBody GpsTimeReq req) {
		return waybillService.getCompleteTrajectoryTime(req.getId());
	}

	@ApiOperation(value = "运单管理-复制运单时的运单详情", notes = "运单管理-复制运单时的运单详情-4.3.4-cys")
	@GetMapping("/customer/obligationsWaybillList/copyWaybill/{id}")
	public CommonResult<WaybillCopyRes> copyWaybill(@PathVariable Long id) {
		return waybillService.copyWaybill(id);
	}

	@ApiOperation(value = "运单编辑--查询不可编辑字段类型-4.3.4-cys")
	@GetMapping("/customer/obligationsWaybillList/queryEditingWaybill/{id}")
	public CommonResult queryEditingWaybill(@PathVariable Long id) {
		return CommonResult.success(waybillService.queryEditingWaybill(id));
	}

	@ApiOperation(value = "运单托运人关联表--数据处理--v4.3.4-zxy")
	@GetMapping("/dealSQLData/test")
	public CommonResult dealSQLData() {
		waybillCustomerService.dealSQLData();
		return CommonResult.success();
	}

	/**
	 * 根据经纬度获取详细地理位置信息
	 *
	 * @param lon
	 * @param lat
	 * @return
	 */
	@RequestMapping(value = "/getFormattedAddressByLonLat", method = RequestMethod.GET)
	@ResponseBody
	@RateLimiter(key = "Amap", limitType = LimitType.IP)
	public CommonResult searchProjectCorporationName(@RequestParam("lon") String lon, @RequestParam("lat") String lat) {
		if (TextUtil.isNull(lon) || TextUtil.isNull(lat)) {
			return CommonResult.error("经纬度信息为空");
		}
		AmapLocationModel model = amapService.getLocationInfo(lon, lat);
		if (null == model) {
			return CommonResult.error("该经纬度没有获取到详细地理位置信息");
		}
		return CommonResult.success(model.getFormattedAddress());
	}

	/**
	 * 记录用户公路运单筛选列查询记录
	 *
	 * @return
	 */
	@ApiOperation(value = "保存搜索项--4.4.16--shw", notes = "保存搜索项--4.4.16--shw")
	@RequestMapping(value = "/saveScreen", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult<?> saveScreen(@RequestBody ScreenReq screen) {
		// logger.debug("保存运单筛选列，请求参数：{}", JSON.toJSONString(screen));
		try {
			String items = URLDecoder.decode(screen.getScreen(), StandardCharsets.UTF_8.name());
			Integer type = screen.getType();
			// 将用户筛选记录保存
			if (type == 1) {
				redisCache.setCacheObject(SecurityUtils.getUserId() + "billScreen", items);
			} else if (type == 2) {
				redisCache.setCacheObject(SecurityUtils.getUserId() + "billLoadingScreen", items);
			} else if (type == 3) {
				redisCache.setCacheObject(SecurityUtils.getUserId() + "billUnloadScreen", items);
			} else if (type == 4) {
				redisCache.setCacheObject(SecurityUtils.getUserId() + "billUnreceiptScreen", items);
			} else if (type == 5) {
				redisCache.setCacheObject(SecurityUtils.getUserId() + "billReceiptScreen", items);
			}

			return CommonResult.success(null, "保存成功");
		} catch (Exception e) {
			logger.error("保存筛选记录出错--" + ExceptionUtils.getStackTrace(e));
			return CommonResult.error("保存筛选记录出错");
		}
	}

	/**
	 * 查询用户公路运单筛选列查询记录
	 *
	 * @return
	 */
	@ApiOperation(value = "查询搜索项--4.4.16--shw", notes = "查询搜索项--4.4.16--shw")
	@RequestMapping(value = "/getScreen/{type}", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult<String> getScreen(@PathVariable("type") Integer type) {
		try {
			// 获取用户公路运单列表查询记录
			String record = "";
			if (type == 1) {
				record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billScreen");
			} else if (type == 2) {
				record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billLoadingScreen");
			} else if (type == 3) {
				record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billUnloadScreen");
			} else if (type == 4) {
				record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billUnreceiptScreen");
			} else if (type == 5) {
				record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billReceiptScreen");
			}

			logger.debug("获取运单筛选列，{}", record);
			return CommonResult.success(record);
		} catch (Exception e) {
			logger.error("获取筛选记录出错--" + ExceptionUtils.getStackTrace(e));
			return CommonResult.error("获取筛选记录失败");
		}
	}

	/**
	 * 保存合同签署地址
	 *
	 * @return
	 */
	@ApiOperation(value = "保存合同签署地址-4.3.13-shw", notes = "保存合同签署地址-4.3.13-shw")
	@RequestMapping(value = "/saveElectronicContractUrl", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult<?> saveElectronicContractUrl(@RequestBody ElectronicContractReq req) {
		return CommonResult.error("功能暂未开放");// waybillService.saveElectronicContractUrl(req);
	}

	@ApiOperation(value = "签署合同-4.3.13-shw", notes = "保存合同签署地址-4.3.13-shw")
	@RequestMapping(value = "/makeElectronicContract", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult getElectronicContract(@RequestBody ElectronicContractReq req) {

		return waybillElectronicContractService.makeElectronicContract(req);

	}

	/**
	 * 根据运单编号查询运单信息
	 *
	 * @param shippingNoteNumber
	 * @return
	 */
	@ApiOperation(value = "根据运单编号查询运单信息", notes = "根据运单编号查询运单信息")
	@RequestMapping(value = "/getWaybillInfoByCode", method = RequestMethod.GET)
	@ResponseBody
	public CommonResult<Waybill> getWaybillInfoByCode(String shippingNoteNumber) {
		if (StringUtils.isBlank(shippingNoteNumber)) {
			return CommonResult.requestError("运单编号为空");
		}
		Waybill waybill = waybillService.getWaybillInfoByCode(shippingNoteNumber);
		if (null == waybill) {
			return CommonResult.error("未查询到运单");
		}
		return CommonResult.success(waybill);
	}

	/**
	 * 付款审核界面---根据项目id查询该项目下付款审核中，累计需要支付的金额总额
	 *
	 * @param contractId
	 * @return
	 */
	@ApiOperation(value = "根据项目id查询该项目下付款审核中，累计需要支付的金额总额--4.3.14", notes = "根据项目id查询该项目下付款审核中，累计需要支付的金额总额--4.3.14")
	@RequestMapping(value = "/paymentReviewTotalAmountByContractId/{contractId}", method = RequestMethod.GET)
	public CommonResult paymentReviewTotalAmountByContractId(@PathVariable("contractId") Long contractId) {
		WaybillReq waybillReq = new WaybillReq();
		waybillReq.setPageSize(null);
		waybillReq.setPageNum(null);
		if (ClientType.SHIPPER.equals(SecurityUtils.getClientType())) {
			waybillReq.setIsRecorder(0);
		} else {
			waybillReq.setIsRecorder(1);
			waybillReq.setBusinessModelId(SecurityUtils.getBusinessModelId());
		}
		waybillReq.setFrameworkContractId(contractId);
		waybillReq.setClientType(SecurityUtils.getClientType().val());
		waybillReq.setCustomerId(SecurityUtils.getShipperId());
		BigDecimal amount = waybillService.paymentReviewTotalAmountByContractId(waybillReq);
		return CommonResult.success(null == amount ? BigDecimal.ZERO : amount);
	}

	/**
	 * 申请付款页面---根据项目id查询申请付款未付金额得统计
	 *
	 * @param contractId
	 * @return
	 */
	@ApiOperation(value = "根据项目id查询申请付款未付金额得统计--4.3.14", notes = "根据项目id查询申请付款未付金额得统计--4.3.14")
	@RequestMapping(value = "/statisticsUnpaidFareByContractId/{contractId}", method = RequestMethod.GET)
	public CommonResult statisticsUnpaidFareByContractId(@PathVariable("contractId") Long contractId) {
		Long customerId = SecurityUtils.getShipperId();
		return CommonResult.success(waybillService.statisticsUnpaidFareByContractId(contractId, customerId));
	}

	/**
	 *
	 * @param page
	 *            1付款申请 2付款审核 3付款结算4.付款已申请
	 * @return
	 */
	@ApiOperation(value = "记录用户最后一次选择项目--4.3.14", notes = "记录用户最后一次选择项目--4.3.14")
	@GetMapping("/saveLastSelectedContract")
	public CommonResult saveLastSelectedContract(Integer page, Long contractId) {
		redisCache.setCacheMapValue("lastSelectedContracts", SecurityUtils.getUserId() + "_" + page, JSON.toJSONString(contractId));
		return CommonResult.success("保存成功");
	}

	/**
	 * @param page
	 * 		1付款申请 2付款审核 3付款结算
	 * @return
	 */
	@ApiOperation(value = "获取用户最后一次选择项目--4.3.14", notes = "记录用户最后一次选择项目--4.3.14")
	@GetMapping("/getLastSelectedContract")
	public CommonResult getLastSelectedContract(Integer page) {
		Long contracts = JSON.parseObject((String) redisCache.getCacheMapValue("lastSelectedContracts", SecurityUtils.getUserId() + "_" + page), Long.class);
		return CommonResult.success(contracts);
	}

	/**
	 * 根据waybillId判断是否属于某一个项目
	 *
	 * @param waybillIds
	 * 		运单id
	 * @return
	 */
	@ApiOperation(value = "根据waybillId判断是否属于数贷通项目", notes = "根据waybillId判断是否属于数贷通项目")
	@GetMapping(value = "/checkProjectByWaybillId")
	@ResponseBody
	public AjaxResult checkProjectByWaybillIds(Long[] waybillIds) {
		if (waybillIds == null) {
			return AjaxResult.requestError("运单编号列表为空");
		}
		// 根据waybillid查询到项目id
		List<String> shippingNoteNumberList = new ArrayList<>();
		for (Long waybillId : waybillIds) {
			Waybill waybill = waybillService.selectWaybillById(waybillId);
			if (huaXiaUtils.isHuaXiaProject(waybill.getFrameworkContractId())) {
				shippingNoteNumberList.add(waybill.getShippingNoteNumber());
			}
		}
		if (!shippingNoteNumberList.isEmpty()) {
			StringBuilder errorMessage = new StringBuilder("运单：");
			for (String shippingNoteNumber : shippingNoteNumberList) {
				errorMessage.append(shippingNoteNumber).append(",");
			}
			errorMessage.deleteCharAt(errorMessage.length() - 1);
			errorMessage.append("所属项目是数贷通模式不支持驳回修改");

			return AjaxResult.error(HttpStatus.BAD_REQUEST, errorMessage.toString());
		}
		return AjaxResult.success();
	}

	@ApiOperation(value = "确认付款保存用户设置的筛选列表至缓存--4.3.20版本", notes = "确认付款保存用户设置的筛选列表至缓存--4.3.20版本")
	@PostMapping("/savePaymentFilterItems")
	@ResponseBody
	public CommonResult<?> savePaymentFilterItems(@RequestBody PayApplyQueryReq applyQuery) {
		// logger.debug("保存确认付款列表筛选列，请求参数：{}", JSON.toJSONString(applyQuery));
		try {
			String items = URLDecoder.decode(applyQuery.getPaymentFilterItems(), StandardCharsets.UTF_8.name());
			logger.debug("保存确认付款列表筛选列，items：{}", items);
			// 将用户筛选记录保存
			redisCache.setCacheObject(SecurityUtils.getUserId() + "PaymentFilterItems", items);
			return CommonResult.success(null, "保存成功");
		} catch (Exception e) {
			logger.error("保存确认付款列表筛选列出错--" + ExceptionUtils.getStackTrace(e));
			return CommonResult.error("保存筛选记录出错");
		}
	}

	@ApiOperation(value = "确认付款获取用户设置的筛选列表缓存--4.3.20版本", notes = "确认付款获取用户设置的筛选列表缓存--4.3.20版本")
	@GetMapping("/getPaymentFilterItems")
	@ResponseBody
	public CommonResult<String> getPaymentFilterItems() {
		try {
			// 获取用户公路运单列表查询记录
			String record = redisCache.getCacheObject(SecurityUtils.getUserId() + "PaymentFilterItems");
			logger.debug("获取确认付款筛选列，{}", record);
			return CommonResult.success(record);
		} catch (Exception e) {
			logger.error("获取确认付款筛选列出错--" + ExceptionUtils.getStackTrace(e));
			return CommonResult.error("获取筛选记录失败");
		}
	}

	@ApiOperation(value = "获取托运人运单投保记录列表--v4.3.24", notes = "获取托运人运单投保记录列表--v4.3.24")
	@PostMapping("/getCustomerWaybillPolicyRecordList")
	public TableInfo<WaybillPolicyRecordRes> getCustomerWaybillPolicyRecordList(@RequestBody WaybillPolicyRecordReq req) {
		startPage();
		List<WaybillPolicyRecordRes> list = waybillPolicyService.getCustomerWaybillPolicyRecordList(req);
		return getTableData(list);
	}

	@ApiOperation(value = "获取托运人运单投保记录列表统计--v4.4.12 yxq", notes = "获取托运人运单投保记录列表统计--v4.4.12 yxq")
	@PostMapping("/getCustomerWaybillPolicyRecordListSum")
	public CommonResult<WaybillPolicyRecordRes> getCustomerWaybillPolicyRecordListSum(@RequestBody WaybillPolicyRecordReq req) {
		WaybillPolicyRecordRes waybillPolicyRecordRes = waybillPolicyService.getCustomerWaybillPolicyRecordListSum(req);
		return CommonResult.success(waybillPolicyRecordRes);
	}

	@ApiOperation(value = "获取托运人运单投保记录列表导出--v4.4.12", notes = "获取托运人运单投保记录列表导出--v4.4.12")
	@PostMapping("/getCustomerWaybillPolicyRecordList/export")
	public void policyRecordListExport(@RequestBody WaybillPolicyRecordReq req, HttpServletResponse response) {
		List<WaybillPolicyRecordRes> list = waybillPolicyService.getCustomerWaybillPolicyRecordList(req);
		ExcelUtil<WaybillPolicyRecordRes> util = new ExcelUtil<WaybillPolicyRecordRes>(WaybillPolicyRecordRes.class);
		util.exportExcel(response, list, "投保记录");
	}

	@ApiOperation(value = "根据id获取运单投保记录--v4.3.24", notes = "根据id获取运单投保记录--v4.3.24")
	@GetMapping("/getCustomerWaybillPolicyRecordById/{id}")
	public CommonResult<WaybillPolicyRecordInfoRes> getCustomerWaybillPolicyRecordById(@PathVariable("id") Long id) {
		return waybillPolicyService.getCustomerWaybillPolicyRecordById(id);
	}

	/**
	 * 记录用户公路运单筛选列查询记录
	 *
	 * @return
	 */
	@ApiOperation(value = "保存总览搜索项", notes = "保存总览搜索项")
	@RequestMapping(value = "/saveOverviewScreen", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult<?> saveOverviewScreen(@RequestBody ScreenReq screen) {
		try {
			Integer type = screen.getType();
			String items = URLDecoder.decode(screen.getScreen(), StandardCharsets.UTF_8.name());
			// 将用户筛选记录保存
			if (type == 1) {
				redisCache.setCacheObject(SecurityUtils.getUserId() + "billOverviewScreen", items);
			} else if (type == 2) {
				redisCache.setCacheObject(SecurityUtils.getUserId() + "billLoadingOverviewScreen", items);
			} else if (type == 3) {
				redisCache.setCacheObject(SecurityUtils.getUserId() + "billUnloadOverviewScreen", items);
			} else if (type == 4) {
				redisCache.setCacheObject(SecurityUtils.getUserId() + "billUnreceiptOverviewScreen", items);
			} else if (type == 5) {
				redisCache.setCacheObject(SecurityUtils.getUserId() + "billReceiptOverviewScreen", items);
			}
			return CommonResult.success(null, "保存成功");
		} catch (Exception e) {
			logger.error("保存筛选记录出错--" + ExceptionUtils.getStackTrace(e));
			return CommonResult.error("保存筛选记录出错");
		}
	}

	/**
	 * 查询用户公路运单筛选列查询记录
	 *
	 * @return
	 */
	@ApiOperation(value = "查询总览搜索项", notes = "查询总览搜索项")
	@RequestMapping(value = "/getOverviewScreen/{type}", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult<String> getOverviewScreen(@PathVariable("type") Integer type) {
		try {
			// 获取用户公路运单列表查询记录
			String record = "";
			if (type == 1) {
				if (redisCache.hasKey(SecurityUtils.getUserId() + "billOverviewScreen")) {
					record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billOverviewScreen");
				} else {
					record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billScreen");
					redisCache.setCacheObject(SecurityUtils.getUserId() + "billOverviewScreen", record);
				}
			} else if (type == 2) {
				if (redisCache.hasKey(SecurityUtils.getUserId() + "billLoadingOverviewScreen")) {
					record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billLoadingOverviewScreen");
				} else {
					record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billLoadingScreen");
					redisCache.setCacheObject(SecurityUtils.getUserId() + "billLoadingOverviewScreen", record);
				}
			} else if (type == 3) {
				if (redisCache.hasKey(SecurityUtils.getUserId() + "billUnloadOverviewScreen")) {
					record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billUnloadOverviewScreen");
				} else {
					record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billUnloadScreen");
					redisCache.setCacheObject(SecurityUtils.getUserId() + "billUnloadOverviewScreen", record);
				}
			} else if (type == 4) {
				if (redisCache.hasKey(SecurityUtils.getUserId() + "billUnreceiptOverviewScreen")) {
					record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billUnreceiptOverviewScreen");
				} else {
					record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billUnreceiptScreen");
					redisCache.setCacheObject(SecurityUtils.getUserId() + "billUnreceiptOverviewScreen", record);
				}
			} else if (type == 5) {
				if (redisCache.hasKey(SecurityUtils.getUserId() + "billReceiptOverviewScreen")) {
					record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billReceiptOverviewScreen");
				} else {
					record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billReceiptScreen");
					redisCache.setCacheObject(SecurityUtils.getUserId() + "billReceiptOverviewScreen", record);
				}
			}
			logger.debug("获取运单筛选列，{}", record);
			return CommonResult.success(record);
		} catch (Exception e) {
			logger.error("获取筛选记录出错--" + ExceptionUtils.getStackTrace(e));
			return CommonResult.error("获取筛选记录失败");
		}
	}

	/**
	 * 查询用户公路运单筛选列查询记录
	 *
	 * @return
	 */
	@ApiOperation(value = "查询运单总览列表", notes = "查询运单总览列表")
	@RequestMapping(value = "/getWaybillOverview", method = RequestMethod.POST)
	@ResponseBody
	public TableInfo<WaybillTableRes> getWaybillOverview(@RequestBody WaybillQuery waybillQuery, HttpServletRequest request) {
		logger.info("托运人运单列表，请求参数：" + JSON.toJSONString(waybillQuery));
		if (waybillQuery == null) {
			return TableInfo.requestError("参数错误");
		}
		if (!ClientType.SHIPPER.equals(SecurityUtils.getClientType())) {
			return TableInfo.forbidden("托运人端功能，您没有权限使用该接口");
		}
		waybillQuery.setCustomerId(SecurityUtils.getShipperId());
		waybillQuery.setPageSize(Integer.parseInt(request.getParameterMap().get("pageSize")[0]));
		waybillQuery.setOffSet((Integer.parseInt(request.getParameterMap().get("pageNum")[0]) - 1) * waybillQuery.getPageSize());
		waybillQuery.setPageNum(null); // 防止分页插件自动分页

		// 校验托运人项目分组权限
		this.addContractIds(waybillQuery);

		return waybillService.selectShipperWaybillPageList(waybillQuery);

	}

	/**
	 * 记录用户申请付款筛选项
	 *
	 * @return
	 */
	@ApiOperation(value = "记录用户申请付款筛选项-4.4.8-ysy", notes = "记录用户申请付款筛选项-4.4.8-ysy")
	@RequestMapping(value = "/saveApplyFareScreen", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult<?> saveApplyFareScreen(@RequestBody ScreenReq screen) {
		// logger.debug("保存运单筛选列，请求参数：{}", JSON.toJSONString(screen));
		try {
			String items = URLDecoder.decode(screen.getScreen(), StandardCharsets.UTF_8.name());
			logger.debug("保存申请付款筛选列，items：{}", items);
			// 将用户筛选记录保存
			redisCache.setCacheObject(SecurityUtils.getUserId() + "applyFareScreen", items);
			return CommonResult.success(null, "保存成功");
		} catch (Exception e) {
			logger.error("保存申请付款筛选记录出错--" + ExceptionUtils.getStackTrace(e));
			return CommonResult.error("保存申请付款筛选记录出错");
		}
	}

	/**
	 * 查询用户申请付款筛选列查询记录
	 *
	 * @return
	 */
	@ApiOperation(value = "查询用户申请付款筛选项-4.4.8-ysy", notes = "查询用户申请付款筛选项-4.4.8-ysy")
	@RequestMapping(value = "/getApplyFareScreen", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult<String> getApplyFareScreen() {
		try {
			// 获取用户公路运单列表查询记录
			String record = redisCache.getCacheObject(SecurityUtils.getUserId() + "applyFareScreen");
			logger.debug("获取申请付款筛选列，{}", record);
			return CommonResult.success(record);
		} catch (Exception e) {
			logger.error("获取申请付款筛选记录出错--" + ExceptionUtils.getStackTrace(e));
			return CommonResult.error("获取申请付款筛选记录失败");
		}
	}

	/**
	 * 记录用户申请付款筛选项
	 *
	 * @return
	 */
	@ApiOperation(value = "记录用户申请付款筛选项(已申请)-4.4.8-ysy", notes = "记录用户申请付款筛选项(已申请)-4.4.8-ysy")
	@RequestMapping(value = "/saveApplyFareAlreadyScreen", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult<?> saveApplyFareAlreadyScreen(@RequestBody ScreenReq screen) {
		// logger.debug("保存运单筛选列，请求参数：{}", JSON.toJSONString(screen));
		try {
			String items = URLDecoder.decode(screen.getScreen(), StandardCharsets.UTF_8.name());
			logger.debug("保存申请付款（已申请）筛选列，items：{}", items);
			// 将用户筛选记录保存
			redisCache.setCacheObject(SecurityUtils.getUserId() + "applyFareAlreadyScreen", items);
			return CommonResult.success(null, "保存成功");
		} catch (Exception e) {
			logger.error("保存申请付款（已申请）筛选记录出错--" + ExceptionUtils.getStackTrace(e));
			return CommonResult.error("保存申请付款（已申请）筛选记录出错");
		}
	}

	/**
	 * 查询用户申请付款筛选列查询记录
	 *
	 * @return
	 */
	@ApiOperation(value = "查询用户申请付款（已申请）筛选项-4.4.8-ysy", notes = "查询用户申请付款（已申请）筛选项-4.4.8-ysy")
	@RequestMapping(value = "/getApplyFareAlreadyScreen", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult<String> getApplyFareAlreadyScreen() {
		try {
			// 获取用户公路运单列表查询记录
			String record = redisCache.getCacheObject(SecurityUtils.getUserId() + "applyFareAlreadyScreen");
			logger.debug("获取申请付款（已申请）筛选列，{}", record);
			return CommonResult.success(record);
		} catch (Exception e) {
			logger.error("获取申请付款（已申请）筛选记录出错--" + ExceptionUtils.getStackTrace(e));
			return CommonResult.error("获取申请付款（已申请）筛选记录失败");
		}
	}

	/**
	 * 记录用户付款审核筛选项
	 *
	 * @return
	 */
	@ApiOperation(value = "记录用户付款审核筛选项-4.4.8-ysy", notes = "记录用户付款审核筛选项-4.4.8-ysy")
	@RequestMapping(value = "/saveApplyFareAuditScreen", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult<?> saveApplyFareAuditScreen(@RequestBody ScreenReq screen) {
		// logger.debug("保存运单筛选列，请求参数：{}", JSON.toJSONString(screen));
		try {
			String items = URLDecoder.decode(screen.getScreen(), StandardCharsets.UTF_8.name());
			logger.debug("保存付款审核筛选列，items：{}", items);
			// 将用户筛选记录保存
			redisCache.setCacheObject(SecurityUtils.getUserId() + "applyFareAuditScreen", items);
			return CommonResult.success(null, "保存成功");
		} catch (Exception e) {
			logger.error("保存付款审核筛选记录出错--" + ExceptionUtils.getStackTrace(e));
			return CommonResult.error("保存付款审核筛选记录出错");
		}
	}

	/**
	 * 查询用户申请付款筛选列查询记录
	 *
	 * @return
	 */
	@ApiOperation(value = "查询用户付款审核筛选项-4.4.8-ysy", notes = "查询用户付款审核筛选项-4.4.8-ysy")
	@RequestMapping(value = "/getApplyFareAuditScreen", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult<String> getApplyFareAuditScreen() {
		try {
			// 获取用户公路运单列表查询记录
			String record = redisCache.getCacheObject(SecurityUtils.getUserId() + "applyFareAuditScreen");
			logger.debug("获取付款审核筛选列，{}", record);
			return CommonResult.success(record);
		} catch (Exception e) {
			logger.error("获取付款审核筛选记录出错--" + ExceptionUtils.getStackTrace(e));
			return CommonResult.error("获取付款审核筛选记录失败");
		}
	}

	/**
	 * 申请付款运单导出
	 */
	@ApiOperation(value = "申请付款运单导出", notes = "申请付款运单导出")
	@PreAuthorize("@ss.hasPermi('feesettle:payapply:customer:list')")
	@PostMapping("/customer/exportRequestedTradeApplyWaybillList")
	public CommonResult exportRequestedTradeApplyWaybillList(@RequestBody WaybillReq waybillReq) {

		// 校验托运人项目分组权限
		this.addContractIds(waybillReq);
		waybillReq.setCustomerId(SecurityUtils.getShipperId());

		if (waybillReq.getPayFlag() == 0) {
			return exportService.commonExport(ExportEnum.APPLY_PAYMENT.getCode(), JSON.toJSONString(waybillReq), ClientType.SHIPPER.val());
		} else {
			return exportService.commonExport(ExportEnum.TRADE_APPLY_WAYBILL_LIST.getCode(), JSON.toJSONString(waybillReq), ClientType.SHIPPER.val());
		}
	}

	/**
	 * 重新计算运费
	 */
	@ApiOperation(value = "重新计算运费", notes = "重新计算运费")
	@PostMapping("/recalculateWaybillFare")
	public CommonResult recalculateWaybillFare(@RequestBody WaybillReq waybillReq) {
		return waybillService.recalculateWaybillFare(waybillReq);
	}

	/**
	 * 收款人列表
	 */
	@ApiOperation(value = "收款人列表--v4.5.0.2--shw", notes = "收款人列表--v4.5.0.2--shw")
	@PostMapping("/payeeInfoList")
	public CommonResult<List> payeeInfoList(@RequestBody PayeeReq req) {
		return waybillService.payeeInfoList(req);
	}

	// 判断传入的项目id是否属于善道，如果是善道的项目则返回善道id，不是善道项目返回0
	@ApiOperation(value = "判断项目是否属于善道--v4.5.7--yxq", notes = "判断项目是否属于善道--v4.5.7--yxq")
	@GetMapping("/shandaoFrameworkContract/{frameworkContractId}")
	public CommonResult shandaoFrameworkContract(@PathVariable Long frameworkContractId) {
		return waybillService.shandaoFrameworkContract(frameworkContractId);
	}

	@ApiOperation(value = "善道签约的辅助员列表--v4.5.7--yxq", notes = "善道签约的辅助员列表--v4.5.7--yxq")
	@PostMapping("/shandaoPayeeInfoList")
	public CommonResult<List> shandaoPayeeInfoList(@RequestBody PayeeReq req) {
		return waybillService.shandaoPayeeInfoList(req);
	}

	/**
	 * 收款人列表
	 */
	@ApiOperation(value = "处理运单orderCreateTime", notes = "处理运单orderCreateTime")
	@PostMapping("/updateOrderCreateTime")
	public CommonResult updateOrderCreateTime() {
		return waybillService.updateOrderCreateTime();
	}

	/**
	 * 收款人列表
	 */
	@ApiOperation(value = "处理运单updateLoadTime", notes = "处理运单updateLoadTime")
	@PostMapping("/updateLoadTime")
	public CommonResult updateLoadTime() {
		return waybillService.updateLoadTime();
	}

	/**
	 * 重新计算运费
	 */
	@ApiOperation(value = "计算运费--4.4.16--shw", notes = "计算运费--4.4.16--shw")
	@PostMapping("/countPayFare")
	public CommonResult<BigDecimal> countPayFare(@RequestBody ConfirmReceiptReq req) {
		return waybillService.countPayFare(req);
	}

	/**
	 * 确认签收
	 */
	@ApiOperation(value = "确认签收--4.4.16--shw", notes = "确认签收--4.4.16--shw")
	@PostMapping("/confirmReceipt")
	public CommonResult confirmReceipt(@RequestBody ConfirmReceiptReq req) {
		return waybillService.confirmReceipt(req);
	}

	/**
	 * 确认签收详情
	 */
	@ApiOperation(value = "确认签收详情--4.4.16--shw", notes = "确认签收详情--4.4.16--shw")
	@PostMapping("/confirmReceipt/{id}")
	public CommonResult<ConfirmReceiptInfo> confirmReceiptInfo(@PathVariable("id") Long id) {
		return waybillService.confirmReceiptInfo(id);
	}

	/**
	 * 记录用户公路运单搜索项查询记录
	 *
	 * @return
	 */
	@ApiOperation(value = "运单管理保存顶部搜索项--4.5.5--ysy", notes = "运单管理保存顶部搜索项--4.5.5--ysy")
	@RequestMapping(value = "/saveWaybillSearchScreen", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult<?> saveWaybillSearchScreen(@RequestBody ScreenReq screen) {
		// logger.debug("保存运单筛选列，请求参数：{}", JSON.toJSONString(screen));
		try {
			String items = URLDecoder.decode(screen.getScreen(), StandardCharsets.UTF_8.name());
			Integer type = screen.getType();
			// 将用户筛选记录保存
			if (type == 1) {
				redisCache.setCacheObject(SecurityUtils.getUserId() + "billSearchScreen", items);
			} else if (type == 2) {
				redisCache.setCacheObject(SecurityUtils.getUserId() + "billSearchLoadingScreen", items);
			} else if (type == 3) {
				redisCache.setCacheObject(SecurityUtils.getUserId() + "billSearchUnloadScreen", items);
			} else if (type == 4) {
				redisCache.setCacheObject(SecurityUtils.getUserId() + "billSearchUnreceiptScreen", items);
			} else if (type == 5) {
				redisCache.setCacheObject(SecurityUtils.getUserId() + "billSearchReceiptScreen", items);
			}

			return CommonResult.success(null, "保存成功");
		} catch (Exception e) {
			logger.error("保存搜索记录出错--" + ExceptionUtils.getStackTrace(e));
			return CommonResult.error("保存搜索记录出错");
		}
	}

	/**
	 * 查询用户公路运单筛选列查询记录
	 *
	 * @return
	 */
	@ApiOperation(value = "查询运单顶部搜索项--4.5.5--ysy", notes = "查询运单顶部搜索项--4.5.5--ysy")
	@RequestMapping(value = "/getSearchScreen/{type}", method = RequestMethod.GET)
	@ResponseBody
	public CommonResult<String> getSearchScreen(@PathVariable("type") Integer type) {
		try {
			// 获取用户公路运单列表查询记录
			String record = "";
			if (type == 1) {
				record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billSearchScreen");
			} else if (type == 2) {
				record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billSearchLoadingScreen");
			} else if (type == 3) {
				record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billSearchUnloadScreen");
			} else if (type == 4) {
				record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billSearchUnreceiptScreen");
			} else if (type == 5) {
				record = redisCache.getCacheObject(SecurityUtils.getUserId() + "billSearchReceiptScreen");
			}

			logger.debug("获取运单搜索项，{}", record);
			return CommonResult.success(record);
		} catch (Exception e) {
			logger.error("获取搜索记录出错--" + ExceptionUtils.getStackTrace(e));
			return CommonResult.error("获取搜索记录失败");
		}
	}

	/**
	 * 记录用户申请付款,付款审核搜索项
	 *
	 * @return
	 */
	@ApiOperation(value = "记录用户申请付款，付款审核搜索项-4.5.5-ysy", notes = "记录用户申请付款，付款审核搜索项-4.5.5-ysy")
	@RequestMapping(value = "/saveApplyFareSerachScreen", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult<?> saveApplyFareSerachScreen(@RequestBody ScreenReq screen) {
		// logger.debug("保存运单筛选列，请求参数：{}", JSON.toJSONString(screen));
		try {
			String items = URLDecoder.decode(screen.getScreen(), StandardCharsets.UTF_8.name());
			Integer type = screen.getType();
			logger.debug("保存申请付款筛选列，items：{}", items);
			// 将用户筛选记录保存s
			if (type == 1) {// 付款申请（待申请）
				redisCache.setCacheObject(SecurityUtils.getUserId() + "applyFareSerachScreen", items);
			} else if (type == 2) {// 付款申请（已申请）
				redisCache.setCacheObject(SecurityUtils.getUserId() + "applyFareAlreadySerachScreen", items);
			} else if (type == 3) {// 付款审核
				redisCache.setCacheObject(SecurityUtils.getUserId() + "applyFareAuditSerachScreen", items);
			} else if (type == 4) {// 确认付款
				redisCache.setCacheObject(SecurityUtils.getUserId() + "applyFareconfirmSerachScreen", items);
			}
			return CommonResult.success(null, "保存成功");
		} catch (Exception e) {
			logger.error("保存搜索记录出错--" + ExceptionUtils.getStackTrace(e));
			return CommonResult.error("保存搜索记录出错");
		}
	}

	/**
	 * 查询用户申请付款筛选列查询记录
	 *
	 * @return
	 */
	@ApiOperation(value = "查询用户申请付款，付款审核搜索项-4.5.5-ysy", notes = "查询用户申请付款，付款审核搜索项-4.5.5-ysy")
	@RequestMapping(value = "/getApplyFareSearchScreen/{type}", method = RequestMethod.GET)
	@ResponseBody
	public CommonResult<String> getApplyFareSearchScreen(@PathVariable("type") Integer type) {
		try {
			// 获取用户公路运单列表查询记录
			String record = "";
			if (type == 1) {
				record = redisCache.getCacheObject(SecurityUtils.getUserId() + "applyFareSerachScreen");
			} else if (type == 2) {
				record = redisCache.getCacheObject(SecurityUtils.getUserId() + "applyFareAlreadySerachScreen");
			} else if (type == 3) {
				record = redisCache.getCacheObject(SecurityUtils.getUserId() + "applyFareAuditSerachScreen");
			} else if (type == 4) {
				record = redisCache.getCacheObject(SecurityUtils.getUserId() + "applyFareconfirmSerachScreen");
			}
			logger.debug("获取搜索项，{}", record);
			return CommonResult.success(record);
		} catch (Exception e) {
			logger.error("获取搜索项记录出错--" + ExceptionUtils.getStackTrace(e));
			return CommonResult.error("获取搜索记录失败");
		}
	}

	/**
	 * 根据运单编号查询运单信息（API查询使用）
	 *
	 * @param shippingNoteNumbers
	 * @return
	 */
	@ApiOperation(value = "根据运单编号批量查询运单信息（API查询使用）", notes = "根据运单编号批量查询运单信息")
	@RequestMapping(value = "/getWaybillInfoByCodes", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult<List<Waybill>> getWaybillInfoByCodes(@RequestBody List<String> shippingNoteNumbers) {
		if (StringUtils.isEmpty(shippingNoteNumbers)) {
			return CommonResult.error("运单号不能为空！");
		}
		List<Waybill> waybillList = waybillService.getWaybillInfoByCodes(shippingNoteNumbers);
		if (StringUtils.isEmpty(waybillList)) {
			return CommonResult.error("未查询到运单");
		}
		return CommonResult.success(waybillList);
	}

	/**
	 * 网络货运人端-运单审核查询-列表
	 *
	 * @param waybillReq
	 * @return
	 */
	@ApiOperation(value = "网络货运人端运单审核查询列表---4.5.7cys", notes = "网络货运人端运单审核查询列表---4.5.7cys")
	@PostMapping("/freightForwarder/audit/waybillList")
	public TableInfo<WaybillAuditListRes> freightForwarderWaybillAuditList(@RequestBody WaybillReq waybillReq, HttpServletRequest request) {
		if (!ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
			return TableInfo.forbidden("网络货运人端功能，您没有权限使用该接口");
		}
		BeanUtils.beanAttributeValueTrim(waybillReq);// 去掉该对象中string字段前后空格
		if (StringUtils.isNotNull(waybillReq.getStatus()) && waybillReq.getStatus() == BusinessConstants.WAYBILL_STATUS_DELETE) {
			return TableInfo.requestError("参数错误");
		}
		Date start = DateUtils.strToDate(waybillReq.getBeginCreateTime());
		Date end = DateUtils.strToDate(waybillReq.getEndCreateTime());
		if (null == start || null == end || DateUtils.addYears(start, 1).before(end)) {
			return TableInfo.requestError("请指定运单创建时间，跨度不能超过1年");
		}
		Long freightForwarderId = SecurityUtils.getFreightForwarderId();
		if (null == freightForwarderId) {
			return TableInfo.error("登录用户错误");
		}
		// 获取登录的网络货运人信息
		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(freightForwarderId);
		if (freightForwarderInfo == null || freightForwarderInfo.getCustomerId() == null || freightForwarderInfo.getCustomerId() == 0) {
			return TableInfo.error("登录用户错误");
		}

		waybillReq.setFreightForwarderId(freightForwarderId);

		return getTableData(waybillService.freightForwarderWaybillAuditList(waybillReq, request));
	}

	/**
	 * 网络货运人端-运单审核记录
	 *
	 * @param
	 * @return
	 */
	@ApiOperation(value = "网络货运人端运单审核记录---4.5.7cys", notes = "网络货运人端运单审核记录---4.5.7cys")
	@GetMapping("/freightForwarder/audit/waybillInfo/{id}")
	public CommonResult<List<WaybillAuditRes>> freightForwarderWaybillAuditRecord(@PathVariable Long id) {
		if (!ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
			return CommonResult.error("网络货运人端功能，您没有权限使用该接口");
		}
		return CommonResult.success(waybillService.freightForwarderWaybillAuditRecord(id));
	}

	/**
	 * 网络货运人端-运单审核
	 *
	 * @param
	 * @return
	 */
	@ApiOperation(value = "网络货运人端运单审核---4.5.7cys", notes = "网络货运人端运单审核---4.5.7cys")
	@PostMapping("/freightForwarder/audit/waybill")
	public CommonResult<?> freightForwarderWaybillAudit(@RequestBody WaybillAuditReq req) {
		if (!ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
			return CommonResult.error("网络货运人端功能，您没有权限使用该接口");
		}

		return waybillService.freightForwarderWaybillAudit(req);
	}

	/**
	 * 网络货运人端-撤销审核
	 *
	 * @param
	 * @return
	 */
	@ApiOperation(value = "网络货运人端撤销审核---4.5.9---ysy", notes = "网络货运人端撤销审核---4.5.9---ysy")
	@PostMapping("/freightForwarder/waybillAuditQuash")
	public CommonResult<?> waybillAuditQuash(@RequestBody WaybillAuditReq req) {
		if (!ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
			return CommonResult.error("网络货运人端功能，您没有权限使用该接口");
		}
		return waybillService.waybillAuditQuash(req);
	}

	/**
	 * 托运人端-运输风控
	 *
	 * @param
	 * @return
	 */
	@ApiOperation(value = "托运人端运输风控---4.5.7cys", notes = "托运人端运输风控---4.5.7cys")
	@PostMapping("/tenant/transportRiskList")
	public TableInfo<WaybillTransportRiskRes> tenantTransportRiskList(@RequestBody WaybillTransportRiskReq req) {
		// 校验托运人项目分组权限
		this.addContractIds(req);
		startPage();
		return getTableData(waybillService.tenantTransportRiskList(req));
	}

	/**
	 * 托运人端-运输风控-提交审核
	 *
	 * @param
	 * @return
	 */
	@ApiOperation(value = "托运人端运输风控提交审核---4.5.7cys", notes = "托运人端运输风控提交审核---4.5.7cys")
	@PostMapping("/tenant/submitWaybillAudit")
	public CommonResult<?> tenantSubmitWaybillAudit(@RequestBody WaybillTransportRiskReq req) {

		return waybillService.tenantSubmitWaybillAudit(req);
	}

	/**
	 * 托运人实时调度生成订单
	 *
	 * @param dispatchReqList
	 * @return
	 */
	@ApiOperation(value = "托运人实时调度生成订单--v4.5.9--ysy", notes = "托运人实时调度生成订单--v4.5.9--ysy")
	@Log(title = "运单", businessType = BusinessType.INSERT)
	@PostMapping(value = "/shipper/addDispatchOrder")
	public CommonResult addDispatchOrder(@RequestBody List<DispatchReq> dispatchReqList) {
		String redisKey = Constants.PC_CACHE + "waybill_shipper_" + SecurityUtils.getUserId();
		if (redisCache.hasKey(redisKey)) {
			return CommonResult.error("新建订单请求已提交，目前处理中，请勿重复提交");
		}
		// 信息到redis中，保留10s,避免重复提交
		redisCache.setCacheObject(redisKey, 1, 10, TimeUnit.SECONDS);

		if (!ClientType.SHIPPER.equals(SecurityUtils.getClientType())) {
			return CommonResult.error("托运人实时调度功能，您无权访问");
		}
		CommonResult commonResult = waybillService.addDispatchOrder(dispatchReqList);
		// 解锁
		redisCache.deleteObject(redisKey);
		return commonResult;
	}

	/**
	 * 托运人端订单列表查询
	 */
	@ApiOperation(value = "托运人端订单列表查询--v4.5.9--ysy", notes = "托运人端订单列表查询--v4.5.9--ysy")
	@PreAuthorize("@ss.hasPermi('transport:waybill:list')")
	@PostMapping("/customer/orderList")
	public TableInfo<WaybillTableRes> orderList(@RequestBody OrderQuery orderQuery, HttpServletRequest request) {
		logger.info("托运人运单列表，请求参数：" + JSON.toJSONString(orderQuery));
		if (orderQuery == null) {
			return TableInfo.requestError("参数错误");
		}
		if (!ClientType.SHIPPER.equals(SecurityUtils.getClientType())) {
			return TableInfo.forbidden("托运人端功能，您没有权限使用该接口");
		}
		orderQuery.setCustomerId(SecurityUtils.getShipperId());
		orderQuery.setPageSize(Integer.parseInt(request.getParameterMap().get("pageSize")[0]));
		orderQuery.setOffSet((Integer.parseInt(request.getParameterMap().get("pageNum")[0]) - 1) * orderQuery.getPageSize());
		orderQuery.setPageNum(null); // 防止分页插件自动分页

		// 校验托运人项目分组权限
		this.addContractIds(orderQuery);

		return waybillService.selectOrderList(orderQuery);
	}

	/**
	 * 删除订单
	 */
	@ApiOperation(value = "删除订单", notes = "删除订单")
	@Log(title = "运单", businessType = BusinessType.DELETE)
	@PostMapping("/removeOrder")
	public AjaxResult removeOrder(@RequestParam Long[] ids) {
		logger.info("托运人删除订单，请求参数：{}", JSON.toJSONString(ids));
		return waybillService.removeOrderByIds(ids);
	}

	/**
	 * 托运人订单生成运单
	 *
	 * @param req
	 * @return
	 */
	@ApiOperation(value = "托运人订单生成运单---4.5.9---ysy", notes = "托运人订单生成运单---4.5.9---ysy")
	@Log(title = "运单", businessType = BusinessType.INSERT)
	@PostMapping(value = "/shipper/orderToWaybill")
	public CommonResult orderToWaybill(@RequestBody WaybillUpdateRequest req) {
		if (StringUtils.isNotEmpty(req.getUuid())) {
			String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(req.getUuid(), "");
			String captcha = redisCache.getCacheObject(verifyKey);
			if (captcha == null) {
				return CommonResult.error("验证码已失效");
			}
			if (!req.getPicCode().equalsIgnoreCase(captcha)) {
				return CommonResult.error("验证码错误");
			}
		}
		String redisKey = Constants.PC_CACHE + "waybill_shipper_" + SecurityUtils.getUserId();
		if (redisCache.hasKey(redisKey)) {
			return CommonResult.error("生成运单请求已提交，目前处理中，请勿重复提交");
		}
		// 信息到redis中，保留10s,避免重复提交
		redisCache.setCacheObject(redisKey, 1, 10, TimeUnit.SECONDS);

		if (!ClientType.SHIPPER.equals(SecurityUtils.getClientType())) {
			return CommonResult.error("托运人订单生成运单功能，您无权访问");
		}
		CommonResult commonResult = waybillService.orderToWaybill(req);
		// 解锁
		redisCache.deleteObject(redisKey);
		return commonResult;
	}

	/**
	 * 获取订单详细信息
	 */
	@ApiOperation(value = "获取订单详细信息--v4.5.9--ysy", notes = "获取订单详细信息，")
	@GetMapping(value = "/order/{id}")
	public CommonResult<WaybillRes> orderDetail(@PathVariable("id") Long id) {
		return waybillService.getOrderById(id);
	}

	/**
	 * 托运人订单生成运单前置校验
	 *
	 * @param id
	 * @return
	 */
	@ApiOperation(value = "托运人订单生成运单前置校验---4.5.9---ysy", notes = "托运人订单生成运单前置校验---4.5.9---ysy")
	@GetMapping(value = "/shipper/orderToWaybillCheck/{id}")
	public CommonResult orderToWaybillCheck(@PathVariable("id") Long id) {
		CommonResult commonResult = waybillService.orderToWaybillCheck(id);
		return commonResult;
	}

	/**
	 * 获取运输合同
	 */
	@ApiOperation(value = "获取运输合同--v4.5.9shuiwu--ysy", notes = "获取运输合同--v4.5.9shuiwu--ysy")
	@GetMapping(value = "transportationAgreement/{id}")
	public CommonResult<WaybillRes> selectTransportationAgreement(@PathVariable("id") Long id) {
		return waybillService.getTransportationAgreement(id);
	}

	/**
	 * 获取运输合同
	 */
	@ApiOperation(value = "运输合同是否已上传--v4.5.12--shw", notes = "运输合同是否已上传--v4.5.12--shw")
	@GetMapping(value = "isUploadFile/{type}/{id}")
	public CommonResult isUploadFile(@PathVariable("type") Integer type, @PathVariable("id") Long id) {
		return waybillService.isUploadFile(id, type);
	}


	/**
	 * 升平获取运单信息
	 */
	@PostMapping(value = "getSPWaybillInfo")
	public CommonResult getSPWaybillInfo(@RequestBody SPReq req) {
		return waybillService.waybillExport(req);
	}

	/**
	 * 升平获取付款信息
	 */
	@PostMapping(value = "getSPPayInfo")
	public CommonResult getSPPayInfo(@RequestBody SPReq req) {
		return waybillService.payExport(req);
	}
}
