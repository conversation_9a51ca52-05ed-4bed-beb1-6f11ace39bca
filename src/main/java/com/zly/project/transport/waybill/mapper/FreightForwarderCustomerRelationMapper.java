package com.zly.project.transport.waybill.mapper;

import java.util.HashMap;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.zly.project.freightforwarder.domain.FreightForwarderInfo;
import com.zly.project.transport.waybill.domain.FreightForwarderCustomerRelation;

/**
 * 网络货运人托运人关联Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-07
 */
public interface FreightForwarderCustomerRelationMapper
{
    /**
     * 查询网络货运人托运人关联
     *
     * @param customerId 网络货运人托运人关联主键
     * @return 网络货运人托运人关联
     */
    public FreightForwarderCustomerRelation selectFreightForwarderCustomerRelationByCustomerId(Long customerId);

    /**
     * 查询网络货运人托运人关联列表
     *
     * @param freightForwarderCustomerRelation 网络货运人托运人关联
     * @return 网络货运人托运人关联集合
     */
    public List<FreightForwarderCustomerRelation> selectFreightForwarderCustomerRelationList(FreightForwarderCustomerRelation freightForwarderCustomerRelation);

    /**
     * 新增网络货运人托运人关联
     *
     * @param freightForwarderCustomerRelation 网络货运人托运人关联
     * @return 结果
     */
    public int insertFreightForwarderCustomerRelation(FreightForwarderCustomerRelation freightForwarderCustomerRelation);

    /**
     * 修改网络货运人托运人关联
     *
     * @param freightForwarderCustomerRelation 网络货运人托运人关联
     * @return 结果
     */
    public int updateFreightForwarderCustomerRelation(FreightForwarderCustomerRelation freightForwarderCustomerRelation);

    /**
     * 删除网络货运人托运人关联
     *
     * @param customerId 网络货运人托运人关联主键
     * @return 结果
     */
    public int deleteFreightForwarderCustomerRelationByCustomerId(Long customerId);

    /**
     * 批量删除网络货运人托运人关联
     *
     * @param customerIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFreightForwarderCustomerRelationByCustomerIds(Long[] customerIds);

    List<Long> selectCustomerIdsByForwarderId(Long forwarderId);


    void mergeShippers(@Param("fromId")Long fromId,@Param("toId") Long toId);

    List<Long> queryRepeatInfo(@Param("fromId")Long fromId,@Param("toId") Long toId);

    void delRepeatInfo(@Param("delIds")List<Long> delIds, @Param("fromId")Long fromId);

	List<FreightForwarderInfo> selectForwarderInfoByCustomerId(@Param("shipperId")Long shipperId,@Param("freightForwarderIdList")List<Long> freightForwarderIdList);

	List<FreightForwarderCustomerRelation> selectFreightForwarderCustomerRelationListByMap(HashMap<String, Object> map);

	List<FreightForwarderCustomerRelation> selectByCustomerIdsAndForwarderId(List<Long> customerIds, Long forwarderId);

}
