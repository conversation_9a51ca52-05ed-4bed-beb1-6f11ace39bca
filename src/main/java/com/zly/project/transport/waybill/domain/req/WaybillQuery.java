package com.zly.project.transport.waybill.domain.req;

import com.zly.framework.web.domain.QueryCommon;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Title WaybillQuery
 * @Description
 * @Create 2023-03-31 19:01
 * <AUTHOR>
 */
@Data
public class WaybillQuery extends QueryCommon {
	@ApiModelProperty("运单号")
	private String shippingNoteNumber;
	@ApiModelProperty("运单来源(1:PC客户端建单 2:微信快速录单 3:APP 4:小黑卡 5:合作方 6:微信小程序 7:批量导入)")
	private String resource;
	@ApiModelProperty("司机信息（姓名、手机号）")
	private String driverInfo;
	@ApiModelProperty("司机ID")
	private String driverId;
	@ApiModelProperty("车牌号码")
	private String vehicleNumber;
	@ApiModelProperty("收款信息（姓名、收款人卡号）")
	private String receiptInfo;
	@ApiModelProperty("收款人ID")
	private String payeeId;
	@ApiModelProperty("项目合约名称")
	private String frameworkContractName;
	/**
	 * 结算状态(0:未结算 1:部分结算 2:已结算)
	 */
	@ApiModelProperty("结算状态(0:未结算 1:部分结算 2:已结算)")
	private Integer settleStatus;
	/**
	 * 运输状态(0:未开始 1:在途中 2:已完成)
	 */
	@ApiModelProperty("运输状态(0:未开始 1:在途中 2:已完成)")
	private Integer transportStatus;
	/**
	 * 开票状态(0.未开票 1.部分开票 2.已开票 3.开票中 4.撤销开票 5.驳回开票)
	 */
	@ApiModelProperty("开票状态(0.未开票 1.部分开票 2.已开票 3.开票中 4.撤销开票 5.驳回开票)")
	private Integer billStatus;
	@ApiModelProperty("服务项ID列表")
	private List<Long> serviceIds;
	@ApiModelProperty("定位状态(-1:未设置(无定位) 0:未开始 1:定位中 2:定位完成 3:车辆未入网 4:定位异常)")
	private Integer locateStatus;
	@ApiModelProperty("单据日期")
	private String billDate;
	@ApiModelProperty("单据日期开始")
	private String billDateStart;
	@ApiModelProperty("单据日期结束")
	private String billDateEnd;
	/**
	 * 创建时间开始
	 */
	@ApiModelProperty("创建时间开始")
	private String createTimeStart;
	/**
	 * 创建时间结束
	 */

	@ApiModelProperty("审批状态  0待申请1审批中2审批通过")
	private Integer payApplyStatus;
	/**
	 * 创建时间结束
	 */
	@ApiModelProperty("创建时间结束")
	private String createTimeEnd;


	@ApiModelProperty("创建时间开始")
	private String orderCreateTimeStart;
	/**
	 * 创建时间结束
	 */
	@ApiModelProperty("创建时间结束")
	private String orderCreateTimeEnd;
	/**
	 * 付款时间开始
	 */
	/**
	 * 付款时间开始
	 */
	@ApiModelProperty("付款时间开始 ")
	private String payTimeStart;
	/**
	 * 付款时间结束
	 */
	@ApiModelProperty("付款时间结束")
	private String payTimeEnd;
	@ApiModelProperty("货物名称")
	private String descriptionOfGoods;
	@ApiModelProperty("确认回单状态(0已签收  1未签收)")
	private Integer confirmReceiptState;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 托运人ID
	 */
	private Long customerId;

	private Integer infoIsComplete;
	//货源id
	private Long makeCodeId;
	private String nameAlias;

	//电子合同状态(0 未签约  1待签约  2已签约)
	private Integer electronicContractState;

	//是否上传回单（0上传，1未上传）
	private Integer receiptStatus;

	private List<Long> makeCodeIds;

	private Integer frameworkContractState;

	@ApiModelProperty("收款人ids")
	private List<Long> payeeIds;

	private String receiptCard;
	private String payeeIdentityCard;
	private String receiptName;
	private String driverPhone;
	private String driverName;
	private String createBy;

	// 自定义导出的字段
	@ApiModelProperty("自定义导出的字段")
	private List<String> exportNameList;
	@ApiModelProperty("运单号列表")
	private List<String> shippingNoteNumbers;
	@ApiModelProperty("运单导出类型 1：正常导出 2：自定义导出")
	private Integer exportType;

	@ApiModelProperty("发票号码")
	private String invoiceNumber;
	@ApiModelProperty("发票开始时间")
	private String invoiceStartTime;
	@ApiModelProperty("发票结束时间")
	private String invoiceEndTime;
	private List<Long> waybillIds;
	private Long auditFreightForwarderId;
	private Long freightForwarderId;

	@ApiModelProperty("车队长id")
	private Long carCaptainId;
	@ApiModelProperty("车队长名称")
	private String carCaptainName;
	@ApiModelProperty("车队长手机号")
	private String carCaptainPhone;
	@ApiModelProperty("车队长身份证号")
	private String carCaptainIdentityCard;
	@ApiModelProperty("辅助员id")
	private Long auxiliaryStaffId;
	@ApiModelProperty("辅助员名称")
	private String auxiliaryStaffName;
	@ApiModelProperty("辅助员身份证号")
	private String auxiliaryStaffIdentityCard;
	@ApiModelProperty("辅助员手机号")
	private String auxiliaryStaffPhone;

	@ApiModelProperty("车队长信息")
	private String carCaptainInfo;
	@ApiModelProperty("辅助员信息")
	private String auxiliaryStaffInfo;

	@ApiModelProperty("预计装货时间开始")
	private String despatchActualDateTimeStart;
	@ApiModelProperty("预计装货时间结束")
	private String despatchActualDateTimeEnd;
	@ApiModelProperty("预计卸货时间开始")
	private String goodsReceiptDateTimeStart;
	@ApiModelProperty("预计卸货时间结束")
	private String goodsReceiptDateTimeEnd;

	@ApiModelProperty("实际装货时间开始")
	private String actualStartTimeStart;
	@ApiModelProperty("实际装货时间结束")
	private String actualStartTimeEnd;
	@ApiModelProperty("实际卸货时间开始")
	private String actualEndTimeStart;
	@ApiModelProperty("实际卸货时间结束")
	private String actualEndTimeEnd;

	@ApiModelProperty("发货地址")
	private String placeOfLoading;
	@ApiModelProperty("收货地址")
	private String goodsReceiptPlace;

	@ApiModelProperty("大宗单价范围起")
	private BigDecimal feePriceStart;
	@ApiModelProperty("大宗单价范围止")
	private BigDecimal feePriceEnd;
	@ApiModelProperty("运输协议签署状态")
	private Integer transportationAgreementState;
    @ApiModelProperty("发货单位")
    private String consignor;
    @ApiModelProperty("收货单位")
    private String consignee;

	// 托运人id
	private List<String> list;
}
