package com.zly.project.transport.waybill.domain.res;

import lombok.Data;

@Data
public class SPPayRes {

    //("运单号")
    private String shippingNoteNumber;

    //("项目名称")
    private String frameworkContractName;

    //("项目编号")
    private String frameworkContractCode;

    //("托运人名称")
    private String customerName;

    //("付款单号")
    private String applyPayCode;

    //("付款状态 3:已付款 4:付款失败 5:付款中")
    private String auditStatus;

    //("付款阶段（0、总费用；1、预付；2、到付、3回单付）")
    private String fareStage;

    //("付款金额")
    private String applyMoney;

    //("收款人")
    private String payeeName;

    //("银行卡号")
    private String bankCardNo;

    //("收款银行")
    private String bankName;

    //("身份证号")
    private String identityCard;

    //("付款时间")
    private String payTime;

    //("备注")
    private String waybillRemark;

    //("货源别名")
    private String demainName;

    //("运输状态")
    private String status;

    //("货物名称")
    private String goodsName;

    //("司机姓名")
    private String driverName;

    //("司机电话")
    private String telephone;

    //("车牌号")
    private String vehicleNumber;
}
