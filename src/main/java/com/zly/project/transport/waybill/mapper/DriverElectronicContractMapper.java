package com.zly.project.transport.waybill.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.zly.project.transport.waybill.domain.DriverElectronicContract;

/**
 * 司机与网络货运人大合同Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-19
 */
public interface DriverElectronicContractMapper
{
    /**
     * 查询司机与网络货运人大合同
     *
     * @param id 司机与网络货运人大合同主键
     * @return 司机与网络货运人大合同
     */
    public DriverElectronicContract selectDriverElectronicContractById(Long id);

    /**
     * 查询司机与网络货运人大合同列表
     *
     * @param driverElectronicContract 司机与网络货运人大合同
     * @return 司机与网络货运人大合同集合
     */
    public List<DriverElectronicContract> selectDriverElectronicContractList(DriverElectronicContract driverElectronicContract);

    /**
     * 新增司机与网络货运人大合同
     *
     * @param driverElectronicContract 司机与网络货运人大合同
     * @return 结果
     */
    public int insertDriverElectronicContract(DriverElectronicContract driverElectronicContract);

    /**
     * 修改司机与网络货运人大合同
     *
     * @param driverElectronicContract 司机与网络货运人大合同
     * @return 结果
     */
    public int updateDriverElectronicContract(DriverElectronicContract driverElectronicContract);

    /**
     * 删除司机与网络货运人大合同
     *
     * @param id 司机与网络货运人大合同主键
     * @return 结果
     */
    public int deleteDriverElectronicContractById(Long id);

    /**
     * 批量删除司机与网络货运人大合同
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDriverElectronicContractByIds(Long[] ids);

    DriverElectronicContract selectEffectiveDriverElectronicContractByFreightForwarderId(DriverElectronicContract driverElectronicContract);

    List<DriverElectronicContract> selectEffectiveDriverElectronicContract(DriverElectronicContract driverElectronicContract);

	List<DriverElectronicContract> queryByState(Integer state);

    void updateWillExpire(List<Long> ids);

	void updateExpire(List<Long> ids);

    void updateTake(List<Long> ids);

    List<DriverElectronicContract> queryBydriverIdCardAndFreightForwarderId(List<String> keysList, Long freightForwarderId);

    List<DriverElectronicContract> queryBydriverIdCardAndFreightForwarderIdAndState(List<String> keysList, Long freightForwarderId, int i);

    void updateStateByIds(Integer state, List<Long> ids);

    void insertDriverElectronicContractList(List<DriverElectronicContract> insDriverElectronicContractTwoList);

	List<DriverElectronicContract> selectEffectiveDriverElectronicContractByFreightForwarderIds(@Param("driverIdentityCard") String driverIdentityCard,@Param("freightForwarderIdList") List<Long> freightForwarderIds);

	List<DriverElectronicContract> selectByDrivingLicensesAndForwarderId(List<String> drivingLicenses, Long freightForwarderId);

}
