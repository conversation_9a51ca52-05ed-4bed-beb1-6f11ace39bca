package com.zly.project.transport.waybill.mapper;

import com.zly.project.carrier.auxiliary.domain.req.AuxiliaryStaffReq;
import com.zly.project.carrier.carCaptain.domain.req.CarCaptainReq;
import com.zly.project.carrier.carrier.domain.ActualCarrierInfo;
import com.zly.project.carrier.driver.domain.Driver;
import com.zly.project.carrier.payee.domain.PayeeInfo;
import com.zly.project.carrier.vehicle.domain.Vehicle;
import com.zly.project.transport.waybill.domain.Waybill;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 运单Mapper接口
 *
 * <AUTHOR>
 * @date 2021-11-12
 */
@Repository
public interface WaybillMapper {
	/**
	 * 查询运单
	 *
	 * @param id
	 *            运单主键
	 * @return 运单
	 */
	Waybill selectWaybillById(Long id);

	Waybill selectWaybillAllFieldsById(Long id);

	/**
	 * 查询运单列表
	 *
	 * @param waybill
	 *            运单
	 * @return 运单集合
	 */
	List<Waybill> selectWaybillList(Waybill waybill);

	List<Long> selectWaybillIds(Waybill waybill);

	/**
	 * 新增运单
	 *
	 * @param waybill
	 *            运单
	 * @return 结果
	 */
	int insertWaybill(Waybill waybill);

	/**
	 * 修改运单
	 *
	 * @param waybill
	 *            运单
	 * @return 结果
	 */
	int updateWaybill(Waybill waybill);

	/**
	 * 删除运单
	 *
	 * @param id
	 *            运单主键
	 * @return 结果
	 */
	int deleteWaybillById(Long id);

	/**
	 * 通过运单主键删除运单子表信息
	 *
	 * @param id
	 *            运单ID
	 * @return 结果
	 */
	int deleteWaybillSlaveByWaybillId(Long id);

	Long selectWaybillByAttribute();

	/**
	 * 查询某项目下，尚未走到付款审核结束的运单id列表
	 *
	 * @param
	 * @return 运单集合
	 */
	List<Long> selectNotPayApproveWaybillIdsByContractId(Long contractId);

	List<Long> selectNotPayWaybillIdsByDriverIdAndCustomerId(String oldDrivingLicense, Long customerId);

	List<Long> selectNotPayWaybillIdsByPayeeIdentityCardAndCustomerId(String oldDrivingLicense, Long customerId);

	List<Long> selectNotPayWaybillIdsByAuxiliaryIdAndCustomerId(String oldIdentityCard, Long customerId);

	List<Long> selectNotPayWaybillIdsByCaptainIdAndCustomerId(String oldIdentityCard, Long customerId);

	List<Long> selectNotPayWaybillIdsByCarrierIdentityCardAndCustomerId(List<String> identityCards, Long customerId);

	List<Long> selectNotPayWaybillIdsByPayeeIdAndCustomerId(List<String> bankCardNos, Long customerId);

	List<Long> selectNotPayWaybillIdsByVehicleNumberAndCustomerId(String vehicleNumber, Long customerId);

	List<Long> selectNotPayWaybillIdsByVehicleIdAndCustomerId(Long vehicleId, Long customerId);

	int updateWaybillByIdsAndDriverInfo(@Param("ids") List<Long> waybillIds, @Param("driver") Driver driver, @Param("nickname") String nickname);

	int updateWaybillByIdsAndAuxiliaryInfo(@Param("ids") List<Long> waybillIds, @Param("auxiliaryStaffInfo") AuxiliaryStaffReq auxiliaryStaffInfo, @Param("nickname") String nickname);

	int updateWaybillByIdsAndCarCaptainInfo(@Param("ids") List<Long> waybillIds, @Param("carCaptainInfo") CarCaptainReq carCaptainInfo, @Param("nickname") String nickname);

	int updateWaybillByIdsAndCarrierInfo(@Param("ids") List<Long> waybillIds, @Param("carrier") ActualCarrierInfo carrierInfo, @Param("nickname") String nickname);

	int updateWaybillByIdsAndVehicleInfo(@Param("ids") List<Long> waybillIds, @Param("vehicle") Vehicle vehicle, @Param("nickname") String nickname);

	List<Waybill> selectListLimit(@Param("offset") Integer offset, @Param("pageSize") Integer pageSize);

	int updateWaybillDriverId(List<Long> oldDriverIds, Long newDriverId);

	int countAll();

	List<Waybill> selectWaybillByIds(List<Long> ids);

	// 处理代运营运单车辆与托运人没有关联关系
	List<Waybill> selectHandleNonAssociatedTransporterOperation();

	int updateWaybillVehicleId(List<Long> oldVehicleIds, Long newVehicleId);

	int updateWaybillCarrierId(List<Long> oldCarrierIds, Long newCarrierId);

	int updateWaybillPayeeId(List<Long> oldPayeeIds, Long newPayeeId);

	int updateWaybillByIdsAndPayeeInfo(List<Long> waybillIds, PayeeInfo payee, String updateBy);

	List<Long> selectShippingNoteNumberList(List<String> waybillNos);

	List<Waybill> selectWaybillByAuxiliaryIdentityCards(List<String> identityCards);

	List<Waybill> selectWaybillByPayeeType(@Param("payeeType") int type, @Param("id") Long freightForwarderId);

	List<Waybill> selectWaybillListOld(Waybill waybillSearch);

	int insertWaybills(List<Waybill> waybills);

	List<Waybill> selectWaybillByCaptainIdentityCards(List<String> identityCards);

	int deleteWaybillByIds(List<Long> waybillIds);

	List<Waybill> selectShanDaoWaybillByIds(List<Long> ids);

	List<Long> selectRiskCheckFailWaybillIds(Waybill waybillSearch);

	/**
	 * 根据运单 id逻辑删除运单号。
	 *
	 * @param waybillIds
	 */
	void updateWaybillStatusForDeleteByIds(List<Long> waybillIds);

	int updateWaybillRiskGradeByWaybillIdsAndGrade(List<Long> waybillIds, Integer grade);

	List<String> selectConflictWaybillByDriverIdAndTime(Long waybillId, Long driverId, Date despatchActualDateTime, Date goodsReceiptDateTime, Long freightForwarderId);

	List<String> selectConflictWaybillByVehicleIdAndTime(Long waybillId, Long vehicleId, Date despatchActualDateTime, Date goodsReceiptDateTime, Long freightForwarderId);

	int updateWaybillStatementReceiveFareAndBillStatusByWaybillIds(List<Waybill> waybills, Integer billStatus, List<Long> waybillIds);

	List<Long> selectWaybillIdsByQueryWrapper(Waybill waybill);

	int updateWaybillTransportationAgreementState(List<Long> waybillIds);

	List<Long> selectWaybillIdsByAuxiliaryStaffIdentityCard(Long freightForwarderId, List<String> identityCards);

	int updateWXOrdersCarrierInfo(ActualCarrierInfo carrierInfo);

	List<Long> selectNeedChangeFamilyPayeeWaybillIdsByDriver(String drivingLicense, List<Long> freightForwarderIds);

	List<Waybill> selectNeedChangeFamilyPayeeFareByDriver(String drivingLicense, List<Long> freightForwarderIds);

	List<Waybill> selectNeedChangeFamilyPayeeOriginalPayeeFareByDriver(String drivingLicense, List<Long> freightForwarderIds);

	List<Long> selectUnBindWaybillVerify(String identityCard);

	int updateFillWxPayeeInfoByPayeeIdentityCard(PayeeInfo newPayeeInfo);

	List<Long> selectFillWxPayeeInfoByPayeeIdentityCard(String payeeIdentityCard);

	int updateWXWaybill(ActualCarrierInfo carrierInfo);

	int updatePayeeTypeByIds(List<Long> waybillIds, int payeeType);

	int disposeWaybillPayeeIdentityCard(@Param("customerId") Long customerId);

	List<Long> selectCustomerInfoByWaybillIds(@Param("waybillIds") List<Long> waybillIds);

	List<Long> selectUnBindWaybillOrder(String identityCard);

}
