package com.zly.project.transport.waybill.mapper;

import com.zly.project.transport.waybill.domain.WaybillUploadTransportation;
import com.zly.project.transport.waybill.domain.req.TransportionProtocolReq;
import com.zly.project.transport.waybill.domain.res.TransportionProtocolRes;

import java.util.List;

/**
 * 上传运输协议附件审核Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
public interface WaybillUploadTransportationMapper {
    /**
     * 查询上传运输协议附件审核
     *
     * @param id
     * 		上传运输协议附件审核主键
     * @return 上传运输协议附件审核
     */
    public WaybillUploadTransportation selectWaybillUploadTransportationById(Long id);

    /**
     * 查询上传运输协议附件审核列表
     *
     * @param waybillUploadTransportation
     * 		上传运输协议附件审核
     * @return 上传运输协议附件审核集合
     */
    public List<WaybillUploadTransportation> selectWaybillUploadTransportationList(WaybillUploadTransportation waybillUploadTransportation);

    /**
     * 新增上传运输协议附件审核
     *
     * @param waybillUploadTransportation
     * 		上传运输协议附件审核
     * @return 结果
     */
    public int insertWaybillUploadTransportation(WaybillUploadTransportation waybillUploadTransportation);

    /**
     * 修改上传运输协议附件审核
     *
     * @param waybillUploadTransportation
     * 		上传运输协议附件审核
     * @return 结果
     */
    public int updateWaybillUploadTransportation(WaybillUploadTransportation waybillUploadTransportation);

    /**
     * 删除上传运输协议附件审核
     *
     * @param id
     * 		上传运输协议附件审核主键
     * @return 结果
     */
    public int deleteWaybillUploadTransportationById(Long id);

    /**
     * 批量删除上传运输协议附件审核
     *
     * @param ids
     * 		需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWaybillUploadTransportationByIds(Long[] ids);

    List<TransportionProtocolRes> transportationProtocolList(TransportionProtocolReq req);

    Integer transportationProtocolCount(Long freightForwarderId);

    void deleteOld(Long waybillId);

    List<WaybillUploadTransportation> selectJoinAttachmentsByWaybillIds(List<Long> waybillIds, List<String> fileNames);

    void auditFile(Long freightForwarderId);

    List<Long> auditWaybillIds(Long freightForwarderId);
}
