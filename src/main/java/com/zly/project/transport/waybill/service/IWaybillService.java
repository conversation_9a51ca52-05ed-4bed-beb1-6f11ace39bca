package com.zly.project.transport.waybill.service;

import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.TableInfo;
import com.zly.project.carrier.driver.domain.Driver;
import com.zly.project.financeOperation.domain.req.FinancingWaybillQueryReq;
import com.zly.project.financeOperation.domain.req.ProjectOptionReq;
import com.zly.project.financeOperation.domain.req.RiskControllerReq;
import com.zly.project.financeOperation.domain.res.FinancingWaybillRes;
import com.zly.project.financeOperation.domain.res.ProjectOptionRes;
import com.zly.project.financeOperation.domain.res.RiskControllerRes;
import com.zly.project.financeOperation.domain.res.StatementWaybillInfo;
import com.zly.project.settlement.domain.request.TradeFlowQueryReq;
import com.zly.project.transport.waybill.domain.ConfirmReceiptInfo;
import com.zly.project.transport.waybill.domain.Waybill;
import com.zly.project.transport.waybill.domain.WaybillTrackAbnormal;
import com.zly.project.transport.waybill.domain.req.*;
import com.zly.project.transport.waybill.domain.res.*;
import com.zly.project.wlhy.model.UploginkWaybillQuery;
import com.zly.project.wlhy.model.UploginkWaybillRes;
import com.zly.project.wlhy.model.WaybillUploginkInfo;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 运单Service接口
 *
 * <AUTHOR>
 * @date 2021-10-29
 */
public interface IWaybillService {
	/**
	 * 查询运单
	 *
	 * @param id
	 * 		运单主键
	 * @return 运单
	 */
	Waybill selectWaybillById(Long id);

	/**
	 * 查询运单列表
	 *
	 * @param waybill
	 * 		运单
	 * @return 运单集合
	 */
	List<Waybill> selectWaybillList(Waybill waybill);

	/**
	 * 新增运单
	 *
	 * @param waybill
	 * 		运单
	 * @return 结果
	 */
	int insertWaybill(Waybill waybill);

	/**
	 * 修改运单
	 *
	 * @param waybill
	 * 		运单
	 * @return 结果
	 */
	int updateWaybill(Waybill waybill);

	/**
	 * APP查询调度对应的运单列表
	 *
	 * @param waybill
	 * 		运单
	 * @return 运单集合
	 */
	TableInfo<WaybillWxRes> selectWaybillListFromApp(Waybill waybill);

	/**
	 * 查询运单列表(状态必须是'订单'或'运单')
	 */
	List<WaybillRes> waybillList(WaybillReq waybillReq);

	/**
	 * 新增运单信息
	 *
	 * @param req
	 * @return
	 */
	CommonResult<Waybill> addWaybill(WaybillReq req, String createBy);

	/**
	 * 查询运单详情
	 *
	 * @param id
	 * @return
	 */
	CommonResult<WaybillRes> getWaybillById(Long id);

	/**
	 * 批量删除运单信息
	 *
	 * @param ids
	 * 		需要删除的主键集合
	 * @return 结果
	 */
	AjaxResult removeWaybillByIds(Long[] ids);

	/**
	 * 查询各状态的运单数量
	 *
	 * @param status
	 * @return
	 */
	Integer getStatusCount(Integer status);

	/**
	 * 司机是否有运单进行中（未开始或运输中）
	 *
	 * @param driverId
	 * 		司机ID
	 * @return
	 */
	boolean driverInTransport(long driverId, Long customerId);

	/**
	 * 车辆是否有运单进行中（未开始或运输中）
	 *
	 * @param vehicleId
	 * 		车辆ID
	 * @return
	 */
	boolean vehicleInTransport(long vehicleId, Long customerId);

	/**
	 * 托运人我录的运单列表
	 *
	 * @param waybillQuery
	 * @return
	 */
	TableInfo<WaybillTableRes> selectShipperWaybillPageList(WaybillQuery waybillQuery);

	List<WaybillTableRes> selectShipperWaybillList(WaybillQuery waybillQuery);

	/**
	 * 批量提交
	 *
	 * @param ids
	 * 		需要提交的数据ID
	 * @return 结果
	 */
	AjaxResult submitWaybillByIds(Long[] ids);

	/**
	 * 查询要GPS实时定位的运单
	 *
	 * @return
	 */
	List<Waybill> getGpsPositioningWaybillList();

	/**
	 * 查询网络货运运单列表
	 *
	 * @param waybillQuery
	 * @return
	 */
	List<UploginkWaybillRes> uploginkWaybillList(UploginkWaybillQuery waybillQuery);

	/**
	 * 查询待上传的网络货运运单列表（上传服务使用）
	 *
	 * @param waybillQuery
	 * @return
	 */
	List<WaybillUploginkInfo> selectWaybillUploginkList(UploginkWaybillQuery waybillQuery);

	/**
	 * 查询上传成功的网络货运运单列表（补传资金流水单用）【非江苏省的运单】
	 *
	 * @param waybillQuery
	 * @return
	 */
	List<WaybillUploginkInfo> selectWaybillUploginkSuccessList(UploginkWaybillQuery waybillQuery);

	/**
	 * 运单实时定位
	 *
	 * @param waybillId
	 * @return
	 */
	void waybillPositioning(long waybillId);

	/**
	 * 查询运单物流详情
	 *
	 * @param id
	 * @param flag
	 * 		0:PC端 1:小程序货主端
	 * @return
	 */
	AjaxResult getWaybillLogisticsDetails(Long id, int flag);

	AjaxResult getWaybillLogisticsDetailsSD(Long id, int flag);

	/**
	 * 获取运单信息
	 *
	 * @param waybillCode
	 * @return
	 */
	Waybill selectWaybillByCode(String waybillCode);

	/**
	 * 网络货运人端-运单查询-列表
	 *
	 * @param waybillReq
	 * @return
	 */
	TableInfo<WaybillTableResOfFreightForwarder> selectFreightForwarderWaybillList(WaybillReq waybillReq, HttpServletRequest request);

	/**
	 * 获取转包运单列表
	 */
	List<WaybillRes> selectSubcontractWaybillList(WaybillReq waybillReq);

	/**
	 * 托运人待申请开票运单列表
	 *
	 * @param waybillReq
	 * @return
	 */
	List<WaybillRes> waybillListForStatement(WaybillReq waybillReq);

	/**
	 * 托运人新增运单
	 *
	 * @param waybillUpdate
	 * @return
	 */
	CommonResult shipperAddWaybill(WaybillUpdateRequest waybillUpdate);

	Integer getWaybillLocateType(Long contractId);

	Integer getWaybillLocateStatus(Date planStartTime, Date planEndTime);

	/**
	 * 确认收货
	 *
	 * @param waybillReq
	 * 		运单ID集合
	 * @return
	 */
	CommonResult<Object> shipperConfirmArrive(WaybillReq waybillReq);

	/**
	 * 获取每个阶段的费用
	 *
	 * @param waybillId
	 * @return
	 */
	AjaxResult selectUnSettlementWaybillInfoV2(Long waybillId);

	/**
	 * 获取每个阶段的费用
	 *
	 * @param waybill
	 * @return
	 */
	AjaxResult selectUnSettlementWaybillInfoV2ByWaybill(Waybill waybill);

	/**
	 * 批量插入运单表
	 *
	 * @param waybillList
	 * 		待插入运单列表
	 * @return
	 */
	int insertWaybillList(List<Waybill> waybillList);

	/**
	 * 查询某项目下，尚未走到付款审核结束的运单id列表
	 *
	 * @param
	 * @return 运单集合
	 */
	List<Long> selectNotPayApproveWaybillIdsByContractId(Long contractId);

	/**
	 * 批量更新运单表中的托运人信息
	 *
	 * @param customerId
	 * @param customerName
	 * @return
	 */
	int editCustomerInfoOfWaybillByCustomerId(Long customerId, String customerName);

	/**
	 * 批量更新运单表中的收款人信息
	 *
	 * @param payeeId
	 * @param payeeName
	 * @return
	 */
	int editPayeeInfoOfWaybillByPayeeId(Long payeeId, String payeeName);

	/**
	 * 批量更新运单表中的司机信息
	 *
	 * @param drvierId
	 * @param driverName
	 * @param driverPhone
	 * @return
	 */
	int editDriverInfoOfWaybillByDriverId(Long drvierId, String driverName, String driverPhone);

	/**
	 * 更改运单结算状态
	 *
	 * @param waybillReq
	 * @return
	 */
	int updateWaybillSettleStatus(WaybillReq waybillReq);

	/**
	 * 获取对账清单运单信息
	 *
	 * @param waybillIds
	 * 		运单ID
	 * @return
	 */
	List<WaybillRes> selectStatementWaybillList(List<Long> waybillIds);

	List<WaybillRes> selectStatementWaybillList(List<Long> waybillIds, Long shipperId);

	/**
	 * 获取运单列表
	 */
	List<Waybill> selectWaybillList(List<String> ShippingNoteNumbers);

	/**
	 * 批量更新运单关联的项目信息
	 *
	 * @param waybillIds
	 * 		运单ID集合
	 * @param contractId
	 * 		项目ID
	 * @param contractCode
	 * 		项目编号
	 * @param contractName
	 * 		项目名称
	 * @return
	 */
	int updateWaybillFrameContractByIds(List<Long> waybillIds, Long contractId, String contractCode, String contractName);

	List<Long> selectUnArriveWaybillByDriverid(Long driverId);

	int updateWayBillActualCarrierByCarrierIds(List<Long> carrierIds, Long carrierId, String name, String phone);

	void updateDriverInfoByDriverIds(List<Long> existIds, Driver driver);

	/**
	 * 金融服务系统签约主体托运人运单列表
	 *
	 * @param waybillQuery
	 * @return
	 */
	List<FinancingWaybillRes> selectSignorWaybillList(FinancingWaybillQueryReq waybillQuery);

	/**
	 * 金融服务系统待对账运单列表
	 *
	 * @param map
	 * @return
	 */
	List<StatementWaybillInfo> selectPreStatementWaybillList(Map<String, Object> map);

	List<RiskControllerRes> riskControllerList(RiskControllerReq req);

	/**
	 * 获取运单状态信息
	 *
	 * @param id
	 * 		运单ID
	 * @return
	 */
	CommonResult<WaybillEditableInfo> getWaybillStatusInfo(Long id);

	CommonResult getCompleteTrajectory(Long id);

	CommonResult getCompleteTrajectoryByTime(GpsTimeReq req);

	CommonResult<WaybillCopyRes> copyWaybill(Long id);

	List<Integer> queryEditingWaybill(Long id);

	CommonResult batchUpdateDriverInfoIsComplete(Long driverId, Integer infoIsComplete);

	CommonResult batchUpdateVehicleInfoIsComplete(Long vehicleId, Integer infoIsComplete);

	CommonResult batchUpdateActualCarrierInfoIsComplete(String identityCard, Integer infoIsComplete);

	CommonResult etcWaybillStart(WaybillReq req);

	CommonResult etcWaybillEnd(Waybill waybill);

	Waybill getWaybillInfoByCode(String shippingNoteNumber);

	List<ProjectOptionRes> selectProjectOptionList(ProjectOptionReq req);

	/**
	 * 对外服务查询运单列表
	 *
	 * @param waybillReq
	 * @return
	 */
	TableInfo<WaybillRes> selectAppWaybillList(WaybillReq waybillReq);

	List<WaybillRes> getWaybillPayeeInfo(WaybillReq waybillReq);

	/**
	 * 根据项目id统计未付款金额
	 */
	BigDecimal statisticsUnpaidFareByContractId(Long contractId, Long customerId);

	BigDecimal paymentReviewTotalAmountByContractId(WaybillReq waybillReq);

	BigDecimal statisticsApplyMoneyByContractId(TradeFlowQueryReq req);

	AjaxResult selectShipperWaybillSum(WaybillQuery waybillQuery);

	FinancingWaybillRes selectSignorWaybillSum(FinancingWaybillQueryReq waybillQuery);

	void handleData(Long customerId, Integer page);

	Integer queryUnPaidWaybillByChooseId(Map<String, Long> map);

	Integer updateWaybillContractByContractId(Long contractId, String contractName, String contractCode);

	void verifyTrajectory();

	CommonResult getCompleteTrajectoryTime(Long id);

	AjaxResult selectFreightForwarderWaybillSum(WaybillReq waybillReq);

	void sendUnloadMsg();

	Integer selectUnDeleteWaybills(Long contractId);

	CommonResult recalculateWaybillFare(WaybillReq waybillReq);

	CommonResult<List> payeeInfoList(PayeeReq req);

	/**
	 * 获取某个网络货运人的运单
	 *
	 * @param waybillQuery
	 * @return
	 */
	List<Waybill> selectNewWaybillList(UploginkWaybillQuery waybillQuery);

	CommonResult updateOrderCreateTime();

	CommonResult updateLoadTime();

	AjaxResult shipperWaybillListCount(WaybillQuery waybillQuery);

	CommonResult<BigDecimal> countPayFare(ConfirmReceiptReq req);

	BigDecimal countPayFareWithExtraFare(ConfirmReceiptReq req, Boolean containExtraFare);

	CommonResult confirmReceipt(ConfirmReceiptReq req);

	CommonResult<ConfirmReceiptInfo> confirmReceiptInfo(Long id);

	// 同步善道运单mq
	void pushWaybillReachShanDao(List<Long> waybillIds, Integer type);

	/**
	 * 查询到了时间但是还未发车的运单
	 *
	 * @return
	 */
	void todoConfirmDepartureWaybillList();

	/**
	 * 更新到达预计发车时间的运单为开始运输
	 *
	 * @param waybillReq
	 * @return
	 */
	int updateWaybillStatusForConfirmDeparture(WaybillReq waybillReq);

	/**
	 * 处理善道和4.0数据库运单状态不一致的运单，重新推送。
	 */
	void todoWaybillResynchronize();

	public CommonResult<WaybillTrackAbnormal> checkTrack(WaybillRes waybill, String nickName, Integer trajectoryDistance, Integer trajectoryTime);

	public void waybillConfirmArrival(List<Long> waybillIds);

	List<Waybill> getWaybillInfoByCodes(List<String> shippingNoteNumbers);

	/**
	 * 托运人待申请开票运单列表金额汇总
	 *
	 * @param waybillReq
	 * @return
	 */
	StatementMoneyRes statementMoneyTotal(WaybillReq waybillReq);

	CommonResult<Integer> waybillListForStatementCount(WaybillReq waybillReq);

	List<WaybillProxyInvoiceRes> proxyInvoiceWaybillList(WaybillReq waybillReq);

	CommonResult settingProxyInvoice(WaybillProxyInvoiceReq req);

	CommonResult<?> selectProxyInvoiceWaybillListSum(WaybillReq waybillReq);

	List<WaybillAuditListRes> freightForwarderWaybillAuditList(WaybillReq waybillReq, HttpServletRequest request);

	CommonResult<?> freightForwarderWaybillAudit(WaybillAuditReq req);

	CommonResult<?> waybillAuditQuash(WaybillAuditReq req);

	List<WaybillAuditRes> freightForwarderWaybillAuditRecord(Long id);

	List<WaybillTransportRiskRes> tenantTransportRiskList(WaybillTransportRiskReq req);

	CommonResult<?> tenantSubmitWaybillAudit(WaybillTransportRiskReq req);

	CommonResult<List> shandaoPayeeInfoList(PayeeReq req);

	CommonResult<Long> shandaoFrameworkContract(Long frameworkContractId);

	CommonResult addDispatchOrder(List<DispatchReq> dispatchReqList);

	TableInfo<WaybillTableRes> selectOrderList(OrderQuery orderQuery);

	AjaxResult shipperOrderListCount(OrderQuery orderQuery);

	AjaxResult removeOrderByIds(Long[] ids);

	CommonResult orderToWaybill(WaybillUpdateRequest waybillUpdate);

	CommonResult<WaybillRes> getOrderById(Long id);

	CommonResult orderToWaybillCheck(Long id);

	CommonResult<WaybillRes> getTransportationAgreement(Long id);

	CommonResult isUploadFile(Long id, Integer type);

	String getAddress(String province, String city, String area, String address);

	String getAllAddress(String province, String city, String area, String address);

	CommonResult<WaybillRes> getWaybillPayById(Long id);

	CommonResult<WaybillRes> getWaybillInvoiceById(Long id);

	CommonResult<WaybillRes> getWaybillImageById(Long id);

	CommonResult<WaybillRes> getWaybillAgreementById(Long id);

	CommonResult waybillExport(SPReq req);

	CommonResult payExport(SPReq req);
}
