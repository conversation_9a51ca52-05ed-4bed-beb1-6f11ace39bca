package com.zly.project.transport.waybill.domain.res;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SPWaybillRes {

	@ApiModelProperty("运单号")
	private String shippingNoteNumber;

	@ApiModelProperty("关联项目（项目合约名称）")
	private String frameworkContractName;

	@ApiModelProperty("单据日期")
	private String billDate;

	@ApiModelProperty("关联货源（货源别名）")
	private String nameAlias;

	@ApiModelProperty("运输协议单号")
	private String originalDocumentNumber;

	@ApiModelProperty("运输状态(0:未开始 1:在途中 2:已完成)")
	private Integer transportStatus;

	@ApiModelProperty("运输协议(0:未发送短信 1:已签署 2:已发送短信)")
	private Integer transportationAgreementState;

	@ApiModelProperty("结算状态(0:未结算1:部分结算 2:已结算)")
	private Integer settleStatus;

	@ApiModelProperty("开票状态(0.未开票 1.部分开票 2.已开票 3.开票中 4.撤销开票 5.驳回开票)")
	private Integer billStatus;

	@ApiModelProperty("车载定位(-1:未设置(无定位) 0:未开始 1:定位中 2:定位完成 3:车辆未入网 4:定位异常 5:无定位 6:查询完成)")
	private Integer locateStatus;

	@ApiModelProperty("回单状态(0：已上传，1：未上传)")
	private Integer receiptStatus;

	@ApiModelProperty("保单状态(-1:未投保 1:不投保 2:投保受理成功 3:投保受理失败 4:系统异常 5:投保成功 6:投保失败 7:人工处理)")
	private Integer policyStatus;

	@ApiModelProperty("保价金额")
	private BigDecimal policyFare;

	@ApiModelProperty("车牌号码")
	private String vehicleNumber;

	@ApiModelProperty("司机姓名")
	private String driverName;

	@ApiModelProperty("驾驶证号")
	private String drivingLicense;

	@ApiModelProperty("司机电话")
	private String telephone;

	@ApiModelProperty("收款人户名")
	private String payeeName;

	@ApiModelProperty("收款人银行卡号")
	private String bankCardNo;

	@ApiModelProperty("收款人身份证")
	private String identityCard;

	@ApiModelProperty("收款人手机号")
	private String bankMobile;

	@ApiModelProperty("货主单价")
	private String feePriceString;

	@ApiModelProperty("货主货款")
	private BigDecimal receiveFare;

	@ApiModelProperty("应付总运费(元)不带油卡")
	private BigDecimal payFare;

	@ApiModelProperty("支付计划")
	private String payType;

	@ApiModelProperty("预付运费金额")
	private BigDecimal prepayMoney;

	@ApiModelProperty("到付运费金额")
	private BigDecimal arriveMoney;

	@ApiModelProperty("回单付运费金额")
	private BigDecimal receiptMoney;

	@ApiModelProperty("预付运费类型")
	private Integer prepayType;

	@ApiModelProperty("油卡卡号")
	private String oilCardNo;

	@ApiModelProperty("实付运费(元)")
	private BigDecimal actualFare;

	@ApiModelProperty("应收上游")
	private BigDecimal receiveUpstream;

	@ApiModelProperty("应付下游")
	private BigDecimal payableDownstream;

	@ApiModelProperty("发货单位")
	private String consignor;

	@ApiModelProperty("发货详细地址")
	private String placeOfLoading;

	@ApiModelProperty("发货联系方式")
	private String consignorContactPhone;

	@ApiModelProperty("收货单位")
	private String consignee;

	@ApiModelProperty("收货详细地址")
	private String goodsReceiptPlace;

	@ApiModelProperty("收货联系方式")
	private String consigneeContactPhone;

	@ApiModelProperty("运输距离(千米)")
	private BigDecimal mileage;

	@ApiModelProperty("货物名称")
	private String descriptionOfGoods;

	@ApiModelProperty("货物包装")
	private String packagingName;

	@ApiModelProperty("货物类别")
	private String cargoTypeClassificationName;

	@ApiModelProperty("大宗单价")
	private String feePrice;

	@ApiModelProperty("结算数量")
	private BigDecimal feeAmount;

	@ApiModelProperty("应收单位(1.吨 2.方 3.车)")
	private Integer feeUnit;

	@ApiModelProperty("装货数量")
	private BigDecimal loadingWeight;

	@ApiModelProperty("卸货数量")
	private BigDecimal unloadWeight;

	@ApiModelProperty("装车日期")
	private String despatchActualDateTime;

	@ApiModelProperty("到达日期")
	private String goodsReceiptDateTime;

	@ApiModelProperty("实际装货时间")
	private String actualStartTime;

	@ApiModelProperty("实际卸货时间")
	private String actualEndTime;

	@ApiModelProperty("备注")
	private String remark;

	@ApiModelProperty("运单来源(1:PC建单 2:微信快速录单 3:APP 4:小黑卡 5:合作方 6:微信小程序 7:批量导入)")
	private Integer resource;

	@ApiModelProperty("创建人")
	private String createBy;

	@ApiModelProperty("创建时间")
	private String createTime;

	@ApiModelProperty("付款时间")
	private String payTime;

	@ApiModelProperty("发票号码")
	private String invoiceNumber;

	@ApiModelProperty("开票时间")
	private String invoiceTime;

	@ApiModelProperty("项目编号")
	private String contractCode;

	@ApiModelProperty("车型")
	private String vehicleType;

	@ApiModelProperty("车辆总质量")
	private BigDecimal grossMass;

	@ApiModelProperty("省")
	private String loadingProvince;

	@ApiModelProperty("市")
	private String loadingCity;

	@ApiModelProperty("区")
	private String loadingArea;

	@ApiModelProperty("发货经度")
	private String loadingLon;

	@ApiModelProperty("发货维度")
	private String loadingLat;

	@ApiModelProperty("省")
	private String unloadProvince;

	@ApiModelProperty("市")
	private String unloadCity;

	@ApiModelProperty("区")
	private String unloadArea;

	@ApiModelProperty("收货经度")
	private String unloadLon;

	@ApiModelProperty("收货经度")
	private String unloadLat;


}
