package com.zly.project.transport.waybill.controller;

import cn.hutool.json.JSONUtil;
import com.zly.common.constant.BusinessConstants;
import com.zly.common.constant.CacheConstants;
import com.zly.common.constant.PDFConfig;
import com.zly.common.enums.CodeEnum;
import com.zly.common.utils.*;
import com.zly.common.utils.file.WordUtil;
import com.zly.framework.aspectj.lang.annotation.RateLimiter;
import com.zly.framework.aspectj.lang.enums.LimitType;
import com.zly.framework.config.CustomConfig;
import com.zly.framework.interceptor.annotation.RepeatSubmit;
import com.zly.framework.redis.RedisCache;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.project.carrier.driver.domain.Driver;
import com.zly.project.carrier.driver.service.impl.DriverServiceImpl;
import com.zly.project.common.domain.FreightForwarderAttachmentInfo;
import com.zly.project.common.service.IFreightForwarderAttachmentInfoService;
import com.zly.project.consignor.customer.domain.CustomerInfo;
import com.zly.project.consignor.customer.service.impl.CustomerDriverRelationServiceImpl;
import com.zly.project.consignor.customer.service.impl.CustomerInfoServiceImpl;
import com.zly.project.freightforwarder.domain.FreightForwarderInfo;
import com.zly.project.freightforwarder.service.impl.FreightForwarderInfoServiceImpl;
import com.zly.project.riskManagement.service.WaybillRiskService;
import com.zly.project.system.service.impl.SysClientLogService;
import com.zly.project.system.service.impl.SysCostServiceImpl;
import com.zly.project.transport.waybill.domain.*;
import com.zly.project.transport.waybill.domain.req.ElectronicContractReq;
import com.zly.project.transport.waybill.domain.req.SignDriverReq;
import com.zly.project.transport.waybill.domain.req.WaybillReq;
import com.zly.project.transport.waybill.domain.res.ElectronicContractRes;
import com.zly.project.transport.waybill.domain.res.SignDriverRes;
import com.zly.project.transport.waybill.domain.res.WaybillRes;
import com.zly.project.transport.waybill.mapper.WaybillMapper;
import com.zly.project.transport.waybill.mapper.WaybillMapperEx;
import com.zly.project.transport.waybill.service.SignContractService;
import com.zly.project.transport.waybill.service.WaybillElectronicContractService;
import com.zly.project.transport.waybill.service.impl.CarrierElectronicContractServiceImpl;
import com.zly.project.transport.waybill.service.impl.DriverElectronicContractServiceImpl;
import com.zly.project.transport.waybill.service.impl.WaybillServiceImpl;
import com.zly.project.transport.waybill.service.impl.WaybillSlaveServiceImpl;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("sign/contract")
public class SignContractContriller {

	@Resource
	private WaybillServiceImpl waybillService;
	@Resource
	private WaybillSlaveServiceImpl waybillSlaveService;
	@Resource
	private CarrierElectronicContractServiceImpl contractService;
	@Resource
	private CustomConfig customConfig;
	@Resource
	private WaybillElectronicContractService waybillElectronicContractService;
	@Resource
	private IFreightForwarderAttachmentInfoService freightForwarderAttachmentInfoService;
	@Resource
	private SignContractService signContractService;
	@Resource
	private FreightForwarderInfoServiceImpl freightForwarderInfoService;
	@Resource
	private DriverElectronicContractServiceImpl driverElectronicContractService;
	@Resource
	private RedisCache redisCache;
	@Resource
	private DriverServiceImpl driverService;
	@Resource
	private CustomerInfoServiceImpl customerInfoService;
	@Resource
	private MessageUtil messageUtil;
	@Resource
	private SysCostServiceImpl sysCostService;
	@Resource
	private WaybillMapperEx waybillMapperEx;
	@Value("${msg.url}")
	private String msgUrl;
	@Resource
	private SysClientLogService sysClientLogService;
	@Resource
	private WaybillMapper waybillMapper;
	@Resource
	private WaybillRiskService waybillRiskService;

	@RateLimiter(key = "getWaybillIdByUrl", limitType = LimitType.IP)
	@ApiOperation(value = "根据短链换取运单id -- v4.6.1 -- zxy")
	@GetMapping("/getWaybillIdByUrl/{url}")
	public CommonResult<Long> signContract(@PathVariable String url) {
		if (CommonUtil.isNullOrEmpty(url)) {
			return CommonResult.error("参数不得为空");
		}
		if (url.contains("_")) {
			url = url.substring(0, url.indexOf("_"));
		}
		Long waybillId = Base62Utils.decodeToLong(url);
		if (waybillId == null) {
			return CommonResult.error("参数不正确无法转换，请确认");
		}
		return CommonResult.success(waybillId);
	}

	@ApiOperation(value = "生成签署合同-4.3.13-shw", notes = "保存合同签署地址-4.3.13-shw")
	@RequestMapping(value = "", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult signContract(@RequestBody ElectronicContractReq req) {
		ElectronicContractRes res = new ElectronicContractRes();

		WaybillSlave slave = waybillSlaveService.selectWaybillSlaveById(req.getId());
		if (!slave.getElectronicContractCode().contains("id=" + req.getId() + "&time=" + req.getTime())) {
			return AjaxResult.success("链接已失效", 0);
		}

		if (!StringUtils.isEmpty(slave.getElectronicContractUrl())) {
			res.setFileUrl(slave.getElectronicContractUrl());
			return AjaxResult.success("操作成功", res);
		}

		// 获取合同模板。替换文档中的数字。没有的为空。
		req.setIsSave(false);
		res = waybillElectronicContractService.writeWaybillSignTemplate(req);

		String htmlPath = PDFConfig.asposeHtmlOutPath + req.getId() + "_" + (new Random().nextInt(89) + 10) + ".html";
		WordUtil.docToHtml(res.getUrl(), htmlPath);
		String content = WordUtil.readHtmlContent(htmlPath);
		res.setContent(content);
		return AjaxResult.success("操作成功", res);
	}

	@RequestMapping(value = "showBigContract", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult showBigSignTemplate(@RequestBody ElectronicContractReq req) {
		CarrierElectronicContract electronicContract = contractService.selectCarrierElectronicContractById(req.getId());
		if (electronicContract == null) {
			return AjaxResult.error("未查询到合同签字申请");
		}
		if (!electronicContract.getCarrierIdentityCard().equals(SecurityUtils.getLoginUser().getCarrierUser().getIdentityCard())) {
			return AjaxResult.error("认证的用户与签约的承运人信息不符，无法操作");
		}
		if (electronicContract.getState() == 2) {
			return AjaxResult.error("签约成功");
		}

		AjaxResult result = showBigSignTemplate(electronicContract);
		if (!result.isSuccess()) {
			return result;
		}

		ElectronicContractRes res = (ElectronicContractRes) result.getData();
		String htmlPath = PDFConfig.asposeHtmlOutPath + req.getId() + "_" + (new Random().nextInt(89) + 10) + ".html";
		WordUtil.docToHtml(res.getUrl(), htmlPath);
		String content = WordUtil.readHtmlContent(htmlPath);
		res.setContent(content);
		return AjaxResult.success("操作成功", res);
	}

	@RequestMapping(value = "writeBigContract", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult writeBigSignTemplate(@RequestBody ElectronicContractReq req) {
		CarrierElectronicContract electronicContract = contractService.selectCarrierElectronicContractById(req.getId());
		if (electronicContract == null) {
			return AjaxResult.error("未查询到合同签字申请");
		}
		if (StringUtils.isEmpty(req.getBase64Str())) {
			return AjaxResult.error("请签名确认");
		}
		electronicContract.setBase64Str(req.getBase64Str());
		AjaxResult result = waybillElectronicContractService.writeBigSignTemplate(electronicContract);
		if (!result.isSuccess()) {
			return result;
		}

		ElectronicContractRes res = (ElectronicContractRes) result.getData();

		String PdfFilePath = PDFConfig.asposePdfOutPath + req.getId() + "_" + (new Random().nextInt(89) + 10) + ".pdf";
		WordUtil.docToPdf(res.getUrl(), PdfFilePath);

		//上传pdf
		String key = "运输协议" + UUID.randomUUID() + ".pdf";
		String url = customConfig.getQiniuDomain() + key;
		QiNiuUtil.upload(PdfFilePath, key);

		Date now = new Date();
		electronicContract.setTakeTime(now);
		electronicContract.setLoseTime(DateUtils.addMonth(now, 12));
		electronicContract.setFileUrl(url);
		electronicContract.setState(2);
		electronicContract.setSignFileUrl(res.getFileUrl());
		contractService.updateCarrierElectronicContract(electronicContract);

		return AjaxResult.success("生成运输协议成功");
	}

	//获取签约信息
	@ApiOperation(value = "获取签约信息-4.3.18-shw", notes = "司机签约-4.3.18-shw")
	@RequestMapping(value = "getContract", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult driver(@RequestBody SignDriverReq driverReq) {
		String identityCard = driverReq.getIdentityCard();
		String driverName = driverReq.getDriverName();
		if (driverReq.getId() == null) {
			//二维码签约
			//查询司机是否存在
			List<Driver> drivers = driverService.selectDriverList(identityCard);
			if (drivers == null || drivers.size() == 0) {
				return AjaxResult.popError("该司机尚未在平台注册，请确认填写的内容是否正确");
			}
		} else {
			//短信签约
			//查询是否是同一个司机
			DriverElectronicContract driverElectronicContract = driverElectronicContractService.selectDriverElectronicContractById(driverReq.getId());
			if (driverElectronicContract != null) {
				driverReq.setFreightForwarderId(driverElectronicContract.getFreightForwarderId());
				driverReq.setCustomerId(driverElectronicContract.getCustomerId());
				if (!driverReq.getDriverName().equals(driverElectronicContract.getDriverName()) || !driverReq.getIdentityCard().equals(driverElectronicContract.getDriverIdentityCard())) {
					return AjaxResult.popError("请填写司机 " + driverElectronicContract.getDriverName() + " 的信息");
				}
			}
		}
		//是否已经签署合同
		DriverElectronicContract driverElectronicContract = new DriverElectronicContract();
		driverElectronicContract.setDriverIdentityCard(identityCard);
		driverElectronicContract.setFreightForwarderId(driverReq.getFreightForwarderId());
		DriverElectronicContract contract = driverElectronicContractService.selectEffectiveDriverElectronicContractByFreightForwarderId(driverElectronicContract);
		if (contract != null && contract.getState() != 2) {
			ElectronicContractRes res = new ElectronicContractRes();
			res.setFileUrl(contract.getFileUrl());
			return AjaxResult.success("已签约", res);
		}

		AjaxResult result = waybillService.showDriverBigSignTemplate(driverReq);
		if (!result.isSuccess()) {
			return result;
		}

		ElectronicContractRes res = (ElectronicContractRes) result.getData();
		String htmlPath = PDFConfig.asposeHtmlOutPath + TextUtil.getTimeSequenceID(5) + "_" + (new Random().nextInt(89) + 10) + ".html";
		WordUtil.docToHtml(res.getUrl(), htmlPath);
		String content = WordUtil.readHtmlContent(htmlPath);
		res.setContent(content);
		return AjaxResult.success(res);
	}

	//签署合同
	@ApiOperation(value = "签署合同-4.3.18-shw", notes = "签署合同-4.3.18-shw")
	@RequestMapping(value = "signContract", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult signContract(@RequestBody SignDriverReq driverReq) {
		if (StringUtils.isEmpty(driverReq.getBase64Str())) {
			return AjaxResult.error("签名信息为空");
		}
		if (driverReq.getId() == null && driverReq.getFreightForwarderId() == null) {
			return AjaxResult.error("参数缺失");
		}
		if (driverReq.getId() != null && driverReq.getFreightForwarderId() != null) {
			return AjaxResult.error("参数错误,无法判断是扫码还是短信");
		}

		String identityCard = driverReq.getIdentityCard();
		String driverName = driverReq.getDriverName();
		DriverElectronicContract query = new DriverElectronicContract();
		DriverElectronicContract update = new DriverElectronicContract();
		if (driverReq.getId() != null) {
			update = driverElectronicContractService.selectDriverElectronicContractById(driverReq.getId());
			driverReq.setFreightForwarderId(update.getFreightForwarderId());
		}
		//签署协议
		AjaxResult result = waybillElectronicContractService.writeDriverBigSignTemplate(driverReq);
		if (!result.isSuccess()) {
			return result;
		}

		ElectronicContractRes res = (ElectronicContractRes) result.getData();

		String PdfFilePath = PDFConfig.asposePdfOutPath + TextUtil.getTimeSequenceID(5) + "_" + (new Random().nextInt(89) + 10) + ".pdf";
		WordUtil.docToPdf(res.getUrl(), PdfFilePath);

		//上传pdf
		String key = "运输协议" + UUID.randomUUID() + ".pdf";
		String url = customConfig.getQiniuDomain() + key;
		QiNiuUtil.upload(PdfFilePath, key);

		if (driverReq.getId() == null) {
			//二维码签约
			//查询是新的签约还是续约
			query.setDriverIdentityCard(identityCard);
			query.setFreightForwarderId(driverReq.getFreightForwarderId());

			DriverElectronicContract driverElectronicContract = driverElectronicContractService.selectEffectiveDriverElectronicContractByFreightForwarderId(query);
			DriverElectronicContract inster = new DriverElectronicContract();
			inster.setId(TextUtil.getTimeSequenceID(5));
			inster.setFreightForwarderId(driverReq.getFreightForwarderId());
			inster.setCustomerId(0L);
			inster.setDriverName(driverName);
			inster.setDriverIdentityCard(identityCard);
			inster.setPhone("");
			inster.setSource(1);
			inster.setFileUrl(url);
			inster.setMsgUrl("");
			inster.setSendMsg("");
			if (driverElectronicContract == null) {
				inster.setTakeTime(new Date());
				inster.setLoseTime(DateUtils.addSecond(DateUtils.strToDate(DateUtils.dateToStr(DateUtils.addMonth(inster.getTakeTime(), 12), DateUtils.YYMMDD) + " 00:00:00"), -1));
				inster.setIsEffective(1);
				inster.setState(1);
			} else {
				inster.setTakeTime(DateUtils.addSecond(driverElectronicContract.getLoseTime(), 1));
				inster.setLoseTime(DateUtils.addSecond(DateUtils.addMonth(inster.getTakeTime(), 12), -1));
				inster.setIsEffective(-1);
				inster.setState(0);

				//即将失效改为已补传
				driverElectronicContract.setState(3);
				driverElectronicContractService.updateDriverElectronicContract(driverElectronicContract);
			}
			driverElectronicContractService.insertDriverElectronicContract(inster);
		} else {
			//短信签约
			query.setDriverIdentityCard(identityCard);
			query.setFreightForwarderId(update.getFreightForwarderId());

			DriverElectronicContract driverElectronicContract = driverElectronicContractService.selectEffectiveDriverElectronicContractByFreightForwarderId(query);
			update.setFileUrl(url);
			if (driverElectronicContract == null) {
				update.setTakeTime(new Date());
				update.setLoseTime(DateUtils.addSecond(DateUtils.strToDate(DateUtils.dateToStr(DateUtils.addMonth(update.getTakeTime(), 12), DateUtils.YYMMDD) + " 00:00:00"), -1));
				update.setIsEffective(1);
				update.setState(1);
			} else {
				update.setTakeTime(DateUtils.addSecond(driverElectronicContract.getLoseTime(), 1));
				update.setLoseTime(DateUtils.addSecond(DateUtils.addMonth(update.getTakeTime(), 12), -1));
				update.setIsEffective(-1);
				update.setState(0);

				//即将失效改为已补传
				driverElectronicContract.setState(3);
				driverElectronicContractService.updateDriverElectronicContract(driverElectronicContract);
			}
			driverElectronicContractService.updateDriverElectronicContract(update);
		}
		return AjaxResult.success("签署成功");
	}

	//托运人关联几个税源地
	@ApiOperation(value = "司机有几个未签约的税源地-4.3.18-shw", notes = "司机有几个未签约的税源地-4.3.18-shw")
	@RequestMapping(value = "freightForwarderInfo", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult freightForwarderInfo(@RequestBody SignDriverReq driverReq) {
		return signContractService.selectDriverFreightForwarderInfo(driverReq);
	}

	@Resource
	private CustomerDriverRelationServiceImpl customerDriverRelationService;

	//发送短信
	@ApiOperation(value = "发送签约短信-4.3.18-shw", notes = "发送签约短信-4.3.18-shw")
	@RequestMapping(value = "sendMsg", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult sendMsg(@RequestBody SignDriverReq driverReq) {
		String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(driverReq.getUuid(), "");
		String captcha = redisCache.getCacheObject(verifyKey);
		if (captcha == null) {
			return AjaxResult.error("验证码已失效");
		}
		if (!driverReq.getPicCode().equalsIgnoreCase(captcha)) {
			return AjaxResult.error("验证码错误");
		}

		Long customerId = SecurityUtils.getShipperId();
		CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(customerId);
		Driver driver = customerDriverRelationService.selectCustomerDriverInfo(customerId, driverReq.getDriverId());

		String phoneCarrierValidate = CommonUtil.msgWithUrlIsIntercept(driver.getTelephone());
		if (CommonUtil.isNotNullOrEmpty(phoneCarrierValidate)) {
			return AjaxResult.error(phoneCarrierValidate);
		}
		//查询今天是否发送过
		if (!redisCache.hasKey(driver.getTelephone() + "_" + driverReq.getFreightForwarderId())) {
			// 查询是否有生效合同
			DriverElectronicContract driverElectronicContract = new DriverElectronicContract();
			driverElectronicContract.setDriverIdentityCard(driver.getIdentityCard());
			driverElectronicContract.setFreightForwarderId(driverReq.getFreightForwarderId());
			DriverElectronicContract contract = driverElectronicContractService.selectEffectiveDriverElectronicContractByFreightForwarderId(driverElectronicContract);
			driverElectronicContract.setPhone(driver.getTelephone());
			FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(driverReq.getFreightForwarderId());
			if (contract == null || contract.getState() == 4 || contract.getState() == 2) {
				// 没有生效合同,发送短信
				String url = "";
				String sendMsg = "";
				//没有发过短信和即将到期的发送新短信,未签署的把以前的短信重新发送
				if (contract == null || contract.getState() == 2) {
					Long id = TextUtil.getTimeSequenceID(5);
					url = msgUrl + id;
					sendMsg = "【" + customerInfo.getCustomerName() + "】" + driver.getDriverName() + "，您好！【网货平台-" + freightForwarderInfo.getName() + "】即将为您支付运费，点击以下链接：" + url + " 签署运输协议。链接会打开网页，通过实名认证后签署";
					DriverElectronicContract electronicContract = new DriverElectronicContract();
					electronicContract.setId(id);
					electronicContract.setFreightForwarderId(driverReq.getFreightForwarderId());
					electronicContract.setDriverIdentityCard(driver.getIdentityCard());
					electronicContract.setDriverName(driver.getDriverName());
					electronicContract.setMsgUrl(url);
					electronicContract.setSendMsg(sendMsg);
					electronicContract.setCustomerId(customerId);
					electronicContract.setPhone(driver.getTelephone());
					electronicContract.setState(4);
					electronicContract.setIsEffective(-1);
					driverElectronicContractService.insertDriverElectronicContract(electronicContract);
				} else {
					url = contract.getMsgUrl();
					sendMsg = "【" + customerInfo.getCustomerName() + "】" + driver.getDriverName() + "，您好！【网货平台-" + freightForwarderInfo.getName() + "】即将为您支付运费，点击以下链接：" + url + " 签署运输协议。链接会打开网页，通过实名认证后签署";
					contract.setDriverIdentityCard(driver.getIdentityCard());
					contract.setSendMsg(sendMsg);
					contract.setCustomerId(driverElectronicContract.getCustomerId());
					driverElectronicContractService.updateDriverElectronicContract(contract);
				}

				messageUtil.sendNormalMessage(driverElectronicContract.getPhone(), sendMsg, freightForwarderInfo.getCreditCode());
				// messageUtil.sendNormalMessage("13584395408", sendMsg);
				redisCache.setCacheObject(driver.getTelephone() + "_" + driverElectronicContract.getFreightForwarderId(), 0, DateUtils.getRemainSecondsOneDay(new Date()), TimeUnit.SECONDS);

				// 添加计费
				sysCostService.addSecurityCodeNormalCost(CodeEnum.SECURITY_CODE, driver.getTelephone(), driverElectronicContract.getCustomerId(), customerInfo.getCustomerName(), 0L, "");
			}
		} else {
			FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(driverReq.getFreightForwarderId());
			return AjaxResult.errorNext("今日已向该司机发送过签约短信（" + freightForwarderInfo.getName() + "），是否再次发送？");
		}

		//记录操作日志
		String actionName = "发送运输合同(司机姓名：" + driver.getDriverName() + "司机身份证号" + driver.getDrivingLicense();
		actionName += "，请求参数：" + JSONUtil.toJsonStr(driverReq);
		sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_SEND_CONTRACT_OF_CARRIAGE, BusinessConstants.ACTION_TYPE_OTHER,
				org.apache.commons.lang3.StringUtils.substring(actionName, 0, 5000), driver.getDrivingLicense());

		return AjaxResult.success("已向司机发送签约短信，请提醒司机注意接收短信，并尽快签约。");
	}

	//发送短信
	@ApiOperation(value = "再次发送签约短信-4.3.18-shw", notes = "再次发送签约短信-4.3.18-shw")
	@RequestMapping(value = "sendMsgAg", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult sendMsgAg(@RequestBody SignDriverReq driverReq) {
		Driver driver = driverService.selectAllDriverById(driverReq.getDriverId());
		Long customerId = SecurityUtils.getShipperId();
		CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(customerId);

		// 查询是否有生效合同
		DriverElectronicContract driverElectronicContract = new DriverElectronicContract();
		driverElectronicContract.setDriverIdentityCard(driver.getIdentityCard());
		driverElectronicContract.setFreightForwarderId(driverReq.getFreightForwarderId());
		DriverElectronicContract contract = driverElectronicContractService.selectEffectiveDriverElectronicContractByFreightForwarderId(driverElectronicContract);
		driverElectronicContract.setPhone(driver.getTelephone());
		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(driverReq.getFreightForwarderId());

		String phoneCarrierValidate = CommonUtil.msgWithUrlIsIntercept(driver.getTelephone());
		if (CommonUtil.isNotNullOrEmpty(phoneCarrierValidate)) {
			return AjaxResult.error(phoneCarrierValidate);
		}

		if (contract == null || contract.getState() == 4 || contract.getState() == 2) {
			// 没有生效合同,发送短信
			String url = "";
			String sendMsg = "";
			//没有发过短信和即将到期的发送新短信,未签署的把以前的短信重新发送
			if (contract == null || contract.getState() == 2) {
				Long id = TextUtil.getTimeSequenceID(5);
				url = msgUrl + id;
				sendMsg = "【" + customerInfo.getCustomerName() + "】" + driver.getDriverName() + "，您好！【网货平台-" + freightForwarderInfo.getName() + "】即将为您支付运费，点击以下链接：" + url + " 签署运输协议。链接会打开网页，通过实名认证后签署";
				DriverElectronicContract electronicContract = new DriverElectronicContract();
				electronicContract.setId(id);
				electronicContract.setFreightForwarderId(driverReq.getFreightForwarderId());
				electronicContract.setDriverIdentityCard(driver.getIdentityCard());
				electronicContract.setDriverName(driver.getDriverName());
				electronicContract.setMsgUrl(url);
				electronicContract.setSendMsg(sendMsg);
				electronicContract.setCustomerId(customerId);
				electronicContract.setPhone(driver.getTelephone());
				electronicContract.setState(4);
				electronicContract.setIsEffective(-1);
				driverElectronicContractService.insertDriverElectronicContract(electronicContract);
			} else {
				url = contract.getMsgUrl();
				sendMsg = "【" + customerInfo.getCustomerName() + "】" + driver.getDriverName() + "，您好！【网货平台-" + freightForwarderInfo.getName() + "】即将为您支付运费，点击以下链接：" + url + " 签署运输协议。链接会打开网页，通过实名认证后签署";
				contract.setDriverIdentityCard(driver.getIdentityCard());
				contract.setSendMsg(sendMsg);
				contract.setCustomerId(driverElectronicContract.getCustomerId());
				driverElectronicContractService.updateDriverElectronicContract(contract);
			}

			messageUtil.sendNormalMessage(driverElectronicContract.getPhone(), sendMsg, freightForwarderInfo.getCreditCode());
			// messageUtil.sendNormalMessage("13584395408", sendMsg);
			redisCache.setCacheObject(driverElectronicContract.getPhone() + "_" + driverElectronicContract.getFreightForwarderId(), 0, DateUtils.getRemainSecondsOneDay(new Date()), TimeUnit.SECONDS);

			// 添加计费
			sysCostService.addSecurityCodeNormalCost(CodeEnum.SECURITY_CODE, driver.getTelephone(), driverElectronicContract.getCustomerId(), customerInfo.getCustomerName(), 0L, "");
		}

		return AjaxResult.success("已向司机发送签约短信，请提醒司机注意接收短信，并尽快签约。");
	}

	//发送短信
	@ApiOperation(value = "批量发送签约短信-4.3.18-shw", notes = "批量发送签约短信-4.3.18-shw")
	@RequestMapping(value = "sendMsgAll", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult sendMsgAll(Long[] waybillIds) {
		String retMsg = "";
		Set<String> nameList = new TreeSet<>();
		Set<String> phoneList = new TreeSet<>();
		WaybillReq waybillReq = new WaybillReq();
		waybillReq.setWaybillIds(Arrays.asList(waybillIds));
		waybillReq.setPageNum(null);
		waybillReq.setPageSize(null);
		List<WaybillRes> list = waybillMapperEx.tradeApplyWaybillList(waybillReq);
		for (WaybillRes res : list) {
			String phoneCarrierValidate = CommonUtil.msgWithUrlIsIntercept(res.getTelephone());
			if (CommonUtil.isNotNullOrEmpty(phoneCarrierValidate)) {
				phoneList.add(res.getDriverName());
				continue;
			}
			// 查询redis有没有手机号
			if (!redisCache.hasKey(res.getTelephone() + "_" + res.getFreightForwarderId())) {
				// 查询是否有生效合同
				DriverElectronicContract driverElectronicContract = new DriverElectronicContract();
				driverElectronicContract.setDriverIdentityCard(res.getIdentityCard());
				driverElectronicContract.setFreightForwarderId(res.getFreightForwarderId());
				DriverElectronicContract contract = driverElectronicContractService.selectEffectiveDriverElectronicContractByFreightForwarderId(driverElectronicContract);
				driverElectronicContract.setPhone(res.getTelephone());
				FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(res.getFreightForwarderId());
				if (contract == null || contract.getState() == 4 || contract.getState() == 2) {
					// 没有生效合同,发送短信
					String url = "";
					String sendMsg = "";
					//没有发过短信和即将到期的发送新短信,未签署的把以前的短信重新发送
					if (contract == null || contract.getState() == 2) {
						Long id = TextUtil.getTimeSequenceID(5);
						url = msgUrl + id;
						sendMsg = "【" + res.getCustomerName() + "】" + res.getDriverName() + "，您好！【网货平台-" + freightForwarderInfo.getName() + "】即将为您支付运费，点击以下链接：" + url + " 签署运输协议。链接会打开网页，通过实名认证后签署";
						DriverElectronicContract electronicContract = new DriverElectronicContract();
						electronicContract.setId(id);
						electronicContract.setFreightForwarderId(res.getFreightForwarderId());
						electronicContract.setDriverIdentityCard(res.getDrivingLicense());
						electronicContract.setDriverName(res.getDriverName());
						electronicContract.setMsgUrl(url);
						electronicContract.setSendMsg(sendMsg);
						electronicContract.setCustomerId(res.getCustomerId());
						electronicContract.setPhone(res.getTelephone());
						electronicContract.setState(4);
						electronicContract.setIsEffective(-1);
						driverElectronicContractService.insertDriverElectronicContract(electronicContract);
					} else {
						url = contract.getMsgUrl();
						sendMsg = "【" + res.getCustomerName() + "】" + res.getDriverName() + "，您好！【网货平台-" + freightForwarderInfo.getName() + "】即将为您支付运费，点击以下链接：" + url + " 签署运输协议。链接会打开网页，通过实名认证后签署";
						contract.setDriverIdentityCard(res.getDrivingLicense());
						contract.setSendMsg(sendMsg);
						contract.setCustomerId(driverElectronicContract.getCustomerId());
						driverElectronicContractService.updateDriverElectronicContract(contract);
					}

					messageUtil.sendNormalMessage(driverElectronicContract.getPhone(), sendMsg, freightForwarderInfo.getCreditCode());
					// messageUtil.sendNormalMessage("13584395408", sendMsg);

					redisCache.setCacheObject(res.getTelephone() + "_" + driverElectronicContract.getFreightForwarderId(), 0, DateUtils.getRemainSecondsOneDay(new Date()), TimeUnit.SECONDS);

					// 添加计费
					CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(res.getCustomerId());
					sysCostService.addSecurityCodeNormalCost(CodeEnum.SECURITY_CODE, res.getTelephone(), res.getCustomerId(), customerInfo.getCustomerName(), res.getFrameworkContractId(),
							res.getFrameworkContractName());
				}
			} else {
				nameList.add(res.getDriverName());
			}
		}
		// 如果发过短信,弹出提示
		String errorMsg = "";
		if (!phoneList.isEmpty()) {
			for (String s : phoneList) {
				retMsg += s + ",";
			}
			errorMsg = "司机" + retMsg.substring(0,
					retMsg.length() - 1) + "手机号运营商为电信/联通，" + "根据电信、联通运营商的最新规定，不得向其所属手机号发送带链接的短信，请使用二维码分享的方式进行签约。";
		}
		if (!nameList.isEmpty()) {
			for (String s : nameList) {
				retMsg += s + ",";
			}
			return AjaxResult.errorNext(errorMsg + "司机" + retMsg.substring(0, retMsg.length() - 1) + "今日已发送过短信，不再重复发送，若还未签约，请电话联系对方");
		}
		return AjaxResult.success();
	}

	public AjaxResult showBigSignTemplate(CarrierElectronicContract electronicContract) {
		ElectronicContractRes res = new ElectronicContractRes();
		String wordOutPath = PDFConfig.asposeWordOutPath + System.currentTimeMillis() + ".docx";

		FreightForwarderAttachmentInfo freightForwarderAttachmentInfo = new FreightForwarderAttachmentInfo();
		freightForwarderAttachmentInfo.setRelationId(electronicContract.getFreightForwarderId());
		freightForwarderAttachmentInfo.setFileName("合同章");
		freightForwarderAttachmentInfo.setState(0);
		List<FreightForwarderAttachmentInfo> freightForwarderAttachmentInfos = freightForwarderAttachmentInfoService.selectFreightForwarderAttachmentInfoList(freightForwarderAttachmentInfo);
		if (freightForwarderAttachmentInfos == null || freightForwarderAttachmentInfos.size() == 0) {
			return AjaxResult.error("未查询到合同章");
		} else {
			res.setFileUrl(freightForwarderAttachmentInfos.get(0).getFileUrl());
		}

		CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(electronicContract.getCustomerId());
		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(electronicContract.getFreightForwarderId());
		Map<String, Object> params = new HashMap<String, Object>();
		// 网络货运人
		params.put("freightForwarderInfoName", freightForwarderInfo.getName());
		params.put("creditCode", freightForwarderInfo.getCreditCode());
		// 托运人
		params.put("customerName", customerInfo.getCustomerName());
		// 承运人信息
		params.put("actualCarrierName", SecurityUtils.getNickname());
		params.put("contactPhone", SecurityUtils.getPhonenumber());
		WordUtil.templateWriteWithImg(PDFConfig.asposeWordBigTemplatePath, params, wordOutPath);
		res.setUrl(wordOutPath);
		return AjaxResult.success(res);
	}

	@ApiOperation(value = "获取手机号运营商归属---4.6.3---jb", notes = "获取手机号运营商归属")
	@RequestMapping(value = "/carrier/{phoneNumber}", method = RequestMethod.GET)
	@ResponseBody
	public AjaxResult sendMsgAll(@PathVariable("phoneNumber") String phoneNumber) {
		String carrierByPhoneNumber = CommonUtil.getCarrierByPhoneNumber(phoneNumber);
		return AjaxResult.success(carrierByPhoneNumber);
	}

	//批量司机签约
	@ApiOperation(value = "批量司机签约", notes = "批量司机签约")
	@RequestMapping(value = "/driver/sign", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult driverSign(@RequestBody FreightForwarderInfo freightForwarderInfo) {
		signContractService.driverSign(freightForwarderInfo.getId());
		return AjaxResult.success();
	}

	//批量车辆签约
	@ApiOperation(value = "批量车辆签约", notes = "批量车辆签约")
	@RequestMapping(value = "/vehicle/sign", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult vehicleSign(@RequestBody FreightForwarderInfo freightForwarderInfo) {
		signContractService.vehicleSign(freightForwarderInfo.getId());
		return AjaxResult.success();
	}

	//批量删除签约
	@ApiOperation(value = "批量删除签约", notes = "批量删除签约")
	@RequestMapping(value = "/sign/del", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult signDel() {
		signContractService.signDel();
		return AjaxResult.success();
	}

	//上传附件  1运输协议  2雇佣协议  3委托收款协议
	@ApiOperation(value = "上传协议-4.5.2-shw", notes = "上传协议-4.5.2-shw")
	@PostMapping("/uploadFile")
	public AjaxResult uploadFile(@RequestBody UploadFile uploadFile) {
		if (uploadFile.getType() == null) {
			throw new RuntimeException("请选择正确的附件类型");
		}
		signContractService.uploadFile(uploadFile);
		return AjaxResult.success();
	}

	//上传附件  委托代付协议(API用)
	@ApiOperation(value = "上传委托代付协议---4.5.7---ysy", notes = "上传委托代付协议-4.5.7-ysy")
	@PostMapping("/saveAgencyPaymentAgreement")
	public AjaxResult saveAgencyPaymentAgreement(@RequestBody UploadFile uploadFile) {
		signContractService.saveAgencyPaymentAgreement(uploadFile);
		return AjaxResult.success();
	}

	//上传附件  运输协议(API用)
	@ApiOperation(value = "上传运输协议---ysy", notes = "上传运输协议---ysy")
	@PostMapping("/saveTransportationAgreement")
	public AjaxResult saveTransportationAgreement(@RequestBody UploadFile uploadFile) {
		Waybill waybill = waybillMapper.selectWaybillById(uploadFile.getWaybillId());
		signContractService.saveWaybillAttachmentInfo(waybill, uploadFile, uploadFile.getFileModelList());
		return AjaxResult.success();
	}

	// 发送短信签约雇佣合同
	@ApiOperation(value = "发送短信签约单条运单运输协议-4.5.2-shw", notes = "发送短信签约单条运单运输协议-4.5.2-shw")
	@RequestMapping(value = "/waybill/sendMsg", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult waybillSendMsg(@RequestBody SignDriverReq driverReq) {
		String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(driverReq.getUuid(), "");
		String captcha = redisCache.getCacheObject(verifyKey);
		if (captcha == null) {
			return AjaxResult.error("验证码已失效");
		}
		if (!driverReq.getPicCode().equalsIgnoreCase(captcha)) {
			return AjaxResult.error("验证码错误");
		}
		return signContractService.waybillSendMsg(driverReq);
	}

	//发送短信签约雇佣合同
	@ApiOperation(value = "发送短信签约单条运单运输协议-4.5.2-shw", notes = "发送短信签约单条运单运输协议-4.5.2-shw")
	@RequestMapping(value = "/waybill/sendMsgAg", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult waybillSendMsgAg(@RequestBody SignDriverReq driverReq) {
		return signContractService.waybillSendMsgAg(driverReq);
	}

	// 批量发送短信签约雇佣合同
	@ApiOperation(value = "发送短信签约单条运单运输协议-4.5.2-shw", notes = "发送短信签约单条运单运输协议-4.5.2-shw")
	@RequestMapping(value = "/waybill/sendMsgList", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult waybillSendMsgList(@RequestBody List<Long> waybillIds) {
		return signContractService.waybillSendMsgList(waybillIds);
	}

	//签约单条运单运输协议
	@ApiOperation(value = "显示单条运单运输协议-4.5.2-shw", notes = "显示单条运单运输协议-4.5.2-shw")
	@RequestMapping(value = "/waybill/show", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult waybillShow(@RequestBody SignDriverReq driverReq) {
		return signContractService.waybillShow(driverReq);
	}

	//签约单条运单运输协议
	@ApiOperation(value = "签约单条运单运输协议-4.5.2-shw", notes = "签约单条运单运输协议-4.5.2-shw")
	@RequestMapping(value = "/waybill/sign", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult waybillSign(@RequestBody SignDriverReq driverReq) {
		AjaxResult result = signContractService.waybillSign(driverReq);
		if (result.isSuccess()) {
			waybillRiskService.waybillRiskCheckById((Long) result.getData(), SecurityUtils.getNickname(), false);
		}
		return result;
	}

	//发送短信签约雇佣合同
	@ApiOperation(value = "发送短信签约雇佣合同-4.5.2-shw", notes = "发送短信签约雇佣合同-4.5.2-shw")
	@RequestMapping(value = "/employmentContract/sendMsg", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult employmentContractSendMsg(@RequestBody SignDriverReq driverReq) {
		String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(driverReq.getUuid(), "");
		String captcha = redisCache.getCacheObject(verifyKey);
		if (captcha == null) {
			return AjaxResult.error("验证码已失效");
		}
		if (!driverReq.getPicCode().equalsIgnoreCase(captcha)) {
			return AjaxResult.error("验证码错误");
		}
		return signContractService.employmentContractSendMsg(driverReq);
	}

	//发送短信签约雇佣合同
	@ApiOperation(value = "发送短信签约雇佣合同-4.5.2-shw", notes = "发送短信签约雇佣合同-4.5.2-shw")
	@RequestMapping(value = "/employmentContract/sendMsgAg", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult employmentContractSendMsgAg(@RequestBody SignDriverReq driverReq) {
		return signContractService.employmentContractSendMsgAg(driverReq);
	}

	//显示雇佣合同
	@ApiOperation(value = "显示雇佣合同-4.5.2-shw", notes = "显示雇佣合同-4.5.2-shw")
	@RequestMapping(value = "/employmentContract/show", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult employmentContractShow(@RequestBody SignDriverReq driverReq) {
		return signContractService.employmentContractShow(driverReq);
	}

	//签约雇佣合同
	@ApiOperation(value = "签约雇佣合同-4.5.2-shw", notes = "签约雇佣合同-4.5.2-shw")
	@RequestMapping(value = "/employmentContract/sign", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult employmentContractSign(@RequestBody SignDriverReq driverReq) {
		return signContractService.employmentContractSign(driverReq);
	}

	//发送短信签约委托合同
	@ApiOperation(value = "发送短信签约委托合同-4.5.2-shw", notes = "发送短信签约委托合同-4.5.2-shw")
	@RequestMapping(value = "/entrustmentContract/sendMsg", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult entrustmentContractSendMsg(@RequestBody SignDriverReq driverReq) {
		String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(driverReq.getUuid(), "");
		String captcha = redisCache.getCacheObject(verifyKey);
		if (captcha == null) {
			return AjaxResult.error("验证码已失效");
		}
		if (!driverReq.getPicCode().equalsIgnoreCase(captcha)) {
			return AjaxResult.error("验证码错误");
		}
		return signContractService.entrustmentContractSendMsg(driverReq);
	}

	//发送短信签约委托合同
	@ApiOperation(value = "发送短信签约委托合同-4.5.2-shw", notes = "发送短信签约委托合同-4.5.2-shw")
	@RequestMapping(value = "/entrustmentContract/sendMsgAg", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult entrustmentContractSendMsgAg(@RequestBody SignDriverReq driverReq) {
		return signContractService.entrustmentContractSendMsgAg(driverReq);
	}

	//显示委托合同
	@ApiOperation(value = "显示委托合同-4.5.2-shw", notes = "显示委托合同-4.5.2-shw")
	@RequestMapping(value = "/entrustmentContract/show", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult entrustmentContractShow(@RequestBody SignDriverReq driverReq) {
		return signContractService.entrustmentContractShow(driverReq);
	}

	//签约委托合同
	@ApiOperation(value = "签约委托合同-4.5.2-shw", notes = "签约委托合同-4.5.2-shw")
	@RequestMapping(value = "/entrustmentContract/sign", method = RequestMethod.POST)
	@ResponseBody
	@RepeatSubmit
	public AjaxResult entrustmentContractSign(@RequestBody SignDriverReq driverReq) {
		return signContractService.entrustmentContractSign(driverReq);
	}

	//发送短信签车主声明
	@ApiOperation(value = "发送短信签车主声明-4.5.2-shw", notes = "发送短信签车主声明-4.5.2-shw")
	@RequestMapping(value = "/carOwnerDeclaration/sendMsg", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult carOwnerDeclarationSendMsg(@RequestBody SignDriverReq driverReq) {
		String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(driverReq.getUuid(), "");
		String captcha = redisCache.getCacheObject(verifyKey);
		if (captcha == null) {
			return AjaxResult.error("验证码已失效");
		}
		if (!driverReq.getPicCode().equalsIgnoreCase(captcha)) {
			return AjaxResult.error("验证码错误");
		}
		return signContractService.carOwnerDeclarationSendMsg(driverReq);
	}

	//发送短信签车主声明
	@ApiOperation(value = "发送短信签车主声明-4.5.2-shw", notes = "发送短信签车主声明-4.5.2-shw")
	@RequestMapping(value = "/carOwnerDeclaration/sendMsgAg", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult carOwnerDeclarationSendMsgAg(@RequestBody SignDriverReq driverReq) {
		return signContractService.carOwnerDeclarationSendMsgAg(driverReq);
	}

	//显示车主声明
	@ApiOperation(value = "显示车主声明-4.5.2-shw", notes = "显示车主声明-4.5.2-shw")
	@RequestMapping(value = "/carOwnerDeclaration/show", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult carOwnerDeclarationShow(@RequestBody SignDriverReq driverReq) {
		return signContractService.carOwnerDeclarationShow(driverReq);
	}

	//签约车主声明
	@ApiOperation(value = "签约车主声明-4.5.2-shw", notes = "签约车主声明-4.5.2-shw")
	@RequestMapping(value = "/carOwnerDeclaration/sign", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult carOwnerDeclarationSign(@RequestBody SignDriverReq driverReq) {
		return signContractService.carOwnerDeclarationSign(driverReq);
	}

	//托运人关联几个税源地
	@ApiOperation(value = "辅助员有几个未签约的税源地-4.5.2-shw", notes = "辅助员有几个未签约的税源地-4.5.2-shw")
	@RequestMapping(value = "/auxiliary/freightForwarderInfo/{auxiliaryId}", method = RequestMethod.GET)
	@ResponseBody
	public AjaxResult auxiliaryFreightForwarderInfo(@PathVariable("auxiliaryId") Long auxiliaryId) {
		return signContractService.selectAuxiliaryFreightForwarderInfo(auxiliaryId);
	}

	//获取姓名身份证
	@ApiOperation(value = "获取姓名身份证-4.5.2-shw", notes = "姓名身份证-4.5.2-shw")
	@RequestMapping(value = "/name", method = RequestMethod.POST)
	@ResponseBody
	public CommonResult<SignDriverRes> waybillName(@RequestBody SignDriverReq driverReq) {
		return signContractService.waybillName(driverReq);
	}

	//实人认证
	@ApiOperation(value = "实人认证-4.5.2-shw", notes = "实人认证-4.5.2-shw")
	@RequestMapping(value = "/realPersonAuthentication", method = RequestMethod.POST)
	@ResponseBody
	@RateLimiter(key = "realPersonAuthentication", limitType = LimitType.IP)
	public AjaxResult realPersonAuthentication(@RequestBody SignDriverReq driverReq) {
		return signContractService.realPersonAuthentication(driverReq);
	}

	//查询是否有多个需要签署的协议
	@ApiOperation(value = "查询是否有多个需要签署的协议-4.5.7-shw", notes = "查询是否有多个需要签署的协议-4.5.7-shw")
	@RequestMapping(value = "/hasOtherFile", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult hasOtherFile(@RequestBody SignDriverReq driverReq) {
		return signContractService.hasOtherFile(driverReq);
	}

	//查询是否有多个需要签署的协议
	@ApiOperation(value = "签约多个协议-4.5.7-shw", notes = "签约多个协议-4.5.7-shw")
	@RequestMapping(value = "/signChooseContract", method = RequestMethod.POST)
	@ResponseBody
	public AjaxResult signChooseContract(@RequestBody SignDriverReq driverReq) {
		return signContractService.signChooseContract(driverReq);
	}
}
