package com.zly.project.transport.waybill.service.impl;

import static com.zly.common.enums.ClientType.FINANCE;
import static com.zly.common.enums.ClientType.SHIPPER;
import static com.zly.common.utils.PageUtils.startPage;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.sql.DataSource;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.datasource.DataSourceUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.github.houbb.heaven.util.lang.BeanUtil;
import com.github.pagehelper.PageHelper;
import com.zly.common.constant.BusinessConstants;
import com.zly.common.constant.CacheConstants;
import com.zly.common.constant.DictType;
import com.zly.common.constant.FileNames;
import com.zly.common.constant.HttpStatus;
import com.zly.common.constant.MQConstants;
import com.zly.common.constant.PDFConfig;
import com.zly.common.constant.TaxUploadConstants;
import com.zly.common.enums.ClientType;
import com.zly.common.constant.*;
import com.zly.common.enums.*;
import com.zly.common.enums.CodeEnum;
import com.zly.common.enums.WaybillSource;
import com.zly.common.exception.ServiceException;
import com.zly.common.utils.*;
import com.zly.common.utils.CodeUtil;
import com.zly.common.utils.CommonUtil;
import com.zly.common.utils.DateUtils;
import com.zly.common.utils.PageUtils;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.StringUtils;
import com.zly.common.utils.TextUtil;
import com.zly.common.utils.ValidateUtils;
import com.zly.common.utils.bean.BeanUtils;
import com.zly.common.utils.file.WordUtil;
import com.zly.common.utils.spring.SpringUtils;
import com.zly.framework.redis.RedisCache;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.PageDomain;
import com.zly.framework.web.page.TableInfo;
import com.zly.framework.web.page.TableSupport;
import com.zly.project.backgroundOperation.domain.BusinessModel;
import com.zly.project.backgroundOperation.domain.req.SynchronousShanDaoReq;
import com.zly.project.backgroundOperation.service.BusinessModelService;
import com.zly.project.backgroundOperation.service.FourDataHandlerService;
import com.zly.project.backgroundOperation.service.FourDataOriginalService;
import com.zly.project.backgroundOperation.service.OnlineSynchronousDataService;
import com.zly.project.carrier.auxiliary.domain.AuxiliaryStaffInfo;
import com.zly.project.carrier.auxiliary.mapper.AuxiliaryStaffInfoMapper;
import com.zly.project.carrier.auxiliary.service.AuxiliaryStaffInfoService;
import com.zly.project.carrier.carCaptain.domain.CarCaptainInfo;
import com.zly.project.carrier.carCaptain.domain.PersonVehicleProtocol;
import com.zly.project.carrier.carCaptain.mapper.CarCaptainInfoMapper;
import com.zly.project.carrier.carCaptain.mapper.PersonVehicleProtocolMapper;
import com.zly.project.carrier.carrier.domain.ActualCarrierInfo;
import com.zly.project.carrier.carrier.domain.ActualCarrierWaybillSlaveDetail;
import com.zly.project.carrier.carrier.mapper.ActualCarrierInfoMapper;
import com.zly.project.carrier.carrier.service.IActualCarrierInfoService;
import com.zly.project.carrier.carrier.service.IActualDriverRelationService;
import com.zly.project.carrier.carrier.service.IActualVehicleRelationService;
import com.zly.project.carrier.carrier.service.impl.CustomerActualCarrierImpl;
import com.zly.project.carrier.driver.DriverWaybillSlaveDetail;
import com.zly.project.carrier.driver.domain.CarrierUser;
import com.zly.project.carrier.driver.domain.Driver;
import com.zly.project.carrier.driver.domain.DriverFamilyProtocol;
import com.zly.project.carrier.driver.domain.res.DriverRes;
import com.zly.project.carrier.driver.mapper.DriverMapper;
import com.zly.project.carrier.driver.mapper.DriverMapperEx;
import com.zly.project.carrier.driver.service.DriverFamilyService;
import com.zly.project.carrier.driver.service.IDriverService;
import com.zly.project.carrier.driver.service.impl.CarrierUserServiceImpl;
import com.zly.project.carrier.driver.service.impl.CustomerDriverImpl;
import com.zly.project.carrier.payee.domain.CustomerPayeeRelation;
import com.zly.project.carrier.payee.domain.PayBank;
import com.zly.project.carrier.payee.domain.PayeeInfo;
import com.zly.project.carrier.payee.domain.PayeeInfoWaybillSlaveDetail;
import com.zly.project.carrier.payee.mapper.CustomerPayeeRelationMapper;
import com.zly.project.carrier.payee.mapper.PayeeInfoMapper;
import com.zly.project.carrier.payee.mapper.PayeeInfoMapperEx;
import com.zly.project.carrier.payee.service.impl.CustomerPayeeImpl;
import com.zly.project.carrier.payee.service.impl.PayeeInfoServiceImpl;
import com.zly.project.carrier.payee.service.impl.PtmCertificateCheckServiceImpl;
import com.zly.project.carrier.vehicle.domain.CustomerVehicleRelation;
import com.zly.project.carrier.vehicle.domain.Vehicle;
import com.zly.project.carrier.vehicle.domain.VehicleWaybillSlaveDetail;
import com.zly.project.carrier.vehicle.domain.req.VehicleTrackReq;
import com.zly.project.carrier.vehicle.domain.res.VehicleRes;
import com.zly.project.carrier.vehicle.mapper.CustomerVehicleRelationMapper;
import com.zly.project.carrier.vehicle.mapper.VehicleMapper;
import com.zly.project.carrier.vehicle.service.IVehicleService;
import com.zly.project.carrier.vehicle.service.impl.CustomerVehicleImpl;
import com.zly.project.common.domain.*;
import com.zly.project.common.domain.req.FileSaveRequest;
import com.zly.project.common.domain.res.TrackLocationRes;
import com.zly.project.common.mapper.ChinaProvincesCitiesAreasMapper;
import com.zly.project.common.service.*;
import com.zly.project.common.service.impl.AmapServiceImpl;
import com.zly.project.common.service.impl.CustomerAttachmentInfoServiceImpl;
import com.zly.project.common.service.impl.FreightForwarderAttachmentInfoServiceImpl;
import com.zly.project.consignor.consignee.domain.ConsigneeInfo;
import com.zly.project.consignor.consignee.service.IConsigneeInfoService;
import com.zly.project.consignor.consignor.domain.ConsignorInfo;
import com.zly.project.consignor.consignor.service.IConsignorInfoService;
import com.zly.project.consignor.customer.domain.CustomerAuxiliaryRelation;
import com.zly.project.consignor.customer.domain.CustomerCaptainRelation;
import com.zly.project.consignor.customer.domain.CustomerDriverRelation;
import com.zly.project.consignor.customer.domain.CustomerInfo;
import com.zly.project.consignor.customer.mapper.CustomerAuxiliaryRelationMapper;
import com.zly.project.consignor.customer.mapper.CustomerCaptainRelationMapper;
import com.zly.project.consignor.customer.mapper.CustomerDriverRelationMapper;
import com.zly.project.consignor.customer.mapper.CustomerInfoMapper;
import com.zly.project.consignor.customer.service.impl.CustomerAuxiliaryRelationServiceImpl;
import com.zly.project.consignor.customer.service.impl.CustomerCaptainRelationServiceImpl;
import com.zly.project.contract.domain.FrameworkContract;
import com.zly.project.contract.service.IFrameworkContractService;
import com.zly.project.customerForwarderContract.domain.CustomerForwarderContract;
import com.zly.project.customerForwarderContract.domain.CustomerForwarderContractAttachmentInfo;
import com.zly.project.customerForwarderContract.domain.req.CustomerForwarderContractReq;
import com.zly.project.customerForwarderContract.mapper.CustomerForwarderContractAttachmentInfoMapper;
import com.zly.project.customerForwarderContract.mapper.CustomerForwarderContractMapper;
import com.zly.project.es.waybill.service.EsWaybillService;
import com.zly.project.etc.domain.EtcInvoice;
import com.zly.project.etc.mapper.EtcInvoiceMapper;
import com.zly.project.etc.service.EtcInterfaceService;
import com.zly.project.finance.statement.domain.FinanceStatementBill;
import com.zly.project.finance.statement.domain.FinanceStatementWaybill;
import com.zly.project.finance.statement.mapper.FinanceStatementBillMapperEx;
import com.zly.project.finance.statement.service.IFinanceStatementBillService;
import com.zly.project.finance.statement.service.IFinanceStatementWaybillService;
import com.zly.project.financeOperation.domain.req.FinancingWaybillQueryReq;
import com.zly.project.financeOperation.domain.req.ProjectOptionReq;
import com.zly.project.financeOperation.domain.req.RiskControllerReq;
import com.zly.project.financeOperation.domain.res.FinancingWaybillRes;
import com.zly.project.financeOperation.domain.res.ProjectOptionRes;
import com.zly.project.financeOperation.domain.res.RiskControllerRes;
import com.zly.project.financeOperation.domain.res.StatementWaybillInfo;
import com.zly.project.freightforwarder.domain.FreightForwarderConfig;
import com.zly.project.freightforwarder.domain.FreightForwarderInfo;
import com.zly.project.freightforwarder.mapper.FreightForwarderConfigMapper;
import com.zly.project.freightforwarder.mapper.FreightForwarderInfoMapper;
import com.zly.project.freightforwarder.service.IFreightForwarderInfoService;
import com.zly.project.freightforwarder.service.impl.FreightForwarderAuxiliaryRelationServiceImpl;
import com.zly.project.freightforwarder.service.impl.FreightForwarderCarCaptainRelationServiceImpl;
import com.zly.project.group.domain.FrameworkContractSubchain;
import com.zly.project.group.domain.WaybillContractChain;
import com.zly.project.group.domain.WaybillCustomerRelation;
import com.zly.project.group.mapper.FrameworkContractSubchainMapper;
import com.zly.project.group.mapper.FrameworkContractSubchainMapperEx;
import com.zly.project.group.mapper.WaybillCustomerRelationMapper;
import com.zly.project.group.mapper.WaybillCustomerRelationMapperEx;
import com.zly.project.group.service.IWaybillCustomerService;
import com.zly.project.makecode.domain.MakeCode;
import com.zly.project.makecode.domain.MakeCodeRule;
import com.zly.project.makecode.mapper.MakeCodeMapper;
import com.zly.project.makecode.mapper.MakeCodeMapperEx;
import com.zly.project.makecode.mapper.MakeCodeRuleMapper;
import com.zly.project.makecode.service.IMakeCodeRuleService;
import com.zly.project.makecode.service.IMakeCodeService;
import com.zly.project.miniprogram.domain.res.MakeCodeRes;
import com.zly.project.operation.domain.OperationPlain;
import com.zly.project.operation.service.IOperationPlainService;
import com.zly.project.proxy.domain.ProxyInvoices;
import com.zly.project.proxy.domain.ProxyInvoicesLog;
import com.zly.project.proxy.domain.req.chyProxy.*;
import com.zly.project.proxy.domain.res.cjhyProxy.FailWaybill;
import com.zly.project.proxy.domain.res.cjhyProxy.WaybillFullUploadRes;
import com.zly.project.proxy.mapper.ProxyInvoicesMapper;
import com.zly.project.proxy.service.CjhyProxyInvoiceService;
import com.zly.project.proxy.service.ProxyInvoiceService;
import com.zly.project.proxy.service.ProxyInvoicesLogService;
import com.zly.project.riskManagement.domain.WaybillRiskInfo;
import com.zly.project.riskManagement.mapper.WaybillRiskInfoMapper;
import com.zly.project.riskManagement.service.WaybillRiskService;
import com.zly.project.service.customerservice.service.ICustomerServiceInfoService;
import com.zly.project.settlement.domain.CustomerTradeApply;
import com.zly.project.settlement.domain.request.TradeFlowQueryReq;
import com.zly.project.settlement.domain.response.FlowResEx;
import com.zly.project.settlement.mapper.CustomerTradeApplyMapper;
import com.zly.project.settlement.mapper.CustomerTradeApplyMapperEx;
import com.zly.project.settlement.service.ICustomerTradeFlowService;
import com.zly.project.settlement.service.huaxia.HuaXiaService;
import com.zly.project.settlement.service.impl.CustomerTradeApplyServiceImpl;
import com.zly.project.srms.hybgps.GeoJudgeUtils;
import com.zly.project.srms.hybgps.domain.Point;
import com.zly.project.statistics.domain.*;
import com.zly.project.system.domain.FreightForwarderCapacityRelation;
import com.zly.project.system.domain.SysDictData;
import com.zly.project.system.domain.SysParameter;
import com.zly.project.system.domain.SysUser;
import com.zly.project.system.mapper.FreightForwarderCapacityRelationMapper;
import com.zly.project.system.service.*;
import com.zly.project.system.service.impl.CarrierMessageInfoServiceImpl;
import com.zly.project.system.service.impl.SysClientLogService;
import com.zly.project.system.service.impl.SysParameterServiceImpl;
import com.zly.project.taxUpload.domain.FreightForwarderDriverContract;
import com.zly.project.taxUpload.domain.TaxUploadWaybillInfo;
import com.zly.project.taxUpload.mapper.FreightForwarderDriverContractMapper;
import com.zly.project.taxUpload.service.YwkUploadService;
import com.zly.project.transport.waybill.domain.*;
import com.zly.project.transport.waybill.domain.req.*;
import com.zly.project.transport.waybill.domain.res.*;
import com.zly.project.transport.waybill.mapper.*;
import com.zly.project.transport.waybill.service.*;
import com.zly.project.web.services.CommonServices;
import com.zly.project.wlhy.model.UploginkWaybillQuery;
import com.zly.project.wlhy.model.UploginkWaybillRes;
import com.zly.project.wlhy.model.WaybillUploginkInfo;
import com.zly.project.wlhy.service.AgreementGenerateService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.datasource.DataSourceUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.sql.DataSource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zly.common.enums.ClientType.FINANCE;
import static com.zly.common.enums.ClientType.SHIPPER;
import static com.zly.common.utils.PageUtils.startPage;

/**
 * 运单Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-10-29
 */
@SuppressWarnings({ "rawtypes", "unused" })
@Service
public class WaybillServiceImpl implements IWaybillService {

	private static final Logger log = LoggerFactory.getLogger(WaybillServiceImpl.class);

	@Resource
	private DataSource dataSource;

	@Resource
	private WaybillMapper waybillMapper;
	@Resource
	private WaybillMapperEx waybillMapperEx;
	@Resource
	private IWaybillSlaveService waybillSlaveService;
	@Resource
	private IConsignorInfoService consignorInfoService;
	@Resource
	private IConsigneeInfoService consigneeInfoService;
	@Resource
	private PayeeInfoMapper payeeInfoMapper;
	@Resource
	private IWaybillFareService waybillFareService;
	@Resource
	private ISysDictTypeService dictTypeService;
	@Resource
	private IVehicleService vehicleService;
	@Resource
	private IDriverService driverService;
	@Resource
	private CustomerInfoMapper customerInfoMapper;
	@Resource
	private WaybillMiniprogramTrackMapper waybillMiniprogramTrackMapper;
	@Resource
	private ISysCostService sysCostService;
	@Resource
	private IOperationPlainService operationPlainService;
	@Resource
	private MakeCodeMapper makeCodeMapper;
	@Resource
	private IFrameworkContractService frameworkContractService;
	@Resource
	private ActualCarrierInfoMapper actualCarrierInfoMapper;
	@Resource
	private IActualVehicleRelationService actualVehicleRelationService;
	@Resource
	private CustomerTradeApplyMapper customerTradeApplyMapper;
	@Resource
	private CustomerTradeApplyMapperEx customerTradeApplyMapperEx;
	@Resource
	private ICustomerServiceInfoService customerServiceInfoService;
	@Resource
	private IActualCarrierInfoService actualCarrierInfoService;
	@Resource
	private IWaybillContractChainService waybillContractChainService;
	@Resource
	private IWaybillCustomerService waybillCustomerService;
	@Resource
	private IWaybillGoodsService waybillGoodsService;
	@Resource
	private WaybillGoodsMapperEx waybillGoodsMapperEx;
	@Resource
	private IDriverAttachmentInfoService driverAttachmentInfoService;
	@Resource
	private IVehicleAttachmentInfoService vehicleAttachmentInfoService;
	@Resource
	private IPayeeAttachmentInfoService payeeAttachmentInfoService;
	@Resource
	private IWaybillAttachmentInfoService waybillAttachmentInfoService;
	@Resource
	private TrackService trackService;
	@Resource
	private RedisCache redisCache;
	@Resource
	private SysClientLogService sysClientLogService;
	@Resource
	private BusinessModelService businessModelService;
	@Resource
	private WaybillCustomerRelationMapperEx waybillCustomerRelationMapperEx;
	@Resource
	private IFreightForwarderInfoService freightForwarderInfoService;
	@Resource
	private WaybillServiceImpl waybillService;
	@Resource
	private ISysDictDataService sysDictDataService;
	@Resource
	private FreightForwarderAttachmentInfoServiceImpl freightForwarderAttachmentInfoService;
	@Resource
	private ChinaProvincesCitiesAreasMapper chinaProvincesCitiesAreasMapper;
	@Resource
	private WaybillCustomerRelationMapper waybillCustomerRelationMapper;
	@Resource
	private FrameworkContractSubchainMapper frameworkContractSubchainMapper;
	@Resource
	private FrameworkContractSubchainMapperEx contractSubchainMapperEx;
	@Resource
	private EsWaybillService esWaybillService;
	@Resource
	private EtcInterfaceService etcInterfaceService;
	@Resource
	private IFinanceStatementWaybillService financeStatementWaybillService;
	@Resource
	private IFinanceStatementBillService financeStatementBillService;
	@Resource
	private PaymentRecordService paymentRecordService;
	@Resource
	private DriverElectronicContractMapper driverElectronicContractMapper;
	@Resource
	private MakeCodeMapperEx makeCodeMapperEx;
	@Resource
	private CommonServices commonServices;
	@Resource
	private ICustomerTradeFlowService customerTradeFlowService;
	@Resource
	private WaybillCommonService waybillCommonService;
	@Resource
	private IMakeCodeService makeCodeService;
	@Resource
	private CustomerVehicleImpl customerVehicleService;
	@Resource
	private CustomerDriverImpl customerDriverService;
	@Resource
	private CustomerActualCarrierImpl customerActualService;
	@Resource
	private CustomerPayeeImpl customerPayeeService;
	@Resource
	private IActualDriverRelationService actualDriverRelationService;
	@Resource
	private IActualCarrierAttachmentInfoService actualCarrierAttachmentInfoService;
	@Resource
	private VehicleMapper vehicleMapper;
	@Resource
	private DriverMapper driverMapper;
	@Resource
	private WaybillSlaveMapper waybillSlaveMapper;
	@Resource
	RabbitTemplate rabbitTemplate;
	@Resource
	private SysParameterServiceImpl sysParameterService;
	@Value("${environment.config.mq.verifyTrajectoryMqKey}")
	private String verifyTrajectoryMqKey;// 批量轨迹校验mq的key
	@Resource
	private WaybillTrackAbnormalServiceImpl waybillTrackAbnormalService;
	@Resource
	private CustomerPayeeRelationMapper customerPayeeRelationMapper;
	@Resource
	private IFreightForwarderVehicleRelationService freightForwarderVehicleRelationService;
	@Resource
	private IFreightForwarderDriverRelationService freightForwarderDriverRelationService;
	@Resource
	private IFreightForwarderActualRelationService freightForwarderActualRelationService;
	@Resource
	private ITaxingRulesService taxingRulesService;
	@Resource
	private YwkUploadService ywkUploadService;
	@Resource
	private MakeCodeRuleMapper makeCodeRuleMapper;
	@Resource
	private IMakeCodeRuleService makeCodeRuleService;
	@Resource
	private AttachmentService attachmentService;
	@Resource
	private FinanceStatementBillMapperEx financeStatementBillMapperEx;
	@Resource
	private CarCaptainInfoMapper carCaptainInfoMapper;
	@Resource
	private AuxiliaryStaffInfoMapper auxiliaryStaffInfoMapper;
	@Resource
	private PayeeInfoMapperEx payeeInfoMapperEx;
	@Resource
	private DriverMapperEx driverMapperEx;
	@Resource
	private CustomerCaptainRelationMapper customerCaptainRelationMapper;
	@Resource
	private CustomerAuxiliaryRelationMapper customerAuxiliaryRelationMapper;
	@Resource
	private FreightForwarderCarCaptainRelationServiceImpl freightForwarderCarCaptainRelationService;
	@Resource
	private FreightForwarderAuxiliaryRelationServiceImpl freightForwarderAuxiliaryRelationService;
	@Resource
	private CustomerCaptainRelationServiceImpl customerCaptainRelationService;
	@Resource
	private CustomerAuxiliaryRelationServiceImpl customerAuxiliaryRelationService;
	@Resource
	private FourDataOriginalService fourDataOriginalService;
	@Resource
	private FreightForwarderInfoMapper freightForwarderInfoMapper;
	@Resource
	private AmapServiceImpl amapService;
	@Resource
	private PayeeInfoServiceImpl payeeInfoService;
	@Resource
	private OnlineSynchronousDataService onlineSynchronousDataService;
	@Resource
	private FourDataHandlerService fourDataHandlerService;
	@Resource
	private WaybillRiskService waybillRiskService;
	@Resource
	private WaybillProxyInvoiceMapper waybillProxyInvoiceMapper;
	@Resource
	private WaybillAuditRecordMapper waybillAuditRecordMapper;
	@Resource
	private WaybillRiskInfoMapper waybillRiskInfoMapper;
	@Resource
	private FreightForwarderConfigMapper freightForwarderConfigMapper;
	@Resource
	private WaybillUploadTransportationMapper waybillUploadTransportationMapper;
	@Resource
	private AuxiliaryStaffInfoService auxiliaryStaffInfoService;
	@Resource
	private WaybillAuditAttachmentInfoMapper waybillAuditAttachmentInfoMapper;
	@Resource
	private CustomerDriverRelationMapper customerDriverRelationMapper;
	@Resource
	private CustomerVehicleRelationMapper customerVehicleRelationMapper;
	@Resource
	private PtmCertificateCheckServiceImpl ptmCertificateCheckService;
	@Resource
	private AgreementGenerateService agreementGenerateService;
	@Resource
	private SignContractService signContractService;
	@Resource
	private HuaXiaService huaXiaService;
	@Resource
	private CustomerForwarderContractMapper customerForwarderContractMapper;
	@Resource
	private CustomerForwarderContractAttachmentInfoMapper customerForwarderContractAttachmentInfoMapper;
	@Resource
	private DriverFamilyService driverFamilyService;
	@Resource
	private WaybillVehicleValidateService waybillVehicleValidateService;
	@Resource
	private ProxyInvoiceService proxyInvoiceService;
	@Resource
	private ProxyInvoicesMapper proxyInvoicesMapper;
	@Resource
	private EtcInvoiceMapper etcInvoiceMapper;
	@Resource
	private ProxyInvoicesLogService proxyInvoicesLogService;
	@Resource
	private CjhyProxyInvoiceService cjhyProxyInvoiceService;

	/**
	 * 查询运单
	 *
	 * @param id
	 * 		运单主键
	 * @return 运单
	 */
	public Waybill selectWaybillById(Long id) {
		return waybillMapper.selectWaybillById(id);
	}

	/**
	 * 查询运单列表
	 *
	 * @param waybill
	 * 		运单
	 * @return 运单
	 */
	public List<Waybill> selectWaybillList(Waybill waybill) {
		return waybillMapper.selectWaybillList(waybill);
	}

	/**
	 * 新增运单
	 *
	 * @param waybill
	 * 		运单
	 * @return 结果
	 */
	public int insertWaybill(Waybill waybill) {
		return waybillMapper.insertWaybill(waybill);
	}

	/**
	 * 修改运单
	 *
	 * @param waybill
	 * 		运单
	 * @return 结果
	 */
	public int updateWaybill(Waybill waybill) {
		return waybillMapper.updateWaybill(waybill);
	}

	/**
	 * APP查询调度对应的运单列表
	 *
	 * @param waybillQuery
	 * 		运单
	 * @return 运单
	 */
	public TableInfo<WaybillWxRes> selectWaybillListFromApp(Waybill waybillQuery) {
		if (null == waybillQuery) {
			throw new RuntimeException("查询条件为空");
		}
		if (null == waybillQuery.getMakeCodeId()) {
			throw new RuntimeException("查询运单的所属货源为空");
		}
		if (null == waybillQuery.getOperationPlainId()) {
			throw new RuntimeException("查询运单的所属调度计划为空");
		}
		// 0：订单（待确认费用） 1运单（待签约） 2已签约(已签约 未开始)
		if (null == waybillQuery.getStatus()) {
			throw new RuntimeException("查询运单状态为空");
		}
		if (!Arrays.asList(BusinessConstants.ZERO_ONE_TWO).contains(waybillQuery.getStatus())) {
			throw new RuntimeException("查询运单状态不合法");
		}
		// 如果是2，查询出调车完成后的运单（包含了状态3，4）
		if (2 == waybillQuery.getStatus()) {
			waybillQuery.setStatus(-2);
		}
		startPage();
		List<Waybill> waybillList = this.selectWaybillList(waybillQuery);
		// 组装数据
		List<WaybillWxRes> waybillWxResList = waybillList.stream().map(m -> {
			WaybillWxRes waybillWxRes = new WaybillWxRes();
			BeanUtils.copyProperties(m, waybillWxRes);
			return waybillWxRes;
		}).collect(Collectors.toList());
		List<Long> vehicleIds = waybillList.stream().map(Waybill::getVehicleId).collect(Collectors.toList());
		// 根据车辆id查询车辆
		if (!vehicleIds.isEmpty()) {
			Map<Long, Vehicle> vehicleMap = vehicleMapper.selectVehicleByIds(vehicleIds).stream().collect(Collectors.toMap(Vehicle::getId, Function.identity(), (oldData, newData) -> oldData));
			waybillWxResList.forEach(l -> {
				if (vehicleMap.containsKey(l.getVehicleId())) {
					Vehicle vehicle = vehicleMap.get(l.getVehicleId());
					// 计算车辆长度，数据库中是毫米
					BigDecimal vehicleLength = new BigDecimal(vehicle.getVehicleLength());
					l.setVehicleLength(vehicleLength.divide(new BigDecimal(1000)));
					l.setVehicleTypeName(vehicle.getVehicleTypeName());
				}
			});
		}

		return TableInfo.success(waybillWxResList);
	}

	/**
	 * 查询运单列表(状态必须是'订单'或'运单')
	 *
	 * @param waybillReq
	 * @return
	 */
	public List<WaybillRes> waybillList(WaybillReq waybillReq) {
		if (StringUtils.isNull(waybillReq)) {
			return new ArrayList<WaybillRes>();
		}
		log.info("运单列表查询，查询条件中结算状态为{}!", waybillReq.getSettleStatus());
		BeanUtils.beanAttributeValueTrim(waybillReq);// 去掉该对象中string字段前后空格
		if (StringUtils.isNotNull(waybillReq.getStatus()) && waybillReq.getStatus() == BusinessConstants.WAYBILL_STATUS_DELETE) {
			return new ArrayList<WaybillRes>();
		}
		if (StringUtils.isNotBlank(waybillReq.getBeginOrderCreateTime())) {
			waybillReq.setBeginOrderCreateTime(waybillReq.getBeginOrderCreateTime() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(waybillReq.getEndOrderCreateTime())) {
			waybillReq.setEndOrderCreateTime(waybillReq.getEndOrderCreateTime() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillReq.getBeginCreateTime())) {
			waybillReq.setBeginCreateTime(waybillReq.getBeginCreateTime() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(waybillReq.getEndCreateTime())) {
			waybillReq.setEndCreateTime(waybillReq.getEndCreateTime() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillReq.getBeginPayTime())) {
			waybillReq.setBeginPayTime(waybillReq.getBeginPayTime() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(waybillReq.getEndPayTime())) {
			waybillReq.setEndPayTime(waybillReq.getEndPayTime() + " 23:59:59");
		}
		Long shipperId = SecurityUtils.getShipperId();
		if (null != shipperId) {
			waybillReq.setCustomerId(shipperId);
		}
		return waybillMapperEx.waybillList(waybillReq);
	}


	/**
	 * 小程序端，新增运单信息通用方法， 1、必有托运人、项目、货源、运力信息 2、货物信息在这边生成并插入货物表
	 */
	@Transactional(rollbackFor = Exception.class)
	public CommonResult<Waybill> addWaybill(WaybillReq req, String createBy) {
		// 0、校验必填数据
		CommonResult result = verify(req);
		if (CommonResult.isNotSuccess(result)) {
			return result;
		}
		String nickName = CommonUtil.isNullOrEmpty(createBy) ? SecurityUtils.getNickname() : createBy;

		// 1、创建运单信息
		Waybill waybill = generateWaybillInfoByReq(req, createBy);
		Long waybillId = waybill.getId();
		// 2、插入运单数据
		if (StringUtils.isBlank(waybill.getActualCarrierContactPhone())) {
			waybill.setActualCarrierContactPhone(req.getActualCarrierContactPhone());
		}
		this.insertWaybill(waybill);
		// 3、运单转包链关联关系处理
		// 3.1、增加运单、托运人关联表，这一步操作中会插入es
		waybillCustomerService.saveWaybillCustomer(nickName, waybill);
		// 3.2、增加运单转包链快照表
		waybillContractChainService.saveConatractChainList(Arrays.asList(waybillId), waybill.getFrameworkContractId(), nickName);
		// 4、运单子表处理
		WaybillSlave waybillSlave = createWaybillSlave(waybill, req);
		waybillSlaveService.insertWaybillSlave(waybillSlave);
		// 5、保存货物信息
		WaybillGoods waybillGoods = req.getWaybillGoods();
		waybillGoods.setWaybillId(waybillId);
		waybillGoods.setCreateBy(nickName);// 创建人
		waybillGoods.setCreateTime(DateUtils.getNowDate());// 创建时间
		waybillGoodsService.insertWaybillGoods(waybillGoods);
		// 推送4.0同步善道
		// pushWaybillReachShanDao(Arrays.asList(waybill.getId()), 0);
		return CommonResult.success(waybill);
	}

	private WaybillSlave createWaybillSlave(Waybill waybill, WaybillReq req) {
		WaybillSlave waybillSlave = new WaybillSlave();
		waybillSlave.setId(waybill.getId());
		waybillCommonService.fillTransportCapacityToWaybillSlave(waybill, waybillSlave);
		Long makeCodeId = req.getMakeCode().getId();
		MakeCode makeCode = makeCodeMapper.selectMakeCodeById(makeCodeId);
		waybillSlave.setWaybillPaymentType(makeCode.getWaybillPaymentType());
		if (makeCode.getSupplyType() == 1) {
			MakeCodeRule makeCodeRule = makeCodeRuleMapper.selectMakeCodeRuleById(makeCode.getRuleId());
			if (makeCodeRule != null) {
				waybillSlave.setMakeCodeRuleDetailed(makeCodeRule.getDetailed());
			}
			waybillSlave.setMakeCodeRuleId(makeCode.getRuleId());
			waybillSlave.setZeroingRule(makeCode.getZeroingRule());
		}
		return waybillSlave;
	}

	private Waybill generateWaybillInfoByReq(WaybillReq req, String createBy) {
		// 0、新建运单
		Waybill waybill = new Waybill();
		BeanUtils.copyProperties(req, waybill);
		// 1、基础信息设置
		Long waybillId = TextUtil.getTimeSequenceID(5);
		waybill.setId(waybillId);
		waybill.setShippingNoteNumber(CodeUtil.generateWaybillCode());// 设置运单号
		waybill.setSettleStatus(BusinessConstants.WAYBILL_SETTLE_STATUS_NOT); // 未结算
		waybill.setBillStatus(0); // 0.未开票 1.部分开票 2.已开票 3.开票中 4.撤销开票 5.驳回开票
		waybill.setReceiptStatus(1); // 回单是否已上传（0上传，1未上传）
		// 如果是三方,费用为未确认
		if (req.getRoleType() != null) {
			if (req.getOperationPlainId() != null && req.getOperationPlainId() != 0 && req.getRoleType() == 2) {
				// ActualCarrierInfo actualCarrierInfo = SecurityUtils.getLoginUser().getActualCarrierInfo();
				CarrierUser carrierUser = SecurityUtils.getLoginUser().getCarrierUser();
				if (!carrierUser.getDriverId().equals(waybill.getDriverId()) || !carrierUser.getCarCaptainId().equals(req.getCarCaptainId())) {
					waybill.setFareState(0);
				}
			}
		} else {
			// 辅助员给司机签约
			waybill.setFareState(1);
			if (req.getActualCarrierId() != null) {
				ActualCarrierInfo carrierInfo = actualCarrierInfoMapper.selectActualCarrierInfoById(req.getActualCarrierId());
				if (carrierInfo.getCarCaptainId() != null && carrierInfo.getCarCaptainId() != 0) {
					waybill.setFareState(0);
				}
			}
		}
		// 2、根据运单表中运力id，填充运力信息
		waybillCommonService.fillTransportCapicityInfoByIdToWaybill(waybill);
		// 3、根据货源，填充货源、地址、货物相关信息
		WaybillGoods goods = waybillCommonService.fillWaybillMakeCodeRelatedInfoToWaybill(req.getMakeCode(), waybill);
		req.setWaybillGoods(goods);
		// 4、填充项目相关信息
		MakeCode makeCode = req.getMakeCode();
		FrameworkContract contract = frameworkContractService.selectFrameworkContractById(makeCode.getContractId());
		waybill.setFrameworkContractId(contract.getId());
		waybill.setFrameworkContractName(contract.getContractName());
		waybill.setFrameworkContractCode(contract.getContractCode());
		waybill.setFreightForwarderId(contract.getFreightForwarderId());
		if (!CommonUtil.isEmptyOrZero(contract.getFreightForwarderId())) {
			// 查询配置项
			FreightForwarderConfig freightForwarderConfig = freightForwarderConfigMapper.selectAuditConfigByFreightForwarderId(contract.getFreightForwarderId());
			if (null != freightForwarderConfig && 0 == freightForwarderConfig.getIsAuditWaybill()) {
				waybill.setArtificialAuditState(0);
			}
		}
		// 5、未付金额处理
		// 如果预付金额为线下油卡，未付和实付要减掉油卡金额这部分
		if (PayType.PHASE.equals(req.getPayType()) && PrepayType.OILCARD.equals(req.getPrepayType())) {
			waybill.setUnpaidFare(req.getPayFare().subtract(req.getPrepayMoney()).setScale(2, RoundingMode.HALF_UP));
		}
		waybill.setDeductMoney(makeCode.getDeductMoney()); // 将货源的固定扣款设置到运单中
		waybill.setPrepayUnpaidMoney(req.getPrepayMoney());// 初始化预付未付金额，设置成和预付金额一样
		waybill.setArriveUnpaidMoney(req.getArriveMoney());// 初始化到付未付金额，设置成和到付金额一样
		waybill.setReceiptUnpaidMoney(req.getReceiptMoney());// 初始化回单付未付金额，设置成和回单付金额一样
		// 6、设置运单定位方式，根据来源和托运人端是否有权限区分
		setLocationType(waybill);
		// 7、代开状态设置
		int proxyInvoiceState = proxyInvoiceService.getProxyInvoiceStateByContractId(waybill.getFrameworkContractId(), waybill.getDrivingLicense(), waybill.getPayeeIdentityCard());
		waybill.setProxyInvoiceState(proxyInvoiceState);
		// 8、创建信息设置
		waybill.setCreateBy(createBy);// 创建人
		waybill.setCreateTime(DateUtils.getNowDate());// 创建时间
		return waybill;
	}

	public void setLocationType(Waybill req) {
		if (req.getResource() == WaybillSource.WX_MINIPROGRAM.val()) {
			// 小程序建单，自带小程序定位，再根据项目有无gps权限决定是否还有GPS
			ServiceInfo service = frameworkContractService.contractServiceInfo(req.getFrameworkContractId()).getData();
			if (service == null || service.getIsLocate() == -1) {
				req.setLocateType(BusinessConstants.WAYBILL_LOCATE_TYPE_MINI_PROGRAM);
				req.setLocateStatus(-1); // 定位状态(-1.未设置(无定位) 0:未开始 1:定位中 2:定位完成 3:车辆未入网 4:定位异常)
			} else if (service.getIsLocate() == 1) {
				req.setLocateType(BusinessConstants.WAYBILL_LOCATE_TYPE_MINI_AND_GPS);
				req.setLocateStatus(0); // 定位状态(-1.未设置(无定位) 0:未开始 1:定位中 2:定位完成 3:车辆未入网 4:定位异常)
			}
		} else {
			// PC建单，根据托运人有无gps权限决定是否有GPS
			boolean locateServiceOpen = customerServiceInfoService.shipperHasOpenLocateService(req.getCustomerId());
			if (locateServiceOpen) {
				req.setLocateType(BusinessConstants.WAYBILL_LOCATE_TYPE_GPS);
			}
		}
	}

	/**
	 * 查询运单详情
	 */
	public CommonResult<WaybillRes> getWaybillById(Long id) {
		// 0、校验及基础数据准备
		if (null == id || 0 >= id) {
			return CommonResult.requestError("参数错误，运单ID为空");
		}
		Waybill waybill = this.selectWaybillById(id);
		WaybillSlave waybillSlave = waybillSlaveService.selectWaybillSlaveById(id);
		if (null == waybill || -1 == waybill.getStatus() || null == waybillSlave) {
			return CommonResult.requestError("运单不存在");
		}
		Long customerId = commonServices.getLoginCustomerId();
		MakeCode makeCode = makeCodeMapper.selectMakeCodeById(waybill.getMakeCodeId());
		boolean makeCodeCanChange = true; // 货源是否可编辑标志
		// 1、处理原始运单中需特殊处理字段
		dealOriginalWaybill(waybill, makeCode);
		// 2、数据拷贝至返回类
		WaybillRes waybillResponse = new WaybillRes();
		BeanUtils.copyProperties(waybill, waybillResponse);
		waybillResponse.setMakeCodeCreateTime(makeCode.getCreateTime());
		//处理运单页面公里数显示保留小数---取消，不做处理，改为前端处理
		//		BigDecimal mileage = waybillResponse.getMileage();
		//		BigDecimal ten = new BigDecimal("10.000");
		//		BigDecimal hundred = new BigDecimal("100.000");
		//		if (ten.compareTo(mileage) > 0) {
		//			BigDecimal rounded = mileage.setScale(2, RoundingMode.HALF_UP);
		//			waybillResponse.setMileage(rounded);
		//		} else if (hundred.compareTo(mileage) > 0) {
		//			BigDecimal rounded = mileage.setScale(1, RoundingMode.HALF_UP);
		//			waybillResponse.setMileage(rounded);
		//		} else {
		//			BigDecimal rounded = mileage.setScale(0, RoundingMode.HALF_UP);
		//			waybillResponse.setMileage(rounded);
		//		}
		// 代开被驳回时，将驳回原因处理后返回
		if (5 == waybillResponse.getProxyInvoiceState()) {
			WaybillProxyInvoice queryWrapper = new WaybillProxyInvoice();
			queryWrapper.setWaybillId(id);
			WaybillProxyInvoice waybillProxyInvoice = waybillProxyInvoiceMapper.selectWaybillProxyInvoiceList(queryWrapper).stream().findFirst().orElse(null);
			if (null != waybillProxyInvoice) {
				waybillResponse.setRefuseReason(waybillProxyInvoice.getRefuseReason());
			}
		}
		// 3、从其他类中直接获取的字段赋值
		// 3.1、货源中数据、
		MakeCodeRule rule = makeCodeRuleMapper.selectMakeCodeRuleById(waybillSlave.getMakeCodeRuleId());
		waybillResponse.setMakeCodeRule(rule);
		waybillResponse.setSupplyType(makeCode.getSupplyType());
		waybillResponse.setCargoCode(makeCode.getCargoCode());
		waybillResponse.setWaybillPaymentType(waybillSlave.getWaybillPaymentType());
		waybillResponse.setUseScope(makeCode.getUseScope());
		waybillResponse.setNameAlias((waybill.getId() + "货源").equals(makeCode.getNameAlias()) ? "" : makeCode.getNameAlias());
		waybillResponse.setRisingAndLosingTons("无");
		waybillResponse.setRisingAndLosingMoney(BigDecimal.ZERO);
		// 网货名称
		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoMapper.selectFreightForwarderInfoById(waybill.getFreightForwarderId());
		if (freightForwarderInfo != null) {
			waybillResponse.setFreightForwarderName(freightForwarderInfo.getName());
		}

		BigDecimal loadingAccount = waybill.getFeeUnit() == 1 ? waybillSlave.getLoadingWeight() : waybillSlave.getLoadingCube();
		BigDecimal unloadAccount = waybill.getFeeUnit() == 1 ? waybillSlave.getUnloadWeight() : waybillSlave.getUnloadCube();
		String feeUnit = waybill.getFeeUnit() == 1 ? "吨" : "方";

		if (SupplyType.BULK.equals(makeCode.getSupplyType()) && WaybillSource.WX_MINIPROGRAM.equals(waybill.getResource()) && waybill.getStatus() == 4) {
			if (loadingAccount.compareTo(unloadAccount) > 0) {
				waybillResponse.setRisingAndLosingTons("亏" + loadingAccount.subtract(unloadAccount).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + feeUnit);
			}
			if (unloadAccount.compareTo(loadingAccount) > 0) {
				waybillResponse.setRisingAndLosingTons("涨" + unloadAccount.subtract(loadingAccount).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + feeUnit);
			}
		}

		// 3.2、子表中数据
		if (null != rule) {
			BigDecimal diffMoney = waybillCommonService.calculateDiffMoneyWithRule(loadingAccount, unloadAccount, rule);
			waybillResponse.setRisingAndLosingMoney(diffMoney);
		}
		waybillResponse.setLoadingWeight(waybillSlave.getLoadingWeight());
		waybillResponse.setUnloadWeight(waybillSlave.getUnloadWeight());
		waybillResponse.setLoadingCube(waybillSlave.getLoadingCube());
		waybillResponse.setUnloadCube(waybillSlave.getUnloadCube());
		waybillResponse.setPolicyType(waybillSlave.getPolicyType());
		waybillResponse.setPolicyFare(waybillSlave.getPolicyFare());
		waybillResponse.setPolicyNumber(waybillSlave.getPolicyNumber());
		waybillResponse.setPolicyStatus(waybillSlave.getPolicyStatus());
		waybillResponse.setWaybillRemark(waybillSlave.getRemark());
		waybillResponse.setMakeCodeRuleId(waybillSlave.getMakeCodeRuleId());
		waybillResponse.setZeroingRule(waybillSlave.getZeroingRule());
		waybillResponse.setMakeCodeRuleDetailed(waybillSlave.getMakeCodeRuleDetailed());
		waybillResponse.setReceiveFareCalculationType(waybillSlave.getReceiveFareCalculationType());
		// 4、两个类不同名的字段处理，值不需变化
		waybillResponse.setWaybillRemark(CommonUtil.isNotNullOrEmpty(waybill.getRemark()) ? waybillResponse.getWaybillRemark() : waybill.getRemark());
		waybillResponse.setBillDate(DateUtils.dateToStr(waybill.getBillDate(), DateUtils.YYMMDD));
		waybillResponse.setPlanStartTime(waybill.getDespatchActualDateTime());
		waybillResponse.setPlanEndTime(waybill.getGoodsReceiptDateTime());
		waybillResponse.setActualStartTime(waybill.getLoadTime());
		waybillResponse.setActualEndTime(waybill.getUnloadTime());
		waybillResponse.setActualCarrierIdentityCard(waybill.getActualCarrierIdentityCard());
		waybillResponse.setIdentityCard(waybill.getActualCarrierIdentityCard());

		// 如果单位是车，loadingAccount、unloadAccount固定返回数值1
		if (waybillResponse.getFeeUnit() == 3) {
			waybillResponse.setLoadingAccount(BigDecimal.ONE);
			waybillResponse.setUnloadAccount(BigDecimal.ONE);
		}
		// 5、两个类，值不同的字段处理
		// 5.1、装卸货重量 大宗货源根据运单 未开始 进行中 运送结束
		if (3 == waybill.getStatus()) {
			if (waybillResponse.getFeeUnit() == 2) {
				waybillResponse.setLoadingAccount(waybillSlave.getLoadingCube());
			} else if (waybillResponse.getFeeUnit() == 1) {
				waybillResponse.setLoadingAccount(waybillSlave.getLoadingWeight());
			}
		} else if (4 == waybill.getStatus()) {
			if (waybillResponse.getFeeUnit() == 2) {
				waybillResponse.setLoadingAccount(waybillSlave.getLoadingCube());
				waybillResponse.setUnloadAccount(waybillSlave.getUnloadCube());
			} else if (waybillResponse.getFeeUnit() == 1) {
				waybillResponse.setLoadingAccount(waybillSlave.getLoadingWeight());
				waybillResponse.setUnloadAccount(waybillSlave.getUnloadWeight());
			}
		}
		// 5.2、从运单关联表中取值字段--开票状态--应收应付数据
		WaybillCustomerRelation relation = waybillCustomerRelationMapper.queryInfoByCustomerIdAndWaybillId(customerId, id);
		if (null == relation) {
			return CommonResult.requestError("运单不存在");
		}
		waybillResponse.setPayableDownstream(relation.getPayableDownstream()); // 应付下游
		waybillResponse.setReceiveUpstream(String.valueOf(relation.getReceiveUpstream())); // 应收上游
		// 处理上游费率展示
		if (BigDecimal.ZERO.compareTo(relation.getRateByMe()) == 0) {
			waybillResponse.setReceiveUpstream("——");
		}
		if (SecurityUtils.getClientType().equals(ClientType.SHIPPER) || SecurityUtils.getClientType().equals(ClientType.FINANCE)) {
			if (relation.getWaybillState() == 1 || relation.getWaybillState() == 2 || relation.getWaybillState() == 3) {
				waybillResponse.setBillStatus(0);
			} else if (relation.getWaybillState() == 4) {
				waybillResponse.setBillStatus(2);
			}
			if (relation.getWaybillState() == 4 || relation.getWaybillState() == 3) {
				makeCodeCanChange = false;
			}
		}
		// 5.3、运输状态
		if (Arrays.asList(0, 1, 2).contains(waybillResponse.getStatus())) { // 0=订单,1=待签约,2=已签约,3=运输中,4=已完成
			waybillResponse.setTransportStatus(0); // 0:未开始 1:在途中 2:已完成
		} else if (3 == waybillResponse.getStatus()) {
			waybillResponse.setTransportStatus(1);
		} else if (4 == waybillResponse.getStatus()) {
			waybillResponse.setTransportStatus(2);
		}

		// 5.4、运单是否处于付款流程中
		waybillResponse.setIsPaying(1);
		if (waybillResponse.getSettleStatus() != 2) { // 非 已结算 状态的运单，需根据付款申请进行确认
			CustomerTradeApply customerTradeApply = new CustomerTradeApply();
			customerTradeApply.setWaybillId(id);
			List<CustomerTradeApply> customerTradeApplies = customerTradeApplyMapper.selectCustomerTradeApplyList(customerTradeApply);
			// 判断该运单是否在待付款待审核中 审核状态(0:待审核 1:审核通过（待付款） 2:审核不通过 3:已付款 4:付款失败 5:付款中)
			if (customerTradeApplies.stream().anyMatch(f -> Arrays.asList(0, 1, 4, 5).contains(f.getAuditStatus()))) {
				waybillResponse.setIsPaying(0);
			}
		}
		// 6、从外部获取的信息
		// 6.1、货主信息
		FrameworkContract contract = frameworkContractService.selectFrameworkContractById(waybillResponse.getFrameworkContractId());
		waybillResponse.setLastCustomerName(contract.getLastCustomerName());
		// 6.2、获取发货方、收货方信息
		ConsignorInfo consignorInfo = consignorInfoService.selectConsignorInfoById(waybillResponse.getConsignorId());
		if (consignorInfo != null) {
			consignorInfo.setPlaceOfLoading(getAddress(consignorInfo.getProvince(), consignorInfo.getCity(), consignorInfo.getArea(), consignorInfo.getPlaceOfLoading()));
			waybillResponse.setConsignorInfo(consignorInfo);
			waybillResponse.setConsignorAddersDetails(waybillService.getAllAddress(consignorInfo.getProvince(), consignorInfo.getCity(), consignorInfo.getArea(), consignorInfo.getPlaceOfLoading()));
		}
		ConsigneeInfo consigneeInfo = consigneeInfoService.selectConsigneeInfoById(waybillResponse.getConsigneeId());
		if (consigneeInfo != null) {
			consigneeInfo.setGoodsReceiptPlace(getAddress(consigneeInfo.getProvince(), consigneeInfo.getCity(), consigneeInfo.getArea(), consigneeInfo.getGoodsReceiptPlace()));
			waybillResponse.setConsigneeInfo(consigneeInfo);
			waybillResponse.setConsigneeAddersDetails(
					waybillService.getAllAddress(consigneeInfo.getProvince(), consigneeInfo.getCity(), consigneeInfo.getArea(), consigneeInfo.getGoodsReceiptPlace()));
		}
		// 6.3、获取货物信息
		setGoodsInfo(waybillResponse);

		// 6.4、设置运力相关信息（承运人、司机、车辆、收款人），不包含附件信息
		setTransportCapicityInfo2(waybillResponse, waybillSlave);

		// 6.5、获取运输合同
		List<WaybillAttachmentInfo> transportFiles = new ArrayList<>();
		List<WaybillUploadTransportation> transportationAttachmentInfos = waybillUploadTransportationMapper.selectJoinAttachmentsByWaybillIds(Arrays.asList(id), Arrays.asList(FileNames.ECONTRACT));
		for (WaybillUploadTransportation waybillUploadTransportation : transportationAttachmentInfos) {
			if (waybillUploadTransportation.getAuditState() != -1) {
				transportFiles = waybillAttachmentInfoService.transportFileList(id);
			}
		}
		WaybillAttachmentInfo generatedAgreementFile = null;
		if (SecurityUtils.getClientType().equals(ClientType.PLATFORM)) {
			// 查询上传省平台时生成的运输协议附件，在网络货运平台系统显示（state=3，如果有多条，取最新的一条）
			generatedAgreementFile = waybillAttachmentInfoService.generatedAgreementFile(id);
			if (null != generatedAgreementFile) {
				transportFiles.add(generatedAgreementFile);
			}
		}
		for (WaybillAttachmentInfo transportFile : transportFiles) {
			transportFile.setFileName("运输协议附件");
			transportFile.setOriginalFileName("运输协议附件");
		}
		waybillResponse.setTransportContractAttachmentList(transportFiles.stream().map(FileModel::of).collect(Collectors.toList()));

		// 6.6、设置费用相关信息
		setFareInfo(waybillResponse, waybill);

		// 6.9、运输合同操作记录
		List<WaybillOperation> operationList = this.getOperationList(waybill);
		waybillResponse.setOperationList(operationList);

		// 7、可编辑标志：货源是否可编辑
		waybillResponse.setMakeCodeUpdate(makeCodeCanChange);

		if (waybillResponse.getLocateStatus() == 2 && relation.getIsCompleteTrajectory() == 1) {
			waybillResponse.setLocateStatus(6);
		}
		BigDecimal inclusiveTransportFee = this.calculateTaxInclusiveTransportFee(waybillResponse.getPayFare());
		waybillResponse.setInclusiveTransportFee(inclusiveTransportFee);
		return CommonResult.success(waybillResponse);
	}

	@Resource
	private FreightForwarderCapacityRelationMapper forwarderCapacityRelationMapper;

	private DriverElectronicContract findEntrustedPaymentFile(String identityCard, Long freightForwarderId, Date waybillCreateTime) {
		// 协议相关信息
		List<FreightForwarderCapacityRelation> relations = forwarderCapacityRelationMapper.selectByCapacityIdCardAndForwarderId(identityCard, freightForwarderId, 2, null);
		DriverElectronicContract contract = new DriverElectronicContract();
		if (CommonUtil.isNullOrEmpty(relations)) {
			return contract;
		}
		FreightForwarderCapacityRelation relation = null;
		for (FreightForwarderCapacityRelation capacityRelation : relations) {
			if (capacityRelation.getSignTime().before(waybillCreateTime) && capacityRelation.getExpirationTime().after(waybillCreateTime)){
				relation = capacityRelation;
			}
		}
		int month = 1000;
		// 如果没有在日期中的协议,先放入生效的协议
		if (relation == null){
			for (FreightForwarderCapacityRelation capacityRelation : relations) {
				if (capacityRelation.getSignTime().after(waybillCreateTime)){
					Integer thisMonth = DateUtils.getMonthCountCovered(waybillCreateTime, capacityRelation.getSignTime());
					if (thisMonth < month){
						month = thisMonth;
						relation = capacityRelation;
					}
					if (thisMonth <12){
						if (capacityRelation.getState() == 1){
							month = 0;
							relation = capacityRelation;
						}
					}
				}
			}
		}
		if (relation == null){
			return contract;
		}
		List<FileModel> fileList = new ArrayList<>();
		contract.setState(relation.getState());
		contract.setTakeTime(relation.getSignTime());
		contract.setLoseTime(DateUtils.addMonth(relation.getSignTime(), 12));
		for (String s : relation.getFileUrl().split(",")) {
			FileModel model = new FileModel();
			model.setFileUrl(s);
			model.setKeyValue("");
			model.setFileSize(300);
			model.setFileName("委托付款协议附件");
			model.setOriginalFileName("委托付款协议附件");
			model.setFileType("image");
			String fileType = s.substring(s.lastIndexOf(".") + 1);
			if ("pdf".equals(fileType)) {
				model.setFileType("pdf");
			}
			if ("doc".equals(fileType)) {
				model.setFileType("document");
			}
			if ("docx".equals(fileType)) {
				model.setFileType("document");
			}
			if ("xlsx".equals(fileType)) {
				model.setFileType("sheet");
			}
			if ("xlx".equals(fileType)) {
				model.setFileType("sheet");
			}
			fileList.add(model);
		}
		contract.setFileUrlList(fileList);
		return contract;
	}

	@Resource
	private CustomerAttachmentInfoServiceImpl customerAttachmentInfoService;

	private List<FileModel> findCustomerFile(Long contractId) {
		// 获取网货上一层托运人id
		Long customerId = frameworkContractService.lastCustomerId(contractId);

		List<FileModel> list = new ArrayList<>();
		CustomerAttachmentInfo info = new CustomerAttachmentInfo();
		info.setRelationId(customerId);
		info.setState(0);
		info.setFileName(FileNames.PLATFORMPROTOCOL);
		info.setFreightForwarderId(SecurityUtils.getFreightForwarderId());
		List<CustomerAttachmentInfo> customerAttachmentInfos = customerAttachmentInfoService.selectCustomerAttachmentInfoList(info);

		for (CustomerAttachmentInfo customerAttachmentInfo : customerAttachmentInfos) {
			list.add(FileModel.of(customerAttachmentInfo));
		}
		return list;
	}

	/**
	 * 设置运单详情中，除运力外其他附件
	 *
	 * @param waybillResponse
	 */
	private void setAttachmentInfo(WaybillRes waybillResponse) {

		// 获取发货附件
		List<WaybillAttachmentInfo> loadingFiles = waybillAttachmentInfoService.loadingFileList(waybillResponse.getId());
		waybillResponse.setLoadingAttachmentList(loadingFiles.stream().map(FileModel::of).collect(Collectors.toList()));

		// 获取收货附件
		List<WaybillAttachmentInfo> unloadFiles = waybillAttachmentInfoService.unloadFileList(waybillResponse.getId());
		waybillResponse.setUnloadAttachmentList(unloadFiles.stream().map(FileModel::of).collect(Collectors.toList()));

		// 获取其他附件（附件名就是其他附件）
		List<WaybillAttachmentInfo> otherFiles = waybillAttachmentInfoService.otherFileList(waybillResponse.getId());
		waybillResponse.setOtherAttachmentList(otherFiles.stream().map(FileModel::of).collect(Collectors.toList()));

		// 获取运输附件
		List<WaybillAttachmentInfo> transportFiles = new ArrayList<>();
		List<WaybillUploadTransportation> transportationAttachmentInfos = waybillUploadTransportationMapper.selectJoinAttachmentsByWaybillIds(Arrays.asList(waybillResponse.getId()),
				Arrays.asList(FileNames.ECONTRACT));
		for (WaybillUploadTransportation waybillUploadTransportation : transportationAttachmentInfos) {
			if (waybillUploadTransportation.getAuditState() != -1) {
				transportFiles = waybillAttachmentInfoService.transportFileList(waybillResponse.getId());
			}
		}

		WaybillAttachmentInfo generatedAgreementFile = null;
		if (SecurityUtils.getClientType().equals(ClientType.PLATFORM)) {
			// 查询上传省平台时生成的运输协议附件，在网络货运平台系统显示（state=3，如果有多条，取最新的一条）
			generatedAgreementFile = waybillAttachmentInfoService.generatedAgreementFile(waybillResponse.getId());
			if (null != generatedAgreementFile) {
				transportFiles.add(generatedAgreementFile);
			}
		}
		for (WaybillAttachmentInfo transportFile : transportFiles) {
			transportFile.setFileName("运输协议附件");
			transportFile.setOriginalFileName("运输协议附件");
		}
		waybillResponse.setTransportContractAttachmentList(transportFiles.stream().map(FileModel::of).collect(Collectors.toList()));

		// 获取运输合同附件（包含 运单单独的小合同 与 司机和平台签约的大合同）
		List<SysAttachmentInfo> contractAttachments = getContractAttachments(waybillResponse);
		if (SecurityUtils.getClientType().equals(ClientType.PLATFORM)) {
			if (null != generatedAgreementFile) {
				SysAttachmentInfo file = new SysAttachmentInfo();
				BeanUtils.copyProperties(generatedAgreementFile, file);
				if (null == contractAttachments) {
					contractAttachments = new ArrayList<>();
				}
				contractAttachments.add(file);
			}
		}
		waybillResponse.setContractAttachments(contractAttachments);

		// 如果没有运输合同附件,查询税务生成的合同附件
		if (waybillResponse.getContractAttachments() == null || waybillResponse.getContractAttachments().size() == 0) {
			FreightForwarderDriverContract contract = new FreightForwarderDriverContract();
			contract.setFreightForwarderId(waybillResponse.getFreightForwarderId());
			contract.setDriverId(waybillResponse.getDriverId());
			List<FreightForwarderDriverContract> freightForwarderDriverContracts = freightForwarderDriverContractMapper.selectFreightForwarderDriverContractList(contract);
			if (freightForwarderDriverContracts != null && freightForwarderDriverContracts.size() > 0) {
				List<SysAttachmentInfo> list = new ArrayList<>();
				SysAttachmentInfo info = new SysAttachmentInfo();
				info.setFileName("平台运输合同");
				info.setFileUrl(freightForwarderDriverContracts.get(0).getFileUrl());
				list.add(info);
				waybillResponse.setContractAttachments(list);
			}
		}
		// 查询委托合同
		CustomerForwarderContract customerForwarderContract = new CustomerForwarderContract();
		customerForwarderContract.setCustomerId(waybillResponse.getCustomerId());
		customerForwarderContract.setFreightForwarderId(waybillResponse.getFreightForwarderId());
		customerForwarderContract.setWaybillCreateTime(DateUtils.getDatePart(waybillResponse.getCreateTime()));
		// 查出运单创建时间在委托合同生效期间的委托合同
		List<CustomerForwarderContractReq> customerForwarderContractReqList = customerForwarderContractMapper.selectContractListByTime(customerForwarderContract);
		List<CustomerForwarderContractAttachmentInfo> customerForwarderContractAttachmentInfoList = new ArrayList<>();
		// 查询合同附件
		for (CustomerForwarderContractReq customerForwarderContractReq : customerForwarderContractReqList) {
			CustomerForwarderContractAttachmentInfo customerForwarderContractAttachmentInfo = new CustomerForwarderContractAttachmentInfo();
			customerForwarderContractAttachmentInfo.setRelationId(customerForwarderContractReq.getId());
			List<CustomerForwarderContractAttachmentInfo> customerForwarderContractAttachmentInfos = customerForwarderContractAttachmentInfoMapper.selectCustomerForwarderContractAttachmentInfoList(
					customerForwarderContractAttachmentInfo);
			customerForwarderContractAttachmentInfoList.addAll(customerForwarderContractAttachmentInfos);
		}
		waybillResponse.setCustomerForwarderContractAttachmentInfoList(customerForwarderContractAttachmentInfoList);

	}

	@Resource
	private FreightForwarderDriverContractMapper freightForwarderDriverContractMapper;

	/**
	 * 运单详情中，处理原始运单字段
	 *
	 * @param waybill
	 * @param makeCode
	 */
	private void dealOriginalWaybill(Waybill waybill, MakeCode makeCode) {
		// 1、处理预计提货时间、预计到货时间
		if (null == waybill.getDespatchActualDateTime()) {
			// 签约后一小时为预计提货时间
			waybill.setDespatchActualDateTime(DateUtils.addHour(waybill.getSignTime(), 1));
			// 预计到货时间 = 预计提货时间+运输时长为
			waybill.setGoodsReceiptDateTime(DateUtils.addHour(waybill.getDespatchActualDateTime(), makeCode.getTransportationTime()));
		}
	}

	/**
	 * 运单详情中的货物信息
	 *
	 * @param waybillResponse
	 */
	private void setGoodsInfo(WaybillRes waybillResponse) {
		List<WaybillGoods> goodsList = waybillGoodsService.waybillGoodsList(waybillResponse.getId());
		waybillResponse.setWaybillGoodsList(goodsList.stream().map(GoodsInfo::of).collect(Collectors.toList()));
		if (goodsList.size() > 0) {
			WaybillGoods good = goodsList.get(0);
			// 货源别名
			if (null != waybillResponse.getCargoCode()) {
				SysDictData sysDictData = sysDictDataService.selectDictCode(DictType.CARGO_TYPE, good.getCargoTypeClassificationCode());
				if (null != sysDictData)
					waybillResponse.setCargoCode(sysDictData.getDictCode());
			}
			// 货物名称
			waybillResponse.setDescriptionOfGoods(good.getDescriptionOfGoods());
			waybillResponse.setPackagingLabel(good.getPackagingName());
			waybillResponse.setPackagingValue(good.getPackagingCode());
			waybillResponse.setCargoName(good.getDescriptionOfGoods());
		}
	}

	/**
	 * 运单详情中的运力信息
	 *
	 * @param waybillResponse
	 * @param waybillSlave
	 */
	private void setTransportCapicityInfo(WaybillRes waybillResponse, WaybillSlave waybillSlave) {
		// 1、承运人信息
		List<FileModel> carrierFiles = new ArrayList<>();
		Long payeeId = waybillResponse.getPayeeId();
		PayeeInfo payeeInfo1 = payeeInfoService.selectPayeeInfoById(payeeId);
		if (payeeInfo1 != null) {
			ActualCarrierInfo carrierInfo1 = actualCarrierInfoMapper.selectByIdentityCard(payeeInfo1.getIdentityCard());
			if (carrierInfo1 != null) {
				ActualCarrierWaybillSlaveDetail carrier = selectCarrier(carrierInfo1.getId());
				if (null != carrier) {
					waybillResponse.setActualCarrierName(carrier.getActualCarrierName());
					waybillResponse.setActualCarrierContactPhone(carrier.getContactPhone());
					List<ActualCarrierAttachmentInfo> carrierFileList = actualCarrierAttachmentInfoService.selectCarrierAttachmentInfoByIds(carrier.getAttachmentIds());
					carrierFiles = carrierFileList.stream().map(FileModel::of).collect(Collectors.toList());
				}
			}
		}

		// 2、司机信息
		DriverWaybillSlaveDetail driver = JSON.parseObject(waybillSlave.getDriverDetail(), DriverWaybillSlaveDetail.class);
		if (null != driver) {
			waybillResponse.setDriverName(driver.getDriverName());
			waybillResponse.setTelephone(driver.getTelephone());
			waybillResponse.setDrivingLicense(driver.getDrivingLicense());
			waybillResponse.setDriver(JSON.parseObject(waybillSlave.getDriverDetail(), DriverRes.class));
			// 2.1、获取司机附件
			List<DriverAttachmentInfo> driverFileList = driverAttachmentInfoService.selectDriverAttachmentInfoByIds(driver.getAttachmentIds());
			List<FileModel> driverFiles = driverFileList.stream().map(FileModel::of).collect(Collectors.toList());
			// 身份证拿承运人表附件
			ActualCarrierInfo carrierInfo = actualCarrierInfoMapper.selectByIdentityCard(driver.getDrivingLicense());
			if (carrierInfo != null) {
				List<ActualCarrierAttachmentInfo> actualCarrierAttachmentInfos = actualCarrierAttachmentInfoService.selectCarrierAttachmentList(carrierInfo.getId());
				if (actualCarrierAttachmentInfos != null && actualCarrierAttachmentInfos.size() > 0) {
					List<FileModel> carrierDriverFiles = actualCarrierAttachmentInfos.stream().map(FileModel::of).collect(Collectors.toList());
					for (FileModel carrierFile : carrierDriverFiles) {
						if (FileNames.ID_CARD_A.equals(carrierFile.getFileName())) {
							driverFiles.add(carrierFile);
						}
						if (FileNames.ID_CARD_B.equals(carrierFile.getFileName())) {
							driverFiles.add(carrierFile);
						}
					}
				}
			}
			waybillResponse.setDriverAttachmentList(driverFiles);
		}

		// 3、车辆信息
		VehicleWaybillSlaveDetail vehicle = JSON.parseObject(waybillSlave.getVehicleDetail(), VehicleWaybillSlaveDetail.class);
		if (null != vehicle) {
			waybillResponse.setVehicleType(vehicle.getVehicleType());
			waybillResponse.setVehicleTypeName(vehicle.getVehicleTypeName());
			waybillResponse.setVehicleTonnage(vehicle.getVehicleTonnage().compareTo(BigDecimal.ZERO) < 0 ? null : vehicle.getVehicleTonnage());
			VehicleRes vehicleJson = JSON.parseObject(waybillSlave.getVehicleDetail(), VehicleRes.class);
			if (null != vehicleJson) {
				// 目前司机就两个状态 如果为空的话就说明未对司机做过审核 默认都是审核通过
				vehicleJson.setApproveState(null == vehicleJson.getApproveState() ? 2 : vehicleJson.getApproveState());
			}
			waybillResponse.setVehicle(vehicleJson);
			waybillResponse.getVehicle().setOwnerName(waybillResponse.getVehicle().getOwner());// 业户名称处理
			// 展示时优先取业户名称字段，若业户名称字段为null，则取owner字段填充；
			if (StringUtils.isEmpty(vehicle.getOperatorName())) {
				if (!StringUtils.isEmpty(vehicle.getOwner())) {
					waybillResponse.getVehicle().setOperatorName(vehicle.getOwner());
				}
			} else {
				waybillResponse.getVehicle().setOperatorName(vehicle.getOperatorName());
			}

			// 3.1、获取车辆附件
			List<VehicleAttachmentInfo> vehicleFileList = vehicleAttachmentInfoService.selectVehicleAttachmentInfoByIds(vehicle.getAttachmentIds());
			waybillResponse.setVehicleAttachmentList(vehicleFileList.stream().map(FileModel::of).collect(Collectors.toList()));

		}

		// 4、获取收款人信息
		PayeeInfoWaybillSlaveDetail payeeInfo = JSON.parseObject(waybillSlave.getPayeeDetail(), PayeeInfoWaybillSlaveDetail.class);
		if (null != payeeInfo) {
			// 暂时不用判断是否跟托运人关联
			// CustomerPayeeRelation payeeRelation = customerPayeeRelationMapper.selectCustomerPayeeRelation(waybillResponse.getCustomerId(), payeeInfo.getId());
			waybillResponse.setIdentityCard(payeeInfo.getIdentityCard());
			waybillResponse.setPayeeName(payeeInfo.getPayeeName());
			waybillResponse.setBankCardNo(payeeInfo.getBankCardNo());
			waybillResponse.setBankName(payeeInfo.getBankName());
			waybillResponse.setBankCode(payeeInfo.getBankCode());
			waybillResponse.setBankMobile(payeeInfo.getBankMobile());
			// 4.1、获取收款人附件，将承运人附件也放入
			List<PayeeAttachmentInfo> payeeFileList = payeeAttachmentInfoService.selectPayeeAttachmentInfoByIds(payeeInfo.getAttachmentIds());
			carrierFiles.addAll(payeeFileList.stream().map(FileModel::of).collect(Collectors.toList()));
			waybillResponse.setPayeeAttachmentList(carrierFiles);
		}
	}

	private void setTransportCapicityInfo2(WaybillRes waybillResponse, WaybillSlave waybillSlave) {
		// 1、承运人信息
		Long payeeId = waybillResponse.getPayeeId();
		PayeeInfo payeeInfo1 = payeeInfoService.selectPayeeInfoById(payeeId);
		if (payeeInfo1 != null) {
			ActualCarrierInfo carrierInfo1 = actualCarrierInfoMapper.selectByIdentityCard(payeeInfo1.getIdentityCard());
			if (carrierInfo1 != null) {
				ActualCarrierWaybillSlaveDetail carrier = selectCarrier(carrierInfo1.getId());
				if (null != carrier) {
					waybillResponse.setActualCarrierName(carrier.getActualCarrierName());
					waybillResponse.setActualCarrierContactPhone(carrier.getContactPhone());
				}
			}
		}

		// 2、司机信息
		DriverWaybillSlaveDetail driver = JSON.parseObject(waybillSlave.getDriverDetail(), DriverWaybillSlaveDetail.class);
		if (null != driver) {
			waybillResponse.setDriverName(driver.getDriverName());
			waybillResponse.setTelephone(driver.getTelephone());
			waybillResponse.setDrivingLicense(driver.getDrivingLicense());
			waybillResponse.setDriver(JSON.parseObject(waybillSlave.getDriverDetail(), DriverRes.class));
		}

		// 3、车辆信息
		VehicleWaybillSlaveDetail vehicle = JSON.parseObject(waybillSlave.getVehicleDetail(), VehicleWaybillSlaveDetail.class);
		if (null != vehicle) {
			waybillResponse.setVehicleType(vehicle.getVehicleType());
			waybillResponse.setVehicleTypeName(vehicle.getVehicleTypeName());
			waybillResponse.setVehicleTonnage(vehicle.getVehicleTonnage().compareTo(BigDecimal.ZERO) < 0 ? null : vehicle.getVehicleTonnage());
			VehicleRes vehicleJson = JSON.parseObject(waybillSlave.getVehicleDetail(), VehicleRes.class);
			if (null != vehicleJson) {
				// 目前司机就两个状态 如果为空的话就说明未对司机做过审核 默认都是审核通过
				vehicleJson.setApproveState(null == vehicleJson.getApproveState() ? 2 : vehicleJson.getApproveState());
			}
			waybillResponse.setVehicle(vehicleJson);
			waybillResponse.getVehicle().setOwnerName(waybillResponse.getVehicle().getOwner());// 业户名称处理
			// 展示时优先取业户名称字段，若业户名称字段为null，则取owner字段填充；
			if (StringUtils.isEmpty(vehicle.getOperatorName())) {
				if (!StringUtils.isEmpty(vehicle.getOwner())) {
					waybillResponse.getVehicle().setOperatorName(vehicle.getOwner());
				}
			} else {
				waybillResponse.getVehicle().setOperatorName(vehicle.getOperatorName());
			}
		}

		// 4、获取收款人信息
		PayeeInfoWaybillSlaveDetail payeeInfo = JSON.parseObject(waybillSlave.getPayeeDetail(), PayeeInfoWaybillSlaveDetail.class);
		if (null != payeeInfo) {
			// 暂时不用判断是否跟托运人关联
			// CustomerPayeeRelation payeeRelation = customerPayeeRelationMapper.selectCustomerPayeeRelation(waybillResponse.getCustomerId(), payeeInfo.getId());
			waybillResponse.setIdentityCard(payeeInfo.getIdentityCard());
			waybillResponse.setPayeeName(payeeInfo.getPayeeName());
			waybillResponse.setBankCardNo(payeeInfo.getBankCardNo());
			waybillResponse.setBankName(payeeInfo.getBankName());
			waybillResponse.setBankCode(payeeInfo.getBankCode());
			waybillResponse.setBankMobile(payeeInfo.getBankMobile());
		}
	}

	private ActualCarrierWaybillSlaveDetail selectCarrier(Long carrierId) {
		ActualCarrierWaybillSlaveDetail carrier = new ActualCarrierWaybillSlaveDetail();
		ActualCarrierInfo carrierInfo = actualCarrierInfoMapper.selectActualCarrierInfoById(carrierId);
		if (carrierInfo != null) {
			carrier.setActualCarrierName(carrierInfo.getActualCarrierName());
			carrier.setContactPhone(carrierInfo.getContactPhone());
			List<ActualCarrierAttachmentInfo> actualCarrierAttachmentInfos = actualCarrierAttachmentInfoService.selectCarrierAttachmentList(carrierId);
			carrier.setAttachmentIds(actualCarrierAttachmentInfos.stream().map(ActualCarrierAttachmentInfo::getId).collect(Collectors.toList()));
		}
		return carrier;
	}

	/**
	 * 运单详情中的费用信息
	 *
	 * @param waybillResponse
	 * @param waybill
	 */
	private void setFareInfo(WaybillRes waybillResponse, Waybill waybill) {
		// 1、额外费用在运单相关费用中体现
		List<WaybillFare> fareList = waybillFareService.waybillFareList(waybill.getId());
		waybillResponse.setWaybillFareList(fareList.stream().map(WaybillFareRes::of).collect(Collectors.toList()));
		for (WaybillFare waybillFare : fareList) {
			if (waybillFare.getFareStage() == 0) {
				waybillResponse.setPayFare(waybillResponse.getPayFare().subtract(waybillFare.getFareMoney()));
			}
			if (waybillFare.getFareStage() == 1) {
				waybillResponse.setPrepayMoney(waybillResponse.getPrepayMoney().subtract(waybillFare.getFareMoney()));
			}
			if (waybillFare.getFareStage() == 2) {
				waybillResponse.setArriveMoney(waybillResponse.getArriveMoney().subtract(waybillFare.getFareMoney()));
			}
			if (waybillFare.getFareStage() == 3) {
				waybillResponse.setReceiptMoney(waybillResponse.getReceiptMoney().subtract(waybillFare.getFareMoney()));
			}
		}

		// 计算初始应付 这里需要显示带油卡金额所以都用totalFare
		BigDecimal payFareInitial = waybill.getTotalFare();
		for (WaybillFare waybillFare : fareList) {
			payFareInitial = payFareInitial.subtract(waybillFare.getFareMoney());
		}
		waybillResponse.setPayFareInitial(payFareInitial);
		waybillResponse.setPayFare(waybill.getTotalFare());

		// 获取运单应收相关费用
		WaybillReceiveFareInfo receiveFareInfo = this.calculateWaybillReceiveFare(waybill, SecurityUtils.getClientType());
		BeanUtils.copyProperties(receiveFareInfo, waybillResponse);
	}

	private List<SysAttachmentInfo> getContractAttachments(WaybillRes waybill) {
		List<SysAttachmentInfo> resList = new ArrayList<>();
		// 1、运单中的运输协议
		List<String> fileNames = new ArrayList<>();
		fileNames.add(FileNames.ECONTRACT);
		List<WaybillAttachmentInfo> contractAttachments = waybillAttachmentInfoService.selectWaybillAttachmentList(waybill.getId(), fileNames);
		if (CommonUtil.isNotNullOrEmpty(contractAttachments)) {
			resList = JSON.parseArray(JSON.toJSONString(contractAttachments), SysAttachmentInfo.class);
		}
		// 2、司机与网货人的协议
		/*DriverElectronicContract electronicContractSearch = new DriverElectronicContract();
		electronicContractSearch.setDriverIdentityCard(waybill.getDrivingLicense());
		electronicContractSearch.setFreightForwarderId(waybill.getFreightForwarderId());
		DriverElectronicContract electronicContract = driverElectronicContractMapper.selectEffectiveDriverElectronicContractByFreightForwarderId(electronicContractSearch);
		if (electronicContract != null && !CommonUtil.isNullOrEmpty(electronicContract.getFileUrl())) {
			String[] fileUrls = electronicContract.getFileUrl().split(",");
			for (String fileUrl : fileUrls) {
				SysAttachmentInfo attachmentInfo = new SysAttachmentInfo();
				attachmentInfo.setFileName("平台运输合同");
				attachmentInfo.setFileUrl(fileUrl);
				resList.add(attachmentInfo);
			}
		}
		if (SecurityUtils.getClientType().equals(ClientType.PLATFORM)) {
			if (null == SecurityUtils.getFreightForwarderId()) {
				return resList;
			}

			FreightForwarderInfo freightForwarderInfoExt = new FreightForwarderInfo();
			freightForwarderInfoExt.setCreditCode("91320830MADCRXEJ3Q");
			freightForwarderInfoExt.setState(0);
			// 查询出善道网络货运人id
			List<FreightForwarderInfo> freightForwarderInfoList = freightForwarderInfoService.selectFreightForwarderInfoList(freightForwarderInfoExt);
			// 根据司机身份证与网络货运人id查询出签约信息
			DriverElectronicContract driverElectronicContract = new DriverElectronicContract();
			driverElectronicContract.setDriverIdentityCard(waybill.getDrivingLicense());
			driverElectronicContract.setIsEffective(1);
			driverElectronicContract.setFreightForwarderId(SecurityUtils.getFreightForwarderId());
			DriverElectronicContract driverElectronicContractEx = driverElectronicContractMapper.selectEffectiveDriverElectronicContractByFreightForwarderId(driverElectronicContract);
			// 目前登录人，如果是属于善道网络货运平台的，那么查询签约合同的时候，添加判断。如果该司机没有和善道签约，则添加合同模版并传值给前端
			if (SecurityUtils.getFreightForwarderId() != null && !freightForwarderInfoList.isEmpty() && SecurityUtils.getFreightForwarderId().equals(freightForwarderInfoList.get(0).getId())
					&& null == driverElectronicContractEx) {
				resList.removeIf(info -> "平台运输合同".equals(info.getFileName()));
				// 如果该司机没有和善道签约，则添加合同模版并传值给前端 不实际存在 只返回给前端
				SysAttachmentInfo driverElectronicContractNew = new SysAttachmentInfo();
				// 写死的文件路径
				driverElectronicContractNew.setFileUrl("https://wlhyfile.56zly.com/江苏善道智运运输合同s_44978708293053341714294158432.pdf");
				driverElectronicContractNew.setState(0);
				driverElectronicContractNew.setFileName("平台运输合同");
				driverElectronicContractNew.setRelationId(waybill.getDriverId());
				driverElectronicContractNew.setOriginalFileName("江苏善道智运运输合同");
				driverElectronicContractNew.setFileType("application/pdf");
				driverElectronicContractNew.setKeyValue("~&~transportContract");
				driverElectronicContractNew.setFileSize("1000");
				resList.add(driverElectronicContractNew);
			}
		}*/
		return resList;
	}

	public void countTotalReceiveFare(WaybillRes waybillResponse, FrameworkContract fc) {
		if (fc == null) {
			waybillResponse.setTotalRreceiveFare(BigDecimal.ZERO);
		} else {
			BusinessModel businessModel = businessModelService.selectBusinessModelById(fc.getBusinessModelId());
			Long shipperId;
			if (ClientType.PLATFORM.equals(SecurityUtils.getClientType())) {
				FreightForwarderInfo forwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(SecurityUtils.getFreightForwarderId());
				shipperId = forwarderInfo.getCustomerId();
			} else {
				shipperId = SecurityUtils.getShipperId();
			}
			BigDecimal rate = waybillCustomerRelationMapperEx.queryRateByWaybillIdAndCustomerId(waybillResponse.getId(), shipperId);
			BigDecimal freightForwarderRate = waybillCustomerRelationMapperEx.queryFreightForwarderRateByWaybillId(waybillResponse.getId());

			if (businessModel != null) {
				switch (SecurityUtils.getClientType()) {
				case SHIPPER:
					if (Arrays.asList(1, 2, 3).contains(businessModel.getModelClassify())) {// 托盘
						// 应付/(1-费率)
						BigDecimal divide = waybillResponse.getActualFare().divide((BigDecimal.ONE.subtract(rate)), 2, RoundingMode.HALF_UP);
						waybillResponse.setTotalRreceiveFare(rate.compareTo(BigDecimal.ZERO) == 0 ? waybillResponse.getReceiveFare() : divide);
						waybillResponse.setReceiveFareFormula(rate.compareTo(BigDecimal.ZERO) == 0 ? "应收运费=货主货款" : "应收运费=实付/(1-费率)");
					} else if (businessModel.getModelClassify() == 4) {// 小雨点
						// 货主货款/(1-费率)
						BigDecimal bigDecimal = waybillResponse.getReceiveFare().multiply(rate).setScale(2, RoundingMode.HALF_UP);
						BigDecimal divide = waybillResponse.getReceiveFare().subtract(bigDecimal);
						waybillResponse.setTotalRreceiveFare(divide);
						waybillResponse.setReceiveFareFormula("应收运费=货主货款-货主货款*费率");
						waybillResponse.setIsRequiredReceiveFare(0);
					} else if (businessModel.getModelClassify() == 5) {// 下游垫资
						// 货主货款-利息-实付/（1-费率）*（费率-网络货运费率）
						// 货主货款-利息-实付/[（1-费率）/（费率-网络货运费率）]
						// BigDecimal divide = waybillResponse.getReceiveFare()
						// .subtract(waybillResponse.getActualFare().divide((BigDecimal.ONE.subtract(rate)), 2, BigDecimal.ROUND_HALF_UP).multiply((rate.subtract(freightForwarderRate))));

						BigDecimal divide = waybillResponse.getReceiveFare().subtract(
								waybillResponse.getActualFare().divide((BigDecimal.ONE.subtract(rate).divide(rate.subtract(freightForwarderRate), 2, RoundingMode.HALF_UP)), 2, RoundingMode.HALF_UP));
						waybillResponse.setTotalRreceiveFare(divide);
						waybillResponse.setIsRequiredReceiveFare(0);
						waybillResponse.setReceiveFareFormula("应收运费=货主货款-利息-实付/（1-费率）*（费率-网络货运费率）");
					} else if (businessModel.getModelClassify() == 6) {// 上游垫资
						// 货主货款
						waybillResponse.setTotalRreceiveFare(waybillResponse.getReceiveFare());
						waybillResponse.setIsRequiredReceiveFare(0);
						waybillResponse.setReceiveFareFormula("应收运费=货主货款");
					}
					break;
				case FINANCE:
					if (Arrays.asList(1, 2, 3).contains(businessModel.getModelClassify())) {// 托盘
						// 实付/(1-费率) 不显示货主货款
						BigDecimal divide = waybillResponse.getActualFare().divide((BigDecimal.ONE.subtract(rate)), 2, RoundingMode.HALF_UP);
						waybillResponse.setTotalRreceiveFare(divide);
						waybillResponse.setReceiveFareFormula("预计向上游收取的费用，应收运费=实付/(1-费率)");
						waybillResponse.setIsReceiveFare(1);
					} else if (businessModel.getModelClassify() == 4) {// 小雨点
						// 货主货款-货主货款*费率
						waybillResponse.setTotalRreceiveFare(waybillResponse.getReceiveFare());
						waybillResponse.setReceiveFareFormula("预计向上游收取的费用，应收运费=货主货款");
					} else if (businessModel.getModelClassify() == 5) {// 下游垫资
						// 货主货款
						waybillResponse.setTotalRreceiveFare(waybillResponse.getReceiveFare());
						waybillResponse.setReceiveFareFormula("预计向上游收取的费用，应收运费=货主货款");
					} else if (businessModel.getModelClassify() == 6) {// 上游垫资
						// 实付/(1-费率)
						BigDecimal divide = waybillResponse.getActualFare().compareTo(BigDecimal.ZERO) == 0 ?
								new BigDecimal(BigInteger.ZERO) :
								waybillResponse.getActualFare().divide((BigDecimal.ONE.subtract(rate)), 2, RoundingMode.HALF_UP);
						waybillResponse.setTotalRreceiveFare(divide);
						waybillResponse.setReceiveFareFormula("预计向上游收取的费用，应收运费=实付/(1-费率)+利息");
					}
					break;
				case PLATFORM:
					// 实付/(1-费率) 不显示货主货款
					BigDecimal divide = waybillResponse.getActualFare().divide((BigDecimal.ONE.subtract(freightForwarderRate)), 2, RoundingMode.HALF_UP);
					waybillResponse.setTotalRreceiveFare(divide);
					waybillResponse.setIsReceiveFare(1);
					waybillResponse.setReceiveFareFormula("应收运费=实付/(1-费率)");
					break;
				default:
					break;
				}
			}
		}
	}

	public void countTotalReceiveFareToMe(WaybillRes waybillResponse) {
		Long shipperId = SecurityUtils.getShipperId();

		// 给我的费率
		BigDecimal rate = waybillCustomerRelationMapperEx.queryRateToMeByWaybillIdAndCustomerId(waybillResponse.getId(), shipperId);

		// 实付/(1-费率)
		BigDecimal divide = waybillResponse.getActualFare().divide((BigDecimal.ONE.subtract(rate)), 2, RoundingMode.HALF_UP);
		waybillResponse.setTotalRreceiveFare(divide);
		waybillResponse.setReceiveFareFormula("应收运费=实付/(1-费率)");

	}

	/**
	 * 计算运单应收相关信息
	 *
	 * @param waybill
	 * @param clientType
	 * @return
	 */
	private WaybillReceiveFareInfo calculateWaybillReceiveFare(Waybill waybill, ClientType clientType) {

		// 应收运费(元)
		BigDecimal receiveFare = null;
		// 应收单价
		BigDecimal feePrice = null;
		// 应收数量
		BigDecimal feeAmount = null;
		// 应收单位(1.吨 2.立方米 3.车)
		Integer feeUnit = waybill.getFeeUnit();
		// 转包费率
		BigDecimal rate = null;
		// 对账用的应收运费(开票金额)
		BigDecimal statementReceiveFare = null;

		if (clientType == ClientType.SHIPPER) { // 托运人访问
			// 获取有效的转包链条，获取运单转包费率，计算与上游的应收
			WaybillContractChain chain = waybillContractChainService.findById(waybill.getId());
			if (null != chain && StringUtils.isNotBlank(chain.getContractChain())) {
				List<FrameworkContractSubchain> fcsList = JSON.parseArray(chain.getContractChain(), FrameworkContractSubchain.class);
				FrameworkContract frameworkContract = frameworkContractService.selectFrameworkContractById(waybill.getFrameworkContractId());
				if (3 == frameworkContract.getContractType()) {
					// 如果是网络货运人直签项目，取最后一级网络货运人设置的费率（自己转包给自己）
					for (FrameworkContractSubchain fcs : fcsList) {
						if (0 == fcs.getIsLeaf()) {
							rate = fcs.getRate();
							break;
						}
					}
				} else {
					long customerId = SecurityUtils.getLoginUser().getTenantUser().getTenantId();
					// 如果不是直签项目，取本级给上游设置的转包费率
					for (FrameworkContractSubchain fcs : fcsList) {
						if (fcs.getCustomerId().equals(customerId)) {
							if (0 == fcs.getParentCustomerId()) {
								break;
							}
							if (!fcs.getParentCustomerId().equals(customerId)) {
								// 本级给上游设置的转包费率
								rate = fcs.getRate();
								break;
							}
						}
					}
				}
				// 计算转包运单应收（应收=应付/(1-费率)）
				if (null != rate) {
					if (0 == rate.compareTo(BigDecimal.ONE)) {
						statementReceiveFare = BigDecimal.ZERO;
					} else {
						statementReceiveFare = waybill.getPayFare().divide(BigDecimal.ONE.subtract(rate), 2, RoundingMode.HALF_UP);
					}
				}
			}
		}

		// 网络货运人，获取转包费率，计算应收
		if (clientType == ClientType.PLATFORM) {
			WaybillContractChain chain = waybillContractChainService.findById(waybill.getId());
			if (chain != null) {
				List<FrameworkContractSubchain> frameworkContractSubchains = JSON.parseArray(chain.getContractChain(), FrameworkContractSubchain.class);
				for (FrameworkContractSubchain frameworkContractSubchain : frameworkContractSubchains) {
					if (0 == frameworkContractSubchain.getIsLeaf()) {
						rate = frameworkContractSubchain.getRate();
						receiveFare = waybill.getPayFare().divide((BigDecimal.ONE.subtract(rate)), 2, RoundingMode.HALF_UP);
						statementReceiveFare = receiveFare;
						break;
					}
				}
			}
		}

		WaybillReceiveFareInfo receiveFareInfo = new WaybillReceiveFareInfo();
		receiveFareInfo.setFeeUnit(feeUnit);
		receiveFareInfo.setFeePrice(null == feePrice ? waybill.getFeePrice() : feePrice);
		receiveFareInfo.setFeeAmount(null == feeAmount ? waybill.getFeeAmount() : feeAmount);
		receiveFareInfo.setReceiveFare(null == receiveFare ? waybill.getReceiveFare() : receiveFare);
		receiveFareInfo.setRate(rate);
		receiveFareInfo.setStatementReceiveFare(statementReceiveFare);
		return receiveFareInfo;
	}

	/**
	 * 获取运单操作记录
	 *
	 * @param waybill
	 * @return
	 */
	private List<WaybillOperation> getOperationList(Waybill waybill) {

		List<WaybillOperation> operationList = new ArrayList<WaybillOperation>();
		// 运输合同操作记录
		if (waybill.getStatus() > BusinessConstants.WAYBILL_STATUS_ORDER) {
			WaybillOperation operation = new WaybillOperation();
			operation.setOperationTime(DateUtils.asString(waybill.getUpdateTime()));
			operation.setOperationContent("订单已提交，待司机确认");
			operation.setOperator(waybill.getCreateBy());
			operationList.add(operation);
			if (waybill.getStatus() > BusinessConstants.WAYBILL_STATUS_WAYBILL) {
				operation = new WaybillOperation();
				operation.setOperationTime(DateUtils.asString(waybill.getSignTime()));
				operation.setOperationContent("司机已在线签约，查看附件");
				operation.setOperator(waybill.getDriverName());
				operationList.add(operation);
			}
		}
		return operationList;
	}

	/**
	 * 批量删除运单信息
	 */
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult removeWaybillByIds(Long[] ids) {
		if (StringUtils.isEmpty(ids)) {
			return AjaxResult.requestError("参数错误");
		}
		List<Long> waybillIds = Arrays.stream(ids).filter(id -> id > 0).collect(Collectors.toList());
		if (waybillIds.isEmpty()) {
			return AjaxResult.requestError("参数错误");
		}
		List<WaybillRes> waybillList = waybillMapperEx.selectWaybillListByWaybillIds(Arrays.asList(ids));
		// 已申请付款的运单不可删除
		if (existPaymentApply(waybillIds)) {
			return AjaxResult.requestError(String.format("%s运单已申请付款，不可删除", waybillIds.size() > 1 ? "有" : ""));
		}
		// 已进入开票环节运单不可删除
		List<WaybillRes> invoicingWaybills = waybillList.stream().filter(w -> w.getBillStatus() == 1 || w.getBillStatus() == 2 || w.getBillStatus() == 3).collect(Collectors.toList());
		if (CommonUtil.isNotNullOrEmpty(invoicingWaybills)) {
			return AjaxResult.requestError(String.format("%s运单已进入开票环节，不可删除", invoicingWaybills.size() > 1 ? "有" : ""));
		}
		// 已申请付款的运单不可删除
		/*if (waybillIsLocate(waybillIds)) {
			return AjaxResult.requestError(String.format("%s运单北斗定位中，不可删除", waybillIds.size() > 1 ? "有" : ""));
		}*/
		// 删除运单后需调整项
		for (WaybillRes w : waybillList) {
			// 更新调度计划中的待调车数量
			if (!CommonUtil.isEmptyOrZero(w.getOperationPlainId())) {
				OperationPlain operationPlain = operationPlainService.selectOperationPlainById(w.getOperationPlainId());
				operationPlain.setLastSum(operationPlain.getLastSum() + 1);
				operationPlainService.updateOperationPlain(operationPlain);
			}
			// 导入的运单，更新缓存中数据
			if (w.getResource() == WaybillSource.IMPORT.val()) {
				String planStartTime = DateUtils.getFormatDateTime(w.getDespatchActualDateTime(), DateUtils.YYSS);
				String planEndTime = DateUtils.getFormatDateTime(w.getGoodsReceiptDateTime(), DateUtils.YYSS);
				String mapKey = String.join("_", CacheConstants.WAYBILL_IMPORT, w.getVehicleNumber(), StringUtils.isoTime(planStartTime), StringUtils.isoTime(planEndTime));
				redisCache.deleteObject(mapKey);
			}
		}

		// 校验完成，批量删除运单
		int count = waybillMapperEx.removeWaybillByIds(ids, SecurityUtils.getNickname());
		// 逻辑删除es中运单
		List<Waybill> waybills = waybillMapper.selectWaybillByIds(Arrays.asList(ids));
		waybills.forEach(a -> {
			paymentRecordService.delPaymentRecordByWaybillId(a.getId());
			a.setStatus(-1);
		});
		esWaybillService.updateEsWaybillInfos(waybills);

		log.info("成功删除运单 {} 条", count);

		// 拼接运单号
		StringJoiner waybillCode = new StringJoiner(" ");
		waybillList.stream().map(WaybillRes::getShippingNoteNumber).forEach(waybillCode::add);
		String actionName = "删除运单(运单ID：" + StringUtils.join(ids, ",") + "，运单号：" + waybillCode + ")";

		sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_WAYBILL, BusinessConstants.ACTION_TYPE_DELETE, StringUtils.substring(actionName, 0, 5000), waybillCode.toString());
		return AjaxResult.success();
	}

	private boolean waybillIsLocate(List<Long> waybillIds) {
		return waybillMapperEx.waybillIsLocate(waybillIds);
	}

	/**
	 * 查询各状态的运单数量
	 *
	 * @param status
	 * @return
	 */
	public Integer getStatusCount(Integer status) {
		Integer count = waybillMapperEx.getStatusCount(status);
		return null == count ? 0 : count;
	}

	public AjaxResult selectUnSettlementWaybillInfoV2(Long waybillId) {
		Waybill waybill = this.selectWaybillById(waybillId);
		return selectUnSettlementWaybillInfoV2ByWaybill(waybill);
	}

	public AjaxResult selectUnSettlementWaybillInfoV2ByWaybill(Waybill waybill) {
		if (waybill == null) {
			throw new RuntimeException("没有查询到运单");
		}
		if (BusinessConstants.STATE_DELETE == waybill.getStatus()) {
			throw new RuntimeException("运单已删除，请刷新后重新操作");
		}

		// 得到付款类目和原始的金额和未付金额计算出未付金额
		JSONObject jsonObject = new JSONObject();
		if (waybill.getPayType().equals(0)) {
			jsonObject.put("payStage", 0);
			jsonObject.put("payMoney", waybill.getUnpaidFare());
		} else {
			for (int i = 1; i <= 3; i++) {
				// 计算出未付金额
				BigDecimal unMoney = (i == 1 && waybill.getPrepayType() == 1 ?
						waybill.getPrepayUnpaidMoney() :
						(i == 2 ? waybill.getArriveUnpaidMoney() : (i == 3 ? waybill.getReceiptUnpaidMoney() : BigDecimal.ZERO)));
				if (unMoney.compareTo(BigDecimal.ZERO) > 0) {
					jsonObject.put("payStage", i);
					jsonObject.put("payMoney", unMoney);
					break;
				}
			}
		}
		jsonObject.put("waybill", waybill);
		return AjaxResult.success(jsonObject);
	}

	/**
	 * 查询 当前司机的 累计票数
	 *
	 * @param driverId
	 * @return
	 */
	public int selectSumWayBill(Long driverId) {
		return waybillMapperEx.selectSumWayBill(driverId);
	}

	/**
	 * 根据司机id 查询运费总计
	 *
	 * @param driverId
	 * @return
	 */
	public BigDecimal selectSumReceiveMoney(Long driverId) {
		return waybillMapperEx.selectSumReceiveMoney(driverId);
	}

	/**
	 * 根据司机id 查询待收款总计
	 *
	 * @param driverId
	 * @return
	 */
	public BigDecimal selectWaitReceiveMoney(Long driverId) {
		return waybillMapperEx.selectWaitReceiveMoney(driverId);
	}

	/**
	 * 根据司机id 查询已收款总计
	 *
	 * @param driverId
	 * @return
	 */
	public BigDecimal selectOkReceiveMoney(Long driverId) {
		return waybillMapperEx.selectOkReceiveMoney(driverId);
	}

	/**
	 * 司机是否有运单进行中（未开始或运输中）
	 *
	 * @param driverId
	 * 		司机ID
	 * @return
	 */
	public boolean driverInTransport(long driverId, Long customerId) {
		Waybill driverQuery = new Waybill();
		driverQuery.setDriverId(driverId);
		driverQuery.setCustomerId(customerId);
		int count = waybillMapperEx.countWaybillOnTransport(driverQuery);
		return count > 0;
	}

	/**
	 * 司机是否有运单运输中
	 *
	 * @param driverId
	 * 		司机ID
	 * @return
	 */
	public boolean driverIsTransporting(long driverId, Long customerId) {
		Waybill driverQuery = new Waybill();
		driverQuery.setDriverId(driverId);
		driverQuery.setCustomerId(customerId);
		int count = waybillMapperEx.countWaybillOnTransporting(driverQuery);
		return count > 0;
	}

	/**
	 * 车辆是否有运单进行中（未开始或运输中）
	 *
	 * @param vehicleId
	 * 		车辆ID
	 * @return
	 */
	public boolean vehicleInTransport(long vehicleId, Long customerId) {
		Waybill vehicleQuery = new Waybill();
		vehicleQuery.setVehicleId(vehicleId);
		vehicleQuery.setCustomerId(customerId);
		int count = waybillMapperEx.countWaybillOnTransport(vehicleQuery);
		return count > 0;
	}

	/**
	 * 车辆是否有运单运输中
	 *
	 * @param vehicleId
	 * 		车辆ID
	 * @return
	 */
	public boolean vehicleIsTransporting(long vehicleId, Long customerId) {
		Waybill vehicleQuery = new Waybill();
		vehicleQuery.setVehicleId(vehicleId);
		vehicleQuery.setCustomerId(customerId);
		int count = waybillMapperEx.countWaybillOnTransporting(vehicleQuery);
		return count > 0;
	}

	/**
	 * 数据校验
	 *
	 * @param req
	 * @return
	 */
	private CommonResult verify(WaybillReq req) {
		if (StringUtils.isNull(req)) {
			return CommonResult.error("数据对象为空");
		}
		BeanUtils.beanAttributeValueTrim(req);// 去掉该对象中string字段前后空格
		log.info("运单信息参数：" + JSON.toJSONString(req));
		ConsignorInfo consignorInfo = req.getConsignorInfo();
		ConsigneeInfo consigneeInfo = req.getConsigneeInfo();
		// 提交运单才去校验
		if (req.getStatus() >= BusinessConstants.WAYBILL_STATUS_WAYBILL) {
			// 校验操作类型,只能保存订单、提交订单或签约运单
			if (StringUtils.isNull(
					req.getStatus()) || (req.getStatus() != BusinessConstants.WAYBILL_STATUS_ORDER && req.getStatus() != BusinessConstants.WAYBILL_STATUS_WAYBILL && req.getStatus() != BusinessConstants.WAYBILL_STATUS_LEASE)) {
				return CommonResult.error("无效操作");
			}
			if (StringUtils.isNull(req.getOrderCreateTime())) {
				return CommonResult.error("开单时间为空");
			}
			if (CommonUtil.isEmptyOrZero(req.getCustomerId())) {
				return CommonResult.error("客户信息为空");
			}
			if (StringUtils.isBlank(req.getBusinessTypeCode())) {
				return CommonResult.error("业务类型为空");
			}
			List<SysDictData> dictList = dictTypeService.selectDictDataByType(DictType.BUSINESS_TYPE);// 从数据字典获取业务类型
			if (StringUtils.isNull(dictList) || dictList.isEmpty()) {
				return CommonResult.error("业务类型未配置");
			}
			if (StringUtils.isBlank(req.getBusinessTypeCode()) || dictList.stream().noneMatch(a -> req.getBusinessTypeCode().equals(a.getDictValue()))) {
				return CommonResult.error("业务类型不匹配");
			}
			// 校验发货方
			if (null == consignorInfo || null == consignorInfo.getId()) {
				return CommonResult.error("发货方为空");
			}
			if (StringUtils.isBlank(consignorInfo.getConsignor())) {
				return CommonResult.error("发货方名称为空");
			}
			if (StringUtils.isBlank(consignorInfo.getProvince()) || StringUtils.isBlank(consignorInfo.getCity()) || StringUtils.isBlank(consignorInfo.getArea())) {
				return CommonResult.error("发货方省市区为空");
			}
			if (StringUtils.isBlank(consignorInfo.getPlaceOfLoading())) {
				return CommonResult.error("发货方地址为空");
			}
			// 校验收货方
			if (null == consigneeInfo || null == consigneeInfo.getId()) {
				return CommonResult.error("收货方为空");
			}
			if (StringUtils.isBlank(consigneeInfo.getConsignee())) {
				return CommonResult.error("收货方名称为空");
			}
			if (StringUtils.isBlank(consigneeInfo.getProvince()) || StringUtils.isBlank(consigneeInfo.getCity()) || StringUtils.isBlank(consigneeInfo.getArea())) {
				return CommonResult.error("收货方省市区为空");
			}
			if (StringUtils.isBlank(consigneeInfo.getGoodsReceiptPlace())) {
				return CommonResult.error("收货方地址为空");
			}
			List<SysDictData> cargoList = dictTypeService.selectDictDataByType(DictType.CARGO_TYPE);// 从数据字典获取货物类别
			if (StringUtils.isNull(cargoList) || cargoList.isEmpty()) {
				return CommonResult.error("货物类别未配置");
			}
			List<SysDictData> packagingList = dictTypeService.selectDictDataByType(DictType.PACKING_TYPE);// 从数据字典获取包装类型
			if (StringUtils.isNull(packagingList) || packagingList.isEmpty()) {
				return CommonResult.error("包装类型未配置");
			}

			MakeCode makeCode = req.getMakeCode();
			if (StringUtils.isBlank(makeCode.getCargoValue()) || cargoList.stream().noneMatch(a -> makeCode.getCargoValue().equals(a.getDictValue()))) {
				return CommonResult.error("货物类别不匹配");
			}
			if (StringUtils.isBlank(makeCode.getCargoLabel())) {
				return CommonResult.error("货物类别名称为空");
			}
			if (StringUtils.isBlank(makeCode.getCargoName())) {
				return CommonResult.error("货物名称为空");
			}

		}
		// 只针对PC端校验
		if (WaybillSource.PC.val() == req.getResource()) {
			if (StringUtils.isNull(req.getDespatchActualDateTime())) {
				return CommonResult.error("提货时间为空");
			}
			if (StringUtils.isNull(req.getGoodsReceiptDateTime())) {
				return CommonResult.error("预计送达时间为空");
			}
			// 校验运费
			if (StringUtils.isNull(req.getReceiveFare()) || req.getReceiveFare().compareTo(BigDecimal.ZERO) < 1) {
				return CommonResult.error("应收运费为空");
			}
			if (StringUtils.isNull(req.getFeePrice()) || req.getFeePrice().compareTo(BigDecimal.ZERO) < 1) {
				return CommonResult.error("应收单价为空");
			}
			if (StringUtils.isNull(req.getFeeAmount()) || req.getFeeAmount().compareTo(BigDecimal.ZERO) < 1) {
				return CommonResult.error("数量为空");
			}
			if (StringUtils.isNull(
					req.getFeeUnit()) || (req.getFeeUnit() != BusinessConstants.WAYBILL_FEE_UNIT_TONNE && req.getFeeUnit() != BusinessConstants.WAYBILL_FEE_UNIT_STERE && req.getFeeUnit() != BusinessConstants.WAYBILL_FEE_UNIT_CARLOAD)) {
				return CommonResult.error("单位不匹配");
			}
			if (!PayType.contains(req.getPayType())) {
				return CommonResult.error("支付计划不匹配");
			}
			if (StringUtils.isNull(req.getPayFare()) || req.getPayFare().compareTo(BigDecimal.ZERO) < 1) {
				return CommonResult.error("应付运费为空");
			}
			if (PayType.PHASE.equals(req.getPayType()) && (StringUtils.isNull(req.getPrepayMoney()) || req.getPrepayMoney().compareTo(BigDecimal.ZERO) < 1)) {
				return CommonResult.error("预付金额为空");
			}
			if (!PrepayType.contains(req.getPrepayType())) {
				return CommonResult.error("预付类型不匹配");
			}
			if (PayType.PHASE.equals(req.getPayType()) && PrepayType.OILCARD.equals(req.getPrepayType()) && StringUtils.isBlank(req.getOilCardNo())) {
				return CommonResult.error("油卡卡号为空");
			}
			if (PayType.PHASE.equals(req.getPayType()) && (StringUtils.isNull(req.getArriveMoney()) || req.getArriveMoney().compareTo(BigDecimal.ZERO) < 1)) {
				return CommonResult.error("到付金额为空");
			}
			if (PayType.PHASE.equals(req.getPayType()) && (StringUtils.isNull(req.getReceiptMoney()) || req.getReceiptMoney().compareTo(BigDecimal.ZERO) < 1)) {
				return CommonResult.error("回单付金额为空");
			}
			// 校验承运人信息
			if ((StringUtils.isNull(req.getVehicleId()) || StringUtils.isBlank(req.getVehicleNumber())) && 1 == req.getResource()) {
				return CommonResult.error("车辆信息为空");
			}
			if ((StringUtils.isNull(req.getDriverId()) || StringUtils.isBlank(req.getDriverName())) && 1 == req.getResource()) {
				return CommonResult.error("司机信息为空");
			}
			if ((StringUtils.isBlank(req.getTelephone())) && 1 == req.getResource()) {
				return CommonResult.error("司机电话为空");
			}
			if ((StringUtils.isBlank(req.getDrivingLicense())) && 1 == req.getResource()) {
				return CommonResult.error("司机驾驶证号为空");
			}
			if ((StringUtils.isNull(req.getPayeeId()) || StringUtils.isBlank(req.getPayeeName())) && 1 == req.getResource()) {
				return CommonResult.error("收款人信息为空");
			}
			if ((StringUtils.isBlank(req.getBankCardNo())) && 1 == req.getResource()) {
				return CommonResult.error("收款人银行卡号为空");
			}
			if ((StringUtils.isBlank(req.getBankName())) && 1 == req.getResource()) {
				return CommonResult.error("收款人开户银行为空");
			}
			if ((StringUtils.isBlank(req.getIdentityCard())) && 1 == req.getResource()) {
				return CommonResult.error("收款人身份证号为空");
			}
			// 如果是承运人模式，要校验承运人姓名和收款人姓名是否一致
			if (StringUtils.isNotNull(req.getActualCarrierId()) && req.getActualCarrierId() > 0L && 1 == req.getResource()) {
				if (StringUtils.isBlank(req.getActualCarrierName())) {
					return CommonResult.error("承运人姓名为空");
				}
				if (StringUtils.isBlank(req.getActualCarrierContactPhone())) {
					return CommonResult.error("承运人电话为空");
				}
				if (!req.getPayeeName().equals(req.getActualCarrierName())) {
					return CommonResult.error("承运人和收款人姓名不一致");
				}
			}
			// 如果是善道的业务则需要进行辅助员判定拦截
			FrameworkContract contract = frameworkContractService.selectFrameworkContractById(req.getFrameworkContractId());
			String phone = auxiliaryStaffInfoMapper.selectPhoneByIdentityCard(req.getDrivingLicense());
			if (StringUtils.isNotBlank(phone)) {
				if (!auxiliaryStaffInfoService.validateShandaoRelationIsOk(contract.getFreightForwarderId(), req.getDrivingLicense(), null)) {
					return CommonResult.error("关联辅助员未与平台签约，无法接单，请联系平台");
				}
			}
		}
		// 校验承运车辆是不是和本次运单行程有冲突
		if (req.getVehicleId() != null && this.vehicleInTransport(req.getVehicleId(), req.getCustomerId())) {
			return CommonResult.error("该车辆有已签约或运输中的运单，请先完成后再操作");
		}
		// 校验承运司机是不是和本次运单行程有冲突
		if (req.getDriverId() != null && this.driverInTransport(req.getDriverId(), req.getCustomerId())) {
			return CommonResult.error("该司机目前有已签约或运输中的运单，请先完成后再操作");
		}

		return CommonResult.success();
	}

	/**
	 * 托运人我录的运单列表
	 *
	 * @param waybillQuery
	 * @return
	 */
	public TableInfo<WaybillTableRes> selectShipperWaybillPageList(WaybillQuery waybillQuery) {
		List<WaybillTableRes> list = selectShipperWaybillList(waybillQuery);
		Long count;
		if (CommonUtil.isNotNullOrEmpty(waybillQuery.getRemark())) {
			try {
				// 查询备注，要睡眠3秒，别问为什么。
				Thread.sleep(3000);
			} catch (InterruptedException e) {
				throw new RuntimeException(e);
			}
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (CommonUtil.isNotNullOrEmpty(waybillQuery.getMakeCodeIds())) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (CommonUtil.isNotNullOrEmpty(waybillQuery.getBillDateStart()) && CommonUtil.isNotNullOrEmpty(waybillQuery.getBillDateEnd())) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (CommonUtil.isNotNullOrEmpty(waybillQuery.getCreateBy())) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (CommonUtil.isNotNullOrEmpty(waybillQuery.getDespatchActualDateTimeStart()) && CommonUtil.isNotNullOrEmpty(waybillQuery.getDespatchActualDateTimeEnd())) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (CommonUtil.isNotNullOrEmpty(waybillQuery.getGoodsReceiptDateTimeStart()) && CommonUtil.isNotNullOrEmpty(waybillQuery.getGoodsReceiptDateTimeEnd())) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (CommonUtil.isNotNullOrEmpty(waybillQuery.getActualEndTimeStart()) && CommonUtil.isNotNullOrEmpty(waybillQuery.getActualEndTimeEnd())) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (CommonUtil.isNotNullOrEmpty(waybillQuery.getActualStartTimeStart()) && CommonUtil.isNotNullOrEmpty(waybillQuery.getActualStartTimeEnd())) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (CommonUtil.isNotNullOrEmpty(waybillQuery.getPlaceOfLoading()) || CommonUtil.isNotNullOrEmpty(waybillQuery.getGoodsReceiptPlace())) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (null != waybillQuery.getFeePriceStart() || null != waybillQuery.getFeePriceEnd()) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (null != waybillQuery.getPayApplyStatus()) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (null != waybillQuery.getDescriptionOfGoods()) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (null != waybillQuery.getShippingNoteNumbers()) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (null != waybillQuery.getConfirmReceiptState()) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (null != waybillQuery.getOrderCreateTimeStart() || null != waybillQuery.getOrderCreateTimeEnd()) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (null != waybillQuery.getTransportationAgreementState()) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (null != waybillQuery.getConsignor() || null != waybillQuery.getConsignee()) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (CommonUtil.isNotNullOrEmpty(waybillQuery.getPayeeIdentityCard())) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else if (null != waybillQuery.getReceiptStatus()) {
			count = waybillMapperEx.countShipperWaybillList(waybillQuery);
		} else {
			count = esWaybillService.countWaybillInfo(esWaybillService.generateReq(waybillQuery, 0));
		}
		return new TableInfo<>(list, count);
	}

	/**
	 * 托运人我录的运单列表
	 *
	 * @param waybillQuery
	 * @return
	 */
	public List<WaybillTableRes> selectShipperWaybillList(WaybillQuery waybillQuery) {
		dealWithWaybillQueryReq(waybillQuery);
		// 数据库查询
		List<WaybillTableRes> list = waybillMapperEx.selectShipperWaybillList(waybillQuery);

		// 货源处理：如果适用范围为3，且货源为自动生成，则不显示
		list.forEach(l -> {
			if (l.getUseScope() == 3 && (l.getId() + "货源").equals(l.getNameAlias())) {
				l.setNameAlias("");
			}
		});
		// 处理开票信息相关
		Map<Long, WaybillTableRes> invoiceMap = new HashMap<>();
		if (!list.isEmpty()) {
			List<Long> waybillIds = list.stream().map(WaybillTableRes::getId).collect(Collectors.toList());
			List<WaybillTableRes> invoiceList = financeStatementBillMapperEx.selectInvoiceNumbersByWaybillIds(waybillIds, waybillQuery.getCustomerId(), null);
			if (null != invoiceList)
				invoiceMap = invoiceList.stream().collect(Collectors.toMap(WaybillTableRes::getWaybillId, Function.identity()));
		}
		for (WaybillTableRes w : list) {
			// 现在payFare不带油卡费用,所以用带油卡的totalFare显示
			w.setPayFare(w.getTotalFare());
			if (w.getWaybillState() == 1 || w.getWaybillState() == 2 || w.getWaybillState() == 3) {
				w.setBillStatus(0);// waybillState 运单状态：1 未完成付款不可申请开票 2 可申请开票 3 申请开票中 4开票完成
			} else if (w.getWaybillState() == 4) {
				w.setBillStatus(2);// billStatus 开票状态(0.未开票 1.部分开票 2.已开票 3.开票中 4.撤销开票 5.驳回开票)
			}
			// status 运单状态(0订单 1运单 2已签约(未开始) 3运输中 4已完成 -1删除)
			// transport 运输状态(0:未开始 1:在途中 2:已完成)
			if (Arrays.asList(0, 1, 2).contains(w.getStatus())) {
				w.setTransportStatus(0);
			} else if (3 == w.getStatus()) {
				w.setTransportStatus(1);
			} else if (4 == w.getStatus()) {
				w.setTransportStatus(2);
			}
			if (w.getPolicyType() == -1) {
				w.setPolicyStatusString("未设置");
			} else {
				w.setPolicyStatusString(policyStatusMap.get(w.getPolicyStatus()) + "-" + policyTypeMap.get(w.getPolicyType()));
			}

			List<String> errMsg = new ArrayList<>();
			if (w.getVehicleInfoIsComplete() == 1) {
				errMsg.add("车辆信息不齐全");
			}
			if (w.getDriverInfoIsComplete() == 1) {
				errMsg.add("司机信息不齐全");
			}
			w.setErrMsgs(errMsg);

			// 设置装货数量、卸货数量、涨亏数量等的格式化显示
			this.setWaybillAmountDisplayInfo(w);

			// 处理上游费率展示
			if (BigDecimal.ZERO.compareTo(w.getRateByMe()) == 0) {
				w.setReceiveUpstream("——");
			}
			if (w.getIsCompleteTrajectory() == 1 && w.getLocateStatus() == 2) {
				w.setLocateStatus(6);
			}
			// 处理开票
			if (invoiceMap.containsKey(w.getId())) {
				WaybillTableRes invoice = invoiceMap.get(w.getId());
				w.setInvoiceNumber(invoice.getInvoiceNumber());
				w.setInvoiceTime(invoice.getInvoiceTime());
			}

			// 处理申请付款状态
			if (Arrays.asList(0, 9).contains(w.getPayApplyStatus())) {
				w.setPayApplyStatus(0);
			}
			if (Arrays.asList(2, 3, 4).contains(w.getPayApplyStatus())) {
				w.setPayApplyStatus(2);
			}

			// 处理地址
			w.setPlaceOfLoading(getAddress(w.getConsignorProvince(), w.getConsignorCity(), w.getConsignorArea(), w.getPlaceOfLoading()));
			w.setGoodsReceiptPlace(getAddress(w.getConsigneeProvince(), w.getConsigneeCity(), w.getConsigneeArea(), w.getGoodsReceiptPlace()));

			w.setConsignorAddersDetails(waybillService.getAllAddress(w.getConsignorProvince(), w.getConsignorCity(), w.getConsignorArea(), w.getPlaceOfLoading()));
			w.setConsigneeAddersDetails(waybillService.getAllAddress(w.getConsigneeProvince(), w.getConsigneeCity(), w.getConsigneeArea(), w.getGoodsReceiptPlace()));
		}
		return list;
	}


	public List<WaybillTableRes> selectShipperWaybillListSP(WaybillQuery waybillQuery) {
		dealWithWaybillQueryReq(waybillQuery);
		// 数据库查询
		List<WaybillTableRes> list = waybillMapperEx.selectShipperWaybillListSP(waybillQuery);

		// 货源处理：如果适用范围为3，且货源为自动生成，则不显示
		list.forEach(l -> {
			if (l.getUseScope() == 3 && (l.getId() + "货源").equals(l.getNameAlias())) {
				l.setNameAlias("");
			}
		});
		// 处理开票信息相关
		Map<Long, WaybillTableRes> invoiceMap = new HashMap<>();
		if (!list.isEmpty()) {
			List<Long> waybillIds = list.stream().map(WaybillTableRes::getId).collect(Collectors.toList());
			List<WaybillTableRes> invoiceList = financeStatementBillMapperEx.selectInvoiceNumbersByWaybillIds(waybillIds, waybillQuery.getCustomerId(), null);
			if (null != invoiceList)
				invoiceMap = invoiceList.stream().collect(Collectors.toMap(WaybillTableRes::getWaybillId, Function.identity()));
		}
		for (WaybillTableRes w : list) {
			// 现在payFare不带油卡费用,所以用带油卡的totalFare显示
			w.setPayFare(w.getTotalFare());
			if (w.getWaybillState() == 1 || w.getWaybillState() == 2 || w.getWaybillState() == 3) {
				w.setBillStatus(0);// waybillState 运单状态：1 未完成付款不可申请开票 2 可申请开票 3 申请开票中 4开票完成
			} else if (w.getWaybillState() == 4) {
				w.setBillStatus(2);// billStatus 开票状态(0.未开票 1.部分开票 2.已开票 3.开票中 4.撤销开票 5.驳回开票)
			}
			// status 运单状态(0订单 1运单 2已签约(未开始) 3运输中 4已完成 -1删除)
			// transport 运输状态(0:未开始 1:在途中 2:已完成)
			if (Arrays.asList(0, 1, 2).contains(w.getStatus())) {
				w.setTransportStatus(0);
			} else if (3 == w.getStatus()) {
				w.setTransportStatus(1);
			} else if (4 == w.getStatus()) {
				w.setTransportStatus(2);
			}
			if (w.getPolicyType() == -1) {
				w.setPolicyStatusString("未设置");
			} else {
				w.setPolicyStatusString(policyStatusMap.get(w.getPolicyStatus()) + "-" + policyTypeMap.get(w.getPolicyType()));
			}

			List<String> errMsg = new ArrayList<>();
			if (w.getVehicleInfoIsComplete() == 1) {
				errMsg.add("车辆信息不齐全");
			}
			if (w.getDriverInfoIsComplete() == 1) {
				errMsg.add("司机信息不齐全");
			}
			w.setErrMsgs(errMsg);

			// 设置装货数量、卸货数量、涨亏数量等的格式化显示
			this.setWaybillAmountDisplayInfo(w);

			// 处理上游费率展示
			if (BigDecimal.ZERO.compareTo(w.getRateByMe()) == 0) {
				w.setReceiveUpstream("——");
			}
			if (w.getIsCompleteTrajectory() == 1 && w.getLocateStatus() == 2) {
				w.setLocateStatus(6);
			}
			// 处理开票
			if (invoiceMap.containsKey(w.getId())) {
				WaybillTableRes invoice = invoiceMap.get(w.getId());
				w.setInvoiceNumber(invoice.getInvoiceNumber());
				w.setInvoiceTime(invoice.getInvoiceTime());
			}

			// 处理申请付款状态
			if (Arrays.asList(0, 9).contains(w.getPayApplyStatus())) {
				w.setPayApplyStatus(0);
			}
			if (Arrays.asList(2, 3, 4).contains(w.getPayApplyStatus())) {
				w.setPayApplyStatus(2);
			}

			// 处理地址
			w.setPlaceOfLoading(getAddress(w.getConsignorProvince(), w.getConsignorCity(), w.getConsignorArea(), w.getPlaceOfLoading()));
			w.setGoodsReceiptPlace(getAddress(w.getConsigneeProvince(), w.getConsigneeCity(), w.getConsigneeArea(), w.getGoodsReceiptPlace()));

			w.setConsignorAddersDetails(waybillService.getAllAddress(w.getConsignorProvince(), w.getConsignorCity(), w.getConsignorArea(), w.getPlaceOfLoading()));
			w.setConsigneeAddersDetails(waybillService.getAllAddress(w.getConsigneeProvince(), w.getConsigneeCity(), w.getConsigneeArea(), w.getGoodsReceiptPlace()));
		}
		return list;
	}

	/**
	 * 设置装货数量、卸货数量、涨亏数量等的格式化显示
	 *
	 * @param waybill
	 */
	private void setWaybillAmountDisplayInfo(WaybillTableRes waybill) {
		// 1.吨 2.方 3.车
		String feeUnit = FeeUnit.descOf(waybill.getFeeUnit());
		BigDecimal loadAmount = BigDecimal.ZERO;
		BigDecimal unloadAmount = BigDecimal.ZERO;
		// 1.吨 2.立方米 3.车
		if (waybill.getFeeUnit() == 1) {
			loadAmount = waybill.getLoadingWeight();
			unloadAmount = waybill.getUnloadWeight();
		} else if (waybill.getFeeUnit() == 2) {
			loadAmount = waybill.getLoadingCube();
			unloadAmount = waybill.getUnloadCube();
		} else if (waybill.getFeeUnit() == 3) {
			loadAmount = BigDecimal.ONE;
			unloadAmount = BigDecimal.ONE;
		}
		waybill.setLoadAmount(loadAmount.compareTo(BigDecimal.ZERO) == 0 ? "——" : loadAmount.stripTrailingZeros().toPlainString() + " " + feeUnit);
		waybill.setUnloadAmount(unloadAmount.compareTo(BigDecimal.ZERO) == 0 ? "——" : unloadAmount.stripTrailingZeros().toPlainString() + " " + feeUnit);

		// 显示亏/涨n方、吨
		String lossOrRise;
		BigDecimal amountLoss = loadAmount.subtract(unloadAmount);
		if (amountLoss.compareTo(BigDecimal.ZERO) > 0) {
			lossOrRise = "亏 " + amountLoss.stripTrailingZeros().toPlainString() + " " + feeUnit;
		} else if (amountLoss.compareTo(BigDecimal.ZERO) < 0) {
			lossOrRise = "涨 " + amountLoss.multiply(new BigDecimal("-1")).stripTrailingZeros().toPlainString() + " " + feeUnit;
		} else {
			lossOrRise = "——";
		}
		waybill.setLossOrRise(lossOrRise);

		// 判断货源类型，小程序来源的大宗货源的运单才显示大宗单价（waybill表的waybill_price，返回的是feePrice字段）、涨亏
		if (SupplyType.BULK.equals(waybill.getSupplyType()) && WaybillSource.WX_MINIPROGRAM.equals(waybill.getResource())) {
			// 状态(2已签约(未开始) 3运输中 4已完成)
			if (null != waybill.getStatus() && 3 == waybill.getStatus()) {
				// 运输中的运单，还没有确认收货，不显示涨亏
				waybill.setLossOrRise("——");
			}
		} else {
			waybill.setFeePrice(null);
			waybill.setLossOrRise("——");
		}
		// 小程序来源的运单才显示装货数量、卸货数量
		if (!WaybillSource.WX_MINIPROGRAM.equals(waybill.getResource())) {
			waybill.setLoadAmount("——");
			waybill.setUnloadAmount("——");
		}
	}

	@Override
	public AjaxResult selectShipperWaybillSum(WaybillQuery waybillQuery) {
		dealWithWaybillQueryReq(waybillQuery);
		// 数据库查询
		WaybillTableRes res = waybillMapperEx.selectShipperWaybillListSum(waybillQuery);
		// 处理上游费率展示
		if (null != res && BigDecimal.ZERO.compareTo(res.getRateByMe()) == 0) {
			res.setReceiveUpstream("——");
		}
		return AjaxResult.success(res);
	}

	/**
	 * 托运人--运单页面--前端传入条件，处理后去数据库查询
	 *
	 * @param waybillQuery
	 */
	private void dealWithWaybillQueryReq(WaybillQuery waybillQuery) {
		waybillQuery.setFrameworkContractState(1);// 运单列表只显示项目为启用状态的运单
		if (StringUtils.isNotBlank(waybillQuery.getDriverInfo())) {
			if (ValidateUtils.checkMobilePhone(StringUtils.deleteWhitespace(waybillQuery.getDriverInfo()))) {
				waybillQuery.setDriverPhone(StringUtils.deleteWhitespace(waybillQuery.getDriverInfo()));
			} else {
				waybillQuery.setDriverName(waybillQuery.getDriverInfo().trim());
			}
		}
		if (StringUtils.isNotBlank(waybillQuery.getCarCaptainInfo())) {
			if (ValidateUtils.checkMobilePhone(StringUtils.deleteWhitespace(waybillQuery.getCarCaptainInfo()))) {
				waybillQuery.setCarCaptainPhone(StringUtils.deleteWhitespace(waybillQuery.getCarCaptainInfo()));
			} else {
				waybillQuery.setCarCaptainName(waybillQuery.getCarCaptainInfo().trim());
			}
		}
		if (StringUtils.isNotBlank(waybillQuery.getAuxiliaryStaffInfo())) {
			if (ValidateUtils.checkMobilePhone(StringUtils.deleteWhitespace(waybillQuery.getAuxiliaryStaffInfo()))) {
				waybillQuery.setAuxiliaryStaffPhone(StringUtils.deleteWhitespace(waybillQuery.getAuxiliaryStaffInfo()));
			} else {
				waybillQuery.setAuxiliaryStaffName(waybillQuery.getAuxiliaryStaffInfo().trim());
			}
		}
		// 可以穿名称、身份证号、银行卡号
		String receiptInfo = waybillQuery.getReceiptInfo();
		if (StringUtils.isNotBlank(receiptInfo)) {
			String[] receiptInfos = receiptInfo.split("--");
			String payeeIdentityCard = "";
			if (receiptInfos.length == 2) {
				payeeIdentityCard = receiptInfos[1];
			} else if (ValidateUtils.checkIdentityNo(StringUtils.deleteWhitespace(receiptInfo)) || ValidateUtils.checkIdentityNo15(StringUtils.deleteWhitespace(receiptInfo))) {
				payeeIdentityCard = StringUtils.deleteWhitespace(receiptInfo);
			} else if (ValidateUtils.checkPositiveNumber(StringUtils.deleteWhitespace(receiptInfo))) {
				waybillQuery.setReceiptCard(StringUtils.deleteWhitespace(receiptInfo));
			} else {
				waybillQuery.setReceiptName(receiptInfo.trim());
			}
			if (CommonUtil.isNotNullOrEmpty(payeeIdentityCard)) { // 根据身份证号查询
				waybillQuery.setPayeeIdentityCard(payeeIdentityCard);
				/*PayeeInfo payeeInfo = new PayeeInfo();
				payeeInfo.setIdentityCard(payeeIdentityCard);
				List<PayeeInfo> payeeInfos = payeeInfoMapper.selectPayeeInfoList(payeeInfo);
				if (CommonUtil.isNotNullOrEmpty(payeeInfos)) {
					waybillQuery.setPayeeIds(payeeInfos.stream().map(p -> p.getId()).collect(Collectors.toList()));
				} else {
					waybillQuery.setPayeeIds(Arrays.asList(-1L));
				}*/
			}
		}
		if (StringUtils.isNotBlank(waybillQuery.getFrameworkContractName())) {
			waybillQuery.setFrameworkContractName(waybillQuery.getFrameworkContractName().trim());
		}
		if (StringUtils.isNotBlank(waybillQuery.getBillDateStart())) {
			waybillQuery.setBillDateStart(waybillQuery.getBillDateStart().trim() + " 00:00:00");
		}

		if (StringUtils.isNotBlank(waybillQuery.getBillDateEnd())) {
			waybillQuery.setBillDateEnd(waybillQuery.getBillDateEnd().trim() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillQuery.getNameAlias())) {
			List<Long> list = makeCodeMapperEx.selectCustomerInfoNameAlias(waybillQuery.getNameAlias(), waybillQuery.getCustomerId(), waybillQuery.getContractIds()).stream()
					.filter(f -> StringUtils.isNotEmpty(f.getNameAlias())).map(MakeCodeRes::getId).collect(Collectors.toList());
			waybillQuery.setMakeCodeIds(list);
		}
		// 处理开票相关
		if (StringUtils.isNotEmpty(waybillQuery.getInvoiceNumber()) || StringUtils.isNotEmpty(waybillQuery.getInvoiceStartTime()) || StringUtils.isNotEmpty(waybillQuery.getInvoiceEndTime())) {
			List<Waybill> waybills = financeStatementBillMapperEx.selectWaybillIdsByFinanceStatementInfo(waybillQuery);
			// 如果未查询到开票相关运单给个查不到得值
			if (waybills.isEmpty()) {
				List<String> str = new ArrayList<>();
				str.add("查询查不到得运单号");
				waybillQuery.setShippingNoteNumbers(str);
				// 处理es查询条数
				List<Long> ids = new ArrayList<>();
				ids.add(Long.MAX_VALUE);
				waybillQuery.setWaybillIds(ids);
			} else {
				waybillQuery.setShippingNoteNumbers(waybills.stream().map(Waybill::getShippingNoteNumber).collect(Collectors.toList()));
				waybillQuery.setWaybillIds(waybills.stream().map(Waybill::getId).collect(Collectors.toList()));
			}
		}
	}

	Map<Integer, String> policyTypeMap = new HashMap<Integer, String>() {
		{
			put(0, "人保物责险");
			put(1, "人保货运险");
			put(2, "龙琨货运险");
		}
	};

	Map<Integer, String> policyStatusMap = new HashMap<Integer, String>() {
		{
			put(-1, "未投保");
			put(1, "不投保");
			put(2, "投保受理成功");
			put(3, "投保受理失败");
			put(4, "系统异常");
			put(5, "投保成功");
			put(6, "投保失败");
			put(7, "人工处理");
		}
	};

	/**
	 * 获取转包运单列表
	 *
	 * @param waybillReq
	 * @return
	 */
	public List<WaybillRes> selectSubcontractWaybillList(WaybillReq waybillReq) {
		List<WaybillRes> list = waybillMapperEx.selectSubcontractWaybillList(waybillReq);
		list.forEach(w -> {
			// waybillState 运单状态：1 未完成付款不可申请开票 2 可申请开票 3 申请开票中 4开票完成
			// billStatus 开票状态(0.未开票 1.部分开票 2.已开票 3.开票中 4.撤销开票 5.驳回开票)
			if (w.getWaybillState() == 1 || w.getWaybillState() == 2 || w.getWaybillState() == 3) {
				w.setBillStatus(0);
			} else if (w.getWaybillState() == 4) {
				w.setBillStatus(2);
			}
		});

		return list;
	}

	@Transactional(rollbackFor = Exception.class)
	public AjaxResult submitWaybillByIds(Long[] ids) {
		if (null == ids || ids.length == 0) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "参数错误");
		}
		SysUser sysUser = SecurityUtils.getLoginUser().getUser();
		waybillMapperEx.submitWaybillByIds(ids, sysUser.getUserName());
		List<WaybillRes> waybillByIds = waybillMapperEx.getWaybillByIds(Arrays.stream(ids).collect(Collectors.toList()));
		if (StringUtils.isNotEmpty(waybillByIds)) {
			List<Waybill> list = new ArrayList<>();
			waybillByIds.forEach(l -> {
				if (l.getStatus() == 0) {
					Waybill waybill = waybillService.selectWaybillById(l.getId());
					waybill.setId(l.getId());
					waybill.setStatus(1);
					waybill.setSubmitBy(sysUser.getUserName());
					waybill.setSubmitTime(new Date());
					list.add(waybill);
				}
			});
			// 修改es中运单
			esWaybillService.updateEsWaybillInfos(list);
		}
		return AjaxResult.success(HttpStatus.SUCCESS, BusinessConstants.SUCCESS_DESC);
	}

	/**
	 * 查询要GPS实时定位的运单
	 */
	public List<Waybill> getGpsPositioningWaybillList() {
		return waybillMapperEx.getGpsPositioningWaybillList();
	}

	/**
	 * 查询网络货运运单列表
	 *
	 * @param waybillQuery
	 * @return
	 */
	public List<UploginkWaybillRes> uploginkWaybillList(UploginkWaybillQuery waybillQuery) {
		return waybillMapperEx.uploginkWaybillList(waybillQuery);
	}

	/**
	 * 查询待上传的网络货运运单列表（上传服务使用）
	 *
	 * @param waybillQuery
	 * @return
	 */
	public List<WaybillUploginkInfo> selectWaybillUploginkList(UploginkWaybillQuery waybillQuery) {
		List<WaybillUploginkInfo> list = waybillMapperEx.selectWaybillUploginkList(waybillQuery);
		if (null == list) {
			return Collections.emptyList();
		}
		return list;
	}

	/**
	 * 查询上传成功的网络货运运单列表（补传资金流水单用）【非江苏省的运单】
	 *
	 * @param waybillQuery
	 * @return
	 */
	public List<WaybillUploginkInfo> selectWaybillUploginkSuccessList(UploginkWaybillQuery waybillQuery) {
		List<WaybillUploginkInfo> list = waybillMapperEx.selectWaybillUploginkSuccessList(waybillQuery);
		if (null == list) {
			return Collections.emptyList();
		}
		return list;
	}

	/**
	 * 运单实时定位
	 *
	 * @param waybillId
	 */
	@Transactional(rollbackFor = Exception.class)
	public void waybillPositioning(long waybillId) {
		// 1、校验
		Waybill waybill = waybillMapper.selectWaybillById(waybillId);
		if (waybill.getStatus() == 4) {
			log.info("【GPS定时任务】运单号：{}，运单已完成，终止定位逻辑", waybill.getShippingNoteNumber());
			return;
		}
		// 防止并发时错误更新运单状态，在后续代码中，需更新状态的会重新设置
		waybill.setStatus(null);

		// 运单来源(1:PC建单 2:微信快速录单 3:APP 4:小黑卡 5:合作方 6:微信小程序 7:批量导入)
		Date loadTime = waybill.getDespatchActualDateTime();
		// 小程序运单，如已装车，取装车时间
		if (waybill.getResource() == 6 && waybill.getLoadTime() != null) {
			loadTime = waybill.getLoadTime();
		}
		if (StringUtils.isNull(loadTime)) {
			log.info("【GPS定时任务】运单号：{}，没有发车时间，终止定位逻辑", waybill.getShippingNoteNumber());
			return;
		}

		// 2、计算当前已运输时长，预计装车时间当做发车时间
		long interval = new Date().getTime() - loadTime.getTime();
		// 3、发车后才会开始定位，发车时间在当前时间之后的表示尚未发车的运单，返回
		if (interval < 0) {
			log.info("【GPS定时任务】运单号：{}，发车时间：{} 在当前时间之后，运单尚未发车，终止定位逻辑", waybill.getShippingNoteNumber(), loadTime);
			return;
		}

		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(waybill.getCustomerId());
		if (null == customerInfo) {
			log.info("【GPS定时任务】运单号：{}，所属托运人（托运人ID：{}）不存在，终止定位逻辑", waybill.getShippingNoteNumber(), waybill.getCustomerId());
			throw new RuntimeException("运单所属托运人不存在");
		}
		// 托运人是否开启强制回单确认(0.强制 1.不强制)
		boolean customerConfirmReceiptDisabled = customerInfo.getConfirmReceiptState() == 1;

		// 4、所有运单，发车时间超过7天的运单，或者预计送达时间3天后的运单，系统自动完成，无需再次定位
		Date receiptTime = waybill.getGoodsReceiptDateTime();
		if (getWaybillFinishFlag(interval, receiptTime)) {
			log.info("【GPS定时任务】运单号：{}，车牌号：{}，提货时间：{}，预计送达时间：{}，自动定位完成", waybill.getShippingNoteNumber(), waybill.getVehicleNumber(), DateUtils.dateToStr(loadTime),
					DateUtils.dateToStr(receiptTime));
			waybill.setLocateStatus(BusinessConstants.WAYBILL_LOCATE_STATUS_COMPLETED);// 2:定位完成
			// 非小程序端运单，运单改为已完成
			if (waybill.getResource() != 6) {
				waybill.setStatus(BusinessConstants.WAYBILL_STATUS_COMPLETED); // 运单已完成
				waybill.setUnloadTime(new Date());// 运单已完成,默认已到达
				waybill.setConfirmReceiptState(customerConfirmReceiptDisabled ? 0 : 1); // 托运人未开启强制回单确认，则运单自动设置为已签收，否则设置为未签收
			}
			Integer status = waybillMapperEx.getWaybillStatus(waybill.getId());
			if (null == status || status == -1) {
				log.info("【GPS定时任务】运单号：{}，车牌号：{}，运单不存在或已删除，终止定位逻辑", waybill.getShippingNoteNumber(), waybill.getVehicleNumber());
				return;
			}
			Waybill waybillFinish = new Waybill();
			waybillFinish.setId(waybill.getId());
			waybillFinish.setLocateStatus(waybill.getLocateStatus());// 2:定位完成
			if (waybill.getResource() != 6) { // 非小程序端运单，运单改为已完成
				waybillFinish.setStatus(waybill.getStatus()); // 运单已完成
				waybillFinish.setUnloadTime(waybill.getUnloadTime());// 运单已完成,默认已到达
				waybillFinish.setConfirmReceiptState(waybill.getConfirmReceiptState()); // 托运人未开启强制回单确认，则运单自动设置为已签收，否则设置为未签收
			}
			this.updateWaybill(waybillFinish);
			log.info("【GPS定时任务】运单号：{}，车牌号：{}，更新运单信息成功", waybill.getShippingNoteNumber(), waybill.getVehicleNumber());

			// 确认到达后续操作
			this.waybillConfirmArrival(Collections.singletonList(waybill.getId()));
			log.info("【GPS定时任务】运单号：{}，车牌号：{}，确认到达后续操作成功", waybill.getShippingNoteNumber(), waybill.getVehicleNumber());

			// 记录日志
			String message = "确认到达(运单ID：" + waybill.getId() + "，运单号：" + waybill.getShippingNoteNumber() + ")";
			sysClientLogService.insertLogTask(BusinessConstants.ACTION_SCENE_WAYBILL, BusinessConstants.ACTION_TYPE_OTHER, message, waybill.getShippingNoteNumber());

			return;
		}

		// 5、调用GPS最新位置服务
		Vehicle vehicle = vehicleService.selectVehicleById(waybill.getVehicleId());
		CommonResult<String> track = trackService.getRealTimeTrackByWaybill(waybill, vehicle);
		// 6、调用失败，更新运单状态，打印日志后返回
		if (CommonResult.isNotSuccess(track)) {
			// waybill.setLocateStatus(BusinessConstants.WAYBILL_LOCATE_STATUS_ABNORMAL);// 4:定位异常
			log.info("【GPS定时任务】运单号：{}，车牌号：{}，调用GPS最新位置服务失败：{}", waybill.getShippingNoteNumber(), waybill.getVehicleNumber(), track.getMsg());
			if (!redisCache.hasKey("isExist_" + waybill.getShippingNoteNumber())) {
				Integer isExist = trackService.checkIsExist(vehicle.getVehicleNumber(), vehicle.getVehiclePlateColorCode());
				// 1 入网 其他未入网
				Waybill updateWaybill = new Waybill();
				if (isExist != 1) {
					log.info("【GPS定时任务】运单号：{}，车牌号：{}，车辆未入网", waybill.getShippingNoteNumber(), waybill.getVehicleNumber());
					updateWaybill.setId(waybill.getId());
					updateWaybill.setLocateStatus(BusinessConstants.WAYBILL_LOCATE_STATUS_CAR_OFFLINE);
					this.updateWaybill(updateWaybill);
				} else {
					updateWaybill.setId(waybill.getId());
					updateWaybill.setLocateStatus(BusinessConstants.WAYBILL_LOCATE_STATUS_POSITIONING);
					this.updateWaybill(updateWaybill);
					redisCache.setCacheObject("isExist_" + waybill.getShippingNoteNumber(), 1, 11, TimeUnit.DAYS);
				}
				Waybill _waybill = waybillService.selectWaybillById(waybill.getId());
				BeanUtils.copyProperties(updateWaybill, _waybill);
				esWaybillService.updateEsWaybillInfo(updateWaybill);
			}

			return;
		}

		// 7、调用成功，保存到运单定位轨迹信息表
		log.info("【GPS定时任务】运单号：{}，车牌号：{}，提货时间：{}，发车时间：{}，预计送达时间：{}，定位成功，开始处理运单数据库逻辑", waybill.getShippingNoteNumber(), waybill.getVehicleNumber(),
				DateUtils.dateToStr(loadTime), DateUtils.dateToStr(loadTime), DateUtils.dateToStr(receiptTime));

		TrackLocationRes llRes = JSONObject.parseObject(track.getData(), TrackLocationRes.class);
		// saveWaybillGpsTrack(llRes, waybill);
		// 8、非小程序运单，更新运单表(装车时间、定位状态、运单运输状态)
		// 小程序运单改成定位中

		updateWaybillByPositioning(waybill, llRes, customerConfirmReceiptDisabled);

		// 9、生成消费流水，定时任务不校验权限。
		sysCostService.addWaybillTrackCost(CostPageEnum.WAYBILL_TASK_PAGE, CodeEnum.GPS_LAST_LOCATION, waybill.getCustomerId(), waybill, DateUtils.getNowDate(), null, false);
		log.info("【GPS定时任务】运单号：{}，定位处理完成", waybill.getShippingNoteNumber());
	}

	/**
	 * 判断运单是否定位完成：发车时间超过7天的运单，或者预计送达时间3天后的运单，系统自动完成，无需再次定位
	 *
	 * @param interval
	 * @param receiptTime
	 * @return
	 */
	private boolean getWaybillFinishFlag(Long interval, Date receiptTime) {
		if (interval.compareTo(7L * 24 * 3600 * 1000) > 0) {
			return true;
		}
		if (null != receiptTime) {
			Date endDate = DateUtils.addDays(receiptTime, 3);
			return DateUtils.compareDate(new Date(), endDate) > -1;
		}
		return false;
	}

	/**
	 * 更新运单表(装车时间、定位状态、运单运输状态)
	 *
	 * @param waybill
	 * @param llRes
	 * @param customerConfirmReceiptDisabled
	 */
	private void updateWaybillByPositioning(Waybill waybill, TrackLocationRes llRes, boolean customerConfirmReceiptDisabled) {
		// 目的地范围3公里，自动完成
		ConsigneeInfo consigneeInfo = consigneeInfoService.selectConsigneeInfoById(waybill.getConsigneeId());
		Point circleCenter = new Point(Double.parseDouble(consigneeInfo.getLongitude()), Double.parseDouble(consigneeInfo.getLatitude()));
		Point point = new Point(Double.parseDouble(llRes.getLon()), Double.parseDouble(llRes.getLat()));
		boolean inCircle = GeoJudgeUtils.isInCircle(3000, circleCenter, point);
		if (waybill.getResource() != 6) {
			if (waybill.getLoadTime() == null) {
				waybill.setLoadTime(DateUtils.getNowDate());
			}
			if (inCircle) {
				waybill.setLocateStatus(BusinessConstants.WAYBILL_LOCATE_STATUS_COMPLETED);// 定位完成
				waybill.setStatus(BusinessConstants.WAYBILL_STATUS_COMPLETED); // 运单已完成
				waybill.setUnloadTime(DateUtils.getNowDate());
				waybill.setConfirmReceiptState(customerConfirmReceiptDisabled ? 0 : 1); // 托运人未开启强制回单确认，则运单自动设置为已签收，否则设置为未签收
			} else {
				waybill.setLocateStatus(BusinessConstants.WAYBILL_LOCATE_STATUS_POSITIONING);// 定位中
				waybill.setStatus(BusinessConstants.WAYBILL_STATUS_IN_TRANSIT); // 运输中
			}
		} else {
			if (inCircle) {
				waybill.setLocateStatus(BusinessConstants.WAYBILL_LOCATE_STATUS_COMPLETED);// 定位完成
				waybill.setUnloadTime(DateUtils.getNowDate());
			} else {
				waybill.setLocateStatus(BusinessConstants.WAYBILL_LOCATE_STATUS_POSITIONING);// 定位中
			}
		}
		// 更新运单
		Integer status = waybillMapperEx.getWaybillStatus(waybill.getId());
		if (status != -1) {
			this.updateWaybill(waybill);
			// 确认到达后续操作，其中含有es操作
			if (null != waybill.getStatus()) {
				status = waybill.getStatus();
			}
			if (BusinessConstants.WAYBILL_STATUS_COMPLETED == status) {
				this.waybillConfirmArrival(Collections.singletonList(waybill.getId()));
				// 记录日志
				String message = "实时定位——确认到达(运单ID：" + waybill.getId() + "，运单号：" + waybill.getShippingNoteNumber() + ")";
				sysClientLogService.insertLogTask(BusinessConstants.ACTION_SCENE_WAYBILL, BusinessConstants.ACTION_TYPE_OTHER, message, waybill.getShippingNoteNumber());
			} else {
				// 未确认到达的运单，修改es中运单状态
				esWaybillService.updateEsWaybillInfos(Collections.singletonList(waybill));
			}
		}
	}

	/**
	 * 查询运单物流详情
	 */
	public AjaxResult getWaybillLogisticsDetails(Long id, int flag) {
		if (null == id || id == 0L) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "运单标识为空");
		}
		Waybill waybill = this.selectWaybillById(id);
		if (null == waybill) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "没有找到对应的运单信息");
		}
		WaybillSlave waybillSlave = waybillSlaveService.selectWaybillSlaveById(waybill.getId());
		if (null == waybillSlave) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "没有找到相关的运单信息");
		}
		waybill.setWaybillSlave(waybillSlave);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("driverName", waybill.getDriverName());
		map.put("telephone", waybill.getTelephone());
		map.put("vehicleNumber", waybill.getVehicleNumber());
		map.put("locateStatus", waybill.getLocateStatus());
		map.put("locateType", waybill.getLocateType());
		map.put("status", waybill.getStatus());
		// 已派单
		if (waybill.getStatus() >= BusinessConstants.WAYBILL_STATUS_WAYBILL) {
			Map<String, Object> sendOrdersMap = new HashMap<String, Object>();
			BigDecimal mileage1 = waybill.getMileage();
			String mileage = "";
			if (mileage1.compareTo(BigDecimal.ZERO) > 0) {
				if (mileage1.compareTo(new BigDecimal("10")) < 0) {
					mileage1 = mileage1.setScale(2, RoundingMode.DOWN);
				} else if (mileage1.compareTo(new BigDecimal("100")) < 0) {
					mileage1 = mileage1.setScale(1, RoundingMode.DOWN);
				} else {
					mileage1 = mileage1.setScale(0, RoundingMode.DOWN);
				}
				mileage = "全程：" + mileage1 + "KM";
			}
			//			String mileage = waybill.getMileage() > BigDecimal.ZERO ? "全程：" + waybill.getMileage() + "KM" : "";
			String sendOrders = flag == 0 ?
					"【已派单】\r\n已向司机派单\r\n司机：" + waybill.getDriverName() + " " + waybill.getTelephone() + "\r\n车牌号：" + waybill.getVehicleNumber() + "为您承运\r\n" + mileage :
					"【已派单】\r\n承运人：" + waybill.getDriverName() + " " + waybill.getTelephone() + "\r\n" + mileage;
			if (null != waybill.getSubmitTime()) {
				sendOrdersMap.put("time", DateUtils.parseDateToStr(DateUtils.YYSS, waybill.getSubmitTime()));
			}
			sendOrdersMap.put("sendOrdersInfo", sendOrders);
			map.put("sendOrders", sendOrdersMap);
		}
		// 已签约
		if (waybill.getStatus() >= BusinessConstants.WAYBILL_STATUS_LEASE) {
			Map<String, Object> signedMap = new HashMap<String, Object>();
			String signedInfo = flag == 0 ? "【已签约】\r\n司机已签约" : "【已签约】\r\n承运人已签约";
			List<FileModel> fileList = new ArrayList<>();
			List<WaybillAttachmentInfo> eContractFiles = waybillAttachmentInfoService.eContractFileList(waybill.getId());
			if (null != eContractFiles && !eContractFiles.isEmpty()) {
				fileList = eContractFiles.stream().map(FileModel::of).collect(Collectors.toList());
			}
			if (null != waybill.getSignTime()) {
				signedMap.put("time", DateUtils.parseDateToStr(DateUtils.YYSS, waybill.getSignTime()));
			}
			signedMap.put("signedInfo", signedInfo);
			signedMap.put("fileList", fileList);
			map.put("signed", signedMap);
		}
		// 已发车
		if (waybill.getStatus() >= BusinessConstants.WAYBILL_STATUS_IN_TRANSIT) {
			Map<String, Object> departMap = new HashMap<String, Object>();
			String departInfo = "【已发车】\r\n装货完毕，车辆已出发。\r\n装货信息：";
			if (BigDecimal.ZERO.compareTo(waybill.getWaybillSlave().getLoadingWeight()) != 0) {
				departInfo += "重量" + waybill.getWaybillSlave().getLoadingWeight().setScale(3, RoundingMode.HALF_UP) + "吨";
			} else if (BigDecimal.ZERO.compareTo(waybill.getWaybillSlave().getLoadingCube()) != 0) {
				departInfo += "体积" + waybill.getWaybillSlave().getLoadingCube().setScale(3, RoundingMode.HALF_UP) + "方";
			} else {
				departInfo += "1车";
			}
			List<String> fileUrlList = new ArrayList<String>();
			List<WaybillAttachmentInfo> loadingFiles = waybillAttachmentInfoService.loadingFileList(waybill.getId());
			if (null != loadingFiles && !loadingFiles.isEmpty()) {
				fileUrlList = loadingFiles.stream().map(WaybillAttachmentInfo::getFileUrl).collect(Collectors.toList());
			}
			Date loadClickTime = waybillSlave.getLoadClickTime() != null ? waybillSlave.getLoadClickTime() : waybill.getLoadTime();
			if (null != loadClickTime) {
				departMap.put("time", DateUtils.parseDateToStr(DateUtils.YYSS, loadClickTime));
			}
			departMap.put("departInfo", departInfo);
			departMap.put("fileList", fileUrlList);
			map.put("depart", departMap);
		}
		// 轨迹信息
		if (waybill.getLocateType() == 6 || waybill.getStatus() >= BusinessConstants.WAYBILL_STATUS_IN_TRANSIT) {
			String str = flag == 0 ? "定位" : "最新位置";
			// GPS定位信息
			Map<String, Object> gpsTrackMap = new HashMap<String, Object>();
			List<WaybillTransportTrackRes> gpsTrackList = new ArrayList<>();
			// 0：未补充1：已补充
			Boolean IsCompleteTrajectory = false;
			// 查询当前订单是否补充过轨迹
			int count = waybillCustomerRelationMapper.selectIsCompleteTrajectoryCount(waybill.getId());
			IsCompleteTrajectory = count > 0;
			// 判断当前订单是否补充过轨迹
			if (IsCompleteTrajectory) {
				Vehicle vehicle = vehicleService.selectVehicleById(waybill.getVehicleId());
				VehicleTrackReq vehicleTrackReq = new VehicleTrackReq();
				vehicleTrackReq.setWaybillId(id);
				vehicleTrackReq.setQryBtm(waybill.getDespatchActualDateTime());
				vehicleTrackReq.setQryEtm(waybill.getGoodsReceiptDateTime());
				vehicleTrackReq.setVehicleNumber(vehicle.getVehicleNumber());
				vehicleTrackReq.setResetFlag("");
				vehicleTrackReq.setVehiclePlateColorCode(vehicle.getVehiclePlateColorCode());
				CommonResult<String> result = trackService.getCompleteTrajectory(vehicleTrackReq);
				if (result.getCode().equals("200")) {
					log.info("CompleteTrajectory:【{}】", result.getData());
					gpsTrackList = JSON.parseArray(result.getData(), WaybillTransportTrackRes.class);
				}
			} else {
				CommonResult<String> result = trackService.getGpsTrackList(id);
				if (result.getCode().equals("200")) {
					log.info("GpsTrackList:【{}】", result.getData());
					gpsTrackList = JSON.parseArray(result.getData(), WaybillTransportTrackRes.class);
				}
			}
			gpsTrackMap.put("trackList", gpsTrackList);
			if (gpsTrackList != null && gpsTrackList.size() > 0) {
				// 如果adr为空根据经纬度获取adr信息
				// for (WaybillTransportTrackRes res : gpsTrackList) {
				// if (StringUtils.isEmpty(res.getAdr())) {
				// AmapLocationModel locationModel = amapService.getLocationByLongitudeAndLatitude(res.getLon(), res.getLat());
				// res.setAdr(locationModel.getFormattedAddress());
				// }
				// }
				for (int i = 0; i < gpsTrackList.size(); i++) {
					if (i == 0 || i == gpsTrackList.size() - 1) {
						WaybillTransportTrackRes res = gpsTrackList.get(i);
						AmapLocationModel locationModel = amapService.getLocationByLongitudeAndLatitude(res.getLon(), res.getLat());
						res.setAdr(locationModel.getFormattedAddress());
					}
				}
				List<WaybillTransportTrackRes> gpsListInfo = changeTrack(gpsTrackList);
				gpsTrackMap.put("trackListInfo", gpsListInfo);
				gpsTrackMap.put("newTrackInfo", gpsListInfo.get(0));
				String trackInfo = "【位置追踪】\r\n最新位置：" + gpsListInfo.get(0).getAdr();
				gpsTrackMap.put("trackInfo", trackInfo);
				gpsTrackMap.put("time", DateUtils.dateToStr(gpsListInfo.get(0).getUtc()));
			}
			map.put("gpsTrack", gpsTrackMap);
			// 小程序定位信息
			Map<String, Object> miniProgramTrackMap = new HashMap<String, Object>();
			WaybillMiniprogramTrack mptrackQuery = new WaybillMiniprogramTrack();
			mptrackQuery.setWaybillId(id);
			List<WaybillMiniprogramTrack> mptrackList = waybillMiniprogramTrackMapper.selectWaybillMiniprogramTrackList(mptrackQuery);
			if (null != mptrackList && !mptrackList.isEmpty()) {
				List<WaybillTransportTrackRes> trackList = new ArrayList<WaybillTransportTrackRes>();
				for (WaybillMiniprogramTrack miniprogramTrack : mptrackList) {
					WaybillTransportTrackRes trackRes = new WaybillTransportTrackRes();
					BeanUtils.copyProperties(miniprogramTrack, trackRes);
					trackList.add(trackRes);
				}
				// 运输中的才显示最新的一条数据
				if (waybill.getStatus() == BusinessConstants.WAYBILL_STATUS_IN_TRANSIT || waybill.getStatus() == BusinessConstants.WAYBILL_STATUS_COMPLETED) {
					mptrackQuery = mptrackList.get(mptrackList.size() - 1);// 获取最新的一条位置信息
					String miniProgramTrackInfo = "【位置追踪】\r\n最新位置：" + mptrackQuery.getAdr();
					miniProgramTrackMap.put("trackInfo", miniProgramTrackInfo);
					if (null != mptrackQuery.getCreateTime()) {
						miniProgramTrackMap.put("time", DateUtils.parseDateToStr(DateUtils.YYSS, mptrackQuery.getCreateTime()));
					}
				}
				miniProgramTrackMap.put("trackList", trackList);
				if (trackList.size() > 0) {
					List<WaybillTransportTrackRes> trackListInfo = changeTrack(trackList);
					miniProgramTrackMap.put("trackListInfo", trackListInfo);
					miniProgramTrackMap.put("newTrackInfo", trackListInfo.get(0));
				}
				map.put("miniProgramTrack", miniProgramTrackMap);
			}
		}
		// 运输完成
		if (waybill.getStatus() == BusinessConstants.WAYBILL_STATUS_COMPLETED) {
			Map<String, Object> completeMap = new HashMap<String, Object>();
			String completeInfo = "【运输完成】\r\n卸货完毕，运输完成。\r\n卸货信息：";
			if (BigDecimal.ZERO.compareTo(waybill.getWaybillSlave().getUnloadWeight()) != 0) {
				completeInfo += "重量" + waybill.getWaybillSlave().getUnloadWeight().setScale(3, RoundingMode.HALF_UP) + "吨";
			} else if (BigDecimal.ZERO.compareTo(waybill.getWaybillSlave().getUnloadCube()) != 0) {
				completeInfo += "体积" + waybill.getWaybillSlave().getUnloadCube().setScale(3, RoundingMode.HALF_UP) + "方";
			} else {
				completeInfo += "1车";
			}
			List<String> fileUrlList = new ArrayList<String>();
			// 到货附件
			List<WaybillAttachmentInfo> unloadFiles = waybillAttachmentInfoService.unloadFileList(waybill.getId());
			if (null != unloadFiles && !unloadFiles.isEmpty()) {
				fileUrlList = unloadFiles.stream().map(WaybillAttachmentInfo::getFileUrl).collect(Collectors.toList());
			}
			Date unloadClickTime = waybillSlave.getUnloadClickTime() != null ? waybillSlave.getUnloadClickTime() : waybill.getUnloadTime();
			if (null != unloadClickTime) {
				completeMap.put("time", DateUtils.parseDateToStr(DateUtils.YYSS, unloadClickTime));
			}
			completeMap.put("completeInfo", completeInfo);
			completeMap.put("fileList", fileUrlList);
			map.put("complete", completeMap);
		}
		return AjaxResult.success(map);
	}

	public List<WaybillTransportTrackRes> changeTrack(List<WaybillTransportTrackRes> trackList) {
		List<WaybillTransportTrackRes> list = new ArrayList<>();
		if (trackList.get(0).getUtc() != null) {
			long lastTime = trackList.get(0).getUtc().getTime();
			list.add(trackList.get(0));

			for (int i = 1; i < trackList.size() - 1; i++) {
				WaybillTransportTrackRes track = trackList.get(i);
				long currentTime = track.getUtc().getTime();
				long diff = currentTime - lastTime;
				if (diff >= 7000000) { // 3600000毫秒 = 1小时
					if (StringUtils.isEmpty(track.getAdr())) {
						AmapLocationModel locationByLongitudeAndLatitude = amapService.getLocationByLongitudeAndLatitude(track.getLon(), track.getLat());
						if (locationByLongitudeAndLatitude != null) {
							track.setAdr(locationByLongitudeAndLatitude.getFormattedAddress());
						}
					}
					list.add(trackList.get(i));
					lastTime = currentTime;
				}
			}
			if (trackList.size() != 1 && list.get(list.size() - 1).getUtc() != trackList.get(trackList.size() - 1).getUtc()) {
				list.add(trackList.get(trackList.size() - 1));
			}
			Collections.reverse(list);
		} else {
			long lastTime = trackList.get(0).getCreateTime().getTime();
			list.add(trackList.get(0));

			for (int i = 1; i < trackList.size() - 1; i++) {
				WaybillTransportTrackRes track = trackList.get(i);
				long currentTime = track.getCreateTime().getTime();
				long diff = currentTime - lastTime;
				if (diff >= 7000000) { // 3600000毫秒 = 1小时
					if (StringUtils.isEmpty(track.getAdr())) {
						AmapLocationModel locationByLongitudeAndLatitude = amapService.getLocationByLongitudeAndLatitude(track.getLon(), track.getLat());
						if (locationByLongitudeAndLatitude != null) {
							track.setAdr(locationByLongitudeAndLatitude.getFormattedAddress());
						}
					}
					list.add(trackList.get(i));
					lastTime = currentTime;
				}
			}
			if (trackList.size() != 1 && list.get(list.size() - 1).getCreateTime() != trackList.get(trackList.size() - 1).getCreateTime()) {
				list.add(trackList.get(trackList.size() - 1));
			}
			Collections.reverse(list);
		}
		return list;
	}

	public List<WaybillTransportTrackRes> changeTrackSD(List<WaybillTransportTrackRes> trackList) {
		List<WaybillTransportTrackRes> list = new ArrayList<>();
		list.add(trackList.get(0));

		for (int i = 10; i < trackList.size() - 1; i = i + 10) {
			list.add(trackList.get(i));
		}
		if (trackList.size() != 1 && !list.get(list.size() - 1).getLat().equals(trackList.get(trackList.size() - 1).getLat())) {
			list.add(trackList.get(trackList.size() - 1));
		}
		Collections.reverse(list);
		return list;
	}

	@Override
	public BigDecimal paymentReviewTotalAmountByContractId(WaybillReq waybillReq) {
		return waybillMapperEx.paymentReviewTotalAmountByContractId(waybillReq);
	}

	@Override
	public BigDecimal statisticsApplyMoneyByContractId(TradeFlowQueryReq req) {
		return waybillMapperEx.statisticsApplyMoneyByContractId(req);
	}

	public String getFareStageByApplyFare(WaybillRes waybill) {
		String[] stages = { "总费用", "预付", "到付", "回单付" };
		if (PayType.TOTAL.equals(waybill.getPayType())) {
			// 检查申请的付款金额是否大于运单的总未付金额
			if (waybill.getUnpaidFare().compareTo(BigDecimal.ZERO) > 0) {
				waybill.setApplicableAmount(waybill.getUnpaidFare());
				return stages[BusinessConstants.FARE_STAGE_TOTAL_COST];
			}
		} else {
			// 如果运单状态为“待装车”“待卸车”,则只可以申请付预付
			if (waybill.getStatus() == BusinessConstants.WAYBILL_STATUS_LEASE || waybill.getStatus() == BusinessConstants.WAYBILL_STATUS_IN_TRANSIT) {
				if (waybill.getPrepayType() == 1 && waybill.getPrepayUnpaidMoney().compareTo(BigDecimal.ZERO) > 0) {
					waybill.setApplicableAmount(waybill.getPrepayUnpaidMoney());
					return stages[BusinessConstants.FARE_STAGE_PREPAY];// 预付
				}
			} else {
				// 运单的预付付款完成/没有预付阶段 且 确认收货后，显示【到付】
				// 到付付款完成后，显示【回单付】
				String stage = stages[BusinessConstants.FARE_STAGE_PREPAY];// 预付
				waybill.setApplicableAmount(waybill.getPrepayUnpaidMoney());
				if (waybill.getPrepayUnpaidMoney().compareTo(BigDecimal.ZERO) == 0 && waybill.getStatus() == BusinessConstants.WAYBILL_STATUS_COMPLETED) {
					waybill.setApplicableAmount(waybill.getArriveUnpaidMoney());
					stage = stages[BusinessConstants.FARE_STAGE_ARRIVE];// 到付
				}
				if (waybill.getArriveUnpaidMoney().compareTo(BigDecimal.ZERO) == 0) {
					waybill.setApplicableAmount(waybill.getReceiptUnpaidMoney());
					stage = stages[BusinessConstants.FARE_STAGE_RECEIPT];// 回单付
				}
				return stage;
			}
		}
		return "";
	}

	public Integer getWaybillPayApplyStatus(Integer fareStage, BigDecimal applyFare, WaybillRes waybill) {
		if (fareStage == BusinessConstants.FARE_STAGE_TOTAL_COST) {
			// 如果申请金额小于未付金额，运单的付款申请状态设为'部分通过'
			if (applyFare.compareTo(waybill.getUnpaidFare()) < 0) {
				return BusinessConstants.WAYBILL_PAY_APPLY_STATUS_PARTPASS;// 部分通过
			} else {
				return BusinessConstants.WAYBILL_PAY_APPLY_STATUS_PASS;// 已通过
			}
		} else if (fareStage == BusinessConstants.FARE_STAGE_PREPAY) {
			// 如果申请金额小于预付未付金额，运单的付款申请状态设为'部分通过'
			if (applyFare.compareTo(waybill.getPrepayUnpaidMoney()) < 0) {
				return BusinessConstants.WAYBILL_PAY_APPLY_STATUS_PARTPASS;// 部分通过
			} else {
				return BusinessConstants.WAYBILL_PAY_APPLY_STATUS_PASS;// 通过
			}
		} else if (fareStage == BusinessConstants.FARE_STAGE_ARRIVE) {
			// 如果申请金额小于到付未付金额，运单的付款申请状态设为'部分通过'
			if (applyFare.compareTo(waybill.getArriveUnpaidMoney()) < 0) {
				return BusinessConstants.WAYBILL_PAY_APPLY_STATUS_PARTPASS;// 部分通过
			} else {
				return BusinessConstants.WAYBILL_PAY_APPLY_STATUS_PASS;// 通过
			}
		} else if (fareStage == BusinessConstants.FARE_STAGE_RECEIPT) {
			// 如果申请金额小于回单付未付金额，运单的付款申请状态设为'部分通过'
			if (applyFare.compareTo(waybill.getReceiptUnpaidMoney()) < 0) {
				return BusinessConstants.WAYBILL_PAY_APPLY_STATUS_PARTPASS;// 部分通过
			} else {
				return BusinessConstants.WAYBILL_PAY_APPLY_STATUS_PASS;// 已通过
			}
		}
		return -1;
	}

	public Long getAuditCustomerId(FrameworkContract contract, Long customerId, Long waybillId) {
		Long auditCustomerId = null;
		BusinessModel businessModel = businessModelService.selectById(contract.getBusinessModelId());
		// 1、是否需要审核 0=：需要,1=：不需要
		if (businessModel.getPayApprover() >= 0) {
			// FrameworkContractSubchain payer = waybillContractChainService.getPayerCustomerId(waybillId);
			FrameworkContractSubchain payApprover = waybillContractChainService.getPayerApproverCustomerId(waybillId, businessModel);
			if (payApprover != null) {
				auditCustomerId = payApprover.getCustomerId();
			}
		} else if (businessModel.getPayApprover() == 1) {
			auditCustomerId = customerId;
		}
		return auditCustomerId;
	}

	public Waybill selectWaybillByCode(String waybillCode) {
		if (StringUtils.isBlank(waybillCode)) {
			throw new RuntimeException("运单编码为空");
		}
		Waybill waybill = new Waybill();
		waybill.setShippingNoteNumber(waybillCode);
		List<Waybill> list = this.selectWaybillList(waybill);
		if (list.size() != 1) {
			throw new RuntimeException("没有相关的运单信息");
		}
		return list.get(0);
	}

	/**
	 * 网络货运人端-运单查询-列表
	 *
	 * @param req
	 * @return
	 */
	public TableInfo<WaybillTableResOfFreightForwarder> selectFreightForwarderWaybillList(WaybillReq req, HttpServletRequest request) {
		if (StringUtils.isNotBlank(req.getBeginOrderCreateTime())) {
			req.setBeginOrderCreateTime(req.getBeginOrderCreateTime() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(req.getEndOrderCreateTime())) {
			req.setEndOrderCreateTime(req.getEndOrderCreateTime() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(req.getBeginCreateTime())) {
			req.setBeginCreateTime(req.getBeginCreateTime() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(req.getEndCreateTime())) {
			req.setEndCreateTime(req.getEndCreateTime() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(req.getBeginPayTime())) {
			req.setBeginPayTime(req.getBeginPayTime() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(req.getEndPayTime())) {
			req.setEndPayTime(req.getEndPayTime() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(req.getInvoiceDateStart())) {
			req.setInvoiceDateStart(req.getInvoiceDateStart() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(req.getInvoiceDateEnd())) {
			req.setInvoiceDateEnd(req.getInvoiceDateEnd() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(req.getDriverInfo())) {
			if (ValidateUtils.checkIdentityNo15(StringUtils.deleteWhitespace(req.getDriverInfo())) || ValidateUtils.checkIdentityNo(StringUtils.deleteWhitespace(req.getDriverInfo()))) {
				req.setDrivingLicense(req.getDriverInfo().trim());
			} else {
				req.setDriverName(req.getDriverInfo().trim());
			}
		}
		if (StringUtils.isNotBlank(req.getIdentityCard())) {
			PayeeInfo payeeInfo = new PayeeInfo();
			payeeInfo.setIdentityCard(req.getIdentityCard());
			List<PayeeInfo> payeeInfos = payeeInfoMapper.selectPayeeInfoList(payeeInfo);
			if (CommonUtil.isNotNullOrEmpty(payeeInfos)) {
				req.setPayeeIds(payeeInfos.stream().map(p -> p.getId()).collect(Collectors.toList()));
				req.setIdentityCard(null);
			} else {
				// 如果收款人没有查到，说明没有运单
				return new TableInfo<>(new ArrayList<>(), 0);
			}
		}
		PageDomain pageDomain = TableSupport.buildPageRequest();
		req.setOffSet((pageDomain.getPageNum() - 1) * pageDomain.getPageSize());
		req.setPageSize(pageDomain.getPageSize());
		req.setPageNum(null); // 防止分页插件自动分页

		if (req.getCustomerId() != null) {
			FrameworkContractSubchain subchainReq = new FrameworkContractSubchain();
			subchainReq.setIsLeaf(0);
			subchainReq.setParentCustomerId(req.getCustomerId());
			subchainReq.setFreightForwarderId(req.getFreightForwarderId());
			List<FrameworkContractSubchain> subchains = frameworkContractSubchainMapper.selectFrameworkContractSubchainList(subchainReq);
			List<Long> customerContractIds = null; // 托运人对应的项目id
			if (!CommonUtil.isNullOrEmpty(subchains)) {
				customerContractIds = subchains.stream().map(a -> a.getContractId()).collect(Collectors.toList());
				if (CommonUtil.isNotNullOrEmpty(req.getContractIds())) {
					// 取两者交集
					customerContractIds = customerContractIds.stream().filter(id -> req.getContractIds().contains(id)).collect(Collectors.toList());
				}
			}
			if (CommonUtil.isNotNullOrEmpty(customerContractIds)) {
				req.setContractIds(subchains.stream().map(a -> a.getContractId()).collect(Collectors.toList()));
			} else {
				// 如果根据项目转包链关联关系没有查到，说明没有运单
				return new TableInfo<>(new ArrayList<>(), 0);
			}
			req.setCustomerId(null);
		}

		// 处理开票相关
		if (StringUtils.isNotEmpty(req.getInvoiceNumber()) || StringUtils.isNotEmpty(req.getInvoiceStartTime()) || StringUtils.isNotEmpty(req.getInvoiceEndTime())) {
			WaybillQuery waybillQuery = new WaybillQuery();
			waybillQuery.setPageNum(null);
			waybillQuery.setPageSize(null);
			waybillQuery.setAuditFreightForwarderId(SecurityUtils.getFreightForwarderId());
			waybillQuery.setInvoiceNumber(req.getInvoiceNumber());
			waybillQuery.setInvoiceStartTime(req.getInvoiceStartTime());
			waybillQuery.setInvoiceEndTime(req.getInvoiceEndTime());
			List<Waybill> waybills = financeStatementBillMapperEx.selectWaybillIdsByFinanceStatementInfo(waybillQuery);
			// 如果未查询到开票相关运单给个查不到得值
			if (waybills.isEmpty()) {
				List<String> str = new ArrayList<>();
				str.add("查询查不到得运单号");
				req.setShippingNoteNumbers(str);
				// 处理es查询条数
				List<Long> ids = new ArrayList<>();
				ids.add(Long.MAX_VALUE);
				req.setWaybillIds(ids);
			} else {
				req.setShippingNoteNumbers(waybills.stream().map(Waybill::getShippingNoteNumber).collect(Collectors.toList()));
				req.setWaybillIds(waybills.stream().map(Waybill::getId).collect(Collectors.toList()));
			}
		}

		List<WaybillTableResOfFreightForwarder> list = waybillMapperEx.freightForwarderWaybillList(req);

		if (CommonUtil.isNullOrEmpty(list)) {
			return new TableInfo<>(new ArrayList<>(), 0);
		}
		// 处理开票信息相关
		Map<Long, WaybillTableRes> invoiceMap = new HashMap<>();
		if (!list.isEmpty()) {
			List<Long> waybillIds = list.stream().map(WaybillTableResOfFreightForwarder::getId).collect(Collectors.toList());
			List<WaybillTableRes> invoiceList = financeStatementBillMapperEx.selectInvoiceNumbersByWaybillIds(waybillIds, null, SecurityUtils.getFreightForwarderId());
			if (null != invoiceList) {
				invoiceMap = invoiceList.stream().collect(Collectors.toMap(WaybillTableRes::getWaybillId, Function.identity()));
			}
		}
		for (WaybillTableResOfFreightForwarder w : list) {
			// status 运单状态(0订单 1运单 2已签约(未开始) 3运输中 4已完成 -1删除)
			// transport 运输状态(0:未开始 1:在途中 2:已完成)
			if (Arrays.asList(0, 1, 2).contains(w.getStatus())) {
				w.setTransportStatus(0);
			} else if (3 == w.getStatus()) {
				w.setTransportStatus(1);
			} else if (4 == w.getStatus()) {
				w.setTransportStatus(2);
			}
			// 处理开票
			if (invoiceMap.containsKey(w.getId())) {
				WaybillTableRes invoice = invoiceMap.get(w.getId());
				w.setInvoiceNumber(invoice.getInvoiceNumber());
				w.setInvoiceTime(invoice.getInvoiceTime());
			}

			if (7 == w.getProxyInvoiceState()) {
				w.setInvoiceState(1);
			} else {
				w.setInvoiceState(0);
			}

			// 计算 含税承运费（元）：司机代开代缴的费用=承运费/（1-3%）与 额外费用总计
			BigDecimal inclusiveTransportFee = this.calculateTaxInclusiveTransportFee(w.getPayFare());
			w.setInclusiveTransportFee(inclusiveTransportFee);

			w.setPlaceOfLoading(getAddress(w.getConsignorProvince(), w.getConsignorCity(), w.getConsignorArea(), w.getPlaceOfLoading()));
			w.setGoodsReceiptPlace(getAddress(w.getConsigneeProvince(), w.getConsigneeCity(), w.getConsigneeArea(), w.getGoodsReceiptPlace()));

			w.setConsignorAddersDetails(waybillService.getAllAddress(w.getConsignorProvince(), w.getConsignorCity(), w.getConsignorArea(), w.getPlaceOfLoading()));
			w.setConsigneeAddersDetails(waybillService.getAllAddress(w.getConsigneeProvince(), w.getConsigneeCity(), w.getConsigneeArea(), w.getGoodsReceiptPlace()));
		}
		Long total = 0L;
		if (req.getTransportationAgreementState() != null || null != req.getPayeeType() || null != req.getReceiptStatus() || StringUtils.isNotBlank(
				req.getInvoiceNumber()) || (req.getProxyInvoiceState() != null && req.getProxyInvoiceState() != 0) || CommonUtil.isNotNullOrEmpty(
				req.getHaulway()) || null != req.getRiskId() || null != req.getRiskGrade() || req.getBillStatus() != null || CommonUtil.isNotNullOrEmpty(
				req.getInvoiceNo()) || req.getInvoiceState() != null || CommonUtil.isNotNullOrEmpty(req.getInvoiceStartTime()) || CommonUtil.isNotNullOrEmpty(req.getInvoiceEndTime())) {
			total = waybillMapperEx.countForwarderWaybills(req);
		} else {
			total = esWaybillService.countWaybillInfo(esWaybillService.generateReq(req, 0));
		}
		return new TableInfo<>(list, total);
	}

	/**
	 * 待申请开票运单列表--托运人、物流金融端
	 *
	 * @param waybillReq
	 * @return
	 */
	public List<WaybillRes> waybillListForStatement(WaybillReq waybillReq) {

		Map<String, Object> map = new HashMap<>();
		map.put("customerId", SecurityUtils.getShipperId());
		if (waybillReq.getBusinessModelId() != null) {
			map.put("BusinessModelId", waybillReq.getBusinessModelId());
		}
		if (waybillReq.getIsRecorder() != null) {
			map.put("isRecorder", waybillReq.getIsRecorder());
		}
		if (!StringUtils.isEmpty(waybillReq.getContractIds())) {
			map.put("contractIds", waybillReq.getContractIds());
		}
		if (waybillReq.getFrameworkContractIds() != null && !waybillReq.getFrameworkContractIds().isEmpty()) {
			map.put("frameworkContractIds", waybillReq.getFrameworkContractIds());
		}

		if (StringUtils.isNotBlank(waybillReq.getShippingNoteNumber())) {
			map.put("shippingNoteNumber", waybillReq.getShippingNoteNumber());
		}
		if (StringUtils.isNotBlank(waybillReq.getCreateTimeStart())) {
			map.put("createTimeStart", waybillReq.getCreateTimeStart() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(waybillReq.getCreateTimeEnd())) {
			map.put("createTimeEnd", waybillReq.getCreateTimeEnd() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillReq.getPayTimeStart())) {
			map.put("payTimeStart", waybillReq.getPayTimeStart() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(waybillReq.getPayTimeEnd())) {
			map.put("payTimeEnd", waybillReq.getPayTimeEnd() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillReq.getRemark())) {
			map.put("remark", waybillReq.getRemark());
		}
		if (StringUtils.isNotBlank(waybillReq.getDescriptionOfGoods())) {
			map.put("descriptionOfGoods", waybillReq.getDescriptionOfGoods());
		}
		if (waybillReq.getFeeUnit() != null) {
			map.put("feeUnit", waybillReq.getFeeUnit());
		}
		if (waybillReq.getNextCustomerId() != null) {
			map.put("nextCustomerId", waybillReq.getNextCustomerId());
		}
		if (StringUtils.isNotBlank(waybillReq.getBillHead())) {
			map.put("billHead", waybillReq.getBillHead());
		}
		if (StringUtils.isNotNull(waybillReq.getTaxUploadState())) {
			// 税务上报状态 0 不上报 1 上报异常 2 上报正常
			if (waybillReq.getTaxUploadState() == 0) {
				map.put("isTaxUpload", 1);
			}
			if (waybillReq.getTaxUploadState() == 1 || waybillReq.getTaxUploadState() == 2) {
				map.put("isTaxUpload", 0);
			}
			if (waybillReq.getTaxUploadState() == 1) {
				map.put("taxUploadFail", 0);
			}
			if (waybillReq.getTaxUploadState() == 2) {
				map.put("taxUploadSuccess", 0);
			}
		}
		if (StringUtils.isNotNull(waybillReq.getRiskResultGrade())) {
			map.put("riskResultGrade", waybillReq.getRiskResultGrade());
		}
		if (waybillReq.getShippingNoteNumbers() != null) {
			map.put("shippingNoteNumbers", waybillReq.getShippingNoteNumbers());
		}
		if (waybillReq.getConsignorAddersDetails() != null) {
			map.put("consignorAddersDetails", waybillReq.getConsignorAddersDetails());
		}
		if (waybillReq.getConsigneeAddersDetails() != null) {
			map.put("consigneeAddersDetails", waybillReq.getConsigneeAddersDetails());
		}
		List<WaybillRes> list = new ArrayList<>();
		if (SHIPPER.equals(SecurityUtils.getClientType())) {
			PageUtils.startPage();
			list = waybillMapperEx.waybillListForTenantStatement(map);
		} else if (FINANCE.equals(SecurityUtils.getClientType())) {
			PageUtils.startPage(false);
			list = waybillMapperEx.waybillListFinanceStatement(map);
			list = list.stream().sorted(Comparator.comparing(WaybillRes::getCreateTime).reversed().thenComparing(WaybillRes::getId)).collect(Collectors.toList());
		}
		List<Long> waybillIds = list.stream().map(WaybillRes::getId).collect(Collectors.toList());
		if (StringUtils.isNotEmpty(waybillIds)) {
			Map<Long, String> waybillGoodsMap = waybillGoodsMapperEx.selectGoodsByWaybillIds(waybillIds).stream()
					.collect(Collectors.toMap(WaybillGoods::getWaybillId, WaybillGoods::getDescriptionOfGoods));
			list.forEach(waybill -> {
				if (waybillGoodsMap.containsKey(waybill.getId())) {
					waybill.setDescriptionOfGoods(waybillGoodsMap.get(waybill.getId()));
				}
			});
		}
		for (WaybillRes w : list) {
			// 1、处理费率和费用
			dealRateAndFare(w);
			// 2、处理上报状态
			if (w.getIsTaxUpload() == 0) {
				// 根据状态处理上报展示的逻辑去除，暂时改为仅根据上报原因是否为空进行处理
				// w.setTaxResSummaryState(TaxUploadConstants.TAX_RES_NORMAL);
				// if (!ywkUploadService.uploadSummaryStatusIsSuccess(w, TaxUploadConstants.SECOND_UPLOAD)) {
				// w.setTaxResSummaryState(TaxUploadConstants.TAX_RES_ABNORMAL);
				// }
				// 3、处理上报失败原因
				if (CommonUtil.isNotNullOrEmpty(w.getTaxUploadFailReason())) {
					w.setTaxUploadFailReason(ywkUploadService.getFailReason(w.getTaxUploadFailReason()).getTotalUploadFailReasons());
				}
				w.setTaxResSummaryState(CommonUtil.isNullOrEmpty(w.getTaxUploadFailReason()) ? TaxUploadConstants.TAX_RES_NORMAL : TaxUploadConstants.TAX_RES_ABNORMAL);
			}

			// 处理地址
			w.setPlaceOfLoading(waybillService.getAddress(w.getLoadingProvince(), w.getLoadingCity(), w.getLoadingArea(), w.getPlaceOfLoading()));
			w.setGoodsReceiptPlace(waybillService.getAddress(w.getUnloadProvince(), w.getUnloadCity(), w.getUnloadArea(), w.getGoodsReceiptPlace()));

			w.setConsignorAddress(waybillService.getAllAddress(w.getLoadingProvince(), w.getLoadingCity(), w.getLoadingArea(), w.getPlaceOfLoading()));
			w.setConsigneeAddress(waybillService.getAllAddress(w.getUnloadProvince(), w.getUnloadCity(), w.getUnloadArea(), w.getGoodsReceiptPlace()));

		}
		return list;
	}

	/**
	 * 待申请开票运单列表合计--托运人、物流金融端
	 *
	 * @param waybillReq
	 * @return
	 */
	public CommonResult<Integer> waybillListForStatementCount(WaybillReq waybillReq) {

		Map<String, Object> map = new HashMap<>();
		map.put("customerId", SecurityUtils.getShipperId());
		if (waybillReq.getBusinessModelId() != null) {
			map.put("BusinessModelId", waybillReq.getBusinessModelId());
		}
		if (waybillReq.getIsRecorder() != null) {
			map.put("isRecorder", waybillReq.getIsRecorder());
		}
		if (!StringUtils.isEmpty(waybillReq.getContractIds())) {
			map.put("contractIds", waybillReq.getContractIds());
		}
		if (waybillReq.getFrameworkContractIds() != null && !waybillReq.getFrameworkContractIds().isEmpty()) {
			map.put("frameworkContractIds", waybillReq.getFrameworkContractIds());
		}

		if (StringUtils.isNotBlank(waybillReq.getShippingNoteNumber())) {
			map.put("shippingNoteNumber", waybillReq.getShippingNoteNumber());
		}
		if (StringUtils.isNotBlank(waybillReq.getCreateTimeStart())) {
			map.put("createTimeStart", waybillReq.getCreateTimeStart() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(waybillReq.getCreateTimeEnd())) {
			map.put("createTimeEnd", waybillReq.getCreateTimeEnd() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillReq.getPayTimeStart())) {
			map.put("payTimeStart", waybillReq.getPayTimeStart() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(waybillReq.getPayTimeEnd())) {
			map.put("payTimeEnd", waybillReq.getPayTimeEnd() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillReq.getRemark())) {
			map.put("remark", waybillReq.getRemark());
		}
		if (StringUtils.isNotBlank(waybillReq.getDescriptionOfGoods())) {
			map.put("descriptionOfGoods", waybillReq.getDescriptionOfGoods());
		}
		if (waybillReq.getFeeUnit() != null) {
			map.put("feeUnit", waybillReq.getFeeUnit());
		}
		if (waybillReq.getNextCustomerId() != null) {
			map.put("nextCustomerId", waybillReq.getNextCustomerId());
		}
		if (StringUtils.isNotBlank(waybillReq.getBillHead())) {
			map.put("billHead", waybillReq.getBillHead());
		}
		if (StringUtils.isNotNull(waybillReq.getTaxUploadState())) {
			// 税务上报状态 0 不上报 1 上报异常 2 上报正常
			if (waybillReq.getTaxUploadState() == 0) {
				map.put("isTaxUpload", 1);
			}
			if (waybillReq.getTaxUploadState() == 1 || waybillReq.getTaxUploadState() == 2) {
				map.put("isTaxUpload", 0);
			}
			if (waybillReq.getTaxUploadState() == 1) {
				map.put("taxUploadFail", 0);
			}
			if (waybillReq.getTaxUploadState() == 2) {
				map.put("taxUploadSuccess", 0);
			}
		}
		if (StringUtils.isNotNull(waybillReq.getRiskResultGrade())) {
			map.put("riskResultGrade", waybillReq.getRiskResultGrade());
		}
		if (waybillReq.getShippingNoteNumbers() != null) {
			map.put("shippingNoteNumbers", waybillReq.getShippingNoteNumbers());
		}
		if (waybillReq.getConsignorAddersDetails() != null) {
			map.put("consignorAddersDetails", waybillReq.getConsignorAddersDetails());
		}
		if (waybillReq.getConsigneeAddersDetails() != null) {
			map.put("consigneeAddersDetails", waybillReq.getConsigneeAddersDetails());
		}
		Integer cnt = 0;
		if (SHIPPER.equals(SecurityUtils.getClientType())) {
			cnt = waybillMapperEx.waybillListForTenantStatementCount(map);
		} else if (FINANCE.equals(SecurityUtils.getClientType())) {
			cnt = waybillMapperEx.waybillListFinanceStatementCount(map);
		}

		return CommonResult.success(cnt);
	}

	private void dealRateAndFare(WaybillRes waybill) {
		if (null == waybill.getRate()) {
			waybill.setRate(BigDecimal.ZERO);
		}
		waybill.setInvoiceFare(waybill.getActualFare().divide(BigDecimal.ONE.subtract(waybill.getRate()), 2, RoundingMode.HALF_UP));
		int unit = waybill.getFeeUnit();
		if (3 == unit) {
			waybill.setFeeAmount(BigDecimal.ONE);
		} else {
			// 货源类型（0：三方 1：大宗）
			if (1 == waybill.getSupplyType()) {
				// 大宗货源，获取运费结算方式（0：按装货数量结算 1：按卸货数量结算 2：按最小值结算）
				if (0 == waybill.getWaybillPaymentType()) {
					waybill.setFeeAmount(1 == unit ? waybill.getLoadingWeight() : waybill.getLoadingCube());
				} else if (1 == waybill.getWaybillPaymentType()) {
					waybill.setFeeAmount(1 == unit ? waybill.getUnloadWeight() : waybill.getUnloadCube());
				} else if (2 == waybill.getWaybillPaymentType()) {
					waybill.setFeeAmount(1 == unit ? NumberUtil.min(waybill.getLoadingWeight(), waybill.getUnloadWeight()) : NumberUtil.min(waybill.getLoadingCube(), waybill.getUnloadCube()));
				}
			}
		}
	}

	/**
	 * 托运人新增运单
	 *
	 * @param req
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public CommonResult shipperAddWaybill(WaybillUpdateRequest req) {
		BigDecimal receiveFare = req.getReceiveFare();
		// 1、校验参数
		req.setIsUpdate(false);
		// 新增的时候肯定没有实付。
		req.setActualFare(BigDecimal.ZERO);
		req.setPayeeIdentityCard(req.getIdentityCard());
		CommonResult judgeResult = judgeParameter(req);
		if (CommonResult.isNotSuccess(judgeResult)) {
			return judgeResult;
		}
		Long waybillId = TextUtil.getTimeSequenceID(5);
		// 2、校验货源是否需要新增，需要的话进行新增操作
		CommonResult validateMakeCodeRes = makeCodeService.validateThenAddMakeCode(req, waybillId);
		if (CommonResult.isNotSuccess(validateMakeCodeRes)) {
			return validateMakeCodeRes;
		}
		// 3、运单信息填充保存
		Waybill waybill = generateWaybillPCInfo(req, waybillId);
		// 3.1、采用复制运单的方式时，如需要同步运单附件，则走以下操作
		if (req.getWaybillCopyType() != null && req.getWaybillCopyType() == 2) {
			List<String> fileNames = Arrays.asList("到货附件", "发货附件", "运输附件");
			List<WaybillAttachmentInfo> waybillAttachmentInfos = waybillAttachmentInfoService.selectWaybillAttachmentList(req.getId(), fileNames);
			for (WaybillAttachmentInfo waybillAttachmentInfo : waybillAttachmentInfos) {
				waybillAttachmentInfo.setId(TextUtil.getTimeSequenceID(5));
				waybillAttachmentInfo.setRelationId(waybillId);
				waybillAttachmentInfoService.insertWaybillAttachmentInfo(waybillAttachmentInfo);
			}
			if (CommonUtil.isNotNullOrEmpty(waybillAttachmentInfos)) {
				waybill.setReceiptStatus(0); // 回单是否已上传（0上传，1未上传）
			}
		}
		// 3.2、写入运单表
		if (receiveFare != null) {
			waybill.setReceiveFare(receiveFare);
		}
		this.insertWaybill(waybill);
		// 4、写入运单子表
		WaybillSlave waybillSlave = new WaybillSlave();
		waybillSlave.setId(waybillId);
		// PC端异常补录的大宗货源的运单，默认写入发货数量、卸货数量
		// if (SupplyType.BULK.equals(req.getMakeCode().getSupplyType())) {
		// 应收单位(1.吨 2.立方米 3.车)
		if (1 == req.getFeeUnit()) {// 应收单位 1.吨
			waybillSlave.setLoadingWeight(req.getFeeAmount());
			waybillSlave.setUnloadWeight(req.getFeeAmount());
		} else if (2 == req.getFeeUnit()) {// 应收单位 2.立方米
			waybillSlave.setLoadingCube(req.getFeeAmount());
			waybillSlave.setUnloadCube(req.getFeeAmount());
		}
		// }
		waybillCommonService.fillTransportCapacityToWaybillSlave(waybill, waybillSlave);
		waybillSlave.setPolicyType(req.getPolicyType());
		waybillSlave.setRemark(req.getWaybillRemark());
		waybillSlave.setReceiveFareCalculationType(req.getReceiveFareCalculationType());
		MakeCode makeCode = makeCodeMapper.selectMakeCodeById(req.getMakeCode().getId());
		if (makeCode == null) {
			waybillSlave.setWaybillPaymentType(0);
		} else {
			waybillSlave.setWaybillPaymentType(makeCode.getWaybillPaymentType());
		}

		waybillSlaveService.insertWaybillSlave(waybillSlave);
		// 5、写入运单货物表
		waybillGoodsService.saveWaybillGoodsInfo(waybillId, req.getMakeCode(), req.getFeeUnit(), req.getFeeAmount(), req.getGoodsValue());
		// 6、写入运单托运人关系表 ，这一步操作中会插入es
		waybillCustomerService.saveWaybillCustomer(SecurityUtils.getNickname(), waybill);
		// 7、写入运单转包链关系表
		waybillContractChainService.saveConatractChain(waybillId, waybill.getFrameworkContractId(), SecurityUtils.getNickname());
		// 8、收款人限额表增加数据
		paymentRecordService.addPaymentRecord(waybill);
		// 9、写入托运人承运人关系表
		customerActualService.saveShipperCarrierInfo(waybill.getCustomerId(), req.getCarrierId());
		// 10、写入托运人收款账户关系表
		customerPayeeService.saveShipperPayeeInfo(waybill.getCustomerId(), req.getReceiptId());
		// 11、写入托运人司机关系表
		customerDriverService.saveShipperDriverInfo(waybill.getCustomerId(), req.getDriverId());
		// 12、写入托运人车辆关系表
		customerVehicleService.saveShipperVehicleInfo(waybill.getCustomerId(), req.getVehicleId());
		// 13、写入承运人车辆关系表
		actualVehicleRelationService.checkThenAddCarrierVehicleRelation(waybill);
		// 14、写入承运人司机关系表
		actualDriverRelationService.checkThenAddCarrierDriverRelation(waybill);
		// 14.1、写入网络货运人司机关系表
		freightForwarderDriverRelationService.saveFreightForwarderDriverInfo(waybill.getFreightForwarderId(), req.getDriverId(), waybill.getDrivingLicense());
		// 14.2、写入网络货运人车辆关系表
		freightForwarderVehicleRelationService.saveFreightForwarderVehicleInfo(waybill.getFreightForwarderId(), req.getVehicleId(), waybill.getVehicleNumber());
		// 托运人车队长关系表
		if (waybill.getCarCaptainId() != null && waybill.getCarCaptainId() != 0) {
			customerCaptainRelationService.validateThenAddRelationFromWaybill(waybill.getCustomerId(), waybill.getCarCaptainId());
			freightForwarderCarCaptainRelationService.saveFreightForwarderCapacityInfo(waybill.getFreightForwarderId(), waybill.getCarCaptainId());
		}
		// 托运人辅助员关系表
		if (waybill.getAuxiliaryStaffId() != null && waybill.getAuxiliaryStaffId() != 0) {
			customerAuxiliaryRelationService.validateThenAddRelationFromWaybill(waybill.getCustomerId(), waybill.getAuxiliaryStaffId());
			freightForwarderAuxiliaryRelationService.saveFreightForwarderAuxiliaryInfo(waybill.getFreightForwarderId(), waybill.getAuxiliaryStaffId());
		}
		// 14.3、写入网络货运人承运人关系表
		List<Waybill> waybillList = new ArrayList<>();
		waybillList.add(waybill);
		freightForwarderActualRelationService.saveFreightForwarderActualRelationByWaybillList(waybillList);
		// 15、保存运单附件
		if (CommonUtil.isNotNullOrEmpty(req.getFileSaveList())) {
			for (FileSaveRequest tmp : req.getFileSaveList()) {
				tmp.setFileCategory(FileCategory.WAYBILL.val());
				tmp.setBusinessId(waybillId);
				attachmentService.addFile(tmp);
			}
		}
		// 16、合作方来源，已到达的运单，走确认到达操作
		if (waybill.getStatus() == BusinessConstants.WAYBILL_STATUS_COMPLETED) {
			this.waybillConfirmArrival(Arrays.asList(waybillId));
		}
		// 17、日志记录
		String actionName = "新增运单(运单ID：" + waybillId + "，运单号：" + waybill.getShippingNoteNumber() + ")";
		actionName += "，请求参数：" + JSONUtil.toJsonStr(req);
		sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_WAYBILL, BusinessConstants.ACTION_TYPE_ADD, org.apache.commons.lang3.StringUtils.substring(actionName, 0, 5000),
				waybill.getShippingNoteNumber());
		// 18、如果不是合作方来源的已到达运单，需进行税务起运上报，发送MQ
		if (waybill.getIsTaxUpload() == 0 && waybill.getStatus() != BusinessConstants.WAYBILL_STATUS_COMPLETED) {
			taxingRulesService.sendMqInfoAfterCommit(TaxUploadConstants.FIRST_UPLOAD, waybill);
		}
		// 同步善道4.0
		// pushWaybillReachShanDao(Arrays.asList(waybillId), 0);
		return CommonResult.success(waybill.getShippingNoteNumber());
	}

	/**
	 * PC异常录单
	 *
	 * @param req
	 * @param waybillId
	 * @return
	 */
	private Waybill generateWaybillPCInfo(WaybillUpdateRequest req, Long waybillId) {
		// 0、数据准备
		Waybill waybill = new Waybill();
		BeanUtil.copyProperties(req, waybill);
		FrameworkContract contract = req.getContract();
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(SecurityUtils.getShipperId());
		// 1、基础信息
		waybill.setId(waybillId);
		waybill.setResource(null != req.getResource() ? req.getResource() : WaybillSource.PC.val());
		waybill.setShippingNoteNumber(CodeUtil.generateWaybillCode());
		waybill.setStatus(null == req.getStatus() ? BusinessConstants.WAYBILL_STATUS_LEASE : req.getStatus());// 没有运单状态时默认为2.已签约(未开始)
		waybill.setSettleStatus(BusinessConstants.WAYBILL_SETTLE_STATUS_NOT);
		waybill.setBillStatus(0); // 0.未开票 1.部分开票 2.已开票 3.开票中 4.撤销开票 5.驳回开票
		waybill.setReceiptStatus(1); // 回单是否已上传（0上传，1未上传）
		waybill.setRemark(req.getWaybillRemark());
		waybill.setPayeeType(req.getPayeeType());
		BigDecimal weight = 1 == req.getFeeUnit() ? req.getFeeAmount() : BigDecimal.ZERO;
		BigDecimal volume = 2 == req.getFeeUnit() ? req.getFeeAmount() : BigDecimal.ZERO;
		waybill.setWeightCube(waybillCommonService.getWeightCubeByWeightAndCube(weight, volume)); // 货物重量/体积
		waybill.setCreateBy(SecurityUtils.getNickname());
		waybill.setCreateTime(DateUtils.getNowDate());
		// 2、录单人信息
		waybill.setCustomerId(customerInfo.getId());
		waybill.setCustomerName(customerInfo.getCustomerName());
		// 3、项目信息
		waybill.setFrameworkContractId(contract.getId());
		waybill.setFrameworkContractCode(contract.getContractCode());
		waybill.setFrameworkContractName(contract.getContractName());
		waybill.setFreightForwarderId(contract.getFreightForwarderId());
		if (!CommonUtil.isEmptyOrZero(contract.getFreightForwarderId())) {
			// 查询配置项
			FreightForwarderConfig freightForwarderConfig = freightForwarderConfigMapper.selectAuditConfigByFreightForwarderId(contract.getFreightForwarderId());
			if (null != freightForwarderConfig && 0 == freightForwarderConfig.getIsAuditWaybill()) {
				waybill.setArtificialAuditState(0);
			}
		}
		// 4、时间相关
		// 计算范围大小
		Random random = new Random();
		int randomInt = random.nextInt(121);
		int randomNumber = -60 - randomInt;
		if (DateUtils.compareDate(DateUtils.strToDate(req.getPlanStartTime()), new Date()) < 0) {
			waybill.setOrderCreateTime(DateUtils.addMinute(DateUtils.strToDate(req.getPlanStartTime()), randomNumber));
		} else {
			waybill.setOrderCreateTime(new Date());
		}
		waybill.setBillDate(DateUtils.strToDate(req.getBillDate(), DateUtils.YYMMDD));
		waybill.setDespatchActualDateTime(DateUtils.strToDate(req.getPlanStartTime())); // 提货时间
		waybill.setGoodsReceiptDateTime(DateUtils.strToDate(req.getPlanEndTime())); // 预计送达时间
		waybill.setLoadTime(DateUtils.strToDate(req.getLoadTime()));
		waybill.setUnloadTime(DateUtils.strToDate(req.getUnloadTime()));
		waybill.setSubmitTime(DateUtils.getNowDate()); // 提交时间
		// 5、固定值
		waybill.setBusinessTypeCode("1002996");
		waybill.setBusinessTypeName("干线普货运输");
		// 6、费用相关
		waybill.setUnpaidFare(req.getPayFare());
		waybill.setPrepayUnpaidMoney(waybill.getPrepayType() == 2 ? BigDecimal.ZERO : req.getPrepayMoney());
		waybill.setPrepayActualMoney(waybill.getPrepayType() == 1 ? BigDecimal.ZERO : req.getPrepayMoney());
		waybill.setArriveUnpaidMoney(req.getArriveMoney());
		waybill.setReceiptUnpaidMoney(req.getReceiptMoney());
		// 7、定位相关
		waybill.setLocateType(this.getWaybillLocateType(contract.getId()));
		waybill.setLocateStatus(waybill.getLocateType() == -1 ? -1 : this.getWaybillLocateStatus(DateUtils.parseDate(req.getPlanStartTime()), DateUtils.parseDate(req.getPlanEndTime())));
		// 8、货源相关
		waybillCommonService.fillWaybillMakeCodeRelatedInfoToWaybill(req.getMakeCode(), waybill);
		// 9、运力相关
		waybill.setActualCarrierId(req.getCarrierId());
		waybill.setActualCarrierName(req.getCarrier().getActualCarrierName());
		waybill.setActualCarrierIdentityCard(req.getCarrier().getIdentityCard());
		waybill.setActualCarrierContactPhone(req.getCarrier().getContactPhone());
		waybill.setPayeeId(req.getReceiptId());
		// 根据收款人id查询到收款人信息，插入收款人身份证号
		PayeeInfo payeeInfo = payeeInfoMapper.selectPayeeInfoById(req.getReceiptId());
		if (null != payeeInfo) {
			waybill.setPayeeIdentityCard(payeeInfo.getIdentityCard());
		}

		if (req.getCarCaptainId() != null && req.getCarCaptainId() != 0) {
			waybill.setCarCaptainName(req.getCarrier().getActualCarrierName());
			waybill.setCarCaptainIdentityCard(req.getCarrier().getIdentityCard());
			waybill.setCarCaptainPhone(req.getCarrier().getContactPhone());
		}
		if (req.getAuxiliaryStaffId() != null && req.getAuxiliaryStaffId() != 0) {
			waybill.setAuxiliaryStaffName(req.getCarrier().getActualCarrierName());
			waybill.setAuxiliaryStaffIdentityCard(req.getCarrier().getIdentityCard());
			waybill.setAuxiliaryStaffPhone(req.getCarrier().getContactPhone());
		}
		// 如果是总费用,把预付到付回单付都变成0
		if (waybill.getPayType() == 0) {
			waybill.setPrepayMoney(BigDecimal.ZERO);
			waybill.setPrepayUnpaidMoney(BigDecimal.ZERO);
			waybill.setArriveMoney(BigDecimal.ZERO);
			waybill.setArriveUnpaidMoney(BigDecimal.ZERO);
			waybill.setPrepayMoney(BigDecimal.ZERO);
			waybill.setPrepayUnpaidMoney(BigDecimal.ZERO);
		}
		waybillCommonService.fillTransportCapicityInfoByIdToWaybill(waybill);
		// 10、代开状态设置
		int proxyInvoiceState = proxyInvoiceService.getProxyInvoiceStateByContractId(waybill.getFrameworkContractId(), waybill.getDrivingLicense(), waybill.getPayeeIdentityCard());
		waybill.setProxyInvoiceState(proxyInvoiceState);

		// 推送已完成
		if (waybill.getResource() == 5 && waybill.getStatus() == 4) {
			if (customerInfo.getConfirmReceiptState() == 1) {
				waybill.setConfirmReceiptState(0);
			}
		}
		return waybill;
	}

	private Waybill generateOrderPCInfo(WaybillUpdateRequest req, Long waybillId) {
		// 0、数据准备
		Waybill waybill = new Waybill();
		BeanUtil.copyProperties(req, waybill);
		FrameworkContract contract = req.getContract();
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(SecurityUtils.getShipperId());
		// 1、基础信息
		waybill.setId(waybillId);
		waybill.setResource(16);
		waybill.setStatus(null == req.getStatus() ? BusinessConstants.WAYBILL_STATUS_LEASE : req.getStatus());// 没有运单状态时默认为2.已签约(未开始)
		waybill.setSettleStatus(BusinessConstants.WAYBILL_SETTLE_STATUS_NOT);
		waybill.setBillStatus(0); // 0.未开票 1.部分开票 2.已开票 3.开票中 4.撤销开票 5.驳回开票
		waybill.setReceiptStatus(1); // 回单是否已上传（0上传，1未上传）
		waybill.setRemark(req.getWaybillRemark());
		waybill.setPayeeType(req.getPayeeType());
		BigDecimal weight = 1 == req.getFeeUnit() ? req.getFeeAmount() : BigDecimal.ZERO;
		BigDecimal volume = 2 == req.getFeeUnit() ? req.getFeeAmount() : BigDecimal.ZERO;
		waybill.setWeightCube(waybillCommonService.getWeightCubeByWeightAndCube(weight, volume)); // 货物重量/体积
		waybill.setCreateBy(SecurityUtils.getNickname());
		waybill.setCreateTime(DateUtils.getNowDate());
		// 2、录单人信息
		waybill.setCustomerId(customerInfo.getId());
		waybill.setCustomerName(customerInfo.getCustomerName());
		// 3、项目信息
		waybill.setFrameworkContractId(contract.getId());
		waybill.setFrameworkContractCode(contract.getContractCode());
		waybill.setFrameworkContractName(contract.getContractName());
		waybill.setFreightForwarderId(contract.getFreightForwarderId());
		if (!CommonUtil.isEmptyOrZero(contract.getFreightForwarderId())) {
			// 查询配置项
			FreightForwarderConfig freightForwarderConfig = freightForwarderConfigMapper.selectAuditConfigByFreightForwarderId(contract.getFreightForwarderId());
			if (null != freightForwarderConfig && 0 == freightForwarderConfig.getIsAuditWaybill()) {
				waybill.setArtificialAuditState(0);
			}
		}
		// 4、时间相关
		// 计算范围大小
		Random random = new Random();
		int randomInt = random.nextInt(121);
		int randomNumber = -60 - randomInt;
		if (DateUtils.compareDate(DateUtils.strToDate(req.getPlanStartTime()), new Date()) < 0) {
			waybill.setOrderCreateTime(DateUtils.addMinute(DateUtils.strToDate(req.getPlanStartTime()), randomNumber));
		} else {
			waybill.setOrderCreateTime(new Date());
		}
		waybill.setBillDate(DateUtils.strToDate(req.getBillDate(), DateUtils.YYMMDD));
		waybill.setDespatchActualDateTime(DateUtils.strToDate(req.getPlanStartTime())); // 提货时间
		waybill.setGoodsReceiptDateTime(DateUtils.strToDate(req.getPlanEndTime())); // 预计送达时间
		waybill.setLoadTime(DateUtils.strToDate(req.getLoadTime()));
		waybill.setUnloadTime(DateUtils.strToDate(req.getUnloadTime()));
		waybill.setSubmitTime(DateUtils.getNowDate()); // 提交时间
		// 5、固定值
		waybill.setBusinessTypeCode("1002996");
		waybill.setBusinessTypeName("干线普货运输");
		// 6、费用相关
		waybill.setUnpaidFare(req.getPayFare());
		waybill.setPrepayUnpaidMoney(waybill.getPrepayType() == 2 ? BigDecimal.ZERO : req.getPrepayMoney());
		waybill.setPrepayActualMoney(waybill.getPrepayType() == 1 ? BigDecimal.ZERO : req.getPrepayMoney());
		waybill.setArriveUnpaidMoney(req.getArriveMoney());
		waybill.setReceiptUnpaidMoney(req.getReceiptMoney());
		// 7、定位相关
		waybill.setLocateType(this.getWaybillLocateType(contract.getId()));
		waybill.setLocateStatus(waybill.getLocateType() == -1 ? -1 : this.getWaybillLocateStatus(DateUtils.parseDate(req.getPlanStartTime()), DateUtils.parseDate(req.getPlanEndTime())));
		// 8、货源相关
		// waybillCommonService.fillWaybillMakeCodeRelatedInfoToWaybill(req.getMakeCode(), waybill);
		// 9、运力相关
		waybill.setActualCarrierId(req.getCarrierId());
		waybill.setActualCarrierName(req.getCarrier().getActualCarrierName());
		waybill.setActualCarrierIdentityCard(req.getCarrier().getIdentityCard());
		waybill.setActualCarrierContactPhone(req.getCarrier().getContactPhone());
		waybill.setPayeeId(req.getReceiptId());
		if (req.getCarCaptainId() != null && req.getCarCaptainId() != 0) {
			waybill.setCarCaptainName(req.getCarrier().getActualCarrierName());
			waybill.setCarCaptainIdentityCard(req.getCarrier().getIdentityCard());
			waybill.setCarCaptainPhone(req.getCarrier().getContactPhone());
		}
		if (req.getAuxiliaryStaffId() != null && req.getAuxiliaryStaffId() != 0) {
			waybill.setAuxiliaryStaffName(req.getCarrier().getActualCarrierName());
			waybill.setAuxiliaryStaffIdentityCard(req.getCarrier().getIdentityCard());
			waybill.setAuxiliaryStaffPhone(req.getCarrier().getContactPhone());
		}
		// 如果是总费用,把预付到付回单付都变成0
		if (waybill.getPayType() == 0) {
			waybill.setPrepayMoney(BigDecimal.ZERO);
			waybill.setPrepayUnpaidMoney(BigDecimal.ZERO);
			waybill.setArriveMoney(BigDecimal.ZERO);
			waybill.setArriveUnpaidMoney(BigDecimal.ZERO);
			waybill.setPrepayMoney(BigDecimal.ZERO);
			waybill.setPrepayUnpaidMoney(BigDecimal.ZERO);
		}
		waybillCommonService.fillTransportCapicityInfoByIdToWaybill(waybill);
		// 10、代开状态设置
		int proxyInvoiceState = proxyInvoiceService.getProxyInvoiceStateByContractId(waybill.getFrameworkContractId(), waybill.getDrivingLicense(), waybill.getPayeeIdentityCard());
		waybill.setProxyInvoiceState(proxyInvoiceState);

		// 推送已完成
		if (waybill.getResource() == 5 && waybill.getStatus() == 4) {
			if (customerInfo.getConfirmReceiptState() == 1) {
				waybill.setConfirmReceiptState(0);
			}
		}
		return waybill;
	}

	@Override
	public Integer getWaybillLocateType(Long contractId) {
		// 运单来源：pc补录；定位方式：北斗定位
		boolean hasLocation = false;
		ServiceInfo service = frameworkContractService.contractServiceInfo(contractId).getData();
		if (service != null && service.getIsLocate() == 1) {
			hasLocation = true;
		}
		// 有定位：录单时间早于预计发车时间 或 早于预计到达时间
		// 不定位：录单时间晚于到达时间
		// -1:未设置 0:GPS
		if (hasLocation) {
			return 0;
		}
		return -1;
	}

	@Override
	public Integer getWaybillLocateStatus(Date planStartTime, Date planEndTime) {
		// 运单来源：pc补录；定位方式：北斗定位
		// 录单时间早于预计发车时间：根据发车时间，开始定位
		// 录单时间晚于发车时间，但是早于预计到达时间：根据录单时间，开始定位
		// 录单时间晚于到达时间，不定位，状态是未开始
		boolean afterStartTime = new Date().after(planStartTime);
		boolean afterEndTime = new Date().after(planEndTime);
		if (afterStartTime && !afterEndTime) {
			return BusinessConstants.WAYBILL_LOCATE_STATUS_POSITIONING;
		}
		if (afterEndTime) {
			return BusinessConstants.WAYBILL_LOCATE_STATUS_NO_POSITIONING;
		}
		return BusinessConstants.WAYBILL_LOCATE_STATUS_NOTSTARTED;
	}

	/**
	 * 校验托运人新增、编辑运单参数合法性
	 *
	 * @param waybillReq
	 * 		编辑运单传 true，新增运单传 false
	 * @return
	 */
	private CommonResult judgeParameter(WaybillUpdateRequest waybillReq) {
		// 1、运单基础信息校验 协议单号、单据日期、备注
		CommonResult validateWaybillBasicInfoRes = waybillCommonService.validateWaybillBasicInfo(waybillReq);
		if (CommonResult.isNotSuccess(validateWaybillBasicInfoRes)) {
			return validateWaybillBasicInfoRes;
		}
		// 2、运单调度信息校验 除基础、运力外的其他信息
		CommonResult validateWaybillDispatchInfoRes = waybillCommonService.validateWaybillDispatchInfo(waybillReq);
		if (CommonResult.isNotSuccess(validateWaybillDispatchInfoRes)) {
			return validateWaybillDispatchInfoRes;
		}
		// 3、运力信息校验
		CommonResult validateWaybillCapacity = waybillCommonService.validateWaybillCapacity(waybillReq);
		if (CommonResult.isNotSuccess(validateWaybillCapacity)) {
			return validateWaybillCapacity;
		}
		FrameworkContract contract = waybillReq.getContract();
		// 4、投保服务权限校验
		if (null == waybillReq.getPolicyType()) {
			return CommonResult.requestError("投保状态不可为空");
		}
		// policyType 保险类型(-1:未设置 0:物责险 1:货物险 2:龙琨货运险)
		if (Arrays.asList(0, 1, 2).contains(waybillReq.getPolicyType())) {
			// insuranceState 保险状态：1 审核中 2 审核完成 3 无需审核
			if (1 == contract.getInsuranceState()) {
				return CommonResult.requestError("项目合约保险服务审核中，目前保险服务不可用");
			} else if (3 == contract.getInsuranceState()) {
				return CommonResult.requestError("项目合约未开通保险服务");
			}
		}
		// 5、新增时，进行承运人收款限额校验
		if (!waybillReq.getIsUpdate()) {
			// 检查当前收款人是否在收款人年收入白名单里
			boolean containsIdentityCard = false;
			SysParameter sysParameter = new SysParameter();
			sysParameter.setCode("ANNUAL_COLLECTION_AMOUNT_WHITE");
			sysParameter.setType(1);
			sysParameter.setState(0);
			List<SysParameter> sysParameters = sysParameterService.selectSysParameterList(sysParameter);
			if (CommonUtil.isNotNullOrEmpty(sysParameters)) {
				String paraValue = sysParameters.get(0).getParaValue();// 获取白名单中的身份证
				if (StringUtils.isNotBlank(paraValue)) {
					containsIdentityCard = paraValue.contains(waybillReq.getPayee().getIdentityCard());
				}
			}
			// 如果不在白名单，校验已收款是否超限
			if (!containsIdentityCard) {
				Waybill waybillWrapper = new Waybill();
				BeanUtils.copyProperties(waybillReq, waybillWrapper);
				waybillWrapper.setCustomerId(SecurityUtils.getShipperId());
				waybillWrapper.setFreightForwarderId(contract.getFreightForwarderId());
				waybillWrapper.setPayeeId(waybillReq.getPayee().getId());
				waybillWrapper.setPayeeIdentityCard(waybillReq.getPayee().getIdentityCard());
				waybillWrapper.setPayeeName(waybillReq.getPayee().getPayeeName());
				waybillWrapper.setPayeeType(waybillReq.getPayeeType());
				CommonResult result = paymentRecordService.verifyPaymentRecord(waybillWrapper);
				if (CommonResult.isNotSuccess(result)) {
					return CommonResult.requestPopError(result.getMsg());
				}
			}
		}
		// 司机信息
		if (waybillReq.getPayeeType() == 1) {
			ActualCarrierInfo carrierInfo = actualCarrierInfoMapper.selectByIdentityCard(waybillReq.getPayee().getIdentityCard());
			if (carrierInfo != null) {
				carrierInfo.setContactPhone(waybillReq.getDriver().getTelephone());
				waybillReq.setCarrier(carrierInfo);
				waybillReq.setCarrierId(carrierInfo.getId());
			}
		}
		// 车队长信息
		if (waybillReq.getPayeeType() == 2) {
			ActualCarrierInfo carrierInfo = actualCarrierInfoMapper.selectByIdentityCard(waybillReq.getPayee().getIdentityCard());
			if (carrierInfo != null) {
				waybillReq.setCarrier(carrierInfo);
				waybillReq.setCarrierId(carrierInfo.getId());
				CarCaptainInfo carCaptainInfo = carCaptainInfoMapper.selectCarCaptainInfoById(carrierInfo.getCarCaptainId());
				if (carCaptainInfo != null) {
					CustomerCaptainRelation relation = new CustomerCaptainRelation();
					relation.setCarCaptainId(carCaptainInfo.getId());
					relation.setCustomerId(SecurityUtils.getShipperId());
					List<CustomerCaptainRelation> customerCaptainRelationList = customerCaptainRelationMapper.selectCustomerCaptainRelationList(relation);
					if (customerCaptainRelationList != null && customerCaptainRelationList.size() != 0) {
						carrierInfo.setContactPhone(customerCaptainRelationList.get(0).getCarCaptainContactPhone());
						waybillReq.setCarrier(carrierInfo);
					}
					waybillReq.setCarCaptainId(carCaptainInfo.getId());
				}
			}
		}

		// 辅助员信息
		if (waybillReq.getPayeeType() == 3) {
			ActualCarrierInfo carrierInfo = actualCarrierInfoMapper.selectByIdentityCard(waybillReq.getPayee().getIdentityCard());
			if (carrierInfo != null) {
				waybillReq.setCarrier(carrierInfo);
				waybillReq.setCarrierId(carrierInfo.getId());
				AuxiliaryStaffInfo auxiliaryStaffInfo = auxiliaryStaffInfoMapper.selectAuxiliaryStaffInfoById(carrierInfo.getAuxiliaryStaffId());
				if (auxiliaryStaffInfo != null) {
					CustomerAuxiliaryRelation relation = new CustomerAuxiliaryRelation();
					relation.setAuxiliaryStaffId(auxiliaryStaffInfo.getId());
					relation.setCustomerId(SecurityUtils.getShipperId());
					List<CustomerAuxiliaryRelation> customerAuxiliaryRelationList = customerAuxiliaryRelationMapper.selectCustomerAuxiliaryRelationList(relation);
					if (customerAuxiliaryRelationList != null && customerAuxiliaryRelationList.size() != 0) {
						carrierInfo.setContactPhone(customerAuxiliaryRelationList.get(0).getAuxiliaryStaffContactPhone());
						waybillReq.setCarrier(carrierInfo);
					}
					waybillReq.setAuxiliaryStaffId(auxiliaryStaffInfo.getId());
				}
			}
		}
		// 如果是善道的业务，则进行辅助员签约判断
		if (!auxiliaryStaffInfoService.validateShandaoRelationIsOk(contract.getFreightForwarderId(), null, waybillReq.getAuxiliaryStaffId())) {
			return CommonResult.error("关联辅助员未与平台签约，无法接单，请联系平台");
		}
		return CommonResult.success();
	}

	/**
	 * 校验托运人新增订单参数合法性
	 *
	 * @param waybillReq
	 * 		编辑运单传 true，新增运单传 false
	 * @return
	 */
	private CommonResult orderJudgeParameter(WaybillUpdateRequest waybillReq) {
		// 1、运单基础信息校验 协议单号、单据日期、备注
		CommonResult validateWaybillBasicInfoRes = waybillCommonService.validateWaybillBasicInfo(waybillReq);
		if (CommonResult.isNotSuccess(validateWaybillBasicInfoRes)) {
			return validateWaybillBasicInfoRes;
		}
		// 2、运单调度信息校验 除基础、运力外的其他信息
		CommonResult validateWaybillDispatchInfoRes = waybillCommonService.validateWaybillDispatchInfo(waybillReq);
		if (CommonResult.isNotSuccess(validateWaybillDispatchInfoRes)) {
			return validateWaybillDispatchInfoRes;
		}
		// 3、运力信息校验
		CommonResult validateWaybillCapacity = waybillCommonService.validateOrderCapacity(waybillReq);
		if (CommonResult.isNotSuccess(validateWaybillCapacity)) {
			return validateWaybillCapacity;
		}
		FrameworkContract contract = waybillReq.getContract();
		// 4、投保服务权限校验
		if (null == waybillReq.getPolicyType()) {
			return CommonResult.requestError("投保状态不可为空");
		}
		// policyType 保险类型(-1:未设置 0:物责险 1:货物险 2:龙琨货运险)
		if (Arrays.asList(0, 1, 2).contains(waybillReq.getPolicyType())) {
			// insuranceState 保险状态：1 审核中 2 审核完成 3 无需审核
			if (1 == contract.getInsuranceState()) {
				return CommonResult.requestError("项目合约保险服务审核中，目前保险服务不可用");
			} else if (3 == contract.getInsuranceState()) {
				return CommonResult.requestError("项目合约未开通保险服务");
			}
		}
		// 5、新增时，进行承运人收款限额校验
		if (!waybillReq.getIsUpdate()) {
			// 检查当前收款人是否在收款人年收入白名单里
			boolean containsIdentityCard = false;
			SysParameter sysParameter = new SysParameter();
			sysParameter.setCode("ANNUAL_COLLECTION_AMOUNT_WHITE");
			sysParameter.setType(1);
			sysParameter.setState(0);
			List<SysParameter> sysParameters = sysParameterService.selectSysParameterList(sysParameter);
			if (CommonUtil.isNotNullOrEmpty(sysParameters)) {
				String paraValue = sysParameters.get(0).getParaValue();// 获取白名单中的身份证
				if (StringUtils.isNotBlank(paraValue)) {
					containsIdentityCard = paraValue.contains(waybillReq.getPayee().getIdentityCard());
				}
			}
			// 如果不在白名单，校验已收款是否超限
			if (!containsIdentityCard) {
				Waybill waybillWrapper = new Waybill();
				BeanUtils.copyProperties(waybillReq, waybillWrapper);
				waybillWrapper.setCustomerId(SecurityUtils.getShipperId());
				waybillWrapper.setFreightForwarderId(contract.getFreightForwarderId());
				waybillWrapper.setPayeeId(waybillReq.getPayee().getId());
				waybillWrapper.setPayeeIdentityCard(waybillReq.getPayee().getIdentityCard());
				waybillWrapper.setPayeeName(waybillReq.getPayee().getPayeeName());
				CommonResult result = paymentRecordService.verifyPaymentRecord(waybillWrapper);
				if (CommonResult.isNotSuccess(result)) {
					return CommonResult.requestPopError(result.getMsg());
				}
			}
		}
		// 司机信息
		if (waybillReq.getPayeeType() == 1) {
			ActualCarrierInfo carrierInfo = actualCarrierInfoMapper.selectByIdentityCard(waybillReq.getPayee().getIdentityCard());
			if (carrierInfo != null) {
				carrierInfo.setContactPhone(waybillReq.getDriver().getTelephone());
				waybillReq.setCarrier(carrierInfo);
				waybillReq.setCarrierId(carrierInfo.getId());
			}
		}
		// 车队长信息
		if (waybillReq.getPayeeType() == 2) {
			ActualCarrierInfo carrierInfo = actualCarrierInfoMapper.selectByIdentityCard(waybillReq.getPayee().getIdentityCard());
			if (carrierInfo != null) {
				waybillReq.setCarrier(carrierInfo);
				waybillReq.setCarrierId(carrierInfo.getId());
				CarCaptainInfo carCaptainInfo = carCaptainInfoMapper.selectCarCaptainInfoById(carrierInfo.getCarCaptainId());
				if (carCaptainInfo != null) {
					CustomerCaptainRelation relation = new CustomerCaptainRelation();
					relation.setCarCaptainId(carCaptainInfo.getId());
					relation.setCustomerId(SecurityUtils.getShipperId());
					List<CustomerCaptainRelation> customerCaptainRelationList = customerCaptainRelationMapper.selectCustomerCaptainRelationList(relation);
					if (customerCaptainRelationList != null && customerCaptainRelationList.size() != 0) {
						carrierInfo.setContactPhone(customerCaptainRelationList.get(0).getCarCaptainContactPhone());
						waybillReq.setCarrier(carrierInfo);
					}
					waybillReq.setCarCaptainId(carCaptainInfo.getId());
				}
			}
		}

		// 辅助员信息
		if (waybillReq.getPayeeType() == 3) {
			ActualCarrierInfo carrierInfo = actualCarrierInfoMapper.selectByIdentityCard(waybillReq.getPayee().getIdentityCard());
			if (carrierInfo != null) {
				waybillReq.setCarrier(carrierInfo);
				waybillReq.setCarrierId(carrierInfo.getId());
				AuxiliaryStaffInfo auxiliaryStaffInfo = auxiliaryStaffInfoMapper.selectAuxiliaryStaffInfoById(carrierInfo.getAuxiliaryStaffId());
				if (auxiliaryStaffInfo != null) {
					CustomerAuxiliaryRelation relation = new CustomerAuxiliaryRelation();
					relation.setAuxiliaryStaffId(auxiliaryStaffInfo.getId());
					relation.setCustomerId(SecurityUtils.getShipperId());
					List<CustomerAuxiliaryRelation> customerAuxiliaryRelationList = customerAuxiliaryRelationMapper.selectCustomerAuxiliaryRelationList(relation);
					if (customerAuxiliaryRelationList != null && customerAuxiliaryRelationList.size() != 0) {
						carrierInfo.setContactPhone(customerAuxiliaryRelationList.get(0).getAuxiliaryStaffContactPhone());
						waybillReq.setCarrier(carrierInfo);
					}
					waybillReq.setAuxiliaryStaffId(auxiliaryStaffInfo.getId());
				}
			}
		}
		// 如果是善道的业务，则进行辅助员签约判断
		if (!auxiliaryStaffInfoService.validateShandaoRelationIsOk(contract.getFreightForwarderId(), null, waybillReq.getAuxiliaryStaffId())) {
			return CommonResult.error("关联辅助员未与平台签约，无法接单，请联系平台");
		}
		return CommonResult.success();
	}

	/**
	 * 有运单已申请付款
	 *
	 * @param waybillIds
	 * @return
	 */
	private boolean existPaymentApply(List<Long> waybillIds) {
		return customerTradeApplyMapperEx.existPaymentApply(waybillIds);
	}

	/**
	 * 运单批量确认收货
	 *
	 * @param waybillReq
	 * 		运单ID集合
	 * @return
	 */
	@Transactional
	public CommonResult shipperConfirmArrive(WaybillReq waybillReq) {
		List<Long> ids = waybillReq.getWaybillIds();
		if (null == ids || 0 == ids.size()) {
			return CommonResult.requestError("请选择要确认收货的运单");
		}

		List<Waybill> waybillList = new ArrayList<>();

		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(SecurityUtils.getShipperId());

		// 循环校验运单状态
		for (Long waybillId : ids) {
			Waybill waybill = this.selectWaybillById(waybillId);
			if (waybill == null) {
				return CommonResult.requestError("运单不存在");
			}
			if (waybill.getStatus() == -1) {
				return CommonResult.requestError("运单 [" + waybill.getShippingNoteNumber() + "] 已删除，不能确认收货");
			}
			// 运单运输完成、定位完成都满足的情况下，不可以再更改了，其他情况下都可以
			// status 运单状态(-1.删除 0.订单 1.运单 2.已签约(未开始) 3.运输中 4.已完成)
			// locateStatus 定位状态(-1.未设置(无定位) 0:未开始 1:定位中 2:定位完成 3:车辆未入网 4:定位异常)
			if (waybill.getStatus() == 4) {
				return CommonResult.requestError("运单 [" + waybill.getShippingNoteNumber() + "] 已确认收货，无需重复确认");
			}
			if (WaybillSource.isMobile(waybill.getResource())) {
				return CommonResult.requestError("运单 [" + waybill.getShippingNoteNumber() + "] 来自" + WaybillSource.descOf(waybill.getResource()) + "，请联系司机确认到达");
			}
			if (null == waybill.getDespatchActualDateTime()) {
				return CommonResult.requestError("运单 " + waybill.getShippingNoteNumber() + " 数据错误，预计发车时间为空");
			}
			// 手动选择时间不为空，以手动传入运输完成时间为准
			if (StringUtils.isNotEmpty(waybillReq.getSelectedUnloadTime())) {
				waybill.setUnloadTime(DateUtils.parseDate(waybillReq.getSelectedUnloadTime()));
			} else {
				waybill.setUnloadTime(waybill.getGoodsReceiptDateTime());
			}
			// 单条运单，取前端传入的实际发货时间，并加以校验
			if (ids.size() == 1) {
				if (waybillReq.getSelectedLoadTime() == null) {
					return CommonResult.requestError("运单 " + waybill.getShippingNoteNumber() + " 数据错误，实际发车时间不得为空");
				}
				waybill.setLoadTime(waybillReq.getSelectedLoadTime());
				if (waybill.getLoadTime().after(waybill.getUnloadTime())) {
					return CommonResult.requestError("运单 " + waybill.getShippingNoteNumber() + " 数据错误，实际发车时间不能晚于实际到达时间");
				}
				if (waybill.getUnloadTime().after(DateUtils.getNowDate())) {
					return CommonResult.requestError("运单 " + waybill.getShippingNoteNumber() + " 数据错误，实际到达时间不能晚于当前时间");
				}
			} else { // 多条运单，实际发车时间为空时，取预计发车时间填充
				if (waybill.getLoadTime() == null) {
					waybill.setLoadTime(waybill.getDespatchActualDateTime());
				}
			}

			if (customerInfo.getConfirmReceiptState() == 1) {
				waybill.setConfirmReceiptState(0);
			} else {
				waybill.setConfirmReceiptState(1);
			}
			waybillList.add(waybill);
		}

		// // etc结束指令
		// for (Long id : ids) {
		// // 根据运单id查询到运单信息
		// Waybill waybill = waybillService.selectWaybillById(id);
		// // 目前只有善道实时单开放etc
		// SysParameter sysParameterQuery = new SysParameter();
		// sysParameterQuery.setCode("etcFreightForwarderId");
		// List<SysParameter> sysParameters = sysParameterMapper.selectSysParameterList(sysParameterQuery);
		// sysParameters.stream().findFirst().ifPresent(sysParameter -> {
		// List<String> paramValues = Arrays.asList(sysParameter.getParaValue().split(","));
		// if (paramValues.contains(waybill.getFreightForwarderId().toString())) {
		// WaybillReq waybillReqEtc = new WaybillReq();
		// BeanUtils.copyProperties(waybill, waybillReqEtc);
		// waybillReqEtc.setWaybill(waybill);
		// //waybillService.etcWaybillStart(waybillReqEtc);
		// waybillService.etcWaybillEnd(waybill);
		// }
		// });
		// }
		// 批量确认收货
		for (Waybill waybill : waybillList) {
			confirmArrive(waybill);
		}
		// 确认到达后续操作
		this.waybillConfirmArrival(waybillList.stream().map(Waybill::getId).collect(Collectors.toList()));

		// 记录日志
		List<Long> waybillIds = waybillList.stream().map(Waybill::getId).collect(Collectors.toList());
		List<String> shippingNoteNumbers = waybillList.stream().map(Waybill::getShippingNoteNumber).collect(Collectors.toList());
		String message = "确认到达(运单ID：" + waybillIds + "，运单号：" + shippingNoteNumbers + ")，请求参数：" + JSON.toJSONString(waybillReq);
		sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_WAYBILL, BusinessConstants.ACTION_TYPE_OTHER, message, StringUtils.join(shippingNoteNumbers, ","), waybillIds);
		// 每条运单都记录日志
		for (Waybill waybill : waybillList) {
			sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_WAYBILL, BusinessConstants.ACTION_TYPE_OTHER, message, waybill.getShippingNoteNumber(), Arrays.asList(waybill.getId()));
		}
		// sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_WAYBILL, BusinessConstants.ACTION_TYPE_OTHER, message,StringUtils.join(shippingNoteNumbers,","), waybillIds);
		return CommonResult.success();
	}

	/**
	 * 运单确认收货
	 *
	 * @param waybill
	 * 		运单信息
	 */
	private void confirmArrive(Waybill waybill) {

		Waybill waybillUpdate = waybillService.selectWaybillById(waybill.getId());
		// 运单运输状态改为已完成，运单状态(-1.删除 0.订单 1.运单 2.已签约(未开始) 3.运输中 4.已完成)
		waybillUpdate.setStatus(4);
		waybillUpdate.setLoadTime(waybill.getLoadTime());
		// 如果运输完成时间为空，则设置为当前时间
		if (null == waybill.getUnloadTime()) {
			waybillUpdate.setUnloadTime(new Date());
		} else {
			waybillUpdate.setUnloadTime(waybill.getUnloadTime());
		}
		// 运单开启了定位，并且未结束，则改为定位完成，否则保持原样（未设置、未入网、定位完成）
		if (Arrays.asList(0, 1, 4).contains(waybill.getLocateStatus())) {
			// 定位状态(-1.未设置(无定位) 0:未开始 1:定位中 2:定位完成 3:车辆未入网 4:定位异常)
			waybillUpdate.setLocateStatus(2);
		}
		// 如果收货时间为空，则更新收货时间为当前时间
		if (null == waybill.getGoodsReceiptDateTime()) {
			waybillUpdate.setGoodsReceiptDateTime(new Date());
		} else {
			waybillUpdate.setGoodsReceiptDateTime(waybill.getGoodsReceiptDateTime());
		}

		waybillUpdate.setConfirmReceiptState(waybill.getConfirmReceiptState());

		this.updateWaybill(waybillUpdate);
	}

	/**
	 * 批量插入运单表
	 *
	 * @param waybillList
	 * 		待插入运单列表
	 * @return
	 */
	public int insertWaybillList(List<Waybill> waybillList) {
		return waybillMapperEx.insertWaybillList(waybillList);
	}

	public Integer getAllWaybillCount(Map<String, Object> map) {
		return waybillMapperEx.getAllWaybillCount(map);
	}

	public Integer getMonthWaybills(Map<String, Object> map) {
		return waybillMapperEx.getMonthWaybills(map);
	}

	public int getDayWaybills(Map<String, Object> map) {
		return waybillMapperEx.getDayWaybills(map);
	}

	/**
	 * 查询某项目下，尚未走到付款审核结束的运单id列表
	 *
	 * @param
	 * @return 运单集合
	 */
	public List<Long> selectNotPayApproveWaybillIdsByContractId(Long contractId) {
		return waybillMapper.selectNotPayApproveWaybillIdsByContractId(contractId);
	}

	/**
	 * 批量更新运单表中的司机信息
	 *
	 * @param customerId
	 * @param customerName
	 * @return
	 */
	public int editCustomerInfoOfWaybillByCustomerId(Long customerId, String customerName) {
		Waybill waybill = new Waybill();
		waybill.setCustomerId(customerId);
		List<Waybill> waybills = waybillService.selectWaybillList(waybill);
		if (StringUtils.isNotEmpty(waybills)) {
			waybills.forEach(l -> {
				l.setCustomerName(customerName);
			});
			// 修改es中运单
			try {
				esWaybillService.updateEsWaybillInfos(waybills);
			} catch (Exception e) {
				log.error("更新托运人信息时，修改es运单报错");
			}
		}
		waybill.setCustomerName(customerName);
		return waybillMapperEx.editWaybill(waybill);
	}

	/**
	 * 批量更新运单表中的司机信息
	 *
	 * @param payeeId
	 * @param payeeName
	 * @return
	 */
	public int editPayeeInfoOfWaybillByPayeeId(Long payeeId, String payeeName) {
		Waybill waybill = new Waybill();
		waybill.setPayeeId(payeeId);
		List<Waybill> waybills = waybillService.selectWaybillList(waybill);
		if (StringUtils.isNotEmpty(waybills)) {
			waybills.forEach(l -> {
				l.setPayeeName(payeeName);
			});
			// 修改es中运单
			esWaybillService.updateEsWaybillInfos(waybills);
		}
		waybill.setPayeeName(payeeName);
		return waybillMapperEx.editWaybill(waybill);
	}

	/**
	 * 批量更新运单表中的司机信息
	 *
	 * @param drvierId
	 * @param driverName
	 * @param driverPhone
	 * @return
	 */
	public int editDriverInfoOfWaybillByDriverId(Long drvierId, String driverName, String driverPhone) {
		Waybill waybill = new Waybill();
		waybill.setDriverId(drvierId);
		List<Waybill> waybills = waybillService.selectWaybillList(waybill);
		if (StringUtils.isNotEmpty(waybills)) {
			waybills.forEach(l -> {
				l.setDriverName(driverName);
				l.setTelephone(driverPhone);
			});
			// 修改es中运单
			esWaybillService.updateEsWaybillInfos(waybills);
		}
		waybill.setDriverName(driverName);
		waybill.setTelephone(driverPhone);
		return waybillMapperEx.editWaybill(waybill);
	}

	/**
	 * 更改运单结算状态
	 *
	 * @param waybillReq
	 * @return
	 */
	public int updateWaybillSettleStatus(WaybillReq waybillReq) {
		return waybillMapperEx.updateWaybillSettleStatus(waybillReq);
	}

	/**
	 * 获取对账清单运单信息
	 *
	 * @param waybillIds
	 * 		运单ID
	 * @return
	 */
	public List<WaybillRes> selectStatementWaybillList(List<Long> waybillIds) {
		return waybillMapperEx.selectStatementWaybillList(waybillIds);
	}

	public List<WaybillRes> selectStatementWaybillList(List<Long> waybillIds, Long shipperId) {
		return waybillMapperEx.selectStatementWaybillListByShipperId(waybillIds, shipperId);
	}

	/**
	 * 根据运单号批量查询运单信息
	 *
	 * @param waybillCodes
	 * @return
	 */
	public List<Waybill> selectWaybillList(List<String> waybillCodes) {
		return waybillMapperEx.selectWaybillList(waybillCodes);
	}

	/**
	 * 批量更新运单关联的项目信息
	 *
	 * @param waybillIds
	 * 		运单ID集合
	 * @param contractId
	 * 		项目ID
	 * @param contractCode
	 * 		项目编号
	 * @param contractName
	 * 		项目名称
	 * @return
	 */
	public int updateWaybillFrameContractByIds(List<Long> waybillIds, Long contractId, String contractCode, String contractName) {
		List<Waybill> list = waybillMapper.selectWaybillByIds(waybillIds);
		list.forEach(l -> {
			l.setFrameworkContractId(contractId);
			l.setFrameworkContractCode(contractCode);
			l.setFrameworkContractName(contractName);
		});
		// 修改es中运单
		esWaybillService.updateEsWaybillInfos(list);
		return waybillMapperEx.updateWaybillFrameContractByIds(waybillIds, contractId, contractCode, contractName);
	}

	@Override
	public List<Long> selectUnArriveWaybillByDriverid(Long driverId) {
		return waybillMapperEx.selectUnArriveWaybillByDriverid(driverId);
	}

	@Override
	public int updateWayBillActualCarrierByCarrierIds(List<Long> carrierIds, Long carrierId, String name, String phone) {
		List<Long> list = waybillMapperEx.selectWayBillActualCarrierByCarrierIds(carrierIds);
		if (StringUtils.isNotEmpty(list)) {
			List<Waybill> waybills = waybillMapper.selectWaybillByIds(list);
			waybills.forEach(l -> {
				l.setActualCarrierId(carrierId);
				l.setActualCarrierName(name);
				l.setActualCarrierContactPhone(phone);
			});
			// 修改es中运单
			esWaybillService.updateEsWaybillInfos(waybills);
		}
		return waybillMapperEx.updateWayBillActualCarrierByCarrierIds(carrierIds, carrierId, name, phone);
	}

	@Override
	public void updateDriverInfoByDriverIds(List<Long> driverIds, Driver driver) {
		List<Long> list = waybillMapperEx.selectDriverInfoByDriverIds(driverIds);
		if (StringUtils.isNotEmpty(list)) {
			List<Waybill> waybills = waybillMapper.selectWaybillByIds(list);
			waybills.forEach(l -> {
				l.setDriverId(driver.getId());
				l.setDriverName(driver.getDriverName());
				l.setDrivingLicense(driver.getDrivingLicense());
				l.setTelephone(driver.getTelephone());
			});
			// 修改es中运单
			esWaybillService.updateEsWaybillInfos(waybills);
		}
		waybillMapperEx.updateDriverInfoByDriverIds(driverIds, driver);
	}

	/**
	 * 金融服务系统签约主体托运人运单列表
	 *
	 * @param waybillQuery
	 * @return
	 */
	@Override
	public List<FinancingWaybillRes> selectSignorWaybillList(FinancingWaybillQueryReq waybillQuery) {
		Map<String, Object> map = dealMap(waybillQuery);
		List<FinancingWaybillRes> list = waybillMapperEx.selectSignorWaybillList(map);
		if (null == list) {
			return Collections.emptyList();
		}
		return list;
	}

	public Map<String, Object> dealMap(FinancingWaybillQueryReq waybillQuery) {
		Map<String, Object> map = new HashMap<>();
		map.put("customerId", waybillQuery.getCustomerId()); // 托运人ID
		map.put("businessModelId", waybillQuery.getBusinessModelId()); // 业务模式id
		// 项目分组ID
		if (StringUtils.isNotEmpty(waybillQuery.getContractIds())) {
			map.put("contractIds", waybillQuery.getContractIds());
		}
		if (StringUtils.isNotEmpty(waybillQuery.getContractId())) {
			map.put("contractId", waybillQuery.getContractId());
		}
		if (StringUtils.isNotBlank(waybillQuery.getResource())) {
			map.put("resource", waybillQuery.getResource());
		}
		if (StringUtils.isNotBlank(waybillQuery.getShippingNoteNumber())) {
			map.put("shippingNoteNumber", waybillQuery.getShippingNoteNumber());
		}
		if (StringUtils.isNotBlank(waybillQuery.getDriverInfo())) {
			if (ValidateUtils.checkMobilePhone(StringUtils.deleteWhitespace(waybillQuery.getDriverInfo()))) {
				map.put("driverPhone", StringUtils.deleteWhitespace(waybillQuery.getDriverInfo()));
			} else {
				map.put("driverName", waybillQuery.getDriverInfo().trim());
			}
		}
		if (StringUtils.isNumeric(waybillQuery.getDriverId())) {
			map.put("driverId", waybillQuery.getDriverId());
		}
		if (StringUtils.isNotBlank(waybillQuery.getVehicleNumber())) {
			map.put("vehicleNumber", waybillQuery.getVehicleNumber());
		}
		if (StringUtils.isNotBlank(waybillQuery.getReceiptInfo())) {
			if (ValidateUtils.checkPositiveNumber(StringUtils.deleteWhitespace(waybillQuery.getReceiptInfo()))) {
				map.put("receiptCard", StringUtils.deleteWhitespace(waybillQuery.getReceiptInfo()));
			} else {
				map.put("receiptName", waybillQuery.getReceiptInfo().trim());
			}
		}
		if (StringUtils.isNumeric(waybillQuery.getPayeeId())) {
			map.put("payeeId", waybillQuery.getPayeeId());
		}
		if (StringUtils.isNotBlank(waybillQuery.getFrameworkContractName())) {
			map.put("frameworkContractName", waybillQuery.getFrameworkContractName().trim());
		}
		if (null != waybillQuery.getSettleStatus()) {
			map.put("settleStatus", waybillQuery.getSettleStatus());
		}
		if (null != waybillQuery.getTransportStatus()) {
			map.put("transportStatus", waybillQuery.getTransportStatus());
		}
		if (null != waybillQuery.getBillStatus()) {
			map.put("billStatus", waybillQuery.getBillStatus());
		}
		if (null != waybillQuery.getServiceIds() && !waybillQuery.getServiceIds().isEmpty()) {
			map.put("serviceIds", waybillQuery.getServiceIds());
		}
		if (null != waybillQuery.getLocateStatus()) {
			map.put("locateStatus", waybillQuery.getLocateStatus());
		}
		if (StringUtils.isNotBlank(waybillQuery.getBillDate())) {
			map.put("billDateStart", waybillQuery.getBillDate().trim() + " 00:00:00");
			map.put("billDateEnd", waybillQuery.getBillDate().trim() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillQuery.getBillDateStart())) {
			map.put("billDateStart", waybillQuery.getBillDateStart());
		}
		if (StringUtils.isNotBlank(waybillQuery.getBillDateEnd())) {
			map.put("billDateEnd", waybillQuery.getBillDateEnd());
		}
		if (StringUtils.isNotBlank(waybillQuery.getPayTimeStart())) {
			map.put("payTimeStart", waybillQuery.getPayTimeStart());
		}
		if (StringUtils.isNotBlank(waybillQuery.getPayTimeEnd())) {
			map.put("payTimeEnd", waybillQuery.getPayTimeEnd());
		}
		if (StringUtils.isNotBlank(waybillQuery.getCreateTimeStart())) {
			map.put("createTimeStart", waybillQuery.getCreateTimeStart());
		}
		if (StringUtils.isNotBlank(waybillQuery.getCreateTimeEnd())) {
			map.put("createTimeEnd", waybillQuery.getCreateTimeEnd());
		}
		if (null != waybillQuery.getElectronicContractState()) {
			map.put("electronicContractState", waybillQuery.getElectronicContractState());
		}
		if (null != waybillQuery.getBuyerBillStatus()) {
			map.put("buyerBillStatus", waybillQuery.getBuyerBillStatus());
		}
		if (null != waybillQuery.getSellerBillStatus()) {
			map.put("sellerBillStatus", waybillQuery.getSellerBillStatus());
		}
		if (null != waybillQuery.getPushStatus()) {
			map.put("pushStatus", waybillQuery.getPushStatus());
		}

		if (null != waybillQuery.getReceiptStatus()) {
			map.put("receiptStatus", waybillQuery.getReceiptStatus());
		}
		map.put("offSet", waybillQuery.getOffSet());
		map.put("pageSize", waybillQuery.getPageSize());

		return map;
	}

	/**
	 * 金融服务系统签约主体托运人运单列表
	 *
	 * @param waybillQuery
	 * @return
	 */
	@Override
	public FinancingWaybillRes selectSignorWaybillSum(FinancingWaybillQueryReq waybillQuery) {
		Map<String, Object> map = new HashMap<>();
		map.put("customerId", waybillQuery.getCustomerId()); // 托运人ID
		map.put("businessModelId", waybillQuery.getBusinessModelId()); // 托运人ID
		// 项目分组ID
		if (StringUtils.isNotEmpty(waybillQuery.getContractIds())) {
			map.put("contractIds", waybillQuery.getContractIds());
		}
		if (StringUtils.isNotEmpty(waybillQuery.getContractId())) {
			map.put("contractId", waybillQuery.getContractId());
		}
		if (StringUtils.isNotBlank(waybillQuery.getResource())) {
			map.put("resource", waybillQuery.getResource());
		}
		if (StringUtils.isNotBlank(waybillQuery.getShippingNoteNumber())) {
			map.put("shippingNoteNumber", waybillQuery.getShippingNoteNumber());
		}
		if (StringUtils.isNotBlank(waybillQuery.getDriverInfo())) {
			if (ValidateUtils.checkMobilePhone(StringUtils.deleteWhitespace(waybillQuery.getDriverInfo()))) {
				map.put("driverPhone", StringUtils.deleteWhitespace(waybillQuery.getDriverInfo()));
			} else {
				map.put("driverName", waybillQuery.getDriverInfo().trim());
			}
		}
		if (StringUtils.isNumeric(waybillQuery.getDriverId())) {
			map.put("driverId", waybillQuery.getDriverId());
		}
		if (StringUtils.isNotBlank(waybillQuery.getVehicleNumber())) {
			map.put("vehicleNumber", waybillQuery.getVehicleNumber());
		}
		if (StringUtils.isNotBlank(waybillQuery.getReceiptInfo())) {
			if (ValidateUtils.checkPositiveNumber(StringUtils.deleteWhitespace(waybillQuery.getReceiptInfo()))) {
				map.put("receiptCard", StringUtils.deleteWhitespace(waybillQuery.getReceiptInfo()));
			} else {
				map.put("receiptName", waybillQuery.getReceiptInfo().trim());
			}
		}
		if (StringUtils.isNumeric(waybillQuery.getPayeeId())) {
			map.put("payeeId", waybillQuery.getPayeeId());
		}
		if (StringUtils.isNotBlank(waybillQuery.getFrameworkContractName())) {
			map.put("frameworkContractName", waybillQuery.getFrameworkContractName().trim());
		}
		if (null != waybillQuery.getSettleStatus()) {
			map.put("settleStatus", waybillQuery.getSettleStatus());
		}
		if (null != waybillQuery.getTransportStatus()) {
			map.put("transportStatus", waybillQuery.getTransportStatus());
		}
		if (null != waybillQuery.getBillStatus()) {
			map.put("billStatus", waybillQuery.getBillStatus());
		}
		if (null != waybillQuery.getServiceIds() && !waybillQuery.getServiceIds().isEmpty()) {
			map.put("serviceIds", waybillQuery.getServiceIds());
		}
		if (null != waybillQuery.getLocateStatus()) {
			map.put("locateStatus", waybillQuery.getLocateStatus());
		}
		if (StringUtils.isNotBlank(waybillQuery.getBillDate())) {
			map.put("billDateStart", waybillQuery.getBillDate().trim() + " 00:00:00");
			map.put("billDateEnd", waybillQuery.getBillDate().trim() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillQuery.getBillDateStart())) {
			map.put("billDateStart", waybillQuery.getBillDateStart());
		}
		if (StringUtils.isNotBlank(waybillQuery.getBillDateEnd())) {
			map.put("billDateEnd", waybillQuery.getBillDateEnd());
		}
		if (StringUtils.isNotBlank(waybillQuery.getPayTimeStart())) {
			map.put("payTimeStart", waybillQuery.getPayTimeStart());
		}
		if (StringUtils.isNotBlank(waybillQuery.getPayTimeEnd())) {
			map.put("payTimeEnd", waybillQuery.getPayTimeEnd());
		}
		if (StringUtils.isNotBlank(waybillQuery.getCreateTimeStart())) {
			map.put("createTimeStart", waybillQuery.getCreateTimeStart());
		}
		if (StringUtils.isNotBlank(waybillQuery.getCreateTimeEnd())) {
			map.put("createTimeEnd", waybillQuery.getCreateTimeEnd());
		}
		if (null != waybillQuery.getElectronicContractState()) {
			map.put("electronicContractState", waybillQuery.getElectronicContractState());
		}
		if (null != waybillQuery.getReceiptStatus()) {
			map.put("receiptStatus", waybillQuery.getReceiptStatus());
		}
		if (null != waybillQuery.getSellerBillStatus()) {
			map.put("sellerBillStatus", waybillQuery.getSellerBillStatus());
		}
		return waybillMapperEx.selectSignorWaybillSum(map);
	}

	/**
	 * 金融服务系统待对账运单列表
	 *
	 * @param map
	 * @return
	 */
	@Override
	public List<StatementWaybillInfo> selectPreStatementWaybillList(Map<String, Object> map) {
		List<StatementWaybillInfo> list = waybillMapperEx.selectPreStatementWaybillList(map);
		if (null == list) {
			return Collections.emptyList();
		}
		return list;
	}

	@Override
	public List<RiskControllerRes> riskControllerList(RiskControllerReq req) {
		req.setBusinessModelId(SecurityUtils.getBusinessModelId());
		// req.setBusinessModelId(1L);
		req.setCustomerId(SecurityUtils.getShipperId());
		if (StringUtils.isNotEmpty(req.getWaybillCodes())) {
			req.setWaybillCodeList(Arrays.asList(req.getWaybillCodes().split(",")));
		}
		if (StringUtils.isNotEmpty(req.getOverruleTimeStart())) {
			req.setOverruleTimeStart(req.getOverruleTimeStart() + " 00:00:00");
		}
		if (StringUtils.isNotEmpty(req.getOverruleTimeEnd())) {
			req.setOverruleTimeEnd(req.getOverruleTimeEnd() + " 23:59:59");
		}
		List<RiskControllerRes> riskControllerRes = new ArrayList<>();
		if (req.getType() == 1) {
			riskControllerRes = waybillMapperEx.riskWaitList(req);
		} else if (req.getType() == 2) {
			riskControllerRes = waybillMapperEx.riskRefuseList(req);
		} else if (req.getType() == 3) {
			riskControllerRes = waybillMapperEx.pushList(req);
		} else if (req.getType() == 4) {
			riskControllerRes = waybillMapperEx.pushSuccessList(req);
		} else if (req.getType() == 5) {
			riskControllerRes = waybillMapperEx.pushOverruleList(req);
		} else if (req.getType() == 6) {
			riskControllerRes = waybillMapperEx.shuDaiTongPushList(req);
		}
		List<Long> waybillIds = new ArrayList<>();
		for (RiskControllerRes riskControllerRe : riskControllerRes) {
			waybillIds.add(Long.parseLong(riskControllerRe.getId()));
		}
		// 回单附件列表
		List<WaybillAttachmentInfo> unloadFileList = waybillAttachmentInfoService.unloadFileList(waybillIds);
		// 已上传回单附件的运单ID
		List<Long> upIds = unloadFileList.stream().map(WaybillAttachmentInfo::getRelationId).collect(Collectors.toList());

		for (RiskControllerRes res : riskControllerRes) {
			res.setReceiptState(upIds.contains(Long.parseLong(res.getId())) ? 1 : 0);
		}
		return riskControllerRes;
	}

	/**
	 * 获取运单状态信息
	 *
	 * @param id
	 * 		运单ID
	 * @return
	 */
	@Override
	public CommonResult<WaybillEditableInfo> getWaybillStatusInfo(Long id) {
		Waybill waybill = this.selectWaybillById(id);
		if (null == waybill) {
			return CommonResult.requestError("运单不存在");
		}
		WaybillEditableInfo editableInfo = new WaybillEditableInfo();
		editableInfo.setId(waybill.getId());
		editableInfo.setResource(waybill.getResource());
		editableInfo.setShippingNoteNumber(waybill.getShippingNoteNumber());

		// status 运单状态(0订单 1运单 2已签约(未开始) 3运输中 4已完成 -1删除)
		// transport 运输状态(0:未开始 1:在途中 2:已完成)
		if (Arrays.asList(0, 1, 2).contains(waybill.getStatus())) {
			editableInfo.setTransportStatus(0);
		} else if (3 == waybill.getStatus()) {
			editableInfo.setTransportStatus(1);
		} else if (4 == waybill.getStatus()) {
			editableInfo.setTransportStatus(2);
		}

		CustomerTradeApply applyQuery = new CustomerTradeApply();
		applyQuery.setWaybillId(waybill.getId());
		List<CustomerTradeApply> applyList = customerTradeApplyMapper.selectCustomerTradeApplyList(applyQuery);
		// 申请列表无数据，则表示未支付
		if (applyList.isEmpty()) {
			editableInfo.setPayStatus(0);
		} else {
			// 申请列表存在非驳回的数据，则表示已进入付款流程，否则驳回数据表示未支付
			editableInfo.setPayStatus(applyList.stream().anyMatch(a -> 2 != a.getAuditStatus()) ? 1 : 0);
		}

		return CommonResult.success(editableInfo);
	}

	@Override
	public CommonResult getCompleteTrajectory(Long id) {

		Waybill waybill = waybillMapper.selectWaybillById(id);
		if (BusinessConstants.WAYBILL_STATUS_COMPLETED != waybill.getStatus()) {
			return CommonResult.requestError("运输还未完成，请在运输完成后再尝试补全轨迹");
		}
		Long customerId;
		Boolean isCheck = true;

		// 网货不校验权限
		if (SecurityUtils.getClientType().equals(ClientType.PLATFORM)) {
			isCheck = false;
			customerId = freightForwarderInfoService.selectFreightForwarderInfoById(SecurityUtils.getFreightForwarderId()).getCustomerId();
		} else {
			customerId = SecurityUtils.getShipperId();
		}

		// 托运人校验权限
		if (SecurityUtils.getClientType().equals(ClientType.SHIPPER)) {
			Integer count = customerServiceInfoService.selectGPSIsOpen(waybill.getCustomerId());
			if (count == 0) {
				return CommonResult.requestError("您未开启定位权限，该功能是收费项。确定要开通，请联系您的客户经理！");
			}
		}

		// 金融端校验权限
		if (SecurityUtils.getClientType().equals(ClientType.FINANCE)) {
			Integer count = customerServiceInfoService.selectGPSIsOpen(SecurityUtils.getShipperId());
			if (count == 0) {
				return CommonResult.requestError("您未开启定位权限，该功能是收费项。确定要开通，请联系集团运营！");
			}
		}

		Date startTime;
		Date endTime = waybill.getUnloadTime();
		if (waybill.getLoadTime() == null) {
			startTime = waybill.getDespatchActualDateTime();
		} else {
			startTime = waybill.getLoadTime();
		}

		Vehicle vehicle = vehicleService.selectVehicleById(waybill.getVehicleId());

		VehicleTrackReq vehicleTrackReq = new VehicleTrackReq();
		vehicleTrackReq.setWaybillId(id);
		vehicleTrackReq.setQryBtm(startTime);
		vehicleTrackReq.setQryEtm(endTime);
		vehicleTrackReq.setVehicleNumber(vehicle.getVehicleNumber());
		vehicleTrackReq.setResetFlag("1");
		vehicleTrackReq.setVehiclePlateColorCode(vehicle.getVehiclePlateColorCode());
		CommonResult<String> result = trackService.getCompleteTrajectory(vehicleTrackReq);
		if (result.getCode().equals("200")) {
			// 修改当前运单标记为已补充过轨迹,轨迹不修改es
			waybillCustomerRelationMapperEx.completionTrajectory(customerId, waybill.getId());
			Waybill updateWaybill = new Waybill();
			updateWaybill.setId(waybill.getId());
			updateWaybill.setLocateStatus(BusinessConstants.WAYBILL_LOCATE_STATUS_COMPLETED);

			waybill.setLocateStatus(BusinessConstants.WAYBILL_LOCATE_STATUS_COMPLETED);
			esWaybillService.updateEsWaybillInfo(waybill);
			// 修改为最新的计费方法 by zk
			sysCostService.addWaybillTrackCost(CostPageEnum.WAYBILL_INFO_PAGE, CodeEnum.GPS_HISTORY_LOCATION, customerId, waybill, startTime, endTime, isCheck);
			return CommonResult.success("轨迹已补全");
		} else {
			/*
					"<p style='font-weight:600'><span style='color:#f00'>" + "该时间段内无轨迹信息" + "</span></p >" + "<p>请对如下因素进行排查：</p >" + "<p>1.北斗设备故障</p >" + "<p>2.sm卡失效（由sim卡欠费导致）</p >"
							+ "<p>3.车辆长时间停车导致离线，请联系对应司机</p >")*/
			return CommonResult.result(AjaxResult.popError(""));
		}

	}

	@Override
	@Transactional
	public CommonResult getCompleteTrajectoryByTime(GpsTimeReq req) {
		if (req == null) {
			return CommonResult.error("参数为空");
		}
		if (req.getId() == null) {
			return CommonResult.error("id为空");
		}
		if (StringUtils.isEmpty(req.getStartTime())) {
			return CommonResult.error("开始时间为空");
		}
		if (StringUtils.isEmpty(req.getEndTime())) {
			return CommonResult.error("结束时间为空");
		}
		if (DateUtils.getXcDays(req.getStartTime(), req.getEndTime()) > 30) {
			return CommonResult.error("实际发车时间与实际到达时间，间隔不能超过30天");
		}

		Waybill waybill = waybillMapper.selectWaybillById(req.getId());
		if (BusinessConstants.WAYBILL_STATUS_COMPLETED != waybill.getStatus()) {
			return CommonResult.requestError("运输还未完成，请在运输完成后再尝试补全轨迹");
		}
		Long customerId;
		Boolean isCheck = true;

		// 网货不校验权限
		if (SecurityUtils.getClientType().equals(ClientType.PLATFORM)) {
			isCheck = false;
			customerId = freightForwarderInfoService.selectFreightForwarderInfoById(SecurityUtils.getFreightForwarderId()).getCustomerId();
		} else {
			customerId = SecurityUtils.getShipperId();
		}

		// 托运人校验权限
		if (SecurityUtils.getClientType().equals(ClientType.SHIPPER)) {
			Integer count = customerServiceInfoService.selectGPSIsOpen(waybill.getCustomerId());
			if (count == 0) {
				return CommonResult.requestError("您未开启定位权限，该功能是收费项。确定要开通，请联系您的客户经理！");
			}
		}

		// 金融端校验权限
		if (SecurityUtils.getClientType().equals(ClientType.FINANCE)) {
			Integer count = customerServiceInfoService.selectGPSIsOpen(SecurityUtils.getShipperId());
			if (count == 0) {
				return CommonResult.requestError("您未开启定位权限，该功能是收费项。确定要开通，请联系集团运营！");
			}
		}

		// 轨迹定位时间根据前端传值来确定
		Date startTime = DateUtils.strToDate(req.getStartTime());
		Date endTime = DateUtils.strToDate(req.getEndTime());

		VehicleTrackReq vehicleTrackReq = new VehicleTrackReq();
		vehicleTrackReq.setWaybillId(req.getId());
		vehicleTrackReq.setQryBtm(startTime);
		vehicleTrackReq.setQryEtm(endTime);
		vehicleTrackReq.setVehicleNumber(waybill.getVehicleNumber());
		vehicleTrackReq.setVehiclePlateColorCode(waybill.getVehiclePlateColorCode());
		vehicleTrackReq.setResetFlag("1");
		CommonResult<String> result = trackService.getCompleteTrajectory(vehicleTrackReq);
		// 无轨迹时，直接返回
		if (!result.getCode().equals("200")) {
			return CommonResult.result(AjaxResult.popError(
					"<p style='font-weight:600'><span style='color:#f00'>" + "该时间段内无轨迹信息" + "</span></p >" + "<p>请对如下因素进行排查：</p >" + "<p>1.北斗设备故障</p >" + "<p>2.sm卡失效（由sim卡欠费导致）</p >" + "<p>3.车辆长时间停车导致离线，请联系对应司机</p >"));
		}
		// 修改当前运单标记为已补充过轨迹
		WaybillCustomerRelation relation = waybillCustomerRelationMapper.selectByWaybillIdsAndCustomerId(Arrays.asList(waybill.getId()), customerId).get(0);
		relation.setIsCompleteTrajectory(1);
		waybillCustomerRelationMapper.updateWaybillCustomerRelation(relation);
		Waybill updateWaybill = new Waybill();
		updateWaybill.setId(waybill.getId());
		updateWaybill.setLocateStatus(BusinessConstants.WAYBILL_LOCATE_STATUS_COMPLETED);
		waybill.setLocateStatus(BusinessConstants.WAYBILL_LOCATE_STATUS_COMPLETED);

		// 修改为最新的计费方法 by zk
		sysCostService.addWaybillTrackCost(CostPageEnum.WAYBILL_INFO_PAGE, CodeEnum.GPS_HISTORY_LOCATION, customerId, waybill, startTime, endTime, isCheck);

		// 时间不一致且轨迹有信息的时候，用当前时间替换原有的运单实际时间
		if (waybill.getLoadTime() == null || waybill.getUnloadTime() == null || (waybill.getLoadTime().compareTo(startTime) != 0 || waybill.getUnloadTime().compareTo(endTime) != 0)) {
			updateWaybill.setUnloadTime(endTime);
			updateWaybill.setLoadTime(startTime);
			updateWaybill.setUpdateBy(SecurityUtils.getNickname());
			// 补全轨迹后，同时使用实际时间来变更预计收发货时间
			updateWaybill.setDespatchActualDateTime(startTime);
			updateWaybill.setGoodsReceiptDateTime(endTime);

			String actionName = "补全轨迹修改收发货时间(运单ID：" + waybill.getId() + "，运单号：" + waybill.getShippingNoteNumber() + ")";
			actionName += "，请求参数：" + JSONUtil.toJsonStr(req);
			sysClientLogService.insertLog(2, 4, actionName, waybill.getShippingNoteNumber(), Collections.singletonList(waybill.getId()));
		}
		// 更新数据库、es运单信息
		waybillMapper.updateWaybill(updateWaybill);
		esWaybillService.updateEsWaybillInfo(waybill);

		// 记录操作日志
		String actionName = "补全轨迹(运单ID：" + waybill.getId() + "，运单号：" + waybill.getShippingNoteNumber() + ")";
		actionName += "，请求参数：" + JSON.toJSONString(req);
		sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_WAYBILL, BusinessConstants.ACTION_TYPE_UPDATE, actionName, waybill.getShippingNoteNumber(),
				Collections.singletonList(waybill.getId()));

		return CommonResult.success("轨迹已补全");

	}

	@Override
	public CommonResult getCompleteTrajectoryTime(Long id) {
		Waybill waybill = waybillMapper.selectWaybillById(id);
		Date startTime = waybill.getDespatchActualDateTime();
		if (waybill.getLoadTime() != null && waybill.getLoadTime().compareTo(DateUtils.default1970()) > 0) {
			startTime = waybill.getLoadTime();
		}
		Date endTime = waybill.getUnloadTime();

		GpsTimeRes res = new GpsTimeRes();
		res.setEndTime(DateUtils.dateToStr(endTime));
		res.setStartTime(DateUtils.dateToStr(startTime));

		return CommonResult.success(res);
	}

	@Override
	public CommonResult<WaybillCopyRes> copyWaybill(Long id) {
		// 0、运单基础信息查询
		Waybill waybill = waybillMapper.selectWaybillById(id);
		if (waybill == null) {
			return CommonResult.error("当前运单不存在");
		}
		if (waybill.getResource() == 6) {
			return CommonResult.error("运单来源于小程序，不能复制运单");
		}
		// 1、项目信息是否含有保险，且保险已投保校验
		FrameworkContract contract = frameworkContractService.selectFrameworkContractById(waybill.getFrameworkContractId());
		if (contract.getInsuranceState() == 1) {// 保险状态：1 审核中 2 审核完成 3 无需审核
			return CommonResult.error("该运单所属的项目正在保险审核中，暂时无法复制");
		}
		// 2、运力信息查询并校验
		Vehicle vehicle = waybillCommonService.searchVehicleInfoByWaybill(waybill);
		Driver driver = waybillCommonService.searchDriverInfoByWaybill(waybill);
		ActualCarrierInfo carrier = waybillCommonService.searchCarrierInfoByWaybill(waybill);
		PayeeInfo payeeInfo = waybillCommonService.searchPayeeInfoByWaybill(waybill);
		StringBuilder errorMessageBuilder = new StringBuilder();
		if (null == vehicle) {
			errorMessageBuilder.append("车辆【").append(waybill.getVehicleNumber()).append("】已被删除，请先前往运力管理中添加后，再复制运单").append("\n");
		}
		if (null == driver) {
			errorMessageBuilder.append("司机【").append(waybill.getDriverName()).append("】已被删除，请先前往运力管理中添加后，再复制运单").append("\n");
		}
		if (null == carrier) {
			errorMessageBuilder.append("承运人【").append(waybill.getActualCarrierName()).append("】已被删除，请先前往运力管理中添加后，再复制运单").append("\n");
		}
		if (errorMessageBuilder.length() > 0) {
			return CommonResult.error(errorMessageBuilder.toString().replaceFirst("\\n$", ""));
		}
		if (vehicle != null && vehicle.getInfoIsComplete() == 1) {
			return CommonResult.error("车辆运力信息不完整");
		}
		if (driver != null && driver.getInfoIsComplete() == 1) {
			return CommonResult.error("司机运力信息不完整");
		}

		// 3、货源信息处理，如为导入的货源，需根据货物表填充信息
		WaybillGoods waybillGoods = waybillGoodsService.selectGoodsByWaybillId(waybill.getId());
		MakeCode makeCode = makeCodeMapper.selectMakeCodeById(waybill.getMakeCodeId());
		if (makeCode.getUseScope() == 2) { // 适用范围：2 导入创建，仅导入使
			if (waybillGoods != null) {
				SysDictData sysDictData = sysDictDataService.selectDictCode(DictType.CARGO_TYPE, waybillGoods.getCargoTypeClassificationCode());
				if (null != sysDictData) {
					makeCode.setCargoCode(sysDictData.getDictCode());
					makeCode.setCargoLabel(sysDictData.getDictLabel());
					makeCode.setCargoValue(sysDictData.getDictValue());
				}
				makeCode.setCargoName(waybillGoods.getDescriptionOfGoods());
				makeCode.setPackagingValue(waybillGoods.getPackagingCode());
				makeCode.setPackagingLabel(waybillGoods.getPackagingName());
				SysDictData packageInfo = sysDictDataService.selectDictCode(DictType.PACKING_TYPE, waybillGoods.getPackagingCode());
				makeCode.setPackagingCode(packageInfo.getDictCode());
			}
		}
		if ((waybill.getId() + "货源").equals(makeCode.getNameAlias())) {
			makeCode.setNameAlias("");
		}
		ConsigneeInfo consigneeInfo = consigneeInfoService.selectConsigneeInfoById(waybill.getConsigneeId());
		ConsignorInfo consignorInfo = consignorInfoService.selectConsignorInfoById(waybill.getConsignorId());
		// 如果收发货地址省编码为空 todo 是否已经可以删除
		if (StringUtils.isEmpty(consigneeInfo.getProvinceCode())) {
			ChinaProvincesCitiesAreas chinaProvincesCitiesAreas = new ChinaProvincesCitiesAreas();
			if (consigneeInfo.getProvince().contains("新疆")) {
				chinaProvincesCitiesAreas.setAreaName("新疆");
			} else {
				chinaProvincesCitiesAreas.setAreaName(consigneeInfo.getProvince());
			}
			chinaProvincesCitiesAreas.setAreaType(1);
			List<ChinaProvincesCitiesAreas> provincesCitiesAreas = chinaProvincesCitiesAreasMapper.selectChinaProvincesCitiesAreasList(chinaProvincesCitiesAreas);
			if (StringUtils.isNotEmpty(provincesCitiesAreas)) {
				consigneeInfo.setProvinceCode(provincesCitiesAreas.get(0).getAreaCode());
			}
		}
		// 4、组装数据，先从运单拷贝
		WaybillCopyRes waybillCopyRes = new WaybillCopyRes();
		BeanUtils.copyProperties(waybill, waybillCopyRes);
		// 4.2、不一致字段处理
		waybillCopyRes.setOriginalDocumentNumber(CodeUtil.generateOriginalDocumentNumber());
		waybillCopyRes.setContractId(waybill.getFrameworkContractId());
		waybillCopyRes.setContractName(waybill.getFrameworkContractName());
		waybillCopyRes.setWaybillRemark(waybill.getRemark());
		waybillCopyRes.setPolicyType(waybillSlaveService.selectWaybillSlaveById(waybill.getId()).getPolicyType());// 保险类型
		// 4.3、冗余信息填充
		waybillCopyRes.setMakeCode(makeCode);
		waybillCopyRes.getWaybillGoodsList().add(waybillGoods);
		waybillCopyRes.setConsigneeInfo(consigneeInfo);
		waybillCopyRes.setConsignorInfo(consignorInfo);
		waybillCopyRes.setDriver(driver);
		waybillCopyRes.setVehicle(vehicle);
		waybillCopyRes.setCarrier(carrier);
		waybillCopyRes.setPayee(payeeInfo);
		return CommonResult.success(waybillCopyRes);
	}

	@Override
	public List<Integer> queryEditingWaybill(Long id) {
		/**
		 * 运单已开票状态，不可点击【变更运单】 0 来源是 pc/导入/接口对接的，允许编辑 1 编辑运力信息，来源是小程序的三方货源 2 已过时 编辑运力信息，来源是小程序的大宗货源 3 已过时 2 ：表示小程序货源 3 就无意义不返回 未结算且正在付款审核流程的运单，仅货主货款的变更按钮可点击 4 未结算且付款审完成核流程的运单，仅货主货款的变更按钮可点击 5
		 * 未结算且正在付款审核流程-【已审核】的运单 部分结算/已结算的运单，仅货主货款的变更按钮可点击，其他项的【变更】置灰 6 31未开始 32 运输中 33 运输完成 当运单在付款结算中 是付款失败状态时 7
		 */
		List<Integer> list = new ArrayList<>();
		Waybill waybill = waybillService.selectWaybillById(id);
		if (waybill == null) {
			throw new ServiceException("未查询到当前运单");
		}
		if (3 == waybill.getArtificialAuditState()) {
			throw new ServiceException("运单已人工审核通过，不可修改");
		}
		if (1 == waybill.getBillStatus() || 2 == waybill.getBillStatus() || 3 == waybill.getBillStatus()) {
			list.add(0);
			return list;
		}
		// 查询申请付款记录表
		CustomerTradeApply customerTradeApply = new CustomerTradeApply();
		customerTradeApply.setWaybillId(id);
		List<CustomerTradeApply> customerTradeApplies = customerTradeApplyMapper.selectCustomerTradeApplyList(customerTradeApply);
		// 判断该运单是否在待付款待审核中
		if (customerTradeApplies.stream().anyMatch(f -> 0 == f.getAuditStatus())) {
			list.add(4);
		}
		// 判断改运单是否已完成付款审核
		if (customerTradeApplies.stream().anyMatch(f -> 1 == f.getAuditStatus() || 5 == f.getAuditStatus())) {
			list.add(5);
		}
		// 当运单在付款结算中 是付款失败状态时
		if (customerTradeApplies.stream().anyMatch(f -> 4 == f.getAuditStatus())) {
			list.add(7);
		}

		// 判断当前运单是否是部分结算或已结算
		if (Arrays.asList(1, 2).contains(waybill.getSettleStatus())) {
			list.add(6);
			return list;
		}

		MakeCode makeCode = makeCodeMapper.selectMakeCodeById(waybill.getMakeCodeId());
		if (1 == makeCode.getSupplyType()) {
			if (3 == waybill.getStatus()) {
				list.add(32);
			} else if (4 == waybill.getStatus()) {
				list.add(33);
			} else {
				list.add(31);
			}
		}
		// 判断来源是小程序得运单
		if (6 == waybill.getResource()) {
			list.add(2);
		} else if (Arrays.asList(1, 7).contains(waybill.getResource())) {
			list.add(1);
		}

		return list;
	}

	@Override
	public CommonResult batchUpdateDriverInfoIsComplete(Long driverId, Integer infoIsComplete) {
		// 车辆信息完整,修改运单司机信息完整
		Waybill waybill = new Waybill();
		waybill.setDriverId(driverId);
		waybill.setInfoIsComplete(1);
		List<Waybill> waybills = this.selectWaybillList(waybill);
		if (StringUtils.isNotEmpty(waybills)) {
			waybills.forEach(l -> {
				l.setDriverInfoIsComplete(infoIsComplete);
				// 修改es中运单
			});
			esWaybillService.updateEsWaybillInfos(waybills);
			if (infoIsComplete == 0)
				this.batchUpdateWaybillInfoIsComplete(waybills, infoIsComplete);
		}
		waybillMapperEx.batchUpdateDriverInfoIsComplete(driverId, infoIsComplete);
		return CommonResult.success();
	}

	@Override
	public CommonResult batchUpdateVehicleInfoIsComplete(Long vehicleId, Integer infoIsComplete) {
		// 车辆信息完整,车辆信息完整
		Waybill waybill = new Waybill();
		waybill.setVehicleId(vehicleId);
		waybill.setInfoIsComplete(1);
		List<Waybill> waybills = this.selectWaybillList(waybill);
		if (StringUtils.isNotEmpty(waybills)) {
			waybills.forEach(l -> {
				l.setVehicleInfoIsComplete(infoIsComplete);
			});
			// 修改es中运单
			esWaybillService.updateEsWaybillInfos(waybills);
			if (infoIsComplete == 0)
				this.batchUpdateWaybillInfoIsComplete(waybills, infoIsComplete);
		}
		waybillMapperEx.batchUpdateVehicleInfoIsComplete(vehicleId, infoIsComplete);
		return CommonResult.success();
	}

	@Override
	public CommonResult batchUpdateActualCarrierInfoIsComplete(String identityCard, Integer infoIsComplete) {
		// 承运人信息完整
		ActualCarrierInfo info = new ActualCarrierInfo();
		info.setIdentityCard(identityCard);
		List<ActualCarrierInfo> list = actualCarrierInfoMapper.selectActualCarrierInfoList(info);
		List<Long> actualCarrierIds = list.stream().map(ActualCarrierInfo::getId).collect(Collectors.toList());
		if (actualCarrierIds.size() > 0) {
			Waybill waybill = new Waybill();
			waybill.setActualCarrierIds(actualCarrierIds);
			waybill.setInfoIsComplete(1);
			List<Waybill> waybills = this.selectWaybillList(waybill);
			if (StringUtils.isNotEmpty(waybills)) {
				waybills.forEach(l -> {
					l.setActualCarrierInfoIsComplete(0);
				});
				// 修改es中运单
				esWaybillService.updateEsWaybillInfos(waybills);
			}
			waybillMapperEx.batchUpdateActualCarrierInfoIsComplete(actualCarrierIds, 0);
		}
		return CommonResult.success();
	}

	private void batchUpdateWaybillInfoIsComplete(List<Waybill> waybills, Integer infoIsComplete) {
		// 为1表示司机运力信息不齐全，运单信息也不齐全 为0说明司机运力信息齐全 判断其他运力是否齐全
		List<Long> waybillIds = waybills.stream().map(Waybill::getId).collect(Collectors.toList());
		if (infoIsComplete == 1) {
			if (StringUtils.isNotEmpty(waybillIds)) {
				waybillMapperEx.batchUpdateWaybillInfoIsComplete(waybillIds, infoIsComplete);
				List<Waybill> list = waybillMapper.selectWaybillByIds(waybillIds);
				list.forEach(l -> {
					l.setInfoIsComplete(infoIsComplete);
				});
				// 修改es中运单
				esWaybillService.updateEsWaybillInfos(list);
			}
		} else if (infoIsComplete == 0) {
			List<Long> isModifyIds = waybills.stream().filter(f -> f.getInfoIsComplete() == 1 && f.getDriverInfoIsComplete() == 0 && f.getVehicleInfoIsComplete() == 0).map(Waybill::getId)
					.collect(Collectors.toList());
			if (StringUtils.isNotEmpty(isModifyIds)) {
				List<Waybill> list = waybillMapper.selectWaybillByIds(isModifyIds);
				list.forEach(l -> {
					l.setInfoIsComplete(infoIsComplete);
				});
				// 修改es中运单
				esWaybillService.updateEsWaybillInfos(list);
				waybillMapperEx.batchUpdateWaybillInfoIsComplete(isModifyIds, infoIsComplete);
			}
		}
	}

	public List<Integer> selectFeeUnit(List<Long> waybillIds) {
		return waybillMapperEx.selectFeeUnit(waybillIds);
	}

	public List<Long> selectWaybillByContractId(Long contractId) {
		return waybillMapperEx.selectWaybillByContractId(contractId);
	}

	public int updateWaybillByContractId(Long contractId) {
		return waybillMapperEx.updateWaybillByContractId(contractId);
	}

	/**
	 * ETC开票开始
	 */
	public CommonResult etcWaybillStart(WaybillReq req) {
		etcInterfaceService.etcWaybillAsyncStart(req);
		return CommonResult.success();
	}

	/**
	 * ETC开票结束
	 */
	public CommonResult etcWaybillEnd(Waybill waybill) {
		FrameworkContract frameworkContract = frameworkContractService.selectFrameworkContractById(waybill.getFrameworkContractId());
		List<FrameworkContractSubchain> frameworkContractSubchains = frameworkContractSubchainMapper.selectFrameworkContractSubchainByContractId(frameworkContract.getId());
		FrameworkContractSubchain frameworkContractSubchain = frameworkContractSubchains.stream().filter(f -> f.getIsLeaf() == 0).findFirst().orElse(null);
		if (null == frameworkContractSubchain) {
			return CommonResult.error("未查询到转包链关系");
		}
		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(frameworkContractSubchain.getFreightForwarderId());
		if (-1 == freightForwarderInfo.getIsUpetc()) {
			WaybillSlave waybillSlave = new WaybillSlave();
			waybillSlave.setId(waybill.getId());
			waybillSlave.setUpetcStatus(4);
			waybillSlaveService.updateWaybillSlave(waybillSlave);
			log.info("---------------------->>>当前网络货运人未开通ETC开票上传功能");
			return CommonResult.error("当前网络货运人未开通ETC开票上传功能");
		}
		// 调用ETC接口
		// WaybillEnd waybillEnd = new WaybillEnd();
		// waybillEnd.setTenantryId(String.valueOf(billInfo.getTenantryId()));
		// waybillEnd.setWaybillCode(billInfo.getWaybillCode());
		// if (DateUtil.defaultTime().equals(billInfo.getActualEndTime())) { //
		// 如果ETC结束时间是“1970-01-01 00:00:00”，要处理一下
		// // ETC结束时间取当前5分钟之前的时间，否则行云时间校验可能会报“结束时间不能大于当前时间”
		// // long etcEndTime = System.currentTimeMillis() - 5 * 60 * 1000;
		// Date etcEndTime = DateUtil.getOffsetTime(new Date(), -5,
		// DateUtil.TimeType.diffMins);
		// waybillEnd.setActualEndTime(DateUtil.dateToStr(etcEndTime));
		// } else {
		// waybillEnd.setActualEndTime(DateUtil.dateToStr(billInfo.getActualEndTime()));
		// }
		// waybillEnd.setUnloadAddress(billInfo.getUnloadAddress());
		etcInterfaceService.waybillEnd(waybill);

		return CommonResult.success();
	}

	@Override
	public Waybill getWaybillInfoByCode(String shippingNoteNumber) {
		List<Waybill> waybillList = waybillMapperEx.selectWaybillList(Collections.singletonList(shippingNoteNumber));
		if (null == waybillList || waybillList.isEmpty()) {
			return null;
		}
		return waybillList.get(0);
	}

	@Override
	public List<ProjectOptionRes> selectProjectOptionList(ProjectOptionReq req) {
		/*1项目管理
		       2运单管理
		       3申请付款
		       4付款审核
		       5确认付款
		       6付款结算
		       7资金分配概览
		       8资金上拨记录
		       9资金下划记录
		       10账户流水
		       11项目流水
		       12申请开票
		       13 申请开票待审批
		       14 申请开票已审批
		       15 开票审批待审批
		       16 开票审批已审批
		       17 发票登记
		       18 员工选择项目
		       */
		req.setResource(SecurityUtils.getClientType().val());
		List<ProjectOptionRes> list = new ArrayList<ProjectOptionRes>();
		if (req.getType() == 8 || req.getType() == 9) {
			list = waybillMapperEx.selectHasAccountContractByNameList(req);// 资金上波下滑记录
		} else if (req.getType() == 15 || req.getType() == 14 || req.getType() == 16 || req.getType() == 17) {
			if (13 == req.getType()) {
				req.setStateList(Arrays.asList(2, 7, 9));
			} else if (14 == req.getType()) {
				// 只要显示审批通过的，不显示驳回的
				// 23.9.20显示通过和驳回的数据
				req.setStateList(Arrays.asList(-1, 0, 3, 6, 8, 10, 11));
				// map.put("state", 3);
			} else if (15 == req.getType()) {
				req.setCustomerId(null);
				req.setAuditCustomerId(SecurityUtils.getLoginUser().getTenantUser().getTenantId());
				req.setState(2);
			} else if (16 == req.getType()) {
				req.setCustomerId(null);
				req.setAuditCustomerId(SecurityUtils.getLoginUser().getTenantUser().getTenantId());
				// 只要显示审批通过的，不显示驳回的
				// 23.9.20显示通过和驳回的数据
				req.setStateList(Arrays.asList(0, 3));
				// map.put("state", 3);
			} else if (17 == req.getType()) {
				req.setCustomerId(null);
				req.setAuditCustomerId(SecurityUtils.getLoginUser().getTenantUser().getTenantId());
				req.setState(3);
			}
			list = waybillMapperEx.selectStatementByNameList(req);
		} else if (12 == req.getType()) {
			// 传入的托运人id作为销售方，登录的用户作为购买方
			list = contractSubchainMapperEx.selectAdjacentProjectsBycustomerId(req.getTransmitCustomerId(), SecurityUtils.getShipperId(), null, req.getContractIds(), null, 0);
		} else {
			list = waybillMapperEx.selectAuthContractByNameList(req);
		}

		return list;
	}

	/**
	 * 对外服务查询运单列表
	 *
	 * @param waybillReq
	 * @return
	 */
	@Override
	public TableInfo<WaybillRes> selectAppWaybillList(WaybillReq waybillReq) {
		Map<String, Object> map = new HashMap<>();
		if (StringUtils.isNotBlank(waybillReq.getShippingNoteNumber())) {
			map.put("shippingNoteNumberList", Arrays.asList(waybillReq.getShippingNoteNumber().split(",")));
		}
		if (StringUtils.isNotBlank(waybillReq.getOriginalDocumentNumber())) {
			map.put("originalDocumentNumber", waybillReq.getOriginalDocumentNumber());
		}
		if (StringUtils.isNotBlank(waybillReq.getVehicleNumber())) {
			map.put("vehicleNumber", waybillReq.getVehicleNumber());
		}
		if (StringUtils.isNotBlank(waybillReq.getBeginCreateTime())) {
			map.put("beginCreateTime", waybillReq.getBeginCreateTime());
		}
		if (StringUtils.isNotBlank(waybillReq.getEndCreateTime())) {
			map.put("endCreateTime", waybillReq.getEndCreateTime());
		}
		if (StringUtils.isNotBlank(waybillReq.getIdentityCard())) {
			map.put("identityCard", waybillReq.getIdentityCard());
		}
		waybillReq.setPageNum(null == waybillReq.getPageNum() || waybillReq.getPageNum() < 1 ? 1 : waybillReq.getPageNum());
		waybillReq.setPageSize(null == waybillReq.getPageSize() || waybillReq.getPageSize() < 1 ? 10 : waybillReq.getPageSize());
		map.put("offSet", (waybillReq.getPageNum() - 1) * waybillReq.getPageSize());
		map.put("pageSize", waybillReq.getPageSize());
		List<WaybillRes> list = waybillMapperEx.selectAppWaybillList(map);
		for (WaybillRes waybillRes : list) {
			// 获取货物信息
			List<WaybillGoods> goodsList = waybillGoodsService.waybillGoodsList(waybillRes.getId());
			waybillRes.setWaybillGoodsList(goodsList.stream().map(GoodsInfo::of).collect(Collectors.toList()));
			// 获取运费支付记录
			List<WaybillApplyRes> tradeFlowList = customerTradeApplyMapperEx.queryCustomerFlowByWaybillId(waybillRes.getId());
			waybillRes.setTradeFlowList(tradeFlowList);
			// 获取运单发票号码
			FinanceStatementWaybill financeStatementWaybill = new FinanceStatementWaybill();
			financeStatementWaybill.setWaybillId(waybillRes.getId());
			List<FinanceStatementWaybill> fswList = financeStatementWaybillService.selectFinanceStatementWaybillList(financeStatementWaybill);
			if (null != fswList && !fswList.isEmpty()) {
				financeStatementWaybill = fswList.get(0);
				List<FinanceStatementBill> fsbList = financeStatementBillService.selectFinanceStatementBillList(financeStatementWaybill.getStatementId());
				if (null != fsbList && !fsbList.isEmpty()) {
					List<String> invoiceNumberList = fsbList.stream().map(FinanceStatementBill::getInvoiceNumber).collect(Collectors.toList());
					invoiceNumberList = invoiceNumberList.stream().distinct().collect(Collectors.toList());
					waybillRes.setInvoiceNumber(String.join(",", invoiceNumberList));
				}
			}
		}
		Long count = waybillMapperEx.selectAppWaybillCount(map);
		return new TableInfo<WaybillRes>(list, count);
	}

	@Override
	public List<WaybillRes> getWaybillPayeeInfo(WaybillReq waybillReq) {
		return waybillMapperEx.getWaybillPayInfo(waybillReq);
	}

	@Override
	public BigDecimal statisticsUnpaidFareByContractId(Long contractId, Long customerId) {
		return waybillMapperEx.statisticsUnpaidFareByContractId(contractId, customerId);
	}

	public void mergeShippers(Long fromId, Long toId, String toName) {
		waybillMapperEx.mergeShippers(fromId, toId, toName);
	}

	@Override
	public void handleData(Long customerId, Integer page) {
		long l1 = System.currentTimeMillis();
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("size", 1000);
		if (null != customerId) {
			map.put("customerId", customerId);
		}
		List<WaybillCustomerRelation> list;
		LocalDate minDate = LocalDate.of(2024, 7, 1);
		// LocalDate maxDate = LocalDate.of(2024, 3, 20);
		LocalDate maxDate = LocalDate.now().plusDays(1);
		long daysBetween = ChronoUnit.DAYS.between(minDate, maxDate); // 计算相差天数
		LocalDate queryStart = null;// 查询开始日期
		LocalDate queryEnd = null; // 查询截止日期
		for (int i = 0; i < daysBetween; i++) {
			page = 0;
			queryStart = null == queryStart ? maxDate.minusDays(1) : queryStart.minusDays(1);
			queryEnd = null == queryEnd ? maxDate : queryEnd.minusDays(1);
			if (queryStart.isBefore(minDate)) {
				log.info("处理应收应付数据已完成，终止的日期：{} -- {}", queryStart, minDate);
				return;
			}
			log.info("处理应收应付数据开始，查询的日期：{} -- {}", queryStart, queryEnd);
			do {
				map.put("page", page * 1000);
				map.put("start", queryStart);
				map.put("end", queryEnd);
				list = waybillCustomerRelationMapperEx.queryList(map);
				if (list == null || list.isEmpty()) {
					log.info("处理应收应付数据已完成，查询的日期：{} -- {}，已经处理完成！", queryStart, queryEnd);
					break;
				}
				for (WaybillCustomerRelation res : list) {
					res.setPayableDownstream(res.getPayFare().divide(BigDecimal.ONE.subtract(res.getRateToMe()), 2, RoundingMode.HALF_UP));
					if (res.getIsRecorder() == 0) {
						res.setReceiveUpstream(res.getTotalFare().divide(BigDecimal.ONE.subtract(res.getRateByMe()), 2, RoundingMode.HALF_UP));
					} else {
						res.setReceiveUpstream(res.getPayFare().divide(BigDecimal.ONE.subtract(res.getRateByMe()), 2, RoundingMode.HALF_UP));
					}
				}
				log.info("处理应收应付数据到更新数据到一步，查询的日期：{} -- {}，第 {} 批处理完成！", queryStart, queryEnd, i);
				waybillCustomerRelationMapperEx.updateRelation(list);
				log.info("处理应收应付数据已完成，查询的日期：{} -- {}，第 {} 批处理完成！", queryStart, queryEnd, i);
				page++;
			} while (!list.isEmpty());
		}
		long l2 = System.currentTimeMillis();
		log.info("处理应收应付数据全部已完成，总耗时：--------------------------------------" + (l2 - l1) + "----------------------------------------");
	}

	@Override
	public Integer queryUnPaidWaybillByChooseId(Map<String, Long> map) {
		return waybillMapperEx.queryUnPaidWaybillByChooseId(map);
	}

	@Override
	public Integer updateWaybillContractByContractId(Long contractId, String contractName, String contractCode) {
		return waybillMapperEx.updateWaybillContractByContractId(contractId, contractName, contractCode);
	}

	public AjaxResult showDriverBigSignTemplate(SignDriverReq driverReq) {
		ElectronicContractRes res = new ElectronicContractRes();
		Long freightForwarderId = driverReq.getFreightForwarderId();

		String wordOutPath = PDFConfig.asposeWordOutPath + System.currentTimeMillis() + ".docx";

		FreightForwarderAttachmentInfo freightForwarderAttachmentInfo = new FreightForwarderAttachmentInfo();
		freightForwarderAttachmentInfo.setRelationId(freightForwarderId);
		freightForwarderAttachmentInfo.setFileName("合同章");
		freightForwarderAttachmentInfo.setState(0);
		List<FreightForwarderAttachmentInfo> freightForwarderAttachmentInfos = freightForwarderAttachmentInfoService.selectFreightForwarderAttachmentInfoList(freightForwarderAttachmentInfo);
		if (freightForwarderAttachmentInfos == null || freightForwarderAttachmentInfos.size() == 0) {
			return AjaxResult.error("未查询到合同章");
		} else {
			res.setFileUrl(freightForwarderAttachmentInfos.get(0).getFileUrl());
		}

		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(freightForwarderId);
		Map<String, Object> params = new HashMap<String, Object>();
		// 网络货运人
		params.put("freightForwarderInfoName", freightForwarderInfo.getName());
		try {
			WordUtil.templateWriteWithImg(PDFConfig.asposeWordBigTemplatePath, params, wordOutPath);
		} catch (Exception e) {
			e.printStackTrace();
		}

		res.setUrl(wordOutPath);
		return AjaxResult.success(res);
	}

	@Override
	public void verifyTrajectory() {
		// 查询需要校验的商户
		SysParameter sysParameter = sysParameterService.selectSysParameterById(3L);
		if (sysParameter != null) {
			String paraValue = sysParameter.getParaValue();
			List<String> list = Arrays.asList(paraValue.split(","));
			if (list.size() > 0) {
				List<WaybillRes> waybills = waybillMapperEx.selectUnCheckWaybillIds(list);
				List<Long> ids = new ArrayList<>();
				for (WaybillRes waybill : waybills) {
					ids.add(waybill.getId());
					if (!waybill.getLoadingAddress().contains(waybill.getLoadingProvince())) {
						waybill.setLoadingAddress(waybill.getLoadingProvince() + waybill.getLoadingCity() + waybill.getLoadingArea() + waybill.getLoadingAddress());
					}
					if (!waybill.getUnloadAddress().contains(waybill.getUnloadProvince())) {
						waybill.setUnloadAddress(waybill.getUnloadProvince() + waybill.getUnloadCity() + waybill.getUnloadArea() + waybill.getUnloadAddress());
					}
				}
				// 先把查询到的运单修改为处理中
				if (ids.size() > 0) {
					waybillMapperEx.updateVerifyTrajectoryState(ids, 3);
					String content = JSON.toJSONString(waybills);
					rabbitTemplate.convertAndSend(MQConstants.EXCHANGE_NAME, verifyTrajectoryMqKey, content);
				} else {
					log.info("本次定时任务没有需要执行轨迹校验的运单");
				}
			}
		}
	}

	@Override
	public AjaxResult selectFreightForwarderWaybillSum(WaybillReq waybillReq) {
		if (StringUtils.isNotBlank(waybillReq.getBeginOrderCreateTime())) {
			waybillReq.setBeginOrderCreateTime(waybillReq.getBeginOrderCreateTime() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(waybillReq.getEndOrderCreateTime())) {
			waybillReq.setEndOrderCreateTime(waybillReq.getEndOrderCreateTime() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillReq.getBeginCreateTime())) {
			waybillReq.setBeginCreateTime(waybillReq.getBeginCreateTime() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(waybillReq.getEndCreateTime())) {
			waybillReq.setEndCreateTime(waybillReq.getEndCreateTime() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillReq.getBeginPayTime())) {
			waybillReq.setBeginPayTime(waybillReq.getBeginPayTime() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(waybillReq.getEndPayTime())) {
			waybillReq.setEndPayTime(waybillReq.getEndPayTime() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillReq.getInvoiceDateStart())) {
			waybillReq.setInvoiceDateStart(waybillReq.getInvoiceDateStart() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(waybillReq.getInvoiceDateEnd())) {
			waybillReq.setInvoiceDateEnd(waybillReq.getInvoiceDateEnd() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillReq.getDriverInfo())) {
			if (ValidateUtils.checkIdentityNo15(StringUtils.deleteWhitespace(waybillReq.getDriverInfo())) || ValidateUtils.checkIdentityNo(StringUtils.deleteWhitespace(waybillReq.getDriverInfo()))) {
				waybillReq.setDrivingLicense(waybillReq.getDriverInfo().trim());
			} else {
				waybillReq.setDriverName(waybillReq.getDriverInfo().trim());
			}
		}
		if (StringUtils.isNotBlank(waybillReq.getIdentityCard())) {
			PayeeInfo payeeInfo = new PayeeInfo();
			payeeInfo.setIdentityCard(waybillReq.getIdentityCard());
			List<PayeeInfo> payeeInfos = payeeInfoMapper.selectPayeeInfoList(payeeInfo);
			if (CommonUtil.isNotNullOrEmpty(payeeInfos)) {
				waybillReq.setPayeeIds(payeeInfos.stream().map(p -> p.getId()).collect(Collectors.toList()));
			} else {
				// 如果收款人没有查到，说明没有运单
				return AjaxResult.success();
			}
		}
		if (waybillReq.getCustomerId() != null) {
			FrameworkContractSubchain subchainReq = new FrameworkContractSubchain();
			subchainReq.setIsLeaf(0);
			subchainReq.setParentCustomerId(waybillReq.getCustomerId());
			subchainReq.setFreightForwarderId(waybillReq.getFreightForwarderId());
			List<FrameworkContractSubchain> subchains = frameworkContractSubchainMapper.selectFrameworkContractSubchainList(subchainReq);
			if (!CommonUtil.isNullOrEmpty(subchains)) {
				waybillReq.setContractIds(subchains.stream().map(a -> a.getContractId()).collect(Collectors.toList()));
			} else {
				// 如果根据项目转包链关联关系没有查到，说明没有运单
				return AjaxResult.success();
			}
			waybillReq.setCustomerId(null);
		}

		// 处理开票相关
		if (StringUtils.isNotEmpty(waybillReq.getInvoiceNumber()) || StringUtils.isNotEmpty(waybillReq.getInvoiceStartTime()) || StringUtils.isNotEmpty(waybillReq.getInvoiceEndTime())) {
			WaybillQuery waybillQuery = new WaybillQuery();
			waybillQuery.setPageNum(null);
			waybillQuery.setPageSize(null);
			waybillQuery.setAuditFreightForwarderId(SecurityUtils.getFreightForwarderId());
			waybillQuery.setInvoiceNumber(waybillReq.getInvoiceNumber());
			waybillQuery.setInvoiceStartTime(waybillReq.getInvoiceStartTime());
			waybillQuery.setInvoiceEndTime(waybillReq.getInvoiceEndTime());
			List<Waybill> waybills = financeStatementBillMapperEx.selectWaybillIdsByFinanceStatementInfo(waybillQuery);
			// 如果未查询到开票相关运单给个查不到得值
			if (waybills.isEmpty()) {
				List<String> str = new ArrayList<>();
				str.add("查询查不到得运单号");
				waybillReq.setShippingNoteNumbers(str);
				// 处理es查询条数
				List<Long> ids = new ArrayList<>();
				ids.add(Long.MAX_VALUE);
				waybillReq.setWaybillIds(ids);
			} else {
				waybillReq.setShippingNoteNumbers(waybills.stream().map(Waybill::getShippingNoteNumber).collect(Collectors.toList()));
				waybillReq.setWaybillIds(waybills.stream().map(Waybill::getId).collect(Collectors.toList()));
			}
		}

		WaybillTableRes res = waybillMapperEx.freightForwarderWaybillListSum(waybillReq);
		return AjaxResult.success(res);
	}

	@Override
	public CommonResult<WaybillTrackAbnormal> checkTrack(WaybillRes waybill, String nickName, Integer trajectoryDistance, Integer trajectoryTime) {
		try {

			// CommonResult<String> startResult = null;
			// CommonResult<String> endResult = null;

			if (null == trajectoryDistance || null == trajectoryTime) {
				// 获取每个网货商户的定位距离
				FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(waybill.getFreightForwarderId());
				trajectoryDistance = freightForwarderInfo.getTrajectoryDistance();
				trajectoryTime = freightForwarderInfo.getTrajectoryTime();
			}

			// 先把所有记录改为历史记录
			Long waybillId = waybill.getId();
			// waybillTrackAbnormalService.delByWaybillId(waybillId);

			CommonResult<String> result1 = trackService.trackCheck(waybillId, 1, trajectoryDistance, trajectoryTime, null);
			CommonResult<String> result2 = trackService.trackCheck(waybillId, 2, trajectoryDistance, trajectoryTime, null);

			// log.error("轨迹校验result1：{}", JSON.toJSONString(result1));
			// log.error("轨迹校验result2：{}", JSON.toJSONString(result2));
			Date curDate = new Date();
			WaybillTrackAbnormal waybillTrackAbnormal = new WaybillTrackAbnormal();
			BeanUtils.copyProperties(waybill, waybillTrackAbnormal);
			waybillTrackAbnormal.setId(TextUtil.getTimeSequenceID(5));
			waybillTrackAbnormal.setWaybillId(waybillId);
			waybillTrackAbnormal.setCustomerId(waybill.getParentCustomerId());
			waybillTrackAbnormal.setWaybillCreateBy(waybill.getCreateBy());
			waybillTrackAbnormal.setWaybillCreateTime(waybill.getCreateTime());
			waybillTrackAbnormal.setLoadTime(waybill.getDespatchActualDateTime());
			waybillTrackAbnormal.setPlaceOfLoading(waybill.getLoadingAddress());
			waybillTrackAbnormal.setLoadLongitude(waybill.getLoadingLongitude());
			waybillTrackAbnormal.setLoadLatitude(waybill.getLoadingLatitude());
			waybillTrackAbnormal.setGoodsReceiptPlace(waybill.getUnloadAddress());
			waybillTrackAbnormal.setCreateTime(curDate);
			waybillTrackAbnormal.setCreateBy(StringUtils.isBlank(nickName) ? "TASK" : nickName);
			waybillTrackAbnormal.setStatus(1);

			if ("200".equals(result1.getCode()) && "200".equals(result2.getCode())) {
				return CommonResult.success();
			} else {
				String createBy = "TASK".equals(waybillTrackAbnormal.getCreateBy()) ? "系统" : waybillTrackAbnormal.getCreateBy();
				if ("1".equals(result1.getCode()) && "2".equals(result2.getCode())) {
					waybillTrackAbnormal.setVerifyResult(3);// 3:起终点都异常
					waybillTrackAbnormal.setVerifyDesc(result1.getMsg() + "," + result2.getMsg());
					waybillTrackAbnormal.setDisposeRecord("<p> ● " + DateUtils.getFormatDateTime(curDate,
							"yyyy年M月d日 aHH:mm:ss") + " <b>" + createBy + "</b>核验结果：起终点都异常【" + waybillTrackAbnormal.getVerifyDesc() + "】<p/>");
				} else if ("4".equals(result1.getCode()) || "4".equals(result2.getCode())) {
					waybillTrackAbnormal.setVerifyResult(4);// 4:车辆未入网
					waybillTrackAbnormal.setVerifyDesc(result1.getMsg());
					waybillTrackAbnormal.setDisposeRecord("<p> ● " + DateUtils.getFormatDateTime(curDate, "yyyy年M月d日 aHH:mm:ss") + " <b>" + createBy + "</b>核验结果：车辆未入网<p/>");
				} else if ("9".equals(result1.getCode()) || "9".equals(result2.getCode())) {
					waybillTrackAbnormal.setVerifyResult(9);// 9:其他异常
					if ("9".equals(result1.getCode()) && "9".equals(result2.getCode())) {
						waybillTrackAbnormal.setVerifyDesc(result1.getMsg() + "," + result2.getMsg());
					} else if ("9".equals(result1.getCode()) && !"9".equals(result2.getCode())) {
						waybillTrackAbnormal.setVerifyDesc(result1.getMsg());
					} else {
						waybillTrackAbnormal.setVerifyDesc(result2.getMsg());
					}
					waybillTrackAbnormal.setDisposeRecord(
							"<p> ● " + DateUtils.getFormatDateTime(curDate, "yyyy年M月d日 aHH:mm:ss") + " <b>" + createBy + "</b>核验结果：其他异常【" + waybillTrackAbnormal.getVerifyDesc() + "】<p/>");
				} else if ("200".equals(result1.getCode())) {
					waybillTrackAbnormal.setVerifyResult(Integer.valueOf(result2.getCode()));// 2:起点通过，终点异常
					waybillTrackAbnormal.setVerifyDesc(result2.getMsg());
					waybillTrackAbnormal.setDisposeRecord(
							"<p> ● " + DateUtils.getFormatDateTime(curDate, "yyyy年M月d日 aHH:mm:ss") + " <b>" + createBy + "</b>核验结果：终点异常【" + waybillTrackAbnormal.getVerifyDesc() + "】<p/>");
				} else if ("200".equals(result2.getCode())) {
					waybillTrackAbnormal.setVerifyResult(Integer.valueOf(result1.getCode()));// 1:起点异常，终点通过
					waybillTrackAbnormal.setVerifyDesc(result1.getMsg());
					waybillTrackAbnormal.setDisposeRecord(
							"<p> ● " + DateUtils.getFormatDateTime(curDate, "yyyy年M月d日 aHH:mm:ss") + " <b>" + createBy + "</b>核验结果：起点异常【" + waybillTrackAbnormal.getVerifyDesc() + "】<p/>");
				}
				// else if ("1".equals(result1.getCode()) && "5".equals(result2.getCode())) {
				// waybillTrackAbnormal.setVerifyResult(7);
				// waybillTrackAbnormal.setVerifyDesc(result1.getMsg() + "," + result2.getMsg());
				// } else if ("4".equals(result1.getCode()) && "2".equals(result2.getCode())) {
				// waybillTrackAbnormal.setVerifyResult(8);
				// waybillTrackAbnormal.setVerifyDesc(result1.getMsg() + "," + result2.getMsg());
				// } else if ("4".equals(result1.getCode()) && "5".equals(result2.getCode())) {
				// waybillTrackAbnormal.setVerifyResult(6);
				// waybillTrackAbnormal.setVerifyDesc(result1.getMsg() + "," + result2.getMsg());
				// }

				if (StringUtils.isNotEmpty(result1.getData())) {
					com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
					Map<String, String> map = mapper.readValue(result1.getData(), Map.class);

					waybillTrackAbnormal.setFirstSuggestLocation(JSONObject.toJSONString(map.get("suggestLocation")));
					waybillTrackAbnormal.setFirstReferenceTrack(JSONObject.toJSONString(map.get("referenceTrack")));
				}

				if (StringUtils.isNotEmpty(result2.getData())) {
					com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
					Map<String, String> map = mapper.readValue(result2.getData(), Map.class);

					waybillTrackAbnormal.setLastSuggestLocation(JSONObject.toJSONString(map.get("suggestLocation")));
					waybillTrackAbnormal.setLastReferenceTrack(JSONObject.toJSONString(map.get("referenceTrack")));
				}

				/*if (startResult != null && StringUtils.isNotEmpty(startResult.getData())) {
					com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
					Map<String, String> map = mapper.readValue(startResult.getData(), Map.class);

					waybillTrackAbnormal.setFirstSuggestLocation(JSONObject.toJSONString(map.get("suggestLocation")));
					waybillTrackAbnormal.setFirstReferenceTrack(JSONObject.toJSONString(map.get("referenceTrack")));
				}

				if (endResult != null && StringUtils.isNotEmpty(endResult.getData())) {
					com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
					Map<String, String> map = mapper.readValue(endResult.getData(), Map.class);

					waybillTrackAbnormal.setLastSuggestLocation(JSONObject.toJSONString(map.get("suggestLocation")));
					waybillTrackAbnormal.setLastReferenceTrack(JSONObject.toJSONString(map.get("referenceTrack")));
				}*/
				return CommonResult.success(waybillTrackAbnormal);
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("轨迹校验异常：{}", ExceptionUtils.getStackTrace(e));
			return CommonResult.error("系统错误");
		}
	}

	public List<Waybill> queryUnArriveWaybillByVehicleId(Long vehicleId, Long customerId) {
		return waybillMapperEx.queryUnArriveWaybillByVehicleId(vehicleId, customerId);
	}

	public List<Waybill> queryUnArriveWaybillByDriverid(Long driverId, Long customerId) {
		return waybillMapperEx.queryUnArriveWaybillByDriverid(driverId, customerId);
	}

	/**
	 * 查询第一次上报税务系统数据
	 *
	 * @param waybillId
	 * 		运单ID
	 */
	public TaxUploadWaybillInfo findTaxUpload1WaybillInfo(Long waybillId) {
		return waybillMapperEx.findTaxUpload1WaybillInfo(waybillId);
	}

	/**
	 * 查询第二次上报税务系统数据
	 *
	 * @param waybillId
	 * 		运单ID
	 */
	public TaxUploadWaybillInfo findTaxUpload2WaybillInfo(Long waybillId) {
		return waybillMapperEx.findTaxUpload2WaybillInfo(waybillId);
	}

	public TaxUploadWaybillInfo findTaxUploadArriveWaybillInfo(Long waybillId) {
		return waybillMapperEx.findTaxUploadArriveWaybillInfo(waybillId);
	}

	public List<Long> selectNeedUpdateArriverTaxBill() {
		return waybillMapperEx.selectNeedUpdateArriverTaxBill();
	}

	public void updateNeedUpdateArriverTaxBill(List<Long> ids) {
		waybillMapperEx.updateNeedUpdateArriverTaxBill(ids);
	}

	public List<Long> selectNeedSecondTaxUploadBill() {
		return waybillMapperEx.selectNeedSecondTaxUploadBill();
	}

	public List<Long> selectNeedUpdateSecondTaxBill() {
		return waybillMapperEx.selectNeedUpdateSecondTaxBill();
	}

	public void updateNeedUpdateSecondTaxBill(List<Long> secondIds) {
		waybillMapperEx.updateNeedUpdateSecondTaxBill(secondIds);
	}

	public List<Long> selectNeedUpdateThirdTaxBill() {
		return waybillMapperEx.selectNeedUpdateThirdTaxBill();
	}

	public void updateNeedUpdateThirdTaxBill(List<Long> thirdIds) {
		waybillMapperEx.updateNeedUpdateThirdTaxBill(thirdIds);
	}

	public Waybill selectWaybillByWaybillCode(String waybillCode) {
		return waybillMapperEx.selectWaybillByWaybillCode(waybillCode);
	}

	@Resource
	private CarrierMessageInfoServiceImpl carrierMessageInfoService;
	@Resource
	private CarrierUserServiceImpl carrierUserService;

	@Override
	public void sendUnloadMsg() {
		List<Waybill> list = waybillMapperEx.selectUnloadBill();
		for (Waybill waybill : list) {
			if (!redisCache.hasKey("sendPhone_" + waybill.getId())) {
				redisCache.setCacheObject("sendPhone_" + waybill.getId(), 1, 15, TimeUnit.MINUTES);

				Long userId = carrierUserService.selectCarrierIdUserByDriverId(waybill.getDriverId());
				carrierMessageInfoService.sendMsg(waybill.getId(), userId, 3, "", 0);
			}
		}
	}

	@Override
	public Integer selectUnDeleteWaybills(Long contractId) {
		return waybillMapperEx.selectUnDeleteWaybills(contractId);
	}

	@Override
	@Transactional
	public CommonResult recalculateWaybillFare(WaybillReq waybillReq) {
		if (waybillReq.getMakeCodeRuleId() == null) {
			return CommonResult.error("货损规则不可为空");
		}

		Waybill updateWaybill = new Waybill();
		updateWaybill.setUpdateBy(SecurityUtils.getNickname());
		updateWaybill.setUpdateTime(new Date());

		List<Waybill> waybills = waybillMapperEx.selectWaybillListByIds(waybillReq.getWaybillIds());
		CommonResult commonResult = judgeWaybill(waybills);
		if (CommonResult.isNotSuccess(commonResult)) {
			return commonResult;
		}

		WaybillSlave updateWaybillSlave = new WaybillSlave();
		updateWaybillSlave.setMakeCodeRuleId(waybillReq.getMakeCodeRuleId());
		updateWaybillSlave.setZeroingRule(waybillReq.getZeroingRule());
		if (waybillReq.getMakeCodeRuleId() != 0L) {
			MakeCodeRule rule = makeCodeRuleMapper.selectMakeCodeRuleById(waybillReq.getMakeCodeRuleId());
			Integer feeUnit = rule.getFeeUnit();
			if (!feeUnit.equals(waybills.get(0).getFeeUnit())) {
				return CommonResult.error("运单单位与货损规则单位不一致");
			}
			updateWaybillSlave.setMakeCodeRuleDetailed(rule.getDetailed());
		} else {
			updateWaybillSlave.setMakeCodeRuleDetailed("");
		}

		for (Waybill waybill : waybills) {
			updateWaybill.setId(waybill.getId());
			updateWaybill.setWaybillPrice(waybill.getWaybillPrice());
			updateWaybill.setFeeUnit(waybill.getFeeUnit());
			updateWaybill.setDeductMoney(waybill.getDeductMoney());
			updateWaybill.setPrepayMoney(waybill.getPrepayMoney());
			updateWaybill.setReceiptMoney(waybill.getReceiptMoney());
			WaybillSlave waybillSlave = waybillSlaveMapper.selectWaybillSlaveById(waybill.getId());
			Integer oldZeroingRule = waybillSlave.getZeroingRule();
			waybillSlave.setZeroingRule(updateWaybillSlave.getZeroingRule());
			waybillSlave.setMakeCodeRuleId(updateWaybillSlave.getMakeCodeRuleId());
			waybillSlave.setMakeCodeRuleDetailed(updateWaybillSlave.getMakeCodeRuleDetailed());
			countPayFare(updateWaybill, waybillReq, waybillSlave);
			waybillMapper.updateWaybill(updateWaybill);

			// 修改子表数据
			updateWaybillSlave.setId(waybill.getId());
			waybillSlaveMapper.updateWaybillSlave(updateWaybillSlave);

			// 修改上下游费用
			waybillCustomerService.updatePayableUpAndDowm(waybill.getId());

			if (waybill.getPayFare().compareTo(updateWaybill.getPayFare()) != 0) {
				paymentRecordService.addPaymentRecord(waybill);
			}

			String oldZeroingRuleString = "";
			if (oldZeroingRule == 0) {
				oldZeroingRuleString = "不抹零";
			} else if (oldZeroingRule == 1) {
				oldZeroingRuleString = "角分抹零";
			} else if (oldZeroingRule == 2) {
				oldZeroingRuleString = "十元以下抹零";
			} else if (oldZeroingRule == 3) {
				oldZeroingRuleString = "二十元以下抹零";
			}

			Integer zeroingRule = waybillReq.getZeroingRule();
			String zeroingRuleString = "";
			if (zeroingRule == 0) {
				zeroingRuleString = "不抹零";
			} else if (zeroingRule == 1) {
				zeroingRuleString = "角分抹零";
			} else if (zeroingRule == 2) {
				zeroingRuleString = "十元以下抹零";
			} else if (zeroingRule == 3) {
				zeroingRuleString = "二十元以下抹零";
			}

			MakeCodeRule oldRule = makeCodeRuleMapper.selectMakeCodeRuleById(waybillSlave.getMakeCodeRuleId());
			if (oldRule == null) {
				oldRule = new MakeCodeRule();
			}
			MakeCodeRule rule = new MakeCodeRule();
			rule.setName("清除现有规则");
			if (waybillReq.getMakeCodeRuleId() != 0) {
				rule = makeCodeRuleMapper.selectMakeCodeRuleById(waybillReq.getMakeCodeRuleId());
			}

			String actionName = "重新计算运费(运单ID：" + waybill.getId() + "，运单号：" + waybill.getShippingNoteNumber() + ",原规则id:" + waybillSlave.getMakeCodeRuleId() + "原规则名称:" + oldRule.getName() + ",原抹零规则:" + oldZeroingRuleString;
			actionName += "，请求参数：" + JSONUtil.toJsonStr(waybillReq);
			sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_WAYBILL, BusinessConstants.ACTION_TYPE_UPDATE, actionName, waybill.getShippingNoteNumber(),
					Collections.singletonList(waybill.getId()));
		}

		return CommonResult.success();
	}

	private CommonResult judgeWaybill(List<Waybill> waybills) {
		StringBuilder payWaybillCode = new StringBuilder();
		StringBuilder applyWaybillCode = new StringBuilder();
		StringBuilder result = new StringBuilder();
		Integer lastFeeUnit = -1;

		Boolean isPayWaybill = false;
		Boolean isApplyWaybill = false;
		if (waybills.size() == 1) {
			Waybill waybill = waybills.get(0);
			if (waybill.getResource() != 6 && waybill.getOperationPlainId() != 0) {
				return CommonResult.error("批量重新计算，仅支持小程序来源的大宗货源，请按照要求检查运单后，再重新操作");
			}
			if (waybill.getSettleStatus() != 0) {
				return CommonResult.error("您选择的运单已有结算数据，不可重新计算运费");
			} else if (waybill.getPayApplyStatus() != 0 && waybill.getPayApplyStatus() != 9) {
				return CommonResult.error("您选择的运单正在付款中，不可重新计算运费");
			}
		} else {
			for (Waybill waybill : waybills) {
				if (lastFeeUnit == -1) {
					lastFeeUnit = waybill.getFeeUnit();
				} else {
					if (!lastFeeUnit.equals(waybill.getFeeUnit())) {
						return CommonResult.error("您选择的运单单位不一致，不可重新计算运费");
					}
				}
			}
			for (Waybill waybill : waybills) {
				if (waybill.getResource() != 6 && waybill.getOperationPlainId() != 0) {
					return CommonResult.error("您选择的运单，非小程序来源的大宗货源，无需重新计算运费，若需修改，请点击变更运单按钮，手动更新运费");
				}
				if (waybill.getSettleStatus() != 0) {
					payWaybillCode.append(waybill.getShippingNoteNumber()).append(",");
					isPayWaybill = true;
				}
				if (waybill.getPayApplyStatus() != 0 && waybill.getPayApplyStatus() != 9) {
					applyWaybillCode.append(waybill.getShippingNoteNumber()).append(",");
					isApplyWaybill = true;
				}
			}
		}
		if (isApplyWaybill) {
			result.append(applyWaybillCode).append("正在付款中");
		}
		if (isPayWaybill) {
			result.append(",").append(payWaybillCode).append("有结算数据");
		}
		if (isPayWaybill || isApplyWaybill) {
			result.append("，请先取消选择，再重新计算运费");
			return CommonResult.error(result.toString());
		}

		return CommonResult.success();
	}

	private void countPayFare(Waybill waybill, WaybillReq req, WaybillSlave waybillSlave) {
		BigDecimal loadingAccount = waybill.getFeeUnit() == 1 ? waybillSlave.getLoadingWeight() : waybillSlave.getLoadingCube();
		BigDecimal unloadAccount = waybill.getFeeUnit() == 1 ? waybillSlave.getUnloadWeight() : waybillSlave.getUnloadCube();
		BigDecimal fare = BigDecimal.ZERO;
		if (waybillSlave.getWaybillPaymentType() == 0) {
			fare = loadingAccount.multiply(waybill.getWaybillPrice());
		} else if (waybillSlave.getWaybillPaymentType() == 1) {
			fare = unloadAccount.multiply(waybill.getWaybillPrice());
		} else if (waybillSlave.getWaybillPaymentType() == 2) {
			fare = waybill.getWaybillPrice().multiply(loadingAccount.compareTo(unloadAccount) < 0 ? loadingAccount : unloadAccount); // 运输类别：方
		}
		BigDecimal diffMoney = waybillCommonService.calculateDiffMoneyWithRule(loadingAccount, unloadAccount, req.getMakeCodeRuleId());
		BigDecimal payFare = fare.subtract(diffMoney).subtract(waybill.getDeductMoney()).setScale(2, RoundingMode.HALF_UP);
		waybill.setPayFare(payFare);
		waybill.setBeforeZeroingMoney(payFare);
		payFare = waybillCommonService.caculateBULKPayFareByZeroingRule(payFare, waybillSlave.getZeroingRule());
		if (waybillSlave.getZeroingRule() > 0) { // 抹零时
			waybill.setZeroingMoney(waybill.getBeforeZeroingMoney().subtract(payFare));
		} else { // 不抹零时
			waybill.setZeroingMoney(BigDecimal.ZERO);
		}
		BigDecimal extraFare = waybillFareService.getTotalExtraFareByWaybillId(waybill.getId());// 加上货损货差
		payFare = payFare.add(extraFare);
		waybill.setPayFare(payFare);// 应付运费
		waybill.setTotalFare(payFare);// 总费用
		waybill.setArriveMoney(payFare.subtract(waybill.getPrepayMoney()).subtract(waybill.getReceiptMoney()));// 到付运费
		waybill.setArriveUnpaidMoney(waybill.getArriveMoney());
		waybill.setStatementReceiveFare(payFare);// 对账用的应收运费
		waybill.setUnpaidFare(payFare);// 未付金额
	}

	/**
	 * 计算含税承运费（元）：司机代开代缴的费用=承运费/（1-3%）
	 *
	 * @param payFare
	 * @return
	 */
	public BigDecimal calculateTaxInclusiveTransportFee(BigDecimal payFare) {
		SysParameter sysParameter = new SysParameter();
		sysParameter.setCode("inclusiveTransportFee");
		sysParameter.setType(1);
		sysParameter.setState(0);
		List<SysParameter> sysParameters = sysParameterService.selectSysParameterList(sysParameter);
		if (CommonUtil.isNullOrEmpty(sysParameters)) {
			return null;
		}
		// 获取税率值，并将其转换为BigDecimal类型
		String taxRateStr = sysParameters.get(0).getParaValue();
		BigDecimal taxRate = new BigDecimal(taxRateStr);

		// 计算含税承运费并返回
		return payFare.divide(BigDecimal.ONE.subtract(taxRate), 2, RoundingMode.HALF_UP);
	}

	/**
	 * 根据运单ID获取运单额外费用合计
	 *
	 * @param waybillId
	 * @return
	 */
	public BigDecimal waybillFareSumByWaybillId(Long waybillId) {
		return waybillMapperEx.waybillFareSumByWaybillId(waybillId);
	}

	@Override
	public CommonResult<List> payeeInfoList(PayeeReq req) {
		if (req.getType() == 1) {
			// 司机
			List<PayeeRes> list = driverMapperEx.queryDriverInfo(req.getKeywords(), SecurityUtils.getShipperId());
			return CommonResult.success(list);
		} else if (req.getType() == 2) {
			// 车队长
			List<PayeeRes> list = carCaptainInfoMapper.queryCaptainInfo(req.getKeywords(), SecurityUtils.getShipperId());
			return CommonResult.success(list);
		} else if (req.getType() == 3) {
			// 辅助员
			List<PayeeRes> list = auxiliaryStaffInfoMapper.queryAuxiliaryInfo(req.getKeywords(), SecurityUtils.getShipperId());
			return CommonResult.success(list);
		} else {
			return CommonResult.success(new ArrayList());
		}
	}

	@Override
	public CommonResult<List> shandaoPayeeInfoList(PayeeReq req) {
		if (null == req || null == req.getFreightForwarderId()) {
			return CommonResult.error("参数错误");
		}
		// 如果是善道的业务，则进行辅助员签约判断
		FreightForwarderInfo freightForwarderInfo = new FreightForwarderInfo();
		freightForwarderInfo.setCreditCode("91320830MADCRXEJ3Q");
		freightForwarderInfo.setState(0);
		FreightForwarderInfo _freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoList(freightForwarderInfo).get(0);
		List<PayeeRes> list = new ArrayList<>();
		if (req.getFreightForwarderId().equals(_freightForwarderInfo.getId())) {
			list = auxiliaryStaffInfoMapper.queryShandaoAuxiliaryInfo(req.getKeywords(), SecurityUtils.getShipperId(), freightForwarderInfo.getId(), 0);
		} else {
			list = auxiliaryStaffInfoMapper.queryShandaoAuxiliaryInfo(req.getKeywords(), SecurityUtils.getShipperId(), null, null);
		}

		return CommonResult.success(list);
	}

	// 判断传入的项目id是否属于善道，如果是善道的项目则返回善道id，不是善道项目返回0
	@Override
	public CommonResult<Long> shandaoFrameworkContract(Long frameworkContractId) {
		if (null == frameworkContractId || frameworkContractId == 0L) {
			return CommonResult.error("参数错误");
		}
		FrameworkContract frameworkContract = frameworkContractService.selectFrameworkContractById(frameworkContractId);
		// 如果是善道的业务，则进行辅助员签约判断
		FreightForwarderInfo freightForwarderInfo = new FreightForwarderInfo();
		freightForwarderInfo.setCreditCode("91320830MADCRXEJ3Q");
		freightForwarderInfo.setState(0);
		FreightForwarderInfo _freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoList(freightForwarderInfo).get(0);
		Long freightForwarderId = 0L;
		if (frameworkContract.getFreightForwarderId().equals(_freightForwarderInfo.getId())) {
			freightForwarderId = frameworkContract.getFreightForwarderId();
		}
		return CommonResult.success(freightForwarderId);
	}

	public CommonResult validatePhone(String phone, String iDCard) {
		// 手机号唯一性校验
		CustomerCaptainRelation customerCaptainRelation = new CustomerCaptainRelation();
		customerCaptainRelation.setCarCaptainContactPhone(phone);
		customerCaptainRelation.setCustomerId(SecurityUtils.getShipperId());
		List<CustomerCaptainRelation> customerCaptainRelationList = customerCaptainRelationMapper.selectCustomerCaptainRelationList(customerCaptainRelation).stream()
				.filter(f -> !f.getActualIdentityCard().equals(iDCard)).collect(Collectors.toList());
		if (!customerCaptainRelationList.isEmpty()) {
			return CommonResult.error("当前托运人下手机号已被其他车队长使用");
		}

		// 手机号唯一性校验
		CustomerAuxiliaryRelation customerAuxiliaryRelation = new CustomerAuxiliaryRelation();
		customerAuxiliaryRelation.setAuxiliaryStaffContactPhone(phone);
		customerAuxiliaryRelation.setCustomerId(SecurityUtils.getShipperId());
		List<CustomerAuxiliaryRelation> customerAuxiliaryRelations = customerAuxiliaryRelationMapper.selectCustomerAuxiliaryRelationList(customerAuxiliaryRelation).stream()
				.filter(f -> !f.getAuxiliaryStaffIdentityCard().equals(iDCard)).collect(Collectors.toList());
		if (!customerAuxiliaryRelations.isEmpty()) {
			return CommonResult.error("当前托运人下手机号已被其他辅助员使用");
		}

		// 手机号唯一性校验
		CustomerDriverRelation customerDriverRelation = new CustomerDriverRelation();
		customerDriverRelation.setTelephone(phone);
		customerDriverRelation.setCustomerId(SecurityUtils.getShipperId());
		List<CustomerDriverRelation> customerDriverRelations = customerDriverRelationMapper.selectCustomerDriverRelationList(customerDriverRelation).stream()
				.filter(f -> !f.getDrivingLicense().equals(iDCard)).collect(Collectors.toList());
		if (!customerDriverRelations.isEmpty()) {
			return CommonResult.error("当前托运人下手机号已被其他司机使用");
		}
		return CommonResult.success();
	}

	public void validatePhoneNew(String phone, String iDCard, int dispatchIndex, List<DispatchError> errorList) {
		// 手机号唯一性校验 - 车队长
		CustomerCaptainRelation customerCaptainRelation = new CustomerCaptainRelation();
		customerCaptainRelation.setCarCaptainContactPhone(phone);
		customerCaptainRelation.setCustomerId(SecurityUtils.getShipperId());
		List<CustomerCaptainRelation> customerCaptainRelationList = customerCaptainRelationMapper.selectCustomerCaptainRelationList(customerCaptainRelation).stream()
				.filter(f -> !f.getActualIdentityCard().equals(iDCard)).collect(Collectors.toList());
		if (!customerCaptainRelationList.isEmpty()) {
			// 添加到错误集合，包含调度索引和错误信息
			errorList.add(new DispatchError(dispatchIndex, "telephone", String.format("第 %d 个调度的手机号已被其他车队长使用", dispatchIndex)));
		}

		// 手机号唯一性校验 - 辅助员
		CustomerAuxiliaryRelation customerAuxiliaryRelation = new CustomerAuxiliaryRelation();
		customerAuxiliaryRelation.setAuxiliaryStaffContactPhone(phone);
		customerAuxiliaryRelation.setCustomerId(SecurityUtils.getShipperId());
		List<CustomerAuxiliaryRelation> customerAuxiliaryRelations = customerAuxiliaryRelationMapper.selectCustomerAuxiliaryRelationList(customerAuxiliaryRelation).stream()
				.filter(f -> !f.getAuxiliaryStaffIdentityCard().equals(iDCard)).collect(Collectors.toList());
		if (!customerAuxiliaryRelations.isEmpty()) {
			// 添加到错误集合，包含调度索引和错误信息
			errorList.add(new DispatchError(dispatchIndex, "telephone", String.format("第 %d 个调度的手机号已被其他辅助员使用", dispatchIndex)));
		}

		// 手机号唯一性校验 - 司机
		CustomerDriverRelation customerDriverRelation = new CustomerDriverRelation();
		customerDriverRelation.setTelephone(phone);
		customerDriverRelation.setCustomerId(SecurityUtils.getShipperId());
		List<CustomerDriverRelation> customerDriverRelations = customerDriverRelationMapper.selectCustomerDriverRelationList(customerDriverRelation).stream()
				.filter(f -> !f.getDrivingLicense().equals(iDCard)).collect(Collectors.toList());
		if (!customerDriverRelations.isEmpty()) {
			// 添加到错误集合，包含调度索引和错误信息
			errorList.add(new DispatchError(dispatchIndex, "telephone", String.format("第 %d 个调度的手机号已被其他司机使用", dispatchIndex)));
		}
	}

	public void registerCarCaptain(DispatchReq req) {
		// 注册车队长
		// 查询运力库是否有车队长
		CarCaptainInfo captainInfoQuery = new CarCaptainInfo();
		captainInfoQuery.setIdentityCard(req.getPayeeIdentityCard());
		captainInfoQuery.setState(0);
		List<CarCaptainInfo> carCaptainInfos = carCaptainInfoMapper.selectCarCaptainInfoList(captainInfoQuery);
		if (StringUtils.isNotEmpty(carCaptainInfos)) {
			CarCaptainInfo carCaptainInfo = carCaptainInfos.get(0);
			// 查询关联关系
			CustomerCaptainRelation customerCaptainRelationQuery = new CustomerCaptainRelation();
			customerCaptainRelationQuery.setCarCaptainId(carCaptainInfo.getId());
			customerCaptainRelationQuery.setCustomerId(SecurityUtils.getShipperId());
			List<CustomerCaptainRelation> customerCaptainRelations = customerCaptainRelationMapper.selectCustomerCaptainRelationList(customerCaptainRelationQuery);
			if (StringUtils.isNotEmpty(customerCaptainRelations)) {
				CustomerCaptainRelation customerCaptainRelation = customerCaptainRelations.get(0);
				customerCaptainRelation.setCarCaptainContactPhone(req.getTelephone());
				customerCaptainRelationMapper.updateCustomerCaptainRelation(customerCaptainRelation);

				ActualCarrierInfo actualCarrierInfoQuery = new ActualCarrierInfo();
				actualCarrierInfoQuery.setIdentityCard(req.getPayeeIdentityCard());
				actualCarrierInfoQuery.setState(0);
				List<ActualCarrierInfo> actualCarrierInfos = actualCarrierInfoMapper.selectActualCarrierInfoList(actualCarrierInfoQuery);
				if (StringUtils.isNotEmpty(actualCarrierInfos)) {
					ActualCarrierInfo actualCarrierInfo = actualCarrierInfos.get(0);
					actualCarrierInfo.setContactPhone(req.getTelephone());
					actualCarrierInfoMapper.updateActualCarrierInfo(actualCarrierInfo);
				}
			} else {
				// 绑定关系
				CustomerCaptainRelation customerCaptainRelationAdd = new CustomerCaptainRelation();
				customerCaptainRelationAdd.setCustomerId(SecurityUtils.getShipperId());
				customerCaptainRelationAdd.setActualIdentityCard(req.getPayeeIdentityCard());
				customerCaptainRelationAdd.setCarCaptainId(carCaptainInfo.getId());
				customerCaptainRelationAdd.setCarCaptainContactPhone(req.getTelephone());
				customerCaptainRelationMapper.insertCustomerCaptainRelation(customerCaptainRelationAdd);

				ActualCarrierInfo actualCarrierInfoQuery = new ActualCarrierInfo();
				actualCarrierInfoQuery.setIdentityCard(req.getPayeeIdentityCard());
				actualCarrierInfoQuery.setState(0);
				List<ActualCarrierInfo> actualCarrierInfos = actualCarrierInfoMapper.selectActualCarrierInfoList(actualCarrierInfoQuery);
				if (StringUtils.isNotEmpty(actualCarrierInfos)) {
					ActualCarrierInfo actualCarrierInfo = actualCarrierInfos.get(0);
					actualCarrierInfo.setContactPhone(req.getTelephone());
					actualCarrierInfoMapper.updateActualCarrierInfo(actualCarrierInfo);
				}
			}
		} else {
			CarCaptainInfo captainInfo = new CarCaptainInfo();
			Long newCaptainId = TextUtil.getTimeSequenceID(5);
			captainInfo.setId(newCaptainId);
			captainInfo.setIdentityCard(req.getPayeeIdentityCard());
			captainInfo.setCarCaptainName(req.getPayeeName());
			captainInfo.setAuthenticationMethod(0);
			captainInfo.setOperation(2);
			captainInfo.setCreateCompanyId(SecurityUtils.getShipperId());
			captainInfo.setCreateBy(SecurityUtils.getNickname());
			captainInfo.setCreateTime(new Date());
			captainInfo.setApproveState(1); // 待审核
			captainInfo.setInfoIsComplete(1);// 资料不齐全
			carCaptainInfoMapper.insertCarCaptainInfo(captainInfo);

			// 绑定关联关系
			CustomerCaptainRelation customerCaptainRelationAdd = new CustomerCaptainRelation();
			customerCaptainRelationAdd.setCustomerId(SecurityUtils.getShipperId());
			customerCaptainRelationAdd.setActualIdentityCard(req.getPayeeIdentityCard());
			customerCaptainRelationAdd.setCarCaptainId(newCaptainId);
			customerCaptainRelationAdd.setCarCaptainContactPhone(req.getTelephone());
			customerCaptainRelationMapper.insertCustomerCaptainRelation(customerCaptainRelationAdd);

			ActualCarrierInfo actualCarrierInfoQuery = new ActualCarrierInfo();
			actualCarrierInfoQuery.setIdentityCard(req.getPayeeIdentityCard());
			actualCarrierInfoQuery.setState(0);
			List<ActualCarrierInfo> actualCarrierInfos = actualCarrierInfoMapper.selectActualCarrierInfoList(actualCarrierInfoQuery);
			if (StringUtils.isNotEmpty(actualCarrierInfos)) {
				ActualCarrierInfo actualCarrierInfo = actualCarrierInfos.get(0);
				actualCarrierInfo.setCarCaptainId(newCaptainId);
				actualCarrierInfo.setContactPhone(req.getTelephone());
				actualCarrierInfoMapper.updateActualCarrierInfo(actualCarrierInfo);
			} else {
				// 添加承运人
				ActualCarrierInfo carrierInfoAdd = new ActualCarrierInfo();
				carrierInfoAdd.setId(TextUtil.getTimeSequenceID(5));
				carrierInfoAdd.setIdentityCard(req.getPayeeIdentityCard());
				carrierInfoAdd.setActualCarrierName(req.getPayeeName());
				carrierInfoAdd.setCreateClient(3);
				carrierInfoAdd.setOperation(15);
				carrierInfoAdd.setIsComplete(1);
				carrierInfoAdd.setContactPhone(req.getTelephone());
				carrierInfoAdd.setInfoIsComplete(1);
				carrierInfoAdd.setCarCaptainId(newCaptainId);
				carrierInfoAdd.setCreateBy(SecurityUtils.getNickname());
				carrierInfoAdd.setCreateTime(new Date());
				carrierInfoAdd.setUpdateBy(SecurityUtils.getNickname());
				carrierInfoAdd.setUpdateTime(new Date());
				actualCarrierInfoMapper.insertActualCarrierInfo(carrierInfoAdd);
			}
		}
	}

	public void registerAuxiliaryStaff(DispatchReq req) {
		// 注册辅助员
		AuxiliaryStaffInfo auxiliaryInfoQuery = new AuxiliaryStaffInfo();
		auxiliaryInfoQuery.setIdentityCard(req.getPayeeIdentityCard());
		auxiliaryInfoQuery.setState(0);
		List<AuxiliaryStaffInfo> auxiliaryStaffInfos = auxiliaryStaffInfoMapper.selectAuxiliaryStaffInfoList(auxiliaryInfoQuery);
		if (StringUtils.isNotEmpty(auxiliaryStaffInfos)) {
			AuxiliaryStaffInfo auxiliaryStaffInfo = auxiliaryStaffInfos.get(0);
			CustomerAuxiliaryRelation customerAuxiliaryRelationQuery = new CustomerAuxiliaryRelation();
			customerAuxiliaryRelationQuery.setAuxiliaryStaffId(auxiliaryStaffInfo.getId());
			customerAuxiliaryRelationQuery.setCustomerId(SecurityUtils.getShipperId());
			List<CustomerAuxiliaryRelation> customerAuxiliaryRelations = customerAuxiliaryRelationMapper.selectCustomerAuxiliaryRelationList(customerAuxiliaryRelationQuery);
			if (StringUtils.isNotEmpty(customerAuxiliaryRelations)) {
				CustomerAuxiliaryRelation customerAuxiliaryRelation = customerAuxiliaryRelations.get(0);
				customerAuxiliaryRelation.setAuxiliaryStaffContactPhone(req.getTelephone());
				customerAuxiliaryRelationMapper.updateCustomerAuxiliaryRelation(customerAuxiliaryRelation);
				ActualCarrierInfo actualCarrierInfoQuery = new ActualCarrierInfo();
				actualCarrierInfoQuery.setIdentityCard(req.getPayeeIdentityCard());
				actualCarrierInfoQuery.setState(0);
				List<ActualCarrierInfo> actualCarrierInfos = actualCarrierInfoMapper.selectActualCarrierInfoList(actualCarrierInfoQuery);
				if (StringUtils.isNotEmpty(actualCarrierInfos)) {
					ActualCarrierInfo actualCarrierInfo = actualCarrierInfos.get(0);
					actualCarrierInfo.setContactPhone(req.getTelephone());
					actualCarrierInfoMapper.updateActualCarrierInfo(actualCarrierInfo);
				}
			} else {
				// 绑定关联关系
				CustomerAuxiliaryRelation customerAuxiliaryRelationAdd = new CustomerAuxiliaryRelation();
				customerAuxiliaryRelationAdd.setCustomerId(SecurityUtils.getShipperId());
				customerAuxiliaryRelationAdd.setAuxiliaryStaffIdentityCard(req.getPayeeIdentityCard());
				customerAuxiliaryRelationAdd.setAuxiliaryStaffId(auxiliaryStaffInfo.getId());
				customerAuxiliaryRelationAdd.setAuxiliaryStaffContactPhone(req.getTelephone());
				customerAuxiliaryRelationMapper.insertCustomerAuxiliaryRelation(customerAuxiliaryRelationAdd);

				ActualCarrierInfo actualCarrierInfoQuery = new ActualCarrierInfo();
				actualCarrierInfoQuery.setIdentityCard(req.getPayeeIdentityCard());
				actualCarrierInfoQuery.setState(0);
				List<ActualCarrierInfo> actualCarrierInfos = actualCarrierInfoMapper.selectActualCarrierInfoList(actualCarrierInfoQuery);
				if (StringUtils.isNotEmpty(actualCarrierInfos)) {
					ActualCarrierInfo actualCarrierInfo = actualCarrierInfos.get(0);
					actualCarrierInfo.setContactPhone(req.getTelephone());
					actualCarrierInfoMapper.updateActualCarrierInfo(actualCarrierInfo);
				}
			}
		} else {
			AuxiliaryStaffInfo auxiliaryInfo = new AuxiliaryStaffInfo();
			Long newAuxiliaryId = TextUtil.getTimeSequenceID(5);
			auxiliaryInfo.setIdentityCard(req.getPayeeIdentityCard());
			auxiliaryInfo.setId(newAuxiliaryId);
			auxiliaryInfo.setAuxiliaryStaffName(req.getPayeeName());
			auxiliaryInfo.setIdentityCard(req.getPayeeIdentityCard());
			auxiliaryInfo.setOperation(2);
			auxiliaryInfo.setApproveState(2);
			auxiliaryInfo.setCreateBy(SecurityUtils.getNickname());
			auxiliaryInfo.setCreateTime(new Date());
			auxiliaryInfo.setInfoIsComplete(1);
			auxiliaryInfo.setPayFarePlan(1);
			auxiliaryInfo.setPayFareData(BigDecimal.ONE);
			auxiliaryStaffInfoMapper.insertAuxiliaryStaffInfo(auxiliaryInfo);

			// 绑定关联关系
			CustomerAuxiliaryRelation customerAuxiliaryRelationAdd = new CustomerAuxiliaryRelation();
			customerAuxiliaryRelationAdd.setCustomerId(SecurityUtils.getShipperId());
			customerAuxiliaryRelationAdd.setAuxiliaryStaffIdentityCard(req.getPayeeIdentityCard());
			customerAuxiliaryRelationAdd.setAuxiliaryStaffId(newAuxiliaryId);
			customerAuxiliaryRelationAdd.setAuxiliaryStaffContactPhone(req.getTelephone());
			customerAuxiliaryRelationMapper.insertCustomerAuxiliaryRelation(customerAuxiliaryRelationAdd);

			ActualCarrierInfo actualCarrierInfoQuery = new ActualCarrierInfo();
			actualCarrierInfoQuery.setIdentityCard(req.getPayeeIdentityCard());
			actualCarrierInfoQuery.setState(0);
			List<ActualCarrierInfo> actualCarrierInfos = actualCarrierInfoMapper.selectActualCarrierInfoList(actualCarrierInfoQuery);
			if (StringUtils.isNotEmpty(actualCarrierInfos)) {
				ActualCarrierInfo actualCarrierInfo = actualCarrierInfos.get(0);
				actualCarrierInfo.setAuxiliaryStaffId(newAuxiliaryId);
				actualCarrierInfo.setContactPhone(req.getTelephone());
				actualCarrierInfoMapper.updateActualCarrierInfo(actualCarrierInfo);
			} else {
				// 添加承运人
				ActualCarrierInfo carrierInfoAdd = new ActualCarrierInfo();
				carrierInfoAdd.setId(TextUtil.getTimeSequenceID(5));
				carrierInfoAdd.setActualCarrierName(req.getPayeeName());
				carrierInfoAdd.setAuxiliaryStaffId(newAuxiliaryId);
				carrierInfoAdd.setIdentityCard(req.getPayeeIdentityCard());
				carrierInfoAdd.setCreateClient(3);
				carrierInfoAdd.setOperation(15);
				carrierInfoAdd.setIsComplete(1);
				carrierInfoAdd.setContactPhone(req.getTelephone());
				carrierInfoAdd.setInfoIsComplete(1);
				carrierInfoAdd.setCreateBy(SecurityUtils.getNickname());
				carrierInfoAdd.setCreateTime(new Date());
				carrierInfoAdd.setUpdateBy(SecurityUtils.getNickname());
				carrierInfoAdd.setUpdateTime(new Date());
				actualCarrierInfoMapper.insertActualCarrierInfo(carrierInfoAdd);
			}
		}
	}

	public void addDriverAndCarrier(DispatchReq req) {
		// 添加新司机
		Driver driverAdd = new Driver();
		Long newDriverId = TextUtil.getTimeSequenceID(5); // 生成新的司机ID
		driverAdd.setId(newDriverId);
		driverAdd.setDriverName(req.getDriverName());
		driverAdd.setDrivingLicense(req.getDrivingLicense());
		driverAdd.setIdentityCard(req.getDrivingLicense());
		driverAdd.setIsComplete(1); // 不是司机本人创建
		driverAdd.setInfoIsComplete(1); // 资料不齐全
		driverAdd.setOperation(15); // 实时调度创建
		driverAdd.setCreateBy(SecurityUtils.getNickname());
		driverAdd.setCreateTime(new Date());
		driverService.insertDriver(driverAdd);

		// 绑定客户与司机关系
		CustomerDriverRelation customerDriverRelationAdd = new CustomerDriverRelation();
		customerDriverRelationAdd.setCustomerId(SecurityUtils.getShipperId());
		customerDriverRelationAdd.setDriverId(newDriverId);
		customerDriverRelationAdd.setDrivingLicense(req.getDrivingLicense());
		customerDriverRelationAdd.setCreateTime(new Date());
		customerDriverRelationAdd.setCreateBy(SecurityUtils.getNickname());
		customerDriverRelationAdd.setTelephone(req.getTelephone());
		customerDriverRelationMapper.insertCustomerDriverRelation(customerDriverRelationAdd);

		// 添加承运人信息
		ActualCarrierInfo actualCarrierInfoQuery = new ActualCarrierInfo();
		actualCarrierInfoQuery.setIdentityCard(req.getDrivingLicense());
		actualCarrierInfoQuery.setState(0);
		List<ActualCarrierInfo> actualCarrierInfos = actualCarrierInfoMapper.selectActualCarrierInfoList(actualCarrierInfoQuery);
		if (StringUtils.isEmpty(actualCarrierInfos)) {
			ActualCarrierInfo carrierInfoAdd = new ActualCarrierInfo();
			carrierInfoAdd.setId(TextUtil.getTimeSequenceID(5)); // 生成新的承运人ID
			carrierInfoAdd.setActualCarrierName(req.getDriverName());
			carrierInfoAdd.setIdentityCard(req.getDrivingLicense());
			carrierInfoAdd.setCreateClient(3); // 3 托运人
			carrierInfoAdd.setOperation(15); // 实时调度创建
			carrierInfoAdd.setIsComplete(1); // 信息不完整
			carrierInfoAdd.setInfoIsComplete(1); // 信息不完整
			carrierInfoAdd.setDriverId(newDriverId);
			carrierInfoAdd.setCreateBy(SecurityUtils.getNickname());
			carrierInfoAdd.setCreateTime(new Date());
			carrierInfoAdd.setUpdateBy(SecurityUtils.getNickname());
			carrierInfoAdd.setUpdateTime(new Date());
			carrierInfoAdd.setContactPhone(req.getTelephone());
			actualCarrierInfoMapper.insertActualCarrierInfo(carrierInfoAdd);
		} else {
			ActualCarrierInfo actualCarrierInfo = actualCarrierInfos.get(0);
			actualCarrierInfo.setDriverId(newDriverId);
			actualCarrierInfoMapper.updateActualCarrierInfo(actualCarrierInfo);
		}
	}

	public void addVehicleAndBindRelation(DispatchReq req) {
		// 新增车辆
		Vehicle vehicleAdd = new Vehicle();
		Long newVehicleId = TextUtil.getTimeSequenceID(5); // 生成新的车辆ID
		vehicleAdd.setId(newVehicleId);
		vehicleAdd.setVehicleNumber(req.getVehicleNumber());
		vehicleAdd.setVehiclePlateColorCode(req.getVehiclePlateColorCode());
		vehicleAdd.setVehicleType("");
		vehicleAdd.setIsComplete(1); // 车辆信息不完整
		vehicleAdd.setInfoIsComplete(1); // 车辆信息不完整
		vehicleAdd.setOperation(15); // 实时调度创建
		vehicleAdd.setCreateBy(SecurityUtils.getNickname());
		vehicleAdd.setCreateTime(new Date());
		vehicleService.insertVehicle(vehicleAdd);

		// 绑定客户与车辆的关联关系
		CustomerVehicleRelation customerVehicleRelationAdd = new CustomerVehicleRelation();
		customerVehicleRelationAdd.setCustomerId(SecurityUtils.getShipperId());
		customerVehicleRelationAdd.setVehicleId(newVehicleId);
		customerVehicleRelationAdd.setVehicleNumber(req.getVehicleNumber());
		customerVehicleRelationAdd.setVehiclePlateColorCode(req.getVehiclePlateColorCode());
		customerVehicleRelationAdd.setCreateBy(SecurityUtils.getNickname());
		customerVehicleRelationAdd.setCreateTime(new Date());
		customerVehicleRelationMapper.insertCustomerVehicleRelation(customerVehicleRelationAdd);
	}

	/**
	 * 获取与当前时间段冲突的时间范围，如果没有冲突则返回 null
	 */
	private Map.Entry<Date, Date> getConflictTime(Map<String, List<Map.Entry<Date, Date>>> map, String key, Date startTime, Date endTime) {
		if (!map.containsKey(key)) {
			return null;
		}
		for (Map.Entry<Date, Date> range : map.get(key)) {
			if (isOverlap(range.getKey(), range.getValue(), startTime, endTime)) {
				return range; // 返回冲突的时间段
			}
		}
		return null;
	}

	/**
	 * 判断两个时间段是否有交集
	 */
	private boolean isOverlap(Date start1, Date end1, Date start2, Date end2) {
		return !(end1.before(start2) || start1.after(end2));
	}

	public void validateTelephone(List<DispatchReq> dispatchReqList, List<DispatchError> errorList) {

	}

	public void validateordersTimeNew(List<DispatchReq> dispatchReqList, List<DispatchError> errorList) {
		Map<String, List<Map.Entry<Date, Date>>> vehicleMap = new HashMap<>();
		Map<String, List<Map.Entry<Date, Date>>> driverMap = new HashMap<>();
		Map<String, Set<String>> payeeTelephoneMap = new HashMap<>();
		Map<String, String> payeeNameMap = new HashMap<>();
		Map<String, List<Integer>> payeePhoneConflictDispatchIndices = new HashMap<>();
		Map<Integer, StringBuilder> dispatchErrorMap = new HashMap<>();

		for (int dispatchIndex = 0; dispatchIndex < dispatchReqList.size(); dispatchIndex++) {
			DispatchReq dispatchReq = dispatchReqList.get(dispatchIndex);
			Date startTime = DateUtils.strToDate(dispatchReq.getPlanStartTime());
			Date endTime = null;
			if (dispatchReq.getPlanEndTime() != null && !dispatchReq.getPlanEndTime().trim().isEmpty()) {
				endTime = DateUtils.strToDate(dispatchReq.getPlanEndTime());
			}

			if (endTime == null) {
				continue;
			}

			StringBuilder currentErrors = dispatchErrorMap.computeIfAbsent(dispatchIndex + 1, k -> new StringBuilder());

			// 校验车辆时间冲突
			Map.Entry<Date, Date> conflictVehicleTime = getConflictTime(vehicleMap, dispatchReq.getVehicleNumber(), startTime, endTime);
			if (conflictVehicleTime != null) {
				appendErrorMessage(currentErrors,
						String.format("planStartTime: %s, planEndTime: %s, message: 车辆[%s]在时间段[%s ~ %s]已有重复调度，请检查", DateUtils.dateToStr(startTime), DateUtils.dateToStr(endTime),
								dispatchReq.getVehicleNumber(), DateUtils.dateToStr(conflictVehicleTime.getKey()), DateUtils.dateToStr(conflictVehicleTime.getValue())));
			}

			// 校验司机时间冲突
			Map.Entry<Date, Date> conflictDriverTime = getConflictTime(driverMap, dispatchReq.getDrivingLicense(), startTime, endTime);
			if (conflictDriverTime != null) {
				appendErrorMessage(currentErrors,
						String.format("planStartTime: %s, planEndTime: %s, message: 司机[%s]在时间段[%s ~ %s]已有重复调度，请检查", DateUtils.dateToStr(startTime), DateUtils.dateToStr(endTime),
								dispatchReq.getDriverName(), DateUtils.dateToStr(conflictDriverTime.getKey()), DateUtils.dateToStr(conflictDriverTime.getValue())));
			}

			// 校验相同身份证号但不同手机号
			String payeeIdentity = dispatchReq.getPayeeIdentityCard();
			String payeeName = dispatchReq.getPayeeName();
			String telephone = dispatchReq.getTelephone();

			if (!payeeTelephoneMap.containsKey(payeeIdentity)) {
				payeeTelephoneMap.put(payeeIdentity, new HashSet<>());
				payeeNameMap.put(payeeIdentity, payeeName);
				payeePhoneConflictDispatchIndices.put(payeeIdentity, new ArrayList<>());
			}

			Set<String> telephoneSet = payeeTelephoneMap.get(payeeIdentity);

			if (!telephoneSet.contains(telephone)) {
				telephoneSet.add(telephone);
			}

			if (telephoneSet.size() > 1) {
				payeePhoneConflictDispatchIndices.get(payeeIdentity).add(dispatchIndex + 1);
			}

			vehicleMap.computeIfAbsent(dispatchReq.getVehicleNumber(), k -> new ArrayList<>()).add(new AbstractMap.SimpleEntry<>(startTime, endTime));
			driverMap.computeIfAbsent(dispatchReq.getDrivingLicense(), k -> new ArrayList<>()).add(new AbstractMap.SimpleEntry<>(startTime, endTime));
		}

		for (Map.Entry<String, List<Integer>> entry : payeePhoneConflictDispatchIndices.entrySet()) {
			List<Integer> conflictingDispatchIndices = entry.getValue();
			if (conflictingDispatchIndices.size() > 0) {
				StringBuilder phoneMismatchMessage = new StringBuilder();
				for (int i = 0; i < conflictingDispatchIndices.size(); i++) {
					if (i > 0) {
						phoneMismatchMessage.append("、");
					}
					phoneMismatchMessage.append(getOrdinalNumber(conflictingDispatchIndices.get(i)));
				}

				for (Integer dispatchIndex : conflictingDispatchIndices) {
					StringBuilder currentErrors = dispatchErrorMap.computeIfAbsent(dispatchIndex, k -> new StringBuilder());
					appendErrorMessage(currentErrors, String.format("payeeTelephone: %s, message: 手机号不一致，请确认", entry.getKey()));
				}
			}
		}

		for (Map.Entry<Integer, StringBuilder> entry : dispatchErrorMap.entrySet()) {
			Integer dispatchIndex = entry.getKey();
			StringBuilder errorMessages = entry.getValue();

			if (errorMessages.length() > 0) {
				String[] errorParts = errorMessages.toString().split(" \\| ");

				String fieldNames = Arrays.stream(errorParts).map(e -> {
							String[] splitByColon = e.split(":");
							if (splitByColon.length > 1) {
								String[] splitByComma = splitByColon[0].split("，");
								// 确保同时处理 planStartTime 和 planEndTime
								return Arrays.stream(splitByComma).map(String::trim) // 去除空格
										.distinct() // 确保字段唯一
										.collect(Collectors.joining("，"));
							}
							return "";
						}).distinct() // 确保整个字段集合唯一
						.collect(Collectors.joining("，"));

				String message = Arrays.stream(errorParts).map(e -> {
					String[] splitMessage = e.split(", message: ");
					return splitMessage.length > 1 ? splitMessage[1] : "";
				}).collect(Collectors.joining(" ； "));

				errorList.add(new DispatchError(dispatchIndex, fieldNames, message));
			}
		}
	}

	private void validatePayeePhoneConflicts(List<DispatchReq> dispatchReqList, Map<Integer, StringBuilder> dispatchErrorMap) {
		Map<String, Map<String, List<Integer>>> payeePhoneMap = new HashMap<>();

		for (int dispatchIndex = 0; dispatchIndex < dispatchReqList.size(); dispatchIndex++) {
			DispatchReq dispatchReq = dispatchReqList.get(dispatchIndex);
			String payeeIdentity = dispatchReq.getPayeeIdentityCard();
			String telephone = dispatchReq.getTelephone();

			payeePhoneMap.putIfAbsent(payeeIdentity, new HashMap<>());
			Map<String, List<Integer>> phoneMap = payeePhoneMap.get(payeeIdentity);

			phoneMap.putIfAbsent(telephone, new ArrayList<>());
			phoneMap.get(telephone).add(dispatchIndex + 1);
		}

		// 处理手机号冲突
		for (Map.Entry<String, Map<String, List<Integer>>> entry : payeePhoneMap.entrySet()) {
			Map<String, List<Integer>> phoneMap = entry.getValue();
			if (phoneMap.size() > 1) { // 说明同一身份证下有多个不同的手机号
				List<String> conflictMessages = new ArrayList<>();

				for (List<Integer> indices : phoneMap.values()) {
					conflictMessages.add(indices.stream().map(i -> "第" + i + "个调度").collect(Collectors.joining("、")));
				}

				String message = String.join("，", conflictMessages) + "的手机号不一致，请确认";

				// 为所有涉及的 dispatchIndex 添加错误信息
				phoneMap.values().stream().flatMap(List::stream).forEach(dispatchIndex -> {
					StringBuilder currentErrors = dispatchErrorMap.computeIfAbsent(dispatchIndex, k -> new StringBuilder());
					appendErrorMessage(currentErrors, "telephone: " + entry.getKey() + ", message: " + message);
				});
			}
		}
	}

	private void validateDuplicatePhoneNumbers(List<DispatchReq> dispatchReqList, Map<Integer, StringBuilder> dispatchErrorMap) {
		Map<String, List<Integer>> phoneDispatchMap = new HashMap<>();
		Map<String, Set<String>> phoneIdentityMap = new HashMap<>();

		for (int dispatchIndex = 0; dispatchIndex < dispatchReqList.size(); dispatchIndex++) {
			DispatchReq dispatchReq = dispatchReqList.get(dispatchIndex);
			String payeeIdentity = dispatchReq.getPayeeIdentityCard();
			String telephone = dispatchReq.getTelephone();

			phoneDispatchMap.putIfAbsent(telephone, new ArrayList<>());
			phoneDispatchMap.get(telephone).add(dispatchIndex + 1);

			phoneIdentityMap.putIfAbsent(telephone, new HashSet<>());
			phoneIdentityMap.get(telephone).add(payeeIdentity);
		}

		// 处理相同手机号但对应不同身份证的情况
		for (Map.Entry<String, Set<String>> entry : phoneIdentityMap.entrySet()) {
			String telephone = entry.getKey();
			Set<String> identitySet = entry.getValue();

			if (identitySet.size() > 1) { // 说明同一手机号对应了多个不同身份证
				List<Integer> conflictingDispatchIndices = phoneDispatchMap.get(telephone);
				String message = conflictingDispatchIndices.stream().map(i -> "第" + i + "个调度").collect(Collectors.joining("、")) + "的手机号不能相同，请确认";

				// 为所有涉及的 dispatchIndex 添加错误信息
				for (Integer dispatchIndex : conflictingDispatchIndices) {
					StringBuilder currentErrors = dispatchErrorMap.computeIfAbsent(dispatchIndex, k -> new StringBuilder());
					appendErrorMessage(currentErrors, "telephone: " + telephone + ", message: " + message);
				}
			}
		}
	}

	private void validateTimeConflicts(List<DispatchReq> dispatchReqList, Map<Integer, StringBuilder> dispatchErrorMap) {
		Map<String, List<Map.Entry<Date, Date>>> vehicleMap = new HashMap<>();
		Map<String, List<Map.Entry<Date, Date>>> driverMap = new HashMap<>();

		for (int dispatchIndex = 0; dispatchIndex < dispatchReqList.size(); dispatchIndex++) {
			DispatchReq dispatchReq = dispatchReqList.get(dispatchIndex);
			Date startTime = DateUtils.strToDate(dispatchReq.getPlanStartTime());
			Date endTime = null;
			if (dispatchReq.getPlanEndTime() != null && !dispatchReq.getPlanEndTime().trim().isEmpty()) {
				endTime = DateUtils.strToDate(dispatchReq.getPlanEndTime());
			}

			if (endTime == null) {
				continue;
			}

			StringBuilder currentErrors = dispatchErrorMap.computeIfAbsent(dispatchIndex + 1, k -> new StringBuilder());

			// 校验车辆时间冲突
			Map.Entry<Date, Date> conflictVehicleTime = getConflictTime(vehicleMap, dispatchReq.getVehicleNumber(), startTime, endTime);
			if (conflictVehicleTime != null) {
				appendErrorMessage(currentErrors,
						String.format("planStartTime: %s, planEndTime: %s, message: 车辆[%s]在时间段[%s ~ %s]已有重复调度，请检查", DateUtils.dateToStr(startTime), DateUtils.dateToStr(endTime),
								dispatchReq.getVehicleNumber(), DateUtils.dateToStr(conflictVehicleTime.getKey()), DateUtils.dateToStr(conflictVehicleTime.getValue())));
			}

			// 校验司机时间冲突
			Map.Entry<Date, Date> conflictDriverTime = getConflictTime(driverMap, dispatchReq.getDrivingLicense(), startTime, endTime);
			if (conflictDriverTime != null) {
				appendErrorMessage(currentErrors,
						String.format("planStartTime: %s, planEndTime: %s, message: 司机[%s]在时间段[%s ~ %s]已有重复调度，请检查", DateUtils.dateToStr(startTime), DateUtils.dateToStr(endTime),
								dispatchReq.getDriverName(), DateUtils.dateToStr(conflictDriverTime.getKey()), DateUtils.dateToStr(conflictDriverTime.getValue())));
			}

			// 记录车辆和司机的时间信息
			vehicleMap.computeIfAbsent(dispatchReq.getVehicleNumber(), k -> new ArrayList<>()).add(new AbstractMap.SimpleEntry<>(startTime, endTime));
			driverMap.computeIfAbsent(dispatchReq.getDrivingLicense(), k -> new ArrayList<>()).add(new AbstractMap.SimpleEntry<>(startTime, endTime));
		}
	}

	public void validateOrdersOtherInfo(List<DispatchReq> dispatchReqList, List<DispatchError> errorList) {
		Map<Integer, StringBuilder> dispatchErrorMap = new HashMap<>();

		// 校验车辆和司机的时间冲突
		validateTimeConflicts(dispatchReqList, dispatchErrorMap);

		// 校验身份证号和手机号冲突
		validatePayeePhoneConflicts(dispatchReqList, dispatchErrorMap);

		// 校验不同身份证相同手机号的冲突
		validateDuplicatePhoneNumbers(dispatchReqList, dispatchErrorMap);

		// 组装 errorList
		for (Map.Entry<Integer, StringBuilder> entry : dispatchErrorMap.entrySet()) {
			Integer dispatchIndex = entry.getKey();
			StringBuilder errorMessages = entry.getValue();

			if (errorMessages.length() > 0) {
				String[] errorParts = errorMessages.toString().split(" \\| ");

				String fieldNames = Arrays.stream(errorParts).map(e -> {
					String[] splitByColon = e.split(":");
					if (splitByColon.length > 1) {
						String[] splitByComma = splitByColon[0].split("，");
						return Arrays.stream(splitByComma).map(String::trim).distinct().collect(Collectors.joining("，"));
					}
					return "";
				}).distinct().collect(Collectors.joining("，"));

				String message = Arrays.stream(errorParts).map(e -> {
					String[] splitMessage = e.split(", message: ");
					return splitMessage.length > 1 ? splitMessage[1] : "";
				}).collect(Collectors.joining("；"));

				errorList.add(new DispatchError(dispatchIndex, fieldNames, message));
			}
		}
	}

	private void appendErrorMessage(StringBuilder currentErrors, String errorMessage) {
		if (currentErrors.length() > 0) {
			currentErrors.append(" | ");
		}
		currentErrors.append(errorMessage);
	}

	// 获取调度索引的序号表示（如：第一、第二）
	private String getOrdinalNumber(int index) {
		switch (index % 10) {
		case 1:
			return index + "st";
		case 2:
			return index + "nd";
		case 3:
			return index + "rd";
		default:
			return index + "th";
		}
	}

	public CommonResult validateordersTime(List<DispatchReq> dispatchReqList) {
		Map<String, List<Map.Entry<Date, Date>>> vehicleMap = new HashMap<>();
		Map<String, List<Map.Entry<Date, Date>>> driverMap = new HashMap<>();

		Map<String, Set<String>> payeeTelephoneMap = new HashMap<>(); // 存储身份证 -> 电话号集合
		Map<String, String> payeeNameMap = new HashMap<>(); // 存储身份证 -> 姓名

		for (DispatchReq dispatchReq : dispatchReqList) {
			Date startTime = DateUtils.strToDate(dispatchReq.getPlanStartTime());
			Date endTime = null;
			if (dispatchReq.getPlanEndTime() != null && !dispatchReq.getPlanEndTime().trim().isEmpty()) {
				endTime = DateUtils.strToDate(dispatchReq.getPlanEndTime());
			}

			// 如果 endTime 为空，跳过当前数据
			if (endTime == null) {
				continue;
			}

			Map.Entry<Date, Date> conflictVehicleTime = getConflictTime(vehicleMap, dispatchReq.getVehicleNumber(), startTime, endTime);
			if (conflictVehicleTime != null) {
				return CommonResult.requestError(String.format("车辆[%s]在时间段[%s ~ %s]已有重复调度，请检查", dispatchReq.getVehicleNumber(), DateUtils.dateToStr(conflictVehicleTime.getKey()),
						DateUtils.dateToStr(conflictVehicleTime.getValue())));
			}

			Map.Entry<Date, Date> conflictDriverTime = getConflictTime(driverMap, dispatchReq.getDrivingLicense(), startTime, endTime);
			if (conflictDriverTime != null) {
				return CommonResult.requestError(String.format("司机[%s]在时间段[%s ~ %s]已有重复调度，请检查", dispatchReq.getDriverName(), DateUtils.dateToStr(conflictDriverTime.getKey()),
						DateUtils.dateToStr(conflictDriverTime.getValue())));
			}

			// 校验相同身份证号但不同手机号
			String payeeIdentity = dispatchReq.getPayeeIdentityCard();
			String payeeName = dispatchReq.getPayeeName();
			String telephone = dispatchReq.getTelephone();

			if (!payeeTelephoneMap.containsKey(payeeIdentity)) {
				payeeTelephoneMap.put(payeeIdentity, new HashSet<>());
				payeeNameMap.put(payeeIdentity, payeeName);
			}
			payeeTelephoneMap.get(payeeIdentity).add(telephone);

			// 如果同一身份证号对应多个不同手机号，报错
			if (payeeTelephoneMap.get(payeeIdentity).size() > 1) {
				return CommonResult.requestError(String.format("收款人[%s]的手机号不一致，请确认", payeeNameMap.get(payeeIdentity)));
			}

			// 3. 记录当前数据
			vehicleMap.computeIfAbsent(dispatchReq.getVehicleNumber(), k -> new ArrayList<>()).add(new AbstractMap.SimpleEntry<>(startTime, endTime));
			driverMap.computeIfAbsent(dispatchReq.getDrivingLicense(), k -> new ArrayList<>()).add(new AbstractMap.SimpleEntry<>(startTime, endTime));
		}
		return CommonResult.success();
	}

	public void realNameCheck(DispatchReq req, int index, List<DispatchError> errorList) {
		BiConsumer<String, String> addError = (fieldName, message) -> {
			errorList.add(new DispatchError(index, fieldName, message)); // 直接添加，不拼接
		};
		// 1.二要素校验(司机)
		CommonResult<String> stringCommonResult = ptmCertificateCheckService.realNameAuthentication(req.getDriverName(), req.getDrivingLicense());
		if (CommonResult.isNotSuccess(stringCommonResult)) {
			addError.accept("driverName,drivingLicense", "司机姓名与驾驶证号不匹配，请确认信息是否正确");
		}
		// 2.二要素校验（收款人）
		CommonResult<String> PayeeCheckCommonResult = ptmCertificateCheckService.realNameAuthentication(req.getPayeeName(), req.getPayeeIdentityCard());
		if (CommonResult.isNotSuccess(PayeeCheckCommonResult)) {
			addError.accept("payeeName,payeeIdentityCard", "收款人姓名与身份证号不匹配，请确认信息是否正确");
		}
		// 3.收款人三要素校验
		if (StringUtils.isNotBlank(req.getPayeeName()) && StringUtils.isNotBlank(req.getPayeeIdentityCard()) && StringUtils.isNotBlank(req.getBankCardNo())) {
			CommonResult<String> result = ptmCertificateCheckService.checkTheThreeElementsOfUsers(req.getPayeeName(), req.getPayeeIdentityCard(), req.getBankCardNo());
			log.info("insertPayeeInfo，收款人三要素校验，payeeName:{}，identityCard:{}，bankCardNo:{}，result: {}", req.getPayeeName(), req.getPayeeIdentityCard(), req.getBankCardNo(),
					JSON.toJSONString(result));
			if (CommonResult.isNotSuccess(result)) {
				addError.accept("payeeName,payeeIdentityCard,bankCardNo", "卡添加异常：非本人的银行卡，请与收款人核实信息");
			}
		}
	}

	public List<DispatchError> validateOrdersList(List<DispatchReq> dispatchReqList) {
		List<DispatchError> errorList = new ArrayList<>();// 存储校验的错误信息
		// 1.校验时间重复，不同身份证手机号相同，以及相同身份证手机号不一致问题
		validateOrdersOtherInfo(dispatchReqList, errorList);
		// 2.字段校验
		for (int i = 0; i < dispatchReqList.size(); i++) {
			DispatchReq req = dispatchReqList.get(i);
			// 2.1必填字段基础校验
			waybillCommonService.validateOrdersBasicInfo(req, i + 1, errorList);
			// 2.2手机号重复性校验
			this.validatePhoneNew(req.getTelephone(), req.getPayeeIdentityCard(), i + 1, errorList);
			// 2.3二要素相关校验（司机，收款人，以及收款人三要素校验）
			realNameCheck(req, i + 1, errorList);
		}
		// 3.对校验出的错误信息做处理合并并返回
		if (!errorList.isEmpty()) {
			List<DispatchError> mergedErrorList = mergeDispatchErrors(errorList);
			return mergedErrorList;
		}
		return errorList;
	}

	public void vehicleDispose(List<DispatchReq> dispatchReqList) {
		for (int i = 0; i < dispatchReqList.size(); i++) {
			DispatchReq req = dispatchReqList.get(i);
			// 车辆相关逻辑处理
			Vehicle vehicleQuery = new Vehicle();
			vehicleQuery.setVehicleNumber(req.getVehicleNumber());
			vehicleQuery.setVehiclePlateColorCode(req.getVehiclePlateColorCode());
			vehicleQuery.setState(0);
			// 先查询运力库中是否有该车
			List<Vehicle> existVehicles = vehicleService.selectVehicleList(vehicleQuery);
			if (StringUtils.isNotEmpty(existVehicles)) {
				Vehicle existVehicle = existVehicles.get(0);
				// 查询车辆和托运人的关联关系表
				CustomerVehicleRelation customerVehicleRelation = customerVehicleRelationMapper.selectCustomerVehicleRelationByCustomerId(SecurityUtils.getShipperId(), existVehicle.getId());
				if (null == customerVehicleRelation) {
					// 运力库有车辆，托运人没有，直接绑定关联关系
					CustomerVehicleRelation customerVehicleRelationAdd = new CustomerVehicleRelation();
					customerVehicleRelationAdd.setCustomerId(SecurityUtils.getShipperId());
					customerVehicleRelationAdd.setVehicleId(existVehicle.getId());
					customerVehicleRelationAdd.setVehicleNumber(existVehicle.getVehicleNumber());
					customerVehicleRelationAdd.setVehiclePlateColorCode(existVehicle.getVehiclePlateColorCode());
					customerVehicleRelationAdd.setCreateBy(SecurityUtils.getNickname());
					customerVehicleRelationAdd.setCreateTime(new Date());
					customerVehicleRelationMapper.insertCustomerVehicleRelation(customerVehicleRelationAdd);
				}
			} else {
				// 运力库没有数据，新增车辆，并绑定关联关系
				this.addVehicleAndBindRelation(req);
			}
		}
	}

	public void telePhoneDispose(List<DispatchReq> dispatchReqList) {
		for (DispatchReq req : dispatchReqList) {
			// 修改承运人表手机号
			ActualCarrierInfo actualCarrierInfoQuery = new ActualCarrierInfo();
			actualCarrierInfoQuery.setIdentityCard(req.getDrivingLicense());
			actualCarrierInfoQuery.setState(0);
			List<ActualCarrierInfo> actualCarrierInfos = actualCarrierInfoMapper.selectActualCarrierInfoList(actualCarrierInfoQuery);
			if (StringUtils.isNotEmpty(actualCarrierInfos)) {
				ActualCarrierInfo actualCarrierInfo = actualCarrierInfos.get(0);
				actualCarrierInfo.setContactPhone(req.getTelephone());
				actualCarrierInfoMapper.updateActualCarrierInfo(actualCarrierInfo);
			}

			// 查询当前是否有司机身份
			Driver driverQuery = new Driver();
			driverQuery.setDrivingLicense(req.getPayeeIdentityCard());
			driverQuery.setState(0);
			// 先查询托运人当前托运人是否有该司机
			List<Driver> existDrivers = driverService.selectDriverList(driverQuery);// 运力库的数据
			if (StringUtils.isNotEmpty(existDrivers)) {
				Driver existDriver = existDrivers.get(0);
				// 查询托运人司机关联关系表
				CustomerDriverRelation customerDriverRelation = customerDriverRelationMapper.selectCustomerDriverRelationByCustomerId(SecurityUtils.getShipperId(), existDriver.getId());
				if (null != customerDriverRelation) {
					customerDriverRelation.setTelephone(req.getTelephone());
					// 修改关联关系里的手机号
					customerDriverRelationMapper.updateCustomerDriverRelation(customerDriverRelation);
				}
			}
			// 查询是否有辅助员身份
			AuxiliaryStaffInfo auxiliaryInfoQuery = new AuxiliaryStaffInfo();
			auxiliaryInfoQuery.setIdentityCard(req.getPayeeIdentityCard());
			auxiliaryInfoQuery.setState(0);
			List<AuxiliaryStaffInfo> auxiliaryStaffInfos = auxiliaryStaffInfoMapper.selectAuxiliaryStaffInfoList(auxiliaryInfoQuery);
			if (StringUtils.isNotEmpty(auxiliaryStaffInfos)) {
				AuxiliaryStaffInfo auxiliaryStaffInfo = auxiliaryStaffInfos.get(0);
				CustomerAuxiliaryRelation customerAuxiliaryRelationQuery = new CustomerAuxiliaryRelation();
				customerAuxiliaryRelationQuery.setAuxiliaryStaffId(auxiliaryStaffInfo.getId());
				customerAuxiliaryRelationQuery.setCustomerId(SecurityUtils.getShipperId());
				List<CustomerAuxiliaryRelation> customerAuxiliaryRelations = customerAuxiliaryRelationMapper.selectCustomerAuxiliaryRelationList(customerAuxiliaryRelationQuery);
				if (StringUtils.isNotEmpty(customerAuxiliaryRelations)) {
					CustomerAuxiliaryRelation customerAuxiliaryRelation = customerAuxiliaryRelations.get(0);
					customerAuxiliaryRelation.setAuxiliaryStaffContactPhone(req.getTelephone());
					customerAuxiliaryRelationMapper.updateCustomerAuxiliaryRelation(customerAuxiliaryRelation);
				}
			}

			// 查询是否有车队长身份
			CarCaptainInfo captainInfoQuery = new CarCaptainInfo();
			captainInfoQuery.setIdentityCard(req.getPayeeIdentityCard());
			captainInfoQuery.setState(0);
			List<CarCaptainInfo> carCaptainInfos = carCaptainInfoMapper.selectCarCaptainInfoList(captainInfoQuery);
			if (StringUtils.isNotEmpty(carCaptainInfos)) {
				CarCaptainInfo carCaptainInfo = carCaptainInfos.get(0);
				// 查询关联关系
				CustomerCaptainRelation customerCaptainRelationQuery = new CustomerCaptainRelation();
				customerCaptainRelationQuery.setCarCaptainId(carCaptainInfo.getId());
				customerCaptainRelationQuery.setCustomerId(SecurityUtils.getShipperId());
				List<CustomerCaptainRelation> customerCaptainRelations = customerCaptainRelationMapper.selectCustomerCaptainRelationList(customerCaptainRelationQuery);
				if (StringUtils.isNotEmpty(customerCaptainRelations)) {
					CustomerCaptainRelation customerCaptainRelation = customerCaptainRelations.get(0);
					customerCaptainRelation.setCarCaptainContactPhone(req.getTelephone());
					customerCaptainRelationMapper.updateCustomerCaptainRelation(customerCaptainRelation);
				}
			}
		}
	}

	public void driverDispose(List<DispatchReq> dispatchReqList) {
		for (int i = 0; i < dispatchReqList.size(); i++) {
			DispatchReq req = dispatchReqList.get(i);

			Driver driverQuery = new Driver();
			driverQuery.setDriverName(req.getDriverName());
			driverQuery.setDrivingLicense(req.getDrivingLicense());
			driverQuery.setState(0);
			// 先查询托运人当前托运人是否有该司机
			List<Driver> existDrivers = driverService.selectDriverList(driverQuery);// 运力库的数据
			if (StringUtils.isNotEmpty(existDrivers)) {
				Driver existDriver = existDrivers.get(0);
				// 查询托运人司机关联关系表
				CustomerDriverRelation customerDriverRelation = customerDriverRelationMapper.selectCustomerDriverRelationByCustomerId(SecurityUtils.getShipperId(), existDriver.getId());
				if (null != customerDriverRelation) {
					// 当前托运人有该司机
					if (req.getPayeeType() == 1) {
						customerDriverRelation.setTelephone(req.getTelephone());
						// 修改关联关系里的手机号
						customerDriverRelationMapper.updateCustomerDriverRelation(customerDriverRelation);
						ActualCarrierInfo actualCarrierInfoQuery = new ActualCarrierInfo();
						actualCarrierInfoQuery.setIdentityCard(req.getDrivingLicense());
						actualCarrierInfoQuery.setState(0);
						List<ActualCarrierInfo> actualCarrierInfos = actualCarrierInfoMapper.selectActualCarrierInfoList(actualCarrierInfoQuery);
						if (StringUtils.isNotEmpty(actualCarrierInfos)) {
							ActualCarrierInfo actualCarrierInfo = actualCarrierInfos.get(0);
							actualCarrierInfo.setContactPhone(req.getTelephone());
							actualCarrierInfoMapper.updateActualCarrierInfo(actualCarrierInfo);
						}
					}
				} else {
					// 运力库有该司机，托运人没有，绑定关联关系
					CustomerDriverRelation customerDriverRelationAdd = new CustomerDriverRelation();
					customerDriverRelationAdd.setCustomerId(SecurityUtils.getShipperId());
					customerDriverRelationAdd.setDriverId(existDriver.getId());
					customerDriverRelationAdd.setDrivingLicense(req.getDrivingLicense());
					if (req.getPayeeType() == 1) {
						customerDriverRelationAdd.setTelephone(req.getTelephone());
						ActualCarrierInfo actualCarrierInfoQuery = new ActualCarrierInfo();
						actualCarrierInfoQuery.setIdentityCard(req.getDrivingLicense());
						actualCarrierInfoQuery.setState(0);
						List<ActualCarrierInfo> actualCarrierInfos = actualCarrierInfoMapper.selectActualCarrierInfoList(actualCarrierInfoQuery);
						if (StringUtils.isNotEmpty(actualCarrierInfos)) {
							ActualCarrierInfo actualCarrierInfo = actualCarrierInfos.get(0);
							actualCarrierInfo.setContactPhone(req.getTelephone());
							actualCarrierInfoMapper.updateActualCarrierInfo(actualCarrierInfo);
						}
					} else {
						customerDriverRelationAdd.setTelephone(existDriver.getTelephone() != null ? existDriver.getTelephone() : null);
					}
					customerDriverRelationAdd.setCreateTime(new Date());
					customerDriverRelationAdd.setCreateBy(SecurityUtils.getNickname());
					customerDriverRelationMapper.insertCustomerDriverRelation(customerDriverRelationAdd);
				}
			} else {
				// 运力库没有数据,新增司机，并绑定关联关系
				this.addDriverAndCarrier(req);
			}
		}
	}

	private List<DispatchError> openAccount(List<PayeeInfo> list) {
		List<DispatchError> errorList = new ArrayList<>();// 存储校验的错误信息
		// for (int i = 0; i < list.size(); i++) {
		// try {
		// PayeeInfo payeeInfo = list.get(i);
		// huaXiaService.openPayeeInfoAccount(payeeInfo);
		// huaXiaService.bindBankCardOperating(payeeInfo, 1);
		// } catch (Exception e) {
		// log.error("银行卡开户失败：{}", ServiceException.getExceptionInfo(e));
		// // TODO 更好的方式是：需要开户或绑卡的接口提供单独的异常类型，或抛弃抛异常的方式而使用返回值的方式来处理结果。
		// // 信息中包含中文则一般可以认为是我们手动抛出的异常
		// String message = StringUtils.containsChineseCharacter(e.getMessage()) ? "银行卡开户失败：" + e.getMessage() : "银行卡开户失败，请稍后再试";
		// addErrorToList(errorList, i + 1, "payeeName,payeeIdentityCard", message);
		// }
		// }
		return errorList;
	}

	public List<DispatchError> payeeDispose(List<DispatchReq> dispatchReqList, List<PayeeInfo> needOpenAccountList) {
		List<DispatchError> errorList = new ArrayList<>();// 存储校验的错误信息
		for (int i = 0; i < dispatchReqList.size(); i++) {
			DispatchReq req = dispatchReqList.get(i);
			// 先查询收款人是否有对应的运力身份
			List<PayeeRes> list = new ArrayList<>();
			if (req.getPayeeType() == 1) {
				// 司机
				list = driverMapperEx.queryDriverInfo(req.getPayeeIdentityCard(), SecurityUtils.getShipperId());
			} else if (req.getPayeeType() == 2) {
				AuxiliaryStaffInfo queryAuxiliary = new AuxiliaryStaffInfo();
				queryAuxiliary.setIdentityCard(req.getPayeeIdentityCard());
				List<AuxiliaryStaffInfo> auxiliaryStaffInfos = auxiliaryStaffInfoMapper.selectAuxiliaryStaffInfoList(queryAuxiliary);
				if (!auxiliaryStaffInfos.isEmpty()) {
					addErrorToList(errorList, i + 1, "payeeName,payeeIdentityCard", "收款人已存在辅助员身份，不可变更成为车队长");
				}
				// 注册车队长
				this.registerCarCaptain(req);
				// 再查一遍
				list = carCaptainInfoMapper.queryCaptainInfo(req.getPayeeIdentityCard(), SecurityUtils.getShipperId());
			} else if (req.getPayeeType() == 3) {
				// 校验是否存在车队长信息
				CarCaptainInfo queryCaptain = new CarCaptainInfo();
				queryCaptain.setIdentityCard(req.getPayeeIdentityCard());
				List<CarCaptainInfo> carCaptainInfos = carCaptainInfoMapper.selectCarCaptainInfoList(queryCaptain);
				if (!carCaptainInfos.isEmpty()) {
					addErrorToList(errorList, i + 1, "payeeName,payeeIdentityCard", "收款人已存在车队长身份，不可变更成为辅助员");
				}
				// 注册辅助员
				this.registerAuxiliaryStaff(req);
				list = auxiliaryStaffInfoMapper.queryAuxiliaryInfo(req.getPayeeIdentityCard(), SecurityUtils.getShipperId());
			}

			if (StringUtils.isNotBlank(req.getBankCardNo()) && StringUtils.isNotEmpty(list)) {
				// 有对应身份
				PayeeInfo payeeInfoQuery = new PayeeInfo();
				payeeInfoQuery.setPayeeName(req.getPayeeName());
				payeeInfoQuery.setIdentityCard(req.getPayeeIdentityCard());
				payeeInfoQuery.setBankCardNo(req.getBankCardNo());
				payeeInfoQuery.setState(0);

				List<PayeeInfo> payeeInfos = payeeInfoMapper.selectPayeeInfoList(payeeInfoQuery);
				if (StringUtils.isNotEmpty(payeeInfos)) {
					PayeeInfo payeeInfo = payeeInfos.get(0);
					// 检查托运人是否已有关联关系
					PayeeInfo payeeInfoRelation = customerPayeeRelationMapper.selectCustomerPayeeInfo(SecurityUtils.getShipperId(), payeeInfo.getId());
					if (payeeInfoRelation == null) {
						// 绑定关联关系
						CustomerPayeeRelation customerPayeeRelationAdd = new CustomerPayeeRelation();
						customerPayeeRelationAdd.setPayeeId(payeeInfo.getId());
						customerPayeeRelationAdd.setCustomerId(SecurityUtils.getShipperId());
						customerPayeeRelationAdd.setActualIdentityCard(payeeInfo.getIdentityCard());
						customerPayeeRelationAdd.setPayeeBankCardNo(payeeInfo.getBankCardNo());
						customerPayeeRelationAdd.setCreateBy(SecurityUtils.getNickname());
						customerPayeeRelationAdd.setCreateTime(new Date());
						customerPayeeRelationMapper.insertCustomerPayeeRelation(customerPayeeRelationAdd);
					}
				} else {
					// 添加银行卡信息
					PayBank payBankByCardNo = payeeInfoService.getPayBankByCardNo(req.getBankCardNo());
					PayeeInfo payeeInfoAdd = new PayeeInfo();
					payeeInfoAdd.setSystemType(BusinessConstants.PAYEEINFO_CUSTOMER_PC);
					payeeInfoAdd.setSourceType(BusinessConstants.SOURCE_PC_10);
					payeeInfoAdd.setBankName(payBankByCardNo.getBankName());
					payeeInfoAdd.setBankCardNo(req.getBankCardNo());
					payeeInfoAdd.setIdentityCard(req.getPayeeIdentityCard());
					payeeInfoAdd.setPayeeName(req.getPayeeName());
					payeeInfoAdd.setCreateBy(SecurityUtils.getNickname());
					payeeInfoAdd.setUpdateBy(SecurityUtils.getNickname());
					payeeInfoAdd.setCheckResult(1);
					payeeInfoAdd.setState(0);
					payeeInfoAdd.setIsOther(0);
					payeeInfoAdd.setId(TextUtil.getTimeSequenceID(5));
					if (StringUtils.isNotBlank(req.getTelephone())) {
						payeeInfoAdd.setBankMobile(req.getTelephone());
					}
					// 根据银行卡号查询银行联号
					CommonResult<PayBank> bank = payeeInfoService.getBankByCardNo(req.getBankCardNo());
					if (CommonResult.isNotSuccess(bank)) {
						addErrorToList(errorList, i + 1, "bankCardNo", "银行联行号查询失败");
					} else {
						payeeInfoAdd.setBankName(bank.getData().getBankName());
						payeeInfoAdd.setBankCode(bank.getData().getBankUnionNumber());
					}
					payeeInfoMapper.insertPayeeInfo(payeeInfoAdd);
					needOpenAccountList.add(payeeInfoAdd);
				}
			}
		}
		return errorList;
	}

	public void orderDispose(List<DispatchReq> dispatchReqList) {
		List<Waybill> waybillAddList = new ArrayList<>();
		List<WaybillSlave> waybillSlaveAddList = new ArrayList<>();
		for (int i = 0; i < dispatchReqList.size(); i++) {
			Waybill waybillAdd = new Waybill();
			waybillAdd.setResource(WaybillSource.DISPATCH.val());
			waybillAdd.setShippingNoteNumber(CodeUtil.generateWaybillCode());
			Long waybillId = TextUtil.getTimeSequenceID(5);
			waybillAdd.setId(waybillId);
			DispatchReq req = dispatchReqList.get(i);

			// 进行数据组装
			// 司机相关信息
			Driver driverReq = new Driver();
			driverReq.setDriverName(req.getDriverName());
			driverReq.setDrivingLicense(req.getDrivingLicense());
			driverReq.setState(0);
			List<Driver> existDriverList = driverService.selectDriverList(driverReq);
			if (StringUtils.isNotEmpty(existDriverList)) {
				Driver existDriver = existDriverList.get(0);
				waybillAdd.setDriverId(existDriver.getId());
				waybillAdd.setDriverName(existDriver.getDriverName());
				waybillAdd.setDrivingLicense(existDriver.getDrivingLicense());
				waybillAdd.setTelephone(existDriver.getTelephone());
				waybillAdd.setDriverInfoIsComplete(existDriver.getInfoIsComplete());
			}
			// 车辆相关信息
			Vehicle vehicleReq = new Vehicle();
			vehicleReq.setVehicleNumber(req.getVehicleNumber());
			vehicleReq.setVehiclePlateColorCode(req.getVehiclePlateColorCode());
			vehicleReq.setState(0);
			List<Vehicle> existVehicleList = vehicleService.selectVehicleList(vehicleReq);
			if (StringUtils.isNotEmpty(existVehicleList)) {
				Vehicle existVehicle = existVehicleList.get(0);
				waybillAdd.setVehicleId(existVehicle.getId());
				waybillAdd.setVehicleNumber(existVehicle.getVehicleNumber());
				waybillAdd.setVehiclePlateColorCode(existVehicle.getVehiclePlateColorCode());
				waybillAdd.setVehicleInfoIsComplete(existVehicle.getInfoIsComplete());
			}
			// 实际承运人
			ActualCarrierInfo actualCarrierInfoQuery = new ActualCarrierInfo();
			actualCarrierInfoQuery.setIdentityCard(req.getPayeeIdentityCard());
			actualCarrierInfoQuery.setState(0);
			List<ActualCarrierInfo> actualCarrierInfos = actualCarrierInfoMapper.selectActualCarrierInfoList(actualCarrierInfoQuery);
			if (StringUtils.isNotEmpty(actualCarrierInfos)) {
				ActualCarrierInfo actualCarrierInfo = actualCarrierInfos.get(0);
				waybillAdd.setActualCarrierId(actualCarrierInfo.getId());
				waybillAdd.setActualCarrierName(actualCarrierInfo.getActualCarrierName());
				waybillAdd.setActualCarrierIdentityCard(actualCarrierInfo.getIdentityCard());
				waybillAdd.setActualCarrierInfoIsComplete(actualCarrierInfo.getInfoIsComplete());
				waybillAdd.setActualCarrierContactPhone(actualCarrierInfo.getContactPhone());
				waybillAdd.setActualCarrierInfoIsComplete(actualCarrierInfo.getInfoIsComplete());
			}
			if (2 == req.getPayeeType()) {
				CarCaptainInfo carCaptainInfoQuery = new CarCaptainInfo();
				carCaptainInfoQuery.setIdentityCard(req.getPayeeIdentityCard());
				carCaptainInfoQuery.setState(0);
				List<CarCaptainInfo> carCaptainInfos = carCaptainInfoMapper.selectCarCaptainInfoList(carCaptainInfoQuery);
				if (StringUtils.isNotEmpty(carCaptainInfos)) {
					CarCaptainInfo carCaptainInfo = carCaptainInfos.get(0);
					waybillAdd.setCarCaptainId(carCaptainInfo.getId());
					waybillAdd.setCarCaptainName(carCaptainInfo.getCarCaptainName());
					waybillAdd.setCarCaptainIdentityCard(carCaptainInfo.getIdentityCard());
				}
			} else if (3 == req.getPayeeType()) {
				AuxiliaryStaffInfo auxiliaryStaffInfoQuery = new AuxiliaryStaffInfo();
				auxiliaryStaffInfoQuery.setIdentityCard(req.getPayeeIdentityCard());
				auxiliaryStaffInfoQuery.setState(0);
				List<AuxiliaryStaffInfo> auxiliaryStaffInfos = auxiliaryStaffInfoMapper.selectAuxiliaryStaffInfoList(auxiliaryStaffInfoQuery);
				if (StringUtils.isNotEmpty(auxiliaryStaffInfos)) {
					AuxiliaryStaffInfo auxiliaryStaffInfo = auxiliaryStaffInfos.get(0);
					waybillAdd.setAuxiliaryStaffId(auxiliaryStaffInfo.getId());
					waybillAdd.setAuxiliaryStaffName(auxiliaryStaffInfo.getAuxiliaryStaffName());
					waybillAdd.setAuxiliaryStaffIdentityCard(auxiliaryStaffInfo.getIdentityCard());
				}
			}
			// 收款人信息
			PayeeInfo payeeInfoQuery = new PayeeInfo();
			payeeInfoQuery.setPayeeName(req.getPayeeName());
			payeeInfoQuery.setIdentityCard(req.getPayeeIdentityCard());
			payeeInfoQuery.setBankCardNo(req.getBankCardNo());
			payeeInfoQuery.setState(0);
			List<PayeeInfo> payeeInfos = payeeInfoMapper.selectPayeeInfoList(payeeInfoQuery);
			if (StringUtils.isNotEmpty(payeeInfos)) {
				PayeeInfo payeeInfo = payeeInfos.get(0);
				waybillAdd.setPayeeId(payeeInfo.getId());
			}
			waybillAdd.setPayeeName(req.getPayeeName());
			waybillAdd.setPayeeBankCardNo(req.getBankCardNo());

			// 处理订单其他数据，插入运单主表
			disposeOrderInfo(req, waybillAdd);
			waybillAddList.add(waybillAdd);
			// 处理子表信息
			WaybillSlave waybillSlave = new WaybillSlave();
			waybillSlave.setId(waybillId);
			waybillSlave.setRemark("");
			waybillSlave.setPolicyType(req.getPolicyType());
			waybillSlaveAddList.add(waybillSlave);
		}
		// 插入运单数据
		waybillMapper.insertWaybills(waybillAddList);
		waybillSlaveMapper.insertWaybillSlaves(waybillSlaveAddList);
	}

	public List<DispatchError> auxiliaryAgencyCheck(List<DispatchReq> dispatchReqList) {
		List<DispatchError> errorList = new ArrayList<>();// 存储校验的错误信息
		for (int i = 0; i < dispatchReqList.size(); i++) {
			DispatchReq req = dispatchReqList.get(i);
			if (req.getPayeeType() == 3) {
				// 辅助员收款时需要判断辅助员是否签约了
				AuxiliaryStaffInfo auxiliaryStaffInfoQuery = new AuxiliaryStaffInfo();
				auxiliaryStaffInfoQuery.setIdentityCard(req.getPayeeIdentityCard());
				auxiliaryStaffInfoQuery.setState(0);
				List<AuxiliaryStaffInfo> auxiliaryStaffInfos = auxiliaryStaffInfoMapper.selectAuxiliaryStaffInfoList(auxiliaryStaffInfoQuery);
				if (StringUtils.isNotEmpty(auxiliaryStaffInfos)) {
					AuxiliaryStaffInfo auxiliaryStaffInfo = auxiliaryStaffInfos.get(0);
					// 如果是善道的业务，则进行辅助员签约判断，只对2025-02-27 12:00:00后创建的运单进行判断
					Date yesterdayEnd = DateUtil.parse("2025-02-27 12:00:00", "yyyy-MM-dd HH:mm:ss");
					Date orderCreateTime = new Date();
					FrameworkContract frameworkContract = frameworkContractService.selectFrameworkContractById(req.getContractId());
					if (orderCreateTime.after(yesterdayEnd)) {
						if (!auxiliaryStaffInfoService.validateShandaoRelationIsOk(frameworkContract.getFreightForwarderId(), null, auxiliaryStaffInfo.getId())) {
							addErrorToList(errorList, i + 1, "payeeName,payeeIdentityCard", "关联辅助员未与平台签约，无法接单，请联系平台");
						}
					}
				}
			}
		}
		return errorList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public CommonResult addDispatchOrder(List<DispatchReq> dispatchReqList) {
		// 1.数据校验，错误直接返回错误
		List<DispatchError> errorList = validateOrdersList(dispatchReqList);
		if (!errorList.isEmpty()) {
			return CommonResult.error("校验失败", errorList);
		}

		// 2.收款人逻辑处理（对应辅助员，车队长身份）
		List<PayeeInfo> needOpenAccountList = new ArrayList<>();// 需要添加的收款账户
		List<DispatchError> disposeError = payeeDispose(dispatchReqList, needOpenAccountList);
		// 校验辅助员是否善道签约
		List<DispatchError> auxiliartAgencyError = auxiliaryAgencyCheck(dispatchReqList);

		if (StringUtils.isNotEmpty(disposeError) || StringUtils.isNotEmpty(auxiliartAgencyError)) {
			// 合并错误信息
			List<DispatchError> dispatchErrors = mergeDispatchErrors(disposeError, auxiliartAgencyError);
			// 回滚
			rollbackDatabaseChanges();
			// 合并错误并返回
			return CommonResult.error("校验失败", dispatchErrors);
		}
		// 3.司机处理
		driverDispose(dispatchReqList);
		// 4.车辆处理
		vehicleDispose(dispatchReqList);
		// 5修改手机号
		telePhoneDispose(dispatchReqList);
		// 6.组装订单数据，插入运单表
		orderDispose(dispatchReqList);
		// 7.开户操作（4.5.10优化，开户挪到申请付款）
		// List<DispatchError> dispatchErrors = openAccount(needOpenAccountList);
		// if (!dispatchErrors.isEmpty()) {
		// //回滚
		// rollbackDatabaseChanges();
		// return CommonResult.error("开户失败", dispatchErrors);
		// }
		return CommonResult.success();
	}

	public static List<DispatchError> mergeDispatchErrors(List<DispatchError> list1, List<DispatchError> list2) {
		Map<Integer, DispatchError> errorMap = new HashMap<>();

		// 处理空列表
		List<DispatchError> allErrors = new ArrayList<>();
		if (list1 != null) {
			allErrors.addAll(list1);
		}
		if (list2 != null) {
			allErrors.addAll(list2);
		}

		for (DispatchError error : allErrors) {
			int index = error.getDispatchIndex();
			errorMap.merge(index, error, (existing, newError) -> {
				existing.setFieldNames(existing.getFieldNames() + "," + newError.getFieldNames());
				existing.setMessage(cleanMessage(existing.getMessage() + ";" + newError.getMessage()));
				return existing;
			});
		}

		return new ArrayList<>(errorMap.values());
	}

	// 清理 message 里的相邻双分号
	private static String cleanMessage(String message) {
		return message.replaceAll(";{2,}", ";");
	}

	private Waybill disposeOrderInfo(DispatchReq req, Waybill waybill) {
		// 其他字段数据
		waybill.setResource(16);
		waybill.setMakeCodeId(req.getMakeCodeId());
		waybill.setFrameworkContractId(req.getContractId());
		waybill.setDespatchActualDateTime(DateUtils.strToDate(req.getPlanStartTime()));
		waybill.setGoodsReceiptDateTime(DateUtils.strToDate(req.getPlanEndTime()));
		waybill.setBillDate(DateUtils.strToDate(req.getBillDateTime(), DateUtils.YYMMDD));
		waybill.setFeeAmount(req.getFeeAmount());
		waybill.setFeeUnit(req.getFeeUnit());
		waybill.setPayType(req.getPayType());
		waybill.setPayeeType(req.getPayeeType());
		waybill.setPayFare(req.getPayFare());
		waybill.setPrepayMoney(req.getPrepayMoney());
		waybill.setArriveMoney(req.getArriveMoney());
		waybill.setReceiptMoney(req.getReceiptMoney());
		waybill.setStatus(0);// 订单
		waybill.setCustomerId(SecurityUtils.getShipperId());
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(SecurityUtils.getShipperId());
		waybill.setCustomerName(customerInfo.getCustomerName());
		MakeCode makeCode = makeCodeMapper.selectMakeCodeById(req.getMakeCodeId());
		waybill.setConsignorId(makeCode.getConsignorId());
		waybill.setConsigneeId(makeCode.getConsigneeId());
		waybill.setDescriptionOfGoods(makeCode.getCargoName());
		FrameworkContract frameworkContract = frameworkContractService.selectFrameworkContractById(req.getContractId());
		waybill.setFrameworkContractCode(frameworkContract.getContractCode());
		waybill.setFrameworkContractName(frameworkContract.getContractName());
		waybill.setOrderCreateTime(new Date());
		return waybill;
	}

	@Override
	public TableInfo<WaybillTableRes> selectOrderList(OrderQuery orderQuery) {
		List<WaybillTableRes> list = waybillMapperEx.selectShipperOrderList(orderQuery);
		// 根据收款人类型查询对应的收款信息
		for (WaybillTableRes waybillTableRes : list) {
			if (1 == waybillTableRes.getPayeeType()) {
				waybillTableRes.setPayeeInfoIsComplete(waybillTableRes.getDriverInfoIsComplete());
				waybillTableRes.setIdentityCard(waybillTableRes.getDrivingLicense());
			} else if (2 == waybillTableRes.getPayeeType()) {
				// 查询车队长信息
				CarCaptainInfo carCaptainInfo = carCaptainInfoMapper.selectCarCaptainInfoById(waybillTableRes.getCarCaptainId());
				if (null != carCaptainInfo) {
					waybillTableRes.setPayeeInfoIsComplete(carCaptainInfo.getInfoIsComplete());
					waybillTableRes.setIdentityCard(carCaptainInfo.getIdentityCard());
				}
			} else if (3 == waybillTableRes.getPayeeType()) {
				// 查询辅助员信息
				AuxiliaryStaffInfo auxiliaryStaffInfo = auxiliaryStaffInfoMapper.selectAuxiliaryStaffInfoById(waybillTableRes.getAuxiliaryStaffId());
				if (null != auxiliaryStaffInfo) {
					waybillTableRes.setPayeeInfoIsComplete(auxiliaryStaffInfo.getInfoIsComplete());
					waybillTableRes.setIdentityCard(auxiliaryStaffInfo.getIdentityCard());
				}
			}
		}

		int count = waybillMapperEx.selectShipperOrderListCount(orderQuery);
		return new TableInfo<>(list, count);
	}

	@Override
	public AjaxResult shipperOrderListCount(OrderQuery orderQuery) {
		return null;
	}

	@Override
	public AjaxResult removeOrderByIds(Long[] ids) {
		if (StringUtils.isEmpty(ids)) {
			return AjaxResult.requestError("参数错误");
		}
		List<Long> waybillIds = Arrays.stream(ids).filter(id -> id > 0).collect(Collectors.toList());
		if (waybillIds.isEmpty()) {
			return AjaxResult.requestError("参数错误");
		}
		List<WaybillRes> waybillList = waybillMapperEx.selectWaybillListByWaybillIds(Arrays.asList(ids));
		if (waybillList.stream().anyMatch(waybill -> waybill.getStatus() != 0)) {
			return AjaxResult.requestError("存在非订单数据，不可删除");
		}
		// 校验完成，批量删除订单
		int count = waybillMapperEx.removeWaybillByIds(ids, SecurityUtils.getNickname());

		// 拼接运单号
		StringJoiner waybillCode = new StringJoiner(" ");
		waybillList.stream().map(WaybillRes::getShippingNoteNumber).forEach(waybillCode::add);
		String actionName = "删除订单(运单ID：" + StringUtils.join(ids, ",") + "，订单号：" + waybillCode + ")";

		sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_ORDER, BusinessConstants.ACTION_TYPE_DELETE, StringUtils.substring(actionName, 0, 5000), waybillCode.toString());
		return AjaxResult.success();
	}

	@Override
	public CommonResult orderToWaybill(WaybillUpdateRequest req) {
		BigDecimal receiveFare = req.getReceiveFare();
		// 1、校验参数
		req.setIsUpdate(false);
		// 新增的时候肯定没有实付。
		req.setActualFare(BigDecimal.ZERO);
		Waybill waybillExist = waybillMapper.selectWaybillById(req.getWaybillId());
		if (null != waybillExist) {
			req.setOrderCreateTime(waybillExist.getOrderCreateTime());
		}
		CommonResult judgeResult = orderJudgeParameter(req);
		if (CommonResult.isNotSuccess(judgeResult)) {
			return judgeResult;
		}
		Long waybillId = req.getWaybillId();
		Waybill existWaybill = waybillMapper.selectWaybillById(waybillId);
		// 3、运单信息填充保存
		Waybill waybill = generateOrderPCInfo(req, waybillId);
		if (receiveFare != null) {
			waybill.setReceiveFare(receiveFare);
		}
		// 修改订单成运单
		this.updateWaybill(waybill);
		// 4、写入运单子表
		WaybillSlave waybillSlave = new WaybillSlave();
		waybillSlave.setId(waybillId);
		if (1 == req.getFeeUnit()) {// 应收单位 1.吨
			waybillSlave.setLoadingWeight(req.getFeeAmount());
			waybillSlave.setUnloadWeight(req.getFeeAmount());
		} else if (2 == req.getFeeUnit()) {// 应收单位 2.立方米
			waybillSlave.setLoadingCube(req.getFeeAmount());
			waybillSlave.setUnloadCube(req.getFeeAmount());
		}

		waybillCommonService.fillTransportCapacityToWaybillSlave(waybill, waybillSlave);
		waybillSlave.setPolicyType(req.getPolicyType());
		waybillSlave.setRemark(req.getWaybillRemark());
		if (null != req.getReceiveFareCalculationType()) {
			waybillSlave.setReceiveFareCalculationType(req.getReceiveFareCalculationType());
		} else {
			waybillSlave.setReceiveFareCalculationType(-1);
		}

		MakeCode makeCode = makeCodeMapper.selectMakeCodeById(req.getMakeCode().getId());
		if (makeCode == null) {
			waybillSlave.setWaybillPaymentType(0);
		} else {
			waybillSlave.setWaybillPaymentType(makeCode.getWaybillPaymentType());
		}
		waybillSlaveService.updateWaybillSlave(waybillSlave);
		// 5、写入运单货物表
		waybillGoodsService.saveWaybillGoodsInfo(waybillId, req.getMakeCode(), req.getFeeUnit(), req.getFeeAmount(), req.getGoodsValue());
		// 6、写入运单托运人关系表 ，这一步操作中会插入es
		waybillCustomerService.saveWaybillCustomer(SecurityUtils.getNickname(), waybill);
		// 7、写入运单转包链关系表
		waybillContractChainService.saveConatractChain(waybillId, waybill.getFrameworkContractId(), SecurityUtils.getNickname());
		// 8、收款人限额表增加数据
		paymentRecordService.addPaymentRecord(waybill);
		// 9、写入托运人承运人关系表
		customerActualService.saveShipperCarrierInfo(waybill.getCustomerId(), req.getCarrierId());
		// 10、写入托运人收款账户关系表
		customerPayeeService.saveShipperPayeeInfo(waybill.getCustomerId(), req.getReceiptId());
		// 11、写入托运人司机关系表
		customerDriverService.saveShipperDriverInfo(waybill.getCustomerId(), req.getDriverId());
		// 12、写入托运人车辆关系表
		customerVehicleService.saveShipperVehicleInfo(waybill.getCustomerId(), req.getVehicleId());
		// 13、写入承运人车辆关系表
		actualVehicleRelationService.checkThenAddCarrierVehicleRelation(waybill);
		// 14、写入承运人司机关系表
		actualDriverRelationService.checkThenAddCarrierDriverRelation(waybill);
		// 14.1、写入网络货运人司机关系表
		freightForwarderDriverRelationService.saveFreightForwarderDriverInfo(waybill.getFreightForwarderId(), req.getDriverId(), waybill.getDrivingLicense());
		// 14.2、写入网络货运人车辆关系表
		freightForwarderVehicleRelationService.saveFreightForwarderVehicleInfo(waybill.getFreightForwarderId(), req.getVehicleId(), waybill.getVehicleNumber());
		// 托运人车队长关系表
		if (waybill.getCarCaptainId() != null && waybill.getCarCaptainId() != 0) {
			customerCaptainRelationService.validateThenAddRelationFromWaybill(waybill.getCustomerId(), waybill.getCarCaptainId());
			freightForwarderCarCaptainRelationService.saveFreightForwarderCapacityInfo(waybill.getFreightForwarderId(), waybill.getCarCaptainId());
		}
		// 托运人辅助员关系表
		if (waybill.getAuxiliaryStaffId() != null && waybill.getAuxiliaryStaffId() != 0) {
			customerAuxiliaryRelationService.validateThenAddRelationFromWaybill(waybill.getCustomerId(), waybill.getAuxiliaryStaffId());
			freightForwarderAuxiliaryRelationService.saveFreightForwarderAuxiliaryInfo(waybill.getFreightForwarderId(), waybill.getAuxiliaryStaffId());
		}
		List<Waybill> waybillList = new ArrayList<>();
		waybillList.add(waybill);

		// 15、保存运单附件
		if (CommonUtil.isNotNullOrEmpty(req.getFileSaveList())) {
			for (FileSaveRequest tmp : req.getFileSaveList()) {
				tmp.setFileCategory(FileCategory.WAYBILL.val());
				tmp.setBusinessId(waybillId);
				attachmentService.addFile(tmp);
			}
		}
		// 16、日志记录
		String actionName = "生成运单(运单ID：" + waybillId + "，运单号：" + existWaybill.getShippingNoteNumber() + ")";
		actionName += "，请求参数：" + JSONUtil.toJsonStr(req);
		sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_WAYBILL, BusinessConstants.ACTION_TYPE_ADD, org.apache.commons.lang3.StringUtils.substring(actionName, 0, 5000),
				waybill.getShippingNoteNumber());
		// 16、如果需税务上报发送MQ
		if (waybill.getIsTaxUpload() == 0) {
			taxingRulesService.sendMqInfoAfterCommit(TaxUploadConstants.FIRST_UPLOAD, waybill);
		}
		return CommonResult.success(waybill.getShippingNoteNumber());
	}

	@Override
	public CommonResult<WaybillRes> getOrderById(Long id) {
		// 0、校验及基础数据准备
		if (null == id || 0 >= id) {
			return CommonResult.requestError("参数错误，运单ID为空");
		}
		Waybill waybill = this.selectWaybillById(id);
		WaybillSlave waybillSlave = waybillSlaveService.selectWaybillSlaveById(id);
		if (null == waybill || -1 == waybill.getStatus() || null == waybillSlave) {
			return CommonResult.requestError("运单不存在");
		}

		Long customerId = commonServices.getLoginCustomerId();
		MakeCode makeCode = makeCodeMapper.selectMakeCodeById(waybill.getMakeCodeId());
		// 2、数据拷贝至返回类
		WaybillRes waybillResponse = new WaybillRes();
		BeanUtils.copyProperties(waybill, waybillResponse);
		Vehicle vehicle = vehicleMapper.selectVehicleById(waybill.getVehicleId());
		waybillResponse.setVehicleTypeName(vehicle.getVehicleTypeName());
		waybillResponse.setVehicleTonnage(vehicle.getVehicleTonnage());
		// 3、从其他类中直接获取的字段赋值
		// 3.1、货源中数据
		MakeCodeRule rule = makeCodeRuleMapper.selectMakeCodeRuleById(waybillSlave.getMakeCodeRuleId());
		waybillResponse.setMakeCodeRule(rule);
		waybillResponse.setSupplyType(makeCode.getSupplyType());
		waybillResponse.setCargoCode(makeCode.getCargoCode());
		waybillResponse.setWaybillPaymentType(waybillSlave.getWaybillPaymentType());
		waybillResponse.setUseScope(makeCode.getUseScope());
		waybillResponse.setNameAlias((waybill.getId() + "货源").equals(makeCode.getNameAlias()) ? "" : makeCode.getNameAlias());
		waybillResponse.setRisingAndLosingTons("无");
		waybillResponse.setRisingAndLosingMoney(BigDecimal.ZERO);

		waybillResponse.setLoadingWeight(waybillSlave.getLoadingWeight());
		waybillResponse.setUnloadWeight(waybillSlave.getUnloadWeight());
		waybillResponse.setLoadingCube(waybillSlave.getLoadingCube());
		waybillResponse.setUnloadCube(waybillSlave.getUnloadCube());
		waybillResponse.setPolicyType(waybillSlave.getPolicyType());
		waybillResponse.setPolicyFare(waybillSlave.getPolicyFare());
		waybillResponse.setPolicyNumber(waybillSlave.getPolicyNumber());
		waybillResponse.setPolicyStatus(waybillSlave.getPolicyStatus());
		waybillResponse.setWaybillRemark(waybillSlave.getRemark());
		waybillResponse.setMakeCodeRuleId(waybillSlave.getMakeCodeRuleId());
		waybillResponse.setZeroingRule(waybillSlave.getZeroingRule());
		waybillResponse.setMakeCodeRuleDetailed(waybillSlave.getMakeCodeRuleDetailed());
		waybillResponse.setReceiveFareCalculationType(waybillSlave.getReceiveFareCalculationType());
		// 4、两个类不同名的字段处理，值不需变化
		waybillResponse.setWaybillRemark(CommonUtil.isNotNullOrEmpty(waybill.getRemark()) ? waybillResponse.getWaybillRemark() : waybill.getRemark());
		waybillResponse.setBillDate(DateUtils.dateToStr(waybill.getBillDate(), DateUtils.YYMMDD));
		waybillResponse.setPlanStartTime(waybill.getDespatchActualDateTime());
		waybillResponse.setPlanEndTime(waybill.getGoodsReceiptDateTime());
		waybillResponse.setActualStartTime(waybill.getLoadTime());
		waybillResponse.setActualEndTime(waybill.getUnloadTime());
		waybillResponse.setActualCarrierIdentityCard(waybill.getActualCarrierIdentityCard());
		waybillResponse.setIdentityCard(waybill.getActualCarrierIdentityCard());

		// 如果单位是车，loadingAccount、unloadAccount固定返回数值1
		if (waybillResponse.getFeeUnit() == 3) {
			waybillResponse.setLoadingAccount(BigDecimal.ONE);
			waybillResponse.setUnloadAccount(BigDecimal.ONE);
		}
		// 5、两个类，值不同的字段处理
		// 5.1、装卸货重量 大宗货源根据运单 未开始 进行中 运送结束
		if (3 == waybill.getStatus()) {
			if (waybillResponse.getFeeUnit() == 2) {
				waybillResponse.setLoadingAccount(waybillSlave.getLoadingCube());
			} else if (waybillResponse.getFeeUnit() == 1) {
				waybillResponse.setLoadingAccount(waybillSlave.getLoadingWeight());
			}
		} else if (4 == waybill.getStatus()) {
			if (waybillResponse.getFeeUnit() == 2) {
				waybillResponse.setLoadingAccount(waybillSlave.getLoadingCube());
				waybillResponse.setUnloadAccount(waybillSlave.getUnloadCube());
			} else if (waybillResponse.getFeeUnit() == 1) {
				waybillResponse.setLoadingAccount(waybillSlave.getLoadingWeight());
				waybillResponse.setUnloadAccount(waybillSlave.getUnloadWeight());
			}
		}

		if (Arrays.asList(0, 1, 2).contains(waybillResponse.getStatus())) { // 0=订单,1=待签约,2=已签约,3=运输中,4=已完成
			waybillResponse.setTransportStatus(0); // 0:未开始 1:在途中 2:已完成
		} else if (3 == waybillResponse.getStatus()) {
			waybillResponse.setTransportStatus(1);
		} else if (4 == waybillResponse.getStatus()) {
			waybillResponse.setTransportStatus(2);
		}
		waybillResponse.setConsignorInfo(consignorInfoService.selectConsignorInfoById(waybillResponse.getConsignorId()));
		waybillResponse.setConsigneeInfo(consigneeInfoService.selectConsigneeInfoById(waybillResponse.getConsigneeId()));
		setGoodsInfo(waybillResponse);
		// 处理默认值
		if (BigDecimal.ZERO.compareTo(waybillResponse.getArriveMoney()) == 0) {
			waybillResponse.setArriveMoney(null);
		}
		if (BigDecimal.ZERO.compareTo(waybillResponse.getPrepayMoney()) == 0) {
			waybillResponse.setPrepayMoney(null);
		}
		if (BigDecimal.ZERO.compareTo(waybillResponse.getPayFare()) == 0) {
			waybillResponse.setPayFare(null);
		}
		if (BigDecimal.ZERO.compareTo(waybillResponse.getReceiptMoney()) == 0) {
			waybillResponse.setReceiptMoney(null);
		}
		if (BigDecimal.ZERO.compareTo(waybillResponse.getFeeAmount()) == 0) {
			waybillResponse.setFeeAmount(null);
		}
		if (BigDecimal.ZERO.compareTo(waybillResponse.getFeePrice()) == 0) {
			waybillResponse.setFeePrice(null);
		}
		if (BigDecimal.ZERO.compareTo(waybillResponse.getReceiveFare()) == 0) {
			waybillResponse.setReceiveFare(null);
		}
		setTransportCapicityInfo(waybillResponse, waybillSlave);
		return CommonResult.success(waybillResponse);
	}

	@Override
	public CommonResult orderToWaybillCheck(Long id) {
		if (null == id) {
			return CommonResult.requestError("id为空");
		}
		// 5、新增时，完整性校验
		StringBuilder errorMessage = new StringBuilder();

		// 根据运单id查询到所需信息
		Waybill waybill = waybillMapper.selectWaybillById(id);
		// 司机
		Driver existDriver = driverMapper.selectDriverById(waybill.getDriverId());
		if (null == existDriver) {
			return CommonResult.requestError("司机不存在，请确认");
		}
		if (existDriver.getInfoIsComplete() == 1) {
			errorMessage.append("司机: ").append(existDriver.getDriverName()).append("，信息不齐全\n");
		}

		// 车辆
		Vehicle existVehicle = vehicleMapper.selectVehicleById(waybill.getVehicleId());
		if (null == existVehicle) {
			return CommonResult.requestError("车辆不存在，请确认");
		}
		if (existVehicle.getInfoIsComplete() == 1) {
			errorMessage.append("车辆: ").append(existVehicle.getVehicleNumber()).append("，信息不齐全\n");
		}

		// 收款人
		if (waybill.getPayeeType() == 2) {
			CarCaptainInfo carCaptainInfo = carCaptainInfoMapper.selectCarCaptainInfoById(waybill.getCarCaptainId());
			if (null == carCaptainInfo) {
				return CommonResult.requestError("收款人不存在，请确认");
			}
			if (carCaptainInfo != null && carCaptainInfo.getInfoIsComplete() == 1) {
				errorMessage.append("收款人: 车队长 ").append(carCaptainInfo.getCarCaptainName()).append("，信息不齐全\n");
			}
		} else if (waybill.getPayeeType() == 3) {
			AuxiliaryStaffInfo auxiliaryStaffInfo = auxiliaryStaffInfoMapper.selectAuxiliaryStaffInfoById(waybill.getAuxiliaryStaffId());
			if (null == auxiliaryStaffInfo) {
				return CommonResult.requestError("收款人不存在，请确认");
			}
			if (auxiliaryStaffInfo != null && auxiliaryStaffInfo.getInfoIsComplete() == 1) {
				errorMessage.append("收款人: ").append(auxiliaryStaffInfo.getAuxiliaryStaffName()).append("，信息不齐全\n");
			}
		}

		// 如果有任何错误信息，统一返回
		if (errorMessage.length() > 0) {
			errorMessage.append("请先前往运力管理中补全后再生成运单!");
			return CommonResult.requestError(errorMessage.toString());
		}

		// 如果是善道的业务，则进行辅助员签约判断，只对2025-02-27 12:00:00后创建的运单进行判断
		Date yesterdayEnd = DateUtil.parse("2025-02-27 12:00:00", "yyyy-MM-dd HH:mm:ss");
		Date orderCreateTime = waybill.getOrderCreateTime();
		FrameworkContract frameworkContract = frameworkContractService.selectFrameworkContractById(waybill.getFrameworkContractId());
		if (orderCreateTime.after(yesterdayEnd)) {
			if (!auxiliaryStaffInfoService.validateShandaoRelationIsOk(frameworkContract.getFreightForwarderId(), null, waybill.getAuxiliaryStaffId())) {
				return CommonResult.error("关联辅助员未与平台签约，无法接单，请联系平台");
			}
		}
		return CommonResult.success();
	}

	@Override
	public CommonResult<WaybillRes> getTransportationAgreement(Long id) {
		Waybill waybill = waybillMapper.selectWaybillById(id);
		if (null == waybill) {
			return CommonResult.requestError("运单不存在");
		}
		DriverElectronicContract driverElectronicContractQuery = new DriverElectronicContract();
		driverElectronicContractQuery.setDriverIdentityCard(waybill.getDrivingLicense());
		driverElectronicContractQuery.setFreightForwarderId(SecurityUtils.getFreightForwarderId());
		driverElectronicContractQuery.setState(0);
		driverElectronicContractQuery.setIsEffective(1);// 使用中
		List<DriverElectronicContract> driverElectronicContracts = driverElectronicContractMapper.selectDriverElectronicContractList(driverElectronicContractQuery);
		List<FileModel> transportContractAttachmentList = new ArrayList<>();
		FileModel fileModel = new FileModel();
		if (StringUtils.isNotEmpty(driverElectronicContracts)) {
			DriverElectronicContract driverElectronicContract = driverElectronicContracts.get(0);
			fileModel.setFileUrl(driverElectronicContract.getFileUrl());
			fileModel.setFileName("运输协议");
			fileModel.setOriginalFileName("运输协议");
			fileModel.setFileSize(null);
			fileModel.setFileType("application/pdf");
		} else {
			String code = signContractService.getYSProtocolCode(id);
			FreightForwarderInfo freightForwarderInfo = freightForwarderInfoMapper.selectFreightForwarderInfoById(waybill.getFreightForwarderId());
			// 自动生成运输协议
			AjaxResult fileModelCommonResult = agreementGenerateService.generateWithSignNotSave(waybill, freightForwarderInfo, code);
			if (fileModelCommonResult.isSuccess()) {
				WaybillAttachmentInfo waybillAttachmentInfo = (WaybillAttachmentInfo) fileModelCommonResult.getData();
				fileModel.setFileUrl(waybillAttachmentInfo.getFileUrl());
				fileModel.setFileName("运输协议");
				fileModel.setOriginalFileName(waybillAttachmentInfo.getOriginalFileName());
				fileModel.setFileSize(Integer.parseInt(waybillAttachmentInfo.getFileSize()));
				fileModel.setFileType(waybillAttachmentInfo.getFileType());
			}
		}
		transportContractAttachmentList.add(fileModel);
		WaybillRes waybillRes = new WaybillRes();
		waybillRes.setTransportContractAttachmentList(transportContractAttachmentList);

		return CommonResult.success(waybillRes);
	}

	@Override
	public List<Waybill> selectNewWaybillList(UploginkWaybillQuery waybillQuery) {
		List<Waybill> list = waybillMapperEx.selectNewWaybillList(waybillQuery);
		if (null == list) {
			return Collections.emptyList();
		}
		return list;
	}

	/**
	 * @param waybillIds
	 * @param type
	 * 		0：同步增加 1：同步删除
	 */
	@Override
	public void pushWaybillReachShanDao(List<Long> waybillIds, Integer type) {
		try {
			if (waybillIds.isEmpty()) {
				return;
			}
			List<Waybill> waybills = waybillMapper.selectWaybillByIds(waybillIds);
			if (waybills.isEmpty()) {
				return;
			}
			FreightForwarderInfo freightForwarderInfo = freightForwarderInfoMapper.selectFreightForwarderInfoByName("江苏善道智运科技有限公司");
			if (null == freightForwarderInfo) {
				return;
			}
			if (null == type) {
				type = 0;
			}
			// 这些项目不需要同步
			List<Long> frameworkContractIds = Arrays.asList(173200250257400042L, 171314458335559986L, 171314448002059879L, 171281817089057200L, 171281597612152041L, 171281714960554857L,
					171281608581352116L, 171281762914156131L, 171281811833156789L, 171281798098256536L, 171281579397951796L, 171281821844658048L, 171281709212354555L, 172370993084951320L,
					171281731699655121L, 171652943151899189L, 172059119695548671L, 172171146886526343L, 171281817089057200L);
			// 筛选 善道平台下司机本人收款 的运单
			List<Waybill> pushWaybills = waybills.stream()
					.filter(waybill -> waybill.getFreightForwarderId().equals(freightForwarderInfo.getId()) && !frameworkContractIds.contains(waybill.getFrameworkContractId()))
					.collect(Collectors.toList());
			if (pushWaybills.isEmpty()) {
				return;
			}
			if (type == 0) { // 同步增加 代开的运单
				pushWaybills = pushWaybills.stream().filter(waybill -> waybill.getProxyInvoiceState() == 4).collect(Collectors.toList());
			}
			if (type == 1) { // 同步删除 不代开的运单
				pushWaybills = pushWaybills.stream().filter(waybill -> waybill.getProxyInvoiceState() == 3).collect(Collectors.toList());
			}
			if (pushWaybills.isEmpty()) {
				return;
			}
			SynchronousShanDaoReq req = new SynchronousShanDaoReq();
			req.setWaybillIds(pushWaybills.stream().map(Waybill::getId).collect(Collectors.toList()));
			req.setType(type);
			String jsonString = JSON.toJSONString(req);
			// 判断当前线程下事务是否提交
			if (TransactionSynchronizationManager.isSynchronizationActive()) {
				TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
					@Override
					public void afterCommit() {
						log.info("MQ-->4.0运单同步修改善道数据推送,事务后再提交的发送消息--->" + jsonString);
						rabbitTemplate.convertAndSend(MQConstants.EXCHANGE_NAME, MQConstants.FOUR_TO_SHAN_DAO_KEY, jsonString);
					}
				});
			} else {
				rabbitTemplate.convertAndSend(MQConstants.EXCHANGE_NAME, MQConstants.FOUR_TO_SHAN_DAO_KEY, jsonString);
			}

			log.info("==============================》4.0运单同步修改善道数据推送成功");
		} catch (Exception e) {
			log.error("==============================》4.0运单同步修改善道数据推送异常{}", e.getMessage());
			e.printStackTrace();
		}

	}

	@Override
	public AjaxResult shipperWaybillListCount(WaybillQuery waybillQuery) {

		Map<String, Long> map = new HashMap<>();

		// 待装车
		waybillQuery.setTransportStatus(0);
		waybillQuery.setConfirmReceiptState(1);
		Long loadingCount = waybillMapperEx.countShipperWaybillList(waybillQuery);

		// 待卸货
		waybillQuery.setTransportStatus(1);
		Long unloadCount = waybillMapperEx.countShipperWaybillList(waybillQuery);

		// 代签收
		waybillQuery.setTransportStatus(2);
		Long unsignCount = waybillMapperEx.countShipperWaybillList(waybillQuery);

		// 已签收
		waybillQuery.setConfirmReceiptState(0);
		Long signCount = waybillMapperEx.countShipperWaybillList(waybillQuery);

		map.put("loadingCount", loadingCount);
		map.put("unloadCount", unloadCount);
		map.put("unsignCount", unsignCount);
		map.put("signCount", signCount);
		return AjaxResult.success(map);
	}

	@Override
	public CommonResult<BigDecimal> countPayFare(ConfirmReceiptReq req) {
		BigDecimal loadingAccount = req.getLoadingAmount();
		BigDecimal unloadAccount = req.getUnloadAmount();
		BigDecimal fare = BigDecimal.ZERO;
		if (req.getWaybillPaymentType() == 0) {
			fare = loadingAccount.multiply(req.getWaybillPrice());
		} else if (req.getWaybillPaymentType() == 1) {
			fare = unloadAccount.multiply(req.getWaybillPrice());
		} else if (req.getWaybillPaymentType() == 2) {
			fare = req.getWaybillPrice().multiply(loadingAccount.compareTo(unloadAccount) < 0 ? loadingAccount : unloadAccount); // 运输类别：方
		}
		BigDecimal diffMoney = waybillCommonService.calculateDiffMoneyWithRule(loadingAccount, unloadAccount, req.getMakeCodeRuleId());
		BigDecimal payFare = fare.subtract(diffMoney).subtract(req.getDeductMoney()).setScale(2, RoundingMode.HALF_UP);
		payFare = waybillCommonService.caculateBULKPayFareByZeroingRule(payFare, req.getZeroingRule());
		BigDecimal extraFare = waybillFareService.getTotalExtraFareByWaybillId(req.getId());// 加上货损货差
		payFare = payFare.add(extraFare);

		return CommonResult.success(payFare);
	}

	@Override
	public BigDecimal countPayFareWithExtraFare(ConfirmReceiptReq req, Boolean containExtraFare) {
		BigDecimal loadingAccount = req.getLoadingAmount();
		BigDecimal unloadAccount = req.getUnloadAmount();
		BigDecimal fare = BigDecimal.ZERO;
		if (req.getWaybillPaymentType() == 0) {
			fare = loadingAccount.multiply(req.getWaybillPrice());
		} else if (req.getWaybillPaymentType() == 1) {
			fare = unloadAccount.multiply(req.getWaybillPrice());
		} else if (req.getWaybillPaymentType() == 2) {
			fare = req.getWaybillPrice().multiply(loadingAccount.compareTo(unloadAccount) < 0 ? loadingAccount : unloadAccount); // 运输类别：方
		}
		BigDecimal diffMoney = waybillCommonService.calculateDiffMoneyWithRule(loadingAccount, unloadAccount, req.getMakeCodeRuleId());
		BigDecimal payFare = fare.subtract(diffMoney).subtract(req.getDeductMoney()).setScale(2, RoundingMode.HALF_UP);
		payFare = waybillCommonService.caculateBULKPayFareByZeroingRule(payFare, req.getZeroingRule());
		if (!containExtraFare) {
			return payFare;
		}
		BigDecimal extraFare = waybillFareService.getTotalExtraFareByWaybillId(req.getId());// 加上货损货差
		return payFare.add(extraFare);
	}

	@Override
	@Transactional
	public CommonResult confirmReceipt(ConfirmReceiptReq req) {

		if (req.getId() == null || req.getId() == 0) {
			return CommonResult.error("id为空");
		}

		if (req.getWaybillType() == 0) {
			CommonResult<BigDecimal> bigDecimalCommonResult = countPayFare(req);
			if (req.getPayFare().compareTo(bigDecimalCommonResult.getData()) != 0) {
				return CommonResult.error("费用不一致");
			}
		}

		Waybill waybill = waybillMapper.selectWaybillById(req.getId());

		if (waybill.getPayType() == 1) {
			if (req.getPayFare().compareTo(req.getPrepayMoney().add(req.getArriveMoney()).add(req.getReceiptMoney())) != 0) {
				return CommonResult.error("费用不一致");
			}
		}
		WaybillSlave waybillSlave = waybillSlaveMapper.selectWaybillSlaveById(req.getId());

		Waybill update = new Waybill();
		update.setId(req.getId());
		update.setWaybillPrice(req.getWaybillPrice());
		update.setDeductMoney(req.getDeductMoney());
		if (req.getLoadingAmount() != null && BigDecimal.ZERO.compareTo(req.getLoadingAmount()) != 0 && req.getUnloadAmount() != null && BigDecimal.ZERO.compareTo(req.getUnloadAmount()) != 0) {
			if (req.getWaybillPaymentType() == 0) {
				update.setFeeAmount(req.getLoadingAmount());
			} else if (req.getWaybillPaymentType() == 1) {
				update.setFeeAmount(req.getUnloadAmount());
			} else if (req.getWaybillPaymentType() == 2) {
				update.setFeeAmount(req.getLoadingAmount().compareTo(req.getUnloadAmount()) < 0 ? req.getLoadingAmount() : req.getUnloadAmount());
			}
		} else {
			update.setFeeAmount(req.getFeeAmount());
		}
		update.setFeeUnit(req.getFeeUnit());
		update.setTotalFare(req.getPayFare());
		if (waybill.getPrepayType() == 2) {
			update.setTotalFare(req.getPayFare().add(req.getPrepayMoney()));
		}
		if (req.getFeeUnit() == 3) {
			update.setFeeAmount(BigDecimal.ONE);
		}
		update.setPayFare(req.getPayFare());
		update.setUnpaidFare(req.getPayFare().subtract(waybill.getActualFare()));
		if (waybill.getPayType() == 1) {
			update.setArriveMoney(req.getArriveMoney());
			update.setArriveUnpaidMoney(update.getArriveMoney());
			update.setReceiptMoney(req.getReceiptMoney());
			update.setReceiptUnpaidMoney(req.getReceiptMoney());
		}
		update.setConfirmReceiptState(0);

		if (waybillSlave.getReceiveFareCalculationType() == 0) {
			update.setFeePrice(waybill.getReceiveFare().divide(req.getFeeAmount(), 2, RoundingMode.HALF_UP));
		}
		if (waybillSlave.getReceiveFareCalculationType() == 1) {
			update.setReceiveFare(waybill.getFeePrice().multiply(req.getFeeAmount()).setScale(2, RoundingMode.HALF_UP));
		}
		waybillMapper.updateWaybill(update);

		WaybillSlave updateSlave = new WaybillSlave();
		updateSlave.setId(req.getId());
		if (req.getFeeUnit() == 1) {
			updateSlave.setLoadingWeight(req.getLoadingAmount());
			updateSlave.setUnloadWeight(req.getUnloadAmount());

			updateSlave.setLoadingCube(BigDecimal.ZERO);
			updateSlave.setUnloadCube(BigDecimal.ZERO);
		}
		if (req.getFeeUnit() == 2) {
			updateSlave.setLoadingCube(req.getLoadingAmount());
			updateSlave.setUnloadCube(req.getUnloadAmount());

			updateSlave.setLoadingWeight(BigDecimal.ZERO);
			updateSlave.setUnloadWeight(BigDecimal.ZERO);
		}
		updateSlave.setDriverUpdateReceiptState(0);
		updateSlave.setMakeCodeRuleId(req.getMakeCodeRuleId());
		updateSlave.setMakeCodeRuleDetailed(req.getMakeCodeRuleDetailed());
		updateSlave.setZeroingRule(req.getZeroingRule());
		updateSlave.setWaybillPaymentType(req.getWaybillPaymentType());
		waybillSlaveMapper.updateWaybillSlave(updateSlave);

		waybillCustomerService.updatePayableUpAndDowm(waybill.getId());

		// 校验风控信息
		List<Long> waybillIds = Arrays.asList(waybill.getId());
		waybillMapperEx.updateRiskResultGradeByIds(waybillIds);
		waybillRiskService.waybillsRiskCheckByIds(waybillIds, StringUtils.isEmpty(SecurityUtils.getNickname()) ? "系统校验" : SecurityUtils.getNickname());
		// 税务上报运单处理，下述方法会过滤非需上报运单
		ywkUploadService.doArriveRelatedUpload(Arrays.asList(waybill));
		// 同步善道4.0
		// waybillService.pushWaybillReachShanDao(waybillIds, 0);

		String actionName = "确认签收(运单ID：" + waybill.getId() + "，运单号：" + waybill.getShippingNoteNumber();
		actionName += "，请求参数:" + JSONUtil.toJsonStr(req);
		sysClientLogService.insertLog(BusinessConstants.ACTION_SCENE_WAYBILL, BusinessConstants.ACTION_TYPE_UPDATE, actionName, waybill.getShippingNoteNumber(),
				Collections.singletonList(waybill.getId()));

		return CommonResult.success();
	}

	@Override
	public CommonResult<ConfirmReceiptInfo> confirmReceiptInfo(Long id) {
		ConfirmReceiptInfo confirmReceiptInfo = waybillMapperEx.confirmReceiptInfo(id);

		if (confirmReceiptInfo.getFeeUnit() == 1) {
			confirmReceiptInfo.setLoadingAmount(confirmReceiptInfo.getLoadingWeight());
			confirmReceiptInfo.setUnloadAmount(confirmReceiptInfo.getUnloadWeight());
		} else if (confirmReceiptInfo.getFeeUnit() == 2) {
			confirmReceiptInfo.setLoadingAmount(confirmReceiptInfo.getLoadingCube());
			confirmReceiptInfo.setUnloadAmount(confirmReceiptInfo.getUnloadCube());
		} else {
			confirmReceiptInfo.setLoadingAmount(BigDecimal.ONE);
			confirmReceiptInfo.setUnloadAmount(BigDecimal.ONE);
		}

		// 获取发货附件
		List<WaybillAttachmentInfo> loadingFiles = waybillAttachmentInfoService.loadingFileList(id);
		confirmReceiptInfo.setLoadingFile(loadingFiles.stream().map(FileModel::of).collect(Collectors.toList()));

		// 获取收货附件
		List<WaybillAttachmentInfo> unloadFiles = waybillAttachmentInfoService.unloadFileList(id);
		confirmReceiptInfo.setUnloadFile(unloadFiles.stream().map(FileModel::of).collect(Collectors.toList()));

		return CommonResult.success(confirmReceiptInfo);

	}

	/**
	 * 查询运单物流详情
	 */
	public AjaxResult getWaybillLogisticsDetailsSD(Long id, int flag) {
		if (null == id || id == 0L) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "运单标识为空");
		}
		Waybill waybill = this.selectWaybillById(id);
		if (null == waybill) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "没有找到对应的运单信息");
		}
		WaybillSlave waybillSlave = waybillSlaveService.selectWaybillSlaveById(waybill.getId());
		if (null == waybillSlave) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "没有找到相关的运单信息");
		}
		waybill.setWaybillSlave(waybillSlave);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("driverName", waybill.getDriverName());
		map.put("telephone", waybill.getTelephone());
		map.put("vehicleNumber", waybill.getVehicleNumber());
		map.put("locateStatus", waybill.getLocateStatus());
		map.put("locateType", waybill.getLocateType());
		map.put("status", waybill.getStatus());
		// 已派单
		if (waybill.getStatus() >= BusinessConstants.WAYBILL_STATUS_WAYBILL) {
			Map<String, Object> sendOrdersMap = new HashMap<String, Object>();
			String mileage = waybill.getMileage().longValue() > 0L ? "全程：" + waybill.getMileage() + "KM" : "";
			String sendOrders = flag == 0 ?
					"【已派单】\r\n已向司机派单\r\n司机：" + waybill.getDriverName() + " " + waybill.getTelephone() + "\r\n车牌号：" + waybill.getVehicleNumber() + "为您承运\r\n" + mileage :
					"【已派单】\r\n承运人：" + waybill.getDriverName() + " " + waybill.getTelephone() + "\r\n" + mileage;
			if (null != waybill.getSubmitTime()) {
				sendOrdersMap.put("time", DateUtils.parseDateToStr(DateUtils.YYSS, waybill.getSubmitTime()));
			}
			sendOrdersMap.put("sendOrdersInfo", sendOrders);
			map.put("sendOrders", sendOrdersMap);
		}
		// 已签约
		if (waybill.getStatus() >= BusinessConstants.WAYBILL_STATUS_LEASE) {
			Map<String, Object> signedMap = new HashMap<String, Object>();
			String signedInfo = flag == 0 ? "【已签约】\r\n司机已签约" : "【已签约】\r\n承运人已签约";
			List<FileModel> fileList = new ArrayList<>();
			List<WaybillAttachmentInfo> eContractFiles = waybillAttachmentInfoService.eContractFileList(waybill.getId());
			if (null != eContractFiles && !eContractFiles.isEmpty()) {
				fileList = eContractFiles.stream().map(FileModel::of).collect(Collectors.toList());
			}
			if (null != waybill.getSignTime()) {
				signedMap.put("time", DateUtils.parseDateToStr(DateUtils.YYSS, waybill.getSignTime()));
			}
			signedMap.put("signedInfo", signedInfo);
			signedMap.put("fileList", fileList);
			map.put("signed", signedMap);
		}
		// 已发车
		if (waybill.getStatus() >= BusinessConstants.WAYBILL_STATUS_IN_TRANSIT) {
			Map<String, Object> departMap = new HashMap<String, Object>();
			String departInfo = "【已发车】\r\n装货完毕，车辆已出发。\r\n装货信息：";
			if (BigDecimal.ZERO.compareTo(waybill.getWaybillSlave().getLoadingWeight()) != 0) {
				departInfo += "重量" + waybill.getWaybillSlave().getLoadingWeight().setScale(3, RoundingMode.HALF_UP) + "吨";
			} else if (BigDecimal.ZERO.compareTo(waybill.getWaybillSlave().getLoadingCube()) != 0) {
				departInfo += "体积" + waybill.getWaybillSlave().getLoadingCube().setScale(3, RoundingMode.HALF_UP) + "方";
			} else {
				departInfo += "1车";
			}
			if (waybill.getFeeUnit() != 3) {
				if (waybill.getFeeUnit() == 1) {
					departInfo += "重量" + waybill.getFeeAmount() + "吨";
				} else if (waybill.getFeeUnit() == 2) {
					departInfo += "体积" + waybill.getFeeAmount() + "方";
				} else {
					departInfo += "1车";
				}
			}

			List<String> fileUrlList = new ArrayList<String>();
			List<WaybillAttachmentInfo> loadingFiles = waybillAttachmentInfoService.loadingFileList(waybill.getId());
			if (null != loadingFiles && !loadingFiles.isEmpty()) {
				fileUrlList = loadingFiles.stream().map(WaybillAttachmentInfo::getFileUrl).collect(Collectors.toList());
			}
			Date loadClickTime = waybillSlave.getLoadClickTime() != null ? waybillSlave.getLoadClickTime() : waybill.getLoadTime();
			if (null != loadClickTime) {
				departMap.put("time", DateUtils.parseDateToStr(DateUtils.YYSS, loadClickTime));
			}
			departMap.put("departInfo", departInfo);
			departMap.put("fileList", fileUrlList);
			map.put("depart", departMap);
		}
		// 轨迹信息
		if (waybill.getStatus() >= BusinessConstants.WAYBILL_STATUS_IN_TRANSIT) {
			String str = flag == 0 ? "定位" : "最新位置";
			// GPS定位信息
			Map<String, Object> gpsTrackMap = new HashMap<String, Object>();
			List<WaybillTransportTrackRes> gpsTrackList = new ArrayList<>();
			// 0：未补充1：已补充
			Boolean IsCompleteTrajectory = false;
			// 查询当前订单是否补充过轨迹
			int count = waybillCustomerRelationMapper.selectIsCompleteTrajectoryCount(waybill.getId());
			IsCompleteTrajectory = count > 0;
			// 判断当前订单是否补充过轨迹
			if (IsCompleteTrajectory) {
				Vehicle vehicle = vehicleService.selectVehicleById(waybill.getVehicleId());
				VehicleTrackReq vehicleTrackReq = new VehicleTrackReq();
				vehicleTrackReq.setWaybillId(id);
				vehicleTrackReq.setQryBtm(waybill.getDespatchActualDateTime());
				vehicleTrackReq.setQryEtm(waybill.getGoodsReceiptDateTime());
				vehicleTrackReq.setVehicleNumber(vehicle.getVehicleNumber());
				vehicleTrackReq.setResetFlag("");
				vehicleTrackReq.setVehiclePlateColorCode(vehicle.getVehiclePlateColorCode());
				CommonResult<String> result = trackService.getCompleteTrajectory(vehicleTrackReq);
				if (result.getCode().equals("200")) {
					log.info("CompleteTrajectory:【{}】", result.getData());
					gpsTrackList = JSON.parseArray(result.getData(), WaybillTransportTrackRes.class);
				}
			} else {
				CommonResult<String> result = trackService.getGpsTrackList(id);
				if (result.getCode().equals("200")) {
					log.info("GpsTrackList:【{}】", result.getData());
					gpsTrackList = JSON.parseArray(result.getData(), WaybillTransportTrackRes.class);
				}
			}
			gpsTrackMap.put("trackList", gpsTrackList);
			if (gpsTrackList != null && gpsTrackList.size() > 0) {
				for (int i = 0; i < gpsTrackList.size(); i++) {
					if (i == 0 || i == gpsTrackList.size() - 1) {
						WaybillTransportTrackRes res = gpsTrackList.get(i);
						AmapLocationModel locationModel = amapService.getLocationByLongitudeAndLatitude(res.getLon(), res.getLat());
						res.setAdr(locationModel.getFormattedAddress());
					}
				}
				List<WaybillTransportTrackRes> gpsListInfo = changeTrack(gpsTrackList);
				gpsTrackMap.put("trackListInfo", gpsListInfo);
				gpsTrackMap.put("newTrackInfo", gpsListInfo.get(0));
				String trackInfo = "【位置追踪】\r\n最新位置：" + gpsListInfo.get(0).getAdr();
				gpsTrackMap.put("trackInfo", trackInfo);
				gpsTrackMap.put("time", DateUtils.dateToStr(gpsListInfo.get(0).getUtc()));
			}
			map.put("gpsTrack", gpsTrackMap);
			// 小程序定位信息
			Map<String, Object> miniProgramTrackMap = new HashMap<String, Object>();
			WaybillMiniprogramTrack mptrackQuery = new WaybillMiniprogramTrack();
			mptrackQuery.setWaybillId(id);
			List<WaybillMiniprogramTrack> mptrackList = waybillMiniprogramTrackMapper.selectWaybillMiniprogramTrackList(mptrackQuery);
			if (StringUtils.isEmpty(gpsTrackList) && StringUtils.isEmpty(mptrackList)) {
				Long consignorId = waybill.getConsignorId();
				Long consigneeId = waybill.getConsigneeId();
				ConsignorInfo consignorInfo = consignorInfoService.selectConsignorInfoById(consignorId);
				ConsigneeInfo consigneeInfo = consigneeInfoService.selectConsigneeInfoById(consigneeId);
				// 预计轨迹
				String shippingNoteNumber = waybill.getShippingNoteNumber();
				Integer substring = Integer.valueOf(shippingNoteNumber.substring(19));
				Integer type = 0;
				if (Arrays.asList(5, 6, 7, 8, 9).contains(substring)) {
					type = 1;
				}
				List<WaybillTransportTrackRes> trackList = trackService.getPlanRoutes(waybill.getVehicleNumber(), consignorInfo.getLongitude(), consignorInfo.getLatitude(),
						consigneeInfo.getLongitude(), consigneeInfo.getLatitude(), type);

				if (waybill.getStatus() != 4) {
					// 根据运输时长截取轨迹
					Date despatchActualDateTime = waybill.getDespatchActualDateTime();
					Date goodsReceiptDateTime = waybill.getGoodsReceiptDateTime();

					BigDecimal allHour = DateUtils.getDateHourPoor(goodsReceiptDateTime, despatchActualDateTime);

					Date loadTime = waybill.getLoadTime();
					Date nowDate = DateUtils.getNowDate();
					BigDecimal arriverHour = DateUtils.getDateHourPoor(nowDate, loadTime);

					BigDecimal divide = allHour.divide(arriverHour, 2, RoundingMode.HALF_DOWN);
					if (BigDecimal.ONE.compareTo(divide) <= 0) {
						divide = new BigDecimal("0.7");
					}

					BigDecimal multiply = divide.multiply(new BigDecimal(trackList.size())).setScale(0, RoundingMode.HALF_DOWN);
					trackList = trackList.subList(0, multiply.intValue());
				}
				miniProgramTrackMap.put("trackList", trackList);

				// 运输中的才显示最新的一条数据
				if (waybill.getStatus() == BusinessConstants.WAYBILL_STATUS_IN_TRANSIT || waybill.getStatus() == BusinessConstants.WAYBILL_STATUS_COMPLETED) {
					WaybillTransportTrackRes res = trackList.get(trackList.size() - 1);// 获取最新的一条位置信息

					AmapLocationModel locationModel = amapService.getLocationByLongitudeAndLatitude(res.getLon(), res.getLat());

					String miniProgramTrackInfo = "【位置追踪】\r\n最新位置：" + locationModel.getFormattedAddress();
					miniProgramTrackMap.put("trackInfo", miniProgramTrackInfo);
				}
				miniProgramTrackMap.put("trackList", trackList);
				if (trackList.size() > 0) {
					for (int i = 0; i < trackList.size(); i++) {
						if (i == 0 || i == trackList.size() - 1) {
							WaybillTransportTrackRes res = trackList.get(i);
							AmapLocationModel locationModel = amapService.getLocationByLongitudeAndLatitude(res.getLon(), res.getLat());
							res.setAdr(locationModel.getFormattedAddress());
						}
					}
					List<WaybillTransportTrackRes> trackListInfo = changeTrackSD(trackList);

					// for (WaybillTransportTrackRes res : trackListInfo) {
					// AmapLocationModel locationModel = amapService.getLocationByLongitudeAndLatitude(res.getLon(), res.getLat());
					// res.setAdr(locationModel.getFormattedAddress());
					// }
					miniProgramTrackMap.put("trackListInfo", trackListInfo);
					miniProgramTrackMap.put("newTrackInfo", trackListInfo.get(0));
				}

				map.put("miniProgramTrack", miniProgramTrackMap);
			} else {
				if (null != mptrackList && !mptrackList.isEmpty()) {
					List<WaybillTransportTrackRes> trackList = new ArrayList<WaybillTransportTrackRes>();
					for (WaybillMiniprogramTrack miniprogramTrack : mptrackList) {
						WaybillTransportTrackRes trackRes = new WaybillTransportTrackRes();
						BeanUtils.copyProperties(miniprogramTrack, trackRes);
						trackList.add(trackRes);
					}
					// 运输中的才显示最新的一条数据
					if (waybill.getStatus() == BusinessConstants.WAYBILL_STATUS_IN_TRANSIT || waybill.getStatus() == BusinessConstants.WAYBILL_STATUS_COMPLETED) {
						mptrackQuery = mptrackList.get(mptrackList.size() - 1);// 获取最新的一条位置信息
						String miniProgramTrackInfo = "【位置追踪】\r\n最新位置：" + mptrackQuery.getAdr();
						miniProgramTrackMap.put("trackInfo", miniProgramTrackInfo);
						if (null != mptrackQuery.getCreateTime()) {
							miniProgramTrackMap.put("time", DateUtils.parseDateToStr(DateUtils.YYSS, mptrackQuery.getCreateTime()));
						}
					}
					miniProgramTrackMap.put("trackList", trackList);
					if (trackList.size() > 0) {
						List<WaybillTransportTrackRes> trackListInfo = changeTrack(trackList);
						miniProgramTrackMap.put("trackListInfo", trackListInfo);
						miniProgramTrackMap.put("newTrackInfo", trackListInfo.get(0));
					}
					map.put("miniProgramTrack", miniProgramTrackMap);
				}
			}
		}
		// 运输完成
		if (waybill.getStatus() == BusinessConstants.WAYBILL_STATUS_COMPLETED) {
			Map<String, Object> completeMap = new HashMap<String, Object>();
			String completeInfo = "【运输完成】\r\n卸货完毕，运输完成。\r\n卸货信息：";
			if (BigDecimal.ZERO.compareTo(waybill.getWaybillSlave().getUnloadWeight()) != 0) {
				completeInfo += "重量" + waybill.getWaybillSlave().getUnloadWeight().setScale(3, RoundingMode.HALF_UP) + "吨";
			} else if (BigDecimal.ZERO.compareTo(waybill.getWaybillSlave().getUnloadCube()) != 0) {
				completeInfo += "体积" + waybill.getWaybillSlave().getUnloadCube().setScale(3, RoundingMode.HALF_UP) + "方";
			} else {
				completeInfo += "1车";
			}
			List<String> fileUrlList = new ArrayList<String>();
			// 到货附件
			List<WaybillAttachmentInfo> unloadFiles = waybillAttachmentInfoService.unloadFileList(waybill.getId());
			if (null != unloadFiles && !unloadFiles.isEmpty()) {
				fileUrlList = unloadFiles.stream().map(WaybillAttachmentInfo::getFileUrl).collect(Collectors.toList());
			}
			Date unloadClickTime = waybillSlave.getUnloadClickTime() != null ? waybillSlave.getUnloadClickTime() : waybill.getUnloadTime();
			if (null != unloadClickTime) {
				completeMap.put("time", DateUtils.parseDateToStr(DateUtils.YYSS, unloadClickTime));
			}
			completeMap.put("completeInfo", completeInfo);
			completeMap.put("fileList", fileUrlList);
			map.put("complete", completeMap);
		}
		return AjaxResult.success(map);
	}

	@Override
	public CommonResult updateOrderCreateTime() {

		List<Waybill> list = waybillMapperEx.selectSD1111Waybill();

		for (Waybill waybill : list) {
			Waybill update = new Waybill();
			update.setId(waybill.getId());
			boolean isUpdate = false;
			if (waybill.getResource() == 6) {
				if (DateUtils.compareDate(waybill.getDespatchActualDateTime(), waybill.getSignTime()) < 0) {
					update.setOrderCreateTime(waybill.getDespatchActualDateTime());
					update.setCreateTime(waybill.getDespatchActualDateTime());
					isUpdate = true;
				} else {
					update.setOrderCreateTime(waybill.getSignTime());
					update.setCreateTime(waybill.getSignTime());
					isUpdate = true;
				}
			} else {
				String shippingNoteNumber = waybill.getShippingNoteNumber();
				Integer substring = Integer.valueOf(shippingNoteNumber.substring(19));
				Integer randomNumber = -60;
				if (Arrays.asList(7).contains(substring)) {
					randomNumber = -70;
				}
				if (Arrays.asList(8).contains(substring)) {
					randomNumber = -80;
				}
				if (Arrays.asList(9).contains(substring)) {
					randomNumber = -90;
				}
				// 如果实际发车时间早于预计发车时间，创建时间调整较早的一个
				Date date = waybill.getDespatchActualDateTime();
				if (null != waybill.getLoadTime() && DateUtils.compareDate(waybill.getLoadTime(), waybill.getDespatchActualDateTime()) < 0) {
					date = waybill.getLoadTime();
				}
				if (DateUtils.compareDate(date, waybill.getCreateTime()) < 0) {
					update.setOrderCreateTime(DateUtils.addMinute(date, randomNumber));
					update.setCreateTime(DateUtils.addMinute(date, randomNumber));
					isUpdate = true;
				} else {
					update.setOrderCreateTime(waybill.getCreateTime());
					isUpdate = true;
				}
			}

			if (isUpdate) {
				waybillService.updateWaybill(update);
			}
		}

		return CommonResult.success();
	}

	@Override
	public CommonResult updateLoadTime() {
		List<Waybill> list = waybillMapperEx.selectLoadTimeAllList();
		List<String> characters = Arrays.asList("T2409261655596715454", "T2411041523464799815", "T2410281053472800669", "T2410281322164872206", "T2404281011412773185");
		for (Waybill waybill : list) {
			if (!characters.contains(waybill.getShippingNoteNumber())) {
				if (waybill.getStatus() == 4) {
					if (waybill.getUnloadTime() == null) {
						// 没有实际发车取预计到达时间
						Waybill update = new Waybill();
						update.setId(waybill.getId());
						update.setUnloadTime(waybill.getGoodsReceiptDateTime());
						waybill.setUnloadTime(waybill.getGoodsReceiptDateTime());
						waybillService.updateWaybill(update);
					}
					if (waybill.getLoadTime() == null) {
						// 没有实际发车取预计到达时间
						Waybill update = new Waybill();
						update.setId(waybill.getId());
						update.setLoadTime(waybill.getDespatchActualDateTime());
						waybill.setLoadTime(waybill.getDespatchActualDateTime());
						waybillService.updateWaybill(update);
					}
				}
				if (waybill.getStatus() == 3) {
					if (waybill.getLoadTime() == null) {
						// 没有实际发车时间取预计发车时间
						Waybill update = new Waybill();
						update.setId(waybill.getId());
						update.setLoadTime(waybill.getDespatchActualDateTime());
						waybill.setLoadTime(waybill.getDespatchActualDateTime());
						waybillService.updateWaybill(update);
					}
				}
			}
		}

		for (Waybill waybill : list) {
			if (!characters.contains(waybill.getShippingNoteNumber())) {
				if (waybill.getStatus() == 4) {
					if (waybill.getLoadTime().compareTo(waybill.getUnloadTime()) >= 0) {
						// 实际发车>=实际到达时间 取预计发车
						Waybill update = new Waybill();
						update.setId(waybill.getId());
						update.setLoadTime(waybill.getDespatchActualDateTime());
						waybill.setLoadTime(waybill.getDespatchActualDateTime());
						waybillService.updateWaybill(update);
					}
				}

				if (waybill.getStatus() > 2) {
					if (waybill.getLoadTime().compareTo(waybill.getCreateTime()) < 0) {
						// 实际发车<创建时间 取创建时间
						Waybill update = new Waybill();
						update.setId(waybill.getId());
						update.setLoadTime(waybill.getCreateTime());
						waybill.setLoadTime(waybill.getCreateTime());
						waybillService.updateWaybill(update);
					}
				}
			}
		}
		return CommonResult.success();
	}

	/**
	 * 查询要到预计发车时间，但是还没发车的运单。
	 */
	@Override
	public void todoConfirmDepartureWaybillList() {
		List<Waybill> waybillList = waybillMapperEx.getConfirmDepartureWaybillList();
		if (null != waybillList && !waybillList.isEmpty()) {
			List<Long> waybillIds = waybillList.stream().map(Waybill::getId).collect(Collectors.toList());
			WaybillReq waybillReq = new WaybillReq();
			waybillReq.setWaybillIds(waybillIds);
			waybillService.updateWaybillStatusForConfirmDeparture(waybillReq);
		}
	}

	/**
	 * 预计发车时间到了，就自动发车
	 *
	 * @param waybillReq
	 * @return
	 */
	@Override
	public int updateWaybillStatusForConfirmDeparture(WaybillReq waybillReq) {
		if (null == waybillReq || StringUtils.isEmpty(waybillReq.getWaybillIds())) {
			return 0;
		}
		return waybillMapperEx.updateWaybillStatusForConfirmDeparture(waybillReq);
	}

	@Value("${transfer.shaoDaoDBName}")
	private String shandaoDBName;
	@Value("${transfer.fourDBName}")
	private String fourDBName;

	/**
	 * 查询善道和4.0的运单状态不一样的，重新同步运单。
	 */
	@Override
	public void todoWaybillResynchronize() {
		List<Waybill> waybillList = waybillMapperEx.selectWaybillListForInconsistentStatus(shandaoDBName, fourDBName);
		if (StringUtils.isEmpty(waybillList)) {
			log.info("没有查询到状态不一样的运单，无需同步");
			return;
		}
		List<Long> waybillIds = waybillList.stream().map(Waybill::getId).collect(Collectors.toList());
		SynchronousShanDaoReq synchronousShanDaoReq = new SynchronousShanDaoReq();
		synchronousShanDaoReq.setWaybillIds(waybillIds);
		fourDataHandlerService.synchronousFullData(synchronousShanDaoReq);
		log.info("同步的运单id，{}", JSONArray.toJSONString(waybillIds));
	}

	/**
	 * 运单确认到达扩展处理
	 *
	 * @param waybillIds
	 */
	@Override
	public void waybillConfirmArrival(List<Long> waybillIds) {
		if (CommonUtil.isNullOrEmpty(waybillIds)) {
			return;
		}
		List<Waybill> waybills = waybillMapper.selectWaybillByIds(waybillIds);
		if (null == waybills || waybills.isEmpty()) {
			return;
		}
		waybills = waybills.stream().filter(f -> 4 == f.getStatus()).collect(Collectors.toList());
		waybillIds = waybills.stream().filter(f -> 4 == f.getStatus()).map(Waybill::getId).collect(Collectors.toList());
		if (waybills.isEmpty()) {
			return;
		}
		// 校验风控信息
		waybillMapperEx.updateRiskResultGradeByIds(waybillIds);
		waybillRiskService.waybillsRiskCheckByIds(waybillIds, StringUtils.isEmpty(SecurityUtils.getNickname()) ? "系统校验" : SecurityUtils.getNickname());
		// 修改es中运单
		esWaybillService.updateEsWaybillInfos(waybills);
		// 税务上报运单处理，下述方法会过滤非需上报运单
		ywkUploadService.doArriveRelatedUpload(waybills);
		// 同步善道4.0
		// waybillService.pushWaybillReachShanDao(waybillIds, 0);
		// 更新承运人服务信息（更新服务时间、服务次数、运输里程（km））
		waybills.forEach(l -> {
			actualCarrierInfoService.saveCarrierExtendServeInfo(l.getActualCarrierId(), l.getMileage().longValue());
		});
	}

	@Override
	public List<Waybill> getWaybillInfoByCodes(List<String> shippingNoteNumbers) {
		return waybillMapperEx.selectWaybillList(shippingNoteNumbers);
	}

	@Override
	public StatementMoneyRes statementMoneyTotal(WaybillReq waybillReq) {
		Map<String, Object> map = new HashMap<>();
		map.put("customerId", SecurityUtils.getShipperId());
		if (waybillReq.getBusinessModelId() != null) {
			map.put("BusinessModelId", waybillReq.getBusinessModelId());
		}
		if (waybillReq.getIsRecorder() != null) {
			map.put("isRecorder", waybillReq.getIsRecorder());
		}
		if (!StringUtils.isEmpty(waybillReq.getContractIds())) {
			map.put("contractIds", waybillReq.getContractIds());
		}
		if (waybillReq.getFrameworkContractIds() != null && !waybillReq.getFrameworkContractIds().isEmpty()) {
			map.put("frameworkContractIds", waybillReq.getFrameworkContractIds());
		}

		if (StringUtils.isNotBlank(waybillReq.getShippingNoteNumber())) {
			map.put("shippingNoteNumber", waybillReq.getShippingNoteNumber());
		}
		if (StringUtils.isNotBlank(waybillReq.getCreateTimeStart())) {
			map.put("createTimeStart", waybillReq.getCreateTimeStart() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(waybillReq.getCreateTimeEnd())) {
			map.put("createTimeEnd", waybillReq.getCreateTimeEnd() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillReq.getPayTimeStart())) {
			map.put("payTimeStart", waybillReq.getPayTimeStart() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(waybillReq.getPayTimeEnd())) {
			map.put("payTimeEnd", waybillReq.getPayTimeEnd() + " 23:59:59");
		}
		if (StringUtils.isNotBlank(waybillReq.getRemark())) {
			map.put("remark", waybillReq.getRemark());
		}
		if (StringUtils.isNotBlank(waybillReq.getDescriptionOfGoods())) {
			map.put("descriptionOfGoods", waybillReq.getDescriptionOfGoods());
		}
		if (waybillReq.getFeeUnit() != null) {
			map.put("feeUnit", waybillReq.getFeeUnit());
		}
		if (waybillReq.getNextCustomerId() != null) {
			map.put("nextCustomerId", waybillReq.getNextCustomerId());
		}
		if (StringUtils.isNotBlank(waybillReq.getBillHead())) {
			map.put("billHead", waybillReq.getBillHead());
		}
		if (StringUtils.isNotNull(waybillReq.getTaxUploadState())) {
			// 税务上报状态 0 不上报 1 上报异常 2 上报正常
			if (waybillReq.getTaxUploadState() == 0) {
				map.put("isTaxUpload", 1);
			}
			if (waybillReq.getTaxUploadState() == 1 || waybillReq.getTaxUploadState() == 2) {
				map.put("isTaxUpload", 0);
			}
			if (waybillReq.getTaxUploadState() == 1) {
				map.put("taxUploadFail", 0);
			}
			if (waybillReq.getTaxUploadState() == 2) {
				map.put("taxUploadSuccess", 0);
			}
		}
		if (StringUtils.isNotNull(waybillReq.getRiskResultGrade())) {
			map.put("riskResultGrade", waybillReq.getRiskResultGrade());
		}
		StatementMoneyRes statementMoneyRes = new StatementMoneyRes();
		if (SHIPPER.equals(SecurityUtils.getClientType())) {
			statementMoneyRes = waybillMapperEx.tenantStatementMoneyTotal(map);
		} else if (FINANCE.equals(SecurityUtils.getClientType())) {
			statementMoneyRes = waybillMapperEx.FinanceStatementMoneyTotal(map);
		}
		return statementMoneyRes;
	}

	@Override
	public List<WaybillProxyInvoiceRes> proxyInvoiceWaybillList(WaybillReq waybillReq) {
		if (StringUtils.isNotBlank(waybillReq.getDriverInfo())) {
			if (ValidateUtils.checkIdentityNo15(StringUtils.deleteWhitespace(waybillReq.getDriverInfo())) || ValidateUtils.checkIdentityNo(StringUtils.deleteWhitespace(waybillReq.getDriverInfo()))) {
				waybillReq.setDrivingLicense(waybillReq.getDriverInfo().trim());
			} else {
				waybillReq.setDriverName(waybillReq.getDriverInfo().trim());
			}
		}

		if (waybillReq.getCustomerId() != null) {
			FrameworkContractSubchain subchainReq = new FrameworkContractSubchain();
			subchainReq.setIsLeaf(0);
			subchainReq.setParentCustomerId(waybillReq.getCustomerId());
			subchainReq.setFreightForwarderId(waybillReq.getFreightForwarderId());
			List<FrameworkContractSubchain> subchains = frameworkContractSubchainMapper.selectFrameworkContractSubchainList(subchainReq);
			if (!CommonUtil.isNullOrEmpty(subchains)) {
				waybillReq.setContractIds(subchains.stream().map(FrameworkContractSubchain::getContractId).collect(Collectors.toList()));
			} else {
				// 如果根据项目转包链关联关系没有查到，说明没有运单
				return new ArrayList<>();
			}
			waybillReq.setCustomerId(null);
		}

		// 处理开票相关
		if (StringUtils.isNotEmpty(waybillReq.getInvoiceNumber()) || StringUtils.isNotEmpty(waybillReq.getInvoiceStartTime()) || StringUtils.isNotEmpty(waybillReq.getInvoiceEndTime())) {
			WaybillQuery waybillQuery = new WaybillQuery();
			waybillQuery.setPageNum(null);
			waybillQuery.setPageSize(null);
			waybillQuery.setAuditFreightForwarderId(waybillReq.getFreightForwarderId());
			waybillQuery.setInvoiceNumber(waybillReq.getInvoiceNumber());
			waybillQuery.setInvoiceStartTime(waybillReq.getInvoiceStartTime());
			waybillQuery.setInvoiceEndTime(waybillReq.getInvoiceEndTime());
			List<Waybill> waybills = financeStatementBillMapperEx.selectWaybillIdsByFinanceStatementInfo(waybillQuery);
			// 如果未查询到开票相关运单给个查不到得值
			if (waybills.isEmpty()) {
				List<String> str = new ArrayList<>();
				str.add("查询查不到得运单号");
				waybillReq.setShippingNoteNumbers(str);
				// 处理es查询条数
				List<Long> ids = new ArrayList<>();
				ids.add(Long.MAX_VALUE);
				waybillReq.setWaybillIds(ids);
			} else {
				waybillReq.setShippingNoteNumbers(waybills.stream().map(Waybill::getShippingNoteNumber).collect(Collectors.toList()));
				waybillReq.setWaybillIds(waybills.stream().map(Waybill::getId).collect(Collectors.toList()));
			}
		}

		List<WaybillProxyInvoiceRes> list = waybillMapperEx.proxyInvoiceWaybillList(waybillReq);

		if (CommonUtil.isNullOrEmpty(list)) {
			return new ArrayList<>();
		}
		// 处理开票信息相关
		Map<Long, WaybillTableRes> invoiceMap = new HashMap<>();
		if (!list.isEmpty()) {
			List<Long> waybillIds = list.stream().map(WaybillProxyInvoiceRes::getWaybillId).collect(Collectors.toList());
			List<WaybillTableRes> invoiceList = financeStatementBillMapperEx.selectInvoiceNumbersByWaybillIds(waybillIds, null, waybillReq.getFreightForwarderId());
			if (null != invoiceList)
				invoiceMap = invoiceList.stream().collect(Collectors.toMap(WaybillTableRes::getWaybillId, Function.identity()));
		}

		for (WaybillProxyInvoiceRes waybill : list) {
			// 处理开票
			if (invoiceMap.containsKey(waybill.getWaybillId())) {
				WaybillTableRes invoice = invoiceMap.get(waybill.getWaybillId());
				waybill.setInvoiceNumber(invoice.getInvoiceNumber());
			}
			// 计算 含税承运费（元）：司机代开代缴的费用=承运费/（1-3%）与 额外费用总计
			BigDecimal inclusiveTransportFee = this.calculateTaxInclusiveTransportFee(waybill.getPayFare());
			waybill.setInclusiveTransportFee(inclusiveTransportFee);
			waybill.setPurchaseInvoiceStatus("未开票");
			// 结算数量单位组合，给导出使用
			waybill.setFeeAmountAndUnit(waybill.getFeeAmount().stripTrailingZeros().toPlainString() + waybill.getFeeUnit());
		}

		return list;
	}

	@Override
	@Transactional
	public CommonResult<?> settingProxyInvoice(WaybillProxyInvoiceReq req) {

		if (CommonUtil.isNullOrEmpty(req.getWaybillId())) {
			return CommonResult.error("运单id为空");
		}
		if (CommonUtil.isEmptyOrZero(req.getProxyInvoiceState())) {
			return CommonResult.error("申请代开状态为空");
		}
		if (3 == req.getProxyInvoiceState() && StringUtils.isEmpty(req.getRefuseReason())) {
			return CommonResult.error("备注信息为空");
		}
		for (Long waybillId : req.getWaybillId()) {
			Waybill waybill = waybillMapper.selectWaybillById(waybillId);
			if (null == waybill) {
				return CommonResult.error("未查到相关运单信息");
			}
			if (waybill.getProxyInvoiceState() <= 1) {
				return CommonResult.error("该运单无税务代开资质");
			}
			// 修改运单信息
			Waybill modifyQueryWrapper = new Waybill();
			modifyQueryWrapper.setId(waybillId);
			modifyQueryWrapper.setProxyInvoiceState(req.getProxyInvoiceState());
			waybillMapper.updateWaybill(modifyQueryWrapper);
			// 代开信息
			WaybillProxyInvoice queryWrapper = new WaybillProxyInvoice();
			// queryWrapper.setState(1);//只查询申请状态
			queryWrapper.setFreightForwarderId(SecurityUtils.getFreightForwarderId());
			queryWrapper.setWaybillId(waybillId);
			WaybillProxyInvoice waybillProxyInvoice = waybillProxyInvoiceMapper.selectWaybillProxyInvoiceList(queryWrapper).stream().findFirst().orElse(null);
			if (null == waybillProxyInvoice) {
				WaybillProxyInvoice proxyInvoice = new WaybillProxyInvoice();
				proxyInvoice.setId(TextUtil.getTimeSequenceID(5));
				proxyInvoice.setWaybillId(waybillId);
				proxyInvoice.setFreightForwarderId(SecurityUtils.getFreightForwarderId());
				proxyInvoice.setState(4 == req.getProxyInvoiceState() ? 0 : 1);// 已申请代开的就是生效状态其他都是申请状态
				proxyInvoice.setProxyInvoiceState(req.getProxyInvoiceState());
				proxyInvoice.setRefuseReason(req.getRefuseReason());
				proxyInvoice.setCreateBy(SecurityUtils.getNickname());
				proxyInvoice.setCreateTime(new Date());
				proxyInvoice.setUpdateBy(SecurityUtils.getNickname());
				proxyInvoice.setUpdateTime(new Date());
				waybillProxyInvoiceMapper.insertWaybillProxyInvoice(proxyInvoice);
			} else {
				// todo ?一直在原数据上进行修改？撤销也是只修改状态？
				waybillProxyInvoice.setState(4 == req.getProxyInvoiceState() ? 0 : 1);// 已申请代开的就是生效状态其他都是申请状态
				waybillProxyInvoice.setProxyInvoiceState(req.getProxyInvoiceState());
				waybillProxyInvoice.setRefuseReason(req.getRefuseReason());
				waybillProxyInvoice.setUpdateBy(SecurityUtils.getNickname());
				waybillProxyInvoice.setUpdateTime(new Date());
				waybillProxyInvoiceMapper.updateWaybillProxyInvoice(waybillProxyInvoice);
			}

		}
		// 申请代开推送善道
		if (2 == req.getProxyInvoiceState()) {
			waybillService.pushWaybillReachShanDao(req.getWaybillId(), 1);
		} else if (4 == req.getProxyInvoiceState()) {
			waybillService.pushWaybillReachShanDao(req.getWaybillId(), 0);
		}

		return CommonResult.success();
	}

	@Override
	public CommonResult<?> selectProxyInvoiceWaybillListSum(WaybillReq waybillReq) {
		if (StringUtils.isNotBlank(waybillReq.getDriverInfo())) {
			if (ValidateUtils.checkIdentityNo15(StringUtils.deleteWhitespace(waybillReq.getDriverInfo())) || ValidateUtils.checkIdentityNo(StringUtils.deleteWhitespace(waybillReq.getDriverInfo()))) {
				waybillReq.setDrivingLicense(waybillReq.getDriverInfo().trim());
			} else {
				waybillReq.setDriverName(waybillReq.getDriverInfo().trim());
			}
		}

		if (waybillReq.getCustomerId() != null) {
			FrameworkContractSubchain subchainReq = new FrameworkContractSubchain();
			subchainReq.setIsLeaf(0);
			subchainReq.setParentCustomerId(waybillReq.getCustomerId());
			subchainReq.setFreightForwarderId(waybillReq.getFreightForwarderId());
			List<FrameworkContractSubchain> subchains = frameworkContractSubchainMapper.selectFrameworkContractSubchainList(subchainReq);
			if (!CommonUtil.isNullOrEmpty(subchains)) {
				waybillReq.setContractIds(subchains.stream().map(a -> a.getContractId()).collect(Collectors.toList()));
			} else {
				// 如果根据项目转包链关联关系没有查到，说明没有运单
				return CommonResult.success();
			}
			waybillReq.setCustomerId(null);
		}

		// 处理开票相关
		if (StringUtils.isNotEmpty(waybillReq.getInvoiceNumber()) || StringUtils.isNotEmpty(waybillReq.getInvoiceStartTime()) || StringUtils.isNotEmpty(waybillReq.getInvoiceEndTime())) {
			WaybillQuery waybillQuery = new WaybillQuery();
			waybillQuery.setPageNum(null);
			waybillQuery.setPageSize(null);
			waybillQuery.setAuditFreightForwarderId(SecurityUtils.getFreightForwarderId());
			waybillQuery.setInvoiceNumber(waybillReq.getInvoiceNumber());
			waybillQuery.setInvoiceStartTime(waybillReq.getInvoiceStartTime());
			waybillQuery.setInvoiceEndTime(waybillReq.getInvoiceEndTime());
			List<Waybill> waybills = financeStatementBillMapperEx.selectWaybillIdsByFinanceStatementInfo(waybillQuery);
			// 如果未查询到开票相关运单给个查不到得值
			if (waybills.isEmpty()) {
				List<String> str = new ArrayList<>();
				str.add("查询查不到得运单号");
				waybillReq.setShippingNoteNumbers(str);
				// 处理es查询条数
				List<Long> ids = new ArrayList<>();
				ids.add(Long.MAX_VALUE);
				waybillReq.setWaybillIds(ids);
			} else {
				waybillReq.setShippingNoteNumbers(waybills.stream().map(Waybill::getShippingNoteNumber).collect(Collectors.toList()));
				waybillReq.setWaybillIds(waybills.stream().map(Waybill::getId).collect(Collectors.toList()));
			}
		}

		WaybillProxyInvoiceRes res = waybillMapperEx.proxyInvoiceWaybillListSum(waybillReq);
		BigDecimal inclusiveTransportFee = this.calculateTaxInclusiveTransportFee(res.getPayFare());
		res.setInclusiveTransportFee(inclusiveTransportFee);
		return CommonResult.success(res);
	}

	public List<PayeeStatisticsInfo> payeeStatisticsList(Map<String, Object> map) {
		return waybillMapperEx.payeeStatisticsList(map);
	}

	public List<HaulwayStatisticsInfo> haulwayStatisticsList(Map<String, Object> map) {
		return waybillMapperEx.haulwayStatisticsList(map);
	}

	public List<BillStatisticsInfo> billStatisticsListByCustomerIds(Map<String, Object> map) {
		return waybillMapperEx.billStatisticsListByCustomerIds(map);
	}

	public List<BillStatisticsInfo> billStatisticsListByContractIds(Map<String, Object> map) {
		return waybillMapperEx.billStatisticsListByContractIds(map);
	}

	public List<ContractStatisticsInfo> contractStatisticsListByContractId(Map<String, Object> map) {
		return waybillMapperEx.contractStatisticsListByContractId(map);
	}

	public List<ContractStatisticsInfo> contractStatisticsListByCustomerId(Map<String, Object> map) {
		return waybillMapperEx.contractStatisticsListByCustomerId(map);
	}

	public List<PayeeStatisticsCountInfo> payeeStatisticsCount(Map<String, Object> map) {
		return waybillMapperEx.payeeStatisticsCount(map);
	}

	public List<String> payeeStatisticsYearCount(Long freightForwarderId) {
		return waybillMapperEx.payeeStatisticsYearCount(freightForwarderId);
	}

	@Override
	public List<WaybillAuditListRes> freightForwarderWaybillAuditList(WaybillReq waybillReq, HttpServletRequest request) {
		if (StringUtils.isNotBlank(waybillReq.getDriverInfo())) {
			if (ValidateUtils.checkIdentityNo15(StringUtils.deleteWhitespace(waybillReq.getDriverInfo())) || ValidateUtils.checkIdentityNo(StringUtils.deleteWhitespace(waybillReq.getDriverInfo()))) {
				waybillReq.setDrivingLicense(waybillReq.getDriverInfo().trim());
			} else {
				waybillReq.setDriverName(waybillReq.getDriverInfo().trim());
			}
		}
		if (waybillReq.getCustomerId() != null) {
			FrameworkContractSubchain subchainReq = new FrameworkContractSubchain();
			subchainReq.setIsLeaf(0);
			subchainReq.setParentCustomerId(waybillReq.getCustomerId());
			subchainReq.setFreightForwarderId(waybillReq.getFreightForwarderId());
			List<FrameworkContractSubchain> subchains = frameworkContractSubchainMapper.selectFrameworkContractSubchainList(subchainReq);
			if (!CommonUtil.isNullOrEmpty(subchains)) {
				waybillReq.setContractIds(subchains.stream().map(a -> a.getContractId()).collect(Collectors.toList()));
			} else {
				// 如果根据项目转包链关联关系没有查到，说明没有运单
				return new ArrayList<>();
			}
			waybillReq.setCustomerId(null);
		}

		// 处理开票相关
		if (StringUtils.isNotEmpty(waybillReq.getInvoiceNumber()) || StringUtils.isNotEmpty(waybillReq.getInvoiceStartTime()) || StringUtils.isNotEmpty(waybillReq.getInvoiceEndTime())) {
			WaybillQuery waybillQuery = new WaybillQuery();
			waybillQuery.setPageNum(null);
			waybillQuery.setPageSize(null);
			waybillQuery.setAuditFreightForwarderId(SecurityUtils.getFreightForwarderId());
			waybillQuery.setInvoiceNumber(waybillReq.getInvoiceNumber());
			waybillQuery.setInvoiceStartTime(waybillReq.getInvoiceStartTime());
			waybillQuery.setInvoiceEndTime(waybillReq.getInvoiceEndTime());
			List<Waybill> waybills = financeStatementBillMapperEx.selectWaybillIdsByFinanceStatementInfo(waybillQuery);
			// 如果未查询到开票相关运单给个查不到得值
			if (waybills.isEmpty()) {
				List<String> str = new ArrayList<>();
				str.add("查询查不到得运单号");
				waybillReq.setShippingNoteNumbers(str);
				// 处理es查询条数
				List<Long> ids = new ArrayList<>();
				ids.add(Long.MAX_VALUE);
				waybillReq.setWaybillIds(ids);
			} else {
				waybillReq.setShippingNoteNumbers(waybills.stream().map(Waybill::getShippingNoteNumber).collect(Collectors.toList()));
				waybillReq.setWaybillIds(waybills.stream().map(Waybill::getId).collect(Collectors.toList()));
			}
		}

		startPage();
		List<WaybillAuditListRes> list = waybillMapperEx.freightForwarderWaybillAuditList(waybillReq);

		if (CommonUtil.isNullOrEmpty(list)) {
			return new ArrayList<>();
		}
		// 处理开票信息相关
		Map<Long, WaybillTableRes> invoiceMap = new HashMap<>();
		if (!list.isEmpty()) {
			List<Long> waybillIds = list.stream().map(WaybillAuditListRes::getId).collect(Collectors.toList());
			List<WaybillTableRes> invoiceList = financeStatementBillMapperEx.selectInvoiceNumbersByWaybillIds(waybillIds, null, SecurityUtils.getFreightForwarderId());
			if (null != invoiceList) {
				invoiceMap = invoiceList.stream().collect(Collectors.toMap(WaybillTableRes::getWaybillId, Function.identity()));
			}
		}
		for (WaybillAuditListRes w : list) {
			// status 运单状态(0订单 1运单 2已签约(未开始) 3运输中 4已完成 -1删除)
			// transport 运输状态(0:未开始 1:在途中 2:已完成)
			if (Arrays.asList(0, 1, 2).contains(w.getStatus())) {
				w.setTransportStatus(0);
			} else if (3 == w.getStatus()) {
				w.setTransportStatus(1);
			} else if (4 == w.getStatus()) {
				w.setTransportStatus(2);
			}
			// 处理开票
			if (invoiceMap.containsKey(w.getId())) {
				WaybillTableRes invoice = invoiceMap.get(w.getId());
				w.setInvoiceNumber(invoice.getInvoiceNumber());
				w.setInvoiceTime(invoice.getInvoiceTime());
			}

			// 处理地址
			w.setPlaceOfLoading(getAddress(w.getConsignorProvince(), w.getConsignorCity(), w.getConsignorArea(), w.getPlaceOfLoading()));
			w.setGoodsReceiptPlace(getAddress(w.getConsigneeProvince(), w.getConsigneeCity(), w.getConsigneeArea(), w.getGoodsReceiptPlace()));

			w.setConsignorAddersDetails(waybillService.getAllAddress(w.getConsignorProvince(), w.getConsignorCity(), w.getConsignorArea(), w.getPlaceOfLoading()));
			w.setConsigneeAddersDetails(waybillService.getAllAddress(w.getConsigneeProvince(), w.getConsigneeCity(), w.getConsigneeArea(), w.getGoodsReceiptPlace()));
		}

		// 处理运单审核记录
		List<Long> waybillIds = list.stream().map(WaybillAuditListRes::getId).distinct().collect(Collectors.toList());
		Map<Long, List<WaybillAuditRecord>> waybillAuditRecordMap = waybillAuditRecordMapper.selectWaybillAuditRecordListByWaybillIds(waybillIds).stream()
				.collect(Collectors.groupingBy(WaybillAuditRecord::getWaybillId));

		for (WaybillAuditListRes auditRes : list) {
			if (waybillAuditRecordMap.containsKey(auditRes.getId())) {
				List<WaybillAuditRecord> waybillAuditRecords = waybillAuditRecordMap.get(auditRes.getId());
				if (waybillAuditRecords != null) {
					waybillAuditRecords.removeIf(record -> record.getAuditState() == 4);
				}
				Integer count = waybillAuditRecords.size();
				auditRes.setSubmitAuditCount(count);
				if (2 == waybillReq.getTabType()) {
					auditRes.setAuditState(2);
				}
				if (3 != waybillReq.getTabType()) {
					waybillAuditRecords.stream().filter(f -> 2 == f.getAuditState()).max(Comparator.comparingLong(WaybillAuditRecord::getSubmitAuditCount))
							.ifPresent(waybillAuditRecord -> auditRes.setReason(waybillAuditRecord.getReason()));
					waybillAuditRecords.stream().filter(f -> 2 == f.getAuditState()).max(Comparator.comparingLong(WaybillAuditRecord::getSubmitAuditCount))
							.ifPresent(waybillAuditRecord -> auditRes.setBatchId(waybillAuditRecord.getBatchId()));
				}
			} else {
				auditRes.setSubmitAuditCount(1);
			}
			if (auditRes.getBatchId() != null && auditRes.getBatchId() != 0L) {
				auditRes.setFileList(waybillAuditAttachmentInfoMapper.selectWaybillAuditAttachmentByBatchId(auditRes.getBatchId()));
			}
			if (waybillReq.getTabType() == 1 && auditRes.getSubmitAuditCount() > 1 && CommonUtil.isNullOrEmpty(auditRes.getReason())) {
				auditRes.setReason("人工撤销审核");
			}
			// 计算 含税承运费（元）：司机代开代缴的费用=承运费/（1-3%）与 额外费用总计
			BigDecimal inclusiveTransportFee = this.calculateTaxInclusiveTransportFee(auditRes.getPayFare());
			auditRes.setInclusiveTransportFee(inclusiveTransportFee);
			auditRes.setPurchaseInvoiceStatus("未开票");
		}

		return list;

	}

	@Override
	public List<WaybillAuditRes> freightForwarderWaybillAuditRecord(Long id) {
		if (null == id) {
			throw new ServiceException("运单id为空");
		}
		if (null == SecurityUtils.getFreightForwarderId()) {
			throw new ServiceException("当前登录信息异常");
		}

		// 查询非撤销审核的记录
		WaybillAuditRecord queryWrapper = new WaybillAuditRecord();
		queryWrapper.setWaybillId(id);
		queryWrapper.setFreightForwarderId(SecurityUtils.getFreightForwarderId());

		List<WaybillAuditRecord> waybillAuditRecords = waybillAuditRecordMapper.selectWaybillAuditRecordList(queryWrapper).stream().filter(l -> l.getAuditState() != 4) // 过滤撤销审核
				.collect(Collectors.toList());

		// 最终返回的列表
		List<WaybillAuditRes> list = new ArrayList<>();

		// 组装提交审核和审核的日志
		waybillAuditRecords.forEach(l -> {
			long submitCount = l.getSubmitAuditCount();

			// 提交审核
			WaybillAuditRes submitRes = new WaybillAuditRes();
			submitRes.setTitle("第" + CommonUtil.convertToChinese(submitCount) + "次提交审核");
			submitRes.setAuditTime(l.getSubmitAuditTime());
			list.add(submitRes);

			// 审核
			if (Arrays.asList(2, 3).contains(l.getAuditState())) {
				WaybillAuditRes auditRes = new WaybillAuditRes();
				auditRes.setTitle("第" + CommonUtil.convertToChinese(submitCount) + "次审核");
				auditRes.setAuditResult(l.getAuditState());
				auditRes.setReason(l.getReason());
				if (l.getBatchId() != 0L) {
					auditRes.setFileList(waybillAuditAttachmentInfoMapper.selectWaybillAuditAttachmentByBatchId(l.getBatchId()));
				}
				auditRes.setAuditTime(l.getAuditTime());
				list.add(auditRes);
			}
		});

		// 查询撤销审核的记录
		queryWrapper.setAuditState(4);
		List<WaybillAuditRecord> revokeRecords = waybillAuditRecordMapper.selectWaybillAuditRecordList(queryWrapper);

		// 组装撤销审核记录
		for (WaybillAuditRecord l : revokeRecords) {
			WaybillAuditRes revokeRes = new WaybillAuditRes();
			revokeRes.setTitle("撤销审核");
			revokeRes.setAuditTime(l.getAuditTime());
			list.add(revokeRes);
		}

		// 按 auditTime 排序，确保撤销审核插入到正确的位置
		list.sort(Comparator.comparing(WaybillAuditRes::getAuditTime));

		// 处理第一次提交审核
		if (list.isEmpty()) {
			WaybillAuditRes submitRes = new WaybillAuditRes();
			submitRes.setTitle("第一次提交审核");
			WaybillRiskInfo waybillRiskInfo = waybillRiskInfoMapper.selectWaybillRiskInfoByWaybillId(id);
			submitRes.setAuditTime(waybillRiskInfo != null ? waybillRiskInfo.getUpdateTime() : new Date());
			list.add(submitRes);
		}

		return list;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public CommonResult<?> freightForwarderWaybillAudit(WaybillAuditReq req) {
		if (CommonUtil.isNullOrEmpty(req.getWaybillIds())) {
			return CommonResult.error("审核运单为空");
		}
		if (null == req.getAuditState()) {
			return CommonResult.error("审核结果为空");
		}
		if (2 == req.getAuditState() && StringUtils.isEmpty(req.getReason())) {
			return CommonResult.error("审核不通过原因为空");
		}
		Long batchId = (req.getFileList() == null || req.getFileList().isEmpty()) ? 0L : TextUtil.getTimeSequenceID(5);
		req.setBatchId(batchId);
		// 根据运单id批量查询
		List<WaybillAuditRecord> waybillAuditRecords = waybillAuditRecordMapper.selectWaybillAuditRecordListByWaybillIds(req.getWaybillIds());
		List<Long> existWaybillIds = waybillAuditRecords.stream().map(WaybillAuditRecord::getWaybillId).distinct().collect(Collectors.toList());
		// 过滤没有记录运单
		List<Long> noWaybillRecords = req.getWaybillIds().stream().filter(f -> !existWaybillIds.contains(f)).collect(Collectors.toList());
		// List<Long> noWaybillRecords = waybillAuditRecords.stream().map(WaybillAuditRecord::getWaybillId).filter(waybillId -> !req.getWaybillIds().contains(waybillId)).collect(Collectors.toList());
		// 过滤筛选出最后一次修改记录id
		List<Long> existWaybillRecords = waybillAuditRecords.stream().filter(f -> req.getWaybillIds().contains(f.getWaybillId()) && 0 == f.getState())
				/*.collect(Collectors.toMap(WaybillAuditRecord::getWaybillId, Function.identity(), (m1, m2) -> {
				if (m1.getSubmitAuditCount() > m2.getSubmitAuditCount()) {
				return m1;
				} else {
				return m2;
				}
				})).values().stream()*/.map(WaybillAuditRecord::getId).distinct().collect(Collectors.toList());
		// 查询风控总结表获取风控完成时间
		Map<Long, WaybillRiskInfo> riskInfoMap = waybillRiskInfoMapper.selectWaybillRiskInfoByWaybillIds(req.getWaybillIds()).stream()
				.collect(Collectors.toMap(WaybillRiskInfo::getWaybillId, Function.identity(), (m1, m2) -> m1));
		// 修改该批运单审核记录表
		if (!existWaybillRecords.isEmpty()) {
			waybillAuditRecordMapper.batchModifyWaybillAuditRecordByIds(existWaybillRecords, req, SecurityUtils.getNickname());
		}
		// 对没有记录运单进行生成并新增
		noWaybillRecords.forEach(l -> {
			WaybillAuditRecord waybillAuditRecord = new WaybillAuditRecord();
			waybillAuditRecord.setId(TextUtil.getTimeSequenceID(5));
			waybillAuditRecord.setWaybillId(l);
			waybillAuditRecord.setFreightForwarderId(SecurityUtils.getFreightForwarderId());
			waybillAuditRecord.setSubmitAuditCount(1L);
			if (riskInfoMap.containsKey(l)) {
				waybillAuditRecord.setSubmitAuditTime(riskInfoMap.get(l).getUpdateTime());
			} else {
				waybillAuditRecord.setSubmitAuditTime(new Date());
			}
			waybillAuditRecord.setAuditState(req.getAuditState());
			waybillAuditRecord.setAuditTime(new Date());
			waybillAuditRecord.setAuditBy(SecurityUtils.getNickname());
			waybillAuditRecord.setReason(req.getReason());
			waybillAuditRecord.setBatchId(batchId);
			waybillAuditRecordMapper.insertWaybillAuditRecord(waybillAuditRecord);
		});

		// 插入附件表
		Date date = new Date();
		String nickname = SecurityUtils.getNickname();
		if (batchId != 0L) {
			req.getFileList().forEach(l -> {
				l.setRelationId(batchId);
				l.setId(TextUtil.getTimeSequenceID(5));
				l.setCreateBy(nickname);
				l.setCreateTime(date);
				l.setUpdateBy(nickname);
				l.setUpdateTime(date);
				waybillAuditAttachmentInfoMapper.insertWaybillAuditAttachmentInfo(l);
			});
		}

		// 修改运单表人工审核状态
		waybillMapperEx.batchModifyArtificialAuditStateByWaybillIds(req.getWaybillIds(), req.getAuditState());
		return CommonResult.success();
	}

	@Override
	public List<WaybillTransportRiskRes> tenantTransportRiskList(WaybillTransportRiskReq req) {
		if (null == SecurityUtils.getShipperId()) {
			throw new ServiceException("当前登录信息异常");
		}
		req.setCustomerId(SecurityUtils.getShipperId());

		List<WaybillTransportRiskRes> waybillTransportRiskRes = waybillAuditRecordMapper.selectTenantTransportRiskList(req);
		for (WaybillTransportRiskRes waybillTransportRiskRe : waybillTransportRiskRes) {
			if (waybillTransportRiskRe != null && waybillTransportRiskRe.getBatchId() != 0L) {
				waybillTransportRiskRe.setFileList(waybillAuditAttachmentInfoMapper.selectWaybillAuditAttachmentByBatchId(waybillTransportRiskRe.getBatchId()));
			}
		}

		return waybillTransportRiskRes;
	}

	@Override
	@Transactional
	public CommonResult<?> tenantSubmitWaybillAudit(WaybillTransportRiskReq req) {
		if (null == SecurityUtils.getShipperId()) {
			return CommonResult.error("当前登录信息异常");
		}
		if (CommonUtil.isNullOrEmpty(req.getWaybillIds())) {
			return CommonResult.error("需要提要审核运单id为空");
		}
		// 校验运单风控是否正常
		List<Waybill> waybills = waybillMapper.selectWaybillByIds(req.getWaybillIds());
		if (CommonUtil.isNullOrEmpty(waybills)) {
			return CommonResult.error("未查询到需要提交审核运单");
		}
		if (waybills.stream().anyMatch(a -> !Arrays.asList(3, 4).contains(a.getRiskResultGrade()))) {
			return CommonResult.error("运单风控中/风控异常，请处理风险后重试");
		}

		// 查询运单审核记录表
		List<WaybillAuditRecord> waybillAuditRecords = waybillAuditRecordMapper.selectWaybillAuditRecordListByWaybillIds(req.getWaybillIds());

		// 组装数据
		List<WaybillAuditRecord> list = new ArrayList<>();
		waybills.forEach(l -> {
			long count = waybillAuditRecords.stream().filter(f -> f.getWaybillId().equals(l.getId()) && f.getAuditState() != 4).count();// 统计提交次数过滤掉状态为撤销审核的记录
			WaybillAuditRecord waybillAuditRecord = new WaybillAuditRecord();
			waybillAuditRecord.setId(TextUtil.getTimeSequenceID(5));
			waybillAuditRecord.setWaybillId(l.getId());
			waybillAuditRecord.setFreightForwarderId(l.getFreightForwarderId());
			waybillAuditRecord.setSubmitAuditCount(count + 1);
			waybillAuditRecord.setSubmitAuditTime(new Date());
			list.add(waybillAuditRecord);

		});
		// 将运单审核历史记录设置为失效
		waybillAuditRecordMapper.updateWaybillAuditRecordStateByWaybillIds(req.getWaybillIds());
		// 新增数据
		if (!list.isEmpty()) {
			waybillAuditRecordMapper.batchInsertWaybillAuditRecord(list);
		}

		// 修改运单表数据
		waybillMapperEx.batchModifyArtificialAuditStateByWaybillIds(req.getWaybillIds(), 1);
		return CommonResult.success();
	}

	@Override
	public CommonResult<?> waybillAuditQuash(WaybillAuditReq req) {
		if (CommonUtil.isNullOrEmpty(req.getWaybillIds())) {
			return CommonResult.error("审核运单为空");
		}
		List<Waybill> waybillList = waybillMapper.selectWaybillByIds(req.getWaybillIds());
		// 非未结算运单不支持撤销审核
		if (waybillList.stream().anyMatch(waybill -> waybill.getSettleStatus() != 0)) {
			return CommonResult.error("运单已结算，不支持撤销审核");
		}
		if (waybillList.stream().anyMatch(waybill -> !Arrays.asList(0, 9).contains(waybill.getPayApplyStatus()))) {
			return CommonResult.error("运单已进入付款流程，请先在托运人端将运单退回至待申请付款状态");
		}
		req.setAuditState(0);// 待审核
		// 根据运单id批量查询
		List<WaybillAuditRecord> waybillAuditRecords = waybillAuditRecordMapper.selectWaybillAuditRecordListByWaybillIds(req.getWaybillIds());

		// 插入数据，撤销审核
		List<WaybillAuditRecord> quashList = new ArrayList<>();
		waybillList.forEach(l -> {
			WaybillAuditRecord waybillAuditRecord = new WaybillAuditRecord();
			waybillAuditRecord.setId(TextUtil.getTimeSequenceID(5));
			waybillAuditRecord.setWaybillId(l.getId());
			waybillAuditRecord.setFreightForwarderId(l.getFreightForwarderId());
			waybillAuditRecord.setAuditState(4);// 撤销审核
			waybillAuditRecord.setAuditTime(new Date());
			quashList.add(waybillAuditRecord);
		});
		// 新增撤销审核的记录
		if (!quashList.isEmpty()) {
			waybillAuditRecordMapper.insertWaybillAuditRecords(quashList);
		}

		List<WaybillAuditRecord> list = new ArrayList<>();
		List<Long> waitAuditWaybillIds = new ArrayList<>();
		List<Long> auditingWaybillIds = new ArrayList<>();
		waybillList.forEach(l -> {
			if (1 != l.getRiskResultGrade() && 2 != l.getRiskResultGrade()) {// 非中高风险
				long count = waybillAuditRecords.stream().filter(f -> f.getWaybillId().equals(l.getId()) && f.getAuditState() != 4).count();// 统计提交次数过滤掉状态为撤销审核的记录
				WaybillAuditRecord waybillAuditRecord = new WaybillAuditRecord();
				waybillAuditRecord.setId(TextUtil.getTimeSequenceID(5));
				waybillAuditRecord.setWaybillId(l.getId());
				waybillAuditRecord.setFreightForwarderId(l.getFreightForwarderId());
				waybillAuditRecord.setSubmitAuditCount(count + 1);
				Date nowTime = new Date();
				nowTime.setSeconds(nowTime.getSeconds() + 1);
				waybillAuditRecord.setSubmitAuditTime(nowTime);
				list.add(waybillAuditRecord);
				// 需要修改运单表审核状态为审核中的运单id
				auditingWaybillIds.add(l.getId());
			} else {
				// 需要修改运单表审核状态为待审核的运单id
				waitAuditWaybillIds.add(l.getId());
			}
		});

		// 将运单审核历史记录设置为失效
		waybillAuditRecordMapper.updateWaybillAuditRecordStateByWaybillIds(req.getWaybillIds());
		// 新增提交审核记录
		if (!list.isEmpty()) {
			waybillAuditRecordMapper.batchInsertWaybillAuditRecord(list);
		}
		// 修改运单表人工审核状态
		// 需要改成待审核
		if (!waitAuditWaybillIds.isEmpty()) {
			waybillMapperEx.batchModifyArtificialAuditStateByWaybillIds(waitAuditWaybillIds, 0);
		}
		// 需要改成审核中的
		if (!auditingWaybillIds.isEmpty()) {
			waybillMapperEx.batchModifyArtificialAuditStateByWaybillIds(auditingWaybillIds, 1);
		}
		return CommonResult.success();
	}

	private void addErrorToList(List<DispatchError> errorList, int dispatchIndex, String fieldName, String message) {
		String formattedMessage = message; // 只保留错误信息

		for (DispatchError error : errorList) {
			if (error.getDispatchIndex() == dispatchIndex) {
				// **如果已经存在该 dispatchIndex，拼接错误信息**
				error.setFieldNames(error.getFieldNames() + "," + fieldName);
				error.setMessage(error.getMessage() + "；" + formattedMessage);
				return;
			}
		}

		errorList.add(new DispatchError(dispatchIndex, fieldName, formattedMessage));

	}

	public List<DispatchError> mergeDispatchErrors(List<DispatchError> errorList) {
		// 用于存储合并后的错误
		Map<Integer, DispatchError> errorMap = new HashMap<>();

		// 遍历错误列表
		for (DispatchError error : errorList) {
			int index = error.getDispatchIndex();

			// 如果 Map 中已存在该 dispatchIndex，合并 fieldNames 和 message
			if (errorMap.containsKey(index)) {
				DispatchError existingError = errorMap.get(index);

				// 合并字段名
				existingError.setFieldNames(existingError.getFieldNames() + "," + error.getFieldNames());

				// 合并错误信息
				existingError.setMessage(existingError.getMessage() + "；" + error.getMessage());
			} else {
				// 如果 Map 中不存在该 dispatchIndex，则直接添加
				errorMap.put(index, error);
			}
		}

		// 返回合并后的错误列表
		return new ArrayList<>(errorMap.values());
	}

	public List<String> mergeDispatchErrorsToMessage(List<DispatchError> errorList) {
		List<String> mergedMessages = new ArrayList<>();
		Set<Integer> dispatchedIndexes = new HashSet<>();
		Map<Integer, Set<String>> dispatchErrorMessages = new TreeMap<>(); // 使用 TreeMap 自动排序

		// 遍历 errorList，将每个错误信息按 dispatchIndex 分类
		for (DispatchError error : errorList) {
			StringBuilder errorMessage = new StringBuilder();

			// 检查该调度是否已经有过错误提示
			if (!dispatchedIndexes.contains(error.getDispatchIndex())) {
				// 如果没有提示过，添加"第 X 个调度有错误"
				errorMessage.append(String.format("第 %d 个调度有错误：", error.getDispatchIndex()));
				dispatchedIndexes.add(error.getDispatchIndex()); // 标记该调度已经提示过错误
			}

			// 获取该 dispatchIndex 已有的错误信息 Set
			Set<String> existingMessages = dispatchErrorMessages.computeIfAbsent(error.getDispatchIndex(), k -> new HashSet<>());

			// 如果该错误消息还没有加入过，添加到 Set
			if (existingMessages.add(error.getMessage())) {
				// 如果该错误消息是新的，加入错误消息
				errorMessage.append(error.getMessage());
			}

			// 添加到 Map
			dispatchErrorMessages.put(error.getDispatchIndex(), existingMessages);
		}

		// 将排序后的 Map 中的信息合并到 mergedMessages 中
		for (Map.Entry<Integer, Set<String>> entry : dispatchErrorMessages.entrySet()) {
			StringBuilder errorMessage = new StringBuilder();
			errorMessage.append(String.format("第 %d 个调度有错误：", entry.getKey()));

			// 合并该调度的所有错误信息
			for (String message : entry.getValue()) {
				errorMessage.append(message).append(" ； ");
			}

			// 去除最后多余的 "| " 并添加到结果列表
			mergedMessages.add(errorMessage.substring(0, errorMessage.length() - 3));
		}

		return mergedMessages;
	}

	private void rollbackDatabaseChanges() {
		Connection connection = DataSourceUtils.getConnection(dataSource);
		try {
			// 回滚数据库操作
			connection.rollback();
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			DataSourceUtils.releaseConnection(connection, dataSource);
		}
	}

	public Integer queryWaybillCountByCustomerIdAndTime(String dateStartString, String dateEndString, Long customerId) {
		return waybillMapperEx.queryWaybillCountByCustomerIdAndTime(dateStartString, dateEndString, customerId);
	}

	public BigDecimal queryWaybillFareByCustomerIdAndTime(String dateStartString, String dateEndString, Long customerId) {
		return waybillMapperEx.queryWaybillFareByCustomerIdAndTime(dateStartString, dateEndString, customerId);
	}

	public Long selectSignorWaybillListCount(Map<String, Object> map) {
		return waybillMapperEx.selectSignorWaybillListCount(map);
	}

	@Resource
	private PersonVehicleProtocolMapper personVehicleProtocolMapper;

	@Override
	public CommonResult isUploadFile(Long id, Integer type) {
		Waybill waybill = waybillMapper.selectWaybillById(id);
		if (type == 1) {
			// 运输协议
			Integer result = Arrays.asList(0, 4).contains(waybill.getTransportationAgreementState()) ? 1 : -1;
			return CommonResult.success(result);
		} else if (type == 2) {
			// 委托协议
			FreightForwarderCapacityRelation freightForwarderCapacityRelation = forwarderCapacityRelationMapper.selectEffectiveContract(waybill.getAuxiliaryStaffIdentityCard(),
					waybill.getFreightForwarderId());
			Integer result = freightForwarderCapacityRelation.getAuditState() == -1 ? 1 : -1;
			return CommonResult.success(result);
		} else if (type == 3) {
			// 车主声明
			PersonVehicleProtocol relation = new PersonVehicleProtocol();
			relation.setIdentityCard(waybill.getDrivingLicense());
			relation.setVehicleNumber(waybill.getVehicleNumber());
			relation.setVehiclePlateColorCode(waybill.getVehiclePlateColorCode());
			relation.setState(0);
			List<PersonVehicleProtocol> personVehicleProtocols = personVehicleProtocolMapper.selectPersonVehicleProtocolList(relation);
			Integer result = 1;
			for (PersonVehicleProtocol personVehicleProtocol : personVehicleProtocols) {
				if (result == -1) {
					break;
				}
				result = personVehicleProtocols.get(0).getAuditState() == -1 ? 1 : -1;
			}
			return CommonResult.success(result);
		}
		return CommonResult.success(-1);
	}

	@Override
	public String getAddress(String province, String city, String area, String address) {
		if (StringUtils.isEmpty(address)) {
			return "";
		}
		String pca = province + city + area;
		String pc = province + city;
		String pa = province + area;
		if (Arrays.asList("上海市", "北京市", "天津市", "重庆市").contains(province)) {
			String firstAddress = address.replaceAll(pca, "");
			return firstAddress.replaceAll(pa, "");
		}
		if (Arrays.asList("东莞市", "中山市", "嘉峪关市", "儋州市").contains(city)) {
			String firstAddress = address.replaceAll(pca, "");
			return firstAddress.replaceAll(pc, "");
		}
		if (Arrays.asList("省直辖县级行政区划", "自治区直辖县级行政区划").contains(city)) {
			String firstAddress = address.replaceAll(pca, "");
			return firstAddress.replaceAll(pa, "");
		}
		return address.replaceAll(pca, "");
	}

	@Override
	public String getAllAddress(String province, String city, String area, String address) {
		if (Arrays.asList("上海市", "北京市", "天津市", "重庆市").contains(province)) {
			return province + area + address;
		}
		if (Arrays.asList("东莞市", "中山市", "嘉峪关市", "儋州市").contains(city)) {
			return province + city + address;
		}
		if (Arrays.asList("省直辖县级行政区划", "自治区直辖县级行政区划").contains(city)) {
			return province + area + address;
		}
		return province + city + area + address;
	}

	@Override
	public CommonResult<WaybillRes> getWaybillPayById(Long id) {
		WaybillRes waybillResponse = new WaybillRes();
		// 6.7、获取运费支付记录
		List<WaybillApplyRes> tradeFlowList = customerTradeApplyMapperEx.queryCustomerFlowByWaybillId(id);
		waybillResponse.setTradeFlowList(tradeFlowList);
		// 6.8、获取支付凭证附件
		List<WaybillAttachmentInfo> tradeFlowFiles = customerTradeFlowService.selectAndDownloadByWaybillId(id);
		waybillResponse.setTradeFlowAttachmentList(tradeFlowFiles.stream().map(FileModel::of).collect(Collectors.toList()));
		return CommonResult.success(waybillResponse);
	}

	@Override
	public CommonResult<WaybillRes> getWaybillInvoiceById(Long id) {
		WaybillRes waybillResponse = new WaybillRes();
		Long customerId = commonServices.getLoginCustomerId();
		// 6.10、发票信息
		waybillResponse.setInvoiceList(financeStatementBillService.selectInvoicesByWaybillId(id, customerId));
		// 6.15、etc发票信息
		List<EtcInvoice> etcInvoices = etcInvoiceMapper.selectEtcInvoiceByWaybillId(id);
		waybillResponse.setEtcInvoices(etcInvoices);
		// 6.16、代开发票信息
		List<ProxyInvoices> proxyInvoices = proxyInvoicesMapper.selectInvoiceByWaybillId(id);
		waybillResponse.setProxyInvoices(proxyInvoices);
		return CommonResult.success(waybillResponse);
	}

	@Override
	public CommonResult<WaybillRes> getWaybillImageById(Long id) {
		WaybillRes waybillResponse = new WaybillRes();
		WaybillSlave waybillSlave = waybillSlaveService.selectWaybillSlaveById(id);
		DriverWaybillSlaveDetail driver = JSON.parseObject(waybillSlave.getDriverDetail(), DriverWaybillSlaveDetail.class);
		// 2.1、获取司机附件
		List<DriverAttachmentInfo> driverFileList = driverAttachmentInfoService.selectDriverAttachmentInfoByIds(driver.getAttachmentIds());
		List<FileModel> driverFiles = driverFileList.stream().map(FileModel::of).collect(Collectors.toList());
		// 身份证拿承运人表附件
		ActualCarrierInfo carrierInfo = actualCarrierInfoMapper.selectByIdentityCard(driver.getDrivingLicense());
		if (carrierInfo != null) {
			List<ActualCarrierAttachmentInfo> actualCarrierAttachmentInfos = actualCarrierAttachmentInfoService.selectCarrierAttachmentList(carrierInfo.getId());
			if (actualCarrierAttachmentInfos != null && actualCarrierAttachmentInfos.size() > 0) {
				List<FileModel> carrierDriverFiles = actualCarrierAttachmentInfos.stream().map(FileModel::of).collect(Collectors.toList());
				for (FileModel carrierFile : carrierDriverFiles) {
					if (FileNames.ID_CARD_A.equals(carrierFile.getFileName())) {
						driverFiles.add(carrierFile);
					}
					if (FileNames.ID_CARD_B.equals(carrierFile.getFileName())) {
						driverFiles.add(carrierFile);
					}
				}
			}
		}
		//司机附件
		waybillResponse.setDriverAttachmentList(driverFiles);
		// 3.1、获取车辆附件
		VehicleWaybillSlaveDetail vehicle = JSON.parseObject(waybillSlave.getVehicleDetail(), VehicleWaybillSlaveDetail.class);
		List<VehicleAttachmentInfo> vehicleFileList = vehicleAttachmentInfoService.selectVehicleAttachmentInfoByIds(vehicle.getAttachmentIds());
		waybillResponse.setVehicleAttachmentList(vehicleFileList.stream().map(FileModel::of).collect(Collectors.toList()));
		// 获取发货附件
		List<WaybillAttachmentInfo> loadingFiles = waybillAttachmentInfoService.loadingFileList(id);
		waybillResponse.setLoadingAttachmentList(loadingFiles.stream().map(FileModel::of).collect(Collectors.toList()));
		// 获取收货附件
		List<WaybillAttachmentInfo> unloadFiles = waybillAttachmentInfoService.unloadFileList(id);
		waybillResponse.setUnloadAttachmentList(unloadFiles.stream().map(FileModel::of).collect(Collectors.toList()));
		//其他附件
		List<WaybillAttachmentInfo> otherFiles = waybillAttachmentInfoService.otherFileList(id);
		waybillResponse.setOtherAttachmentList(otherFiles.stream().map(FileModel::of).collect(Collectors.toList()));
		return CommonResult.success(waybillResponse);
	}

	@Override
	public CommonResult<WaybillRes> getWaybillAgreementById(Long id) {
		WaybillRes waybillResponse = new WaybillRes();
		Waybill waybill = this.selectWaybillById(id);
		// 获取运输协议附件
		List<WaybillAttachmentInfo> transportFiles = new ArrayList<>();
		List<WaybillUploadTransportation> transportationAttachmentInfos = waybillUploadTransportationMapper.selectJoinAttachmentsByWaybillIds(Arrays.asList(id), Arrays.asList(FileNames.ECONTRACT));
		for (WaybillUploadTransportation waybillUploadTransportation : transportationAttachmentInfos) {
			if (waybillUploadTransportation.getAuditState() != -1) {
				transportFiles = waybillAttachmentInfoService.transportFileList(id);
			}
		}
		WaybillAttachmentInfo generatedAgreementFile = null;
		if (SecurityUtils.getClientType().equals(ClientType.PLATFORM)) {
			// 查询上传省平台时生成的运输协议附件，在网络货运平台系统显示（state=3，如果有多条，取最新的一条）
			generatedAgreementFile = waybillAttachmentInfoService.generatedAgreementFile(id);
			if (null != generatedAgreementFile) {
				transportFiles.add(generatedAgreementFile);
			}
		}
		for (WaybillAttachmentInfo transportFile : transportFiles) {
			transportFile.setFileName("运输协议附件");
			transportFile.setOriginalFileName("运输协议附件");
		}
		waybillResponse.setTransportContractAttachmentList(transportFiles.stream().map(FileModel::of).collect(Collectors.toList()));

		// 委托付款协议
		if (waybill.getPayeeType() == 3) {
			waybillResponse.setEntrustedPaymentFile(findEntrustedPaymentFile(waybill.getAuxiliaryStaffIdentityCard(), waybill.getFreightForwarderId(), waybill.getCreateTime()));
		}
		if (SecurityUtils.getClientType().equals(ClientType.PLATFORM)) {
			waybillResponse.setCustomerFile(findCustomerFile(waybill.getFrameworkContractId()));
		}
		// 6.12、亲属代收协议
		if (waybill.getPayeeType() == 4) {
			DriverFamilyProtocol protocol = driverFamilyService.getFamilyProtocolByWaybillId(id);
			waybillResponse.setFamilyFile(protocol);
		}
		// 6.13、代开协议
		FreightForwarderCapacityRelation proxyRelation = forwarderCapacityRelationMapper.selectEffectiveProxyContractByResource(waybill.getPayeeIdentityCard(), waybill.getFreightForwarderId(), null);
		waybillResponse.setProxyAgreeInfo(proxyRelation);
		// 6.14、车主声明
		List<PersonVehicleProtocol> personVehicleProtocols = personVehicleProtocolMapper.selectByIdCardAndVehicleNumber(waybill.getDrivingLicense(), waybill.getVehicleNumber());
		waybillResponse.setPersonVehicleProtocol(personVehicleProtocols);

		return CommonResult.success(waybillResponse);
	}

	public List<WaybillProxyInvoiceRes> matchmakingProxyInvoiceWaybillList(WaybillReq waybillReq) {
		if (StringUtils.isNotBlank(waybillReq.getDriverInfo())) {
			if (ValidateUtils.checkIdentityNo15(StringUtils.deleteWhitespace(waybillReq.getDriverInfo())) || ValidateUtils.checkIdentityNo(StringUtils.deleteWhitespace(waybillReq.getDriverInfo()))) {
				waybillReq.setDrivingLicense(waybillReq.getDriverInfo().trim());
			} else {
				waybillReq.setDriverName(waybillReq.getDriverInfo().trim());
			}
		}

		if (waybillReq.getCustomerId() != null) {
			FrameworkContractSubchain subchainReq = new FrameworkContractSubchain();
			subchainReq.setIsLeaf(0);
			subchainReq.setParentCustomerId(waybillReq.getCustomerId());
			subchainReq.setFreightForwarderId(waybillReq.getFreightForwarderId());
			List<FrameworkContractSubchain> subchains = frameworkContractSubchainMapper.selectFrameworkContractSubchainList(subchainReq);
			if (!CommonUtil.isNullOrEmpty(subchains)) {
				waybillReq.setContractIds(subchains.stream().map(FrameworkContractSubchain::getContractId).collect(Collectors.toList()));
			} else {
				// 如果根据项目转包链关联关系没有查到，说明没有运单
				return new ArrayList<>();
			}
			waybillReq.setCustomerId(null);
		}

		// 处理开票相关
		if (StringUtils.isNotEmpty(waybillReq.getInvoiceNumber()) || StringUtils.isNotEmpty(waybillReq.getInvoiceStartTime()) || StringUtils.isNotEmpty(waybillReq.getInvoiceEndTime())) {
			WaybillQuery waybillQuery = new WaybillQuery();
			waybillQuery.setPageNum(null);
			waybillQuery.setPageSize(null);
			waybillQuery.setAuditFreightForwarderId(waybillReq.getFreightForwarderId());
			waybillQuery.setInvoiceNumber(waybillReq.getInvoiceNumber());
			waybillQuery.setInvoiceStartTime(waybillReq.getInvoiceStartTime());
			waybillQuery.setInvoiceEndTime(waybillReq.getInvoiceEndTime());
			List<Waybill> waybills = financeStatementBillMapperEx.selectWaybillIdsByFinanceStatementInfo(waybillQuery);
			// 如果未查询到开票相关运单给个查不到得值
			if (waybills.isEmpty()) {
				List<String> str = new ArrayList<>();
				str.add("查询查不到得运单号");
				waybillReq.setShippingNoteNumbers(str);
				// 处理es查询条数
				List<Long> ids = new ArrayList<>();
				ids.add(Long.MAX_VALUE);
				waybillReq.setWaybillIds(ids);
			} else {
				waybillReq.setShippingNoteNumbers(waybills.stream().map(Waybill::getShippingNoteNumber).collect(Collectors.toList()));
				waybillReq.setWaybillIds(waybills.stream().map(Waybill::getId).collect(Collectors.toList()));
			}
		}
		List<WaybillProxyInvoiceRes> list = new ArrayList<>();
		if (waybillReq.getProxyInvoiceState() == 2) {
			list = waybillMapperEx.waitMatchmakingProxyInvoiceWaybillList(waybillReq);
		} else {
			list = waybillMapperEx.matchmakingProxyInvoiceWaybillList(waybillReq);
		}

		if (CommonUtil.isNullOrEmpty(list)) {
			return new ArrayList<>();
		}
		// 处理开票信息相关
		Map<Long, WaybillTableRes> invoiceMap = new HashMap<>();
		if (!list.isEmpty()) {
			List<Long> waybillIds = list.stream().map(WaybillProxyInvoiceRes::getWaybillId).collect(Collectors.toList());
			List<WaybillTableRes> invoiceList = financeStatementBillMapperEx.selectInvoiceNumbersByWaybillIds(waybillIds, null, waybillReq.getFreightForwarderId());
			if (null != invoiceList)
				invoiceMap = invoiceList.stream().collect(Collectors.toMap(WaybillTableRes::getWaybillId, Function.identity()));
		}

		for (WaybillProxyInvoiceRes waybill : list) {
			// 处理开票
			if (invoiceMap.containsKey(waybill.getWaybillId())) {
				WaybillTableRes invoice = invoiceMap.get(waybill.getWaybillId());
				waybill.setInvoiceNumber(invoice.getInvoiceNumber());
			}
			// 计算 含税承运费（元）：司机代开代缴的费用=承运费/（1-3%）与 额外费用总计
			BigDecimal inclusiveTransportFee = this.calculateTaxInclusiveTransportFee(waybill.getPayFare());
			waybill.setInclusiveTransportFee(inclusiveTransportFee);
			if (waybillReq.getFlag() != null && waybillReq.getFlag() != 4) {
				waybill.setPurchaseInvoiceStatus("未开票");
			}

			// 处理进项票状态
			if (waybill.getProxyInvoiceState() == 7) {
				waybill.setInvoiceState(1);
			} else {
				waybill.setInvoiceState(0);
			}

			// 结算数量单位组合，给导出使用
			waybill.setFeeAmountAndUnit(waybill.getFeeAmount().stripTrailingZeros().toPlainString() + waybill.getFeeUnit());
		}

		return list;
	}

	@Transactional
	public CommonResult<?> settingMatchmakingProxyInvoice(WaybillProxyInvoiceReq req) {

		if (CommonUtil.isNullOrEmpty(req.getWaybillId())) {
			return CommonResult.error("运单id为空");
		}
		if (CommonUtil.isEmptyOrZero(req.getProxyInvoiceState())) {
			return CommonResult.error("申请代开状态为空");
		}
		/*if (3 == req.getProxyInvoiceState() && StringUtils.isEmpty(req.getRefuseReason())) {
			return CommonResult.error("备注信息为空");
		}*/
		List<WaybillFullInfoReq> reqList = new ArrayList<>();
		String shippingNoteNumber = "";
		for (Long waybillId : req.getWaybillId()) {
			Waybill waybill = waybillMapper.selectWaybillById(waybillId);
			WaybillSlave waybillSlave = waybillSlaveService.selectWaybillSlaveById(waybillId);
			if (null == waybill) {
				return CommonResult.error("未查到相关运单信息");
			}
			if (waybillSlave == null) {
				return CommonResult.error("未查到相关运单信息");
			}
			if (waybill.getProxyInvoiceState() <= 1) {
				return CommonResult.error("该运单无税务代开资质");
			}
			if (req.getProxyInvoiceState() == 4) {
				if (!Arrays.asList(0, 3).contains(waybillSlave.getUpetcInvoiceStatus())) {
					return CommonResult.error("etc开票未完成,无法代开");
				}
				if (waybill.getSettleStatus() != 2) {
					return CommonResult.error("未结算完成的运单无法申请代开");
				}
			}

			shippingNoteNumber.join(waybill.getShippingNoteNumber(), ",");

			// 修改运单信息
			Waybill modifyQueryWrapper = new Waybill();
			modifyQueryWrapper.setId(waybillId);
			modifyQueryWrapper.setProxyInvoiceState(req.getProxyInvoiceState());
			waybillMapper.updateWaybill(modifyQueryWrapper);
			// 代开信息
			WaybillProxyInvoice queryWrapper = new WaybillProxyInvoice();
			queryWrapper.setFreightForwarderId(SecurityUtils.getFreightForwarderId());
			queryWrapper.setWaybillId(waybillId);
			WaybillProxyInvoice waybillProxyInvoice = waybillProxyInvoiceMapper.selectWaybillProxyInvoiceList(queryWrapper).stream().findFirst().orElse(null);
			if (null == waybillProxyInvoice) {
				WaybillProxyInvoice proxyInvoice = new WaybillProxyInvoice();
				proxyInvoice.setId(TextUtil.getTimeSequenceID(5));
				proxyInvoice.setWaybillId(waybillId);
				proxyInvoice.setFreightForwarderId(SecurityUtils.getFreightForwarderId());
				proxyInvoice.setState(4 == req.getProxyInvoiceState() ? 0 : 1);// 已申请代开的就是生效状态其他都是申请状态
				proxyInvoice.setRefuseReason(req.getRefuseReason());
				proxyInvoice.setCreateBy(SecurityUtils.getNickname());
				proxyInvoice.setProxyInvoiceState(req.getProxyInvoiceState());
				proxyInvoice.setCreateTime(new Date());
				proxyInvoice.setUpdateBy(SecurityUtils.getNickname());
				proxyInvoice.setUpdateTime(new Date());
				proxyInvoice.setType(1);
				waybillProxyInvoiceMapper.insertWaybillProxyInvoice(proxyInvoice);
			} else {
				waybillProxyInvoice.setRefuseReason(req.getRefuseReason());
				waybillProxyInvoice.setProxyInvoiceState(req.getProxyInvoiceState());
				waybillProxyInvoice.setUpdateBy(SecurityUtils.getNickname());
				waybillProxyInvoice.setUpdateTime(new Date());
				waybillProxyInvoiceMapper.updateWaybillProxyInvoice(waybillProxyInvoice);
			}

			// 申请代开
			if (req.getProxyInvoiceState() == 4) {
				reqList.add(assemblyParameters(waybill));
			}
		}
		if (reqList.size() > 0) {
			CommonResult<WaybillFullUploadRes> result = cjhyProxyInvoiceService.uploadWaybillFullInfo(reqList);
			if (CommonResult.isSuccess(result)) {
				if (!result.getData().getFailWaybills().isEmpty()) {
					String err = "";
					for (FailWaybill failWaybill : result.getData().getFailWaybills()) {
						err = err + failWaybill.getFailReason();
					}
					throw new ServiceException(err);
				}
			} else {
				throw new ServiceException(result.getMsg());
			}
		}

		ProxyInvoicesLog log = new ProxyInvoicesLog();
		log.setId(TextUtil.getTimeSequenceID(5));
		log.setShippingNoteNumber(shippingNoteNumber);
		log.setActionType(req.getProxyInvoiceState());
		log.setCreateBy(SecurityUtils.getNickname());
		log.setCreateTime(new Date());
		proxyInvoicesLogService.insterLog(log);

		return CommonResult.success();
	}

	private WaybillFullInfoReq assemblyParameters(Waybill waybill) {
		WaybillFullInfoReq waybillFullInfoReq = new WaybillFullInfoReq();
		waybillFullInfoReq.setShippingNoteNumber(waybill.getShippingNoteNumber());

		PayeeInfo payeeInfo = payeeInfoMapper.selectPayeeInfoById(waybill.getPayeeId());
		// 运单
		Long makeCodeId = TextUtil.getTimeSequenceID(5);
		assemblyWaybill(waybillFullInfoReq, waybill, makeCodeId);

		// 司机车辆
		assemblyDriverAndVehicle(waybillFullInfoReq, waybill, payeeInfo);

		// 货源
		assemblyMakeCode(waybillFullInfoReq, waybill, makeCodeId);

		// 定位信息
		assemblyLocationInfo(waybillFullInfoReq, waybill);

		// 资金流水
		assemblyCapitalFlow(waybillFullInfoReq, waybill, payeeInfo);

		// 申请开票
		ProxyInvoiceApplyReq invoiceApplyReq = new ProxyInvoiceApplyReq();
		invoiceApplyReq.setWaybillNo(waybill.getShippingNoteNumber());
		waybillFullInfoReq.setInvoiceApplyReq(invoiceApplyReq);

		return waybillFullInfoReq;
	}

	private void assemblyCapitalFlow(WaybillFullInfoReq waybillFullInfoReq, Waybill waybill, PayeeInfo payeeInfo) {

		List<CustomerTradeApply> customerTradeApplies = customerTradeApplyMapper.selectCustomerTradeApplyByWaybillIds(Arrays.asList(waybill.getId()));

		List<CapitalFlowReq> list = new ArrayList<>();
		for (CustomerTradeApply customerTradeApply : customerTradeApplies) {
			CapitalFlowReq capitalFlowReq = new CapitalFlowReq();
			capitalFlowReq.setFlowId(customerTradeApply.getApplyPayCode());
			capitalFlowReq.setPayee(payeeInfo.getPayeeName());
			capitalFlowReq.setPhone(payeeInfo.getBankMobile());
			capitalFlowReq.setBankTypeName(payeeInfo.getBankName());
			capitalFlowReq.setBankAccount(payeeInfo.getBankCardNo());
			capitalFlowReq.setPayAmount(customerTradeApply.getApplyMoney().doubleValue());
			capitalFlowReq.setPayTime(DateUtils.dateToStr(customerTradeApply.getPayTime(), DateUtils.YYSS));
			capitalFlowReq.setPayType("32");
			capitalFlowReq.setBankSerialNo(customerTradeApply.getApplyPayCode());

			List<CapitalFlowReq.WaybillData> waybillData = new ArrayList<>();
			CapitalFlowReq.WaybillData data = new CapitalFlowReq.WaybillData();
			data.setWaybillNo(waybill.getShippingNoteNumber());
			waybillData.add(data);
			capitalFlowReq.setWaybillData(waybillData);

			List<CapitalFlowReq.BankVoucher> bankVoucherUrl = new ArrayList<>();
			// CapitalFlowReq.BankVoucher bankVoucher = new CapitalFlowReq.BankVoucher();
			// ankVoucher.setPhotoUrl("https://wlhyfile.56zly.com/tmp_fdd063b9a8f5a4a3362fb4c3ce2c1ca0.jpg");
			// ankVoucherUrl.add(bankVoucher);

			// 获取支付凭证附件
			List<WaybillAttachmentInfo> tradeFlowFiles = customerTradeFlowService.cjhySelectAndDownloadByWaybillId(waybill.getId());
			for (WaybillAttachmentInfo tradeFlowFile : tradeFlowFiles) {
				if (tradeFlowFile.getFileName().equals(FileNames.ONLN_PLTFRM_T_DRVR_PYMNT_VCHR)) {
					CapitalFlowReq.BankVoucher bankVoucher = new CapitalFlowReq.BankVoucher();
					bankVoucher.setPhotoUrl(tradeFlowFile.getFileUrl());
					bankVoucherUrl.add(bankVoucher);
				}
			}
			capitalFlowReq.setBankVoucherUrl(bankVoucherUrl);
			list.add(capitalFlowReq);
		}
		waybillFullInfoReq.setCapitalFlowReq(list);
	}

	private void assemblyLocationInfo(WaybillFullInfoReq waybillFullInfoReq, Waybill waybill) {
		LocationInfoReq locationInfoReq = new LocationInfoReq();
		List<LocationInfoReq.LocationData> locations = new ArrayList<>();
		locationInfoReq.setWaybillNo(waybill.getShippingNoteNumber());

		List<WaybillTransportTrackRes> gpsTrackList = new ArrayList<>();

		if (waybill.getLocateType() == 6 || waybill.getStatus() >= BusinessConstants.WAYBILL_STATUS_IN_TRANSIT) {
			Map<String, Object> gpsTrackMap = new HashMap<String, Object>();

			// 0：未补充1：已补充
			Boolean IsCompleteTrajectory = false;
			// 查询当前订单是否补充过轨迹
			int count = waybillCustomerRelationMapper.selectIsCompleteTrajectoryCount(waybill.getId());
			IsCompleteTrajectory = count > 0;
			// 判断当前订单是否补充过轨迹
			if (IsCompleteTrajectory) {
				VehicleTrackReq vehicleTrackReq = new VehicleTrackReq();
				vehicleTrackReq.setWaybillId(waybill.getId());
				vehicleTrackReq.setQryBtm(waybill.getDespatchActualDateTime());
				vehicleTrackReq.setQryEtm(waybill.getGoodsReceiptDateTime());
				vehicleTrackReq.setVehicleNumber(waybill.getVehicleNumber());
				vehicleTrackReq.setResetFlag("");
				vehicleTrackReq.setVehiclePlateColorCode(waybill.getVehiclePlateColorCode());
				CommonResult<String> result = trackService.getCompleteTrajectory(vehicleTrackReq);
				if (result.getCode().equals("200")) {
					gpsTrackList = JSON.parseArray(result.getData(), WaybillTransportTrackRes.class);
				}
			} else {
				CommonResult<String> result = trackService.getGpsTrackList(waybill.getId());
				if (result.getCode().equals("200")) {
					gpsTrackList = JSON.parseArray(result.getData(), WaybillTransportTrackRes.class);
				}
			}
			// 小程序定位信息
			WaybillMiniprogramTrack mptrackQuery = new WaybillMiniprogramTrack();
			mptrackQuery.setWaybillId(waybill.getId());
			List<WaybillMiniprogramTrack> mptrackList = waybillMiniprogramTrackMapper.selectWaybillMiniprogramTrackList(mptrackQuery);
			if (null != mptrackList && !mptrackList.isEmpty()) {
				for (WaybillMiniprogramTrack miniprogramTrack : mptrackList) {
					WaybillTransportTrackRes trackRes = new WaybillTransportTrackRes();
					BeanUtils.copyProperties(miniprogramTrack, trackRes);
					gpsTrackList.add(trackRes);
				}
			}
		}

		for (WaybillTransportTrackRes track : gpsTrackList) {
			LocationInfoReq.LocationData data = new LocationInfoReq.LocationData();

			data.setCarNo(waybill.getVehicleNumber());
			data.setTs(track.getUtc() != null ? track.getUtc().getTime() : track.getCreateTime().getTime());
			data.setLon(track.getLon());
			data.setLat(track.getLat());

			locations.add(data);
		}

		locationInfoReq.setLocations(locations);
		waybillFullInfoReq.setLocationInfoReq(locationInfoReq);
	}

	private void assemblyWaybill(WaybillFullInfoReq waybillFullInfoReq, Waybill waybill, Long makeCodeId) {
		ProxyWaybillReq waybillReq = new ProxyWaybillReq();
		BeanUtils.copyProperties(waybill, waybillReq);

		// payFare = payfare-etc费用
		WaybillSlave waybillSlave = waybillSlaveMapper.selectWaybillSlaveById(waybill.getId());
		waybillReq.setPayFare(waybill.getPayFare().subtract(waybillSlave.getEtcTaxAmount() == null ? BigDecimal.ZERO : waybillSlave.getEtcTaxAmount()));
		// 装卸货附件
		List<WaybillAttachmentInfo> loadingFiles = waybillAttachmentInfoService.selectWaybillAttachmentList(waybill.getId(), Arrays.asList(FileNames.LOADING));
		String loadingFileUrl = loadingFiles.stream().map(WaybillAttachmentInfo::getFileUrl).collect(Collectors.joining(","));
		waybillReq.setLoadingPhotos(loadingFileUrl);
		waybillReq.setStartPhotos(loadingFileUrl);
		waybillReq.setConfirmWeight(waybill.getFeeAmount().doubleValue());
		waybillReq.setOrderTime(DateUtils.dateToStr(DateUtils.addHour(waybill.getLoadTime(), -1)));
		waybillReq.setLoadTime(DateUtils.dateToStr(waybill.getLoadTime()));
		waybillReq.setUnloadTime(DateUtils.dateToStr(waybill.getUnloadTime()));

		List<WaybillAttachmentInfo> unloadFiles = waybillAttachmentInfoService.selectWaybillAttachmentList(waybill.getId(), Arrays.asList(FileNames.UNLOAD));
		String unloadFileUrl = unloadFiles.stream().map(WaybillAttachmentInfo::getFileUrl).collect(Collectors.joining(","));
		waybillReq.setArrivePhotos(unloadFileUrl);
		waybillReq.setSignPhotos(unloadFileUrl);

		waybillReq.setMakeCodeId(makeCodeId);

		waybillFullInfoReq.setWaybillReq(waybillReq);
	}

	private void assemblyDriverAndVehicle(WaybillFullInfoReq waybillFullInfoReq, Waybill waybill, PayeeInfo payeeInfo) {
		Driver driver = driverMapper.selectDriverById(waybill.getDriverId());
		ProxyDriverReq driverReq = new ProxyDriverReq();
		BeanUtils.copyProperties(driver, driverReq);

		if ("2099-12-31".equals(driver.getIdentityvalidPeriodTo()) || "2099-12-31 00:00:00".equals(driver.getIdentityvalidPeriodTo())) {
			driverReq.setIdcardLongTerm("Y");
		} else {
			driverReq.setIdcardLongTerm("N");
		}
		if ("2099-12-31".equals(driver.getValidPeriodTo()) || "2099-12-31 00:00:00".equals(driver.getValidPeriodTo())) {
			driverReq.setLicLongTerm("Y");
		} else {
			driverReq.setLicLongTerm("N");
		}
		driverReq.setIdentityValidPeriodFrom(driver.getIdentityValidPeriodFrom());
		driverReq.setIdentityvalidPeriodTo(driver.getIdentityvalidPeriodTo());

		int vehicleCount = waybillMapperEx.selectVehicleCount(driver.getId());
		if (vehicleCount == 0) {
			driverReq.setTonDownType("Y");
		} else {
			driverReq.setTonDownType("N");
		}

		driverReq.setDriverLicenseType(driver.getVehicleClass());
		driverReq.setAreaCode(driver.getDrivingLicense().substring(0, 2) + "0000");
		// driverReq.setAreaCode("320400");

		DriverAttachmentInfo driverAttachmentInfo = new DriverAttachmentInfo();
		driverAttachmentInfo.setRelationId(driver.getId());
		driverAttachmentInfo.setState(0);
		List<DriverAttachmentInfo> driverAttachmentInfos = driverAttachmentInfoService.selectDriverAttachmentInfoList(driverAttachmentInfo);
		for (DriverAttachmentInfo attachmentInfo : driverAttachmentInfos) {
			if (FileNames.DRIVER_LICENCE.equals(attachmentInfo.getFileName())) {
				driverReq.setDriverLicensePhotoUrl(attachmentInfo.getFileUrl());
			}
			if (FileNames.DRIVER_TRANSPORT_QUALIFICATION.equals(attachmentInfo.getFileName())) {
				driverReq.setCertPhotoUrl(attachmentInfo.getFileUrl());
			}
			if (FileNames.ID_CARD_A.equals(attachmentInfo.getFileName())) {
				driverReq.setIdCardFrontPhotoUrl(attachmentInfo.getFileUrl());
			}
			if (FileNames.ID_CARD_B.equals(attachmentInfo.getFileName())) {
				driverReq.setIdCardBackPhotoUrl(attachmentInfo.getFileUrl());
			}
		}

		ActualCarrierInfo actualCarrierInfo = actualCarrierInfoMapper.selectByIdentityCard(driver.getDrivingLicense());
		driverReq.setAddress(actualCarrierInfo.getAddress());
		ActualCarrierAttachmentInfo actualCarrierAttachmentInfo = new ActualCarrierAttachmentInfo();
		actualCarrierAttachmentInfo.setRelationId(actualCarrierInfo.getId());
		actualCarrierAttachmentInfo.setState(0);
		List<ActualCarrierAttachmentInfo> actualCarrierAttachmentInfos = actualCarrierAttachmentInfoService.selectActualCarrierAttachmentInfoList(actualCarrierAttachmentInfo);
		for (ActualCarrierAttachmentInfo attachmentInfo : actualCarrierAttachmentInfos) {
			if (FileNames.ID_CARD_A.equals(attachmentInfo.getFileName())) {
				driverReq.setIdCardFrontPhotoUrl(attachmentInfo.getFileUrl());
			}
			if (FileNames.ID_CARD_B.equals(attachmentInfo.getFileName())) {
				driverReq.setIdCardBackPhotoUrl(attachmentInfo.getFileUrl());
			}
		}

		// driverReq.setAddress("江苏常州天宁");
		driverReq.setBankAccount(payeeInfo.getBankCardNo());
		driverReq.setBankTypeName(payeeInfo.getBankName());

		List<PayeeAttachmentInfo> payeeAttachmentInfos = payeeAttachmentInfoService.selectPayeeAttachmentList(payeeInfo.getId(), Arrays.asList(FileNames.BANK_CARD));
		driverReq.setBankCardPhotoUrl(payeeAttachmentInfos.size() == 0 ? "" : payeeAttachmentInfos.get(0).getFileUrl());

		waybillFullInfoReq.setDriverReq(driverReq);

		Vehicle vehicle = vehicleMapper.selectVehicleById(waybill.getVehicleId());
		ProxyVehicleReq vehicleReq = new ProxyVehicleReq();
		BeanUtils.copyProperties(vehicle, vehicleReq);

		vehicleReq.setAxleNumber(1);
		vehicleReq.setVehiclePlateColorName(getVehiclePlateColorName(vehicle.getVehiclePlateColorCode()));

		VehicleAttachmentInfo vehicleAttachmentInfo = new VehicleAttachmentInfo();
		vehicleAttachmentInfo.setRelationId(vehicle.getId());
		vehicleAttachmentInfo.setState(0);
		List<VehicleAttachmentInfo> vehicleAttachmentInfos = vehicleAttachmentInfoService.selectVehicleAttachmentInfoList(vehicleAttachmentInfo);

		for (VehicleAttachmentInfo attachmentInfo : vehicleAttachmentInfos) {
			if (FileNames.VEHICLE_LICENSE.equals(attachmentInfo.getFileName())) {
				vehicleReq.setDrivingLicenseOppoImgUrl(attachmentInfo.getFileUrl());
				vehicleReq.setDrivingLicenseBgImgUrl(attachmentInfo.getFileUrl());
			}
			if (FileNames.VEHICLE_ROAD_TRANSPORT_CERTIFICATE.equals(attachmentInfo.getFileName())) {
				vehicleReq.setRoadTransCertiImgUrl(attachmentInfo.getFileUrl());
			}
		}

		waybillFullInfoReq.setVehicleReq(vehicleReq);

		// 人车合照
		PersonCarReq personCarReq = new PersonCarReq();
		personCarReq.setIdcardNo(waybill.getDrivingLicense());
		personCarReq.setCarNumber(waybill.getVehicleNumber());

		for (DriverAttachmentInfo attachmentInfo : driverAttachmentInfos) {
			if (FileNames.DRIVER__VEHICLE_PICTURE.equals(attachmentInfo.getFileName())) {
				personCarReq.setPersonCarPhoto(attachmentInfo.getFileUrl());
			}
		}
		for (VehicleAttachmentInfo attachmentInfo : vehicleAttachmentInfos) {
			if (FileNames.DRIVER__VEHICLE_PICTURE.equals(attachmentInfo.getFileName())) {
				personCarReq.setPersonCarPhoto(attachmentInfo.getFileUrl());
			}
		}
		waybillFullInfoReq.setPersonCarReq(personCarReq);
	}

	private void assemblyMakeCode(WaybillFullInfoReq waybillFullInfoReq, Waybill waybill, Long makeCodeId) {
		MakeCode makeCode = makeCodeMapper.selectMakeCodeById(waybill.getMakeCodeId());
		ProxyMakeCodeReq makeCodeReq = new ProxyMakeCodeReq();
		BeanUtils.copyProperties(makeCode, makeCodeReq);
		// 重新给id,修改创建时间
		makeCodeReq.setId(TextUtil.getTimeSequenceID(5));
		makeCodeReq.setCreateTime(DateUtils.dateToStr(DateUtils.addHour(waybill.getLoadTime(), -3)));
		makeCodeReq.setListTime(DateUtils.dateToStr(DateUtils.addHour(waybill.getLoadTime(), -2)));

		if (waybill.getFeeUnit() == 1) {
			makeCodeReq.setCargoWeight(waybill.getFeeAmount().doubleValue());
			makeCodeReq.setCargoCubic(waybill.getFeeAmount().doubleValue());
			makeCodeReq.setCargoNumber(1);
		}
		if (waybill.getFeeUnit() == 2) {
			makeCodeReq.setCargoWeight(waybill.getFeeAmount().doubleValue());
			makeCodeReq.setCargoCubic(waybill.getFeeAmount().doubleValue());
			makeCodeReq.setCargoNumber(1);
		}
		if (waybill.getFeeUnit() == 3) {
			makeCodeReq.setCargoWeight(waybill.getFeeAmount().doubleValue());
			makeCodeReq.setCargoCubic(waybill.getFeeAmount().doubleValue());
			makeCodeReq.setCargoNumber(1);
		}

		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(SecurityUtils.getFreightForwarderId());

		ConsigneeInfo eeInfo = consigneeInfoService.selectConsigneeInfoById(waybill.getConsigneeId());
		eeInfo.setContactPhone(freightForwarderInfo.getContactPhone());
		eeInfo.setContact(freightForwarderInfo.getContact());
		eeInfo.setAreaCode(eeInfo.getProvinceCode());
		ConsignorInfo orInfo = consignorInfoService.selectConsignorInfoById(waybill.getConsignorId());
		orInfo.setContactPhone(freightForwarderInfo.getContactPhone());
		orInfo.setContact(freightForwarderInfo.getContact());
		orInfo.setAreaCode(orInfo.getProvinceCode());

		ProxyConsigneeInfo consigneeInfo = new ProxyConsigneeInfo();
		BeanUtils.copyProperties(eeInfo, consigneeInfo);
		ProxyConsignorInfo consignorInfo = new ProxyConsignorInfo();
		BeanUtils.copyProperties(orInfo, consignorInfo);

		makeCodeReq.setConsigneeInfo(consigneeInfo);
		makeCodeReq.setConsignorInfo(consignorInfo);
		makeCodeReq.setId(makeCodeId);
		waybillFullInfoReq.setMakeCodeReq(makeCodeReq);
	}

	private String getVehiclePlateColorName(Integer vehiclePlateColorCode) {
		if (vehiclePlateColorCode == 1) {
			return "蓝色";
		} else if (vehiclePlateColorCode == 2) {
			return "黄色";
		} else if (vehiclePlateColorCode == 3) {
			return "黑色";
		} else if (vehiclePlateColorCode == 4) {
			return "白色";
		} else if (vehiclePlateColorCode == 5) {
			return "绿色";
		} else if (vehiclePlateColorCode == 9) {
			return "其他";
		} else if (vehiclePlateColorCode == 91) {
			return "农黄色";
		} else if (vehiclePlateColorCode == 92) {
			return "农绿色";
		} else if (vehiclePlateColorCode == 93) {
			return "黄绿色";
		} else if (vehiclePlateColorCode == 94) {
			return "渐变绿";
		} else {
			return "";
		}
	}

	public BigDecimal getNoTaxFare(WaybillReq waybillReq) {
		if (StringUtils.isNotBlank(waybillReq.getDriverInfo())) {
			if (ValidateUtils.checkIdentityNo15(StringUtils.deleteWhitespace(waybillReq.getDriverInfo())) || ValidateUtils.checkIdentityNo(StringUtils.deleteWhitespace(waybillReq.getDriverInfo()))) {
				waybillReq.setDrivingLicense(waybillReq.getDriverInfo().trim());
			} else {
				waybillReq.setDriverName(waybillReq.getDriverInfo().trim());
			}
		}

		if (waybillReq.getCustomerId() != null) {
			FrameworkContractSubchain subchainReq = new FrameworkContractSubchain();
			subchainReq.setIsLeaf(0);
			subchainReq.setParentCustomerId(waybillReq.getCustomerId());
			subchainReq.setFreightForwarderId(waybillReq.getFreightForwarderId());
			List<FrameworkContractSubchain> subchains = frameworkContractSubchainMapper.selectFrameworkContractSubchainList(subchainReq);
			if (!CommonUtil.isNullOrEmpty(subchains)) {
				waybillReq.setContractIds(subchains.stream().map(FrameworkContractSubchain::getContractId).collect(Collectors.toList()));
			} else {
				// 如果根据项目转包链关联关系没有查到，说明没有运单
				return BigDecimal.ZERO;
			}
			waybillReq.setCustomerId(null);
		}

		// 处理开票相关
		if (StringUtils.isNotEmpty(waybillReq.getInvoiceNumber()) || StringUtils.isNotEmpty(waybillReq.getInvoiceStartTime()) || StringUtils.isNotEmpty(waybillReq.getInvoiceEndTime())) {
			WaybillQuery waybillQuery = new WaybillQuery();
			waybillQuery.setPageNum(null);
			waybillQuery.setPageSize(null);
			waybillQuery.setAuditFreightForwarderId(waybillReq.getFreightForwarderId());
			waybillQuery.setInvoiceNumber(waybillReq.getInvoiceNumber());
			waybillQuery.setInvoiceStartTime(waybillReq.getInvoiceStartTime());
			waybillQuery.setInvoiceEndTime(waybillReq.getInvoiceEndTime());
			List<Waybill> waybills = financeStatementBillMapperEx.selectWaybillIdsByFinanceStatementInfo(waybillQuery);
			// 如果未查询到开票相关运单给个查不到得值
			if (waybills.isEmpty()) {
				List<String> str = new ArrayList<>();
				str.add("查询查不到得运单号");
				waybillReq.setShippingNoteNumbers(str);
				// 处理es查询条数
				List<Long> ids = new ArrayList<>();
				ids.add(Long.MAX_VALUE);
				waybillReq.setWaybillIds(ids);
			} else {
				waybillReq.setShippingNoteNumbers(waybills.stream().map(Waybill::getShippingNoteNumber).collect(Collectors.toList()));
				waybillReq.setWaybillIds(waybills.stream().map(Waybill::getId).collect(Collectors.toList()));
			}
		}
		return waybillMapperEx.getNoTaxFare(waybillReq);
	}


	@Override
	public CommonResult waybillExport(SPReq spReq) {

		List<SPWaybillRes> retList = new ArrayList<>();

		spReq.setList(Arrays.asList(spReq.getCustomerIds().split(",")));

		// 1、分页查询获取数据
		WaybillQuery waybillQuery = new WaybillQuery();
		waybillQuery.setList(spReq.getList());
		waybillQuery.setCreateTimeStart(spReq.getStartTime());
		// 生产环境查询善道数据
		if (Arrays.asList("prd").contains(SpringUtils.getActiveProfile())) {
			waybillQuery.setFreightForwarderId(171256450117183964L);
		}

		// 详细地址放入省市区详细信息
		PageHelper.offsetPage(0,5000);
		List<WaybillTableRes> list = selectShipperWaybillListSP(waybillQuery);

		String nextStartTime = "";
		for (WaybillTableRes w : list) {
			String feeUnitString = (w.getFeeUnit() == 1 ? "吨" : (w.getFeeUnit() == 2 ? "方" : (w.getFeeUnit() == 3 ? "车" : "")));
			w.setFeeUnitToString(feeUnitString);

			String pol = getAddress(w.getConsignorProvince(), w.getConsignorCity(), w.getConsignorArea(), w.getPlaceOfLoading());
			w.setPlaceOfLoading(getAllAddress(w.getConsignorProvince(), w.getConsignorCity(), w.getConsignorArea(), pol));

			String grp = getAddress(w.getConsigneeProvince(), w.getConsigneeCity(), w.getConsigneeArea(), w.getGoodsReceiptPlace());
			w.setGoodsReceiptPlace(getAllAddress(w.getConsigneeProvince(), w.getConsigneeCity(), w.getConsigneeArea(), grp));

			if (w.getPrepayType() != null && !"2".equals(w.getPrepayType()) && w.getOilCardNo() != null && "0".equals(w.getOilCardNo())) {
				w.setOilCardNo("");
			}
			// 方
			if (w.getFeeUnit() == 2) {
				w.setLoadingWeight(w.getLoadingCube());
				w.setUnloadWeight(w.getUnloadCube());
			}
			// 设置装卸货数量，不是小程序的不显示
			if (w.getResource() != 6) {
				w.setLoadingWeight(null);
				w.setUnloadWeight(null);
			}
			// 设置大宗货源单价，不是小程序的显示0 对立方米处理
			w.setFeePriceToString("0");
			if (w.getSupplyType() == 1) {
				if (ObjectUtils.isNotEmpty(w.getFeePrice())) {
					w.setFeePriceToString(NumberFormat.getNumberInstance().format(w.getFeePrice()) + " 元/");
					w.setFeePriceToString(w.getFeePriceToString() + feeUnitString);
				}
			}
			if (w.getFeePriceString() == null || "0".equals(w.getFeePriceString()) || "0.00".equals(w.getFeePriceString())) {
				w.setFeePriceString("0");
			} else {
				w.setFeePriceString(w.getFeePriceString() + " 元/" + feeUnitString);
			}

			SPWaybillRes res = new SPWaybillRes();
			BeanUtils.copyProperties(w,res);


			res.setBillDate(DateUtils.dateToStr(w.getBillDate(),DateUtils.YYMMDD));
			res.setDespatchActualDateTime(DateUtils.dateToStr(w.getDespatchActualDateTime(),DateUtils.YYSS));
			res.setGoodsReceiptDateTime(DateUtils.dateToStr(w.getGoodsReceiptDateTime(),DateUtils.YYSS));
			res.setActualStartTime(DateUtils.dateToStr(w.getActualStartTime(),DateUtils.YYSS));
			res.setActualEndTime(DateUtils.dateToStr(w.getActualEndTime(),DateUtils.YYSS));
			res.setCreateTime(DateUtils.dateToStr(w.getCreateTime(),DateUtils.YYSS));
			res.setPayTime(DateUtils.dateToStr(w.getPayTime(),DateUtils.YYSS));
			res.setInvoiceTime(DateUtils.dateToStr(w.getInvoiceTime(),DateUtils.YYSS));
			res.setLoadingProvince(w.getConsignorProvince());
			res.setLoadingCity(w.getConsignorCity());
			res.setLoadingArea(w.getConsignorArea());
			res.setLoadingLat(w.getConsignorLatitude());
			res.setLoadingLon(w.getConsignorLongitude());
			res.setUnloadProvince(w.getConsigneeProvince());
			res.setUnloadCity(w.getConsigneeCity());
			res.setUnloadArea(w.getConsigneeArea());
			res.setUnloadLat(w.getConsigneeLatitude());
			res.setUnloadLon(w.getConsigneeLongitude());
			res.setReceiveUpstream("——".equals(w.getReceiveUpstream()) ? null : new BigDecimal(w.getReceiveUpstream()));
			res.setFeePriceString(w.getFeePriceString());
			res.setFeePrice(w.getFeePriceToString());
			res.setFeeAmount(BigDecimal.valueOf(w.getFeeAmount()));
			res.setContractCode(w.getFrameworkContractCode());
			retList.add(res);
		}

		return CommonResult.success(retList);
	}


	@Resource
	private CustomerTradeApplyServiceImpl customerTradeApplyService;

	@Override
	public CommonResult payExport(SPReq spReq) {

		// 生产环境查询善道数据
		if (Arrays.asList("prd").contains(SpringUtils.getActiveProfile())) {
			spReq.setFreightForwarderId(171256450117183964L);
		}

		spReq.setList(Arrays.asList(spReq.getCustomerIds().split(",")));

		List<SPPayRes> retList = new ArrayList<>();

		TradeFlowQueryReq req = new TradeFlowQueryReq();
		req.setList(spReq.getList());
		req.setStartTime(spReq.getStartTime());

		PageHelper.offsetPage(0,5000);
		List<FlowResEx> flowResExes = customerTradeApplyService.queryCustomerFlowWithWaybillSP(req);

		for (FlowResEx flowResEx : flowResExes) {
			SPPayRes res = new SPPayRes();
			BeanUtils.copyProperties(flowResEx,res);

			res.setShippingNoteNumber(flowResEx.getWaybillCode());
			res.setTelephone(flowResEx.getDriverPhone());
			res.setVehicleNumber(flowResEx.getPlateNo());
			res.setStatus(flowResEx.getStatusString());
			res.setFrameworkContractCode(flowResEx.getFrameworkContractCode());
			retList.add(res);
		}

		return CommonResult.success(retList);
	}

}
