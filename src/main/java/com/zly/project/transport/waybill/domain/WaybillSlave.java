package com.zly.project.transport.waybill.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运单子表对象 waybill_slave
 *
 * <AUTHOR>
 * @date 2022-04-16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WaybillSlave extends BaseEntity {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("运单子表id")
	private Long id;

	@Excel(name = "装货过磅重量", readConverterExp = "吨=")
	@ApiModelProperty("装货过磅重量（吨）")
	private BigDecimal loadingWeight;

	@Excel(name = "装货过磅体积", readConverterExp = "立=方米")
	@ApiModelProperty("装货过磅体积（立方米）")
	private BigDecimal loadingCube;

	@Excel(name = "卸货过磅重量", readConverterExp = "吨=")
	@ApiModelProperty("卸货过磅重量（吨）")
	private BigDecimal unloadWeight;

	@Excel(name = "卸货过磅体积", readConverterExp = "立=方米")
	@ApiModelProperty("卸货过磅体积（立方米）")
	private BigDecimal unloadCube;

	@Excel(name = "保险类型(-1:未设置 0:物责险 1:货物险 2:龙琨货运险)")
	@ApiModelProperty(notes = "保险类型", value = "保险类型(-1:未设置 0:物责险 1:货物险 2:龙琨货运险)",allowableValues = "-1:未设置,0:物责险,1:货物险,2:龙琨货运险")
	private Integer policyType;

	@Excel(name = "保单状态(-1:未设置 1:不投保 2:投保受理成功 3:投保受理失败 4:系统异常 5:投保成功 6:投保失败 7:人工处理)")
	@ApiModelProperty(notes = "保单状态", value = "保单状态(-1:未设置 1:不投保 2:投保受理成功 3:投保受理失败 4:系统异常 5:投保成功 6:投保失败 7:人工处理)",allowableValues = "-1:未设置 ,1:不投保 ,2:投保受理成功 ,3:投保受理失败 ,4:系统异常, 5:投保成功, 6:投保失败 ,7:人工处理")
	private Integer policyStatus;

	@Excel(name = "保价金额")
	@ApiModelProperty("保价金额（投保人支付的金额）")
	private BigDecimal policyFare;

	@Excel(name = "保单号")
	@ApiModelProperty("保单号")
	private String policyNumber;

	@Excel(name = "保险公司代码")
	@ApiModelProperty("保险公司代码")
	private String insuranceCompanyCode;

	@Excel(name = "上传网络货运平台说明")
	@ApiModelProperty("上传网络货运平台说明")
	private String uploginkDesc;

	@Excel(name = "上传开具ETC电子发票标识(-1:未设置 0:已设置但未上传 1:车辆已备案 2:运单开始指令已上传 3:运单结束指令已上传 4:不上传 5:车辆备案失败 6:运单开始指令上传失败 7:运单结束指令上传失败 10:历史运单开始指令已上传 11:历史运单结束指令已上传 12:历史运单开始指令上传失败 13:历史运单结束指令上传失败)")
	@ApiModelProperty(notes = "上传开具ETC电子发票标识", value = "上传开具ETC电子发票标识(-1:未设置 0:已设置但未上传 1:车辆已备案 2:运单开始指令已上传 3:运单结束指令已上传 4:不上传 5:车辆备案失败 6:运单开始指令上传失败 7:运单结束指令上传失败 10:历史运单开始指令已上传 11:历史运单结束指令已上传 12:历史运单开始指令上传失败 13:历史运单结束指令上传失败",allowableValues = "-1:未设置 ,0:已设置但未上传, 1:车辆已备案, 2:运单开始指令已上传, 3:运单结束指令已上传, 4:不上传, 5:车辆备案失败, 6:运单开始指令上传失败, 7:运单结束指令上传失败 ,10:历史运单开始指令已上传, 11:历史运单结束指令已上传 ,12:历史运单开始指令上传失败, 13:历史运单结束指令上传失败")
	private Integer upetcStatus;

	@JsonFormat(pattern = "yyyy-MM-dd")
	@Excel(name = "上传开具ETC电子发票时间", width = 30, dateFormat = "yyyy-MM-dd")
	@ApiModelProperty("上传开具ETC电子发票时间")
	private Date upetcTime;

	@Excel(name = "上传开具ETC电子发票说明")
	@ApiModelProperty("上传开具ETC电子发票说明")
	private String upetcDesc;

	@Excel(name = "上传网络货运平台标识(-1:不上传 0:已设置但未上传 1:已上传 2:上传中 3:上传失败 4:上传异常 5:在不上传名单中 6:重复上传失败不再上传)")
	@ApiModelProperty(notes = "上传网络货运平台标识", value = "上传网络货运平台标识(-1:不上传 0:已设置但未上传 1:已上传 2:上传中 3:上传失败 4:上传异常 5:在不上传名单中 6:重复上传失败不再上传)",allowableValues = "-1:不上传 ,0:已设置但未上传 ,1:已上传, 2:上传中, 3:上传失败, 4:上传异常, 5:在不上传名单中, 6:重复上传失败不再上传")
	private Integer uploginkStatus;

	@JsonFormat(pattern = "yyyy-MM-dd")
	@Excel(name = "上传到网络货运平台时间", width = 30, dateFormat = "yyyy-MM-dd")
	@ApiModelProperty("上传到网络货运平台时间")
	private Date uploginkTime;

	@ApiModelProperty("备注")
	private String remark;

	@ApiModelProperty(notes = "风控审核状态", value = "风控审核状态 0未审核 1审核通过 2审核不通过 ",allowableValues =" 0:未审核,1:审核通过, 2:审核不通过" )
	private Integer financingAuditStatus;

	@ApiModelProperty("风控审核时间")
	private Date financingAuditTime;

	@ApiModelProperty("风控不通过原因")
	private String financingAuditRemark;

	@ApiModelProperty(notes = "推送状态", value = "推送状态（0待推送 1推送成功 2推送失败）",allowableValues =" 0:待推送,1:推送成功,2:推送失败")
	private Integer pushStatus;

	/** 推送时间 */
	@ApiModelProperty("推送时间")
	private Date pushTime;

	@ApiModelProperty(notes = "融资状态", value = "融资状态（1融资成功 2融资失败）",allowableValues =" 1:融资成功,2:融资失败")
	private Integer financingStatus;

	@ApiModelProperty(notes = "与上游对账状态", value = "与上游对账状态（0.未对账 1.已对账）",allowableValues = "0:未对账 ,1:已对账")
	private Integer upstreamStatementStatus;

	@ApiModelProperty("与上游对账时间")
	private Date upstreamStatementTime;

	@ApiModelProperty(notes = "上报网络货运列表校验情况", value = "上报网络货运列表  校验情况 0:符合情况 1:不符合情况",allowableValues = "0:符合情况,1:不符合情况")
	private Integer checkState;

	@ApiModelProperty(notes = "上报网络货运校验情况", value = "上报网络货运  校验情况 0:符合情况 1:不符合情况",allowableValues = "0:符合情况,1:不符合情况")
	private Integer agreementGenerateStatus;

	@ApiModelProperty("运输协议生成时间（生成状态更新时间）")
	private Date agreementGenerateTime;

	@ApiModelProperty("运输协议生成备注（生成失败原因等）")
	private String agreementGenerateRemark;

	@ApiModelProperty(notes = "_电子合同状态", value = "_电子合同状态(0 未签约  1待签约  2已签约)",allowableValues = "0:未签约,1:待签约,2:已签约")
	private Integer electronicContractState;

	@ApiModelProperty("电子合同地址")
	private String electronicContractUrl;

	@ApiModelProperty(notes = "数贷通融资状态", value = "数贷通融资状态：（ 0：融资成功或无需融资（默认值），1：融资尚未开始 2：融资进行中）",allowableValues = "0:融资成功或无需融资（默认值）,1:融资尚未开始 ,2:融资进行中")
	private Integer shudaitongFinancingStatus;

	@ApiModelProperty("电子合同唯一编码")
	private String electronicContractCode;

	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty("推送驳回时间")
	private Date overruleTime;

	@ApiModelProperty("运单车辆快照详情json串")
	private String vehicleDetail;

	@ApiModelProperty("运单司机快照详情json串")
	private String driverDetail;

	@ApiModelProperty("运单收款人快照详情json串")
	private String payeeDetail;

	@ApiModelProperty("运单承运人快照详情json串")
	private String carrierDetail;

	@ApiModelProperty("货源规则id")
	private Long makeCodeRuleId;

	@ApiModelProperty(notes = "抹零规则", value = "抹零规则(0不抹零  1角分抹零  2十元以下抹零  3二十元以下抹零)", allowableValues = "0:不抹零,1:角分抹零,2:十元以下抹零,3:二十元以下抹零")
	private Integer zeroingRule;

	@ApiModelProperty("货源规则详情")
	private String makeCodeRuleDetailed;

	@ApiModelProperty(notes = "货主货款计算方式", value = "0填货主货款,单价计算   1填单价,货主货款计算",allowableValues = "0:填货主货款单价计算,1:填单价货主货款计算")
	private Integer receiveFareCalculationType;

	@ApiModelProperty(notes = "运费结算方式", value = "运费结算方式0：按装货数量结算 1：按卸货数量结算 2：按最小值结算",allowableValues = "0:按装货数量结算 1:按卸货数量结算 2:按最小值结算")
	private Integer waybillPaymentType;

	@ApiModelProperty("车队长详情json字符串")
	private String carCaptainDetail;

	@ApiModelProperty("辅助员详情json字符串")
	private String auxiliaryStaffDetail;

	@ApiModelProperty("司机修改回单后是否回单确认(0 已确认  1 未确认)")
	private Integer driverUpdateReceiptState;

	@ApiModelProperty("运输协议编号")
	private String transportationProtocolCode;

	@ApiModelProperty("推送原因")
	private String pushReason;

	@ApiModelProperty("拒绝时间")
	private String refuseTime;

	@ApiModelProperty("拒绝人")
	private String refuseBy;

	@ApiModelProperty("etc开票金额")
	private BigDecimal etcTaxAmount;

	@ApiModelProperty("etc开票状态 0 无需开票 1未开票 2 开票中 3开票完成")
	private Integer upetcInvoiceStatus;

	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty("小程序点击发车时间")
	private Date loadClickTime;

	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty("小程序点击到达时间")
	private Date unloadClickTime;

}
