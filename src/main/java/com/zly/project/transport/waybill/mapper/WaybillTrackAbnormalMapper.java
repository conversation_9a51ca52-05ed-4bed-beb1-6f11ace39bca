package com.zly.project.transport.waybill.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.zly.project.statistics.domain.TrajectoryVerificationCount;
import com.zly.project.transport.waybill.domain.WaybillTrackAbnormal;
import com.zly.project.transport.waybill.domain.res.WaybillTrackAbnormalRes;

/**
 * 运单轨迹异常Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface WaybillTrackAbnormalMapper {
	/**
	 * 查询运单轨迹异常
	 *
	 * @param id
	 *            运单轨迹异常主键
	 * @return 运单轨迹异常
	 */
	public WaybillTrackAbnormal selectWaybillTrackAbnormalById(Long id);

	/**
	 * 查询运单轨迹异常列表
	 *
	 * @param waybillTrackAbnormal
	 *            运单轨迹异常
	 * @return 运单轨迹异常集合
	 */
	public List<WaybillTrackAbnormal> selectWaybillTrackAbnormalList(WaybillTrackAbnormal waybillTrackAbnormal);

	/**
	 * 新增运单轨迹异常
	 *
	 * @param waybillTrackAbnormal
	 *            运单轨迹异常
	 * @return 结果
	 */
	public int insertWaybillTrackAbnormal(WaybillTrackAbnormal waybillTrackAbnormal);

	/**
	 * 修改运单轨迹异常
	 *
	 * @param waybillTrackAbnormal
	 *            运单轨迹异常
	 * @return 结果
	 */
	public int updateWaybillTrackAbnormal(WaybillTrackAbnormal waybillTrackAbnormal);

	/**
	 * 删除运单轨迹异常
	 *
	 * @param id
	 *            运单轨迹异常主键
	 * @return 结果
	 */
	public int deleteWaybillTrackAbnormalById(Long id);

	/**
	 * 批量删除运单轨迹异常
	 *
	 * @param ids
	 *            需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteWaybillTrackAbnormalByIds(Long[] ids);

	List<WaybillTrackAbnormal> selectList(Map<String, Object> map);

	void delByWaybillId(Long waybillId);

	List<WaybillTrackAbnormalRes> projectAnalysis(WaybillTrackAbnormal waybillTrackAbnormal);

	WaybillTrackAbnormalRes projectAnalysisSum(WaybillTrackAbnormal waybillTrackAbnormal);

	int manual(@Param("waybillIds") List<Long> waybillIds, @Param("status") Integer status, @Param("disposeRecord") String disposeRecord);

	TrajectoryVerificationCount trajectoryVerificationCount(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("freightForwarderId") Long id);
}
