package com.zly.project.transport.waybill.mapper;

import java.util.List;

import com.zly.project.transport.waybill.domain.WaybillSlave;

/**
 * 运单子表Mapper接口
 *
 * <AUTHOR>
 * @date 2022-04-16
 */
public interface WaybillSlaveMapper {
	/**
	 * 查询运单子表
	 *
	 * @param id
	 *            运单子表主键
	 * @return 运单子表
	 */
	WaybillSlave selectWaybillSlaveById(Long id);

	/**
	 * 查询运单子表列表
	 *
	 * @param waybillSlave
	 *            运单子表
	 * @return 运单子表集合
	 */
	List<WaybillSlave> selectWaybillSlaveList(WaybillSlave waybillSlave);

	/**
	 * 新增运单子表
	 *
	 * @param waybillSlave
	 *            运单子表
	 * @return 结果
	 */
	int insertWaybillSlave(WaybillSlave waybillSlave);

	/**
	 * 修改运单子表
	 *
	 * @param waybillSlave
	 *            运单子表
	 * @return 结果
	 */
	int updateWaybillSlave(WaybillSlave waybillSlave);

	/**
	 * 删除运单子表
	 *
	 * @param id
	 *            运单子表主键
	 * @return 结果
	 */
	int deleteWaybillSlaveById(Long id);

	/**
	 * 批量删除运单子表
	 *
	 * @param ids
	 *            需要删除的数据主键集合
	 * @return 结果
	 */
	int deleteWaybillSlaveByIds(Long[] ids);

	void updateWaybillSlaveUnPush(WaybillSlave slave);

    List<WaybillSlave> selectWaybillSlaveByWaybillIdsAndRemarkNotEmpty(List<Long> waybillIds);

	int pushWaybillOverrule(List<Long> ids);

	int pushWaybillOverruleByXyd(List<Long> ids);

	int insertWaybillSlaves(List<WaybillSlave> waybillSlaves);

	List<WaybillSlave> selectWaybillSlaveByIds(List<Long> waybillIds);

	List<WaybillSlave> selectWeightCubeByIds(List<Long> waybillIds);

}
