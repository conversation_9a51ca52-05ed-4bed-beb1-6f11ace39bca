package com.zly.project.transport.waybill.domain.res;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zly.common.utils.export.EasyConverter;
import com.zly.framework.aspectj.lang.annotation.Excel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Title WaybillTableRes
 * @Description 运单列表展示字段信息
 * @Create 2023-03-24 16:59
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
public class WaybillTableRes {

	@ApiModelProperty("运单号")
	@Excel(name = "运单号", sort = 0)
	@ExcelProperty(value = "运单号", index = 0)
	private String shippingNoteNumber;

	@ApiModelProperty("关联项目（项目合约名称）")
	@Excel(name = "关联项目", sort = 1)
	@ExcelProperty(value = "关联项目", index = 1)
	private String frameworkContractName;

	@ApiModelProperty("单据日期")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@Excel(name = "单据日期", sort = 2, dateFormat = "yyyy-MM-dd")
	@ExcelProperty(value = "单据日期", index = 2, converter = EasyConverter.DateConverter.class)
	private Date billDate;

	@ApiModelProperty("关联货源（货源别名）")
	@Excel(name = "关联货源", sort = 3)
	@ExcelProperty(value = "关联货源", index = 3)
	private String nameAlias;

	@ApiModelProperty("运输协议单号")
	@Excel(name = "运输协议单号", sort = 4)
	@ExcelProperty(value = "运输协议单号", index = 4)
	private String originalDocumentNumber;

	@ApiModelProperty("运输状态(0:未开始 1:在途中 2:已完成)")
	@Excel(name = "运输状态", sort = 5, readConverterExp = "0=未开始,1=在途中,2=已完成")
	@ExcelProperty(value = "运输状态", index = 5, converter = EasyConverter.TransportStatus.class)
	private Integer transportStatus;

	@ApiModelProperty("运输协议(0:未发送短信 1:已签署 2:已发送短信)")
	@Excel(name = "运输协议", sort = 6, readConverterExp = "0=未发送短信,1=已签署,2=已发送短信")
	@ExcelProperty(value = "运输协议", index = 6, converter = EasyConverter.TransportationAgreementState.class)
	private Integer transportationAgreementState;

	@ApiModelProperty("结算状态(0:未结算1:部分结算 2:已结算)")
	@Excel(name = "结算状态", sort = 7, readConverterExp = "0=未结算,1=部分结算,2=已结算")
	@ExcelProperty(value = "结算状态", index = 7, converter = EasyConverter.SettleStatus.class)
	private Integer settleStatus;

	@ApiModelProperty("开票状态(0.未开票 1.部分开票 2.已开票 3.开票中 4.撤销开票 5.驳回开票)")
	@Excel(name = "开票状态", sort = 8, readConverterExp = "0=未开票,1=部分开票,2=已开票,3=开票中,4=撤销开票,5=驳回开票")
	@ExcelProperty(value = "开票状态", index = 8, converter = EasyConverter.BillStatus.class)
	private Integer billStatus;

	@ApiModelProperty("车载定位(-1:未设置(无定位) 0:未开始 1:定位中 2:定位完成 3:车辆未入网 4:定位异常 5:无定位 6:查询完成)")
	@Excel(name = "车载定位", sort = 9, readConverterExp = "-1=无定位,0=未开始,1=定位中,2=定位完成,3=车辆未入网,4=定位异常,5=无定位,6=查询完成")
	@ExcelProperty(value = "车载定位", index = 9, converter = EasyConverter.LocateStatus.class)
	private Integer locateStatus;

	@ApiModelProperty("回单状态(0：已上传，1：未上传)")
	@Excel(name = "回单状态", sort = 10, readConverterExp = "0=已上传,1=未上传")
	@ExcelProperty(value = "回单状态", index = 10, converter = EasyConverter.ReceiptStatus.class)
	private Integer receiptStatus;

	@ApiModelProperty("保单状态(-1:未投保 1:不投保 2:投保受理成功 3:投保受理失败 4:系统异常 5:投保成功 6:投保失败 7:人工处理)")
	@Excel(name = "保单状态", sort = 11, readConverterExp = "-1=未投保,1=不投保,2=投保受理成功,3=投保受理失败,4=系统异常,5=投保成功,6=投保失败,7=人工处理")
	@ExcelProperty(value = "保单状态", index = 11, converter = EasyConverter.PolicyStatus.class)
	private Integer policyStatus;

	@ApiModelProperty("保价金额")
	@Excel(name = "保价金额", sort = 12)
	@ExcelProperty(value = "保价金额", index = 12)
	private BigDecimal policyFare;

	@ApiModelProperty("车牌号码")
	@Excel(name = "车牌号码", sort = 13)
	@ExcelProperty(value = "车牌号码", index = 13)
	private String vehicleNumber;

	@ApiModelProperty("司机姓名")
	@Excel(name = "司机姓名", sort = 14)
	@ExcelProperty(value = "司机姓名", index = 14)
	private String driverName;

	@ApiModelProperty("驾驶证号")
	@Excel(name = "驾驶证号", sort = 15)
	@ExcelProperty(value = "驾驶证号", index = 15)
	private String drivingLicense;

	@ApiModelProperty("司机电话")
	@Excel(name = "司机电话", sort = 16)
	@ExcelProperty(value = "司机电话", index = 16)
	private String telephone;

	@ApiModelProperty("收款人户名")
	@Excel(name = "收款人", sort = 17)
	@ExcelProperty(value = "收款人", index = 17)
	private String payeeName;

	@ApiModelProperty("收款人银行卡号")
	@Excel(name = "收款人银行卡号", sort = 18)
	@ExcelProperty(value = "收款人银行卡号", index = 18)
	private String bankCardNo;

	@ApiModelProperty("收款人身份证")
	@Excel(name = "收款人身份证", sort = 19)
	@ExcelProperty(value = "收款人身份证", index = 19)
	private String identityCard;

	@ApiModelProperty("收款人手机号")
	@Excel(name = "收款人手机号", sort = 20)
	@ExcelProperty(value = "收款人手机号", index = 20)
	private String bankMobile;

	@ApiModelProperty("货主单价")
	@Excel(name = "货主单价", sort = 21)
	@ExcelProperty(value = "货主单价", index = 21)
	private String feePriceString;

	@ApiModelProperty("货主货款")
	@Excel(name = "货主货款", sort = 22)
	@ExcelProperty(value = "货主货款", index = 22)
	private BigDecimal receiveFare;

	@ApiModelProperty("应付总运费(元)不带油卡")
	@Excel(name = "应付总运费(元)", sort = 23)
	@ExcelProperty(value = "应付总运费(元)", index = 23)
	private BigDecimal payFare;

	@ApiModelProperty("支付计划")
	@Excel(name = "支付计划", sort = 24)
	@ExcelProperty(value = "支付计划", index = 24)
	private String payType;

	@ApiModelProperty("预付运费金额")
	@Excel(name = "预付运费金额", sort = 25)
	@ExcelProperty(value = "预付运费金额", index = 25)
	private String prepayMoney;

	@ApiModelProperty("到付运费金额")
	@Excel(name = "到付运费金额", sort = 26)
	@ExcelProperty(value = "到付运费金额", index = 26)
	private String arriveMoney;

	@ApiModelProperty("回单付运费金额")
	@Excel(name = "回单付运费金额", sort = 27)
	@ExcelProperty(value = "回单付运费金额", index = 27)
	private String receiptMoney;

	@ApiModelProperty("预付运费类型")
	@Excel(name = "预付运费类型", sort = 28)
	@ExcelProperty(value = "预付运费类型", index = 28)
	private String prepayType;

	@ApiModelProperty("油卡卡号")
	@Excel(name = "油卡卡号", sort = 29)
	@ExcelProperty(value = "油卡卡号", index = 29)
	private String oilCardNo;

	@ApiModelProperty("实付运费(元)")
	@Excel(name = "实付总运费(元)", sort = 30)
	@ExcelProperty(value = "实付总运费(元)", index = 30)
	private BigDecimal actualFare;

	@ApiModelProperty("应收上游")
	@Excel(name = "应收上游(元)", sort = 31)
	@ExcelProperty(value = "应收上游(元)", index = 31)
	private String receiveUpstream;

	@ApiModelProperty("应付下游")
	@Excel(name = "应付下游(元)", sort = 32)
	@ExcelProperty(value = "应付下游(元)", index = 32)
	private BigDecimal payableDownstream;

	@ApiModelProperty("发货单位")
	@Excel(name = "发货单位", sort = 33)
	@ExcelProperty(value = "发货单位", index = 33)
	private String consignor;

	@ApiModelProperty("发货详细地址")
	@Excel(name = "发货详细地址", sort = 34)
	@ExcelProperty(value = "发货详细地址", index = 34)
	private String placeOfLoading;

	@ApiModelProperty("发货联系方式")
	@Excel(name = "发货联系方式", sort = 35)
	@ExcelProperty(value = "发货联系方式", index = 35)
	private String consignorContactPhone;

	@ApiModelProperty("收货单位")
	@Excel(name = "收货单位", sort = 36)
	@ExcelProperty(value = "收货单位", index = 36)
	private String consignee;

	@ApiModelProperty("收货详细地址")
	@Excel(name = "收货详细地址", sort = 37)
	@ExcelProperty(value = "收货详细地址", index = 37)
	private String goodsReceiptPlace;

	@ApiModelProperty("收货联系方式")
	@Excel(name = "收货联系方式", sort = 38)
	@ExcelProperty(value = "收货联系方式", index = 38)
	private String consigneeContactPhone;

	@ApiModelProperty("运输距离(千米)")
	@Excel(name = "运输距离(千米)", sort = 39)
	@ExcelProperty(value = "运输距离(千米)", index = 39)
	private BigDecimal mileage;

	@ApiModelProperty("货物名称")
	@Excel(name = "货物名称", sort = 40)
	@ExcelProperty(value = "货物名称", index = 40)
	private String descriptionOfGoods;


	private String packagingLabel;

	@ApiModelProperty("货物包装")
	@Excel(name = "货物包装", sort = 41)
	@ExcelProperty(value = "货物包装", index = 41)
	private String packagingName;

	private String cargoLabel;

	@ApiModelProperty("货物类别")
	@Excel(name = "货物类别", sort = 42)
	@ExcelProperty(value = "货物类别", index = 42)
	private String cargoTypeClassificationName;

	@ApiModelProperty("大宗单价")
	@Excel(name = "大宗单价", sort = 43)
	@ExcelProperty(value = "大宗单价", index = 43)
	private String feePriceToString;

	@ApiModelProperty("结算数量")
	@Excel(name = "结算数量", sort = 44)
	@ExcelProperty(value = "结算数量", index = 44)
	private Double feeAmount;

	@ApiModelProperty("应收单位(1.吨 2.方 3.车)")
	@Excel(name = "单位", sort = 45)
	@ExcelProperty(value = "单位", index = 45)
	private String feeUnitToString;

	@Excel(name = "装货数量", sort = 46)
	@ExcelProperty(value = "装货数量", index = 46)
	private BigDecimal loadingWeight;

	@Excel(name = "卸货数量", sort = 47)
	@ExcelProperty(value = "卸货数量", index = 47)
	private BigDecimal unloadWeight;

	@ApiModelProperty("装车日期")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Excel(name = "预计装货时间", sort = 48, dateFormat = "yyyy-MM-dd HH:mm:ss")
	@ExcelProperty(value = "预计装货时间", index = 48, converter = EasyConverter.Datetime.class)
	private Date despatchActualDateTime;

	@ApiModelProperty("到达日期")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Excel(name = "预计卸货时间", sort = 49, dateFormat = "yyyy-MM-dd HH:mm:ss")
	@ExcelProperty(value = "预计卸货时间", index = 49, converter = EasyConverter.Datetime.class)
	private Date goodsReceiptDateTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Excel(name = "实际装货时间", sort = 50, dateFormat = "yyyy-MM-dd HH:mm:ss")
	@ExcelProperty(value = "实际装货时间", index = 50, converter = EasyConverter.Datetime.class)
	private Date actualStartTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Excel(name = "实际卸货时间", sort = 51, dateFormat = "yyyy-MM-dd HH:mm:ss")
	@ExcelProperty(value = "实际卸货时间", index = 51, converter = EasyConverter.Datetime.class)
	private Date actualEndTime;

	@ApiModelProperty("备注")
	@Excel(name = "备注", sort = 52)
	@ExcelProperty(value = "备注", index = 52)
	private String remark;

	@ApiModelProperty("运单来源(1:PC建单 2:微信快速录单 3:APP 4:小黑卡 5:合作方 6:微信小程序 7:批量导入)")
	@Excel(name = "运单来源", sort = 53, readConverterExp = "1=PC建单,2=微信快速录单,3=APP,4=小黑卡,5=合作方,6=微信小程序,7=批量导入")
	@ExcelProperty(value = "运单来源", index = 53, converter = EasyConverter.Resource.class)
	private Integer resource;

	@ApiModelProperty("创建人")
	@Excel(name = "创建人", sort = 54)
	@ExcelProperty(value = "创建人", index = 54)
	private String createBy;

	@ApiModelProperty("创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Excel(name = "创建时间", sort = 55, dateFormat = "yyyy-MM-dd HH:mm:ss")
	@ExcelProperty(value = "创建时间", index = 55, converter = EasyConverter.Datetime.class)
	private Date createTime;

	@ApiModelProperty("付款时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Excel(name = "付款时间", sort = 56, dateFormat = "yyyy-MM-dd HH:mm:ss")
	@ExcelProperty(value = "付款时间", index = 56, converter = EasyConverter.Datetime.class)
	private Date payTime;

	@ApiModelProperty("发票号码")
	@Excel(name = "发票号码", sort = 57)
	@ExcelProperty(value = "发票号码", index = 57)
	private String invoiceNumber;

	@ApiModelProperty("开票时间")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@Excel(name = "开票时间", sort = 58, dateFormat = "yyyy-MM-dd")
	@ExcelProperty(value = "开票时间", index = 58, converter = EasyConverter.DateConverter.class)
	private Date invoiceTime;

	@ApiModelProperty("大宗单价")
	private BigDecimal feePrice;

	@ApiModelProperty("运单ID")
	private Long id;

	@ApiModelProperty("项目合约ID")
	private String frameworkContractId;

	@ApiModelProperty("项目合约编号")
	private String frameworkContractCode;

	@ApiModelProperty("关联货源ID")
	private Long makeCodeId;

	@ApiModelProperty("货源类型（0：三方 1：大宗）")
	private Integer supplyType;

	@ApiModelProperty("整个链条的开票状态(0.未开票 1.部分开票 2.已开票 3.开票中)")
	private Integer wholeBillStatus;

	@ApiModelProperty("保险类型(-1:不投保 0:物责险 1:货物险 2:龙琨货运险)")
	//	@Excel(name = "保险类型", sort = 9, readConverterExp = "-1=不投保,0=物责险,1=货物险")
	//	@ExcelProperty(value = "保险类型", index = 9, converter = EasyConverter.PolicyType.class)
	private Integer policyType;

	@ApiModelProperty("保单状态")
	private String policyStatusString;

	@ApiModelProperty("应付总运费(元)带油卡")
	private BigDecimal totalFare;

	@ApiModelProperty("定位方式(-1:未设置(无定位) 0:GPS 1:基站 2:基站[司机未确认] 3:小程序定位 4:历史轨迹 5:人工)")
	private Integer locateType;

	@ApiModelProperty("电子合同状态(0 未签约  1待签约  2已签约)")
	private Integer electronicContractState;

	@ApiModelProperty("电子合同地址")
	private String electronicContractUrl;

	@ApiModelProperty(notes = "代开申请状态 0无需处理 1申请待定 2未申请 3不申请 4已申请审核中 5审核驳回 6审核通过 7已开票")
	private Integer proxyInvoiceState;

	private String electronicContractCode;
	//发货地址详情
	private String consignorAddersDetails;
	//收货地址详情
	private String consigneeAddersDetails;

	private String bankName;

	private String bankCode;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date loadTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date unloadTime;

	private Date updateTime;
	private String updateBy;
	//适用范围：1 所有 2 导入创建，仅导入使用 3.异常补录
	private Integer useScope;

	// 中间字段
	/**
	 * 运单状态：1 未完成付款不可申请开票 2 可申请开票 3 申请开票中 4开票完成
	 */
	private Integer waybillState;
	/**
	 * 状态(0订单 1运单 2已签约(未开始) 3运输中 4已完成 -1删除)
	 */
	private Integer status;

	private Integer infoIsComplete;

	private List<String> errMsgs = new ArrayList<>();

	private Long vehicleId;
	private Long driverId;
	private Long actualCarrierId;

	//司机信息是否完整
	private Integer driverInfoIsComplete;
	//车辆信息是否完整
	private Integer vehicleInfoIsComplete;
	//承运人信息是否完整
	private Integer actualCarrierInfoIsComplete;

	/**
	 * 付款申请状态(0:待申请 1:申请中 2:部分通过 3:已通过 4:已完结 9:被驳回)
	 */
	private Integer payApplyStatus;

	// @Excel(name = "应收单位(1.吨 2.立方米 3.车)")
	@ApiModelProperty("应收单位(1.吨 2.立方米 3.车)")
	private Integer feeUnit;

	@ApiModelProperty("数量单位")
	private String feeAmountAndUnit;

	@ApiModelProperty("应收金额（元）")
	private BigDecimal statementReceiveFare;

	@ApiModelProperty("收货省")
	private String consigneeProvince;
	@ApiModelProperty("收货市")
	private String consigneeCity;
	@ApiModelProperty("收货区")
	private String consigneeArea;
	private String consigneeLongitude;
	private String consigneeLatitude;

	@ApiModelProperty("发货省")
	private String consignorProvince;
	@ApiModelProperty("发货市")
	private String consignorCity;
	@ApiModelProperty("发货区")
	private String consignorArea;
	private String consignorLongitude;
	private String consignorLatitude;


	@ApiModelProperty("车型")
	private String vehicleType;

	@ApiModelProperty("车辆总质量")
	private BigDecimal grossMass;

	@ApiModelProperty("发货数量")
	private String loadAmount;

	@ApiModelProperty("卸货数量")
	private String unloadAmount;


	private BigDecimal loadingCube;
	private BigDecimal unloadCube;
	private String actualCarrierName;

	@ApiModelProperty("是否上报税务 0 是 1 不是")
	private Integer isTaxUpload;

	@ApiModelProperty("第二次（支付）上报税务状态(-1上报失败 0未上报 1上报中 2上报成功 3上报拦截)")
	private Integer taxSecondUploadState;

	@ApiModelProperty("税务异常原因")
	private String taxUploadFailReason;
	@ApiModelProperty("自身设置的收取上游的服务费率")
	private BigDecimal rateByMe;
	@ApiModelProperty("是否补充过轨迹（0：未补充1：已补充）")
	private Integer isCompleteTrajectory;

	private Long waybillId;

	@ApiModelProperty("车队长id")
	private Long carCaptainId;
	@ApiModelProperty("车队长名称")
	private String carCaptainName;
	@ApiModelProperty("车队长手机号")
	private String carCaptainPhone;
	@ApiModelProperty("车队长身份证号")
	private String carCaptainIdentityCard;
	@ApiModelProperty("辅助员id")
	private Long auxiliaryStaffId;
	@ApiModelProperty("辅助员名称")
	private String auxiliaryStaffName;
	@ApiModelProperty("辅助员身份证号")
	private String auxiliaryStaffIdentityCard;
	@ApiModelProperty("辅助员手机号")
	private String auxiliaryStaffPhone;

	@ApiModelProperty("亏/涨")
	private String lossOrRise;
	@ApiModelProperty("网货名称")
	private String freightForwarderName;
	private String payeeIdentityCard;
	private Integer payeeType;
	private Integer payeeInfoIsComplete;
	private Long freightForwarderId;
}
