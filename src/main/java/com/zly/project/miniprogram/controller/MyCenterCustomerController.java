package com.zly.project.miniprogram.controller;

import com.alibaba.fastjson2.JSON;
import com.zly.common.constant.BusinessConstants;
import com.zly.common.utils.SecurityUtils;
import com.zly.framework.security.LoginUser;
import com.zly.framework.security.service.TokenService;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.framework.web.page.TableInfo;
import com.zly.project.applets.evaluate.domain.CustomerAppraiseEx;
import com.zly.project.applets.evaluate.service.ICustomerAppraiseService;
import com.zly.project.applets.feedback.domain.FeedbackComplaints;
import com.zly.project.applets.feedback.domain.FeedbackComplaintsEx;
import com.zly.project.applets.feedback.service.IFeedbackComplaintsService;
import com.zly.project.consignor.consignee.domain.ConsigneeInfo;
import com.zly.project.consignor.consignee.service.IConsigneeInfoService;
import com.zly.project.consignor.consignor.domain.ConsignorInfo;
import com.zly.project.consignor.consignor.service.IConsignorInfoService;
import com.zly.project.consignor.customer.domain.req.CustomerInfoReq;
import com.zly.project.consignor.customer.service.ICustomerInfoService;
import com.zly.project.makecode.domain.MakeCode;
import com.zly.project.makecode.domain.MakeCodeList;
import com.zly.project.makecode.domain.req.MakeCodeAddReq;
import com.zly.project.makecode.domain.req.MakeCodeQuery;
import com.zly.project.makecode.service.IMakeCodeService;
import com.zly.project.miniprogram.domain.MessageInfo;
import com.zly.project.miniprogram.domain.res.DriverMiniRes;
import com.zly.project.miniprogram.domain.res.VehicleCarList;
import com.zly.project.miniprogram.service.IMessageInfoService;
import com.zly.project.operation.domain.OperationPlain;
import com.zly.project.tenant.domain.TenantUser;
import com.zly.project.tenant.mapper.TenantUserMapper;
import com.zly.project.transport.waybill.domain.Waybill;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 小程序  货主端
 *
 * <AUTHOR>
 * @date 2021-10-29
 */
@Api(tags = "小程序  货主端")
@RestController
@RequestMapping("/vx/customer/center")
public class MyCenterCustomerController extends BaseController {

	@Resource
	private IMessageInfoService messageService;

	@Resource
	private ICustomerInfoService customerInfoService;

	@Resource
	private IMakeCodeService makeCodeService;

	@Resource
	private IConsigneeInfoService consigneeInfoService;

	@Resource
	private IConsignorInfoService consignorInfoService;

	@Resource
	private IFeedbackComplaintsService feedbackComplaintsService;

	@Resource
	private ICustomerAppraiseService customerAppraiseService;

	@Resource
	private TenantUserMapper tenantUserMapper;

	@Resource
	private TokenService tokenService;

	/**
	 * 根据货主ID 查询对应的消息列表
	 *
	 * @return
	 */
	@ApiOperation(value = "根据货主ID 查询对应的消息列表", notes = "根据货主ID 查询对应的消息列表")
	@GetMapping("/getmessageid")
	public AjaxResult getMessageByCargoId() {
		Long id = SecurityUtils.getLoginUser().getUserId();
		return AjaxResult.success(messageService.selectMessageInfoByCargoId(id));
	}

	/**
	 * 设置消息为 已读
	 *
	 * @param messageInfo
	 * @return
	 */
	@ApiOperation(value = "设置消息为 已读", notes = "设置消息为 已读")
	@PostMapping("/update/readflag")
	public AjaxResult updateMessageById(@RequestBody MessageInfo messageInfo) {
		messageInfo.setReadFlag(1);
		return toAjax(messageService.updateMessageInfo(messageInfo));
	}

	/**
	 * 企业认证货主端
	 *
	 * @param customerInfoReq
	 * @return
	 */
	@ApiOperation(value = "企业认证货主端", notes = "企业认证货主端")
	@PostMapping("/insert/identity")
	public AjaxResult InsertIdentity(@RequestBody CustomerInfoReq customerInfoReq) {
		return customerInfoService.insertIdentity(customerInfoReq);
	}

	/**
	 * 审核 企业货主端信息
	 *
	 * @return
	 */
	@ApiOperation(value = "审核 企业货主端信息", notes = "审核 企业货主端信息")
	@PostMapping("/approve/identity")
	public AjaxResult approveIdentity(@RequestBody CustomerInfoReq customerInfoReq) {
		return customerInfoService.approveIdentity(customerInfoReq);
	}

    /**
     * 货主端 查询自己的收货地址
     *
     * @return
     */
    @ApiOperation(value = "货主端 查询自己的收货地址",notes = "货主端 查询自己的收货地址")
    @PostMapping("/consigneeinfo/list")
    public TableInfo<ConsigneeInfo> ConsignInfoList(@RequestBody CustomerInfoReq req) {
        Long cargoOwnerId = SecurityUtils.getLoginUser().getTenantUser().getTenantId();
        ConsigneeInfo consigneeInfo = new ConsigneeInfo();
        consigneeInfo.setCargoOwnerId(cargoOwnerId);
        consigneeInfo.setGoodsReceiptPlace(req.getSearchValue());
        List<ConsigneeInfo> consigneeInfos = consigneeInfoService.selectOwnerConsigneeInfoList(consigneeInfo);
        //查询条数
        int count = consigneeInfoService.selectOwnerConsigneeInfoListCount(consigneeInfo);
        return new TableInfo<>(consigneeInfos, count);
    }

    /**
     * 货主端 查询自己的发货地址
     *
     * @return
     */
    @ApiOperation(value = "货主端 查询自己的发货地址",notes = "货主端 查询自己的发货地址")
    @PostMapping("/consignorinfo/list")
    public TableInfo<ConsignorInfo> ConsignorInfoList(@RequestBody CustomerInfoReq req) {
        Long cargoOwnerId = SecurityUtils.getLoginUser().getTenantUser().getTenantId();
        ConsignorInfo consignorInfo = new ConsignorInfo();
        consignorInfo.setCargoOwnerId(cargoOwnerId);
        consignorInfo.setPlaceOfLoading(req.getSearchValue());
        List<ConsignorInfo> consignorInfos = consignorInfoService.selectOwnerConsignorInfoList(consignorInfo);
        //查询条数
        int count = consignorInfoService.selectOwnerConsignorInfoListCount(consignorInfo);
        return new TableInfo<>(consignorInfos, count);
    }

	/**
	 * 货主端 修改自己的收货地址
	 *
	 * @return
	 */
	@ApiOperation(value = "货主端 修改自己的收货地址", notes = "货主端 修改自己的收货地址")
	@PostMapping("/consigneeinfo/update")
	public AjaxResult ConsignInfoUpdate(@RequestBody ConsigneeInfo consigneeInfo) {
		return consigneeInfoService.consignInfoUpdate(consigneeInfo);
	}

	/**
	 * 货主端 修改自己的发货地址
	 *
	 * @return
	 */
	@ApiOperation(value = "货主端 修改自己的发货地址", notes = "货主端 修改自己的发货地址")
	@PostMapping("/consignorinfo/update")
	public AjaxResult ConsignorInfoUpdate(@RequestBody ConsignorInfo consignorInfo) {
		return consignorInfoService.consignInfoUpdate(consignorInfo);
	}

	/**
	 * 货主端  删除自己的收货地址
	 *
	 * @return
	 */
	@ApiOperation(value = "货主端 删除自己的收货地址", notes = "货主端 删除自己的收货地址")
	@PostMapping("/consigneeinfo/delete")
	public AjaxResult ConsignInfoDelete(@RequestBody ConsigneeInfo consigneeInfo) {
		return consigneeInfoService.consignInfoDelete(consigneeInfo);
	}

	/**
	 * 货主端  删除自己的发货地址
	 *
	 * @return
	 */
	@ApiOperation(value = "货主端 删除自己的发货地址", notes = "货主端 删除自己的发货地址")
	@PostMapping("/consignorinfo/delete")
	public AjaxResult ConsignorInfoDelete(@RequestBody ConsignorInfo consignorInfo) {
		return consignorInfoService.consignInfoDelete(consignorInfo);
	}

	/**
	 * 货主端  新增自己的收货地址
	 *
	 * @return
	 */
	@ApiOperation(value = "货主端 新增自己的收货地址", notes = "货主端 新增自己的收货地址")
	@PostMapping("/consigneeinfo/add")
	public CommonResult<Long> ConsigninfoAdd(@RequestBody ConsigneeInfo consigneeInfo) {
		return CommonResult.success(consigneeInfoService.consigneeInfoAdd(consigneeInfo));
	}

	/**
	 * 货主端  新增自己的发货地址
	 *
	 * @return
	 */
	@ApiOperation(value = "货主端 新增自己的发货地址", notes = "货主端 新增自己的发货地址")
	@PostMapping("/consignorinfo/add")
	public CommonResult<Long> ConsignorInfoAdd(@RequestBody ConsignorInfo consignorInfo) {
		return CommonResult.success(consignorInfoService.consignorInfoAdd(consignorInfo));
	}

	/**
	 * 货主端 发货地址详情
	 *
	 * @param id
	 * @return
	 */
	@ApiOperation(value = "货主端 发货地址详情", notes = "货主端 发货地址详情")
	@GetMapping("/consignorinfo/detail/{id}")
	public AjaxResult ConsignorInfoDetail(@PathVariable("id") Long id) {
		return AjaxResult.success(consignorInfoService.selectConsignorInfoById(id));
	}

	/**
	 * 货主端 收货地址详情
	 *
	 * @param id
	 * @return
	 */
	@ApiOperation(value = "货主端 收货地址详情", notes = "货主端 收货地址详情")
	@GetMapping("/consigneeinfo/detail/{id}")
	public AjaxResult ConsigneeInfoDetail(@PathVariable("id") Long id) {
		return AjaxResult.success(consigneeInfoService.selectConsigneeInfoById(id));
	}

	/**
	 * 新建货源
	 *
	 * @param makeCode
	 * @return
	 */
	@ApiOperation(value = "新建货源", notes = "新建货源")
	@PostMapping("/customer/makecode/add")
	public CommonResult addCustomerMakeCode(@RequestBody MakeCodeAddReq makeCode) {
		logger.info("货主端——新建货源：{}", JSON.toJSONString(makeCode));
		return makeCodeService.addCustomerMakeCode(makeCode, BusinessConstants.CLIENT_WXMP_TENANT);
	}

	/**
	 * 获取货源详情
	 *
	 * @param id
	 * @return
	 */
	@ApiOperation(value = "获取货源详情", notes = "获取货源详情")
	@GetMapping("/customer/makecode/detail/{id}")
	public AjaxResult detailCustomerMakeCode(@PathVariable("id") Long id) {
		return AjaxResult.success(makeCodeService.detailCustomerMakeCode(id));
	}

	/**
	 * 货源编辑
	 *
	 * @param makeCode
	 * @return
	 */
	@ApiOperation(value = "货源编辑", notes = "货源编辑")
	@PostMapping("/customer/makecode/edit")
	public CommonResult editCustomerMakeCode(@RequestBody MakeCodeAddReq makeCode) {
		return makeCodeService.editCustomerMakeCode(makeCode);
	}

	@ApiOperation("货主端——删除货源--4.5.9 -- zxy")
	@PostMapping("/customer/makecode/del")
	public CommonResult editCustomerMakeCode(@RequestBody List<Long> ids) {
		return makeCodeService.delMakeCode(ids);
	}

	/**
	 * 变更货源价格
	 *
	 * @param makeCode
	 * @return
	 */
	@ApiOperation(value = "变更货源价格", notes = "变更货源价格")
	@PostMapping("/customer/makecode/editPrice")
	public AjaxResult editMakeCodeWaybillPrice(@RequestBody MakeCode makeCode) {
		return makeCodeService.editMakeCodeWaybillPrice(makeCode);
	}

	/**
	 * 小程序货源详情里的调度计划： 查询今天，明天，后天 这三天时间范围内未完成的调度计划
	 *
	 * @param makeCodeId
	 * @return
	 */
	@ApiOperation(value = "小程序货源详情里的调度计划", notes = "小程序货源详情里的调度计划")
	@GetMapping("/customer/makecode/Operationplain/{makeCodeId}")
	public TableDataInfo makeCodeOperationplainList(@PathVariable("makeCodeId") Long makeCodeId) {
		startPage();
		List<OperationPlain> operationPlainList = makeCodeService.selectThreeDaysOperationPlainList(makeCodeId);
		return getDataTable(operationPlainList);
	}

	/**
	 * 货源列表
	 *
	 * @param makeCodeQuery
	 * @return
	 */
	@ApiOperation(value = "货源列表", notes = "货源列表")
	@PostMapping("/customer/makecode/list")
	public TableDataInfo listCustomerMakeCode(@RequestBody MakeCodeQuery makeCodeQuery) {
		Long userId = SecurityUtils.getUserId();
		TenantUser tenantUser = tenantUserMapper.selectUserByUserId(userId, null);
		if (tenantUser.getTenantId() == 0) {
			return getDataTable(new ArrayList<>());
		}
		// 校验托运人项目分组权限
		//this.addProjectGroup(makeCodeQuery);
		this.addContractIds(makeCodeQuery);
		makeCodeQuery.setUserMakeCodeIds(makeCodeService.getUserMakeCodeIds());
		startPage();
		List<MakeCodeList> list = makeCodeService.listCustomerMakeCode(makeCodeQuery, tenantUser);
		return getDataTable(list);
	}

	/**
	 * 货主端确认费用
	 *
	 * @param waybill
	 * @return
	 */
	@ApiOperation(value = "货主端确认费用", notes = "货主端确认费用")
	@PostMapping("/confirm/customer/makecode")
	public AjaxResult confirmCustomerMakeCode(@RequestBody Waybill waybill) {
		return makeCodeService.confirmCustomerMakeCode(waybill);
	}

	/**
	 * 货主端的 车辆列表
	 *
	 * @param waybill
	 * @return
	 */
	@ApiOperation(value = "货主端的 车辆列表", notes = "货主端的 车辆列表")
	@PostMapping("/car/list")
	public TableDataInfo carList(@RequestBody Waybill waybill) {
		startPage();
		List<VehicleCarList> vehicleCarListList = makeCodeService.carList(waybill);
		return getDataTable(vehicleCarListList);
	}

	/**
	 * 货主端 车辆管理的车辆详情
	 *
	 * @param vehicleCarList
	 * @return
	 */
	@ApiOperation(value = "货主端 车辆管理的车辆详情", notes = "货主端 车辆管理的车辆详情")
	@PostMapping("/car/detail")
	public AjaxResult carDetail(@RequestBody VehicleCarList vehicleCarList) {
		return AjaxResult.success(makeCodeService.carDetail(vehicleCarList));
	}

	/**
	 * 货主端的 司机管理
	 *
	 * @return
	 */
	@ApiOperation(value = "货主端 司机管理", notes = "货主端 司机管理")
	@PostMapping("/driver/list")
	public TableDataInfo driverList() {
		startPage();
		List<DriverMiniRes> driverList = makeCodeService.driverList();
		return getDataTable(driverList);
	}

	/**
	 * 货主端的 司机详情
	 *
	 * @return
	 */
	@ApiOperation(value = "货主端 司机详情", notes = "货主端 司机详情")
	@GetMapping("/driver/detail/{id}")
	public AjaxResult driverDetail(@PathVariable("id") Long id) {
		return AjaxResult.success(makeCodeService.driverDetail(id));
	}

	/**
	 * 货主端的  举报投诉
	 *
	 * @param feedbackComplaintsEx
	 * @return
	 */
	@ApiOperation(value = "货主端 举报投诉", notes = "货主端 举报投诉")
	@PostMapping("/report/complaints")
	public AjaxResult reportComplaints(@RequestBody FeedbackComplaintsEx feedbackComplaintsEx) {
		int success = feedbackComplaintsService.insertFeedbackComplaints(feedbackComplaintsEx);
		if (success == 2) {
			return AjaxResult.error("已举报一次,无法多次举报");
		} else {
			return toAjax(success);
		}
	}

	/**
	 * 货主端的  举报投诉详情（运单id）
	 *
	 * @param id
	 * @return
	 */
	@ApiOperation(value = "货主端 举报投诉详情（运单id）", notes = "货主端 举报投诉详情（运单id）")
	@GetMapping("/report/complaints/detail/{id}")
	public AjaxResult reportComplaintsDetail(@PathVariable("id") Long id) {
		return AjaxResult.success(feedbackComplaintsService.selectFeedbackComplaintsByWayBillId(id));
	}

	/**
	 * 货主端 评价司机
	 *
	 * @param customerAppraiseEx
	 * @return
	 */
	@ApiOperation(value = "货主端 评价司机", notes = "货主端 评价司机")
	@PostMapping("/evaluation/driver")
	public AjaxResult evaluationDriver(@RequestBody CustomerAppraiseEx customerAppraiseEx) {
		return toAjax(customerAppraiseService.insertCustomerAppraise(customerAppraiseEx));
	}

	/**
	 * 货主端 评价司机详情（运单id）
	 *
	 * @param id
	 * @return
	 */
	@ApiOperation(value = "货主端 评价司机详情（运单id）", notes = "货主端 评价司机详情（运单id）")
	@GetMapping("/evaluation/driver/detail/{id}")
	public AjaxResult evaluationDriver(@PathVariable("id") Long id) {
		return AjaxResult.success(customerAppraiseService.selectCustomerAppraiseByIds(id));
	}

	/**
	 * 货主端 意见反馈
	 *
	 * @param feedbackComplaintsEx
	 * @return
	 */
	@ApiOperation(value = "货主端 意见反馈", notes = "货主端 意见反馈")
	@PostMapping("/opinion/feedback")
	public AjaxResult opinionFeedback(@RequestBody FeedbackComplaintsEx feedbackComplaintsEx) {
		return feedbackComplaintsService.insertFeedbackComplaintsOpinion(feedbackComplaintsEx);
	}

	/**
	 * 货主端 意见反馈详情
	 *
	 * @param id
	 * @return
	 */
	@ApiOperation(value = "货主端 意见反馈详情", notes = "货主端 意见反馈详情")
	@GetMapping("/opinion/feedback/detail/{id}")
	public AjaxResult opinionFeedbackDetailById(@PathVariable("id") Long id) {
		return AjaxResult.success(feedbackComplaintsService.selectFeedbackComplaintsById(id));
	}

	/**
	 * 货主端 意见反馈列表
	 *
	 * @param feedbackComplaints
	 * @return
	 */
	@ApiOperation(value = "货主端 意见反馈列表", notes = "货主端 意见反馈列表")
	@PostMapping("/opinion/feedback/list")
	public TableDataInfo opinionFeedbackList(FeedbackComplaints feedbackComplaints) {
		startPage();
		Long userId = SecurityUtils.getLoginUser().getTenantUser().getUserId();
		feedbackComplaints.setDriverId(userId);
		List<FeedbackComplaints> list = feedbackComplaintsService.selectFeedbackComplaintsList(feedbackComplaints);
		return getDataTable(list);
	}

	// 货物类别历史选择
	@ApiOperation(value = "货物类别历史选择", notes = "货物类别历史选择")
	@GetMapping("/opinion/goodsTypeHistorySelect")
	public AjaxResult goodsTypeHistorySelect() {
		return AjaxResult.success(makeCodeService.goodsTypeHistorySelect());
	}

	// 货物包装历史选择
	@ApiOperation(value = "货物包装历史选择", notes = "货物包装历史选择")
	@GetMapping("/opinion/getPackagingHistory")
	public AjaxResult getPackagingHistory() {
		return AjaxResult.success(makeCodeService.getPackagingHistory());
	}

	@ApiOperation(value = "更换货源显示——v4.5.9——shw", notes = "更换货源显示——v4.5.9——shw")
	@PostMapping("/updateIsAllMakeCode/{type}")
	public CommonResult updateIsAllMakeCode(@PathVariable("type") Integer type) {
		tenantUserMapper.updateIsAllMakeCode(SecurityUtils.getUserId(), type);
		LoginUser loginUser = SecurityUtils.getLoginUser();
		loginUser.getTenantUser().setIsAllMakeCode(type);
		tokenService.refreshToken(loginUser);

		return CommonResult.success();
	}
}
