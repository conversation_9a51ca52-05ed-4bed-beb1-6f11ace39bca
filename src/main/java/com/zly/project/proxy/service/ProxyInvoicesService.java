package com.zly.project.proxy.service;

import com.zly.common.utils.DateUtils;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.StringUtils;
import com.zly.common.utils.TextUtil;
import com.zly.framework.web.domain.CommonResult;
import com.zly.project.freightforwarder.domain.FreightForwarderInfo;
import com.zly.project.freightforwarder.service.impl.FreightForwarderInfoServiceImpl;
import com.zly.project.proxy.domain.ProxyInvoices;
import com.zly.project.proxy.domain.ProxyInvoicesWaybill;
import com.zly.project.proxy.domain.req.InvoiceStatusCallbackReq;
import com.zly.project.proxy.domain.req.ProxyInvoicesReq;
import com.zly.project.proxy.domain.res.InvoicesWaybill;
import com.zly.project.proxy.domain.res.ProxyCount;
import com.zly.project.proxy.domain.res.ProxyInvoicesRes;
import com.zly.project.proxy.domain.res.cjhyProxy.InvoiceDetailRes;
import com.zly.project.proxy.domain.res.cjhyProxy.WaybillInvoiceStatusRes;
import com.zly.project.proxy.mapper.ProxyInvoicesMapper;
import com.zly.project.proxy.mapper.ProxyInvoicesWaybillMapper;
import com.zly.project.transport.waybill.mapper.WaybillMapperEx;
import com.zly.project.transport.waybill.mapper.WaybillProxyInvoiceMapper;
import com.zly.project.transport.waybill.service.impl.WaybillServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.zly.common.utils.PageUtils.startPage;

/**
 * 代发发票Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@Service
public class ProxyInvoicesService {
    @Resource
    private ProxyInvoicesMapper proxyInvoicesMapper;
    @Resource
    private ProxyInvoicesWaybillMapper proxyInvoicesWaybillMapper;
    @Resource
    private WaybillMapperEx waybillMapperEx;
    @Resource
    private WaybillProxyInvoiceMapper waybillProxyInvoiceMapper;
    @Resource
    private FreightForwarderInfoServiceImpl freightForwarderInfoService;
    @Resource
    private WaybillServiceImpl waybillService;

    // 开票
    public void bill(WaybillInvoiceStatusRes req) {

        List<String> shippingNoteNumberList = new ArrayList<>();
        List<ProxyInvoicesWaybill> list = new ArrayList<>();
        Long proxyId = 0L;
        String code = req.getInvoiceCode();
        String no = req.getInvoiceNo();

        if ("1".equals(req.getStatus()) || "6".equals(req.getStatus()) || "7".equals(req.getStatus()) ) {
            for (InvoiceDetailRes invoiceDetail : req.getInvoiceList()) {

                String invoiceCode = req.getInvoiceCode();
                String invoiceNo = invoiceDetail.getInvoiceNo();
                Long id = TextUtil.getTimeSequenceID(5);
                if (code.equals(invoiceCode) && no.equals(invoiceNo)) {
                    proxyId = id;
                }
                ProxyInvoices invoices = new ProxyInvoices();
                invoices.setId(id);
                BeanUtils.copyProperties(invoiceDetail, invoices);

                invoices.setInvoiceDate(DateUtils.strToDate(invoiceDetail.getInvoiceDate()));
                invoices.setInvoiceMoney(new BigDecimal(req.getInvoiceMoney()));
                invoices.setInvoiceTaxMoney(new BigDecimal(req.getInvoiceTaxMoney()));
                invoices.setIndividualIncomeTax(new BigDecimal(invoiceDetail.getIndividualIncomeTax()));
                invoices.setInTaxSales(new BigDecimal(invoiceDetail.getInTaxSales()));
                invoices.setExcludeTaxSales(new BigDecimal(invoiceDetail.getExcludeTaxSales()));
                invoices.setTax(new BigDecimal(invoiceDetail.getTax()));
                invoices.setTaxAmount(new BigDecimal(req.getTaxAmount()));
                invoices.setVat(new BigDecimal(invoiceDetail.getVat()));
                invoices.setStampTax(new BigDecimal(invoiceDetail.getStampTax()));
                invoices.setHouseTax(new BigDecimal(invoiceDetail.getHouseTax()));
                invoices.setUrbanConstructionTax(new BigDecimal(invoiceDetail.getUrbanConstructionTax()));
                invoices.setEducationSurtax(new BigDecimal(invoiceDetail.getEducationSurtax()));
                invoices.setLoaclEducationSurtax(StringUtils.isEmpty(invoiceDetail.getLoaclEducationSurtax()) ? BigDecimal.ZERO : new BigDecimal(invoiceDetail.getLoaclEducationSurtax()));
                invoices.setSpecialFunds(StringUtils.isEmpty(invoiceDetail.getSpecialFunds()) ? BigDecimal.ZERO : new BigDecimal(invoiceDetail.getSpecialFunds()));

                // 删除重新插入
                proxyInvoicesMapper.deleteProxyInvoicesByInvoiceNo(invoiceNo);
                proxyInvoicesMapper.insertProxyInvoices(invoices);

                // 删除对应运单
                List<ProxyInvoicesWaybill> waybillList = proxyInvoicesWaybillMapper.selectByInvoiceNo(invoiceNo);
                List<Long> ids = waybillList.stream().map(ProxyInvoicesWaybill::getId).collect(Collectors.toList());
                List<String> shippingNoteNumbers = waybillList.stream().map(ProxyInvoicesWaybill::getShippingNoteNumber).collect(Collectors.toList());
                shippingNoteNumberList.addAll(shippingNoteNumbers);
                if (ids.size() > 0) {
                    proxyInvoicesWaybillMapper.deleteProxyInvoicesByIds(ids);
                }
            }

            if (!shippingNoteNumberList.contains(req.getWaybillNo())) {
                shippingNoteNumberList.add(req.getWaybillNo());
            }

            // 重新插入运单关系
            for (String s : shippingNoteNumberList) {
                ProxyInvoicesWaybill waybill = new ProxyInvoicesWaybill();
                waybill.setId(TextUtil.getTimeSequenceID(5));
                waybill.setWaybillId(waybillMapperEx.selectIdByShippingNoteNumber(s));
                waybill.setShippingNoteNumber(s);
                waybill.setInvoiceId(proxyId);
                waybill.setInvoiceCode(code);
                waybill.setInvoiceNo(no);

                list.add(waybill);
            }

            proxyInvoicesWaybillMapper.insertList(list);

            // 修改运单表,申请表状态
            waybillMapperEx.updateProxyStateByShipperNoteNumbers(shippingNoteNumberList);
            waybillProxyInvoiceMapper.updateProxyStateByShipperNoteNumbers(shippingNoteNumberList);
        }
    }

    // 开票
    public void bill(InvoiceStatusCallbackReq req) {

        List<String> shippingNoteNumberList = new ArrayList<>();
        List<Long> waybillIdList = new ArrayList<>();
        List<ProxyInvoicesWaybill> list = new ArrayList<>();
        Long proxyId = 0L;
        String code = req.getInvoiceCode();
        String no = req.getInvoiceNo();

        for (InvoiceStatusCallbackReq.InvoiceDetail invoiceDetail : req.getInvoiceList()) {

            String invoiceCode = req.getInvoiceCode();
            String invoiceNo = invoiceDetail.getInvoiceNo();
            Long id = TextUtil.getTimeSequenceID(5);
            if (code.equals(invoiceCode) && no.equals(invoiceNo)) {
                proxyId = id;
            }
            ProxyInvoices invoices = new ProxyInvoices();
            invoices.setId(id);
            BeanUtils.copyProperties(invoiceDetail, invoices);

            // 删除重新插入

            proxyInvoicesMapper.deleteProxyInvoicesByInvoiceNo(invoiceNo);
            proxyInvoicesMapper.insertProxyInvoices(invoices);

            // 删除对应运单
            List<ProxyInvoicesWaybill> waybillList = proxyInvoicesWaybillMapper.selectByInvoiceNo(invoiceNo);
            List<Long> ids = waybillList.stream().map(ProxyInvoicesWaybill::getId).collect(Collectors.toList());
            List<String> shippingNoteNumbers = waybillList.stream().map(ProxyInvoicesWaybill::getShippingNoteNumber).collect(Collectors.toList());
            shippingNoteNumberList.addAll(shippingNoteNumbers);
            List<Long> waybillIds = waybillList.stream().map(ProxyInvoicesWaybill::getId).collect(Collectors.toList());
            waybillIdList.addAll(waybillIds);
            proxyInvoicesWaybillMapper.deleteProxyInvoicesByIds(ids);
        }

        // 重新插入运单关系
        for (String s : shippingNoteNumberList) {
            ProxyInvoicesWaybill waybill = new ProxyInvoicesWaybill();
            waybill.setId(TextUtil.getTimeSequenceID(5));
            waybill.setShippingNoteNumber(s);
            waybill.setInvoiceId(proxyId);
            waybill.setInvoiceCode(code);
            waybill.setInvoiceNo(no);

            list.add(waybill);
        }

        proxyInvoicesWaybillMapper.insertList(list);

        // 修改运单表,申请表状态
        waybillMapperEx.updateProxyStateByShipperNoteNumbers(shippingNoteNumberList);
        waybillProxyInvoiceMapper.updateProxyStateByShipperNoteNumbers(shippingNoteNumberList);

    }

    public List<ProxyInvoices> invoiceList(ProxyInvoicesReq req) {
        if (StringUtils.isNotEmpty(req.getInvoiceDateStart())) {
            req.setInvoiceDateStart(req.getInvoiceDateStart() + " 00:00:00");
        }
        if (StringUtils.isNotEmpty(req.getInvoiceDateEnd())) {
            req.setInvoiceDateEnd(req.getInvoiceDateEnd() + " 23:59:59");
        }
        return proxyInvoicesMapper.invoiceList(req);
    }

    public CommonResult<ProxyInvoicesRes> invoiceInfo(Long id) {
        ProxyInvoicesRes res = new ProxyInvoicesRes();

        FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(SecurityUtils.getFreightForwarderId());
        BeanUtils.copyProperties(freightForwarderInfo, res);

        ProxyInvoices invoices = proxyInvoicesMapper.selectProxyInvoicesById(id);
        if (invoices == null) {
            return CommonResult.error("未查询到发票信息,请先刷新页面后再次查询!");
        }
        BeanUtils.copyProperties(invoices, res);

        // 查询运单信息
        startPage();
        List<InvoicesWaybill> list = proxyInvoicesWaybillMapper.selectWaybillByProxyId(id);
        Integer count = proxyInvoicesWaybillMapper.selectWaybillByProxyIdCount(id);
        for (InvoicesWaybill waybill : list) {
            // 处理地址
            waybill.setPlaceOfLoading(waybillService.getAddress(waybill.getLoadingProvince(), waybill.getLoadingCity(), waybill.getLoadingArea(), waybill.getPlaceOfLoading()));
            waybill.setGoodsReceiptPlace(waybillService.getAddress(waybill.getUnloadProvince(), waybill.getUnloadCity(), waybill.getUnloadArea(), waybill.getGoodsReceiptPlace()));

            waybill.setConsignorAddersDetails(waybillService.getAllAddress(waybill.getLoadingProvince(), waybill.getLoadingCity(), waybill.getLoadingArea(), waybill.getPlaceOfLoading()));
            waybill.setConsigneeAddersDetails(waybillService.getAllAddress(waybill.getUnloadProvince(), waybill.getUnloadCity(), waybill.getUnloadArea(), waybill.getGoodsReceiptPlace()));

        }

        res.setWaybillList(list);
        res.setCount(count);

        return CommonResult.success(res);
    }

    public ProxyCount proxyInvoiceCount() {
        ProxyCount proxyCount = new ProxyCount();

        proxyCount.setInReviewCount(waybillProxyInvoiceMapper.count(4));
        proxyCount.setReviewRejectedCount(waybillProxyInvoiceMapper.count(5));

        return proxyCount;

    }
}
