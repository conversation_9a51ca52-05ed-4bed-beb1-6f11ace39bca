package com.zly.project.makecode.mapper;

import com.zly.project.makecode.domain.MakeCodeSlave;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
public interface MakeCodeSlaveMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public MakeCodeSlave selectMakeCodeSlaveById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param makeCodeSlave 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<MakeCodeSlave> selectMakeCodeSlaveList(MakeCodeSlave makeCodeSlave);

    /**
     * 新增【请填写功能名称】
     *
     * @param makeCodeSlave 【请填写功能名称】
     * @return 结果
     */
    public int insertMakeCodeSlave(MakeCodeSlave makeCodeSlave);

    /**
     * 修改【请填写功能名称】
     *
     * @param makeCodeSlave 【请填写功能名称】
     * @return 结果
     */
    public int updateMakeCodeSlave(MakeCodeSlave makeCodeSlave);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteMakeCodeSlaveById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMakeCodeSlaveByIds(Long[] ids);

    List<MakeCodeSlave> selectMakeCodeSlaveByHour();
}
