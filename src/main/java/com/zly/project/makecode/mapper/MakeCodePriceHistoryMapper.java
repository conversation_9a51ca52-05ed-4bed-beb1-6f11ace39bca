package com.zly.project.makecode.mapper;


import com.zly.project.makecode.domain.MakeCodePriceHistory;

import java.util.List;

/**
 * 货源价格变更记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-08-18
 */
public interface MakeCodePriceHistoryMapper 
{
    /**
     * 查询货源价格变更记录
     * 
     * @param id 货源价格变更记录主键
     * @return 货源价格变更记录
     */
    public MakeCodePriceHistory selectMakeCodePriceHistoryById(Long id);

    /**
     * 查询货源价格变更记录列表
     * 
     * @param makeCodePriceHistory 货源价格变更记录
     * @return 货源价格变更记录集合
     */
    public List<MakeCodePriceHistory> selectMakeCodePriceHistoryList(MakeCodePriceHistory makeCodePriceHistory);

    /**
     * 新增货源价格变更记录
     * 
     * @param makeCodePriceHistory 货源价格变更记录
     * @return 结果
     */
    public int insertMakeCodePriceHistory(MakeCodePriceHistory makeCodePriceHistory);

    /**
     * 修改货源价格变更记录
     * 
     * @param makeCodePriceHistory 货源价格变更记录
     * @return 结果
     */
    public int updateMakeCodePriceHistory(MakeCodePriceHistory makeCodePriceHistory);

    /**
     * 删除货源价格变更记录
     * 
     * @param id 货源价格变更记录主键
     * @return 结果
     */
    public int deleteMakeCodePriceHistoryById(Long id);

    /**
     * 批量删除货源价格变更记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMakeCodePriceHistoryByIds(Long[] ids);
}
