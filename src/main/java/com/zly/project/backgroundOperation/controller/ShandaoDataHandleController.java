package com.zly.project.backgroundOperation.controller;

import java.util.Map;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zly.framework.web.domain.CommonResult;
import com.zly.project.backgroundOperation.service.ShandaoDataHandlerService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 *
 * @ClassName: ShandaoDataHandleController
 * @Description: 善道数据抽取——从集团服务至善道服务
 * @Author: ZXY
 * @Date: 2024/10/31 11:00
 **/
@Api(tags = "善道数据抽取——从集团服务至善道服务")
@RestController
@RequestMapping("/shandao")
public class ShandaoDataHandleController {

	@Resource
	private ShandaoDataHandlerService shandaoDataHandlerService;

	@ApiOperation(value = "善道数据全量抽取——从集团服务至善道服务--v4.5.0.2--zxy")
	@PostMapping("/synchronousData/full")
	public CommonResult synchronousFullData() {
		return shandaoDataHandlerService.synchronousFullData();
	}

	@ApiOperation(value = "善道数据大屏--v4.5.4--yxq")
	@GetMapping("/shanDaoBigScreen")
	public CommonResult<Map<String, Object>> shanDaoBigscreen() {
		return CommonResult.success(shandaoDataHandlerService.shanDaoBigscreen());
	}

}
