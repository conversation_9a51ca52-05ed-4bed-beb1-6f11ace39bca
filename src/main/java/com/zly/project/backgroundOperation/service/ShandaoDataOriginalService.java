package com.zly.project.backgroundOperation.service;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.zly.framework.aspectj.lang.annotation.DataSource;
import com.zly.framework.aspectj.lang.enums.DataSourceType;
import com.zly.project.carrier.carrier.domain.ActualCarrierExtend;
import com.zly.project.carrier.carrier.domain.ActualCarrierInfo;
import com.zly.project.carrier.carrier.domain.ActualDriverRelation;
import com.zly.project.carrier.carrier.domain.ActualVehicleRelation;
import com.zly.project.carrier.carrier.mapper.ActualCarrierExtendMapper;
import com.zly.project.carrier.carrier.mapper.ActualCarrierInfoMapper;
import com.zly.project.carrier.carrier.mapper.ActualDriverRelationMapper;
import com.zly.project.carrier.carrier.mapper.ActualVehicleRelationMapper;
import com.zly.project.carrier.driver.domain.Driver;
import com.zly.project.carrier.driver.mapper.DriverMapper;
import com.zly.project.carrier.payee.domain.CustomerPayeeRelation;
import com.zly.project.carrier.payee.domain.PayeeInfo;
import com.zly.project.carrier.payee.mapper.CustomerPayeeRelationMapper;
import com.zly.project.carrier.payee.mapper.PayeeInfoMapper;
import com.zly.project.carrier.vehicle.domain.CustomerVehicleRelation;
import com.zly.project.carrier.vehicle.domain.Vehicle;
import com.zly.project.carrier.vehicle.domain.VehicleTrailer;
import com.zly.project.carrier.vehicle.mapper.CustomerVehicleRelationMapper;
import com.zly.project.carrier.vehicle.mapper.VehicleMapper;
import com.zly.project.carrier.vehicle.mapper.VehicleTrailerMapper;
import com.zly.project.common.domain.*;
import com.zly.project.common.mapper.*;
import com.zly.project.consignor.consignee.domain.ConsigneeInfo;
import com.zly.project.consignor.consignee.mapper.ConsigneeInfoMapper;
import com.zly.project.consignor.consignor.domain.ConsignorInfo;
import com.zly.project.consignor.consignor.mapper.ConsignorInfoMapper;
import com.zly.project.consignor.contract.domain.CustomerContract;
import com.zly.project.consignor.contract.mapper.CustomerContractMapper;
import com.zly.project.consignor.customer.domain.CustomerActualRelation;
import com.zly.project.consignor.customer.domain.CustomerDriverRelation;
import com.zly.project.consignor.customer.domain.CustomerInfo;
import com.zly.project.consignor.customer.domain.LastCustomerInfo;
import com.zly.project.consignor.customer.mapper.CustomerActualRelationMapper;
import com.zly.project.consignor.customer.mapper.CustomerDriverRelationMapper;
import com.zly.project.consignor.customer.mapper.CustomerInfoMapperEx;
import com.zly.project.consignor.customer.mapper.LastCustomerInfoMapper;
import com.zly.project.contract.domain.FrameworkContract;
import com.zly.project.contract.mapper.FrameworkContractMapper;
import com.zly.project.customerForwarderContract.domain.CustomerForwarderContract;
import com.zly.project.customerForwarderContract.domain.CustomerForwarderContractAttachmentInfo;
import com.zly.project.customerForwarderContract.mapper.CustomerForwarderContractAttachmentInfoMapper;
import com.zly.project.customerForwarderContract.mapper.CustomerForwarderContractMapper;
import com.zly.project.finance.dingDing.domain.FinanceDdInfo;
import com.zly.project.finance.dingDing.mapper.FinanceDdInfoMapper;
import com.zly.project.finance.statement.domain.FinanceStatement;
import com.zly.project.finance.statement.domain.FinanceStatementBill;
import com.zly.project.finance.statement.domain.FinanceStatementWaybill;
import com.zly.project.finance.statement.domain.req.FinanceStatementContractRelation;
import com.zly.project.finance.statement.mapper.FinanceStatementBillMapper;
import com.zly.project.finance.statement.mapper.FinanceStatementContractRelationMapper;
import com.zly.project.finance.statement.mapper.FinanceStatementMapper;
import com.zly.project.finance.statement.mapper.FinanceStatementWaybillMapper;
import com.zly.project.freightforwarder.domain.FreightForwarderInfo;
import com.zly.project.freightforwarder.mapper.FreightForwarderInfoMapper;
import com.zly.project.group.domain.FrameworkContractSubchain;
import com.zly.project.group.domain.WaybillContractChain;
import com.zly.project.group.domain.WaybillCustomerRelation;
import com.zly.project.group.mapper.FrameworkContractSubchainMapper;
import com.zly.project.group.mapper.WaybillContractChainMapperEx;
import com.zly.project.group.mapper.WaybillCustomerRelationMapper;
import com.zly.project.makecode.domain.MakeCode;
import com.zly.project.makecode.domain.MakeCodeRule;
import com.zly.project.makecode.mapper.MakeCodeMapper;
import com.zly.project.makecode.mapper.MakeCodeRuleMapper;
import com.zly.project.service.customerservice.domain.ContractServiceInfo;
import com.zly.project.service.customerservice.domain.CustomerServiceInfo;
import com.zly.project.service.customerservice.mapper.ContractServiceInfoMapper;
import com.zly.project.service.customerservice.mapper.CustomerServiceInfoMapper;
import com.zly.project.settlement.domain.CustomerAccount;
import com.zly.project.settlement.domain.CustomerAccountSub;
import com.zly.project.settlement.domain.CustomerTradeApply;
import com.zly.project.settlement.mapper.CustomerAccountMapper;
import com.zly.project.settlement.mapper.CustomerAccountSubMapper;
import com.zly.project.settlement.mapper.CustomerTradeApplyMapper;
import com.zly.project.system.domain.FreightForwarderActualRelation;
import com.zly.project.system.domain.FreightForwarderDriverRelation;
import com.zly.project.system.domain.FreightForwarderVehicleRelation;
import com.zly.project.system.mapper.FreightForwarderActualRelationMapper;
import com.zly.project.system.mapper.FreightForwarderDriverRelationMapper;
import com.zly.project.system.mapper.FreightForwarderVehicleRelationMapper;
import com.zly.project.transport.waybill.domain.*;
import com.zly.project.transport.waybill.mapper.*;

/**
 * 善道原始数据查询类
 */
@DataSource(value = DataSourceType.SHANDAOFROM)
@Service
public class ShandaoDataOriginalService {
	// 网货相关
	@Resource
	private FreightForwarderInfoMapper freightForwarderInfoMapper;

	// 托运人相关
	@Resource
	private CustomerInfoMapperEx customerInfoMapperEx;
	@Resource
	private CustomerAttachmentInfoMapperEx customerAttachmentInfoMapperEx;
	@Resource
	private CustomerContractMapper customerContractMapper;
	@Resource
	private CustomerForwarderContractMapper customerForwarderContractMapper;
	@Resource
	private CustomerForwarderContractAttachmentInfoMapper customerForwarderContractAttachmentInfoMapper;
	@Resource
	private CustomerServiceInfoMapper customerServiceInfoMapper;
	@Resource
	private LastCustomerInfoMapper lastCustomerInfoMapper;
	@Resource
	private FreightForwarderCustomerRelationMapper forwarderCustomerRelationMapper;
	@Resource
	private CustomerAccountMapper customerAccountMapper;

	// 项目相关
	@Resource
	private FrameworkContractMapper contractMapper;
	@Resource
	private FrameworkContractAttachmentInfoMapper contractAttachmentInfoMapper;
	@Resource
	private FrameworkContractSubchainMapper contractSubchainMapper;
	@Resource
	private ContractServiceInfoMapper contractServiceInfoMapper;
	@Resource
	private CustomerAccountSubMapper customerAccountSubMapper;

	// 运力相关
	@Resource
	private ActualCarrierAttachmentInfoMapper carrierAttachmentInfoMapper;
	@Resource
	private ActualCarrierInfoMapper carrierInfoMapper;
	@Resource
	private ActualCarrierExtendMapper carrierExtendMapper;
	@Resource
	private DriverMapper driverMapper;
	@Resource
	private DriverAttachmentInfoMapper driverAttachmentInfoMapper;
	@Resource
	private VehicleMapper vehicleMapper;
	@Resource
	private VehicleTrailerMapper vehicleTrailerMapper;
	@Resource
	private VehicleAttachmentInfoMapper vehicleAttachmentInfoMapper;
	@Resource
	private PayeeInfoMapper payeeInfoMapper;
	@Resource
	private PayeeAttachmentInfoMapper payeeAttachmentInfoMapper;
	@Resource
	private CustomerActualRelationMapper customerActualRelationMapper;
	@Resource
	private CustomerDriverRelationMapper customerDriverRelationMapper;
	@Resource
	private CustomerPayeeRelationMapper customerPayeeRelationMapper;
	@Resource
	private CustomerVehicleRelationMapper customerVehicleRelationMapper;
	@Resource
	private FreightForwarderActualRelationMapper forwarderActualRelationMapper;
	@Resource
	private FreightForwarderDriverRelationMapper forwarderDriverRelationMapper;
	@Resource
	private FreightForwarderVehicleRelationMapper forwarderVehicleRelationMapper;
	@Resource
	private ActualDriverRelationMapper actualDriverRelationMapper;
	@Resource
	private ActualCarrierDriverRelationAttachmentInfoMapper carrierDriverRelationAttachmentInfoMapper;
	@Resource
	private ActualVehicleRelationMapper actualVehicleRelationMapper;
	@Resource
	private ActualCarrierVehicleRelationAttachmentInfoMapper carrierVehicleRelationAttachmentInfoMapper;

	// 运单相关
	@Resource
	private WaybillMapper waybillMapper;
	@Resource
	private WaybillContractChainMapperEx waybillContractChainMapperEx;
	@Resource
	private WaybillCustomerRelationMapper waybillCustomerRelationMapper;
	@Resource
	private WaybillSlaveMapper waybillSlaveMapper;
	@Resource
	private WaybillAttachmentInfoMapper waybillAttachmentInfoMapper;
	@Resource
	private WaybillGoodsMapper waybillGoodsMapper;
	@Resource
	private WaybillFareMapper waybillFareMapper;
	@Resource
	private WaybillMiniprogramTrackMapper waybillMiniprogramTrackMapper;
	@Resource
	private WaybillPolicyRecordMapper waybillPolicyRecordMapper;
	@Resource
	private MakeCodeMapper makeCodeMapper;
	@Resource
	private MakeCodeRuleMapper makeCodeRuleMapper;
	@Resource
	private ConsigneeInfoMapper consigneeInfoMapper;
	@Resource
	private ConsignorInfoMapper consignorInfoMapper;

	// 支付
	@Resource
	private CustomerTradeApplyMapper customerTradeApplyMapper;

	// 发票
	@Resource
	private FinanceDdInfoMapper financeDdInfoMapper;
	@Resource
	private FinanceStatementMapper financeStatementMapper;
	@Resource
	private FinanceStatementBillMapper financeStatementBillMapper;
	@Resource
	private FinanceStatementContractRelationMapper financeStatementContractRelationMapper;
	@Resource
	private FinanceStatementWaybillMapper financeStatementWaybillMapper;
	@Resource
	private SysAttachmentInfoMapper sysAttachmentInfoMapper; // 发票附件表

	public FreightForwarderInfo getFreightForwarderId(String name) {
		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoMapper.selectFreightForwarderInfoByName(name);
		return freightForwarderInfo;
	}

	public List<Waybill> getWaybills(Long freightForwarderId) {
		Waybill waybillSearch = new Waybill();
		waybillSearch.setFreightForwarderId(freightForwarderId);
		waybillSearch.setStatusList(Arrays.asList(2, 3, 4));
		List<Waybill> waybills = waybillMapper.selectWaybillListOld(waybillSearch);
		return waybills;
	}

	public List<WaybillCustomerRelation> getWaybillCustomerRelations(List<Long> waybillIds) {
		List<WaybillCustomerRelation> list = waybillCustomerRelationMapper.selectRelationByWaybillIds(waybillIds);
		return list;
	}

	public List<WaybillContractChain> getWaybillContractChains(List<Long> waybillIds) {
		return waybillContractChainMapperEx.findByIds(waybillIds);
	}

	public List<CustomerInfo> getCustomers(List<Long> customerIds) {
		return customerInfoMapperEx.selectCustomerServiceInfoByCustomerIds(customerIds);
	}

	public List<CustomerAttachmentInfo> getCustomerAttachmentInfos(List<Long> customerIds) {
		return customerAttachmentInfoMapperEx.selectCustomerAttachmentInfoByRelationId(customerIds);
	}

	public List<CustomerContract> getCustomerContracts(List<Long> customerIds) {
		return customerContractMapper.selectCustomerContractBycustomerIds(customerIds);
	}

	public List<CustomerForwarderContract> getCustomerForwarderContracts(List<Long> customerIds, Long forwarderId) {
		return customerForwarderContractMapper.selectContractByCustomerIdsAndForwarderId(customerIds, forwarderId);
	}

	public List<CustomerForwarderContractAttachmentInfo> getCustomerForwarderContractAttachmentInfos(List<Long> relationIds) {
		return customerForwarderContractAttachmentInfoMapper.selectByRelationIds(relationIds);
	}

	public List<CustomerServiceInfo> getCustomerServiceInfos(List<Long> customerIds) {
		return customerServiceInfoMapper.selectByCustomerIds(customerIds);
	}

	public List<LastCustomerInfo> getLastCustomerInfos(List<Long> customerIds) {
		return lastCustomerInfoMapper.selectByCustomerIds(customerIds);
	}

	public List<FreightForwarderCustomerRelation> getFreightForwarderCustomerRelations(List<Long> customerIds, Long forwarderId) {
		return forwarderCustomerRelationMapper.selectByCustomerIdsAndForwarderId(customerIds, forwarderId);
	}

	public List<FrameworkContract> getFrameworkContracts(List<Long> contractIds) {
		return contractMapper.selectFrameworkContractByIds(contractIds);
	}

	public List<FrameworkContractAttachmentInfo> getFrameworkContractAttachmentInfos(List<Long> contractIds) {
		return contractAttachmentInfoMapper.selectByRelationIds(contractIds);
	}

	public List<FrameworkContractSubchain> getFrameworkContractSubchains(List<Long> contractIds) {
		return contractSubchainMapper.selectByContractIds(contractIds);
	}

	public List<ContractServiceInfo> getContractServiceInfos(List<Long> contractIds) {
		return contractServiceInfoMapper.selectByContractIds(contractIds);
	}

	public List<ActualCarrierInfo> getcarrierInfos(List<Long> carrierIds) {
		return carrierInfoMapper.selectByIdsOld(carrierIds);
	}

	public List<ActualCarrierAttachmentInfo> getCarrierAttachmentInfos(List<Long> carrierIds) {
		return carrierAttachmentInfoMapper.selectByCarrierIds(carrierIds);
	}

	public List<Driver> getDrivers(List<Long> driverIds) {
		return driverMapper.selectByIds(driverIds);
	}

	public List<DriverAttachmentInfo> getDriverAttachmentInfos(List<Long> driverIds) {
		return driverAttachmentInfoMapper.selectDriverAttachmentInfoByDriverIds(driverIds);
	}

	public List<Vehicle> getVehicles(List<Long> vehicleIds) {
		return vehicleMapper.selectVehicleByIds(vehicleIds);
	}

	public List<VehicleAttachmentInfo> getVehicleAttachmentInfos(List<Long> vehicleIds) {
		return vehicleAttachmentInfoMapper.selectVehicleAttachmentInfoByVehicleIds(vehicleIds);
	}

	public List<PayeeInfo> getPayeeInfos(List<Long> payeeIds) {
		return payeeInfoMapper.selectPayeeInfoByIds(payeeIds);
	}

	public List<PayeeAttachmentInfo> getPayeeAttachmentInfos(List<Long> payeeIds) {
		return payeeAttachmentInfoMapper.selectByPayeeIds(payeeIds);
	}

	public List<ActualCarrierExtend> getCarrierExtends(List<Long> carrierIds) {
		return carrierExtendMapper.selectByCarrierId(carrierIds);
	}

	public List<VehicleTrailer> getVehicleTrailers(List<Long> vehicleIds) {
		return vehicleTrailerMapper.selectByVehicleIds(vehicleIds);
	}

	public List<CustomerActualRelation> getCustomerActualRelations(List<CustomerActualRelation> relations) {
		return customerActualRelationMapper.selectByCustomerIdsAndActualIds(relations);
	}

	public List<CustomerDriverRelation> getCustomerDriverRelations(List<CustomerDriverRelation> relations) {
		return customerDriverRelationMapper.selectByCustomerIdsAndDriverIds(relations);
	}

	public List<CustomerVehicleRelation> getCustomerVehicleRelations(List<CustomerVehicleRelation> relations) {
		return customerVehicleRelationMapper.selectByCustomerIdsAndVehicleIds(relations);
	}

	public List<CustomerPayeeRelation> getCustomerPayeeRelations(List<CustomerPayeeRelation> relations) {
		return customerPayeeRelationMapper.selectByCustomerIdsAndPayeeIds(relations);
	}

	public List<FreightForwarderActualRelation> getForwarderActualRelations(Long forwarderId, List<Long> carrierIds) {
		return forwarderActualRelationMapper.selectFreightForwarderActualRelationListByCarrierIds(forwarderId, carrierIds);
	}

	public List<FreightForwarderDriverRelation> getForwarderDriverRelations(Long forwarderId, List<Long> driverIds) {
		return forwarderDriverRelationMapper.selectByFreightForwarderIdAndDriverIds(forwarderId, driverIds);
	}

	public List<FreightForwarderVehicleRelation> getForwarderVehicleRelations(Long forwarderId, List<Long> vehicleIds) {
		return forwarderVehicleRelationMapper.selectByForwarderIdAndVehicleIds(forwarderId, vehicleIds);
	}

	public List<ActualDriverRelation> getActualDriverRelations(List<ActualDriverRelation> relations) {
		return actualDriverRelationMapper.selectByRelations(relations);
	}

	public List<ActualVehicleRelation> getActualVehicleRelations(List<ActualVehicleRelation> relations) {
		return actualVehicleRelationMapper.selectByRelations(relations);
	}

	public List<ActualCarrierDriverRelationAttachmentInfo> getCarrierDriverRelationAttachmentInfos(List<Long> relationIds) {
		return carrierDriverRelationAttachmentInfoMapper.selectActualCarrierDriverRelationAttachmentInfoByIds(relationIds);
	}

	public List<ActualCarrierVehicleRelationAttachmentInfo> getCarrierVehicleRelationAttachmentInfos(List<Long> relationIds) {
		return carrierVehicleRelationAttachmentInfoMapper.selectByRelationIds(relationIds);
	}

	public List<WaybillSlave> getWaybillSlaves(List<Long> waybillIds) {
		return waybillSlaveMapper.selectWaybillSlaveByIds(waybillIds);
	}

	public List<WaybillAttachmentInfo> getWaybillAttachmentInfos(List<Long> waybillIds) {
		return waybillAttachmentInfoMapper.selectWaybillAttachmentInfoByWaybillIds(waybillIds);
	}

	public List<WaybillGoods> getWaybillGoods(List<Long> waybillIds) {
		return waybillGoodsMapper.selectWaybillGoodsListByWaybillIds(waybillIds);
	}

	public List<WaybillFare> getWaybillFares(List<Long> waybillIds) {
		return waybillFareMapper.selectWaybillFareByWaybillIds(waybillIds);
	}

	public List<WaybillMiniprogramTrack> getWaybillMiniprogramTracks(List<Long> waybillIds) {
		return waybillMiniprogramTrackMapper.selectWaybillMiniprogramTrackByWaybillIds(waybillIds);
	}

	public List<WaybillPolicyRecord> getWaybillPolicyRecords(List<Long> waybillIds) {
		return waybillPolicyRecordMapper.selectWaybillPolicyRecordByWaybillIds(waybillIds);
	}

	public List<MakeCode> getMakeCodes(List<Long> makecodeIds) {
		return makeCodeMapper.selectMakeCodeByIds(makecodeIds);
	}

	public List<MakeCodeRule> getMakeCodeRules(List<Long> ruleIds) {
		return makeCodeRuleMapper.selectMakeCodeRuleByIds(ruleIds);
	}

	public List<ConsigneeInfo> getConsigneeInfos(List<Long> consigneeIds) {
		return consigneeInfoMapper.selectConsigneeInfoByIds(consigneeIds);
	}

	public List<ConsignorInfo> getConsignorInfos(List<Long> consignorIds) {
		return consignorInfoMapper.selectConsignorInfoByIds(consignorIds);
	}

	public List<CustomerAccount> getCustomerAccounts(List<Long> customerIds) {
		return customerAccountMapper.selectCustomerAccountByIds(customerIds);
	}

	public List<CustomerAccountSub> getCustomerAccountSubs(List<Long> contractIds) {
		return customerAccountSubMapper.selectCustomerAccountSubByContractIds(contractIds);
	}

	public List<CustomerAccount> getCustomerAccountsByIdcards(List<String> idcards) {
		return customerAccountMapper.selectCustomerAccountByIdCards(idcards);
	}

	public List<CustomerTradeApply> getCustomerTradeApplys(List<Long> waybillIds) {
		return customerTradeApplyMapper.selectCustomerTradeApplyByWaybillIds(waybillIds);
	}

	public List<FinanceStatementWaybill> getFinanceStatementWaybills(List<Long> waybillIds) {
		return financeStatementWaybillMapper.selectByWaybillIds(waybillIds);
	}

	public List<FinanceStatement> getFinanceStatements(List<Long> statementIds) {
		return financeStatementMapper.selectFinanceStatementByIds(statementIds);
	}

	public List<FinanceDdInfo> getFinanceDdInfos(List<Long> statementIds) {
		return financeDdInfoMapper.selectFinanceDdInfoByStatementIds(statementIds);
	}

	public List<FinanceStatementContractRelation> getFinanceStatementContractRelations(List<Long> statementIds) {
		return financeStatementContractRelationMapper.selectByStatementIds(statementIds);
	}

	public List<FinanceStatementBill> getFinanceStatementBills(List<Long> statementIds) {
		return financeStatementBillMapper.selectFinanceStatementBillByStatementIds(statementIds);
	}

	public List<SysAttachmentInfo> getSysAttachmentInfos(List<Long> billIds) {
		return sysAttachmentInfoMapper.selectSysAttachmentInfoByRelationIds(billIds);
	}
}
