package com.zly.project.backgroundOperation.service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.zly.common.utils.CommonUtil;
import com.zly.common.utils.ListUtils;
import com.zly.common.utils.ThreadPoolUtil;
import com.zly.framework.redis.RedisCache;
import com.zly.framework.web.domain.CommonResult;
import com.zly.project.carrier.carrier.domain.ActualCarrierExtend;
import com.zly.project.carrier.carrier.domain.ActualCarrierInfo;
import com.zly.project.carrier.carrier.domain.ActualDriverRelation;
import com.zly.project.carrier.carrier.domain.ActualVehicleRelation;
import com.zly.project.carrier.carrier.mapper.ActualCarrierExtendMapper;
import com.zly.project.carrier.carrier.mapper.ActualCarrierInfoMapper;
import com.zly.project.carrier.carrier.mapper.ActualDriverRelationMapperEx;
import com.zly.project.carrier.carrier.mapper.ActualVehicleRelationMapperEx;
import com.zly.project.carrier.driver.domain.Driver;
import com.zly.project.carrier.driver.mapper.DriverMapper;
import com.zly.project.carrier.payee.domain.CustomerPayeeRelation;
import com.zly.project.carrier.payee.domain.PayeeInfo;
import com.zly.project.carrier.payee.mapper.CustomerPayeeRelationMapper;
import com.zly.project.carrier.payee.mapper.PayeeInfoMapper;
import com.zly.project.carrier.vehicle.domain.CustomerVehicleRelation;
import com.zly.project.carrier.vehicle.domain.Vehicle;
import com.zly.project.carrier.vehicle.domain.VehicleTrailer;
import com.zly.project.carrier.vehicle.mapper.CustomerVehicleRelationMapper;
import com.zly.project.carrier.vehicle.mapper.VehicleMapper;
import com.zly.project.carrier.vehicle.mapper.VehicleTrailerMapper;
import com.zly.project.common.domain.*;
import com.zly.project.common.mapper.*;
import com.zly.project.consignor.consignee.domain.ConsigneeInfo;
import com.zly.project.consignor.consignee.mapper.ConsigneeInfoMapper;
import com.zly.project.consignor.consignor.domain.ConsignorInfo;
import com.zly.project.consignor.consignor.mapper.ConsignorInfoMapper;
import com.zly.project.consignor.contract.domain.CustomerContract;
import com.zly.project.consignor.contract.mapper.CustomerContractMapper;
import com.zly.project.consignor.customer.domain.CustomerActualRelation;
import com.zly.project.consignor.customer.domain.CustomerDriverRelation;
import com.zly.project.consignor.customer.domain.CustomerInfo;
import com.zly.project.consignor.customer.domain.LastCustomerInfo;
import com.zly.project.consignor.customer.mapper.CustomerActualRelationMapperEx;
import com.zly.project.consignor.customer.mapper.CustomerDriverRelationMapper;
import com.zly.project.consignor.customer.mapper.CustomerInfoMapper;
import com.zly.project.consignor.customer.mapper.LastCustomerInfoMapper;
import com.zly.project.contract.domain.FrameworkContract;
import com.zly.project.contract.mapper.FrameworkContractMapper;
import com.zly.project.customerForwarderContract.domain.CustomerForwarderContract;
import com.zly.project.customerForwarderContract.domain.CustomerForwarderContractAttachmentInfo;
import com.zly.project.customerForwarderContract.mapper.CustomerForwarderContractAttachmentInfoMapper;
import com.zly.project.customerForwarderContract.mapper.CustomerForwarderContractMapper;
import com.zly.project.finance.dingDing.domain.FinanceDdInfo;
import com.zly.project.finance.dingDing.mapper.FinanceDdInfoMapper;
import com.zly.project.finance.statement.domain.FinanceStatement;
import com.zly.project.finance.statement.domain.FinanceStatementBill;
import com.zly.project.finance.statement.domain.FinanceStatementWaybill;
import com.zly.project.finance.statement.domain.req.FinanceStatementContractRelation;
import com.zly.project.finance.statement.mapper.FinanceStatementBillMapper;
import com.zly.project.finance.statement.mapper.FinanceStatementContractRelationMapper;
import com.zly.project.finance.statement.mapper.FinanceStatementMapper;
import com.zly.project.finance.statement.mapper.FinanceStatementWaybillMapper;
import com.zly.project.freightforwarder.domain.FreightForwarderInfo;
import com.zly.project.freightforwarder.mapper.FreightForwarderInfoMapper;
import com.zly.project.group.domain.FrameworkContractSubchain;
import com.zly.project.group.domain.WaybillContractChain;
import com.zly.project.group.domain.WaybillCustomerRelation;
import com.zly.project.group.mapper.FrameworkContractSubchainMapper;
import com.zly.project.group.mapper.WaybillContractChainMapper;
import com.zly.project.group.mapper.WaybillCustomerRelationMapper;
import com.zly.project.makecode.domain.MakeCode;
import com.zly.project.makecode.domain.MakeCodeRule;
import com.zly.project.makecode.mapper.MakeCodeMapper;
import com.zly.project.makecode.mapper.MakeCodeRuleMapper;
import com.zly.project.service.customerservice.domain.ContractServiceInfo;
import com.zly.project.service.customerservice.domain.CustomerServiceInfo;
import com.zly.project.service.customerservice.mapper.ContractServiceInfoMapper;
import com.zly.project.service.customerservice.mapper.CustomerServiceInfoMapper;
import com.zly.project.settlement.domain.CustomerAccount;
import com.zly.project.settlement.domain.CustomerAccountSub;
import com.zly.project.settlement.domain.CustomerTradeApply;
import com.zly.project.settlement.mapper.CustomerAccountMapper;
import com.zly.project.settlement.mapper.CustomerAccountSubMapper;
import com.zly.project.settlement.mapper.CustomerTradeApplyMapper;
import com.zly.project.system.domain.FreightForwarderActualRelation;
import com.zly.project.system.domain.FreightForwarderDriverRelation;
import com.zly.project.system.domain.FreightForwarderVehicleRelation;
import com.zly.project.system.mapper.FreightForwarderActualRelationMapper;
import com.zly.project.system.mapper.FreightForwarderDriverRelationMapper;
import com.zly.project.system.mapper.FreightForwarderVehicleRelationMapper;
import com.zly.project.transfer.mapper.TransferMapper;
import com.zly.project.transport.waybill.domain.*;
import com.zly.project.transport.waybill.mapper.*;

import lombok.extern.slf4j.Slf4j;

/**
 * 善道数据同步
 */
@Service
@Slf4j
public class ShandaoDataHandlerService {

	@Resource
	ShandaoDataOriginalService originalService;

	// 托运人
	@Resource
	private CustomerInfoMapper customerInfoMapper;
	@Resource
	private CustomerContractMapper customerContractMapper;
	@Resource
	private CustomerAttachmentInfoMapper customerAttachmentInfoMapper;
	@Resource
	private CustomerForwarderContractMapper customerForwarderContractMapper;
	@Resource
	private CustomerForwarderContractAttachmentInfoMapper customerForwarderContractAttachmentInfoMapper;
	@Resource
	private CustomerServiceInfoMapper customerServiceInfoMapper;
	@Resource
	private LastCustomerInfoMapper lastCustomerInfoMapper;
	@Resource
	private FreightForwarderCustomerRelationMapper forwarderCustomerRelationMapper;
	@Resource
	private CustomerAccountMapper customerAccountMapper;

	// 项目
	@Resource
	private FrameworkContractMapper contractMapper;
	@Resource
	private FrameworkContractAttachmentInfoMapper contractAttachmentInfoMapper;
	@Resource
	private FrameworkContractSubchainMapper contractSubchainMapper;
	@Resource
	private ContractServiceInfoMapper contractServiceInfoMapper;
	@Resource
	private CustomerAccountSubMapper customerAccountSubMapper;

	// 运力基础信息
	@Resource
	private ActualCarrierAttachmentInfoMapper carrierAttachmentInfoMapper;
	@Resource
	private ActualCarrierInfoMapper carrierInfoMapper;
	@Resource
	private ActualCarrierExtendMapper carrierExtendMapper;
	@Resource
	private DriverMapper driverMapper;
	@Resource
	private DriverAttachmentInfoMapper driverAttachmentInfoMapper;
	@Resource
	private VehicleMapper vehicleMapper;
	@Resource
	private VehicleTrailerMapper vehicleTrailerMapper;
	@Resource
	private VehicleAttachmentInfoMapper vehicleAttachmentInfoMapper;
	@Resource
	private PayeeInfoMapper payeeInfoMapper;
	@Resource
	private PayeeAttachmentInfoMapper payeeAttachmentInfoMapper;
	// 运力、托运人关联关系
	@Resource
	private CustomerActualRelationMapperEx customerActualRelationMapperEx;
	@Resource
	private CustomerDriverRelationMapper customerDriverRelationMapper;
	@Resource
	private CustomerPayeeRelationMapper customerPayeeRelationMapper;
	@Resource
	private CustomerVehicleRelationMapper customerVehicleRelationMapper;
	// 运力、网货关联关系
	@Resource
	private FreightForwarderActualRelationMapper forwarderActualRelationMapper;
	@Resource
	private FreightForwarderDriverRelationMapper forwarderDriverRelationMapper;
	@Resource
	private FreightForwarderVehicleRelationMapper forwarderVehicleRelationMapper;
	// 运力间关联关系
	@Resource
	private ActualDriverRelationMapperEx actualDriverRelationMapperEx;
	@Resource
	private ActualCarrierDriverRelationAttachmentInfoMapper carrierDriverRelationAttachmentInfoMapper;
	@Resource
	private ActualVehicleRelationMapperEx actualVehicleRelationMapperEx;
	@Resource
	private ActualCarrierVehicleRelationAttachmentInfoMapper carrierVehicleRelationAttachmentInfoMapper;

	// 运单
	@Resource
	private WaybillMapperEx waybillMapperEx;
	@Resource
	private WaybillMapper waybillMapper;
	@Resource
	private WaybillContractChainMapper waybillContractChainMapper;
	@Resource
	private WaybillCustomerRelationMapper waybillCustomerRelationMapper;
	@Resource
	private WaybillSlaveMapper waybillSlaveMapper;
	@Resource
	private WaybillAttachmentInfoMapper waybillAttachmentInfoMapper;
	@Resource
	private WaybillGoodsMapper waybillGoodsMapper;
	@Resource
	private WaybillFareMapper waybillFareMapper;
	@Resource
	private WaybillMiniprogramTrackMapper waybillMiniprogramTrackMapper;
	@Resource
	private WaybillPolicyRecordMapper waybillPolicyRecordMapper;
	@Resource
	private MakeCodeMapper makeCodeMapper;
	@Resource
	private MakeCodeRuleMapper makeCodeRuleMapper;
	@Resource
	private ConsigneeInfoMapper consigneeInfoMapper;
	@Resource
	private ConsignorInfoMapper consignorInfoMapper;

	// 支付
	@Resource
	private CustomerTradeApplyMapper customerTradeApplyMapper;

	// 发票
	@Resource
	private FinanceDdInfoMapper financeDdInfoMapper;
	@Resource
	private FinanceStatementMapper financeStatementMapper;
	@Resource
	private FinanceStatementBillMapper financeStatementBillMapper;
	@Resource
	private FinanceStatementContractRelationMapper financeStatementContractRelationMapper;
	@Resource
	private FinanceStatementWaybillMapper financeStatementWaybillMapper;
	@Resource
	private SysAttachmentInfoMapper sysAttachmentInfoMapper; // 发票附件表
	@Resource
	private RedisCache redisCache;
	@Resource
	private TransferMapper transferMapper;
	@Resource
	private FreightForwarderActualRelationMapper freightForwarderActualRelationMapper;
	@Resource
	private FreightForwarderDriverRelationMapper freightForwarderDriverRelationMapper;
	@Resource
	private FreightForwarderVehicleRelationMapper freightForwarderVehicleRelationMapper;
	@Resource
	private FreightForwarderInfoMapper freightForwarderInfoMapper;
	@Resource
	private FreightForwarderCustomerRelationMapper freightForwarderCustomerRelationMapper;

	/**
	 * 全量同步善道数据
	 *
	 * @return
	 */
	// @Transactional
	public CommonResult synchronousFullData() {
		// 1、清理删除善道库中需重新同步的表数据

		// 2、从集团库复制善道数据至善道库
		CommonResult copyRes = copyDataFromGroupToShandao();
		if (CommonResult.isNotSuccess(copyRes)) {
			return copyRes;
		}
		// 3、清洗善道库中数据

		// 4、清洗后的善后工作（补充协议之类）

		return CommonResult.success();
	}

	/**
	 * 从集团库复制善道数据至善道库
	 *
	 * @return
	 */
	public CommonResult copyDataFromGroupToShandao() {
		// 基础步骤：获取善道所有未删除运单及运单托运人关系
		// FreightForwarderInfo freightForwarderInfo = originalService.getFreightForwarderId("湖北汉联物流产业发展有限公司");
		FreightForwarderInfo freightForwarderInfo = originalService.getFreightForwarderId("江苏善道智运科技有限公司");
		if (freightForwarderInfo == null)
			return CommonResult.error("网货平台不存在");
		Long forwarderId = freightForwarderInfo.getId();

		List<Waybill> waybills = originalService.getWaybills(forwarderId);
		if (CommonUtil.isNullOrEmpty(waybills))
			return CommonResult.error("网货平台下没有需要处理的运单");

		List<Long> waybillIds = waybills.stream().map(w -> w.getId()).collect(Collectors.toList());
		List<WaybillCustomerRelation> waybillCustomerRelations = originalService.getWaybillCustomerRelations(waybillIds);

		// 从运单中获取基础数据
		List<Long> customerIds = waybillCustomerRelations.stream().map(r -> r.getCustomerId()).distinct().collect(Collectors.toList());
		Set<Long> contractIds = new HashSet<>();
		Set<Long> carrierIds = new HashSet<>();
		Set<Long> driverIds = new HashSet<>();
		Set<Long> vehicleIds = new HashSet<>();
		Set<Long> payeeIds = new HashSet<>();
		Set<String> actualDrivers = new HashSet<>();
		Set<String> actualVehicles = new HashSet<>();
		for (Waybill waybill : waybills) {
			contractIds.add(waybill.getFrameworkContractId());
			carrierIds.add(waybill.getActualCarrierId());
			driverIds.add(waybill.getDriverId());
			vehicleIds.add(waybill.getVehicleId());
			payeeIds.add(waybill.getPayeeId());
			if (CommonUtil.isNotNullOrEmpty(waybill.getActualCarrierIdentityCard())) {
				if (CommonUtil.isNotNullOrEmpty(waybill.getDrivingLicense()) && !waybill.getActualCarrierIdentityCard().equals(waybill.getDrivingLicense())) {
					actualDrivers.add(waybill.getActualCarrierId() + "_" + waybill.getDriverId());
				}
				if (CommonUtil.isNotNullOrEmpty(waybill.getVehicleNumber())) {
					actualVehicles.add(waybill.getActualCarrierId() + "_" + waybill.getVehicleId());
				}
			}
		}
		log.info("善道数据同步——基准运单数据——查询成功");

		// 1、处理运单相关
		this.dealWaybills(waybillIds, waybills, waybillCustomerRelations);
		log.info("善道数据同步——运单——插入成功");

		// 2、处理托运人相关
		this.dealCustomers(customerIds, forwarderId);
		log.info("善道数据同步——托运人——插入成功");

		// 3、处理项目相关
		this.dealClontracts(new ArrayList<>(contractIds));
		log.info("善道数据同步——项目——插入成功");

		// 4、处理运力相关
		this.dealTransportationCapacities(customerIds, forwarderId, new ArrayList<>(carrierIds), new ArrayList<>(driverIds), new ArrayList<>(vehicleIds), new ArrayList<>(payeeIds), actualDrivers,
				actualVehicles);
		log.info("善道数据同步——运力——插入成功");

		// 5、处理开票相关
		this.dealStatements(waybillIds);
		log.info("善道数据同步——开票——插入成功");

		return CommonResult.success();
	}

	/**
	 * 插入托运人相关信息
	 *
	 * @param customerIds
	 * @param forwarderId
	 * @return
	 */
	private CommonResult dealCustomers(List<Long> customerIds, Long forwarderId) {
		// 1、基础信息
		List<CustomerInfo> customerInfos = originalService.getCustomers(customerIds);
		for (CustomerInfo customerInfo : customerInfos) {
			ThreadPoolUtil.getPool().execute(() -> {
				customerInfoMapper.insertCustomerInfo(customerInfo);
			});
		}
		// 2、附件信息
		List<CustomerAttachmentInfo> customerAttachmentInfos = originalService.getCustomerAttachmentInfos(customerIds);
		for (CustomerAttachmentInfo attachmentInfo : customerAttachmentInfos) {
			ThreadPoolUtil.getPool().execute(() -> {
				customerAttachmentInfoMapper.insertCustomerAttachmentInfo(attachmentInfo);
			});
		}
		// 3、托运人货主信息
		List<LastCustomerInfo> lastCustomerInfos = originalService.getLastCustomerInfos(customerIds);
		for (LastCustomerInfo lastCustomerInfo : lastCustomerInfos) {
			ThreadPoolUtil.getPool().execute(() -> {
				lastCustomerInfoMapper.insertLastCustomerInfo(lastCustomerInfo);
			});
		}
		// 4、客户合约
		List<CustomerContract> customerContracts = originalService.getCustomerContracts(customerIds);
		for (CustomerContract customerContract : customerContracts) {
			ThreadPoolUtil.getPool().execute(() -> {
				customerContractMapper.insertCustomerContract(customerContract);
			});
		}
		// 5、跟网货委托合同
		List<CustomerForwarderContract> customerForwarderContracts = originalService.getCustomerForwarderContracts(customerIds, forwarderId);
		for (CustomerForwarderContract customerForwarderContract : customerForwarderContracts) {
			ThreadPoolUtil.getPool().execute(() -> {
				customerForwarderContractMapper.insertCustomerForwarderContract(customerForwarderContract);
			});
		}
		// 6、委托合同附件
		if (CommonUtil.isNotNullOrEmpty(customerForwarderContracts)) {
			List<Long> forwarderContractIds = customerForwarderContracts.stream().map(c -> c.getId()).collect(Collectors.toList());
			List<CustomerForwarderContractAttachmentInfo> customerForwarderContractAttachmentInfos = originalService.getCustomerForwarderContractAttachmentInfos(forwarderContractIds);
			for (CustomerForwarderContractAttachmentInfo customerForwarderContractAttachmentInfo : customerForwarderContractAttachmentInfos) {
				ThreadPoolUtil.getPool().execute(() -> {
					customerForwarderContractAttachmentInfoMapper.insertCustomerForwarderContractAttachmentInfo(customerForwarderContractAttachmentInfo);
				});
			}
		}
		// 7、项目服务项
		List<CustomerServiceInfo> customerServiceInfos = originalService.getCustomerServiceInfos(customerIds);
		for (CustomerServiceInfo customerServiceInfo : customerServiceInfos) {
			ThreadPoolUtil.getPool().execute(() -> {
				customerServiceInfoMapper.insertCustomerServiceInfo(customerServiceInfo);
			});
		}
		// 8、托运人网货关联关系
		List<FreightForwarderCustomerRelation> freightForwarderCustomerRelations = originalService.getFreightForwarderCustomerRelations(customerIds, forwarderId);
		for (FreightForwarderCustomerRelation freightForwarderCustomerRelation : freightForwarderCustomerRelations) {
			ThreadPoolUtil.getPool().execute(() -> {
				forwarderCustomerRelationMapper.insertFreightForwarderCustomerRelation(freightForwarderCustomerRelation);
			});
		}
		// 9、托运人账户信息
		List<CustomerAccount> customerAccounts = originalService.getCustomerAccounts(customerIds);
		customerAccountMapper.insertCustomerAccounts(customerAccounts);

		customerIds = null;
		return CommonResult.success();
	}

	/**
	 * 插入项目信息
	 *
	 * @param contractIds
	 */
	private CommonResult dealClontracts(List<Long> contractIds) {
		// 1、项目基础信息
		List<FrameworkContract> contracts = originalService.getFrameworkContracts(contractIds);
		contractMapper.insertFrameworkContracts(contracts);
		// 2、项目附件信息
		List<FrameworkContractAttachmentInfo> contractAttachmentInfos = originalService.getFrameworkContractAttachmentInfos(contractIds);
		if (CommonUtil.isNotNullOrEmpty(contractAttachmentInfos)) {
			contractAttachmentInfoMapper.insertFrameworkContractAttachmentInfos(contractAttachmentInfos);

		}
		// 3、项目转包链信息
		List<FrameworkContractSubchain> contractSubchains = originalService.getFrameworkContractSubchains(contractIds);
		contractSubchainMapper.insertFrameworkContractSubchains(contractSubchains);
		// 4、项目增值服务信息
		List<ContractServiceInfo> contractServiceInfos = originalService.getContractServiceInfos(contractIds);
		contractServiceInfoMapper.insertContractServiceInfos(contractServiceInfos);
		// 5、项目子账户信息
		List<CustomerAccountSub> customerAccountSubs = originalService.getCustomerAccountSubs(contractIds);
		customerAccountSubMapper.insertCustomerAccountSubs(customerAccountSubs);

		contracts = null;
		return CommonResult.success();
	}

	/**
	 * 插入运力信息
	 *
	 * @param customerIds
	 * @param forwarderId
	 * @param carrierIds
	 * @param driverIds
	 * @param vehicleIds
	 * @param payeeIds
	 * @param actualDrivers
	 * @param actualVehicles
	 */
	private CommonResult dealTransportationCapacities(List<Long> customerIds, Long forwarderId, List<Long> carrierIds, List<Long> driverIds, List<Long> vehicleIds, List<Long> payeeIds,
			Set<String> actualDrivers, Set<String> actualVehicles) {
		// 1、处理运力基础信息 当中含有收款人账户信息
		dealCapacityBaseInfo(carrierIds, driverIds, vehicleIds, payeeIds);
		// 2、处理托运人运力关联关系
		dealCustomerCapacityRelations(customerIds, carrierIds, driverIds, vehicleIds, payeeIds);
		// 3、处理网货、运力关联关系
		dealForwarderCapacityRelations(forwarderId, carrierIds, driverIds, vehicleIds, payeeIds);
		// 4、处理运力间关联关系
		dealCapacityRelations(actualDrivers, actualVehicles);
		// 5、结束后返回
		return CommonResult.success();
	}

	/**
	 * 插入运力基础信息
	 *
	 * @param carrierIds
	 * @param driverIds
	 * @param vehicleIds
	 * @param payeeIds
	 * @return
	 */
	private CommonResult dealCapacityBaseInfo(List<Long> carrierIds, List<Long> driverIds, List<Long> vehicleIds, List<Long> payeeIds) {
		// 1.1、承运人基础信息
		List<ActualCarrierInfo> carrierInfos = originalService.getcarrierInfos(carrierIds);
		carrierInfoMapper.insertActualCarrierInfoList(carrierInfos);
		// 1.2、承运人附件信息
		List<ActualCarrierAttachmentInfo> carrierAttachmentInfos = originalService.getCarrierAttachmentInfos(carrierIds);
		if (CommonUtil.isNotNullOrEmpty(carrierAttachmentInfos)) {
			carrierAttachmentInfoMapper.insertActualCarrierAttachmentInfos(carrierAttachmentInfos);
		}
		// 1.3、承运人扩展表信息
		List<ActualCarrierExtend> carrierExtends = originalService.getCarrierExtends(carrierIds);
		if (CommonUtil.isNotNullOrEmpty(carrierExtends)) {
			carrierExtendMapper.insertActualCarrierExtends(carrierExtends);
		}

		// 2.1、司机基础信息
		List<List<Long>> lists = ListUtils.splitList(driverIds, 1000);
		for (List<Long> driverSplitIds : lists) {
			ThreadPoolUtil.getPool().execute(() -> {
				List<Driver> drivers = originalService.getDrivers(driverSplitIds);
				driverMapper.insertDriversAllFields(drivers);
				// 2.2、司机附件信息
				List<DriverAttachmentInfo> driverAttachmentInfos = originalService.getDriverAttachmentInfos(driverSplitIds);
				if (CommonUtil.isNotNullOrEmpty(driverAttachmentInfos)) {
					driverAttachmentInfoMapper.insertDriverAttachmentInfos(driverAttachmentInfos);
				}
			});
		}

		// 3.1、车辆基础信息
		lists = ListUtils.splitList(vehicleIds, 1000);
		for (List<Long> vehicleSplitIds : lists) {
			ThreadPoolUtil.getPool().execute(() -> {
				List<Vehicle> vehicles = originalService.getVehicles(vehicleSplitIds);
				vehicleMapper.insertVehiclesAllFields(vehicles);
				// 3.2、车辆附件信息
				List<VehicleAttachmentInfo> vehicleAttachmentInfos = originalService.getVehicleAttachmentInfos(vehicleSplitIds);
				if (CommonUtil.isNotNullOrEmpty(vehicleAttachmentInfos)) {
					vehicleAttachmentInfoMapper.insertVehicleAttachmentInfos(vehicleAttachmentInfos);
				}
				// 3.3、挂车信息
				List<VehicleTrailer> vehicleTrailers = originalService.getVehicleTrailers(vehicleSplitIds);
				if (CommonUtil.isNotNullOrEmpty(vehicleTrailers)) {
					vehicleTrailerMapper.insertVehicleTrailers(vehicleTrailers);
				}
			});
		}

		// 4.1、收款人基础信息
		lists = ListUtils.splitList(payeeIds, 1000);
		for (List<Long> payeeSplitIds : lists) {
			ThreadPoolUtil.getPool().execute(() -> {
				List<PayeeInfo> payeeInfos = originalService.getPayeeInfos(payeeSplitIds);
				payeeInfoMapper.insertPayeeInfos(payeeInfos);
				// 4.2、收款人附件信息
				List<PayeeAttachmentInfo> payeeAttachmentInfos = originalService.getPayeeAttachmentInfos(payeeSplitIds);
				if (CommonUtil.isNotNullOrEmpty(payeeAttachmentInfos)) {
					payeeAttachmentInfoMapper.insertPayeeAttachmentInfoList(payeeAttachmentInfos);
				}
				// 4.3、处理收款人账户信息
				List<String> payeeIdcards = payeeInfos.stream().map(p -> p.getIdentityCard()).distinct().collect(Collectors.toList());
				List<CustomerAccount> customerAccounts = originalService.getCustomerAccountsByIdcards(payeeIdcards);
				if (CommonUtil.isNotNullOrEmpty(customerAccounts)) {
					customerAccountMapper.insertCustomerAccounts(customerAccounts);
				}
			});
		}

		return CommonResult.success();

	}

	/**
	 * 插入托运人运力关联关系
	 *
	 * @param customerIds
	 * @param carrierIds
	 * @param driverIds
	 * @param vehicleIds
	 * @param payeeIds
	 * @return
	 */
	private CommonResult dealCustomerCapacityRelations(List<Long> customerIds, List<Long> carrierIds, List<Long> driverIds, List<Long> vehicleIds, List<Long> payeeIds) {
		// 1、组装数据，将id互相关联，方便后续查询
		List<CustomerActualRelation> customerActualRelations = new ArrayList<>();
		List<CustomerDriverRelation> customerDriverRelations = new ArrayList<>();
		List<CustomerVehicleRelation> customerVehicleRelations = new ArrayList<>();
		List<CustomerPayeeRelation> customerPayeeRelations = new ArrayList<>();
		for (Long customerId : customerIds) {
			List<CustomerActualRelation> finalCustomerActualRelations = customerActualRelations;
			carrierIds.forEach(carrierId -> {
				CustomerActualRelation customerActualRelation = new CustomerActualRelation();
				customerActualRelation.setCustomerId(customerId);
				customerActualRelation.setActualId(carrierId);
				finalCustomerActualRelations.add(customerActualRelation);
			});
			List<CustomerDriverRelation> finalCustomerDriverRelations = customerDriverRelations;
			driverIds.forEach(driverId -> {
				CustomerDriverRelation customerDriverRelation = new CustomerDriverRelation();
				customerDriverRelation.setCustomerId(customerId);
				customerDriverRelation.setDriverId(driverId);
				finalCustomerDriverRelations.add(customerDriverRelation);
			});
			List<CustomerVehicleRelation> finalCustomerVehicleRelations = customerVehicleRelations;
			vehicleIds.forEach(vehicleId -> {
				CustomerVehicleRelation customerVehicleRelation = new CustomerVehicleRelation();
				customerVehicleRelation.setCustomerId(customerId);
				customerVehicleRelation.setVehicleId(vehicleId);
				finalCustomerVehicleRelations.add(customerVehicleRelation);
			});
			List<CustomerPayeeRelation> finalCustomerPayeeRelations = customerPayeeRelations;
			payeeIds.forEach(payeeId -> {
				CustomerPayeeRelation customerPayeeRelation = new CustomerPayeeRelation();
				customerPayeeRelation.setCustomerId(customerId);
				customerPayeeRelation.setPayeeId(payeeId);
				finalCustomerPayeeRelations.add(customerPayeeRelation);
			});
		}
		// 2、查询后插入
		customerActualRelations = originalService.getCustomerActualRelations(customerActualRelations);
		customerActualRelationMapperEx.insertRelations(customerActualRelations);

		customerDriverRelations = originalService.getCustomerDriverRelations(customerDriverRelations);
		customerDriverRelationMapper.insertCustomerDriverRelations(customerDriverRelations);

		customerVehicleRelations = originalService.getCustomerVehicleRelations(customerVehicleRelations);
		customerVehicleRelationMapper.insertCustomerVehicleRelations(customerVehicleRelations);

		customerPayeeRelations = originalService.getCustomerPayeeRelations(customerPayeeRelations);
		customerPayeeRelationMapper.insertCustomerPayeeRelations(customerPayeeRelations);

		return CommonResult.success();
	}

	/**
	 * 插入网货、运力关联信息
	 *
	 * @param forwarderId
	 * @param carrierIds
	 * @param driverIds
	 * @param vehicleIds
	 * @param payeeIds
	 * @return
	 */
	private CommonResult dealForwarderCapacityRelations(Long forwarderId, List<Long> carrierIds, List<Long> driverIds, List<Long> vehicleIds, List<Long> payeeIds) {
		// 1、网货承运人关联关系
		List<FreightForwarderActualRelation> forwarderActualRelations = originalService.getForwarderActualRelations(forwarderId, carrierIds);
		forwarderActualRelationMapper.insertFreightForwarderActualRelationList(forwarderActualRelations);

		// 2、网货司机关联关系
		List<FreightForwarderDriverRelation> forwarderDriverRelations = originalService.getForwarderDriverRelations(forwarderId, driverIds);
		forwarderDriverRelationMapper.insertFreightForwarderDriverRelationList(forwarderDriverRelations);

		// 3、网货车辆关联关系
		List<FreightForwarderVehicleRelation> forwarderVehicleRelations = originalService.getForwarderVehicleRelations(forwarderId, vehicleIds);
		forwarderVehicleRelationMapper.insertFreightForwarderVehicleRelationList(forwarderVehicleRelations);

		return CommonResult.success();
	}

	/**
	 * 插入运力间关联关系
	 *
	 * @param actualDrivers
	 * @param actualVehicles
	 * @return
	 */
	private CommonResult dealCapacityRelations(Set<String> actualDrivers, Set<String> actualVehicles) {
		// 1、组装数据，将id互相关联，方便后续查询
		List<ActualDriverRelation> actualDriverRelations = new ArrayList<>();
		List<ActualVehicleRelation> actualVehicleRelations = new ArrayList<>();
		for (String actualDriver : actualDrivers) {
			ActualDriverRelation actualDriverRelation = new ActualDriverRelation();
			actualDriverRelation.setActualId(Long.valueOf(actualDriver.split("_")[0]));
			actualDriverRelation.setDriverId(Long.valueOf(actualDriver.split("_")[1]));
			actualDriverRelations.add(actualDriverRelation);
		}
		for (String actualVehicle : actualVehicles) {
			ActualVehicleRelation actualVehicleRelation = new ActualVehicleRelation();
			actualVehicleRelation.setActualId(Long.valueOf(actualVehicle.split("_")[0]));
			actualVehicleRelation.setVehicleId(Long.valueOf(actualVehicle.split("_")[1]));
			actualVehicleRelations.add(actualVehicleRelation);
		}

		// 1、人和人关系
		actualDriverRelations = originalService.getActualDriverRelations(actualDriverRelations);
		if (CommonUtil.isNotNullOrEmpty(actualDriverRelations)) {
			actualDriverRelationMapperEx.insertCarrierDriverRelations(actualDriverRelations);
			List<Long> actualDriverRelationIds = actualDriverRelations.stream().map(r -> r.getId()).distinct().collect(Collectors.toList());
			List<ActualCarrierDriverRelationAttachmentInfo> carrierDriverRelationAttachmentInfos = originalService.getCarrierDriverRelationAttachmentInfos(actualDriverRelationIds);
			if (CommonUtil.isNotNullOrEmpty(carrierDriverRelationAttachmentInfos)) {
				carrierDriverRelationAttachmentInfoMapper.insertList(carrierDriverRelationAttachmentInfos);
			}
		}

		// 2、人、车关系
		actualVehicleRelations = originalService.getActualVehicleRelations(actualVehicleRelations);
		if (CommonUtil.isNotNullOrEmpty(actualVehicleRelations)) {
			actualVehicleRelationMapperEx.insertCarrierVehicleRelations(actualVehicleRelations);
			List<Long> actualVehicleRelationIds = actualVehicleRelations.stream().map(r -> r.getId()).distinct().collect(Collectors.toList());
			List<ActualCarrierVehicleRelationAttachmentInfo> carrierVehicleRelationAttachmentInfos = originalService.getCarrierVehicleRelationAttachmentInfos(actualVehicleRelationIds);
			if (CommonUtil.isNotNullOrEmpty(carrierVehicleRelationAttachmentInfos)) {
				carrierVehicleRelationAttachmentInfoMapper.insertList(carrierVehicleRelationAttachmentInfos);
			}
		}

		return CommonResult.success();
	}

	/**
	 * 插入运单信息
	 *
	 * @param waybillIds
	 * @param waybills
	 * @param waybillCustomerRelations
	 */
	private CommonResult dealWaybills(List<Long> waybillIds, List<Waybill> waybills, List<WaybillCustomerRelation> waybillCustomerRelations) {
		List<Long> makecodeIds = waybills.stream().map(w -> w.getMakeCodeId()).distinct().collect(Collectors.toList());
		List<Long> consigneeIds = waybills.stream().map(w -> w.getConsigneeId()).distinct().collect(Collectors.toList());
		List<Long> consignorIds = waybills.stream().map(w -> w.getConsignorId()).distinct().collect(Collectors.toList());
		List<List<Long>> waybillIdList = ListUtils.splitList(waybillIds, 1000);
		// 1、运单基础表
		List<List<Waybill>> waybillLists = ListUtils.splitList(waybills, 1000);
		for (List<Waybill> waybillSplits : waybillLists) {
			ThreadPoolUtil.getPool().execute(() -> {
				waybillMapper.insertWaybills(waybillSplits);
			});
		}
		List<List<WaybillCustomerRelation>> relationLists = ListUtils.splitList(waybillCustomerRelations, 1000);
		for (List<WaybillCustomerRelation> relationSplits : relationLists) {
			ThreadPoolUtil.getPool().execute(() -> {
				waybillCustomerRelationMapper.insertWaybillCustomerRelations(relationSplits);
			});
		}
		// 2、分批插入运单相关表
		for (List<Long> waybillSplitIds : waybillIdList) {
			ThreadPoolUtil.getPool().execute(() -> {
				// 2、运单转包链表
				List<WaybillContractChain> waybillContractChains = originalService.getWaybillContractChains(waybillSplitIds);
				waybillContractChainMapper.insertWaybillContractChainList(waybillContractChains);
				List<WaybillSlave> waybillSlaves = originalService.getWaybillSlaves(waybillSplitIds);
				waybillSlaveMapper.insertWaybillSlaves(waybillSlaves);
				// 5、运单附件表
				List<WaybillAttachmentInfo> waybillAttachmentInfos = originalService.getWaybillAttachmentInfos(waybillSplitIds);
				waybillAttachmentInfoMapper.insertWaybillAttachmentInfos(waybillAttachmentInfos);
				// 6、运单货物
				List<WaybillGoods> waybillGoods = originalService.getWaybillGoods(waybillSplitIds);
				waybillGoodsMapper.insertWaybillGoodsList(waybillGoods);
				// 7、运单额外费用
				List<WaybillFare> waybillFares = originalService.getWaybillFares(waybillSplitIds);
				if (CommonUtil.isNotNullOrEmpty(waybillFares)) {
					waybillFareMapper.insertWaybillFares(waybillFares);
				}
				// 8、运单小程序轨迹
				List<WaybillMiniprogramTrack> waybillMiniprogramTracks = originalService.getWaybillMiniprogramTracks(waybillSplitIds);
				if (CommonUtil.isNotNullOrEmpty(waybillMiniprogramTracks)) {
					waybillMiniprogramTrackMapper.insertWaybillMiniprogramTracks(waybillMiniprogramTracks);
				}
				// 9、运单投保记录 waybill_policy_record
				List<WaybillPolicyRecord> waybillPolicyRecords = originalService.getWaybillPolicyRecords(waybillSplitIds);
				if (CommonUtil.isNotNullOrEmpty(waybillPolicyRecords)) {
					waybillPolicyRecordMapper.insertWaybillPolicyRecords(waybillPolicyRecords);
				}
				// 10、运单支付表
				List<CustomerTradeApply> customerTradeApplies = originalService.getCustomerTradeApplys(waybillSplitIds);
				customerTradeApplyMapper.insertCustomerTradeApplys(customerTradeApplies);
			});
		}

		// 11、货源表
		List<MakeCode> makeCodes = originalService.getMakeCodes(makecodeIds);
		makeCodeMapper.insertMakeCodes(makeCodes);
		// 12、货源规则表
		List<Long> ruleIds = makeCodes.stream().map(m -> m.getRuleId()).distinct().filter(r -> !r.equals(0l)).collect(Collectors.toList());
		if (CommonUtil.isNotNullOrEmpty(ruleIds)) {
			List<MakeCodeRule> makeCodeRules = originalService.getMakeCodeRules(ruleIds);
			makeCodeRuleMapper.insertMakeCodeRules(makeCodeRules);
		}
		// 13、收货地址表
		List<ConsigneeInfo> consigneeInfos = originalService.getConsigneeInfos(consigneeIds);
		consigneeInfoMapper.insertConsigneeInfos(consigneeInfos);
		// 14、发货地址表
		List<ConsignorInfo> consignorInfos = originalService.getConsignorInfos(consignorIds);
		consignorInfoMapper.insertConsignorInfos(consignorInfos);

		return CommonResult.success();
	}


	/**
	 * 插入开票相关信息
	 */
	private CommonResult dealStatements(List<Long> waybillIds) {
		// 1、开票申请的运单清单表
		List<FinanceStatementWaybill> financeStatementWaybills = originalService.getFinanceStatementWaybills(waybillIds);
		if (CommonUtil.isNotNullOrEmpty(financeStatementWaybills)) {
			financeStatementWaybillMapper.insertFinanceStatementWaybills(financeStatementWaybills);
		} else {
			return CommonResult.success();
		}
		// 得到申请单id合集
		List<Long> statementIds = financeStatementWaybills.stream().map(w -> w.getStatementId()).distinct().collect(Collectors.toList());
		// 2、申请开票表
		List<FinanceStatement> financeStatements = originalService.getFinanceStatements(statementIds);
		if (CommonUtil.isNotNullOrEmpty(financeStatements)) {
			financeStatementMapper.insertFinanceStatements(financeStatements);
		}

		// 3、钉钉申请表
		List<FinanceDdInfo> financeDdInfos = originalService.getFinanceDdInfos(statementIds);
		if (CommonUtil.isNotNullOrEmpty(financeDdInfos)) {
			financeDdInfoMapper.insertFinanceDdInfos(financeDdInfos);
		}

		// 4、开票-项目关联表
		List<FinanceStatementContractRelation> relations = originalService.getFinanceStatementContractRelations(statementIds);
		if (CommonUtil.isNotNullOrEmpty(relations)) {
			financeStatementContractRelationMapper.insertFinanceStatementContractRelations(relations);
		}

		// 5、发票表
		List<FinanceStatementBill> financeStatementBills = originalService.getFinanceStatementBills(statementIds);
		if (CommonUtil.isNotNullOrEmpty(financeStatementBills)) {
			financeStatementBillMapper.insertFinanceStatementBills(financeStatementBills);
		}

		// 6、发票附件表
		if (CommonUtil.isNotNullOrEmpty(financeStatementBills)) {
			List<Long> billIds = financeStatementBills.stream().map(bill -> bill.getId()).collect(Collectors.toList());
			List<SysAttachmentInfo> sysAttachmentInfos = originalService.getSysAttachmentInfos(billIds);
			sysAttachmentInfoMapper.deleteSysAttachmentInfoByRelationIds(billIds);
			sysAttachmentInfoMapper.insertSysAttachmentInfos(sysAttachmentInfos);
		}

		return CommonResult.success();
	}

	public Map<String, Object> shanDaoBigscreen() {
		// String statisticsKe = "shandaoWaybill:statistics";
		Map<String, Object> statistics = new HashMap<>();
		// if (redisCache.hasKey(statisticsKe)) {
		// statistics = redisCache.getCacheObject(statisticsKe);
		// return statistics;
		// }
		String creditCode = "91320830MADCRXEJ3Q";
		Map<String, Object> totalStats = transferMapper.getTotalStatistics(creditCode);
		statistics.putAll(totalStats);
		String goodsCounts = transferMapper.getGoodsCounts(creditCode);
		JsonArray jsonArray = JsonParser.parseString(goodsCounts).getAsJsonArray();
		Iterator<JsonElement> iterator = jsonArray.iterator();
		Iterable<JsonElement> iterable = () -> iterator;
		int topGoodsTotal = StreamSupport.stream(iterable.spliterator(), false).mapToInt(element -> element.getAsJsonObject().get("waybillCount").getAsInt()).sum();
		Integer totalWaybills = ((Long) totalStats.get("totalWaybills")).intValue();
		int otherGoodsCount = totalWaybills - topGoodsTotal;
		if (otherGoodsCount > 0) {
			JsonObject otherGoods = new JsonObject();
			otherGoods.addProperty("descriptionOfGoods", "其他种类");
			otherGoods.addProperty("waybillCount", otherGoodsCount);
			jsonArray.add(otherGoods);
		}
		String updatedTopGoodsJson = jsonArray.toString();
		statistics.put("goodsPercentage", updatedTopGoodsJson);
		statistics.put("haulwayStats", transferMapper.getHaulwayWaybills(creditCode));
		statistics.put("recentDailyCounts", transferMapper.getRecentDailyCounts(creditCode));
		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoMapper.selectFreightForwarderInfoByName("江苏善道智运科技有限公司");
		FreightForwarderCustomerRelation customerInfo = new FreightForwarderCustomerRelation();
		customerInfo.setFreightForwarderId(freightForwarderInfo.getId());
		int size = freightForwarderCustomerRelationMapper.selectFreightForwarderCustomerRelationList(customerInfo).size();
		statistics.put("customerCounts", size);
		FreightForwarderVehicleRelation vehicleSearch = new FreightForwarderVehicleRelation();
		vehicleSearch.setFreightForwarderId(freightForwarderInfo.getId());
		int size1 = freightForwarderVehicleRelationMapper.count(vehicleSearch);
		statistics.put("vehicleCounts", size1);
		FreightForwarderDriverRelation driverSearch = new FreightForwarderDriverRelation();
		driverSearch.setFreightForwarderId(freightForwarderInfo.getId());
		int size2 = freightForwarderDriverRelationMapper.count(driverSearch);
		statistics.put("carrierCounts", size2);
		// redisCache.setCacheObject(statisticsKe, statistics, 60, TimeUnit.MINUTES);
		return statistics;
	}

}
