package com.zly.project.settlement.controller;

import cn.hutool.core.bean.BeanUtil;
import com.zly.common.utils.PageUtils;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.poi.ExcelUtil;
import com.zly.framework.aspectj.lang.annotation.Log;
import com.zly.framework.aspectj.lang.enums.BusinessType;
import com.zly.framework.redis.RedisCache;
import com.zly.framework.security.service.SysLoginService;
import com.zly.framework.web.controller.BaseController;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.project.carrier.payee.domain.PayBank;
import com.zly.project.carrier.payee.service.IPayBankService;
import com.zly.project.consignor.customer.domain.CustomerInfo;
import com.zly.project.consignor.customer.service.ICustomerInfoService;
import com.zly.project.contract.domain.FrameworkContract;
import com.zly.project.contract.service.IFrameworkContractService;
import com.zly.project.settlement.domain.CustomerAccount;
import com.zly.project.settlement.domain.request.CorporateReq;
import com.zly.project.settlement.domain.request.CustomerAccountReq;
import com.zly.project.settlement.domain.request.ali.FixcardCreateReq;
import com.zly.project.settlement.domain.response.CustomerAccountRes;
import com.zly.project.settlement.service.ICustomerAccountService;
import com.zly.project.settlement.service.ali.AliBankService;
import com.zly.project.settlement.service.ali.ShipAliBankService;
import com.zly.project.settlement.service.huaxia.HuaXiaService;
import com.zly.project.settlement.service.huaxia.ShipHuaXiaService;
import com.zly.project.tenant.domain.TenantUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 客户账户Controller
 *
 * <AUTHOR>
 * @date 2021-10-22
 */
@Api(tags = "客户账户")
@Slf4j
@RestController
@RequestMapping("/customer/account")
public class CustomerAccountController extends BaseController {

	@Autowired
	private ICustomerAccountService customerAccountService;

	@Autowired
	private AliBankService aliBankService;

	@Autowired
	private HuaXiaService huaXiaService;

	@Autowired
	private ICustomerInfoService customerInfoService;

	@Autowired
	private IFrameworkContractService frameworkContractService;

	@Resource
	private SysLoginService sysLoginService;

	@Resource
	private RedisCache redisCache;

	@Value("${platform.payment.channel}")
	private String paymentChannel;// 平台支付通道

	@Autowired
	private ShipAliBankService shipAliBankService;
	@Autowired
	private ShipHuaXiaService shipHuaXiaService;

	@Resource
	private IPayBankService payBankService;

	/**
	 * 客户开户
	 */
	@ApiOperation(value = "客户开户", notes = "客户开户")
	@PreAuthorize("@ss.hasPermi('customer:account:open')")
	@PostMapping("/open")
	public AjaxResult open(@RequestBody FixcardCreateReq req) throws Exception {
		return customerAccountService.openCorporateAccount(req);
	}

	/**
	 * 查询客户账户列表
	 */
	@ApiOperation(value = "查询客户账户列表", notes = "查询客户账户列表")
	@PreAuthorize("@ss.hasPermi('customer:account:list')")
	@PostMapping("/list")
	public TableDataInfo list(@RequestBody CustomerAccountReq customerAccount) {
		List<CustomerAccountRes> list;
		// 托运人账户
		if ("tenant".equals(customerAccount.getAccountType())) {
			list = customerAccountService.selectTenantAccountListEx(customerAccount);
		} else if ("carrier".equals(customerAccount.getAccountType())) {
			// 承运人账户
			list = customerAccountService.selectCarrierAccountListEx(customerAccount);
		} else {
			// 旧的未区分身份
			list = BeanUtil.copyToList(customerAccountService.selectCustomerAccountListEx(customerAccount), CustomerAccountRes.class);
		}
		return getDataTable(list);
	}

	@ApiOperation(value = "交易易账户管理-所属银行下拉框列表返回接口---v4.6.3", notes = "交易易账户管理-所属银行下拉框列表返回接口")
	@PostMapping("/bank")
	public TableDataInfo selectBank(@RequestBody PayBank payBank) {
		PageUtils.startPage();
		List<PayBank> payBanks = payBankService.selectPayBankList(payBank);
		return getDataTable(payBanks);
	}

	@ApiOperation(value = "添加对公账户---v4.6.3", notes = "添加对公账户")
	@PostMapping("/corporate/add")
	public AjaxResult addCorporateAccount(@RequestBody CorporateReq corporateReq) {
		return customerAccountService.addCorporateAccount(corporateReq);
	}

	@ApiOperation(value = "删除对公账户---v4.6.3", notes = "删除对公账户")
	@PostMapping("/corporate/del")
	public AjaxResult delCorporateAccount(@RequestBody CorporateReq corporateReq) {
		return customerAccountService.delCorporateAccount(corporateReq);
	}

	/**
	 * 导出客户账户列表
	 */
	@ApiOperation(value = "导出客户账户列表", notes = "导出客户账户列表")
	@PreAuthorize("@ss.hasPermi('customer:account:export')")
	@Log(title = "客户账户", businessType = BusinessType.EXPORT)
	@GetMapping("/export")
	public AjaxResult export(CustomerAccount customerAccount) {
		List<CustomerAccount> list = customerAccountService.selectCustomerAccountList(customerAccount);
		ExcelUtil<CustomerAccount> util = new ExcelUtil<CustomerAccount>(CustomerAccount.class);
		return util.exportExcel(list, "客户账户数据");
	}

	/**
	 * 获取客户账户详细信息
	 */
	@ApiOperation(value = "获取客户账户详细信息", notes = "获取客户账户详细信息")
	@PreAuthorize("@ss.hasPermi('customer:account:getInfo')")
	@PostMapping(value = "/getInfo")
	public AjaxResult getInfo() {
		TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		if (tenantUser == null) {
			return AjaxResult.error("请重新登录！");
		}
		return customerAccountService.selectCustomerAccountByCustomerId(tenantUser.getTenantId());
	}

	/**
	 * 新增客户账户
	 */
	@ApiOperation(value = "新增客户账户", notes = "新增客户账户")
	@PreAuthorize("@ss.hasPermi('customer:account:add')")
	@Log(title = "客户账户", businessType = BusinessType.INSERT)
	@PostMapping
	public AjaxResult add(@RequestBody CustomerAccount customerAccount) {
		return toAjax(customerAccountService.insertCustomerAccount(customerAccount));
	}

	/**
	 * 发送修改资金密码的验证密码
	 */
	@ApiOperation(value = "发送修改资金密码的验证密码", notes = "发送修改资金密码的验证密码")
	@PreAuthorize("@ss.hasPermi('customer:account:sendMsg')")
	@Log(title = "客户账户", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/sendMsg")
	public AjaxResult sendMsg(@RequestBody CustomerAccount customerAccount) {
		TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		if (null == tenantUser) {
			return AjaxResult.error("请重新登录");
		}
		String key = "sendMsg_" + SecurityUtils.getShipperId();
		String lockKey = "sendMsg_lock_" + SecurityUtils.getShipperId();
		if (redisCache.hasKey(lockKey)) {
			return AjaxResult.error("图形验证码错误次数太多，请10分钟以后再试");
		}
		AjaxResult result = sysLoginService.validateCaptcha(customerAccount.getPicCode(), customerAccount.getUuid(), 1);
		if (!result.isSuccess()) {
			if (redisCache.hasKey(key)) {
				Integer count = redisCache.getCacheObject(key);
				redisCache.setCacheObject(key, count + 1);
				if (count >= 4) {
					redisCache.setCacheObject(lockKey, 1, 10, TimeUnit.MINUTES);
				}
			} else {
				redisCache.setCacheObject(key, 1);
			}
			return result;
		}
		redisCache.deleteObject(key);
		return customerAccountService.sendMsg(customerAccount, tenantUser.getTenantId());
	}

	/**
	 * 修改客户账户
	 */
	@ApiOperation(value = "修改客户账户", notes = "修改客户账户")
	@PreAuthorize("@ss.hasPermi('customer:account:updatepd')")
	@Log(title = "客户账户", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/updatePd")
	public AjaxResult updatePassword(@RequestBody CustomerAccount customerAccount) {
		TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		if (null == tenantUser) {
			return AjaxResult.error("请重新登录");
		}
		String key = "updatePd_" + SecurityUtils.getShipperId();
		String lockKey = "updatePd_lock_" + SecurityUtils.getShipperId();
		if (redisCache.hasKey(lockKey)) {
			return AjaxResult.error("图形验证码错误次数太多，请10分钟以后再试");
		}
		AjaxResult codeResult = sysLoginService.validateCaptcha(customerAccount.getPicCode(), customerAccount.getUuid(), 2);
		if (!codeResult.isSuccess()) {
			if (redisCache.hasKey(key)) {
				Integer count = redisCache.getCacheObject(key);
				redisCache.setCacheObject(key, count + 1);
				if (count >= 4) {
					redisCache.setCacheObject(lockKey, 1, 10, TimeUnit.MINUTES);
				}
			} else {
				redisCache.setCacheObject(key, 1);
			}
			return codeResult;
		}
		redisCache.deleteObject(key);
		AjaxResult result = customerAccountService.updatePassword(customerAccount, tenantUser.getTenantId());
		return result;
	}

	/**
	 * 删除客户账户
	 */
	@ApiOperation(value = "删除客户账户", notes = "删除客户账户")
	@PreAuthorize("@ss.hasPermi('customer:account:remove')")
	@Log(title = "客户账户", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult remove(@PathVariable Long[] ids) {
		return toAjax(customerAccountService.deleteCustomerAccountByIds(ids));
	}

	/**
	 * 判断该项目是否已设置密码
	 */
	@ApiOperation(value = "判断该项目是否已设置密码", notes = "判断该项目是否已设置密码")
	@PreAuthorize("@ss.hasPermi('customer:account:verifyPasswordSet')")
	@GetMapping(value = "/verifyPasswordSet/{frameworkContractId}")
	public AjaxResult verifyPasswordSet(@PathVariable String frameworkContractId) {
		TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		if (tenantUser == null) {
			return AjaxResult.error("请重新登录！");
		}
		//有多笔运单同时付款
		List<String> list = Arrays.stream(frameworkContractId.split(",")).distinct().collect(Collectors.toList());
		if (list.size() > 1) {
			return AjaxResult.error("请选择同一项目的运单付款");
		}
		CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(tenantUser.getTenantId());
		if (null == customerInfo) {
			return AjaxResult.error("没有相关的企业信息");
		}
		CustomerAccount customerAccount = customerAccountService.selectCustomerAccountById(tenantUser.getTenantId());
		if (null == customerAccount) {
			return AjaxResult.error("未查询到当前客户账户信息！");
		}

		FrameworkContract frameworkContract = frameworkContractService.selectFrameworkContractById(Long.valueOf(list.get(0)));
		if (null == frameworkContract) {
			return AjaxResult.error("没有相关的项目信息");
		}
		return huaXiaService.isProjectPasswordSet(customerAccount.getIdNumber(), frameworkContract);
	}
}
