package com.zly.project.settlement.service.impl;

import com.alibaba.fastjson2.JSON;
import com.zly.common.constant.BusinessConstants;
import com.zly.common.constant.MQConstants;
import com.zly.common.utils.DateUtils;
import com.zly.common.utils.SecurityUtils;
import com.zly.common.utils.TextUtil;
import com.zly.framework.redis.RedisCache;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.framework.web.page.TableInfo;
import com.zly.project.agentOperation.domain.req.AgentFlowReq;
import com.zly.project.carrier.payee.domain.PayBank;
import com.zly.project.carrier.payee.domain.PayeeInfo;
import com.zly.project.carrier.payee.mapper.PayBankMapper;
import com.zly.project.consignor.customer.domain.CustomerInfo;
import com.zly.project.consignor.customer.mapper.CustomerInfoMapper;
import com.zly.project.freightforwarder.domain.FreightForwarderInfo;
import com.zly.project.freightforwarder.service.IFreightForwarderInfoService;
import com.zly.project.settlement.domain.CustomerAccount;
import com.zly.project.settlement.domain.CustomerBankCard;
import com.zly.project.settlement.domain.request.CorporateReq;
import com.zly.project.settlement.domain.request.CustomerAccountReq;
import com.zly.project.settlement.domain.request.ali.FixcardCreateReq;
import com.zly.project.settlement.domain.response.CustomerAccountRes;
import com.zly.project.settlement.domain.response.CustomerBankFlowRollOutRes;
import com.zly.project.settlement.domain.response.huaxia.UserAccount;
import com.zly.project.settlement.domain.response.huaxia.UserBankFlow;
import com.zly.project.settlement.mapper.CustomerAccountMapper;
import com.zly.project.settlement.mapper.CustomerAccountMapperEx;
import com.zly.project.settlement.mapper.CustomerBankCardMapper;
import com.zly.project.settlement.mapstruct.UserBankFlowToCustomerBankFlowRollOutResMapStruct;
import com.zly.project.settlement.service.ICustomerAccountFlowService;
import com.zly.project.settlement.service.ICustomerAccountService;
import com.zly.project.settlement.service.ali.AliBankService;
import com.zly.project.settlement.service.ali.ShipAliBankService;
import com.zly.project.settlement.service.huaxia.HuaXiaService;
import com.zly.project.settlement.service.huaxia.ShipHuaXiaService;
import com.zly.project.system.domain.SysUser;
import com.zly.project.system.domain.req.NetWithdrawalReq;
import com.zly.project.system.domain.req.SysUpdatePasswordReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 客户账户Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-10-22
 */
@Service
@Slf4j
public class CustomerAccountServiceImpl implements ICustomerAccountService {
	@Autowired
	private CustomerAccountMapper customerAccountMapper;
	@Autowired
	private ICustomerAccountFlowService iCustomerAccountFlowService;
	@Autowired
	private CustomerAccountMapperEx customerAccountMapperEx;
	@Autowired
	private CustomerInfoMapper customerInfoMapper;
	@Autowired
	private CheckTheParametersService checkTheParametersService;
	@Autowired
	private HuaXiaService huaXiaService;
	@Autowired
	private RedisCache redisCache;
	@Autowired
	private IFreightForwarderInfoService freightForwarderInfoService;
	@Autowired
	private UserBankFlowToCustomerBankFlowRollOutResMapStruct userBankFlowToCustomerBankFlowRollOutResMapStruct;
	@Resource
	private PayBankMapper payBankMapper;
	@Resource
	private CustomerBankCardMapper customerBankCardMapper;

	@Value("${platform.payment.channel}")
	private String paymentChannel;// 平台支付通道

	@Autowired
	private AliBankService aliBankService;
	@Autowired
	private ShipAliBankService shipAliBankService;
	@Autowired
	private ShipHuaXiaService shipHuaXiaService;

	/**
	 * 查询客户账户
	 *
	 * @param id
	 * 		客户账户主键
	 * @return 客户账户
	 */
	@Override
	public CustomerAccount selectCustomerAccountById(Long id) {
		return customerAccountMapper.selectCustomerAccountById(id);
	}

	/**
	 * 查询客户账户列表
	 *
	 * @param customerAccount
	 * 		客户账户
	 * @return 客户账户
	 */
	@Override
	public List<CustomerAccount> selectCustomerAccountList(CustomerAccount customerAccount) {
		return customerAccountMapper.selectCustomerAccountList(customerAccount);
	}

	/**
	 * 新增客户账户
	 *
	 * @param customerAccount
	 * 		客户账户
	 * @return 结果
	 */
	@Override
	public int insertCustomerAccount(CustomerAccount customerAccount) {
		customerAccount.setCreateTime(DateUtils.getNowDate());
		customerAccount.setUpdateTime(customerAccount.getCreateTime());
		return customerAccountMapper.insertCustomerAccount(customerAccount);
	}

	/**
	 * 修改客户账户
	 *
	 * @param customerAccount
	 * 		客户账户
	 * @return 结果
	 */
	@Override
	public int updateCustomerAccount(CustomerAccount customerAccount) {
		customerAccount.setUpdateTime(DateUtils.getNowDate());
		int result = customerAccountMapper.updateCustomerAccount(customerAccount);
		if (result <= 0) {
			throw new RuntimeException("更新客户账户信息失败");
		}
		return result;
	}

	/**
	 * 批量删除客户账户
	 *
	 * @param ids
	 * 		需要删除的客户账户主键
	 * @return 结果
	 */
	@Override
	public int deleteCustomerAccountByIds(Long[] ids) {
		return customerAccountMapper.deleteCustomerAccountByIds(ids);
	}

	/**
	 * 删除客户账户信息
	 *
	 * @param id
	 * 		客户账户主键
	 * @return 结果
	 */
	@Override
	public int deleteCustomerAccountById(Long id) {
		return customerAccountMapper.deleteCustomerAccountById(id);
	}

	/**
	 * 更新账户余额，并记录账户余额变动流水，账户流水默认为成功。
	 *
	 * @param id
	 * @param money
	 * @param isPay
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public CustomerAccount updateAccountMoney(Long id, Long customerId, BigDecimal money, Integer isPay, Boolean isContract, String waybillCode, String payCode, Integer tranType, String tradeName,
			String tradeNo, String remark) {
		for (int i = 0; i < 10; i++) {
			CustomerAccount account = this.selectCustomerAccountById(id);
			if (null == account) {
				throw new RuntimeException("支付失败：没有查询到结算账户");
			}
			// 如果是扣费合同变动。肯定是上划下拨或者运费支付。
			if (isContract) {
				// 上划 冻结的- 现金+；下拨 冻结+ 先进-；运费支付，冻结-，总金额-；
				account.setContractMoney(account.getContractMoney().add(money.multiply(BigDecimal.valueOf(isPay))));
				// 运费支付的话，需要扣除总余额
				if (BusinessConstants.TRADE_TYPE_PAY.equals(tranType)) {
					account.setSumMoney(account.getSumMoney().add(money.multiply(BigDecimal.valueOf(isPay))));
				} else {
					account.setMoney(account.getMoney().subtract(money.multiply(BigDecimal.valueOf(isPay))));
				}
				// 判断余额
				if (account.getContractMoney().compareTo(BigDecimal.ZERO) < 0 || account.getMoney().compareTo(BigDecimal.ZERO) < 0) {
					throw new RuntimeException("支付失败：" + account.getCustomerName() + "，资金不足");
				}
			} else {
				// 充值 现金+ 总金额+，转出 现金- 总金额-。
				account.setMoney(account.getMoney().add(money.multiply(BigDecimal.valueOf(isPay))));
				account.setSumMoney(account.getSumMoney().add(money.multiply(BigDecimal.valueOf(isPay))));
				// 判断余额
				if (account.getContractMoney().compareTo(BigDecimal.ZERO) < 0 || account.getMoney().compareTo(BigDecimal.ZERO) < 0) {
					throw new RuntimeException("支付失败：" + account.getCustomerName() + "，资金不足");

				}
			}
			int result = this.updateCustomerAccountByUpdateTime(account);
			if (result == 1) {
				// 添加总账户扣钱的流水
				iCustomerAccountFlowService.insertAccountFlowByAny(account, customerId, waybillCode, payCode, tranType, isPay, money, tradeName, tradeNo, remark);
				return account;
			}
		}
		throw new RuntimeException("支付失败：更新余额失败");
	}

	/**
	 * 根据updatetime 和ID更新账户（乐观锁）
	 *
	 * @param account
	 * @return
	 */
	@Override
	public int updateCustomerAccountByUpdateTime(CustomerAccount account) {
		if (null == account || null == account.getId() || null == account.getUpdateTime()) {
			return 0;
		}
		return customerAccountMapperEx.updateCustomerAccountByUpdateTime(account);
	}

	/**
	 * 查询客户账户列表
	 *
	 * @param customerAccount
	 * 		客户账户
	 * @return 客户账户集合
	 */
	@Override
	public List<CustomerAccount> selectCustomerAccountListEx(CustomerAccountReq customerAccount) {
		return customerAccountMapperEx.selectCustomerAccountListEx(customerAccount);
	}

	/**
	 * 给货主开户，直接把客户ID当成账号ID。也是pds的摊位ID
	 *
	 * @param customerInfo
	 * @param bankCardNo
	 * @param branchNo
	 * @param branchName
	 */
	@Override
	public void initCustomerInfoAccount(CustomerInfo customerInfo, String bankCardNo, String branchNo, String branchName) {
		// 更新用户表customer_info
		if (null == customerInfo || null == customerInfo.getId()) {
			throw new RuntimeException("更新客户信息失败，客户ID为空");
		}
		String nickname = SecurityUtils.getLoginUser().getNickname();
		customerInfo.setAccountNo(bankCardNo);
		customerInfo.setBankCode(branchNo);
		customerInfo.setBankName(branchName);
		customerInfo.setUpdateBy(nickname);
		customerInfo.setApproveState(4);
		customerInfoMapper.updateCustomerInfo(customerInfo);
		// 插入用户账户表customer_account
		CustomerAccount customerAccount = new CustomerAccount();
		customerAccount.setId(customerInfo.getId());
		customerAccount.setIdNumber(customerInfo.getCreditCode());
		customerAccount.setCustomerName(customerInfo.getCustomerName());
		customerAccount.setAccountNo(bankCardNo);
		customerAccount.setBankCode(branchNo);
		customerAccount.setBankName(branchName);
		customerAccount.setCreateBy(nickname);
		customerAccount.setUpdateBy(nickname);
		this.insertCustomerAccount(customerAccount);
	}

	/**
	 * 给收款人，账号ID随机取，但是是PDS的摊位no
	 *
	 * @param payeeInfo
	 * @param bankCardNo
	 * @param branchNo
	 * @param branchName
	 * @return
	 */
	@Override
	public CustomerAccount initPayeeInfoAccount(PayeeInfo payeeInfo, String bankCardNo, String branchNo, String branchName) {
		// 更新用户表customer_info
		if (null == payeeInfo || null == payeeInfo.getIdentityCard()) {
			throw new RuntimeException("新增收款人账户失败，客户证件信息为空");
		}
		// 插入用户账户表customer_account
		CustomerAccount customerAccount = new CustomerAccount();
		customerAccount.setId(TextUtil.getTimeSequenceID(5));
		customerAccount.setIdNumber(payeeInfo.getIdentityCard());
		customerAccount.setCustomerName(payeeInfo.getPayeeName());
		customerAccount.setAccountNo(bankCardNo);
		customerAccount.setBankCode(branchNo);
		customerAccount.setBankName(branchName);
		customerAccount.setCreateBy(SecurityUtils.getNickname());
		customerAccount.setUpdateBy(SecurityUtils.getNickname());
		this.insertCustomerAccount(customerAccount);
		return customerAccount;
	}

	@Override
	public AjaxResult sendMsg(CustomerAccount customerAccount, Long customerId) {
		if (StringUtils.isBlank(customerAccount.getRemark())) {
			return AjaxResult.error("请输入手机号码");
		}
		// TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();
		if (null == customerId) {
			return AjaxResult.error("客户信息为空");
		}
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(customerId);
		if (null == customerInfo) {
			return AjaxResult.error("没有相关的企业信息");
		}
		if (null == customerInfo.getContactPhone()) {
			return AjaxResult.error("没有找到企业负责人的联系电话");
		}
		if (!customerAccount.getRemark().equals(customerInfo.getContactPhone())) {
			return AjaxResult.error("输入的手机号码与登记信息不一致");
		}
		return huaXiaService.getCustomerCapitalVerifyCode(customerInfo.getCreditCode(), customerInfo.getContactPhone());
		// 2023-02-21sendMsg
		/*checkTheParametersService.getCustomerAccountByIdNumber(customerInfo.getCreditCode());
		return messageUtil.sendMessage(customerInfo.getContactPhone(), null);*/
	}

	@Override
	public AjaxResult sendFreightForwarderMsg(CustomerAccount customerAccount, Long freightForwarderInfoId) {
		if (StringUtils.isBlank(customerAccount.getRemark())) {
			return AjaxResult.error("请输入手机号码");
		}
		if (null == freightForwarderInfoId) {
			return AjaxResult.error("客户信息为空");
		}
		// 查询客户
		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(freightForwarderInfoId);
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(freightForwarderInfo.getCustomerId());
		if (null == customerInfo) {
			return AjaxResult.error("没有相关的企业信息");
		}
		if (null == customerInfo.getContactPhone()) {
			return AjaxResult.error("没有找到企业负责人的联系电话");
		}
		if (!customerAccount.getRemark().equals(customerInfo.getContactPhone())) {
			return AjaxResult.error("输入的手机号码与登记信息不一致");
		}
		return huaXiaService.getCustomerCapitalVerifyCode(customerInfo.getCreditCode(), customerInfo.getContactPhone());
	}

	@Override
	public AjaxResult updatePassword(CustomerAccount customerAccount, Long customerId) {
		if (StringUtils.isBlank(customerAccount.getRemark())) {
			return AjaxResult.error("请输入验证码");
		}
		if (StringUtils.isBlank(customerAccount.getPassword())) {
			return AjaxResult.error("请输入新的密码");
		}
		/*TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();*/
		if (null == customerId) {
			return AjaxResult.error("客户信息为空");
		}
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(customerId);
		if (null == customerInfo) {
			return AjaxResult.error("没有相关的企业信息");
		}
		if (null == customerInfo.getContactPhone()) {
			return AjaxResult.error("没有找到企业负责人的联系电话");
		}
		// 请求账户体系，发送短信
		return huaXiaService.customerCapitalPasswordChange(customerInfo.getCreditCode(), customerInfo.getContactPhone(), customerAccount.getRemark(), customerAccount.getPassword());
	}

	@Override
	public AjaxResult updateFreightForwarderPassword(SysUpdatePasswordReq sysUpdatePasswordReq, Long freightForwarderInfoId) {
		if (StringUtils.isBlank(sysUpdatePasswordReq.getCode())) {
			return AjaxResult.error("请输入验证码");
		}
		if (StringUtils.isBlank(sysUpdatePasswordReq.getPassword())) {
			return AjaxResult.error("请输入新的密码");
		}
		/*TenantUser tenantUser = SecurityUtils.getLoginUser().getTenantUser();*/
		if (null == freightForwarderInfoId) {
			return AjaxResult.error("客户信息为空");
		}
		// 查询客户
		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(freightForwarderInfoId);
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(freightForwarderInfo.getCustomerId());
		if (null == customerInfo) {
			return AjaxResult.error("没有相关的企业信息");
		}
		if (null == customerInfo.getContactPhone()) {
			return AjaxResult.error("没有找到企业负责人的联系电话");
		}
		// 请求账户体系，发送短信
		return huaXiaService.customerCapitalPasswordChange(customerInfo.getCreditCode(), customerInfo.getContactPhone(), sysUpdatePasswordReq.getCode(), sysUpdatePasswordReq.getPassword());
	}

	@Override
	public AjaxResult selectCustomerAccountByCustomerId(Long customerId) {
		if (null == customerId) {
			return AjaxResult.error("没有企业ID的信息");
		}
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(customerId);
		if (null == customerInfo) {
			return AjaxResult.error("没有相关的企业信息");
		}
		// 为什么一开始判断电话，没看懂
		/*if (null == customerInfo.getContactPhone()) {
			return AjaxResult.error("没有找到企业负责人的联系电话");
		}*/
		// CustomerAccount _account = checkTheParametersService.getCustomerAccountByIdNumber(customerInfo.getCreditCode());
		// 查询总金额
		UserAccount userAccount = huaXiaService.queryTotalAccountMoney(customerInfo.getCreditCode(), BusinessConstants.USER_TYPE_CUSTOMER);
		// 查询已分配的金额
		UserAccount unassignedUserAccount = huaXiaService.queryUnassignedAccountMoney(customerInfo.getCreditCode());
		userAccount.setMoney(unassignedUserAccount.getSumMoney());
		return AjaxResult.success(userAccount);
	}

	@Override
	public List<CustomerAccountRes> selectTenantAccountListEx(CustomerAccountReq customerAccount) {
		// 获取当前网络货运人id
		SysUser user = SecurityUtils.getLoginUser().getUser();
		if (null == user) {
			return new ArrayList<>();
		}
		Long freightForwarderId = SecurityUtils.getLoginUser().getUser().getFreightForwarderId();
		customerAccount.setFreightForwarderId(freightForwarderId);
		List<CustomerAccountRes> list = customerAccountMapperEx.selectTenantAccountListEx(customerAccount);
		// 得到数据后，查询账户体系的账户余额，先试试，不行的话，还是账户体系查询后返回。
		for (CustomerAccountRes res : list) {
			UserAccount userAccount = huaXiaService.queryTotalAccountMoney(res.getIdNumber(), BusinessConstants.USER_TYPE_CUSTOMER);
			res.setMoney(userAccount.getSumMoney());
		}
		return list;
	}

	@Override
	public List<CustomerAccountRes> selectCarrierAccountListEx(CustomerAccountReq customerAccount) {
		// 得到数据后，查询账户体系的账户余额
		customerAccount.setFreightForwarderId(SecurityUtils.getFreightForwarderId());
		List<CustomerAccountRes> list = customerAccountMapperEx.selectCarrierAccountListEx(customerAccount);
		// 得到数据后，查询账户体系的账户余额，先试试，不行的话，还是账户体系查询后返回。
		for (CustomerAccountRes res : list) {
			UserAccount userAccount = huaXiaService.queryTotalAccountMoney(res.getIdNumber(), BusinessConstants.USER_TYPE_PAYEE);
			res.setMoney(userAccount.getSumMoney());
		}
		return list;
	}

	@Override
	public CommonResult<String> freightForwarderWithdrawal(NetWithdrawalReq netWithdrawalReq, SysUser user) {
		if (user == null) {
			return CommonResult.error("登录已过期，请重新登录！");
		}
		if (user.getFreightForwarderId() == null) {
			return CommonResult.error("网络货运平台ID为空！");
		}
		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(user.getFreightForwarderId());
		if (freightForwarderInfo == null) {
			return CommonResult.error("网络货运平台信息为空！");
		}

		if (com.zly.common.utils.StringUtils.isBlank(netWithdrawalReq.getBankCardNo())) {
			return CommonResult.error("转出银行账户为空！");
		}
		if (com.zly.common.utils.StringUtils.isBlank(netWithdrawalReq.getMoney())) {
			return CommonResult.error("转出金额为空！");
		}
		if (!TextUtil.isGtZero(netWithdrawalReq.getMoney())) {
			return CommonResult.error("转出金额格式不正确！");
		}
		if (com.zly.common.utils.StringUtils.isBlank(netWithdrawalReq.getPassword())) {
			return CommonResult.error("资金密码为空！");
		}
		return huaXiaService.customerWithdraw(freightForwarderInfo.getCreditCode(), netWithdrawalReq.getBankCardNo(), netWithdrawalReq.getPassword(), netWithdrawalReq.getMoney(), user.getNickName(),
				"运输费转出");
	}

	@Override
	public TableInfo<CustomerBankFlowRollOutRes> selectCustomerWalletFlowList(AgentFlowReq agentFlowReq) {
		if (agentFlowReq.getResourceCustomerInfoId() == null) {
			return TableInfo.error("网络货运平台信息为空!");
		}
		FreightForwarderInfo freightForwarderInfo = freightForwarderInfoService.selectFreightForwarderInfoById(agentFlowReq.getResourceCustomerInfoId());
		if (freightForwarderInfo == null) {
			return TableInfo.error("网络货运平台信息为空！");
		}
		TableDataInfo tableDataInfo = huaXiaService.queryCustomerWithdrawList(freightForwarderInfo.getCreditCode(), agentFlowReq.getStartTime(), agentFlowReq.getEndTime());
		List<CustomerBankFlowRollOutRes> list = userBankFlowToCustomerBankFlowRollOutResMapStruct.customerBankFlowRollOutResListConvert((List<UserBankFlow>) tableDataInfo.getRows());
		tableDataInfo.setRows(list);
		return TableInfo.result(tableDataInfo);
	}

	@Override
	public AjaxResult addCorporateAccount(CorporateReq corporateReq) {
		if (corporateReq == null || corporateReq.getCustomerId() == null || corporateReq.getCorporateAccount() == null || corporateReq.getBankId() == null) {
			return AjaxResult.error("参数缺失");
		}
		//查询当前托运人对公账户开通情况
		List<CustomerBankCard> customerBankCards1 = customerBankCardMapper.selectCustomerBankCardListByCustomerId(corporateReq.getCustomerId());
		for (CustomerBankCard customerBankCard : customerBankCards1) {
			//如果存在对公账户状态为审核中，则直接返回提示。
			if (customerBankCard.getState() == 1) {
				return AjaxResult.error("存在待授权的银行账户，请联系客服人员授权后继续操作。");
			}
		}
		//获取托运人信息
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(corporateReq.getCustomerId());
		//获取托运人托运人证件号码
		String creditCode = customerInfo.getCreditCode();
		//获取银行信息
		PayBank payBank = payBankMapper.selectPayBankById(corporateReq.getBankId());

		//组装数据
		CustomerBankCard customerBankCard = new CustomerBankCard();
		customerBankCard.setCustomerId(corporateReq.getCustomerId());
		customerBankCard.setBankCardNo(corporateReq.getCorporateAccount());
		List<CustomerBankCard> customerBankCards = customerBankCardMapper.selectCustomerBankCardList(customerBankCard);
		if (customerBankCards != null && !customerBankCards.isEmpty()) {
			return AjaxResult.error("已存在相同账户");
		}
		customerBankCard.setBankName(payBank.getBankName());
		customerBankCard.setBankCode(payBank.getBankUnionNumber());
		customerBankCard.setBankMobile(customerInfo.getContactPhone());
		customerBankCard.setIsOther(1);
		AjaxResult ajaxResult = huaXiaService.bindCustomerBankCardOperating(customerBankCard, creditCode, 1);
		if (!ajaxResult.isSuccess()) {
			return ajaxResult;
		}
		//所有开通的卡默认都为审核中，然后查询时调用钟珂接口复写开通成功
		customerBankCard.setState(1);
		customerBankCard.setCreateBy(SecurityUtils.getNickname());
		customerBankCard.setCreateTime(new Date());
		Long l = TextUtil.generateId();
		customerBankCard.setId(l);
		//成功插库
		customerBankCardMapper.insertCustomerBankCard(customerBankCard);
		return AjaxResult.success("银行卡添加成功");
	}

	@Override
	public AjaxResult delCorporateAccount(CorporateReq corporateReq) {
		if (corporateReq == null || corporateReq.getCustomerId() == null || corporateReq.getCorporateAccount() == null || corporateReq.getBankId() == null) {
			return AjaxResult.error("请求参数缺失");
		}
		CustomerBankCard customerBankCard = null;
		//查询当前托运人对公账户开通情况
		List<CustomerBankCard> customerBankCards = customerBankCardMapper.selectCustomerBankCardListByCustomerId(corporateReq.getCustomerId());
		for (CustomerBankCard x : customerBankCards) {
			//如果存在对公账户状态为审核中，则直接返回提示。
			if (x.getState() == 1) {
				return AjaxResult.error("存在待授权的银行账户，请联系客服人员授权后继续操作。");
			}
			//获取所需删除账户信息
			if (corporateReq.getCustomerId().equals(x.getCustomerId()) && corporateReq.getCorporateAccount().equals(x.getBankCardNo())) {
				customerBankCard = x;
			}
		}
		if (customerBankCard == null) {
			return AjaxResult.error("删除账户为空");
		}
		//获取托运人信息
		CustomerInfo customerInfo = customerInfoMapper.selectCustomerInfoById(corporateReq.getCustomerId());
		AjaxResult ajaxResult = huaXiaService.bindCustomerBankCardOperating(customerBankCard, customerInfo.getCreditCode(), 3);
		if (ajaxResult.isSuccess()) {
			customerBankCard.setUpdateBy(SecurityUtils.getNickname());
			customerBankCard.setUpdateTime(new Date());
			//成功更新数据库状态为删除状态
			customerBankCard.setState(-1);
			customerBankCardMapper.updateCustomerBankCard(customerBankCard);
		}
		return ajaxResult;
	}

	@Override
	public AjaxResult openCorporateAccount(FixcardCreateReq req) throws Exception {
		log.info("平台下客户开户开始-->" + JSON.toJSONString(req));
		// businessType：0或者不传 是公路开户 1水运开户 2水运公路都开户
		switch (paymentChannel) {
		case MQConstants.PAYMENT_CHANNEL_ALI:
			if (null == req.getBusinessType() || 0 == req.getBusinessType()) {
				return aliBankService.fixcardCreate(req);
			} else if (req.getBusinessType() == 1) {
				return shipAliBankService.fixcardCreate(req);
			} else {
				AjaxResult result = shipAliBankService.fixcardCreate(req);
				if (result.isSuccess()) {
					return aliBankService.fixcardCreate(req);
				} else {
					return result;
				}
			}
		case MQConstants.PAYMENT_CHANNEL_HUA_XIA:
			if (null == req.getBusinessType() || 0 == req.getBusinessType()) {
				return huaXiaService.openCustomerInfoAccount(req);
			} else if (req.getBusinessType() == 1) {
				return shipHuaXiaService.openCustomerInfoAccount(req);
			} else {
				AjaxResult result = shipHuaXiaService.openCustomerInfoAccount(req);
				if (result.isSuccess()) {
					return huaXiaService.openCustomerInfoAccount(req);
				} else {
					return result;
				}
			}
		}
		return AjaxResult.error("开户失败，非法的银行支付通道");
	}

}
