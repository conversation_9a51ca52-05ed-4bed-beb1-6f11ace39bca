package com.zly.project.settlement.service.huaxia;

import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zly.common.constant.*;
import com.zly.common.enums.ClientType;
import com.zly.common.enums.WaybillSource;
import com.zly.common.exception.ServiceException;
import com.zly.common.utils.*;
import com.zly.common.utils.spring.SpringUtils;
import com.zly.framework.config.CustomConfig;
import com.zly.framework.manager.AsyncManager;
import com.zly.framework.redis.RedisCache;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.PageDomain;
import com.zly.framework.web.page.TableDataInfo;
import com.zly.framework.web.page.TableInfo;
import com.zly.framework.web.page.TableSupport;
import com.zly.project.backgroundOperation.domain.BusinessModel;
import com.zly.project.backgroundOperation.service.BusinessModelService;
import com.zly.project.carrier.driver.service.ICarrierUserService;
import com.zly.project.carrier.driver.service.impl.CarrierUserServiceImpl;
import com.zly.project.carrier.payee.domain.PayeeInfo;
import com.zly.project.carrier.payee.domain.req.BankFlowQueryReqWithTime;
import com.zly.project.carrier.payee.mapper.PayeeInfoMapper;
import com.zly.project.carrier.payee.service.IPayeeInfoService;
import com.zly.project.common.domain.FileModel;
import com.zly.project.common.domain.req.CustomerExportRecordReq;
import com.zly.project.common.service.ExportService;
import com.zly.project.consignor.customer.domain.CustomerInfo;
import com.zly.project.consignor.customer.domain.res.CustomerAdminConfigRes;
import com.zly.project.consignor.customer.service.ICustomerInfoService;
import com.zly.project.contract.domain.FrameworkContract;
import com.zly.project.contract.service.IFrameworkContractService;
import com.zly.project.es.waybill.service.EsWaybillService;
import com.zly.project.financeOperation.service.HuaXiaFinancingService;
import com.zly.project.freightforwarder.domain.FreightForwarderInfo;
import com.zly.project.freightforwarder.service.IFreightForwarderInfoService;
import com.zly.project.group.domain.FrameworkContractSubchain;
import com.zly.project.group.domain.WaybillCustomerRelation;
import com.zly.project.group.mapper.WaybillCustomerRelationMapper;
import com.zly.project.group.service.IContractServiceInfoService;
import com.zly.project.group.service.impl.GroupFrameworkContractServiceImpl;
import com.zly.project.riskManagement.service.WaybillRiskService;
import com.zly.project.service.customerservice.domain.ContractServiceInfo;
import com.zly.project.service.servicehome.domain.SystemService;
import com.zly.project.service.servicehome.service.IHomeServiceService;
import com.zly.project.settlement.domain.CustomerAccount;
import com.zly.project.settlement.domain.CustomerBankCard;
import com.zly.project.settlement.domain.CustomerTradeApply;
import com.zly.project.settlement.domain.UserBankBind;
import com.zly.project.settlement.domain.request.ali.AccOpenReq;
import com.zly.project.settlement.domain.request.huaxia.DownLoadReq;
import com.zly.project.settlement.domain.request.huaxia.PDSNoticeRes;
import com.zly.project.settlement.domain.response.CustomerTradeFlowRes;
import com.zly.project.settlement.domain.response.huaxia.*;
import com.zly.project.settlement.mapstruct.UserTradeFlowToCustomerTradeFlowVoMapStruct;
import com.zly.project.settlement.service.*;
import com.zly.project.settlement.service.impl.CheckTheParametersService;
import com.zly.project.settlement.service.impl.CustomerAccountSubServiceImpl;
import com.zly.project.settlement.service.impl.RollbackService;
import com.zly.project.settlement.service.mq.MQNoticeService;
import com.zly.project.system.service.ISysUserService;
import com.zly.project.system.service.impl.CarrierMessageInfoServiceImpl;
import com.zly.project.taxUpload.service.YwkUploadService;
import com.zly.project.transport.waybill.domain.PaymentRecord;
import com.zly.project.transport.waybill.domain.Waybill;
import com.zly.project.transport.waybill.mapper.WaybillMapperEx;
import com.zly.project.transport.waybill.service.IWaybillService;
import com.zly.project.transport.waybill.service.PaymentRecordService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.FileCopyUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.security.MessageDigest;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * @Title: HuaXiaService
 * @ProjectName: zly-nfs-catalog
 * @Description: TODO
 * @Author: Mr.zhong
 * @Date: 2022/1/1214:09
 */
@Slf4j
@Service
public class HuaXiaService {

	@Resource
	private CustomerAccountSubServiceImpl customerAccountSubService;
	@Resource
	private IFrameworkContractService contractService;
	@Resource
	private GroupFrameworkContractServiceImpl groupFrameworkContractService;
	@Resource
	private BusinessModelService businessModelService;
	@Resource
	private PaymentRecordService paymentRecordService;
	@Resource
	private ExportService exportService;
	@Resource
	private YwkUploadService ywkUploadService;
	@Resource
	private CarrierMessageInfoServiceImpl carrierMessageInfoService;
	@Resource
	private CarrierUserServiceImpl carrierUserService;
	@Resource
	private WaybillMapperEx waybillMapperEx;
	@Resource
	private WaybillRiskService waybillRiskService;

	/**
	 * 企业客户开户，这个接口只能用于企业用户开户
	 *
	 * @param req
	 * @return
	 */
	@Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
	public AjaxResult openCustomerInfoAccount(AccOpenReq req) throws Exception {
		checkTheParametersService.checkAccountOpenPara(req);
		String key = "OPEN_ACCOUNT_" + req.getIdCode();
		String value = UUID.randomUUID().toString();
		try {
			// 60秒锁定
			if (!redisCache.lock(key, value, 60)) {
				log.info("开户失败，返回错误：{}", key);
				throw new RuntimeException("开户失败，请稍后再操作！");
			}
			// 查询客户信息
			CustomerInfo customerInfo = checkTheParametersService.getCustomerByIdCode(req.getIdCode());
			// 查询客户账户如果已开户，返回
			try {
				CustomerAccount customerAccount = checkTheParametersService.getCustomerAccountByIdNumber(customerInfo.getCreditCode());
				// return AjaxResult.success();
				return this.accountOpen(customerInfo.getId(), customerAccount.getAccountNo());
			} catch (Exception e) {
				log.info("继续正常开户");
			}
			if (StringUtils.isBlank(customerInfo.getCustomerName())) {
				throw new RuntimeException("开户名称为空");
			}
			if (StringUtils.isBlank(customerInfo.getContact())) {
				throw new RuntimeException("开户联系人为空");
			}
			if (StringUtils.isBlank(customerInfo.getContactPhone())) {
				throw new RuntimeException("开户联系人电话为空");
			}
			JSONObject sendJson = new JSONObject();
			// 用户类型（1：商户，7：司机，10：保险经纪人）
			sendJson.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
			// 客户来源类型(10：PC，20：小程序端)
			sendJson.put("sourceType", BusinessConstants.SOURCE_PC_10);
			// 如果是承运人自己开户的，是20的来源 这个接口只能企业开户，不需要修改sourceType
			/*if (ClientType.CARRIER.equals(SecurityUtils.getClientType())) {
				sendJson.put("sourceType", BusinessConstants.SOURCE_APP_20);
			}*/
			sendJson.put("idNo", customerInfo.getCreditCode()); // 原证件号码
			sendJson.put("callerUserName", customerInfo.getCustomerName());// 合作平台用户ID（机构ID，司机ID）
			sendJson.put("isPersonal", BusinessConstants.BUSINESS_CUSTOMER);// 商户性质(:0:个人，1：企业)
			sendJson.put("cardType", BusinessConstants.CERTIFICATES_CUSTOMER);// 证件类型(1.身份证)
			sendJson.put("cardNo", customerInfo.getCreditCode());// 证件号码（分公司营业执照或收款人身份证号）
			sendJson.put("contact", customerInfo.getContact());// 联系人
			sendJson.put("contactMobile", customerInfo.getContactPhone());// 手机号码
			sendJson.put("operType", BusinessConstants.PDS_OPERATION_TYPE_ADD);// 操作类型（1新增，2变更，3删除）
			// 请求银行
			AjaxResult result = huaXiaHttpUtil.sendAccountPost(openAccountUrl, sendJson);
			/*
			String accountNo = ("5601" + TextUtil.getSecondTimeSequenceID(2));
			Object obj = hxpdsData.openAccount("0", customerInfo.getCustomerName(), customerInfo.getContact(), customerInfo.getContactPhone(), customerInfo.getAddress(), "16",
					customerInfo.getCreditCode(), customerInfo.getId(), accountNo);
			log.info("请求银行，开户结果：{}", JSONObject.toJSONString(result));
			*/
			if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
				log.info("用户新增--->" + result.getMsg());
				throw new RuntimeException(result.getMsg());
			}
			UserTotalAccount account = JSONObject.parseObject(JSONObject.toJSONString(result.getData())).getObject("userTotalAccount", UserTotalAccount.class);
			// 插入用户信息
			iCustomerAccountService.initCustomerInfoAccount(customerInfo, account.getAccountNo(), bankCode, bankName);
			// 开户成功返回系统账户信息

			log.info("--------------开始托运人开户--------------");
			this.accountOpen(customerInfo.getId(), account.getAccountNo());

			log.info("--------------开始项目开户--------------");

			// 查询托运人所有项目
			FrameworkContract frameworkContract = new FrameworkContract();
			frameworkContract.setState(1);
			frameworkContract.setNeedAuditing(1);
			frameworkContract.setIsPrepay(0);
			frameworkContract.setTenantId(customerInfo.getId());
			List<FrameworkContract> contractList = frameworkContractService.selectFrameworkContractList(frameworkContract);
			log.info("--------------开始循环项目开户--------------");
			for (FrameworkContract contract : contractList) {
				log.info("--------------开始循环项目开户--------------项目名称:" + contract.getContractName() + "-----------");
				List<FrameworkContractSubchain> contractSubchains = contractService.generateSubchains(contract, contract.getBusinessModelId() == 168981728998800015L);
				BusinessModel businessModel = businessModelService.selectBusinessModelById(contract.getBusinessModelId());
				groupFrameworkContractService.addSubchainsNoInster(contractSubchains, contract.getId(), SecurityUtils.getNickname(), businessModel);
				groupFrameworkContractService.createContractAccount(contractSubchains, contract);
			}
			return AjaxResult.success();
		} finally {
			redisCache.unlock(key, value);
		}
	}

	/**
	 * 收款人本人（司机、车队长、辅助员）开户(收款人的账号，从customer_account里面划分到payee_info)
	 *
	 * @param payeeInfo
	 * @return
	 * @deprecated 这个方法所有引用的地方在2025年06月30日发现都被注释掉了，现在把这个方法标记为弃用，如果重新启用，要删除该注解（@Deprecated）
	 */
	@Deprecated
	public CustomerAccount openPayeeInfoAccount(PayeeInfo payeeInfo) {
		Integer sourceType = BusinessConstants.SOURCE_PC_10;
		if (ClientType.CARRIER.equals(SecurityUtils.getClientType())) {
			sourceType = BusinessConstants.SOURCE_APP_20;
		}
		return this.huaxiaOpenAccount(payeeInfo, sourceType);
	}

	/**
	 * 亲属收款人开户(收款人的账号，从customer_account里面划分到info)
	 *
	 * @param payeeInfo
	 * @return
	 */
	public CustomerAccount openDriverFamilyAccount(PayeeInfo payeeInfo) {
		return this.huaxiaOpenAccount(payeeInfo, BusinessConstants.SOURCE_FAMILY_30);
	}

	/**
	 * @param payeeInfo
	 * @param sourceType
	 * 		10 pc虚拟户 20 app虚拟户 30 亲属虚拟户
	 * @return
	 */
	public CustomerAccount huaxiaOpenAccount(PayeeInfo payeeInfo, Integer sourceType) {
		if (null == payeeInfo) {
			throw new RuntimeException("收款人开户信息缺失");
		}
		if (StringUtils.isBlank(payeeInfo.getIdentityCard())) {
			throw new RuntimeException("收款人身份信息为空");
		}
		if (StringUtils.isBlank(payeeInfo.getPayeeName())) {
			throw new RuntimeException("收款人姓名为空");
		}
		if (StringUtils.isBlank(payeeInfo.getBankMobile())) {
			throw new RuntimeException("联系电话为空");
		}
		String key = "OPEN_ACCOUNT_" + payeeInfo.getIdentityCard();
		String value = UUID.randomUUID().toString();
		try {
			if (!redisCache.lock(key, value, 60)) { // 修改未锁定时间60秒
				log.info("收款人开户失败，返回错误：{}", key);
				throw new RuntimeException("收款人开户失败，请稍后再操作！");
			}
			JSONObject sendJson = new JSONObject();
			// 用户类型（1：商户，7：司机，10：保险经纪人）
			sendJson.put("userType", BusinessConstants.USER_TYPE_PAYEE);
			sendJson.put("sourceType", sourceType);
			// 原证件号码
			sendJson.put("idNo", payeeInfo.getIdentityCard());
			// 合作平台用户ID（机构ID，司机ID）
			sendJson.put("callerUserName", payeeInfo.getPayeeName());
			// 商户性质(:0:个人，1：企业)
			sendJson.put("isPersonal", BusinessConstants.BUSINESS_PERSONAL);
			// 证件类型(10.身份证)
			sendJson.put("cardType", BusinessConstants.CERTIFICATES_PERSONAL);
			// 证件号码（分公司营业执照或收款人身份证号）
			sendJson.put("cardNo", payeeInfo.getIdentityCard());
			// 联系人
			sendJson.put("contact", payeeInfo.getPayeeName());
			// 手机号码
			sendJson.put("contactMobile", payeeInfo.getBankMobile());
			// 操作类型（1新增，2变更，3删除）
			sendJson.put("operType", BusinessConstants.PDS_OPERATION_TYPE_ADD);
			AjaxResult result = huaXiaHttpUtil.sendAccountPost(openAccountUrl, sendJson);

			/* =================================== ↓↓↓↓↓ TEST CODE ↓↓↓↓↓ =================================== */
			if (Arrays.asList("dev", "test").contains(SpringUtils.getActiveProfile())) {
				if (Arrays.asList("342222197003020148", "14021119750925471X", "14020319750925121X").contains(payeeInfo.getIdentityCard())) {
					// 开户失败
					// 刘民侠 342222197003020148
					// 翟亚兵 14021119750925471X
					// 刘全义 14020319750925121X
					result.setCode(HttpStatus.ERROR);
					result.setMsg("开户失败（测试环境模拟用户开户失败）");
				} else if (Arrays.asList("642102197911090088", "142332199701242821", "140211197412034710").contains(payeeInfo.getIdentityCard())) {
					// 开户失败，黑户
					// 李娟 642102197911090088
					// 王云连 142332199701242821
					// 刘利军 140211197412034710
					result.setCode(HttpStatus.ERROR);
					result.setMsg("交易异常，请联系客户经理，请勿重复提交（测试环境模拟用户黑户导致的开户失败）");
				}
			}
			/* =================================== ↑↑↑↑↑ TEST CODE ↑↑↑↑↑ =================================== */

			if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
				log.info("司机开户失败，用户新增--->" + result.getMsg());
				throw new RuntimeException(result.getMsg());
			}
			// 查询客户账户如果已开户，返回customerAccount，不插入表。
			CustomerAccount customerAccount = checkTheParametersService.customerAccountIsExistByIdNumber(payeeInfo.getIdentityCard());
			if (customerAccount == null) {
				UserTotalAccount account = JSONObject.parseObject(JSONObject.toJSONString(result.getData())).getObject("userTotalAccount", UserTotalAccount.class);
				UserBankBind caller = JSONObject.parseObject(JSONObject.toJSONString(result.getData())).getObject("caller", UserBankBind.class);
				// 插入一个虚拟的收款人。用户放入运单。
				PayeeInfo payeeInsert = new PayeeInfo();
				BeanUtils.copyProperties(payeeInfo, payeeInsert);
				payeeInsert.setId(TextUtil.getTimeSequenceID(5));
				payeeInsert.setState(0);
				payeeInsert.setIsOther(0);
				payeeInsert.setBankCode("************");
				payeeInsert.setBankName("华夏银行股份有限公司");
				payeeInsert.setBankCardNo(account.getAccountNo());
				payeeInsert.setIsDefault(-1);
				payeeInsert.setCheckResult(1);
				// 银行卡类型（0.支付系统虚拟子账号 10：PC端实体卡，20：小程序端实体卡 30：PC小程序公用）
				payeeInsert.setSourceType(BusinessConstants.SOURCE_VIRTUAL_0);
				// 判断数据库中是否存在虚拟账户，如果存在虚拟账户会导致添加重复虚拟账户
				int cnt = payeeInfoMapper.selectPayeeInfoFictitiousByIdentityCardCnt(payeeInfo.getIdentityCard());
				if (0 == cnt) {
					payeeInfoMapper.insertPayeeInfo(payeeInsert);
				}
				customerAccount = iCustomerAccountService.initPayeeInfoAccount(payeeInsert, account.getAccountNo(), bankCode, bankName);
				customerAccount.setCustomerName(caller.getCallerUserName());
			}
			return customerAccount;
		} finally {
			redisCache.unlock(key, value);
		}
	}

	/**
	 * 更新托运人企业用户的信息
	 *
	 * @param req
	 * @return
	 */
	public AjaxResult updateCustomerInfoAccount(AccOpenReq req) {
		checkTheParametersService.checkAccountOpenPara(req);
		// 查询客户信息
		CustomerInfo customerInfo = checkTheParametersService.getCustomerByIdCode(req.getIdCode());
		customerInfo.setCustomerName(req.getName());
		customerInfo.setContact(req.getContact());
		customerInfo.setContactPhone(req.getContactMobile());
		customerInfo.setCreditCode(req.getIdCode());
		// 查询客户账户如果已开户，返回
		CustomerAccount account = checkTheParametersService.getCustomerAccountByIdNumber(customerInfo.getCreditCode());
		account.setCustomerName(req.getName());
		account.setIdNumber(req.getIdCode());
		JSONObject sendJson = new JSONObject();
		// 用户类型（1：商户，7：司机，10：保险经纪人）
		sendJson.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		// 客户来源类型(10：PC，20：小程序端)
		sendJson.put("sourceType", BusinessConstants.SOURCE_PC_10);
		// 如果是承运人自己开户的，是20的来源
		// 这个接口企业专用户，没有20的移动端
		/*if (ClientType.CARRIER.equals(SecurityUtils.getClientType())) {
			sendJson.put("sourceType", BusinessConstants.SOURCE_APP_20);
		}*/
		// 原证件号码
		sendJson.put("idNo", customerInfo.getCreditCode());
		// 合作平台用户ID（机构ID，司机ID）
		sendJson.put("callerUserName", customerInfo.getCustomerName());
		// 商户性质(:0:个人，1：企业)
		sendJson.put("isPersonal", BusinessConstants.BUSINESS_CUSTOMER);
		// 证件类型(1.身份证)
		sendJson.put("cardType", BusinessConstants.CERTIFICATES_CUSTOMER);
		// 证件号码（分公司营业执照或收款人身份证号）
		sendJson.put("cardNo", customerInfo.getCreditCode());
		// 联系人
		sendJson.put("contact", customerInfo.getContact());
		// 手机号码
		sendJson.put("contactMobile", customerInfo.getContactPhone());
		// 操作类型（1新增，2变更，3删除）
		sendJson.put("operType", BusinessConstants.PDS_OPERATION_TYPE_EDIT);
		// 请求银行
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(openAccountUrl, sendJson);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("用户编辑--->" + result.getMsg());
			return result;
		}
		customerInfo.setUpdateBy(SecurityUtils.getNickname());
		int ret = customerInfoService.updateCustomerInfo(customerInfo);
		if (ret < 1) {
			return AjaxResult.error("更新客户信息失败");
		}
		// 在service
		iCustomerAccountService.updateCustomerAccount(account);
		return AjaxResult.success();
	}

	/**
	 * 修改收款人个人信息
	 *
	 * @param req
	 * @return
	 */
	public CommonResult updatePayeeInfoAccount(AccOpenReq req, Boolean isNameTrue) {
		if (StringUtils.isBlank(req.getIdCode())) {
			throw new RuntimeException("收款人身份信息为空");
		}
		if (StringUtils.isBlank(req.getName())) {
			throw new RuntimeException("收款人姓名为空");
		}
		if (StringUtils.isBlank(req.getContactMobile())) {
			throw new RuntimeException("联系电话为空");
		}
		JSONObject sendJson = new JSONObject();
		// 用户类型（1：商户，7：司机，10：保险经纪人）
		sendJson.put("userType", BusinessConstants.USER_TYPE_PAYEE);
		// 客户来源类型(10：PC，20：小程序端)
		if (null == SecurityUtils.getClientType() || ClientType.CARRIER.equals(SecurityUtils.getClientType())) {
			// 如果是承运人自己开户的，是20的来源
			sendJson.put("sourceType", BusinessConstants.SOURCE_APP_20);
		} else {
			sendJson.put("sourceType", BusinessConstants.SOURCE_PC_10);
		}
		// 原证件号码
		sendJson.put("idNo", req.getIdCode());
		// 合作平台用户ID（机构ID，司机ID）
		sendJson.put("callerUserName", req.getName());
		// 商户性质(:0:个人，1：企业)
		sendJson.put("isPersonal", BusinessConstants.BUSINESS_PERSONAL);
		// 证件类型(1.身份证)
		sendJson.put("cardType", BusinessConstants.CERTIFICATES_PERSONAL);
		// 证件号码（分公司营业执照或收款人身份证号）
		sendJson.put("cardNo", req.getIdCode());
		// 联系人
		sendJson.put("contact", req.getContact());
		// 手机号码
		sendJson.put("contactMobile", req.getContactMobile());
		// 操作类型（1新增，2变更，3删除）
		sendJson.put("operType", BusinessConstants.PDS_OPERATION_TYPE_EDIT);
		// 请求银行
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(openAccountUrl, sendJson);
		if (result.isNotSuccess()) {
			log.info("用户编辑--->" + result.getMsg());
			return CommonResult.error(result.getMsg());
		}
		if (isNameTrue) {
			// 查询客户账户如果已开户，返回
			CustomerAccount account = checkTheParametersService.getCustomerAccountByIdNumber(req.getIdCode());
			account.setCustomerName(req.getName());
			account.setIdNumber(req.getIdCode());
			// 在service
			iCustomerAccountService.updateCustomerAccount(account);
		}
		return CommonResult.success();
	}

	/**
	 * 绑定银行卡操作
	 *
	 * @param payeeInfo
	 * @param operateType
	 * @return
	 */
	public AjaxResult bindBankCardOperating(PayeeInfo payeeInfo, int operateType) {
		return bindBankCardOperating(payeeInfo, operateType, null);
	}

	/**
	 * 绑定亲属银行银行卡操作，可用于新增 编辑 删除
	 *
	 * @param payeeInfo
	 * @param operateType
	 * 		操作类型（1新增，2变更，3删除）
	 * @return
	 */
	public AjaxResult bindFamilyBankCardOperating(PayeeInfo payeeInfo, int operateType) {
		return bindBankCardOperating(payeeInfo, operateType, BusinessConstants.SOURCE_FAMILY_30);
	}

	/**
	 * 绑定银行卡操作
	 *
	 * @param payeeInfo
	 * @param operateType
	 * 		操作类型（1新增，2变更，3删除）
	 * @param sourceType
	 * 		来源，10：pc，20：app 30 亲属，亲属的时候要传，其他的时候会根据客户端判断
	 * @return
	 */
	public AjaxResult bindBankCardOperating(PayeeInfo payeeInfo, int operateType, Integer sourceType) {
		if (StringUtils.isBlank(payeeInfo.getIdentityCard())) {
			throw new RuntimeException("证件号码为空");
		}
		if (StringUtils.isBlank(payeeInfo.getBankCardNo())) {
			throw new RuntimeException("银行账户为空");
		}
		// 取出空格，防止前台传入空格
		payeeInfo.setBankCardNo(payeeInfo.getBankCardNo().replaceAll("\\s*", ""));
		if (StringUtils.isBlank(payeeInfo.getBankName())) {
			throw new RuntimeException("结算银行名称为空");
		}
		if (StringUtils.isBlank(payeeInfo.getBankCode())) {
			throw new RuntimeException("结算银行卡联行号为空");
		}
		// 取出空格，防止前台传入空格
		payeeInfo.setBankCode(payeeInfo.getBankCode().replaceAll("\\s*", ""));
		if (StringUtils.isBlank(payeeInfo.getBankMobile())) {
			throw new RuntimeException("银行预留手机号码为空");
		}
		// 对公/对私（:0：对私，1：对公）
		if (null == payeeInfo.getIsOther()) {
			throw new RuntimeException("银行账户性质为空，对公/对私？");
		}
		if (!Arrays.asList(HuaXiaBankConstants.ZERO_ONE).contains(payeeInfo.getIsOther())) {
			throw new RuntimeException("结算卡性质不合法");
		}
		if (!Arrays.asList(BusinessConstants.PDS_OPERATION_TYPE_LIST).contains(operateType)) {
			throw new RuntimeException("操作类型不合法");
		}
		JSONObject sendJson = new JSONObject();
		// 用户类型（1：商户，7：司机，10：保险经纪人）
		sendJson.put("userType", BusinessConstants.USER_TYPE_PAYEE);
		// 客户来源类型(10：PC，20：小程序端 30: 亲属户)
		if (null != sourceType && 0 != sourceType.intValue()) {
			sendJson.put("sourceType", sourceType);
		} else {
			sendJson.put("sourceType", BusinessConstants.SOURCE_PC_10);
			if (ClientType.CARRIER.equals(SecurityUtils.getClientType())) {
				sendJson.put("sourceType", BusinessConstants.SOURCE_APP_20);
			}
		}
		// 用户证件号（分公司营业执照或收款人身份证号）
		sendJson.put("idNo", payeeInfo.getIdentityCard());
		// 银行卡号
		sendJson.put("bankCardNo", payeeInfo.getBankCardNo());
		// 银行卡号
		sendJson.put("oldBankCardNo", null);
		// 银行支付系统行号（联行号）
		sendJson.put("bankCode", payeeInfo.getBankCode());
		// 银行预留手机
		sendJson.put("bankMobile", payeeInfo.getBankMobile());
		// 银行名称
		sendJson.put("bankName", payeeInfo.getBankName());
		// 对公/对私:(0:对私 1:对公)
		sendJson.put("isOther", payeeInfo.getIsOther());
		// 操作类型（1新增，2变更，3删除）
		sendJson.put("operType", operateType);
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(bindBankCardUrl, sendJson);
		if (HttpStatus.SUCCESS != result.getCode()) {
			log.info("绑定银行卡：{}", result.getMsg());
			throw new RuntimeException(result.getMsg());
		}
		return AjaxResult.success();
	}

	/**
	 * 添加托运人账户
	 *
	 * @param customerBankCard,
	 * 		托运人银行卡信息
	 * @param creditCode
	 * 		托运人证件号码
	 * @param operateType
	 * 		操作类型，1新增，3删除
	 * @return
	 */
	public AjaxResult bindCustomerBankCardOperating(CustomerBankCard customerBankCard, String creditCode, int operateType) {
		if (null == customerBankCard) {
			throw new RuntimeException("托运人账户信息为空");
		}
		if (StringUtils.isBlank(creditCode)) {
			throw new RuntimeException("证件号码为空");
		}
		if (StringUtils.isBlank(customerBankCard.getBankCardNo())) {
			throw new RuntimeException("银行账户为空");
		}
		// 取出空格，防止前台传入空格
		customerBankCard.setBankCardNo(customerBankCard.getBankCardNo().replaceAll("\\s*", ""));
		if (StringUtils.isBlank(customerBankCard.getBankName())) {
			throw new RuntimeException("结算银行名称为空");
		}
		if (StringUtils.isBlank(customerBankCard.getBankCode())) {
			throw new RuntimeException("结算银行卡联行号为空");
		}
		// 取出空格，防止前台传入空格
		customerBankCard.setBankCode(customerBankCard.getBankCode().replaceAll("\\s*", ""));
		if (StringUtils.isBlank(customerBankCard.getBankMobile())) {
			throw new RuntimeException("银行预留手机号码为空");
		}
		// 对公/对私（:0：对私，1：对公）
		customerBankCard.setIsOther(1);
		if (!Arrays.asList(BusinessConstants.PDS_OPERATION_TYPE_LIST).contains(operateType)) {
			throw new RuntimeException("操作类型不合法");
		}
		JSONObject sendJson = new JSONObject();
		// 用户类型（1：商户，7：司机，10：保险经纪人）
		sendJson.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		// 客户来源类型(10：PC，20：小程序端 30: 亲属户)
		sendJson.put("sourceType", BusinessConstants.SOURCE_PC_10);
		// 用户证件号（分公司营业执照或收款人身份证号）
		sendJson.put("idNo", creditCode);
		// 银行卡号
		sendJson.put("bankCardNo", customerBankCard.getBankCardNo());
		// 银行卡号
		sendJson.put("oldBankCardNo", null);
		// 银行支付系统行号（联行号）
		sendJson.put("bankCode", customerBankCard.getBankCode());
		// 银行预留手机
		sendJson.put("bankMobile", customerBankCard.getBankMobile());
		// 银行名称
		sendJson.put("bankName", customerBankCard.getBankName());
		// 对公/对私:(0:对私 1:对公)
		sendJson.put("isOther", customerBankCard.getIsOther());
		// 操作类型（1新增，2变更，3删除）
		sendJson.put("operType", operateType);
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(bindBankCardUrlV2, sendJson);
		if (HttpStatus.SUCCESS != result.getCode()) {
			log.info("添加/删除托运人账户：{}", result.getMsg());
			if ("该内部簿记存在未复核的记录,请先复核".equals(result.getMsg())) {
				throw new RuntimeException("存在待授权的银行账户，请联系客服人员授权后继续操作。");
			} else {
				throw new RuntimeException(result.getMsg());
			}
		}
		return AjaxResult.success();
	}

	/**
	 * 查询银行卡状态
	 *
	 * @param creditCode
	 * 		托运人证件号码
	 * @return settleAccount 银行卡，IsOuath 0-不需要授权1-不通过授权2-通过授权 ，SignState 1-失败 2-成功 3-未查询。SignState 为2是授权成功。（如果需要严谨点，IsOuath  0、2，SignState  2 是授权成功）
	 */
	public AjaxResult queryCustomerBankState(String creditCode) {
		if (StringUtils.isBlank(creditCode)) {
			throw new RuntimeException("证件号码为空");
		}
		JSONObject sendJson = new JSONObject();
		// 用户类型（1：商户，7：司机，10：保险经纪人）
		sendJson.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		// 客户来源类型(10：PC，20：小程序端 30: 亲属户)
		sendJson.put("sourceType", BusinessConstants.SOURCE_PC_10);
		// 用户证件号（分公司营业执照或收款人身份证号）
		sendJson.put("idNo", creditCode);
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(queryBankCardState, sendJson);
		if (HttpStatus.SUCCESS != result.getCode()) {
			log.info("查询银行卡状态：{}", result.getMsg());
			throw new RuntimeException(result.getMsg());
		}
		return result;
	}

	/**
	 * 创建项目账户，一条链子上的那种
	 *
	 * @param frameworkContract
	 * 		项目合约
	 * @param contractList
	 * 		合同链子上的一群人,证件的集合
	 * @param advanceSequence
	 * 		垫资的层级。
	 * @return
	 */
	public List<UserLittleAccount> createProjectAccount(@NotNull FrameworkContract frameworkContract, String contractList, Integer advanceSequence, Integer recorderSequence, Integer payerSequence) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("callerProjectId", frameworkContract.getId());
		jsonObject.put("sourceType", BusinessConstants.SOURCE_PC_10);
		jsonObject.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		jsonObject.put("callerProjectName", frameworkContract.getContractName());
		jsonObject.put("callerObject", contractList);
		jsonObject.put("summary", frameworkContract.getAbstracts());
		jsonObject.put("phone", frameworkContract.getContactPhone());
		// 项目自费情况下传入5，否则为空，同时建立5和6，2个类型账户。(5：项目账户，6：垫资项目)
		jsonObject.put("accountType", frameworkContract.getIsPrepay() == 0 ? 5 : "");
		jsonObject.put("advanceSequence", advanceSequence);
		// 是否垫资项目 0 不是 1 是
		jsonObject.put("isPrepay", frameworkContract.getIsPrepay());
		// 录单人位置
		jsonObject.put("recorderSequence", recorderSequence);
		// 付款人的位置
		jsonObject.put("payerSequence", payerSequence);
		log.info("-- 项目子帐户开户（按项目创建） -- 请求数据，{}", jsonObject.toJSONString());
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(subAccountOpenUrl, jsonObject);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("项目子帐户开户--->" + result.getMsg());
			throw new RuntimeException("项目帐户开户失败：" + result.getMsg());
		}
		log.info("-- 项目子帐户开户（按项目创建） -- 返回数据：{}", JSONObject.toJSONString(result));
		JSONArray array = JSONArray.parseArray(JSONObject.toJSONString(result.getData()));
		List<UserLittleAccount> list = new ArrayList<>();
		for (int i = 0; i < array.size(); i++) {
			list.add(array.getJSONObject(i).getObject("sub_31", UserLittleAccount.class));
		}
		return list;
	}

	/**
	 * 更新项目子账户（只编辑摘要，项目名称。项目联系电话）
	 *
	 * @param frameworkContract
	 * @return
	 */
	public AjaxResult updateSubAccount(FrameworkContract frameworkContract) {
		if (null == frameworkContract) {
			throw new RuntimeException("修改项目账户失败，调拨项目合约信息为空");

		}
		if (null == frameworkContract.getId()) {
			throw new RuntimeException("修改项目账户失败，调拨项目合约ID为空");

		}
		if (StringUtils.isBlank(frameworkContract.getContractName())) {
			throw new RuntimeException("修改项目账户失败，调拨项目合约名称为空");
		}
		/*if (StringUtils.isBlank(frameworkContract.getAbstracts())) {
		    throw new RuntimeException("修改项目账户失败，调拨项目合约摘要为空");
		}*/
		if (StringUtils.isBlank(frameworkContract.getContactPhone())) {
			throw new RuntimeException("修改项目账户失败，调拨项目合约联系电话为空");
		}
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("callerProjectId", frameworkContract.getId());
		jsonObject.put("callerProjectName", frameworkContract.getContractName());
		jsonObject.put("summary", frameworkContract.getAbstracts());
		jsonObject.put("phone", frameworkContract.getContactPhone());
		log.info("-- 项目子账户变更 --请求数据" + jsonObject.toJSONString());
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(updateSubAccountUrl, jsonObject);
		log.info("------ 项目子账户变更 -----返回" + JSONObject.toJSONString(result));
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("项目子账户变更--->" + result.getMsg());
			throw new RuntimeException("项目子账户变更失败" + result.getMsg());
		}
		return result;
	}

	/**
	 * 注销项目子账户
	 *
	 * @param frameworkContract
	 * @return
	 */
	public AjaxResult cancelSubAccount(FrameworkContract frameworkContract) {
		if (null == frameworkContract) {
			throw new RuntimeException("注销项目子账户失败，项目合约信息为空");

		}
		if (null == frameworkContract.getId()) {
			throw new RuntimeException("注销项目子账户失败，项目合约ID为空");
		}
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("callerProjectId", frameworkContract.getId());
		log.info("-- 注销项目子账户 --请求数据" + jsonObject.toJSONString());
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(delSubAccountUrl, jsonObject);
		log.info("------ 注销项目子账户 -----返回" + JSONObject.toJSONString(result));
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("注销项目子账户--->" + result.getMsg());
			throw new RuntimeException("注销项目子账户失败，" + result.getMsg());
		}
		return result;
	}

	/**
	 * 查询移动端是否设置资金密码
	 *
	 * @param creditCode
	 * @return
	 * @throws Exception
	 */
	public AjaxResult isCapitalPasswordSet(String creditCode) {
		return this.isPasswordSet(creditCode, null, null);
	}

	/**
	 * 查询托运人是否设置资金密码
	 *
	 * @param creditCode
	 * @return
	 * @throws Exception
	 */
	public AjaxResult isCustomerPasswordSet(String creditCode) {
		return this.isPasswordSet(creditCode, null, null);
	}

	/**
	 * 查询亲属是否设置资金密码
	 *
	 * @param creditCode
	 * @return
	 * @throws Exception
	 */
	public AjaxResult isDriverFamilyPasswordSet(String creditCode) {
		return this.isPasswordSet(creditCode, null, BusinessConstants.SOURCE_FAMILY_30);
	}

	/**
	 * 查询是否托运人设置项目密码
	 *
	 * @param creditCode
	 * @return
	 * @throws Exception
	 */
	public AjaxResult isProjectPasswordSet(String creditCode, FrameworkContract frameworkContract) {
		if (null == frameworkContract || null == frameworkContract.getId()) {
			throw new RuntimeException("判断项目密码失败，项目信息为空");
		}
		return this.isPasswordSet(creditCode, frameworkContract, null);
	}

	/**
	 * 查询是否设置资金密码或者项目密码
	 *
	 * @param creditCode
	 * @param frameworkContract
	 * @param sourceType
	 * 		来源，10：pc，20：app 30 亲属，亲属的时候要传，其他的时候会根据客户端判断
	 * @return
	 * @throws Exception
	 */
	private AjaxResult isPasswordSet(String creditCode, FrameworkContract frameworkContract, Integer sourceType) {
		if (StringUtils.isBlank(creditCode)) {
			throw new RuntimeException("判断密码失败，证件信息为空");
		}
		JSONObject jsonObject = new JSONObject();
		// 用户类型（1：商户，7：司机，10：保险经纪人）
		if (ClientType.SHIPPER.equals(SecurityUtils.getClientType())) {
			jsonObject.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		} else {
			jsonObject.put("userType", BusinessConstants.USER_TYPE_PAYEE);
		}
		// 客户来源类型(10：PC，20：小程序端 30: 亲属户)
		if (null != sourceType && 0 != sourceType.intValue()) {
			jsonObject.put("sourceType", sourceType);
		} else {
			jsonObject.put("sourceType", BusinessConstants.SOURCE_PC_10);
			if (ClientType.CARRIER.equals(SecurityUtils.getClientType())) {
				jsonObject.put("sourceType", BusinessConstants.SOURCE_APP_20);
			}
		}
		// 用户证件号（分公司营业执照或收款人身份证号）
		jsonObject.put("idNo", creditCode);
		if (null != frameworkContract && null != frameworkContract.getId()) {
			jsonObject.put("callerProjectId", frameworkContract.getId());
		}
		log.info("-- 查询用户是否已设置密码 --请求数据" + jsonObject.toJSONString());
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(isPasswordSettedUrl, jsonObject);
		log.info("------ 查询用户是否已设置密码 -----返回" + JSONObject.toJSONString(result));
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("查询用户是否已设置密码--->" + result.getMsg());
			throw new RuntimeException("查询用户是否已设置密码：" + result.getMsg());
		}
		return result;
	}

	/**
	 * 获取商户的资金验证码
	 *
	 * @param creditCode
	 * @param phone
	 * @return
	 * @throws Exception
	 */
	public AjaxResult getCustomerCapitalVerifyCode(String creditCode, String phone) {
		return this.getVerifyCode(creditCode, null, BusinessConstants.USER_TYPE_CUSTOMER, phone, null);
	}

	/**
	 * 获取商户的项目验证码
	 *
	 * @param creditCode
	 * @param phone
	 * @return
	 * @throws Exception
	 */
	public AjaxResult getCustomerProjectVerifyCode(String creditCode, FrameworkContract frameworkContract, String phone) {
		if (null == frameworkContract) {
			throw new RuntimeException("发送验证码失败，项目信息为空");
		}
		return this.getVerifyCode(creditCode, frameworkContract, BusinessConstants.USER_TYPE_CUSTOMER, phone, null);
	}

	/**
	 * 获取承运人的资金验证码
	 *
	 * @param creditCode
	 * @param phone
	 * @return
	 * @throws Exception
	 */
	public AjaxResult getDriverCapitalVerifyCode(String creditCode, String phone) {
		return this.getVerifyCode(creditCode, null, BusinessConstants.USER_TYPE_PAYEE, phone, null);
	}

	/**
	 * 获取承运人亲属的资金验证码
	 *
	 * @param creditCode
	 * @param phone
	 * @return
	 * @throws Exception
	 */
	public AjaxResult getDriverFamilyVerifyCode(String creditCode, String phone) {
		return this.getVerifyCode(creditCode, null, BusinessConstants.USER_TYPE_PAYEE, phone, BusinessConstants.SOURCE_FAMILY_30);
	}

	/**
	 * 获取验证码
	 *
	 * @param creditCode
	 * @param frameworkContract
	 * @param phone
	 * @param sourceType
	 * 		来源，10：pc，20：app 30 亲属，亲属的时候要传，其他的时候会根据客户端判断
	 * @return
	 * @throws Exception
	 */
	private AjaxResult getVerifyCode(String creditCode, FrameworkContract frameworkContract, Integer userType, String phone, Integer sourceType) {
		if (StringUtils.isBlank(creditCode)) {
			throw new RuntimeException("发送验证码失败，证件信息为空");
		}
		if (StringUtils.isBlank(phone)) {
			throw new RuntimeException("发送验证码失败，手机号码为空");
		}
		if (null == frameworkContract) {
			frameworkContract = new FrameworkContract();
		}
		JSONObject jsonObject = new JSONObject();
		// 用户类型（1：商户，7：司机，10：保险经纪人）
		jsonObject.put("userType", userType);
		// 客户来源类型(10：PC，20：小程序端)
		if (null != sourceType && 0 != sourceType.intValue()) {
			jsonObject.put("sourceType", sourceType);
		} else {
			jsonObject.put("sourceType", BusinessConstants.SOURCE_PC_10);
			if (ClientType.CARRIER.equals(SecurityUtils.getClientType())) {
				jsonObject.put("sourceType", BusinessConstants.SOURCE_APP_20);
			}
		}
		// 如果是承运人自己开户的，是20的来源
		jsonObject.put("idNo", creditCode); // 用户证件号（分公司营业执照或收款人身份证号）
		jsonObject.put("callerProjectId", frameworkContract.getId());
		jsonObject.put("phone", phone);
		log.info("-- 获取验证码 --请求数据" + jsonObject.toJSONString());
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(verifyCodeUrl, jsonObject);
		log.info("------ 获取验证码 -----返回" + JSONObject.toJSONString(result));
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("获取验证码--->" + result.getMsg());
			throw new RuntimeException("获取验证码失败：" + result.getMsg());
		}
		return result;
	}

	/**
	 * 修改商户的资金密码
	 *
	 * @param creditCode
	 * @param phone
	 * @return
	 * @throws Exception
	 */
	public AjaxResult customerCapitalPasswordChange(String creditCode, String phone, String code, String password) {
		return this.passwordChange(creditCode, null, BusinessConstants.USER_TYPE_CUSTOMER, password, code, phone, null, true);
	}

	/**
	 * 修改商户的项目密码
	 *
	 * @param creditCode
	 * @param phone
	 * @return
	 * @throws Exception
	 */
	public AjaxResult customerProjectPasswordChange(String creditCode, FrameworkContract frameworkContract, String phone, String code, String password) {
		if (null == frameworkContract) {
			throw new RuntimeException("修改项目密码失败，项目信息为空");
		}
		return this.passwordChange(creditCode, frameworkContract, BusinessConstants.USER_TYPE_CUSTOMER, password, code, phone, null, true);
	}

	/**
	 * 修改承运人的资金密码
	 *
	 * @param creditCode
	 * @param phone
	 * @return
	 * @throws Exception
	 */
	public AjaxResult driverPasswordChange(String creditCode, String phone, String code, String password) {
		return this.passwordChange(creditCode, null, BusinessConstants.USER_TYPE_PAYEE, password, code, phone, null, false);
	}

	/**
	 * 修改亲属的资金密码
	 *
	 * @param creditCode
	 * @param phone
	 * @return
	 * @throws Exception
	 */
	public AjaxResult familyPasswordChange(String creditCode, String phone, String code, String password) {
		return this.passwordChange(creditCode, null, BusinessConstants.USER_TYPE_PAYEE, password, code, phone, BusinessConstants.SOURCE_FAMILY_30, false);
	}

	/**
	 * 修改密码
	 *
	 * @param creditCode
	 * @param frameworkContract
	 * @param password
	 * @param code
	 * @param phone
	 * @param sourceType
	 * 		来源，10：pc，20：app 30 亲属，亲属的时候要传，其他的时候会根据客户端判断
	 * @param validateCode
	 * 		是否验证code
	 * @return
	 */
	private AjaxResult passwordChange(String creditCode, FrameworkContract frameworkContract, Integer userType, String password, String code, String phone, Integer sourceType, Boolean validateCode) {
		if (StringUtils.isBlank(creditCode)) {
			throw new RuntimeException("修改密码失败，证件信息为空");
		}
		if (StringUtils.isBlank(password)) {
			throw new RuntimeException("修改密码失败，密码为空");
		}
		if (validateCode && StringUtils.isBlank(code)) {
			throw new RuntimeException("修改密码失败，验证码为空");
		}
		if (StringUtils.isBlank(phone)) {
			throw new RuntimeException("修改密码失败，手机号码为空");
		}
		if (null == frameworkContract) {
			frameworkContract = new FrameworkContract();
		}
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("userType", userType); // 用户类型（1：商户，7：司机，10：保险经纪人）
		// 客户来源类型(10：PC，20：小程序端 30: 亲属户)
		if (null != sourceType && 0 != sourceType.intValue()) {
			jsonObject.put("sourceType", sourceType);
		} else {
			jsonObject.put("sourceType", BusinessConstants.SOURCE_PC_10);
			if (ClientType.CARRIER.equals(SecurityUtils.getClientType())) {
				jsonObject.put("sourceType", BusinessConstants.SOURCE_APP_20);
			}
		}
		jsonObject.put("idNo", creditCode); // 用户证件号（分公司营业执照或收款人身份证号）
		jsonObject.put("callerProjectId", frameworkContract.getId());
		jsonObject.put("password", password);
		jsonObject.put("code", code);
		jsonObject.put("phone", phone);
		// 如果不需要验证code,填入超级密码。
		if (!validateCode) {
			jsonObject.put("validateCode", validateCode);
			jsonObject.put("validateCodePassword", "wY0^hG5]nQ3?wD8/jA8=uO2!oM7");
		}
		log.info("-- 修改密码 --请求数据" + jsonObject.toJSONString());
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(passwordChangeUrl, jsonObject);
		log.info("------ 修改密码 -----返回" + JSONObject.toJSONString(result));
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("修改密码--->" + result.getMsg());
			return AjaxResult.error(result.getMsg());
			// throw new RuntimeException("修改密码失败：" + result.getMsg());
		}
		return result;
	}

	/**
	 * 司机的密码复制给亲属
	 *
	 * @param driverCreditCode
	 * @param familyCreditCode
	 * @return
	 */
	public AjaxResult passwordCopyToFamily(String driverCreditCode, Integer sourceType, String familyCreditCode, Integer targetType) {
		if (StringUtils.isBlank(driverCreditCode)) {
			throw new RuntimeException("司机证件信息为空");
		}
		if (StringUtils.isBlank(familyCreditCode)) {
			throw new RuntimeException("亲属证件信息为空");
		}
		JSONObject jsonObject = new JSONObject();
		// 用户证件号（分公司营业执照或收款人身份证号）
		jsonObject.put("sourceIdNo", driverCreditCode);
		jsonObject.put("sourceType", sourceType);
		jsonObject.put("targetIdNo", familyCreditCode);
		jsonObject.put("targetType", targetType);
		log.info("-- 司机的密码复制给亲属 --请求数据" + jsonObject.toJSONString());
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(copyPayPassUrl, jsonObject);
		log.info("------ 司机的密码复制给亲属 -----返回" + JSONObject.toJSONString(result));
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("司机的密码复制给亲属--->" + result.getMsg());
			throw new RuntimeException(result.getMsg());
		}
		return result;
	}

	/**
	 * 清空亲属的账户密码
	 *
	 * @param password
	 * @param creditCode
	 * @return
	 */
	public AjaxResult cleanFamilyPayPass(String password, String creditCode) {
		return this.cleanPayPass(creditCode, password, BusinessConstants.SOURCE_FAMILY_30);
	}

	/**
	 * 清空密码
	 *
	 * @param creditCode
	 * @param sourceType
	 * 		来源，10：pc，20：app 30 亲属，亲属的时候要传，不传默认20
	 * @return
	 */
	private AjaxResult cleanPayPass(String creditCode, String password, Integer sourceType) {
		if (StringUtils.isBlank(creditCode)) {
			throw new RuntimeException("司机证件信息为空");
		}
		/*if (StringUtils.isBlank(password)) {
			throw new RuntimeException("密码为空");
		}*/
		JSONObject jsonObject = new JSONObject();
		// 用户证件号（分公司营业执照或收款人身份证号）
		jsonObject.put("idNo", creditCode);
		jsonObject.put("password", password);
		// 客户来源类型(10：PC，20：小程序端 30: 亲属户)
		if (null != sourceType && 0 != sourceType.intValue()) {
			jsonObject.put("sourceType", sourceType);
		} else {
			jsonObject.put("sourceType", BusinessConstants.SOURCE_APP_20);
		}
		log.info("-- 清空密码 --请求数据" + jsonObject.toJSONString());
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(cleanPayPassUrl, jsonObject);
		log.info("------ 清空密码 -----返回" + JSONObject.toJSONString(result));
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("清空密码--->" + result.getMsg());
			throw new RuntimeException(result.getMsg());
		}
		return result;
	}

	/**
	 * 项目资金上划下拨
	 *
	 * @param customerInfo
	 * @param frameworkContract
	 * @param tranType
	 * @param money
	 * @param password
	 * @return
	 */
	public AjaxResult transferPayment(CustomerInfo customerInfo, FrameworkContract frameworkContract, Integer tranType, BigDecimal money, String password) {
		if (null == customerInfo) {
			throw new RuntimeException("上划下拨失败，调拨商户信息为空");
		}
		if (null == frameworkContract) {
			throw new RuntimeException("上划下拨失败，调拨项目合约为空");
		}
		if (money.compareTo(BigDecimal.ZERO) <= 0) {
			throw new RuntimeException("上划下拨失败，请填入大于0的调拨金额");
		}
		if (TextUtil.isNull(password)) {
			throw new RuntimeException("上划下拨失败，调拨密码为空");
		}
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("sourceType", BusinessConstants.SOURCE_PC_10);
		jsonObject.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		jsonObject.put("idNo", customerInfo.getCreditCode()); // 用户证件号（分公司营业执照或收款人身份证号）
		jsonObject.put("callerFlowId", "UD" + TextUtil.getTimeSequenceID(3));// 合作交易流水
		jsonObject.put("tranType", tranType);// 交易类型（ 5:运费下拨 6:运费上划，11：服务费下拨，12服务费上划）
		jsonObject.put("callerProjectId", frameworkContract.getId());// 合作平台项目id
		jsonObject.put("callerProjectName", frameworkContract.getContractName());// 合作平台项目名称
		jsonObject.put("money", money);// 交易金额
		jsonObject.put("creator", SecurityUtils.getLoginUser().getNickname());// 交易人
		jsonObject.put("password", password);// 资金管理密码
		log.info("-- 项目金额调拨 --请求数据" + jsonObject.toJSONString());
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(transferPaymentUrl, jsonObject);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("项目金额调拨--->" + result.getMsg());
			throw new RuntimeException("项目金额调拨失败：" + result.getMsg());
		}
		log.info("------ 项目金额调拨 -----返回" + JSONObject.toJSONString(result));
		return result;
	}

	/**
	 * 项目运输费资金上划下拨
	 *
	 * @param freightForwarderInfo
	 * @param frameworkContract
	 * @param tranType
	 * @param money
	 * @param password
	 * @return
	 */
	public AjaxResult transferFeePayment(FreightForwarderInfo freightForwarderInfo, FrameworkContract frameworkContract, Integer tranType, BigDecimal money, String password) {
		if (null == freightForwarderInfo) {
			throw new RuntimeException("运输费调拨失败，调拨商户信息为空");
		}
		if (StringUtils.isBlank(freightForwarderInfo.getCreditCode())) {
			throw new RuntimeException("运输费调拨失败，统一信用代码为空");
		}
		if (null == frameworkContract) {
			throw new RuntimeException("运输费调拨失败，调拨项目合约为空");
		}
		if (money == null) {
			throw new RuntimeException("运输费调拨失败，调拨金额为空");
		}
		if (money.compareTo(BigDecimal.ZERO) <= 0) {
			throw new RuntimeException("运输费调拨失败，请填入大于0的调拨金额");
		}
		if (TextUtil.isNull(password)) {
			throw new RuntimeException("运输费调拨失败，调拨密码为空");
		}
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("sourceType", BusinessConstants.SOURCE_PC_10);
		jsonObject.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		jsonObject.put("idNo", freightForwarderInfo.getCreditCode()); // 用户证件号（分公司营业执照或收款人身份证号）
		jsonObject.put("callerFlowId", "FD" + TextUtil.getTimeSequenceID(3));// 合作交易流水
		jsonObject.put("tranType", tranType);// 交易类型（ 5:运费下拨 6:运费上划，11：服务费下拨，12服务费上划 23 运输费上划 24运输费下拨）
		jsonObject.put("callerProjectId", frameworkContract.getId());// 合作平台项目id
		jsonObject.put("callerProjectName", frameworkContract.getContractName());// 合作平台项目名称
		jsonObject.put("money", money);// 交易金额
		jsonObject.put("creator", SecurityUtils.getLoginUser().getNickname());// 交易人
		jsonObject.put("password", password);// 资金管理密码
		log.info("-- 运输费上划下拨 --请求数据" + jsonObject.toJSONString());
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(feeTransFeePaymentUrl, jsonObject);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("运输费上划下拨--->" + result.getMsg());
			throw new RuntimeException("运输费调拨失败：" + result.getMsg());
		}
		log.info("------ 运输费上划下拨 -----返回" + JSONObject.toJSONString(result));
		return result;
	}

	/**
	 * 查询总账户的余额
	 *
	 * @param identityCard
	 * @param userType
	 * @return
	 */
	public UserAccount queryTotalAccountMoney(String identityCard, Integer userType) {
		return this.queryAccountMoney(identityCard, userType, 10, null);
	}

	/**
	 * 查询现金账户
	 *
	 * @param identityCard
	 * @return
	 */
	public UserAccount queryUnassignedAccountMoney(String identityCard) {
		return this.queryAccountMoney(identityCard, BusinessConstants.USER_TYPE_CUSTOMER, 20, null);
	}

	/**
	 * 查询项目账户
	 *
	 * @param identityCard
	 * @return
	 */
	public UserAccount queryProjectAccountMoney(String identityCard) {
		return this.queryAccountMoney(identityCard, BusinessConstants.USER_TYPE_CUSTOMER, 30, null);
	}

	/**
	 * 查询司机的总账户的余额
	 *
	 * @param identityCard
	 * @return
	 */
	public UserAccount queryDriverAccountMoney(String identityCard) {
		return this.queryAccountMoney(identityCard, BusinessConstants.USER_TYPE_PAYEE, 70, null);
	}

	/**
	 * 查询亲属的总账户的余额
	 *
	 * @param identityCard
	 * @return
	 */
	public UserAccount queryFamilyAccountMoney(String identityCard) {
		return this.queryAccountMoney(identityCard, BusinessConstants.USER_TYPE_PAYEE, 70, BusinessConstants.SOURCE_FAMILY_30);
	}

	/**
	 * 查询账户金额
	 *
	 * @param identityCard
	 * @param userType
	 * @param accounType
	 * @param sourceType
	 * 		来源，10：pc，20：app 30 亲属，亲属的时候要传，其他的时候会根据客户端判断
	 * @return
	 */
	public UserAccount queryAccountMoney(String identityCard, Integer userType, Integer accounType, Integer sourceType) {
		if (StringUtils.isBlank(identityCard)) {
			throw new RuntimeException("查询余额的证件信息为空");
		}
		if (null == userType) {
			throw new RuntimeException("查询余额的用户信息为空!");
		}
		String result = this.queryAccountMoney(identityCard, userType, accounType, -1, null, sourceType);
		if (result == null) {
			return new UserAccount();
		}
		JSONArray array = JSONArray.parseArray(result);
		if (null == array || array.isEmpty()) {
			return new UserAccount();
		}
		JSONObject jsonObject = array.getJSONObject(0);
		if (!jsonObject.containsKey("userAccount")) {
			return new UserAccount();
		}
		return jsonObject.getObject("userAccount", UserAccount.class);
	}

	/**
	 * 查询具体某个项目的金额
	 *
	 * @param identityCard
	 * 		证件
	 * @param accountType
	 * 		账户类型 30:项目总账户 40:结算计息总账户 50:保险服务总账户 60:油卡服务总账户
	 * @return
	 */
	public UserLittleAccount queryProjectAccountMoney(String identityCard, Integer accountType, Long projectId) {
		String result = this.queryAccountMoney(identityCard, BusinessConstants.USER_TYPE_CUSTOMER, accountType, 1, projectId, null);
		if (result == null) {
			return new UserLittleAccount();
		}
		JSONArray array = JSONArray.parseArray(result);
		if (array == null || array.isEmpty()) {
			return new UserLittleAccount();
		}
		JSONObject jsonObject = array.getJSONObject(0);
		if (!jsonObject.containsKey("littleAccounts")) {
			return new UserLittleAccount();
		}
		array = jsonObject.getJSONArray("littleAccounts");
		// {"msg":"操作成功","code":200,"data":[{"littleAccounts":[{"accountId":"167715314647400039","accountType":31,"borrowerName":"","callerId":"1","callerProjectId":"167715415218600028","callerProjectName":"吉林省苏泰电气有限公司","callerUserId":"***************","callerUserName":"江苏车联天下卡车俱乐部有限公司","createTime":"2023-02-23
		// 20:09:12","freezeMoney":0.00,"id":"167715415227600073","isPayer":1,"isPrepayer":0,"modifyTime":"*************","money":0.00,"payPassword":"","phone":"***********","remark":"91320400MA1PYBHT2J","status":0,"subAccountName":"吉林省苏泰电气有限公司项目子帐户","subAccountNo":"****************","sumMoney":0.00,"summary":"江苏车联天下卡车俱乐运费"}]}],"message":"操作成功"}
		if (array.isEmpty()) {
			return new UserLittleAccount();
		}
		return array.getObject(0, UserLittleAccount.class);
	}

	/**
	 * 查询余额，10，20现金账户，没有子账户，30，50等才有。 10:总账户 20:现金总帐户 30:项目总账户 40:结算计息总账户 50:保险服务总账户 60:油卡服务总账户 70:司机账户
	 *
	 * @param identityCard
	 * @param userType
	 * 		1的时候只能查询 10-60，7的时候只能查询70
	 * @param accountType
	 * @param isNeedSub
	 * @param projectId
	 * 		项目id
	 * @param sourceType
	 * 		来源，10：pc，20：app 30 亲属，亲属的时候要传，其他的时候会根据客户端判断
	 * @return
	 */
	public String queryAccountMoney(String identityCard, Integer userType, Integer accountType, Integer isNeedSub, Long projectId, Integer sourceType) {
		if (StringUtils.isBlank(identityCard)) {
			throw new RuntimeException("查询余额的证件信息为空");
		}
		if (null == userType) {
			throw new RuntimeException("查询余额的用户信息为空!");
		}
		if (null == accountType) {
			throw new RuntimeException("查询余额的账户信息!");
		}
		if (null == isNeedSub) {
			isNeedSub = 0;
		}
		JSONObject jsonObject = new JSONObject();
		// 客户来源类型(10：PC，20：小程序端 30: 亲属户)
		if (null != sourceType && 0 != sourceType.intValue()) {
			jsonObject.put("sourceType", sourceType);
		} else {
			jsonObject.put("sourceType", BusinessConstants.SOURCE_PC_10);
			if (ClientType.CARRIER.equals(SecurityUtils.getClientType())) {
				jsonObject.put("sourceType", BusinessConstants.SOURCE_APP_20);
			}
		}
		jsonObject.put("userType", userType);
		// 司机驾驶证号
		jsonObject.put("idNo", identityCard);
		// 项目ID（多个用逗号隔开）
		jsonObject.put("accountType", accountType);
		// 是否需要子帐户(1：需要)
		jsonObject.put("isNeedSub", isNeedSub);
		// 查询具体项目的余额
		jsonObject.put("callerProjectId", projectId);
		log.info("-- 查询账户 --请求数据" + jsonObject.toJSONString());
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(accountMoneyOtherUrl, jsonObject);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("查询账户失败--->" + result.getMsg());
			// throw new RuntimeException("查询账户失败");
			return null;
		}
		log.info("------ 查询账户  -----返回" + JSONObject.toJSONString(result));
		return JSONObject.toJSONString(result.getData());
	}

	public TableDataInfo querySubAccountMoney(String identityCard, Long projectId, Integer accountType) {
		return this.querySubAccountMoney(identityCard, projectId, null, accountType);
	}

	public TableDataInfo querySubAccountMoney(String identityCard, Long projectId, List<Long> projectIds, Integer accountType) {
		if (StringUtils.isBlank(identityCard)) {
			throw new RuntimeException("查询账户失败的证件信息为空");
		}
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("sourceType", BusinessConstants.SOURCE_PC_10);
		jsonObject.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		jsonObject.put("idNo", identityCard); // 证号
		jsonObject.put("callerProjectId", projectId); // 查询具体项目的余额
		jsonObject.put("projectIdList", projectIds); // 查询具体项目的余额
		jsonObject.put("accountType", accountType); // 查询具体项目的余额
		PageDomain pageDomain = TableSupport.buildPageRequest();
		jsonObject.put("pageNum", pageDomain.getPageNum());// 获取数据的当前页数
		jsonObject.put("pageSize", pageDomain.getPageSize());// 每页默认获取记录数
		log.info("-- 查询项目子账户余额 --请求数据" + jsonObject.toJSONString());
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(subAccountUrl, jsonObject);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("查询项目子账户余额--->" + result.getMsg());
			throw new RuntimeException("查询项目账户余额：" + result.getMsg());
		}
		log.info("------ 查询项目子账户余额  -----返回" + JSONObject.toJSONString(result));
		// List<UserLittleAccount>
		TableDataInfo rspData = new TableDataInfo();
		rspData.setCode(HttpStatus.SUCCESS);
		rspData.setMsg(String.valueOf(result.getMsg()));
		rspData.setRows(JSONArray.parseArray(JSONObject.toJSONString(result.get("rows"))).toJavaList(UserLittleAccount.class));
		rspData.setTotal(Integer.parseInt(String.valueOf(result.get("total"))));
		return rspData;
	}

	/**
	 * 查询运输费（管理费）账户
	 *
	 * @param identityCard
	 * @param projectId
	 * @param projectIds
	 * @return
	 */
	public TableDataInfo querySubFeeAccountMoney(String identityCard, Long projectId, List<Long> projectIds) {
		if (StringUtils.isBlank(identityCard)) {
			throw new RuntimeException("查询账户失败的证件信息为空");
		}
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("sourceType", BusinessConstants.SOURCE_PC_10);
		jsonObject.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		jsonObject.put("idNo", identityCard); // 证号
		jsonObject.put("callerProjectId", projectId); // 查询具体项目的余额
		jsonObject.put("projectIdList", projectIds); // 查询具体项目的余额
		// jsonObject.put("accountType", accountType); // 查询具体项目的余额
		PageDomain pageDomain = TableSupport.buildPageRequest();
		jsonObject.put("pageNum", pageDomain.getPageNum());// 获取数据的当前页数
		jsonObject.put("pageSize", pageDomain.getPageSize());// 每页默认获取记录数
		log.info("-- 查询项目子账户余额 --请求数据" + jsonObject.toJSONString());
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(subFeeAccountUrl, jsonObject);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("查询项目子账户余额--->" + result.getMsg());
			throw new RuntimeException("查询项目账户余额：" + result.getMsg());
		}
		log.info("------ 查询项目子账户余额  -----返回" + JSONObject.toJSONString(result));
		// List<UserLittleAccount>
		TableDataInfo rspData = new TableDataInfo();
		rspData.setCode(HttpStatus.SUCCESS);
		rspData.setMsg(String.valueOf(result.getMsg()));
		rspData.setRows(JSONArray.parseArray(JSONObject.toJSONString(result.get("rows"))).toJavaList(UserLittleAccount.class));
		rspData.setTotal(Integer.parseInt(String.valueOf(result.get("total"))));
		return rspData;
	}

	/**
	 * 查询项目上划下拨的流水
	 *
	 * @param identityCard
	 * @param projectId
	 * @param projectName
	 * @param startTime
	 * @param endTime
	 * @param tranType
	 * @return
	 */
	/*public List<UserTransferFlowRes> queryTransferFlowList(String identityCard, Long projectId, String projectName, String startTime, String endTime, Integer tranType) {
		return this.queryTransferFlowList(identityCard, projectId, null, projectName, startTime, endTime, tranType);
	}*/

	/**
	 * 查询项目上划下拨的流水
	 *
	 * @param identityCard
	 * @param projectId
	 * @param projectName
	 * @param startTime
	 * @param endTime
	 * @param tranType
	 * @return
	 */
	public TableInfo<UserTransferFlowRes> queryTransferFlowList(String identityCard, Long projectId, List<Long> contractIds, String projectName, String startTime, String endTime, Integer tranType) {
		if (StringUtils.isBlank(identityCard)) {
			throw new RuntimeException("查询项目上划下拨的流水失败，商户证件信息为空");
		}
		JSONObject sendMap = new JSONObject();
		sendMap.put("sourceType", BusinessConstants.SOURCE_PC_10);
		sendMap.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		sendMap.put("idNo", identityCard); // 证件号
		sendMap.put("callerProjectId", projectId);// 合作平台项目id
		sendMap.put("projectIdList", contractIds);// 合作平台项目id
		sendMap.put("callerProjectName", projectName);// 合作平台项目名称（模糊）
		sendMap.put("startTime", startTime);// 交易开始时间
		sendMap.put("endTime", endTime);// 交易开始时间
		sendMap.put("tranType", tranType);// 5:运费下拨 6:运费上划，11：服务费下拨，12服务费上划
		sendMap.put("isPay", (tranType == 5 || tranType == 11 || tranType == 24) ? 1 : -1);// 5:运费下拨 6:运费上划，11：服务费下拨，12服务费上划 23 运输费（申请开票服务费）上划 24运输费（申请开票服务费）下拨
		sendMap.put("state", 1);
		PageDomain pageDomain = TableSupport.buildPageRequest();
		sendMap.put("pageNum", pageDomain.getPageNum());// 获取数据的当前页数
		sendMap.put("pageSize", pageDomain.getPageSize());// 每页默认获取记录数
		sendMap.put("isSum", -1);// 不需要合计
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(transferFlowListUrl, sendMap);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("查询项目上划下拨的流水失败--->" + result.getMsg());
			throw new RuntimeException("查询项目上划下拨的流水失败：" + result.getMsg());
		}
		Object total = result.get("total");
		return TableInfo.success(JSONArray.parseArray(JSONObject.toJSONString(result.get("rows"))).toJavaList(UserTransferFlowRes.class), Long.parseLong(total.toString()));
	}

	/**
	 * 查询PC的交易流水，通用方法
	 *
	 * @param identityCard
	 * @param projectId
	 * @param projectName
	 * @param trader
	 * @param waybillCode
	 * @param flowCode
	 * @param isPay
	 * @param tranStartTime
	 * @param tranEndTime
	 * @param startTime
	 * @param endTime
	 * @param tranType
	 * @param state
	 * @return
	 */
	public TableDataInfo queryTradeFlowList(String identityCard, Long projectId, String projectName, String trader, String waybillCode, String flowCode, String isPay, String tranStartTime,
			String tranEndTime, String startTime, String endTime, Integer tranType, String state) {
		JSONObject sendMap = new JSONObject();
		sendMap.put("sourceType", BusinessConstants.SOURCE_PC_10);
		sendMap.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		sendMap.put("idNo", identityCard); // 证件号
		sendMap.put("isPay", isPay);// 交易类型（-1 ：付，1：收）
		sendMap.put("callerProjectId", projectId);// 合作平台项目id
		sendMap.put("callerProjectName", projectName);// 合作平台项目名称（模糊）
		sendMap.put("callerWaybillCode", waybillCode);// 合作平台运单编号
		sendMap.put("callerFlowId", flowCode);// 合作交易流水
		sendMap.put("twoName", trader);// 交易人名称
		sendMap.put("startTime", startTime);// 交易开始时间
		sendMap.put("endTime", endTime);// 交易开始时间
		sendMap.put("tranStartTime", tranStartTime);// 付款开始时间
		sendMap.put("tranEndTime", tranEndTime);// 付款结束时间
		sendMap.put("tranType", tranType);// 货物类别(10:责任险 19:货运险)
		sendMap.put("state", state);
		sendMap.put("isSum", -1);// 不需要合计
		PageDomain pageDomain = TableSupport.buildPageRequest();
		sendMap.put("pageNum", pageDomain.getPageNum());// 获取数据的当前页数
		sendMap.put("pageSize", pageDomain.getPageSize());// 每页默认获取记录数
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(tradeFlowListUrl, sendMap);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("查询运单交易流水--->" + result.getMsg());
			throw new RuntimeException("查询运单交易流水失败：" + result.getMsg());
		}
		TableDataInfo tableInfo = new TableDataInfo();
		tableInfo.setCode(HttpStatus.SUCCESS);
		tableInfo.setMsg(String.valueOf(result.getMsg()));
		tableInfo.setRows(JSONArray.parseArray(JSONObject.toJSONString(result.get("rows"))).toJavaList(UserTradeFlowRes.class));
		tableInfo.setTotal(Integer.parseInt(String.valueOf(result.get("total"))));
		return tableInfo;
	}

	/**
	 * 查询收款人流水
	 *
	 * @param identityCard
	 * @param waybillCode
	 * @param flowCode
	 * @param isPay
	 * @param isApp
	 * @param isQS
	 * @return
	 */
	public TableDataInfo queryPayeeTradeFlowList(String identityCard, String waybillCode, String flowCode, Integer isPay, Boolean isApp, Boolean isQS) {
		JSONObject sendMap = new JSONObject();
		if (isApp) {
			sendMap.put("sourceType", isQS ? BusinessConstants.SOURCE_FAMILY_30 : BusinessConstants.SOURCE_APP_20);
		} else {
			sendMap.put("sourceType", BusinessConstants.SOURCE_PC_10);
		}
		sendMap.put("userType", BusinessConstants.USER_TYPE_PAYEE);
		sendMap.put("idNo", identityCard); // 证件号
		sendMap.put("isPay", isPay);// 交易类型（-1 ：付，1：收）
		sendMap.put("callerWaybillCode", waybillCode);// 合作平台运单编号
		sendMap.put("callerFlowId", flowCode);// 合作交易流水
		sendMap.put("tranType", BusinessConstants.TRADE_TYPE_PAY);// 货物类别(10:责任险 19:货运险)
		sendMap.put("state", BusinessConstants.SUCCESS);
		sendMap.put("isSum", -1);// 不需要合计
		if (null == ServletUtils.getRequestAttributes()) {
			sendMap.put("pageNum", 1);// 获取数据的当前页数
			sendMap.put("pageSize", 100);// 每页默认获取记录数
		} else {
			PageDomain pageDomain = TableSupport.buildPageRequest();
			sendMap.put("pageNum", pageDomain.getPageNum());// 获取数据的当前页数
			sendMap.put("pageSize", pageDomain.getPageSize());// 每页默认获取记录数
		}
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(tradeFlowListUrl, sendMap);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("查询运单交易流水--->" + result.getMsg());
			throw new RuntimeException("查询运单交易流水失败：" + result.getMsg());
		}
		TableDataInfo tableInfo = new TableDataInfo();
		tableInfo.setCode(HttpStatus.SUCCESS);
		tableInfo.setMsg(String.valueOf(result.getMsg()));
		tableInfo.setRows(JSONArray.parseArray(JSONObject.toJSONString(result.get("rows"))).toJavaList(UserTradeFlowRes.class));
		tableInfo.setTotal(Integer.parseInt(String.valueOf(result.get("total"))));
		return tableInfo;
	}

	/**
	 * 查询网络货运平台交易流水
	 *
	 * @param identityCard
	 * @param waybillCode
	 * @param isPay
	 * @return
	 */
	public TableDataInfo queryFreightForwarderTradeFlowList(String identityCard, String waybillCode, Integer isPay) {
		JSONObject sendMap = new JSONObject();
		sendMap.put("sourceType", BusinessConstants.SOURCE_PC_10);
		sendMap.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		sendMap.put("idNo", identityCard); // 证件号
		sendMap.put("isPay", isPay);// 交易类型（-1 ：付，1：收）
		sendMap.put("callerWaybillCode", waybillCode);// 合作平台运单编号
		sendMap.put("tranType", BusinessConstants.TRADE_TYPE_PAY);// 货物类别(10:责任险 19:货运险)
		sendMap.put("state", BusinessConstants.SUCCESS);
		sendMap.put("isSum", -1);// 不需要合计
		sendMap.put("isAccurate", 1);// 是否精确查询，0否，1是。
		if (null == ServletUtils.getRequestAttributes()) {
			sendMap.put("pageNum", 1);// 获取数据的当前页数
			sendMap.put("pageSize", 100);// 每页默认获取记录数
		} else {
			PageDomain pageDomain = TableSupport.buildPageRequest();
			sendMap.put("pageNum", pageDomain.getPageNum());// 获取数据的当前页数
			sendMap.put("pageSize", pageDomain.getPageSize());// 每页默认获取记录数
		}
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(tradeFlowListUrl, sendMap);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("查询运单交易流水--->" + result.getMsg());
			throw new RuntimeException("查询运单交易流水失败：" + result.getMsg());
		}
		TableDataInfo tableInfo = new TableDataInfo();
		tableInfo.setCode(HttpStatus.SUCCESS);
		tableInfo.setMsg(String.valueOf(result.getMsg()));
		tableInfo.setRows(JSONArray.parseArray(JSONObject.toJSONString(result.get("rows"))).toJavaList(UserTradeFlowRes.class));
		tableInfo.setTotal(Integer.parseInt(String.valueOf(result.get("total"))));
		return tableInfo;
	}

	public List<CustomerTradeFlowRes> queryTradeFlowListByWaybillCode(String waybillCode) {
		JSONObject sendMap = new JSONObject();
		sendMap.put("callerWaybillCode", waybillCode);// 合作平台运单编号
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(tradeFlowListUrlByWaybillCode, sendMap);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("查询运单交易流水--->" + result.getMsg());
			throw new RuntimeException("查询运单交易流水失败：" + result.getMsg());
		}
		return userTradeFlowToCustomerTradeFlowVoMapStruct.customerTradeFlowResListConvert(JSONArray.parseArray(JSONObject.toJSONString(result.get("rows"))).toJavaList(UserTradeFlowRes.class));
	}

	/**
	 * 查询托运人的项目流水
	 *
	 * @param identityCard
	 * @param projectId
	 * @param projectName
	 * @param tradeName
	 * @param waybillCode
	 * @param isPay
	 * @param startTime
	 * @param endTime
	 * @param tranType
	 * @param state
	 * @param pageDomain
	 * @return
	 */
	public TableDataInfo queryCustomerServicesAccountFlow(String identityCard, String projectId, String projectName, String tradeName, String waybillCode, Integer isPay, String startTime,
			String endTime, Integer tranType, Integer state, PageDomain pageDomain) {
		return this.queryServicesFlowList(identityCard, projectId, projectName, tradeName, null, waybillCode, null, null, startTime, endTime, String.valueOf(tranType), String.valueOf(state), null);
	}

	/**
	 * 查询保险的交易流水
	 *
	 * @param identityCard
	 * @param projectId
	 * @param projectName
	 * @param trader
	 * @param waybillCode
	 * @param tranStartTime
	 * @param tranEndTime
	 * @param startTime
	 * @param endTime
	 * @param tranType
	 * @param state
	 * @return
	 */
	public TableDataInfo queryServicesFlowList(String identityCard, String projectId, String projectName, String trader, String tradeIdNo, String waybillCode, String tranStartTime, String tranEndTime,
			String startTime, String endTime, String tranType, String state, PageDomain pageDomain) {
		JSONObject sendMap = new JSONObject();
		sendMap.put("sourceType", BusinessConstants.SOURCE_PC_10);
		sendMap.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		sendMap.put("idNo", identityCard); // 证件号
		sendMap.put("isPay", BusinessConstants.TRANSACTION_PAYMENT);// 交易类型（-1 ：付，1：收）
		sendMap.put("callerProjectId", projectId);// 合作平台项目id
		sendMap.put("callerProjectName", projectName);// 合作平台项目名称（模糊）
		sendMap.put("callerWaybillCode", waybillCode);// 合作平台运单编号
		sendMap.put("tradeIdNo", tradeIdNo);// 交易人名称
		sendMap.put("twoName", trader);// 交易人名称
		sendMap.put("startTime", startTime);// 交易开始时间
		sendMap.put("endTime", endTime);// 交易开始时间
		sendMap.put("tranStartTime", tranStartTime);// 付款开始时间
		sendMap.put("tranEndTime", tranEndTime);// 付款结束时间
		sendMap.put("tranType", tranType);// 货物类别(10:责任险 19:货运险)
		sendMap.put("state", state);
		sendMap.put("isSum", 1);// 不需要合计
		if (pageDomain != null) {
			sendMap.put("pageNum", pageDomain.getPageNum());// 获取数据的当前页数
			sendMap.put("pageSize", pageDomain.getPageSize());// 每页默认获取记录数
		} else {
			if (null == ServletUtils.getRequestAttributes()) {
				sendMap.put("pageNum", 1);// 获取数据的当前页数
				sendMap.put("pageSize", 5000);// 每页默认获取记录数
			} else {
				pageDomain = TableSupport.buildPageRequest();
				sendMap.put("pageNum", pageDomain.getPageNum());// 获取数据的当前页数
				sendMap.put("pageSize", pageDomain.getPageSize());// 每页默认获取记录数
			}
		}
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(servicesFlowListUrl, sendMap);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("查询服务支付流水--->" + result.getMsg());
			throw new RuntimeException("查询服务支付流水失败：" + result.getMsg());
		}
		TableDataInfo tableInfo = new TableDataInfo();
		tableInfo.setCode(HttpStatus.SUCCESS);
		tableInfo.setMsg(String.valueOf(result.getMsg()));
		tableInfo.setRows(JSONArray.parseArray(JSONObject.toJSONString(result.get("rows"))).toJavaList(UserServicesFlowRes.class));
		tableInfo.setTotal(Integer.parseInt(String.valueOf(result.get("total"))));
		return tableInfo;
	}

	/**
	 * 查询保险统计列表
	 *
	 * @param userType
	 * @param projectId
	 * @param projectName
	 * @param tradeIdNo
	 * @param startTime
	 * @param endTime
	 * @param tranType
	 * @return
	 */
	public TableDataInfo queryServicesStatisticsList(String userType, String projectId, String projectName, String tradeIdNo, String startTime, String endTime, String tranType) {
		JSONObject sendMap = new JSONObject();
		sendMap.put("sourceType", BusinessConstants.SOURCE_PC_10);
		sendMap.put("isPay", BusinessConstants.TRANSACTION_RECEIVE);// 交易类型（-1 ：付，1：收）
		sendMap.put("callerProjectId", projectId);// 合作平台项目id
		sendMap.put("callerProjectName", projectName);// 合作平台项目名称（模糊）
		sendMap.put("tradeIdNo", tradeIdNo);// 交易人名称
		sendMap.put("startTime", startTime);// 交易开始时间
		sendMap.put("endTime", endTime);// 交易开始时间
		sendMap.put("tranType", tranType);// 货物类别(10:责任险 19:货运险)
		sendMap.put("state", 1);
		sendMap.put("userType", userType);
		PageDomain pageDomain = TableSupport.buildPageRequest();
		sendMap.put("pageNum", pageDomain.getPageNum());// 获取数据的当前页数
		sendMap.put("pageSize", pageDomain.getPageSize());// 每页默认获取记录数
		log.info("查询服务统计--->" + sendMap.toJSONString());
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(servicesSumListUrl, sendMap);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("查询服务统计--->" + result.getMsg());
			throw new RuntimeException("查询服务统计失败：" + result.getMsg());
		}
		TableDataInfo tableInfo = new TableDataInfo();
		tableInfo.setCode(HttpStatus.SUCCESS);
		tableInfo.setMsg(String.valueOf(result.getMsg()));
		tableInfo.setRows(JSONArray.parseArray(JSONObject.toJSONString(result.get("rows"))).toJavaList(UserServicesFlowRes.class));
		tableInfo.setTotal(Integer.parseInt(String.valueOf(result.get("total"))));
		return tableInfo;
	}

	/**
	 * 查询垫付流水
	 *
	 * @param identityCard
	 * @param projectId
	 * @param projectName
	 * @param waybillCode
	 * @param flowCode
	 * @param isPay
	 * @param payName
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public List<UserInsteadFlow> queryInsteadFlowList(String identityCard, String projectId, String projectName, String waybillCode, String flowCode, String isPay, String payName, String startTime,
			String endTime) {
		JSONObject sendMap = new JSONObject();
		sendMap.put("sourceType", BusinessConstants.SOURCE_PC_10);
		sendMap.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		sendMap.put("idNo", identityCard); // 证件号
		sendMap.put("callerProjectId", projectId);// 合作平台项目id
		sendMap.put("callerProjectName", projectName);// 合作平台项目名称（模糊）
		sendMap.put("callerWaybillCode", waybillCode);// 合作平台运单编号
		sendMap.put("isPay", isPay);// 交易类型（-1 ：付，1：收）
		sendMap.put("callerFlowId", flowCode);// 合作平台运单编号
		sendMap.put("twoAccountName", payName);// 交易人名称
		sendMap.put("twoName", payName);// 交易方名称
		sendMap.put("startTime", startTime);// 交易开始时间
		sendMap.put("endTime", endTime);// 交易开始时间
		sendMap.put("tranType", 4);// 交易类型(1:充值 2:提现)
		PageDomain pageDomain = TableSupport.buildPageRequest();
		sendMap.put("pageNum", pageDomain.getPageNum());// 获取数据的当前页数
		sendMap.put("pageSize", pageDomain.getPageSize());// 每页默认获取记录数
		sendMap.put("isSum", -1);// 不需要合计
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(insteadFlowListUrl, sendMap);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("查询垫付流水--->" + result.getMsg());
			throw new RuntimeException("查询垫付流水失败：" + result.getMsg());
		}
		return JSONArray.parseArray(JSONObject.toJSONString(result.get("rows"))).toJavaList(UserInsteadFlow.class);
	}

	/**
	 * 查询司机转出流水（添加亲属记录的话，这个接口可能要废除）
	 *
	 * @param identityCard
	 * @return
	 */
	public TableDataInfo queryDriverWithdrawList(String identityCard) {
		if (StringUtils.isBlank(identityCard)) {
			throw new RuntimeException("查询转出记录失败，证件信息为空");
		}
		return this.queryRechargeWithdrawList(identityCard, BusinessConstants.USER_TYPE_PAYEE, null, null, null, null, null, null, null, "-1", "2", null, null);
	}

	/**
	 * 查询司机转出流水（添加亲属记录的话，这个接口可能要废除）
	 *
	 * @param identityCard
	 * @return
	 */
	public TableDataInfo queryCustomerWithdrawList(String identityCard, String startTime, String endTime) {
		if (StringUtils.isBlank(identityCard)) {
			throw new RuntimeException("查询转出记录失败，证件信息为空");
		}
		return this.queryRechargeWithdrawList(identityCard, BusinessConstants.USER_TYPE_CUSTOMER, null, null, null, null, null, startTime, endTime, "-1", "2", null, null);
	}

	/**
	 * @param identityCard
	 * @param userType
	 * @param payNo
	 * @param payName
	 * @param callerFlowId
	 * @param tranStartTime
	 * @param tranEndTime
	 * @param startTime
	 * @param endTime
	 * @param isPay
	 * @param tranType
	 * @param state
	 * @param sourceType
	 * 		来源，10：pc，20：app 30 亲属，亲属的时候要传，其他的时候会根据客户端判断
	 * @return
	 */
	public TableDataInfo queryRechargeWithdrawList(String identityCard, Integer userType, String payNo, String payName, String callerFlowId, String tranStartTime, String tranEndTime, String startTime,
			String endTime, String isPay, String tranType, String state, Integer sourceType) {
		JSONObject sendMap = new JSONObject();
		// 客户来源类型(10：PC，20：小程序端 30: 亲属户)
		if (null != sourceType && 0 != sourceType.intValue()) {
			sendMap.put("sourceType", sourceType);
		} else {
			sendMap.put("sourceType", BusinessConstants.SOURCE_PC_10);
			if (ClientType.CARRIER.equals(SecurityUtils.getClientType())) {
				sendMap.put("sourceType", BusinessConstants.SOURCE_APP_20);
			}
		}
		sendMap.put("userType", userType);
		sendMap.put("idNo", identityCard); // 证件号
		sendMap.put("isPay", isPay);// 交易类型（-1 ：付，1：收）
		sendMap.put("callerFlowId", callerFlowId);// 合作平台运单编号
		sendMap.put("payNo", payNo);// 交易人名称
		sendMap.put("payName", payName);// 交易人名称
		sendMap.put("startTime", startTime);// 交易开始时间
		sendMap.put("endTime", endTime);// 交易开始时间
		sendMap.put("tranStartTime", tranStartTime);// 付款开始时间
		sendMap.put("tranEndTime", tranEndTime);// 付款结束时间
		sendMap.put("tranType", tranType);// 交易类型(1:充值 2:提现)
		sendMap.put("state", state);
		sendMap.put("isSum", -1);// 不需要合计
		PageDomain pageDomain = TableSupport.buildPageRequest();
		sendMap.put("pageNum", pageDomain.getPageNum());// 获取数据的当前页数
		sendMap.put("pageSize", pageDomain.getPageSize());// 每页默认获取记录数
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(rechargeWithdrawListUrl, sendMap);
		if (HttpStatus.SUCCESS != result.getCode()) {
			log.info("查询转入转出流水--->" + result.getMsg());
			throw new RuntimeException("查询转入转出流水失败：" + result.getMsg());
		}
		TableDataInfo tableInfo = new TableDataInfo();
		tableInfo.setCode(HttpStatus.SUCCESS);
		tableInfo.setMsg(String.valueOf(result.getMsg()));
		tableInfo.setRows(JSONArray.parseArray(JSONObject.toJSONString(result.get("rows"))).toJavaList(UserBankFlow.class));
		tableInfo.setTotal(Integer.parseInt(String.valueOf(result.get("total"))));
		return tableInfo;
	}

	/**
	 * 查询司机和亲属转出的流水（可用于其他的集合查询，只需要组装好数据），返回的数据上限100条。
	 *
	 * @param queryReqWithTimes
	 * @return
	 */
	public TableDataInfo queryDriverWithdrawListV2(List<BankFlowQueryReqWithTime> queryReqWithTimes) {
		if (null == queryReqWithTimes || queryReqWithTimes.isEmpty()) {
			throw new RuntimeException("查询转出记录失败，证件信息为空");
		}
		JSONObject sendMap = new JSONObject();
		sendMap.put("bankFlowQueryReqWithTimeList", queryReqWithTimes);
		sendMap.put("state", BusinessConstants.SUCCESS);
		sendMap.put("userType", BusinessConstants.USER_TYPE_PAYEE);
		PageDomain pageDomain = TableSupport.buildPageRequest();
		sendMap.put("pageNum", pageDomain.getPageNum());// 获取数据的当前页数
		sendMap.put("pageSize", pageDomain.getPageSize());// 每页默认获取记录数
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(rechargeWithdrawListV2Url, sendMap);
		if (HttpStatus.SUCCESS != result.getCode()) {
			log.info("查询转入转出流水--->" + result.getMsg());
			throw new RuntimeException("查询转入转出流水失败：" + result.getMsg());
		}
		TableDataInfo tableInfo = new TableDataInfo();
		tableInfo.setCode(HttpStatus.SUCCESS);
		tableInfo.setMsg(String.valueOf(result.getMsg()));
		tableInfo.setRows(JSONArray.parseArray(JSONObject.toJSONString(result.get("rows"))).toJavaList(UserBankFlow.class));
		tableInfo.setTotal(Integer.parseInt(String.valueOf(result.get("total"))));
		return tableInfo;
	}

	/**
	 * 查询司机的账户流水（转入、转出、交易）
	 *
	 * @param identityCard
	 * @param tradeName
	 * @param isPay
	 * @param startTime
	 * @param endTime
	 * @param tranType
	 * @return
	 */
	public TableDataInfo queryDriverAccountFlow(String identityCard, String tradeName, Integer isPay, String startTime, String endTime, Integer tranType, PageDomain pageDomain) {
		return this.queryAccountFlowList(identityCard, BusinessConstants.USER_TYPE_PAYEE, null, null, null, tradeName, null, isPay, 70, startTime, endTime, tranType, 1, pageDomain, null);
	}

	/**
	 * 查询托运人的总账户流水（转入、转出、交易）
	 *
	 * @param identityCard
	 * @param tradeName
	 * @param isPay
	 * @param startTime
	 * @param endTime
	 * @param tranType
	 * @return
	 */
	public TableDataInfo queryCustomerAccountFlow(String identityCard, String projectId, String tradeName, String waybillCode, Integer isPay, String startTime, String endTime, Integer tranType,
			Integer state, PageDomain pageDomain) {
		return this.queryAccountFlowList(identityCard, BusinessConstants.USER_TYPE_CUSTOMER, projectId, null, null, tradeName, waybillCode, isPay, 10, startTime, endTime, tranType, state, pageDomain,
				BusinessConstants.SOURCE_PC_10);
	}

	/**
	 * 查询托运人的总账户流水（转入、转出、交易）
	 *
	 * @param identityCard
	 * @param tradeName
	 * @param isPay
	 * @param startTime
	 * @param endTime
	 * @param tranType
	 * @return
	 */
	public TableDataInfo queryCustomerAccountFlow(String identityCard, String projectId, List<Long> contractIds, String tradeName, String waybillCode, Integer isPay, String startTime, String endTime,
			Integer tranType, Integer state, PageDomain pageDomain) {
		return this.queryAccountFlowList(identityCard, BusinessConstants.USER_TYPE_CUSTOMER, projectId, null, contractIds, tradeName, waybillCode, isPay, 10, startTime, endTime, tranType, state,
				pageDomain, BusinessConstants.SOURCE_PC_10);
	}

	/**
	 * 查询托运人的项目账户流水（上划、下拨、运单支付）
	 *
	 * @param identityCard
	 * @param tradeName
	 * @param waybillCode
	 * @param isPay
	 * @param startTime
	 * @param endTime
	 * @param tranType
	 * @return
	 */
	public TableDataInfo queryCustomerProjectAccountFlow(String identityCard, String projectId, String projectName, String tradeName, String waybillCode, Integer isPay, String startTime,
			String endTime, Integer tranType, Integer state, PageDomain pageDomain) {
		return this.queryAccountFlowList(identityCard, BusinessConstants.USER_TYPE_CUSTOMER, projectId, projectName, null, tradeName, null, isPay, 31, startTime, endTime, tranType, state, pageDomain,
				BusinessConstants.SOURCE_PC_10);
	}

	/**
	 * 查询托运人的项目账户流水（上划、下拨、运单支付）
	 *
	 * @param identityCard
	 * @param tradeName
	 * @param waybillCode
	 * @param isPay
	 * @param startTime
	 * @param endTime
	 * @param tranType
	 * @return
	 */
	public TableDataInfo queryCustomerProjectAccountFlow(String identityCard, String projectId, List<Long> contractIds, String projectName, String tradeName, String waybillCode, Integer isPay,
			String startTime, String endTime, Integer tranType, Integer state, PageDomain pageDomain) {
		return this.queryAccountFlowList(identityCard, BusinessConstants.USER_TYPE_CUSTOMER, projectId, projectName, contractIds, tradeName, waybillCode, isPay, 31, startTime, endTime, tranType,
				state, pageDomain, 10);
	}

	/**
	 * 查询账户流水
	 *
	 * @param identityCard
	 * @param userType
	 * @param projectId
	 * @param projectName
	 * @param contractIds
	 * @param tradeName
	 * @param waybillCode
	 * @param isPay
	 * @param accType
	 * @param startTime
	 * @param endTime
	 * @param tranType
	 * @param state
	 * @param pageDomain
	 * @param sourceType
	 * @return
	 */
	public TableDataInfo queryAccountFlowList(String identityCard, Integer userType, String projectId, String projectName, List<Long> contractIds, String tradeName, String waybillCode, Integer isPay,
			Integer accType, String startTime, String endTime, Integer tranType, Integer state, PageDomain pageDomain, Integer sourceType) {
		JSONObject sendMap = new JSONObject();
		// 客户来源类型(10：PC，20：小程序端 30: 亲属户)
		if (null != sourceType && 0 != sourceType.intValue()) {
			sendMap.put("sourceType", sourceType);
		} else {
			sendMap.put("sourceType", BusinessConstants.SOURCE_PC_10);
			if (ClientType.CARRIER.equals(SecurityUtils.getClientType())) {
				sendMap.put("sourceType", BusinessConstants.SOURCE_APP_20);
			}
		}

		sendMap.put("userType", userType);
		sendMap.put("idNo", identityCard); // 证件号
		sendMap.put("accType", accType);// 账户类型(1:总账户 2:现金总帐户 3:项目总账户 4:结算计息总账户 5:项目子帐户(父类是3) 6:结算项目子帐户(父类是4) 7:司机账户)
		sendMap.put("isPay", isPay);// 交易类型（-1 ：付，1：收）
		sendMap.put("callerProjectId", projectId);// 合作平台项目id
		sendMap.put("projectIdList", contractIds);// 合作平台项目id
		sendMap.put("callerProjectName", projectName);// 合作平台项目名称（模糊）
		sendMap.put("callerWaybillCode", waybillCode);// 合作平台运单编号
		sendMap.put("tradeName", tradeName);// 交易人名称
		sendMap.put("startTime", startTime);// 交易开始时间
		sendMap.put("endTime", endTime);// 交易开始时间
		sendMap.put("tranType", tranType);// 货物类别(10:责任险 19:货运险)
		sendMap.put("state", state);
		sendMap.put("isSum", 1);// 需要合计
		sendMap.put("isNeedMoney", -1); // 不需要最后的金额
		sendMap.put("pageNum", pageDomain.getPageNum());// 获取数据的当前页数
		sendMap.put("pageSize", pageDomain.getPageSize());// 每页默认获取记录数
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(accountFlowListUrl, sendMap);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("查询账户流水--->" + result.getMsg());
			throw new RuntimeException("查询账户流水失败：" + result.getMsg());
		}
		TableDataInfo rspData = new TableDataInfo();
		rspData.setCode(HttpStatus.SUCCESS);
		rspData.setMsg(String.valueOf(result.getMsg()));
		rspData.setRows(JSONArray.parseArray(JSONObject.toJSONString(result.get("rows"))).toJavaList(UserAccountFlow.class));
		rspData.setTotal(Integer.parseInt(String.valueOf(result.get("total"))));
		return rspData;
	}

	/**
	 * 这个是根据付款流水号下载司机的凭证。
	 *
	 * @param request
	 * @param response
	 * @param req
	 * @throws Exception
	 */
	public void download(HttpServletRequest request, HttpServletResponse response, DownLoadReq req) throws Exception {
		if (StringUtils.isBlank(req.getPayCode())) {
			throw new Exception("获取银行交易凭证失败：申请单号为空");
		}
		// 根据PN 查询信息
		CustomerTradeApply apply = new CustomerTradeApply();
		apply.setApplyPayCode(req.getPayCode());
		List<CustomerTradeApply> list = customerTradeApplyService.selectCustomerTradeApplyList(apply);
		if (list.size() == 0) {
			throw new RuntimeException("获取银行交易凭证失败：没有查询到付款记录！");
		}
		apply = list.get(0);
		if (BusinessConstants.WAYBILL_PAY_AUDIT_STATUS_PAID != apply.getAuditStatus()) {
			throw new RuntimeException("获取银行交易凭证失败：付款没有完成，无法下载凭证！");
		}
		// 查询收款人的证件信息
		PayeeInfo payeeInfo = iPayeeInfoService.selectPayeeInfoById(apply.getPayeeId());
		// 如果收款人是虚拟户，说明是小程序运单。
		if (Objects.equals(payeeInfo.getSourceType(), BusinessConstants.SOURCE_VIRTUAL_0)) {
			req.setSourceType(BusinessConstants.SOURCE_APP_20);
			throw new RuntimeException("运单号" + apply.getShippingNoteNumber() + "来源于移动端，请联系相关司机，在承运端小程序【运费账户-收支明细】页面查看所需支付凭证");
		} else {
			req.setSourceType(BusinessConstants.SOURCE_PC_10);
		}
		req.setUserType(BusinessConstants.USER_TYPE_PAYEE);
		req.setIdNo(payeeInfo.getIdentityCard());
		req.setTranType(2);// 因为查询的司机凭证，所以肯定是出金
		req.setCallerFlowId(apply.getApplyPayCode());
		req.setIsPay(BusinessConstants.TRANSACTION_PAYMENT);
		this.toDoDownload(response, req, downloadUrl + "download/certificate/single");
	}

	/***
	 * 根据网络货运平台下载交易流水
	 *
	 * @param response
	 * @param payCodeList
	 *            申请付款单号 PN开头的
	 * @param freightForwarderInfo
	 * @throws Exception
	 */
	public void downloadForApi(HttpServletResponse response, String shippingNoteNumber, List<String> payCodeList, FreightForwarderInfo freightForwarderInfo) throws Exception {
		if (null == payCodeList || payCodeList.isEmpty()) {
			throw new RuntimeException("获取银行交易凭证失败：付款申请单号为空！");
		}
		if (null == freightForwarderInfo || StringUtils.isBlank(freightForwarderInfo.getCreditCode())) {
			throw new RuntimeException("获取银行交易凭证失败：网络货运平台统一信用代码为空！");
		}
		this.apiDoDownload(response, shippingNoteNumber, payCodeList, freightForwarderInfo, downloadUrl + "download/certificate/single");
	}

	/**
	 * 批量下载
	 *
	 * @param request
	 * @param response
	 * @param req
	 * @throws Exception
	 */
	public void certificateDownload(HttpServletRequest request, HttpServletResponse response, DownLoadReq req) throws Exception {
		if (StringUtils.isBlank(req.getMonthOrday())) {
			throw new Exception("获取银行交易凭证失败：请选择下载天还是当月的交易凭证");
		}
		req.setUserType(BusinessConstants.USER_TYPE_CUSTOMER);
		req.setSourceType(BusinessConstants.SOURCE_PC_10);
		this.toDoDownload(response, req, downloadUrl + "download/certificateV2");
	}

	/**
	 * 这个是根据支付单号，payCode下载。
	 *
	 * @param request
	 * @param response
	 * @param req
	 * @throws Exception
	 */
	public void downloadV2(HttpServletRequest request, HttpServletResponse response, DownLoadReq req) throws Exception {
		if (StringUtils.isBlank(req.getPayCode())) {
			throw new Exception("获取银行交易凭证失败：支付单号为空");
		}
		this.toDoDownload(response, req, downloadUrl + "download/certificate/singleV2");
	}

	/**
	 * 华夏融资结果查看运单导出
	 *
	 * @param exportReq
	 * @return
	 */
	public List<FileModel> exportDriverPaymentVoucher(CustomerExportRecordReq exportReq) {
		List<FileModel> fileList = new ArrayList<>();
		// 生成对应导出业务的文件名称
		final String fileBaseName = exportService.generateFileName(exportReq.getRequestType());

		// 下载文件请求路径
		String fileRequestUrl = downloadUrl + "download/certificate/single";
		// 本批次下载批次号
		long downloadBatchId = TextUtil.getTimeSequenceID(5);
		// 本批次下载pdf文件本地目录
		String localPdfPath = "/tmp/certificateDownload/" + downloadBatchId + "/pdf/";
		try {

			int count = 0;
			int fileSize = 0;
			List<String> streamList = new ArrayList<>();
			for (String payCode : exportReq.getRequestParams().split(",")) {
				// 在循环里下载单个凭证
				DownLoadReq req = new DownLoadReq();
				CustomerTradeApply apply = new CustomerTradeApply();
				apply.setApplyPayCode(payCode);
				List<CustomerTradeApply> list = customerTradeApplyService.selectCustomerTradeApplyList(apply);
				if (list.size() == 0) {
					log.info("获取银行交易凭证失败：没有查询到付款记录！");
					continue;
				}
				apply = list.get(0);
				if (BusinessConstants.WAYBILL_PAY_AUDIT_STATUS_PAID != apply.getAuditStatus()) {
					log.info("获取银行交易凭证失败：付款没有完成，无法下载凭证！");
					continue;
				}
				// 查询收款人的证件信息
				PayeeInfo payeeInfo = iPayeeInfoService.selectPayeeInfoById(apply.getPayeeId());
				// 如果收款人是虚拟户，说明是小程序运单。
				if (Objects.equals(payeeInfo.getSourceType(), BusinessConstants.SOURCE_VIRTUAL_0)) {
					req.setSourceType(BusinessConstants.SOURCE_APP_20);
					log.info("获取银行交易凭证失败：移动端的运单，无法下载收款人的电子交易凭证！");
					continue;
				} else {
					req.setSourceType(BusinessConstants.SOURCE_PC_10);
				}

				req.setUserType(BusinessConstants.USER_TYPE_PAYEE);
				req.setIdNo(payeeInfo.getIdentityCard());
				req.setTranType(2);// 因为查询的司机凭证，所以肯定是出金
				req.setCallerFlowId(apply.getApplyPayCode());
				req.setIsPay(BusinessConstants.TRANSACTION_PAYMENT);
				req.setCallerFlowId(payCode);
				req.setPayCode(payCode);
				AjaxResult result = certificateSingleDownload(fileRequestUrl, req, localPdfPath, streamList);
				if (result.isSuccess()) {
					if (-2 != Long.parseLong(String.valueOf(result.getData()))) {
						count++;
						fileSize += Long.parseLong(String.valueOf(result.getData()));
					}
				}
			}

			if (0 == count) {
				log.info("本次请求没有查询到有效支付凭证，放弃执行打包逻辑");
				return null;
			}

			// 将本批次凭证文件打包
			// 压缩包文件名称
			String zipFileName = "司机凭证-" + downloadBatchId + ".zip";
			// 分目录名称（当前日期，与导出共用一个目录）
			String subdirname = DateUtils.today();
			// 压缩包本地路径
			String localFilePath = "D://files" + File.separator + subdirname + File.separator + zipFileName;

			toZip(localPdfPath, localFilePath, false);

			// 2、文件名加上当前序号
			String fileName = fileBaseName + ExportConstants.DELIMITER + ".zip";

			// 3、生成文件并上传至七牛云
			String fileUrl = exportService.easyExport(localFilePath, fileName);

			fileList.add(FileModel.of(fileName, fileUrl));

			return fileList;
		} catch (Exception e) {
			log.error("司机凭证下载失败" + ExceptionUtils.getStackTrace(e));
			return null;
		}
	}

	/**
	 * 压缩成ZIP 方法1
	 *
	 * @param source
	 * 		压缩文件夹路径
	 * @param dest
	 * 		压缩文件输出路径
	 * @param keepDirStructure
	 * 		是否保留原来的目录结构,true:保留目录结构; false:所有文件跑到压缩包根目录下(注意：不保留目录结构可能会出现同名文件,会压缩失败)
	 */
	public static void toZip(String source, String dest, boolean keepDirStructure) throws IOException {
		log.info("压缩中... " + source + " ...to... " + dest);
		FileOutputStream fos = null;
		ZipOutputStream zos = null;
		try {
			File destFile = new File(dest);
			if (!destFile.exists()) {
				destFile.getParentFile().mkdirs();
				destFile.createNewFile();
			}
			fos = new FileOutputStream(destFile);
			zos = new ZipOutputStream(fos);
			File sourceFile = new File(source);
			compress(sourceFile, zos, sourceFile.getName(), keepDirStructure);
		} catch (Exception e) {
			log.error("压缩文件失败{}", ExceptionUtils.getStackTrace(e));
			throw new RuntimeException("zip error from FileUtil", e);
		} finally {
			if (zos != null) {
				zos.close();
			}
			if (fos != null) {
				fos.close();
			}
		}
	}

	/**
	 * 递归压缩方法
	 *
	 * @param sourceFile 源文件
	 * @param zos zip输出流
	 * @param name 压缩后的名称
	 * @param keepDirStructure 是否保留原来的目录结构,true:保留目录结构; false:所有文件跑到压缩包根目录下(注意：不保留目录结构可能会出现同名文件,会压缩失败)
	 * @throws Exception
	 */
	private static final int BUFFER_SIZE = 2 * 1024;

	private static void compress(File sourceFile, ZipOutputStream zos, String name, boolean keepDirStructure) throws Exception {
		byte[] buf = new byte[BUFFER_SIZE];
		if (sourceFile.isFile()) {
			// 向zip输出流中添加一个zip实体，构造器中name为zip实体的文件的名字
			FileInputStream in = null;
			try {
				zos.putNextEntry(new ZipEntry(name));
				// copy文件到zip输出流中
				int len;
				in = new FileInputStream(sourceFile);
				while ((len = in.read(buf)) != -1) {
					zos.write(buf, 0, len);
				}
				// Complete the entry
			} finally {
				if (null != zos) {
					zos.closeEntry();
				}
				if (null != in) {
					in.close();
				}
			}
		} else {
			File[] listFiles = sourceFile.listFiles();
			if (listFiles == null || listFiles.length == 0) {
				// 需要保留原来的文件结构时,需要对空文件夹进行处理
				if (keepDirStructure) {
					// 空文件夹的处理
					zos.putNextEntry(new ZipEntry(name + "/"));
					// 没有文件，不需要文件的copy
					zos.closeEntry();
				}
			} else {
				for (File file : listFiles) {
					// 判断是否需要保留原来的文件结构
					if (keepDirStructure) {
						// 注意：file.getName()前面需要带上父文件夹的名字加一斜杠,
						// 不然最后压缩包中就不能保留原来的文件结构,即：所有文件都跑到压缩包根目录下了
						compress(file, zos, name + "/" + file.getName(), true);
					} else {
						compress(file, zos, file.getName(), false);
					}
				}
			}
		}
	}

	/**
	 * 司机凭证单个文件下载
	 *
	 * @param requestFileUrl
	 * 		请求远程文件URL
	 * @param req
	 * 		请求参数
	 * @param localPdfDir
	 * 		下载的pdf凭证文件存放目录 /tmp/certificateDownload/" + downloadBatchId + "/pdf/
	 * @throws IOException
	 */
	private AjaxResult certificateSingleDownload(String requestFileUrl, DownLoadReq req, String localPdfDir, List<String> streamList) throws IOException {
		log.info("执行司机支付凭证下载开始，请求code：{}，请求远程文件的URL：{}，请求参数：{}，pdf文件本地存放目录：{}", req.getPayCode(), requestFileUrl, JSON.toJSONString(req), localPdfDir);
		HttpURLConnection connection = null;
		BufferedInputStream bis = null;
		try {
			URL url = new URL(requestFileUrl);
			connection = (HttpURLConnection) url.openConnection();
			// post方式请求
			connection.setRequestMethod("POST");
			// 设置字符编码
			connection.setRequestProperty("Charset", String.valueOf(UTF_8));
			connection.setRequestProperty("Accept-Encoding", "identity"); // 加上这句话解决问题
			connection.setRequestProperty("Content-Type", "application/json");
			// 添加头文件
			connection.setRequestProperty("acc", accountAcc);
			connection.setRequestProperty("pas", accountPas);
			// 传递参数
			connection.setDoOutput(true);
			connection.setDoInput(true);
			connection.setUseCaches(false);
			// 打开到此 URL 引用的资源的通信链接（如果尚未建立这样的连接）
			connection.connect();
			DataOutputStream dataOutputStream = new DataOutputStream(connection.getOutputStream());
			dataOutputStream.writeBytes(JSONObject.toJSONString(req));
			dataOutputStream.flush();
			dataOutputStream.close();
			// length = -1 表示有凭证文件，length > 0 为下载异常
			int length = connection.getContentLength();
			log.info("下载文件：" + url + ",fileSize=" + length);
			if (length > 0 && "text/html;charset=UTF-8".equals(connection.getContentType())) {
				BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream(), UTF_8));
				String inputLine = bufferedReader.readLine();
				log.error("未查询到相关的交易凭证：" + inputLine);
				return AjaxResult.error(StringUtils.isNotBlank(inputLine) ? inputLine : "未查询到相关的交易凭证");
			}
			bis = new BufferedInputStream(connection.getInputStream());

			// 保存到本地
			String fileName = req.getCallerFlowId() + ".pdf";
			String localFilePath = localPdfDir + fileName;
			exportStreamToFile(bis, localFilePath);
			long fileSize = new File(localFilePath).length() / 1024;
			log.info("下载司机支付凭证文件到本地，文件本地路径：{}，文件大小：{}KB", localFilePath, fileSize);

			File file = new File(localFilePath); // 下载到本地文件
			String sha256File1 = calculateSHA256(file);
			if (streamList.contains(sha256File1)) {
				file.delete();
				return AjaxResult.success("下载文件到本地成功", -2);
			}
			streamList.add(sha256File1);
			return AjaxResult.success("下载文件到本地成功", fileSize);
		} finally {
			if (null != connection) {
				connection.disconnect();
			}
			if (null != bis) {
				bis.close();
			}
		}
	}

	private String calculateSHA256(File file) {
		FileInputStream fis = null;
		try {
			MessageDigest md = MessageDigest.getInstance("SHA-256");
			// 使用BufferedReader按照字节流读取文件内容
			fis = new FileInputStream(file);
			byte[] byteArray = new byte[1024];
			int bytesCount;
			while ((bytesCount = fis.read(byteArray)) != -1) {
				md.update(byteArray, 0, bytesCount);
			}

			// 计算SHA-256值
			byte[] bytes = md.digest();

			// 转换为十六进制字符串
			StringBuilder sb = new StringBuilder();
			for (byte aByte : bytes) {
				sb.append(Integer.toString((aByte & 0xff) + 0x100, 16).substring(1));
			}
			return sb.toString();
		} catch (Exception e) {
			return "";
		} finally {
			try {
				fis.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * 从流中读取内容写入到文件中
	 *
	 * @param ins
	 * @param fileName
	 * 		完整文件路径（/home/<USER>/xxx.txt）
	 */
	public static void exportStreamToFile(InputStream ins, String fileName) {
		try {
			byte[] data = FileCopyUtils.copyToByteArray(ins);
			File file = new File(fileName); // 下载到本地文件
			if (!file.getParentFile().exists()) {
				file.getParentFile().mkdirs();
				file.createNewFile();
			}
			FileOutputStream fops = new FileOutputStream(file);
			fops.write(data);
			fops.close();
		} catch (IOException e) {
			log.info("InputStream保存到本地失败，本地文件：{}，{}", fileName, ExceptionUtils.getStackTrace(e));
		}
	}

	private void toDoDownload(HttpServletResponse response, DownLoadReq req, String urlPath) throws Exception {
		URL url = new URL(urlPath);
		HttpURLConnection conn = (HttpURLConnection) url.openConnection();
		try {
			// post方式请求
			conn.setRequestMethod("POST");
			// 设置字符编码
			conn.setRequestProperty("Charset", "UTF-8");
			conn.setRequestProperty("Accept-Encoding", "identity"); // 加上这句话解决问题
			conn.setRequestProperty("Content-Type", "application/json");
			// 添加头文件
			conn.setRequestProperty("acc", accountAcc);
			conn.setRequestProperty("pas", accountPas);
			// 传递参数
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.connect();
			DataOutputStream dataOutputStream = new DataOutputStream(conn.getOutputStream());
			dataOutputStream.writeBytes(JSONObject.toJSONString(req));
			dataOutputStream.flush();
			dataOutputStream.close();
			int length = conn.getContentLength();
			log.info("下载文件：" + url + ",fileSize=" + length);
			if (length > 0 && "text/html;charset=UTF-8".equals(conn.getContentType())) {
				String readLine;
				StringBuffer resultData = new StringBuffer();
				BufferedReader responseReader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
				while ((readLine = responseReader.readLine()) != null) {
					resultData.append(readLine);
				}
				log.info("下载文件失败：{}", resultData);
				// throw new ServiceException("下载文件失败：" + resultData);
				response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
				response.setCharacterEncoding(String.valueOf(UTF_8));
				PrintWriter out = response.getWriter();
				ObjectMapper mapper = new ObjectMapper();
				String jsonOfRST = mapper.writeValueAsString(AjaxResult.error("下载文件失败：" + resultData));
				out.print(jsonOfRST);
				out.flush();
				return;
			}
		} catch (Exception e) {
			log.error("下载文件失败--->" + ExceptionUtils.getStackTrace(e), e);
			throw new Exception("下载文件失败");
		}
		try (BufferedInputStream in = new BufferedInputStream(conn.getInputStream()); BufferedOutputStream bos = new BufferedOutputStream(response.getOutputStream())) {
			// 请求银行
			response.reset();
			response.addHeader("Access-Control-Allow-Origin", "*");
			response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
			response.setContentType("application/octet-stream; charset=UTF-8");
			// 匹配文件名
			String contentDisposition = URLDecoder.decode(conn.getHeaderField("content-Disposition"), "UTF-8");
			// 转换编码
			Pattern pattern = Pattern.compile(".*=utf-8''(.*)");
			Matcher matcher = pattern.matcher(contentDisposition);
			String filename = System.currentTimeMillis() + ".pdf";
			if (matcher.matches()) {
				filename = matcher.group(1);
			}
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
			// 将网络输入流转换为输出流
			int i;
			while ((i = in.read()) != -1) {
				bos.write(i);
			}
			response.flushBuffer();
		} catch (Exception e) {
			log.error("下载文件失败--->" + ExceptionUtils.getStackTrace(e), e);
			throw new Exception("下载文件失败");
		}
	}

	private void apiDoDownload(HttpServletResponse response, String shippingNoteNumber, List<String> payCodeList, FreightForwarderInfo freightForwarderInfo, String urlPath) throws Exception {
		// 1. 创建临时目录存储文件
		Path tempDir = Files.createTempDirectory("paycode_download_");
		List<Path> tempFiles = new ArrayList<>();
		Map<String, String> originalFileNames = new HashMap<>(); // 存储原始文件名
		try {
			// 2. 循环下载每个payCode对应的文件
			for (String payCode : payCodeList) {
				// 为每个文件创建独立连接（避免复用连接导致数据错乱）
				URL url = new URL(urlPath);
				HttpURLConnection conn = (HttpURLConnection) url.openConnection();
				try {
					// 连接配置
					conn.setRequestMethod("POST");
					conn.setRequestProperty("Charset", "UTF-8");
					conn.setRequestProperty("Accept-Encoding", "identity");
					conn.setRequestProperty("Content-Type", "application/json");
					conn.setRequestProperty("acc", accountAcc);
					conn.setRequestProperty("pas", accountPas);
					conn.setDoInput(true);
					conn.setDoOutput(true);
					conn.setUseCaches(false);
					conn.setConnectTimeout(30_000); // 增加超时控制
					conn.setReadTimeout(120_000);
					conn.connect();

					// 根据PN 查询信息,构建请求体
					DownLoadReq req = new DownLoadReq();
					// 查询收款人的证件信息
					req.setSourceType(BusinessConstants.SOURCE_PC_10);
					req.setUserType(BusinessConstants.USER_TYPE_CUSTOMER);
					req.setIdNo(freightForwarderInfo.getCreditCode());
					req.setTranType(3);// 查询的是网络货运的交易流水
					req.setCallerFlowId(payCode);
					req.setIsPay(BusinessConstants.TRANSACTION_PAYMENT);

					// 发送请求
					try (DataOutputStream dos = new DataOutputStream(conn.getOutputStream())) {
						dos.writeBytes(JSONObject.toJSONString(req));
					}

					// 处理响应
					if (conn.getResponseCode() == HttpURLConnection.HTTP_OK) {
						// 解析原始文件名
						// String contentDisposition = URLDecoder.decode(conn.getHeaderField("content-Disposition"), "UTF-8");
						String contentDisposition = conn.getHeaderField("content-Disposition");
						String originalName = extractFileName(contentDisposition, payCode);

						// 创建临时文件
						Path tempFile = tempDir.resolve(UUID.randomUUID() + ".tmp");
						try (InputStream in = conn.getInputStream(); OutputStream out = Files.newOutputStream(tempFile)) {
							IOUtils.copy(in, out); // 使用Apache Commons IOUtils简化流复制
						}

						tempFiles.add(tempFile);
						originalFileNames.put(tempFile.getFileName().toString(), originalName);
					} else {
						log.error("下载失败: PayCode={}, 状态码={}", payCode, conn.getResponseCode());
						continue;
					}
				} finally {
					conn.disconnect(); // 确保连接关闭
				}
			}

			// 3. 打包为ZIP并输出
			response.reset();
			response.addHeader("Access-Control-Allow-Origin", "*");
			response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
			response.setContentType("application/zip");
			String zipFileName = URLEncoder.encode(shippingNoteNumber + ".zip", "UTF-8");
			response.setHeader("Content-Disposition", "attachment;filename=" + zipFileName);

			try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
				zos.setComment("Generated by system");
				for (Path tempFile : tempFiles) {
					String originalName = originalFileNames.get(tempFile.getFileName().toString());
					// 安全处理：过滤非法字符防止路径穿越[9](@ref)
					String safeName = originalName.replaceAll("[\\\\/:*?\"<>|]", "_");

					try (InputStream fis = Files.newInputStream(tempFile)) {
						ZipEntry entry = new ZipEntry(safeName);
						zos.putNextEntry(entry);
						IOUtils.copy(fis, zos); // 流式写入避免内存溢出
						zos.closeEntry();
					}
				}
			}
		} finally {
			// 4. 清理临时文件
			for (Path file : tempFiles) {
				Files.deleteIfExists(file);
			}
			Files.deleteIfExists(tempDir);
		}
	}

	// 辅助方法：从Content-Disposition提取文件名
	private String extractFileName(String contentDisposition, String defaultName) {
		if (contentDisposition != null && contentDisposition.contains("filename=")) {
			try {
				// 匹配形如：attachment;filename="example.pdf"
				Pattern pattern = Pattern.compile(".*filename\\*?=([^;]+)");
				Matcher matcher = pattern.matcher(contentDisposition);
				if (matcher.find()) {
					String fileNamePart = matcher.group(1);
					// 处理编码文件名（如UTF-8''格式）
					if (fileNamePart.startsWith("UTF-8''")) {
						return URLDecoder.decode(fileNamePart.substring(7), "UTF-8");
					} else if (fileNamePart.startsWith("\"") && fileNamePart.endsWith("\"")) {
						return fileNamePart.substring(1, fileNamePart.length() - 1);
					}
					return fileNamePart;
				}
			} catch (Exception e) {
				log.warn("文件名解析失败: {}", contentDisposition, e);
			}
		}
		return defaultName + ".pdf"; // 默认文件名
	}

	public AjaxResult account(Long customerId, String accountNo) throws Exception {
		// {"code":"1","data":{"transCodeId":"202209081543191031201532","code":"000000","dataBody":{"OthBankPayeeSubAcc":"*****************","OthBankPayeeSubAccSetteName":"华夏银行股份有限公司天宁支行","Remark4":"","Remark5":"","Remark2":"","Remark3":"","Remark1":"9915670166243238562","OthBankPayeeSubAccName":"常州智联云物流科技股份有限公司"},"transCode":"103120","message":"交易成功"}}
		JSONObject jsonObject;
		if (StringUtils.isNotBlank(accountNo)) {
			String str = "{\"code\":\"1\",\"data\":{\"transCodeId\":\"\",\"code\":\"000000\",\"dataBody\":{\"OthBankPayeeSubAcc\":\"*****************\",\"OthBankPayeeSubAccSetteName\":\"华夏银行股份有限公司天宁支行\",\"Remark4\":\"\",\"Remark5\":\"\",\"Remark2\":\"\",\"Remark3\":\"\",\"Remark1\":\"" + accountNo + "\",\"OthBankPayeeSubAccName\":\"常州智联云数字科技股份有限公司\"},\"transCode\":\"103120\",\"message\":\"交易成功\"}}";
			jsonObject = JSONObject.parseObject(str);
			return AjaxResult.success(jsonObject);
		} else {
			// （1.集团端 2.网络货运人端 3.托运人端 4.承运人端）
			CustomerInfo customerInfo = customerInfoService.selectCustomerInfoById(customerId);
			return this.account(customerInfo.getCreditCode(), BusinessConstants.USER_TYPE_CUSTOMER, BusinessConstants.SOURCE_PC_10);
		}
	}

	/**
	 * 查询充值账户
	 *
	 * @param creditCode
	 * @param userType
	 * @param sourceType
	 * @return
	 * @throws Exception
	 */
	public AjaxResult account(String creditCode, Integer userType, Integer sourceType) throws Exception {
		JSONObject object = new JSONObject();
		object.put("userType", userType); // 用户类型（1：商户，7：司机，10：保险经纪人）
		object.put("sourceType", sourceType); // 客户来源类型(10：PC，20：小程序端)
		object.put("idNo", creditCode); // 用户证件号（分公司营业执照或收款人身份证号）
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(userInfoAndCardUrl, object);
		log.info("请求银行，查询账号结果：{}", JSONObject.toJSONString(result));
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("查询账号结果--->" + result.getMsg());
			throw new RuntimeException("查询账号结果：" + result.getMsg());
		}
		JSONObject jsonObject = JSON.parseObject(JSONObject.toJSONString(result.getData()));
		return AjaxResult.success(jsonObject);
	}

	public AjaxResult accountOpen(Long customerId, String accountNo) throws Exception {
		AjaxResult res = this.account(customerId, accountNo);
		JSONObject jsonObject = JSON.parseObject(JSONObject.toJSONString(res.getData()));
		jsonObject.put("loginUrl", customConfig.getLoginUrl());
		CustomerAdminConfigRes cusRes = customerInfoService.adminInfo(customerId);
		if (com.zly.common.utils.StringUtils.isNotNull(cusRes)) {
			jsonObject.put("admin", cusRes.getPhoneNumber());
			jsonObject.put("password", UserConstants.INITIAL_PASSWORD);// 默认初始化密码
		}
		return AjaxResult.success(jsonObject);
	}

	/**
	 * 支付服务费
	 *
	 * @param customerInfo
	 * @param contract
	 * @param waybillCode
	 * @param money
	 * @return
	 */
	public AjaxResult servicesPay(CustomerInfo customerInfo, FrameworkContract contract, String waybillCode, String money) {
		if (null == customerInfo) {
			throw new RuntimeException("支付服务费失败，商户信息为空");
		}
		if (StringUtils.isBlank(customerInfo.getCreditCode())) {
			throw new RuntimeException("支付服务费失败，商户证件信息为空");
		}
		if (null == contract) {
			throw new RuntimeException("支付服务费失败，项目合约信息为空");
		}
		if (null == contract.getId()) {
			throw new RuntimeException("支付服务费失败，项目合约ID为空");
		}
		if (StringUtils.isBlank(waybillCode)) {
			throw new RuntimeException("支付服务费失败，运单编号为空");
		}
		if (StringUtils.isBlank(money)) {
			throw new RuntimeException("支付服务费失败，支付金额为空");
		}
		if (!TextUtil.isGtZero(money)) {
			throw new RuntimeException("支付服务费失败，支付金额格式错误");
		}
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("sourceType", BusinessConstants.SOURCE_PC_10);
		jsonObject.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		// 证件号
		jsonObject.put("idNo", customerInfo.getCreditCode());
		jsonObject.put("callerWaybillCode", waybillCode);
		jsonObject.put("callerProjectId", contract.getId());
		jsonObject.put("money", money);
		// 164180213466600001 -- 人保物责险
		// 164180221745800001 -- 人保货运险
		// 164180227110800001 -- 货运险-龙琨
		List<ContractServiceInfo> contractList = iContractServiceInfoService.selectContractServiceInfoByContractId(contract.getId());
		String serviceType = null;
		for (ContractServiceInfo con : contractList) {
			// 查询保险类型
			if (SystemService.Policy.PICCWZX.equals(con.getServiceId())) {
				serviceType = "10";
				break;
			} else if (SystemService.Policy.PICCHYX.equals(con.getServiceId())) {
				serviceType = "12";
				break;
			} else if (SystemService.Policy.LKWZX.equals(con.getServiceId())) {
				serviceType = "13";
				break;
			}
		}
		if (StringUtils.isBlank(serviceType)) {
			throw new RuntimeException("支付服务费失败，服务类型为空");
		}
		jsonObject.put("userType", serviceType);// 用户类型（10：责任险 12：货运险 13: 龙琨服务商）
		log.info("----  查询模块--支付服务费用 ----");
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(servicePayUrl, jsonObject);
		log.info("请求银行，支付服务费用：{}", JSONObject.toJSONString(result));
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("支付服务费用--->" + result.getMsg());
			throw new RuntimeException("支付服务费用：" + result.getMsg());
		}
		return result;
	}

	/**
	 * 支付运输费（管理费）
	 *
	 * @param customerInfo
	 * @param contract
	 * @param forwarderInfo
	 * @param statementNo
	 * @param money
	 * @return
	 */
	public AjaxResult feePay(CustomerInfo customerInfo, FrameworkContract contract, FreightForwarderInfo forwarderInfo, String statementNo, String money, String password) {
		if (null == customerInfo) {
			throw new RuntimeException("支付运输费失败，商户信息为空");
		}
		if (StringUtils.isBlank(customerInfo.getCreditCode())) {
			throw new RuntimeException("支付运输费失败，商户证件信息为空");
		}
		if (null == contract) {
			throw new RuntimeException("支付运输费失败，项目合约信息为空");
		}
		if (null == contract.getId()) {
			throw new RuntimeException("支付运输费失败，项目合约ID为空");
		}
		if (null == forwarderInfo) {
			throw new RuntimeException("支付运输费失败，网络货运平台为空");
		}
		if (null == forwarderInfo.getCreditCode()) {
			throw new RuntimeException("支付运输费失败，网络货运平台证件号为空");
		}
		if (StringUtils.isBlank(statementNo)) {
			throw new RuntimeException("支付运输费失败，申请开票编号为空");
		}
		if (StringUtils.isBlank(money)) {
			throw new RuntimeException("支付运输费失败，支付金额为空");
		}
		if (!TextUtil.isGtZero(money)) {
			throw new RuntimeException("支付运输费失败，支付金额格式错误");
		}
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("sourceType", BusinessConstants.SOURCE_PC_10);
		jsonObject.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		// 证件号
		jsonObject.put("idNo", customerInfo.getCreditCode());
		jsonObject.put("statementNo", statementNo);
		jsonObject.put("targetIdNo", forwarderInfo.getCreditCode());
		jsonObject.put("callerProjectId", contract.getId());
		jsonObject.put("money", money);
		jsonObject.put("password", password);
		log.info("----  查询模块--支付运输费 ----");
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(feePayUrl, jsonObject);
		log.info("请求银行，支付运输费：{}", JSONObject.toJSONString(result));
		/*if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			throw new RuntimeException("支付运输费失败：" + result.getMsg());
		}*/
		return result;
	}

	/**
	 * 支付运输费退回（管理费）
	 *
	 * @param statementNo
	 * @return
	 */
	public AjaxResult feePayReturn(CustomerInfo customerInfo, String statementNo) {
		if (null == customerInfo) {
			throw new RuntimeException("支付运输费退回失败，商户信息为空");
		}
		if (StringUtils.isBlank(customerInfo.getCreditCode())) {
			throw new RuntimeException("支付运输费退回失败，商户证件信息为空");
		}

		JSONObject jsonObject = new JSONObject();
		jsonObject.put("sourceType", BusinessConstants.SOURCE_PC_10);
		jsonObject.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		// 证件号
		jsonObject.put("idNo", customerInfo.getCreditCode());
		jsonObject.put("statementNo", statementNo);
		jsonObject.put("password", "f8adc787cf0f01aae9327a6286dd41ae");
		log.info("----  查询模块--运输费退回 ----");
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(feePayReturnUrl, jsonObject);
		log.info("请求银行，支付运输费：{}", JSONObject.toJSONString(result));
		/*if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("支付运输费退回失败--->" + result.getMsg());
			throw new RuntimeException("支付运输费退回失败：" + result.getMsg());
		}*/
		return result;
	}

	/**
	 * 查询运输费结果（管理费）
	 *
	 * @param statementNo
	 * @return
	 */
	public AjaxResult queryFeePayResult(CustomerInfo customerInfo, String statementNo, Boolean isReturn) {
		if (null == customerInfo) {
			return AjaxResult.error("查询运输费结果，商户信息为空");
		}
		if (StringUtils.isBlank(customerInfo.getCreditCode())) {
			return AjaxResult.error("查询运输费结果，商户证件信息为空");
		}
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("sourceType", BusinessConstants.SOURCE_PC_10);
		jsonObject.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
		// 证件号
		jsonObject.put("idNo", customerInfo.getCreditCode());
		jsonObject.put("statementNo", statementNo);
		jsonObject.put("isReturn", isReturn);
		log.info("----  查询运输费支付结果--运输费结果 ----");
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(getFeePayResultUrl, jsonObject);
		log.info("请求银行，查询运输费支付结果：{}", JSONObject.toJSONString(result));
		/*if (result.isNotSuccess()) {
			log.info("查询运输费支付结果失败--->" + result.getMsg());
			throw new RuntimeException("查询运输费支付结果失败：" + result.getMsg());
		}*/
		return result;
	}

	/**
	 * 支付的接口，需要循环组装
	 *
	 * @param waybillList
	 * @param customerTradeApplyList
	 * @param payeeInfoList
	 * @param password
	 * @param isMerge
	 * @return
	 */
	public AjaxResult batchTrade(List<CustomerInfo> customerInfoList, List<Waybill> waybillList, List<CustomerTradeApply> customerTradeApplyList, List<PayeeInfo> payeeInfoList,
			List<String> tradeObjectList, List<Integer> prepayerList, String password, Integer isMerge) {
		if (waybillList == null || customerTradeApplyList == null || payeeInfoList == null) {
			throw new RuntimeException("请传入支付信息");
		}
		if (customerTradeApplyList.size() == 0) {
			throw new RuntimeException("请传入支付信息");
		}
		if (customerTradeApplyList.size() != waybillList.size() || customerTradeApplyList.size() != payeeInfoList.size()) {
			throw new RuntimeException("支付信息不对等");
		}
		JSONArray jsonArray = new JSONArray();
		for (int i = 0; i < customerTradeApplyList.size(); i++) {
			Waybill waybill = waybillList.get(i);
			CustomerTradeApply apply = customerTradeApplyList.get(i);
			PayeeInfo payeeInfo = payeeInfoList.get(i);
			JSONObject sendMap = new JSONObject();
			// 收款人的银行卡
			sendMap.put("sourceType", BusinessConstants.SOURCE_PC_10);
			sendMap.put("userType", BusinessConstants.USER_TYPE_CUSTOMER);
			sendMap.put("idNo", customerInfoList.get(i).getCreditCode()); // 证件号
			sendMap.put("collectBankNo", payeeInfo.getBankCardNo());
			sendMap.put("callerFlowId", apply.getApplyPayCode());// 存放申请代码
			sendMap.put("callerPayItemId", CodeUtil.generateToPayCode());// 每次请求的流水，账户体系那边没用到。
			sendMap.put("callerWaybillCode", waybill.getShippingNoteNumber());
			sendMap.put("callerProjectId", waybill.getFrameworkContractId());
			sendMap.put("callerProjectName", waybill.getFrameworkContractName());
			// sendMap.put("bankId", bankId);
			sendMap.put("tradeObject", tradeObjectList.get(i));
			sendMap.put("money", apply.getApplyMoney());
			sendMap.put("tranSummary", "");
			sendMap.put("isInstead", prepayerList.get(i));
			sendMap.put("isDriver", 1);
			sendMap.put("interestRate", 1);// 垫资费率，目前没涉及，先存1
			sendMap.put("password", password);
			sendMap.put("tranType", 3); // 3：运费支付
			// 收款方来源平台（10:iTMS PC、20：iTMS.0移动端（车联天下）） 目前只有10的。
			// 打脸了呀，现在有小程序提现了。 2023-04-04
			// 现在还有亲属收款了呢 2024-04-17
			if (WaybillSource.isMobile(waybill.getResource())) {
				// 1:司机, 2:车队长, 3:辅助员 4亲属
				sendMap.put("payeeSourceType", waybill.getPayeeType() != null && 4 == waybill.getPayeeType() ? BusinessConstants.SOURCE_FAMILY_30 : BusinessConstants.SOURCE_APP_20);
				// isAuto: 是否自动提现 -1:手动提现 1:自动提现 2:合并提现
				sendMap.put("isAuto", String.valueOf(-1));
			} else {
				sendMap.put("payeeSourceType", BusinessConstants.SOURCE_PC_10);
				// isAuto: 是否自动提现 -1:手动提现 1:自动提现 2:合并提现
				sendMap.put("isAuto", String.valueOf(isMerge));
			}
			jsonArray.add(sendMap);
		}
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(batchTradeUrl, jsonArray);
		log.info("请求银行，支付运费：{}", JSONObject.toJSONString(result));
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("支付运费--->" + result.getMsg());
			throw new RuntimeException("支付运费失败：" + result.getMsg());
		}
		return result;
	}

	/**
	 * 司机余额转出，添加sourceType，兼容亲属收款
	 *
	 * @param creditCode
	 * @param password
	 * @param money
	 * @param operator
	 * @param sourceType
	 * 		来源，10：pc，20：app 30 亲属，亲属的时候要传，其他的时候会根据客户端判断
	 * @return
	 */
	public CommonResult<String> withdraw(String creditCode, String collectBankNo, String password, String money, String operator, Integer sourceType, String tranSummary) {
		return this.withdraw(creditCode, collectBankNo, password, money, operator, sourceType, tranSummary, BusinessConstants.USER_TYPE_PAYEE);
	}

	/**
	 * 商户未分配余额转出
	 *
	 * @param creditCode
	 * @param password
	 * @param money
	 * @param operator
	 * @return
	 */
	public CommonResult<String> customerWithdraw(String creditCode, String collectBankNo, String password, String money, String operator, String tranSummary) {
		return this.withdraw(creditCode, collectBankNo, password, money, operator, BusinessConstants.SOURCE_PC_10, tranSummary, BusinessConstants.USER_TYPE_CUSTOMER);
	}

	/**
	 * 余额转出，添加sourceType，兼容亲属收款
	 *
	 * @param creditCode
	 * @param password
	 * @param money
	 * @param operator
	 * @param sourceType
	 * 		来源，10：pc，20：app 30 亲属，亲属的时候要传，其他的时候会根据客户端判断
	 * @return
	 */
	public CommonResult<String> withdraw(String creditCode, String collectBankNo, String password, String money, String operator, Integer sourceType, String tranSummary, Integer userType) {
		if (TextUtil.isNull(creditCode)) {
			return CommonResult.error("转出信息为空");
		}
		if (TextUtil.isNull(money)) {
			return CommonResult.error("转出金额为空");
		}
		if (TextUtil.isNull(collectBankNo)) {
			return CommonResult.error("转出银行账户为空");
		}
		if (!TextUtil.isGtZero(money)) {
			return CommonResult.error("转出金额格式不正确");
		}
		if (TextUtil.isNull(password)) {
			return CommonResult.error("资金管理密码为空");
		}
		if (TextUtil.isNull(operator)) {
			return CommonResult.error("操作人为空");

		}
		// 判断当前商户类型是否为合资公司
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("callerFlowId", CodeUtil.generateWithdrawCode());
		jsonObject.put("sysMoney", "0");
		jsonObject.put("custMoney", "0");
		jsonObject.put("otherMoney", "0");
		jsonObject.put("tranType", "2");
		jsonObject.put("money", money);
		jsonObject.put("creator", operator);
		jsonObject.put("tranSummary", StringUtils.isNotBlank(tranSummary) ? tranSummary : "承运人金额转出");
		// MD5加密一次后的。
		jsonObject.put("password", password);
		// 出金的银行卡号
		jsonObject.put("collectBankNo", collectBankNo);
		// 客户来源类型(10：PC，20：小程序端 30: 亲属户)
		if (null != sourceType && 0 != sourceType.intValue()) {
			jsonObject.put("sourceType", sourceType);
		} else {
			jsonObject.put("sourceType", BusinessConstants.SOURCE_PC_10);
			if (ClientType.CARRIER.equals(SecurityUtils.getClientType())) {
				jsonObject.put("sourceType", BusinessConstants.SOURCE_APP_20);
			}
		}
		jsonObject.put("userType", userType);
		jsonObject.put("idNo", creditCode); // 证件号
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(rechargeWithdrawUrl, jsonObject);
		log.info("请求银行，金额转出：{}", JSONObject.toJSONString(result));
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("金额转出--->" + result.getMsg());
			throw new RuntimeException("金额转出失败：" + result.getMsg());
		}
		return CommonResult.result(result);
	}

	/**
	 * 接受支付消息，并处理
	 *
	 * @param message
	 */
	@RabbitListener(queues = "${environment.config.mq.paymentMqName}")
	@Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
	public void receiveAccountPay(String message) {
		String value = String.valueOf(UUID.randomUUID());
		// 添加日志打印唯一标识
		MDC.put("logId", value);
		log.info("MQ-->收到支付消息 ：" + message);
		String key = MD5.create().digestHex(message);
		try {
			// 判断是否重复请求
			if (!redisCache.lock(key, value, CacheConstants.REDIS_TIME_OUT)) {
				log.info("MQ-->收到支付消息重复通知，" + message);
				return;
			}
			SpringUtils.getBean(HuaXiaService.class).doReceivePay(message);
		} catch (Exception e) {
			log.error("MQ-->支付消息数据处理异常--->" + ExceptionUtils.getStackTrace(e), e);
		} finally {
			redisCache.unlock(key, value);
		}
	}

	/**
	 * 根据消息处理支付
	 *
	 * @param message
	 */
	@Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
	public void doReceivePay(String message) {
		try {
			// {"code":"1","data":{"payCode":"*********************","payState":"1","remark1":"*********************","remark2":""},"message":"支付成功"}
			if (StringUtils.isBlank(message)) {
				log.info("MQ-->收到支付消息为空，{}", message);
				return;
			}
			if (!JSON.isValidObject(message)) {
				log.info("MQ-->没有收到符合规范的数据，{}", message);
				return;
			}
			JSONObject messageJson = JSONObject.parseObject(message);
			if (!messageJson.containsKey("data")) {
				log.info("MQ-->没有收到符合规范的数据，{}", message);
				return;
			}
			PDSNoticeRes res = messageJson.getObject("data", PDSNoticeRes.class);
			// 根据payCode 查询申请单号
			if (StringUtils.isBlank(res.getPayCode())) {
				log.info("MQ-->申请单号不存在，{}", message);
				return;
			}
			CustomerTradeApply apply = new CustomerTradeApply();
			apply.setApplyPayCode(res.getPayCode());
			List<CustomerTradeApply> customerTradeApplyList = customerTradeApplyService.selectCustomerTradeApplyList(apply);
			if (customerTradeApplyList.size() != 1) {
				log.info("MQ-->申请单号不存在，{}", message);
				return;
			}
			apply = customerTradeApplyList.get(0);
			// 不是付款中过滤掉
			if (BusinessConstants.WAYBILL_PAY_AUDIT_STATUS_PAYING != apply.getAuditStatus()) {
				log.info("MQ-->申请单号状态不正确，{}", message);
				return;
			}
			Waybill waybill = waybillService.selectWaybillById(apply.getWaybillId());
			if (waybill == null) {
				log.info("MQ-->运单不存在，运单的id:{}", apply.getWaybillId());
				return;
			}
			FrameworkContract contract = frameworkContractService.selectFrameworkContractById(waybill.getFrameworkContractId());
			if (null == contract) {
				log.info("MQ-->项目合约不存在，项目合约的id：{}", waybill.getFrameworkContractId());
				return;
			}
			Waybill waybillUpdate = new Waybill();
			waybillUpdate.setId(waybill.getId());
			// 如果付款成功的话
			if (String.valueOf(BusinessConstants.TRADE_STATE_SUCCESS).equals(res.getPayState())) {
				// 修改运单的的每阶段的金额
				// 更新已付未付
				switch (apply.getFareStage()) {
				case 1:
					waybill.setPrepayActualMoney(waybill.getPrepayActualMoney().add(apply.getApplyMoney()));
					waybill.setPrepayUnpaidMoney(waybill.getPrepayUnpaidMoney().subtract(apply.getApplyMoney()));
					waybillUpdate.setPrepayActualMoney(waybill.getPrepayActualMoney());
					waybillUpdate.setPrepayUnpaidMoney(waybill.getPrepayUnpaidMoney());
					break;
				case 2:
					waybill.setArriveActualMoney(waybill.getArriveActualMoney().add(apply.getApplyMoney()));
					waybill.setArriveUnpaidMoney(waybill.getArriveUnpaidMoney().subtract(apply.getApplyMoney()));
					waybillUpdate.setArriveActualMoney(waybill.getArriveActualMoney());
					waybillUpdate.setArriveUnpaidMoney(waybill.getArriveUnpaidMoney());
					break;
				case 3:
					waybill.setReceiptActualMoney(waybill.getReceiptActualMoney().add(apply.getApplyMoney()));
					waybill.setReceiptUnpaidMoney(waybill.getReceiptUnpaidMoney().subtract(apply.getApplyMoney()));
					waybillUpdate.setReceiptActualMoney(waybill.getReceiptActualMoney());
					waybillUpdate.setReceiptUnpaidMoney(waybill.getReceiptUnpaidMoney());
					break;
				}
				// 修改已付未付的。
				waybill.setUnpaidFare(waybill.getUnpaidFare().subtract(apply.getApplyMoney()));
				waybill.setActualFare(waybill.getActualFare().add(apply.getApplyMoney()));
				waybillUpdate.setUnpaidFare(waybill.getUnpaidFare());
				waybillUpdate.setActualFare(waybill.getActualFare());
				Integer waybillState = BusinessConstants.WAYBILL_INVOICE_STATE_CAN_NOT;
				if (waybill.getUnpaidFare().compareTo(BigDecimal.ZERO) == 0) {
					// 计算应收=应付/（1-费率）
					// waybill.setReceiveFare(waybill.getPayFare().divide(BigDecimal.ONE.subtract(contract.getRate()), 2, BigDecimal.ROUND_HALF_DOWN));
					// 如果剩余未付金额==0，说明已经全部付完。
					waybill.setSettleStatus(BusinessConstants.WAYBILL_SETTLE_STATUS_COMPLETED);
					waybill.setPayApplyStatus(BusinessConstants.WAYBILL_PAY_APPLY_STATUS_FINISHED);// 付款申请状态设为已完结
					// 查询运单托运人关联表，更新托运人运单关联表
					WaybillCustomerRelation waybillCustomerRelation = new WaybillCustomerRelation();
					waybillCustomerRelation.setWaybillId(waybill.getId());
					List<WaybillCustomerRelation> waybillCustomerRelationList = waybillCustomerRelationMapper.selectWaybillCustomerRelationList(waybillCustomerRelation);
					for (WaybillCustomerRelation w : waybillCustomerRelationList) {
						// 当发票状态低于可申请开票时，逐个更新状态为可申请
						if (w.getWaybillState() < BusinessConstants.WAYBILL_INVOICE_STATE_CAN) {
							waybillState = BusinessConstants.WAYBILL_INVOICE_STATE_CAN;
							WaybillCustomerRelation updateW = new WaybillCustomerRelation();
							updateW.setWaybillState(waybillState);
							updateW.setId(w.getId());
							updateW.setCustomerId(w.getCustomerId());
							updateW.setWaybillId(w.getWaybillId());
							int result = waybillCustomerRelationMapper.updateWaybillCustomerRelation(updateW);
							if (result <= 0) {
								log.info("MQ-->更新运单状态失败，运单的id:{}", waybill.getId());
								throw new RuntimeException("更新运单状态失败");
							}
						}

					}
					// 卸货到达,运费提醒
					// 司机承运人一致,给司机发
					if (waybill.getResource() == 6) {
						Long userId = carrierUserService.selectCarrierIdUserByActualId(waybill.getActualCarrierId());
						carrierMessageInfoService.sendMsg(waybill.getId(), userId, 5, "", 0);
					}
				} else if (waybill.getUnpaidFare().compareTo(BigDecimal.ZERO) > 0) {
					// 如果剩余未付金额！=0，说明部分结算。
					if (waybill.getPrepayType() == 2 && waybill.getPrepayMoney().compareTo(waybill.getUnpaidFare()) == 0) {
						// 如果预付方式是油卡，且剩余未付金额为预付金额，则运单结算状态改为已结算，付款申请状态改为已完结
						waybill.setSettleStatus(BusinessConstants.WAYBILL_SETTLE_STATUS_COMPLETED);
						waybill.setPayApplyStatus(BusinessConstants.WAYBILL_PAY_APPLY_STATUS_FINISHED);// 付款申请状态设为已完结
					} else {
						waybill.setSettleStatus(BusinessConstants.WAYBILL_SETTLE_STATUS_PORTION);
						waybill.setPayApplyStatus(BusinessConstants.WAYBILL_PAY_APPLY_STATUS_NOTAPPLY);// 付款申请状态设为待申请，以便可以下一次申请
					}
				} else {
					log.info("MQ-->剩余未付金额不正确，运单的ID:{}，", waybill.getId());
					throw new RuntimeException("剩余未付金额不正确，请确认");
				}
				// 生成合同
				// new Thread(() -> {
				// if (waybill.getResource() != 6 && waybill.getElectronicContractState() ==0){
				// iWaybillService.makeElectronicContract(waybill.getId());
				// }
				// }).run();
				waybill.setPayTime(DateUtils.getNowSqlDate());
				waybillUpdate.setPayTime(waybill.getPayTime());
				waybillUpdate.setSettleStatus(waybill.getSettleStatus());
				waybillUpdate.setPayApplyStatus(waybill.getPayApplyStatus());
				// waybill.setElectronicContractState(2);
				esWaybillService.updateEsInfo(waybill, waybillState);
				// if (result <= 0) {
				// throw new ServiceException("更新es的运单出错啦");
				// }
				int result = iWaybillService.updateWaybill(waybillUpdate);
				if (result <= 0) {
					throw new ServiceException("更新数据库的运单出错啦");
				}
				// 需要收款限额中的已付金额
				checkPayeePaymentRecord(apply, waybill);
			}
			// 修改申请表的支付状态。
			CustomerTradeApply customerTradeApply = new CustomerTradeApply();
			customerTradeApply.setId(apply.getId());
			customerTradeApply.setPayTime(DateUtils.getNowSqlDate());
			customerTradeApply.setAuditStatus(String.valueOf(BusinessConstants.TRADE_STATE_SUCCESS).equals(res.getPayState()) ?
					BusinessConstants.WAYBILL_PAY_AUDIT_STATUS_PAID :
					BusinessConstants.WAYBILL_PAY_AUDIT_STATUS_FAIL);
			customerTradeApply.setRemark(JSONObject.parseObject(message).getString("message"));
			customerTradeApplyService.updateCustomerTradeApply(customerTradeApply);
			log.info("MQ-->支付消息数据处理完成.消息：{}，", message);
			// 如果支付状态是失败的，更新失败的逻辑

			// 如果是华夏融资的项目运单，需要推送支付结果到华夏银行。
			AsyncManager.me().execute(new TimerTask() {
				@SneakyThrows
				@Override
				public void run() {
					SpringUtils.getBean(HuaXiaFinancingService.class).riskManagePushDriverCollectInfo(waybill);
				}
			});
			// 如果是需要税务上报的运单，在费用全部支付完成后，进行支付上报系统内校验
			if (waybill.getSettleStatus() == BusinessConstants.WAYBILL_SETTLE_STATUS_COMPLETED && waybill.getIsTaxUpload() == 0) {
				log.info("付款完成后变为已结算，开始进行税务上报校验.运单ID：{}，", waybill.getId());
				if (!waybill.getActualCarrierIdentityCard().equals(waybill.getDrivingLicense())) {
					Waybill updateWaybill = new Waybill();
					updateWaybill.setId(waybill.getId());
					updateWaybill.setTaxSecondUploadState(TaxUploadConstants.UPLOAD_INTERSEPT);
					updateWaybill.setTaxSecondResState(TaxUploadConstants.TAX_RES_ABNORMAL);
					updateWaybill.setTaxUploadVerifyLastTime(DateUtils.getNowDate());
					updateWaybill.setTaxUploadFailReason(ywkUploadService.addFailReason(waybill.getTaxUploadFailReason(), "ZLYG01"));
					waybillService.updateWaybill(updateWaybill);
					log.info("付款完成后变为已结算，税务上报校验结束.运单ID：{}，", waybill.getId());
				}
			}

			if (waybill.getSettleStatus() == BusinessConstants.WAYBILL_SETTLE_STATUS_COMPLETED) {
				// 校验风控信息
				waybillMapperEx.updateRiskResultGradeByIds(Collections.singletonList(waybill.getId()));
				waybillRiskService.waybillsRiskCheckByIds(Collections.singletonList(waybill.getId()), SecurityUtils.getNickname());
			}
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			throw e;
		}

	}

	/**
	 * 更新收款限额
	 *
	 * @param apply
	 * @param waybill
	 */
	private void checkPayeePaymentRecord(CustomerTradeApply apply, Waybill waybill) {
		PaymentRecord paymentRecord = new PaymentRecord();
		paymentRecord.setActualFare(apply.getApplyMoney());
		paymentRecord.setFreightForwarderId(waybill.getFreightForwarderId());
		paymentRecord.setPaymentYear(DateUtils.getYear(new Date()));
		paymentRecord.setIdentityCard(waybill.getPayeeIdentityCard());
		paymentRecordService.modifyPaymentSuccess(paymentRecord);
		// 如果是亲属收款的，更新司机自己的收款限额
		if (waybill.getPayeeType() == 4) {
			paymentRecord = new PaymentRecord();
			paymentRecord.setActualFare(apply.getApplyMoney());
			paymentRecord.setFreightForwarderId(waybill.getFreightForwarderId());
			paymentRecord.setPaymentYear(DateUtils.getYear(new Date()));
			paymentRecord.setIdentityCard(waybill.getDrivingLicense());
			paymentRecordService.modifyPaymentSuccess(paymentRecord);
		}
	}

	/**
	 * 华夏的一套
	 */
	@Value("${platform.pay.task.downloadUrl}")
	private String downloadUrl;
	@Value("${platform.pay.account.header.acc}")
	private String accountAcc;
	@Value("${platform.pay.account.header.pas}")
	private String accountPas;
	@Value("${platform.pay.pds.bankCode}")
	private String bankCode;
	@Value("${platform.pay.pds.bankName}")
	private String bankName;
	/*@Value("${ali.platform.contact.identity}")
	private String platformCardNo;// 平台证号
	@Value("${platform.payment.channel}")
	private String paymentChannel;// 平台支付通道*/

	@Autowired
	private IContractServiceInfoService iContractServiceInfoService;
	@Autowired
	private IHomeServiceService iHomeServiceService;
	@Autowired
	private CheckTheParametersService checkTheParametersService;
	@Autowired
	private ICustomerAccountService iCustomerAccountService;
	@Autowired
	private ICustomerTradeFlowService iCustomerTradeFlowService;
	@Autowired
	private RollbackService rollbackService;
	@Autowired
	private ICustomerBankFlowService iCustomerBankFlowService;
	@Autowired
	private HuaXiaHttpUtil huaXiaHttpUtil;
	@Autowired
	private ICustomerInfoService customerInfoService;
	@Autowired
	private HXPDSData hxpdsData;
	@Autowired
	private RedisCache redisCache;
	@Autowired
	private IWaybillService iWaybillService;
	@Autowired
	private IPayeeInfoService iPayeeInfoService;
	@Autowired
	private MQNoticeService mqNoticeService;
	@Autowired
	private ICustomerBankCardService customerBankCardService;
	@Autowired
	private CustomConfig customConfig;
	@Autowired
	private ISysUserService iSysUserService;
	@Autowired
	private ICarrierUserService ICarrierUserService;
	@Autowired
	private IFreightForwarderInfoService iFreightForwarderInfoService;
	@Autowired
	private PayeeInfoMapper payeeInfoMapper;
	@Autowired
	private IWaybillService waybillService;

	@Autowired
	private ICustomerTradeApplyService customerTradeApplyService;

	@Autowired
	private IFrameworkContractService frameworkContractService;

	@Resource
	private WaybillCustomerRelationMapper waybillCustomerRelationMapper;

	@Resource
	private EsWaybillService esWaybillService;

	@Autowired
	private UserTradeFlowToCustomerTradeFlowVoMapStruct userTradeFlowToCustomerTradeFlowVoMapStruct;

	/**
	 * 查询账户（总余额，项目余额，资金垫付，现金账户，子帐户等）
	 */
	private static final String accountMoneyUrl = "account/accMoney";
	/**
	 * 查询账户（总余额，项目余额，资金垫付，现金账户，子帐户等,可单独查询某个账户）
	 */
	private static final String accountMoneyOtherUrl = "account/accountMoney";
	/**
	 * 查询项目子账户余额列表
	 **/
	private static final String subAccountUrl = "account/subAccount";
	/**
	 * 查询项目上缴，分配明细
	 */
	private static final String transferFlowListUrl = "transfer/transferFlowListV2";
	/**
	 * 查询付款明细
	 */
	private static final String tradeFlowListUrl = "trade/tradeFlowList";

	/**
	 * 查询付款明细
	 */
	private static final String tradeFlowListUrlByWaybillCode = "trade/tradeFlowListByWaybillCode";
	/**
	 * 查询服务费付款明细
	 */
	private static final String servicesFlowListUrl = "services/servicesFlowList";
	/**
	 * 查询服务统计
	 */
	private static final String servicesSumListUrl = "services/servicesSumListV2";
	/**
	 * 查询账户流水
	 */
	private static final String accountFlowListUrl = "account/accountFlowListV2";
	/**
	 * 查询流水合计
	 */
	private static final String sumFlowUrl = "account/sumFlowMoney";
	/**
	 * 查询项目垫付明细列表
	 */
	private static final String insteadFlowListUrl = "instead/insteadFlowList";
	/**
	 * 查询充值提现明细列表
	 */
	private static final String rechargeWithdrawListUrl = "account/rechargeWithdrawList";
	/**
	 * 查询充值提现明细列表第2版本，查询集合的流水，上限返回100条。默认15条
	 */
	private static final String rechargeWithdrawListV2Url = "account/rechargeWithdrawListV2";
	/**
	 * 项目资金上拨、下划
	 */
	private static final String transferPaymentUrl = "transfer/transferPayment";
	/**
	 * 用户模块--用户建立账户
	 */
	private static final String openAccountUrl = "userInfo/openAccount";
	/**
	 * 用户模块--项目子帐户开户（按项目创建）
	 */
	private static final String subAccountOpenUrl = "userInfo/subAccountOpen";
	/**
	 * 用户模块--修改项目子帐户信息（项目名称，项目摘要，项目负责人手机）
	 */
	private static final String updateSubAccountUrl = "userInfo/modifySubAccount";

	private static final String delSubAccountUrl = "userInfo/delSubAccount";
	/**
	 * 查询模块---查询充值账户
	 */
	private static final String userInfoAndCardUrl = "account/rechargeAccount";
	/**
	 * 查询模块--查询用户和银行卡信息
	 */
	private static final String queryUserInfoAndCardUrl = "userInfo/queryUserInfoAndCard";
	/**
	 * 用户模块--用户绑定银行结算卡号
	 */
	private static final String bindBankCardUrl = "userInfo/bindBankCard";

	/**
	 * 用户模块--用户绑定银行结算卡号
	 */
	private static final String bindBankCardUrlV2 = "userInfo/bindBankCardV2";

	/**
	 * 用户模块--查询用户银行卡接口
	 */
	private static final String queryBankCardState = "userInfo/queryBankCardState";

	/**
	 * 交易模块--账户提现（可用资金账户）
	 */
	private static final String rechargeWithdrawUrl = "account/rechargeWithdraw";
	/**
	 * 是否已设置密码
	 */
	private static final String isPasswordSettedUrl = "userInfo/isPasswordSet";
	/**
	 * 设置密码
	 */
	private static final String passwordChangeUrl = "userInfo/modifyPayPass";
	/**
	 * 司机密码复制给亲属账户url
	 */
	private static final String copyPayPassUrl = "userInfo/copyPayPass";
	/**
	 * 清空密码
	 */
	private static final String cleanPayPassUrl = "userInfo/cleanPayPass";
	/**
	 * 获取验证码
	 */
	private static final String verifyCodeUrl = "message/getMsgCode";
	/**
	 * 交易模块---交易（垫付，自有资金支付）
	 */
	private static final String batchTradeUrl = "trade/batchTrade";
	/**
	 * 交易模块---服务支付
	 */
	private static final String servicePayUrl = "services/servicePay";

	/**
	 * 交易模块---运输费（服务费）支付接口
	 */
	private static final String feePayUrl = "services/feePay";

	/**
	 * 交易模块---运输费（服务费）支付接口
	 */
	private static final String feePayReturnUrl = "services/feePayReturn";

	/**
	 * 交易模块---运输费（服务费）支付接口
	 */
	private static final String getFeePayResultUrl = "services/getFeePayResult";

	/**
	 * 交易模块---运输费（服务费）项目接口
	 */
	private static final String subFeeAccountUrl = "account/subFeeAccount";

	/**
	 * 运输费上划下拨接口
	 */
	private static final String feeTransFeePaymentUrl = "/transfer/feeTransFeePayment";

	public static void main(String[] args) {
		/*String a = "{\"code\":\"1\",\"data\":{\"transCodeId\":\"202209081543191031201532\",\"code\":\"000000\",\"dataBody\":{\"OthBankPayeeSubAcc\":\"*****************\",\"OthBankPayeeSubAccSetteName\":\"华夏银行股份有限公司天宁支行\",\"Remark4\":\"\",\"Remark5\":\"\",\"Remark2\":\"\",\"Remark3\":\"\",\"Remark1\":\"9915670166243238562\",\"OthBankPayeeSubAccName\":\"常州智联云物流科技股份有限公司\"},\"transCode\":\"103120\",\"message\":\"交易成功\"}}";
		JSONObject jsonObject = JSON.parseObject(a);
		jsonObject = jsonObject.getJSONObject("data").getJSONObject("dataBody");
		System.out.println(JSONObject.toJSONString(AjaxResult.success(jsonObject)));*/
		// System.out.println(Arrays.asList(BusinessConstants.PDS_OPERATION_TYPE_LIST).contains(1));

		/*String message = "{\"code\":\"1\",\"data\":{\"payCode\":\"*********************\",\"payState\":\"1\",\"remark1\":\"*********************\",\"remark2\":\"\"},\"message\":\"支付成功\"}";
		PDSNoticeRes res = JSONObject.parseObject(message).getObject("data", PDSNoticeRes.class);
		System.out.println(res.getPayCode());*/

		AjaxResult result = new AjaxResult();
		result.setData(
				"[{\"sub_31\":{\"accountId\":\"168722475246700384\",\"accountType\":31,\"callerId\":\"1\",\"callerProjectId\":\"168731053152700003\",\"callerProjectName\":\"小雨点测试项目2\",\"callerUserId\":\"***************\",\"callerUserName\":\"车联天下交投物流产业发展有限公司\",\"createTime\":\"2023-06-21 09:22:12\",\"freezeMoney\":0,\"id\":\"168731053211400592\",\"modifyTime\":\"*************\",\"money\":0,\"phone\":\"***********\",\"remark\":\"91344MA2UAL1D2X5666\",\"subAccountName\":\"小雨点测试项目2项目子帐户\",\"subAccountNo\":\"****************\",\"sumMoney\":0,\"summary\":\"车联天下交投物流产业运费\"},\"caller\":{\"bankId\":\"*****************\",\"callerId\":\"1\",\"callerShortName\":\"车联天下交投物流产业\",\"callerUserId\":\"***************\",\"callerUserName\":\"车联天下交投物流产业发展有限公司\",\"cardNo\":\"91344MA2UAL1D2X5666\",\"contact\":\"安庆\",\"contactAddress\":\"默认地址\",\"contactMobile\":\"***********\",\"contactPhone\":\"\",\"createTime\":\"2023-06-20 09:32:32\",\"email\":\"\",\"fax\":\"\",\"fundPassword\":\"\",\"id\":\"168722475242400381\",\"payPassword\":\"\",\"remark\":\"\",\"sourceType\":10,\"state\":0,\"userId\":\"168722475207700379\",\"userType\":1},\"sub_61\":{\"accountId\":\"168722475250200387\",\"accountType\":61,\"callerId\":\"1\",\"callerProjectId\":\"168731053152700003\",\"callerProjectName\":\"小雨点测试项目2\",\"callerUserId\":\"***************\",\"callerUserName\":\"车联天下交投物流产业发展有限公司\",\"createTime\":\"2023-06-21 09:22:12\",\"freezeMoney\":0,\"id\":\"168731053216300594\",\"modifyTime\":\"*************\",\"money\":0,\"phone\":\"***********\",\"remark\":\"91344MA2UAL1D2X5666\",\"subAccountName\":\"小雨点测试项目2油卡项目子帐户\",\"subAccountNo\":\"****************\",\"sumMoney\":0,\"summary\":\"车联天下交投物流产业运费\"},\"sub_51\":{\"accountId\":\"168722475249000386\",\"accountType\":51,\"callerId\":\"1\",\"callerProjectId\":\"168731053152700003\",\"callerProjectName\":\"小雨点测试项目2\",\"callerUserId\":\"***************\",\"callerUserName\":\"车联天下交投物流产业发展有限公司\",\"createTime\":\"2023-06-21 09:22:12\",\"freezeMoney\":0,\"id\":\"168731053213900593\",\"modifyTime\":\"*************\",\"money\":0,\"phone\":\"***********\",\"remark\":\"91344MA2UAL1D2X5666\",\"subAccountName\":\"小雨点测试项目2服务项目子帐户\",\"subAccountNo\":\"****************\",\"sumMoney\":0,\"summary\":\"车联天下交投物流产业运费\"}},{\"sub_31\":{\"accountId\":\"168118155517400052\",\"accountType\":31,\"callerId\":\"1\",\"callerProjectId\":\"168731053152700003\",\"callerProjectName\":\"小雨点测试项目2\",\"callerUserId\":\"***************\",\"callerUserName\":\"弊晋仕小忆痹有限公司\",\"createTime\":\"2023-06-21 09:22:12\",\"freezeMoney\":0,\"id\":\"168731053218800595\",\"isPayer\":0,\"modifyTime\":\"*************\",\"money\":0,\"phone\":\"***********\",\"remark\":\"602701916473407313\",\"subAccountName\":\"小雨点测试项目2项目子帐户\",\"subAccountNo\":\"****************\",\"sumMoney\":0,\"summary\":\"车联天下交投物流产业运费\"},\"caller\":{\"bankId\":\"*****************\",\"callerId\":\"1\",\"callerShortName\":\"弊晋仕小忆痹\",\"callerUserId\":\"***************\",\"callerUserName\":\"弊晋仕小忆痹有限公司\",\"cardNo\":\"602701916473407313\",\"contact\":\"木子鱼香\",\"contactAddress\":\"默认地址\",\"contactMobile\":\"***********\",\"contactPhone\":\"\",\"createTime\":\"2023-04-11 10:52:34\",\"email\":\"\",\"fax\":\"\",\"fundPassword\":\"\",\"id\":\"168118155512000049\",\"payPassword\":\"\",\"remark\":\"\",\"sourceType\":10,\"state\":0,\"userId\":\"168118155478300047\",\"userType\":1},\"sub_61\":{\"accountId\":\"168118155521700055\",\"accountType\":61,\"callerId\":\"1\",\"callerProjectId\":\"168731053152700003\",\"callerProjectName\":\"小雨点测试项目2\",\"callerUserId\":\"***************\",\"callerUserName\":\"弊晋仕小忆痹有限公司\",\"createTime\":\"2023-06-21 09:22:12\",\"freezeMoney\":0,\"id\":\"168731053224400597\",\"modifyTime\":\"*************\",\"money\":0,\"phone\":\"***********\",\"remark\":\"602701916473407313\",\"subAccountName\":\"小雨点测试项目2油卡项目子帐户\",\"subAccountNo\":\"****************\",\"sumMoney\":0,\"summary\":\"车联天下交投物流产业运费\"},\"sub_51\":{\"accountId\":\"168118155520100054\",\"accountType\":51,\"callerId\":\"1\",\"callerProjectId\":\"168731053152700003\",\"callerProjectName\":\"小雨点测试项目2\",\"callerUserId\":\"***************\",\"callerUserName\":\"弊晋仕小忆痹有限公司\",\"createTime\":\"2023-06-21 09:22:12\",\"freezeMoney\":0,\"id\":\"168731053221200596\",\"isPayer\":0,\"modifyTime\":\"*************\",\"money\":0,\"phone\":\"***********\",\"remark\":\"602701916473407313\",\"subAccountName\":\"小雨点测试项目2服务项目子帐户\",\"subAccountNo\":\"****************\",\"sumMoney\":0,\"summary\":\"车联天下交投物流产业运费\"}},{\"sub_31\":{\"accountId\":\"155806138806900005\",\"accountType\":31,\"callerId\":\"1\",\"callerProjectId\":\"168731053152700003\",\"callerProjectName\":\"小雨点测试项目2\",\"callerUserId\":\"***************\",\"callerUserName\":\"湖北车联天下物流有限公司\",\"createTime\":\"2023-06-21 09:22:12\",\"freezeMoney\":0,\"id\":\"168731053226900598\",\"modifyTime\":\"*************\",\"money\":0,\"phone\":\"***********\",\"remark\":\"914209843523400171\",\"subAccountName\":\"小雨点测试项目2项目子帐户\",\"subAccountNo\":\"****************\",\"sumMoney\":0,\"summary\":\"车联天下交投物流产业运费\"},\"caller\":{\"bankId\":\"*****************\",\"callerId\":\"1\",\"callerShortName\":\"湖北车联天下物流\",\"callerUserId\":\"***************\",\"callerUserName\":\"湖北车联天下物流有限公司\",\"cardNo\":\"914209843523400171\",\"contact\":\"岳蓉\",\"contactAddress\":\"默认地址\",\"contactMobile\":\"***********\",\"contactPhone\":\"\",\"createTime\":\"2023-06-20 14:15:13\",\"email\":\"\",\"fax\":\"\",\"fundPassword\":\"\",\"id\":\"168724171413200439\",\"payPassword\":\"\",\"remark\":\"\",\"sourceType\":10,\"state\":0,\"userId\":\"168724171375700437\",\"userType\":1},\"sub_61\":{\"accountId\":\"156619623824600106\",\"accountType\":61,\"callerId\":\"1\",\"callerProjectId\":\"168731053152700003\",\"callerProjectName\":\"小雨点测试项目2\",\"callerUserId\":\"***************\",\"callerUserName\":\"湖北车联天下物流有限公司\",\"createTime\":\"2023-06-21 09:22:12\",\"freezeMoney\":0,\"id\":\"168731053231600600\",\"modifyTime\":\"*************\",\"money\":0,\"phone\":\"***********\",\"remark\":\"914209843523400171\",\"subAccountName\":\"小雨点测试项目2油卡项目子帐户\",\"subAccountNo\":\"****************\",\"sumMoney\":0,\"summary\":\"车联天下交投物流产业运费\"},\"sub_51\":{\"accountId\":\"156438774148800292\",\"accountType\":51,\"callerId\":\"1\",\"callerProjectId\":\"168731053152700003\",\"callerProjectName\":\"小雨点测试项目2\",\"callerUserId\":\"***************\",\"callerUserName\":\"湖北车联天下物流有限公司\",\"createTime\":\"2023-06-21 09:22:12\",\"freezeMoney\":0,\"id\":\"168731053229200599\",\"modifyTime\":\"*************\",\"money\":0,\"phone\":\"***********\",\"remark\":\"914209843523400171\",\"subAccountName\":\"小雨点测试项目2服务项目子帐户\",\"subAccountNo\":\"****************\",\"sumMoney\":0,\"summary\":\"车联天下交投物流产业运费\"}}]");
		/*UserTotalAccount account = JSONObject.parseObject(JSONObject.toJSONString(result.getData())).getObject("userTotalAccount", UserTotalAccount.class);
		System.out.println(account.getAccountNo());*/

		JSONArray array = JSONArray.parseArray((String) result.getData());
		List<UserLittleAccount> list = new ArrayList<>();
		for (int i = 0; i < array.size(); i++) {
			list.add(array.getJSONObject(i).getObject("sub_31", UserLittleAccount.class));
		}
		System.out.println(JSON.toJSONString(list));

	}

	/**
	 * 这个应该废弃了
	 *
	 * @param identityCard
	 * @param userType
	 * @param projectId
	 * @param projectName
	 * @param contractIds
	 * @param tradeName
	 * @param waybillCode
	 * @param isPay
	 * @param accType
	 * @param startTime
	 * @param endTime
	 * @param tranType
	 * @param state
	 * @param pageDomain
	 * @param sourceType
	 * @return
	 */
	public TableDataInfo queryCustomerAccountFlowSum(String identityCard, Integer userType, String projectId, String projectName, List<Long> contractIds, String tradeName, String waybillCode,
			Integer isPay, Integer accType, String startTime, String endTime, Integer tranType, Integer state, PageDomain pageDomain, Integer sourceType) {
		JSONObject sendMap = new JSONObject();
		sendMap.put("sourceType", BusinessConstants.SOURCE_PC_10);
		if (null == sourceType && ClientType.CARRIER.equals(SecurityUtils.getClientType())) {
			sendMap.put("sourceType", BusinessConstants.SOURCE_APP_20);
		}
		sendMap.put("userType", userType);
		sendMap.put("idNo", identityCard); // 证件号
		sendMap.put("accType", accType);// 账户类型(1:总账户 2:现金总帐户 3:项目总账户 4:结算计息总账户 5:项目子帐户(父类是3) 6:结算项目子帐户(父类是4) 7:司机账户)
		sendMap.put("isPay", isPay);// 交易类型（-1 ：付，1：收）
		sendMap.put("callerProjectId", projectId);// 合作平台项目id
		sendMap.put("projectIdList", contractIds);// 合作平台项目id
		sendMap.put("callerProjectName", projectName);// 合作平台项目名称（模糊）
		sendMap.put("callerWaybillCode", waybillCode);// 合作平台运单编号
		sendMap.put("tradeName", tradeName);// 交易人名称
		sendMap.put("startTime", startTime);// 交易开始时间
		sendMap.put("endTime", endTime);// 交易开始时间
		sendMap.put("tranType", tranType);// 货物类别(10:责任险 19:货运险)
		sendMap.put("state", state);
		sendMap.put("isSum", 1);// 不需要合计
		sendMap.put("isNeedMoney", -1); // 不需要最后的金额
		sendMap.put("pageNum", pageDomain.getPageNum());// 获取数据的当前页数
		sendMap.put("pageSize", pageDomain.getPageSize());// 每页默认获取记录数
		AjaxResult result = huaXiaHttpUtil.sendAccountPost(accountFlowListUrl, sendMap);
		if (HttpStatus.SUCCESS != Integer.parseInt(String.valueOf(result.getCode()))) {
			log.info("查询账户流水--->" + result.getMsg());
			throw new RuntimeException("查询账户流水失败：" + result.getMsg());
		}
		TableDataInfo rspData = new TableDataInfo();
		rspData.setCode(HttpStatus.SUCCESS);
		rspData.setMsg(String.valueOf(result.getMsg()));
		rspData.setRows(JSONArray.parseArray(JSONObject.toJSONString(result.get("rows"))).toJavaList(UserAccountFlow.class));
		rspData.setTotal(Integer.parseInt(String.valueOf(result.get("total"))));
		return rspData;
	}

}
