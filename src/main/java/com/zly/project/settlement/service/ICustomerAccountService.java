package com.zly.project.settlement.service;

import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.TableInfo;
import com.zly.project.agentOperation.domain.req.AgentFlowReq;
import com.zly.project.carrier.payee.domain.PayeeInfo;
import com.zly.project.consignor.customer.domain.CustomerInfo;
import com.zly.project.settlement.domain.CustomerAccount;
import com.zly.project.settlement.domain.request.CorporateReq;
import com.zly.project.settlement.domain.request.CustomerAccountReq;
import com.zly.project.settlement.domain.request.ali.FixcardCreateReq;
import com.zly.project.settlement.domain.response.CustomerAccountRes;
import com.zly.project.settlement.domain.response.CustomerBankFlowRollOutRes;
import com.zly.project.system.domain.SysUser;
import com.zly.project.system.domain.req.NetWithdrawalReq;
import com.zly.project.system.domain.req.SysUpdatePasswordReq;

import java.math.BigDecimal;
import java.util.List;

/**
 * 客户账户Service接口
 *
 * <AUTHOR>
 * @date 2021-10-22
 */
public interface ICustomerAccountService {
	/**
	 * 查询客户账户
	 *
	 * @param id
	 * 		客户账户主键
	 * @return 客户账户
	 */
	public CustomerAccount selectCustomerAccountById(Long id);

	/**
	 * 查询客户账户列表
	 *
	 * @param customerAccount
	 * 		客户账户
	 * @return 客户账户集合
	 */
	public List<CustomerAccount> selectCustomerAccountList(CustomerAccount customerAccount);

	/**
	 * 新增客户账户
	 *
	 * @param customerAccount
	 * 		客户账户
	 * @return 结果
	 */
	public int insertCustomerAccount(CustomerAccount customerAccount);

	/**
	 * 修改客户账户
	 *
	 * @param customerAccount
	 * 		客户账户
	 * @return 结果
	 */
	public int updateCustomerAccount(CustomerAccount customerAccount);

	/**
	 * 批量删除客户账户
	 *
	 * @param ids
	 * 		需要删除的客户账户主键集合
	 * @return 结果
	 */
	public int deleteCustomerAccountByIds(Long[] ids);

	/**
	 * 删除客户账户信息
	 *
	 * @param id
	 * 		客户账户主键
	 * @return 结果
	 */
	public int deleteCustomerAccountById(Long id);

	/**
	 * 更新余额，并记录余额变动明细
	 *
	 * @param id
	 * @param money
	 * @param isPay
	 * @param waybillCode
	 * @param payCode
	 * @param tranType
	 * @param tradeName
	 * @param tradeNo
	 * @param remark
	 * @return
	 */
	CustomerAccount updateAccountMoney(Long id, Long customerId, BigDecimal money, Integer isPay, Boolean isContract, String waybillCode, String payCode, Integer tranType, String tradeName,
			String tradeNo, String remark);

	/**
	 * 根据updatetime 和ID更新账户（乐观锁）
	 *
	 * @param account
	 * @return
	 */
	public int updateCustomerAccountByUpdateTime(CustomerAccount account);

	/**
	 * 查询客户账户列表
	 *
	 * @param customerAccount
	 * 		客户账户
	 * @return 客户账户集合
	 */
	List<CustomerAccount> selectCustomerAccountListEx(CustomerAccountReq customerAccount);

	/**
	 * 记录客户账户，更新客户资料
	 *
	 * @param customerInfo
	 * @param bankCardNo
	 * @param branchNo
	 * @param branchName
	 */
	void initCustomerInfoAccount(CustomerInfo customerInfo, String bankCardNo, String branchNo, String branchName);

	/**
	 * 记录收款人账户，更新客户资料
	 *
	 * @param payeeInfo
	 * @param bankCardNo
	 * @param branchNo
	 * @param branchName
	 */
	CustomerAccount initPayeeInfoAccount(PayeeInfo payeeInfo, String bankCardNo, String branchNo, String branchName);

	/**
	 * 发送验证密码
	 *
	 * @param customerAccount
	 * @return
	 */
	AjaxResult sendMsg(CustomerAccount customerAccount, Long customerId);

	AjaxResult sendFreightForwarderMsg(CustomerAccount customerAccount, Long customerId);

	/**
	 * 修改账户密码
	 *
	 * @param customerAccount
	 * @return
	 */
	AjaxResult updatePassword(CustomerAccount customerAccount, Long customerId);

	AjaxResult updateFreightForwarderPassword(SysUpdatePasswordReq sysUpdatePasswordReq, Long freightForwarderInfoId);

	/**
	 * 查询登录商户的账号，（移除了密码）
	 *
	 * @return
	 */
	AjaxResult selectCustomerAccountByCustomerId(Long customerId);

	List<CustomerAccountRes> selectTenantAccountListEx(CustomerAccountReq customerAccount);

	List<CustomerAccountRes> selectCarrierAccountListEx(CustomerAccountReq customerAccount);

	/**
	 * 未分配金额转出
	 *
	 * @param netWithdrawalReq
	 * @return
	 */
	CommonResult<String> freightForwarderWithdrawal(NetWithdrawalReq netWithdrawalReq, SysUser user);

	/**
	 * 转出列表
	 *
	 * @param agentFlowReq
	 * @return
	 */
	TableInfo<CustomerBankFlowRollOutRes> selectCustomerWalletFlowList(AgentFlowReq agentFlowReq);

	AjaxResult addCorporateAccount(CorporateReq corporateReq);

	AjaxResult delCorporateAccount(CorporateReq corporateReq);

	AjaxResult openCorporateAccount(FixcardCreateReq corporateReq) throws Exception;
}
