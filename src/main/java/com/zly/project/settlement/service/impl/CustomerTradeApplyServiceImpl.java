package com.zly.project.settlement.service.impl;

import com.alibaba.fastjson2.JSON;
import com.zly.common.constant.BusinessConstants;
import com.zly.common.constant.ExportConstants;
import com.zly.common.constant.HttpStatus;
import com.zly.common.enums.ClientType;
import com.zly.common.enums.ExportEnum;
import com.zly.common.enums.PayType;
import com.zly.common.utils.CodeUtil;
import com.zly.common.utils.CommonUtil;
import com.zly.common.utils.DateUtils;
import com.zly.common.utils.PageUtils;
import com.zly.common.utils.StringUtils;
import com.zly.common.utils.TextUtil;
import com.zly.common.utils.bean.BeanUtils;
import com.zly.framework.web.domain.AjaxResult;
import com.zly.framework.web.domain.CommonResult;
import com.zly.framework.web.page.TableInfo;
import com.zly.project.common.domain.FileModel;
import com.zly.project.common.domain.req.CustomerExportRecordReq;
import com.zly.project.common.service.ExportService;
import com.zly.project.es.waybill.service.EsWaybillService;
import com.zly.project.miniprogram.domain.res.IncomeRecordsRes;
import com.zly.project.settlement.domain.CustomerTradeApply;
import com.zly.project.settlement.domain.request.TradeFlowQueryReq;
import com.zly.project.settlement.domain.response.FlowRes;
import com.zly.project.settlement.domain.response.FlowResEx;
import com.zly.project.settlement.mapper.CustomerTradeApplyMapper;
import com.zly.project.settlement.mapper.CustomerTradeApplyMapperEx;
import com.zly.project.settlement.service.ICustomerTradeApplyService;
import com.zly.project.statistics.domain.BillStatisticsInfo;
import com.zly.project.system.domain.SysUser;
import com.zly.project.taxUpload.domain.PdsPayment;
import com.zly.project.tenant.domain.TenantUser;
import com.zly.project.transport.waybill.domain.Waybill;
import com.zly.project.transport.waybill.domain.req.WaybillReq;
import com.zly.project.transport.waybill.domain.res.WaybillRes;
import com.zly.project.transport.waybill.mapper.WaybillGoodsMapperEx;
import com.zly.project.transport.waybill.mapper.WaybillMapperEx;
import com.zly.project.transport.waybill.service.IWaybillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zly.common.constant.ExportConstants.SETTLEMENT_LIST;
import static com.zly.common.constant.ExportConstants.SETTLEMENT_LIST_WITH_WAYBILL;

/**
 * 运单费用申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-17
 */
@Slf4j
@Service
public class CustomerTradeApplyServiceImpl implements ICustomerTradeApplyService {

	@Resource
	private CustomerTradeApplyMapper customerTradeApplyMapper;
	@Resource
	private CustomerTradeApplyMapperEx customerTradeApplyMapperEx;
	@Resource
	private IWaybillService waybillService;
	@Resource
	private EsWaybillService esWaybillService;
	@Resource
	private ExportService exportService;
	@Resource
	private WaybillMapperEx waybillMapperEx;
	@Resource
	private WaybillGoodsMapperEx waybillGoodsMapperEx;

	/**
	 * 查询运单费用申请
	 *
	 * @param id
	 *            运单费用申请主键
	 * @return 运单费用申请
	 */
	@Override
	public CustomerTradeApply selectCustomerTradeApplyById(Long id) {
		return customerTradeApplyMapper.selectCustomerTradeApplyById(id);
	}

	/**
	 * 查询运单费用申请列表
	 *
	 * @param customerTradeApply
	 *            运单费用申请
	 * @return 运单费用申请
	 */
	@Override
	public List<CustomerTradeApply> selectCustomerTradeApplyList(CustomerTradeApply customerTradeApply) {
		return customerTradeApplyMapper.selectCustomerTradeApplyList(customerTradeApply);
	}

	/**
	 * 新增运单费用申请
	 *
	 * @param customerTradeApply
	 *            运单费用申请
	 * @return 结果
	 */
	@Override
	public int insertCustomerTradeApply(CustomerTradeApply customerTradeApply) {
		return customerTradeApplyMapper.insertCustomerTradeApply(customerTradeApply);
	}

	/**
	 * 修改运单费用申请
	 *
	 * @param customerTradeApply
	 *            运单费用申请
	 * @return 结果
	 */
	@Override
	public int updateCustomerTradeApply(CustomerTradeApply customerTradeApply) {
		int result = customerTradeApplyMapper.updateCustomerTradeApply(customerTradeApply);
		if (result <= 0) {
			log.info("更新运单的付款申请失败，申请ID:{}", customerTradeApply.getId());
			throw new RuntimeException("更新运单的付款申请失败");
		}
		return result;
	}

	/**
	 * 批量删除运单费用申请
	 *
	 * @param ids
	 *            需要删除的运单费用申请主键
	 * @return 结果
	 */
	@Override
	public int deleteCustomerTradeApplyByIds(Long[] ids) {
		return customerTradeApplyMapper.deleteCustomerTradeApplyByIds(ids);
	}

	/**
	 * 删除运单费用申请信息
	 *
	 * @param id
	 *            运单费用申请主键
	 * @return 结果
	 */
	@Override
	public int deleteCustomerTradeApplyById(Long id) {
		return customerTradeApplyMapper.deleteCustomerTradeApplyById(id);
	}

	/**
	 * 运单费用申请支付
	 */
	@Override
	public AjaxResult applyWaybillFare(WaybillReq waybillReq, Object userObj) {
		BeanUtils.beanAttributeValueTrim(waybillReq);// 去掉该对象中string字段前后空格
		// 校验必填数据
		if (null == waybillReq.getId() || waybillReq.getId() < 1L) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "运单标识为空");
		}
		if (StringUtils.isNull(waybillReq.getFareStage())) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "付款阶段为空");
		}
		Waybill waybill = waybillService.selectWaybillById(waybillReq.getId());
		if (null == waybill) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "没有找到对应的运单信息");
		}
		if (PayType.TOTAL.equals(waybill.getPayType()) && waybillReq.getFareStage() != BusinessConstants.FARE_STAGE_TOTAL_COST) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "该运单付款阶段只能是总费用");
		}
		if (PayType.PHASE.equals(waybill.getPayType()) && (waybillReq.getFareStage() < BusinessConstants.FARE_STAGE_PREPAY || waybillReq.getFareStage() > BusinessConstants.FARE_STAGE_RECEIPT)) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "付款阶段不匹配");
		}
		// 如果是总费用，检查该运单是否确认到达
		if (PayType.TOTAL.equals(waybill.getPayType()) && waybill.getStatus() != BusinessConstants.WAYBILL_STATUS_COMPLETED) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "运单" + waybill.getShippingNoteNumber() + "尚未完成运输，无法申请付款");
		}
		if (waybill.getPrepayUnpaidMoney().compareTo(BigDecimal.ZERO) == 0) {
			if (waybill.getArriveUnpaidMoney().compareTo(BigDecimal.ZERO) > 0 && waybill.getStatus() != BusinessConstants.WAYBILL_STATUS_COMPLETED) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "运单" + waybill.getShippingNoteNumber() + "尚未完成运输，无法申请到付");
			}
			if (waybill.getReceiptUnpaidMoney().compareTo(BigDecimal.ZERO) > 0 && waybill.getStatus() != BusinessConstants.WAYBILL_STATUS_COMPLETED) {
				return AjaxResult.error(HttpStatus.BAD_REQUEST, "运单" + waybill.getShippingNoteNumber() + "尚未完成运输，无法申请回单付");
			}
		}
		// CustomerTradeApply customerTradeApply = new CustomerTradeApply();
		// customerTradeApply.setWaybillId(waybill.getId());// 运单ID
		// customerTradeApply.setShippingNoteNumber(waybill.getShippingNoteNumber());// 运单编号
		// // customerTradeApply.setFareStage(waybillReq.getFareStage());// 付款阶段（0、总费用；1、预付；2、到付、3回单付）
		// customerTradeApply.setAuditStatus(BusinessConstants.WAYBILL_PAY_AUDIT_STATUS_WAIT);// 审核状态(0:待审核 1:审核通过 2:审核不通过)
		// List<CustomerTradeApply> customerTradeApplyList = customerTradeApplyMapper.selectCustomerTradeApplyList(customerTradeApply);
		// if (null != customerTradeApplyList && !customerTradeApplyList.isEmpty()) {
		// return AjaxResult.error(HttpStatus.BAD_REQUEST, "当前有付款申请正在审核。");
		// }
		Long[] ids = { waybill.getId() };
		int count = customerTradeApplyMapperEx.tradeApplyVerify(ids);
		if (count > 0) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "运单费用正在审核或付款中，不能申请付款");
		}

		Date curDate = DateUtils.getNowDate();
		String userName = "";
		if (userObj instanceof SysUser) {
			SysUser sysUser = (SysUser) userObj;
			userName = sysUser.getUserName();
		} else if (userObj instanceof TenantUser) {
			TenantUser tenantUser = (TenantUser) userObj;
			userName = tenantUser.getUserName();
		}
		BigDecimal applyMoney = BigDecimal.ZERO;
		switch (waybillReq.getFareStage()) {
		case BusinessConstants.FARE_STAGE_TOTAL_COST:
			applyMoney = waybill.getUnpaidFare();
			break;
		case BusinessConstants.FARE_STAGE_PREPAY:
			applyMoney = waybill.getPrepayUnpaidMoney();
			break;
		case BusinessConstants.FARE_STAGE_ARRIVE:
			applyMoney = waybill.getArriveUnpaidMoney();
			break;
		case BusinessConstants.FARE_STAGE_RECEIPT:
			applyMoney = waybill.getReceiptUnpaidMoney();
			break;
		default:
			break;
		}
		if (applyMoney.compareTo(BigDecimal.ZERO) == 0) {
			return AjaxResult.error(HttpStatus.BAD_REQUEST, "申请支付的金额为0");
		}
		CustomerTradeApply customerTradeApply = new CustomerTradeApply();
		customerTradeApply.setId(TextUtil.getTimeSequenceID(5));
		customerTradeApply.setWaybillId(waybill.getId());
		customerTradeApply.setShippingNoteNumber(waybill.getShippingNoteNumber());// 运单编号
		customerTradeApply.setFareStage(waybillReq.getFareStage());// 付款阶段（0、总费用；1、预付；2、到付、3回单付）
		customerTradeApply.setApplyPayCode(CodeUtil.generateApplyPayCode());// 付款申请单号
		customerTradeApply.setPayeeId(waybill.getPayeeId());// 收款人ID
		customerTradeApply.setPayeeName(waybill.getPayeeName());// 收款人名称
		customerTradeApply.setApplyMoney(applyMoney);// 申请金额
		customerTradeApply.setSubmitBy(userName);// 提交人
		customerTradeApply.setSubmitTime(curDate);// 提交审核时间
		customerTradeApply.setPayTime(curDate);
		customerTradeApplyMapper.insertCustomerTradeApply(customerTradeApply);

		// 更改运单表申请付款状态
		Waybill waybillUpdate = waybillService.selectWaybillById(waybill.getId());
		Waybill waybillMysqlUpdate = new Waybill();
		waybillMysqlUpdate.setId(waybillUpdate.getId());
		waybillMysqlUpdate.setPayApplyStatus(BusinessConstants.WAYBILL_PAY_APPLY_STATUS_APPLYING);// 申请中
		waybillService.updateWaybill(waybillMysqlUpdate);
		//修改es中运单
		waybillUpdate.setPayApplyStatus(BusinessConstants.WAYBILL_PAY_APPLY_STATUS_APPLYING);// 申请中
		esWaybillService.updateEsWaybillInfo(waybillUpdate);
		return AjaxResult.success(HttpStatus.SUCCESS, BusinessConstants.SUCCESS_DESC);
	}

	@Override
	public List<CustomerTradeApply> selectCustomerTradeApplyListByIds(List<String> ids) {
		if (null == ids || ids.size() == 0) {
			return new ArrayList<>();
		}
		return customerTradeApplyMapperEx.selectCustomerTradeApplyListByIds(ids);
	}

	/**
	 * 查询审核通过的运单
	 *
	 * @param customerTradeApply
	 * @return
	 */
	@Override
	public CustomerTradeApply selectCustomerTradeApplyByIdAndStatus(CustomerTradeApply customerTradeApply) {
		if (customerTradeApply == null || customerTradeApply.getId() == null) {
			throw new RuntimeException("根据运单信息查询付款申请失败");
		}
		customerTradeApply = this.selectCustomerTradeApplyById(customerTradeApply.getId());
		if (StringUtils.isNull(customerTradeApply)) {
			throw new RuntimeException("没有运单的付款申请信息");
		}
		if (BusinessConstants.WAYBILL_PAY_AUDIT_STATUS_PASS != customerTradeApply.getAuditStatus() && BusinessConstants.WAYBILL_PAY_AUDIT_STATUS_FAIL != customerTradeApply.getAuditStatus()) {
			throw new RuntimeException("运单付款申请状态不正确");
		}
		return customerTradeApply;
	}

	@Override
	public int updateCustomerTradeApplyByWaybillIds(Long[] ids) {
		if (ids == null || ids.length == 0) {
			return 0;
		}
		return customerTradeApplyMapperEx.updateCustomerTradeApplyByWaybillId(ids);
	}

	@Override
	public List<FlowRes> queryCustomerFlow(TradeFlowQueryReq req) {
		return customerTradeApplyMapperEx.queryCustomerFlow(req);
	}

	/**
	 * 托运人流水导出
	 *
	 * @param exportReq
	 * @return
	 */
	@Override
	public List<FileModel> exportCustomerFlow(CustomerExportRecordReq exportReq) {
		List<FileModel> fileList = new ArrayList<>();
		// 生成对应导出业务的文件名称
		final String fileBaseName = exportService.generateFileName(exportReq.getRequestType());

		TradeFlowQueryReq req = JSON.parseObject(exportReq.getRequestParams(), TradeFlowQueryReq.class);
		req.setCustomerId(exportReq.getCustomerId());
		if (SETTLEMENT_LIST.equals(exportReq.getRequestUri())) {
			List<FlowRes> list;
			int page = 0;
			do {
				PageUtils.startPage(++page, ExportConstants.PAGE_SIZE, false);
				list = this.queryCustomerFlow(req);

				// 2、文件名加上当前序号
				String fileName = fileBaseName + ExportConstants.DELIMITER + page + ExportConstants.SUFFIX_XLSX;

				// 3、生成文件并上传至七牛云
				String fileUrl = exportService.easyExport(list, fileName, FlowRes.class);

				fileList.add(FileModel.of(fileName, fileUrl));
			} while (list.size() == ExportConstants.PAGE_SIZE);
		} else if (SETTLEMENT_LIST_WITH_WAYBILL.equals(exportReq.getRequestUri())) {
			// 导出结算列表附带运单信息
			List<FlowResEx> list;
			int page = 0;
			do {
				PageUtils.startPage(++page, ExportConstants.PAGE_SIZE, false);
				list = this.queryCustomerFlowWithWaybill(req);

				//List<Long> waybillIds = list.stream().map(FlowResEx::getWaybillId).map(Long::parseLong).collect(Collectors.toList());
				//if (StringUtils.isNotEmpty(waybillIds)) {
				//	Map<Long, String> waybillGoodsMap = waybillGoodsMapperEx.selectGoodsByWaybillIds(waybillIds).stream().collect(Collectors.toMap(WaybillGoods::getWaybillId, WaybillGoods::getDescriptionOfGoods));
				//	list.forEach(waybill -> {
				//		if (waybillGoodsMap.containsKey(Long.parseLong(waybill.getWaybillId()))) {
				//			waybill.setGoodsName(waybillGoodsMap.get(Long.parseLong(waybill.getWaybillId())));
				//		}
				//	});
				//}

				// 2、文件名加上当前序号
				String fileName = fileBaseName + ExportConstants.DELIMITER + page + ExportConstants.SUFFIX_XLSX;

				// 3、生成文件并上传至七牛云
				String fileUrl = exportService.easyExport(list, fileName, FlowResEx.class);

				fileList.add(FileModel.of(fileName, fileUrl));
			} while (list.size() == ExportConstants.PAGE_SIZE);
		}

		return fileList;
	}

	@Override
	public List<FlowResEx> queryCustomerFlowWithWaybill(TradeFlowQueryReq req) {
		return customerTradeApplyMapperEx.queryCustomerFlowWithWaybill(req);
	}


	public List<FlowResEx> queryCustomerFlowWithWaybillSP(TradeFlowQueryReq req) {
		return customerTradeApplyMapperEx.queryCustomerFlowWithWaybillSP(req);
	}

	@Override
	public List<FlowRes> queryplatformFlow(TradeFlowQueryReq req) {
		List<FlowRes> flowRes = customerTradeApplyMapperEx.queryplatformFlow(req);
		if(CommonUtil.isNullOrEmpty(flowRes)){
			return flowRes;
		}
		if(flowRes.get(0).getCustomerName() == null){
			Set<Long> ids = flowRes.stream().map(a -> Long.parseLong(a.getWaybillId())).collect(Collectors.toSet());
			List<WaybillRes> parentCustomerName = waybillMapperEx.selectParentCustomerNameByWaybillIds(new ArrayList<>(ids));
			Map<Long, String> customeNameMap = parentCustomerName.stream().collect(Collectors.toMap(WaybillRes::getId, WaybillRes::getCustomerName));
			flowRes.forEach(a -> {
				a.setCustomerName(customeNameMap.get(Long.parseLong(a.getWaybillId())));
			});
		}
		return flowRes;
	}

	@Override
	public List<FlowResEx> queryplatformFlowWithWaybill(TradeFlowQueryReq req) {
		return customerTradeApplyMapperEx.queryplatformFlowWithWaybill(req);
	}

	/**
	 * 托运人导出功能
	 *
	 * @param type
	 *            导出类型（具体请求类型参照/common/export/types接口返回的说明）
	 * @param params
	 *            参数json串
	 * @return
	 */
	@Override
	public CommonResult<?> listExport(int type, String params) {
		if (!ExportEnum.contains(type)) {
			return CommonResult.requestError("type错误");
		}
		if (StringUtils.isBlank(params)) {
			return CommonResult.requestError("params为空");
		}
		return exportService.commonExport(type, SETTLEMENT_LIST, params, ClientType.SHIPPER.val());
	}

	/**
	 * 托运人导出功能
	 *
	 * @param type
	 *            导出类型（具体请求类型参照/common/export/types接口返回的说明）
	 * @param params
	 *            参数json串
	 * @return
	 */
	@Override
	public CommonResult<?> listWithWaybillExport(int type, String params) {
		if (!ExportEnum.contains(type)) {
			return CommonResult.requestError("type错误");
		}
		if (StringUtils.isBlank(params)) {
			return CommonResult.requestError("params为空");
		}

		return exportService.commonExport(type, SETTLEMENT_LIST_WITH_WAYBILL, params, ClientType.SHIPPER.val());
	}

	@Override
	public TableInfo<IncomeRecordsRes> selectIncomeRecords(Long payeeId, Long driverId) {
		if (null == payeeId && null == driverId) {
			return new TableInfo<>();
		}
		List<IncomeRecordsRes> list = customerTradeApplyMapperEx.selectCustomerTradeApplyListByPayee(payeeId, driverId);
		if (CommonUtil.isNullOrEmpty(list)) {
			list = Collections.emptyList();
		}
		return TableInfo.success(list);
	}

	@Override
	public Long countCustomerFlow(TradeFlowQueryReq req) {
		return customerTradeApplyMapperEx.countCustomerFlow(req);
	}

	public void mergeShippers(Long fromId, Long toId) {
		customerTradeApplyMapperEx.mergeShippersCommit(fromId, toId);
		customerTradeApplyMapperEx.mergeShippersAudit(fromId, toId);
	}

	@Override
	public List<PdsPayment> selectPaymentList(Long waybillId) {
		return customerTradeApplyMapperEx.selectPaymentList(waybillId);
	}

	public List<BillStatisticsInfo> billStatisticsListByCustomerIds(Map<String, Object> map) {
		return customerTradeApplyMapperEx.billStatisticsListByCustomerIds(map);
	}

	public List<BillStatisticsInfo> billStatisticsListByContractIds(Map<String, Object> map) {
		return customerTradeApplyMapperEx.billStatisticsListByContractIds(map);
	}

	public BigDecimal sumByTime(String thisMonth, String today, Long freightForwarderId) {
		return customerTradeApplyMapperEx.sumByTime(thisMonth, today, freightForwarderId);
	}
}
