package com.zly.project.settlement.domain.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zly.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ExcelIgnoreUnannotated
public class FlowRes {
	@ApiModelProperty("id")
	private String id;

	@ApiModelProperty("付款时间")
	private String submitTime;

	@ApiModelProperty("运单id")
	@Excel(name = "运单id")
	private String waybillId;

	@ApiModelProperty("运单号")
	@Excel(name = "运单号")
	@ExcelProperty(value = "运单号", index = 0)
	private String waybillCode;
	private String shippingNoteNumber;

	@ApiModelProperty("项目名称")
	@Excel(name = "项目名称")
	@ExcelProperty(value = "项目名称", index = 1)
	private String frameworkContractName;


	@ApiModelProperty("托运人名称")
	@Excel(name = "托运人名称")
	@ExcelProperty(value = "托运人名称", index = 2)
	private String customerName;

	@ApiModelProperty("付款单号")
	@Excel(name = "付款单号")
	@ExcelProperty(value = "付款单号", index = 3)
	private String applyPayCode;

	@ApiModelProperty("付款状态 3:已付款 4:付款失败 5:付款中")
	private String auditStatus;

	@Excel(name = "付款状态")
	@ExcelProperty(value = "付款状态", index = 4)
	private String auditStatusString;

	@ApiModelProperty("付款阶段（0、总费用；1、预付；2、到付、3回单付）")
	private String fareStage;

	@Excel(name = "付款阶段")
	@ExcelProperty(value = "付款阶段", index = 5)
	private String fareStageString;

	@ApiModelProperty("付款金额")
	@Excel(name = "付款金额")
	@ExcelProperty(value = "付款金额", index = 6)
	private String applyMoney;

	@ApiModelProperty("收款人")
	@Excel(name = "收款人")
	@ExcelProperty(value = "收款人", index = 7)
	private String payeeName;

	@ApiModelProperty("银行卡号")
	@Excel(name = "银行卡号")
	@ExcelProperty(value = "银行卡号", index = 8)
	private String bankCardNo;

	@ApiModelProperty("收款银行")
	@Excel(name = "收款银行")
	@ExcelProperty(value = "收款银行", index = 9)
	private String bankName;

	@ApiModelProperty("身份证号")
	@Excel(name = "身份证号")
	@ExcelProperty(value = "身份证号", index = 10)
	private String identityCard;

	@ApiModelProperty("付款时间")
	@Excel(name = "付款时间")
	@ExcelProperty(value = "付款时间", index = 11)
	private String payTime;

	@ApiModelProperty("备注")
	@Excel(name = "备注")
	@ExcelProperty(value = "备注", index = 12)
	private String waybillRemark;

	@ApiModelProperty("备注")
	private String remark;

	@ApiModelProperty("车牌号")
	private String vehicleNumber;
	@ApiModelProperty("司机姓名")
	private String driverName;
	private String actualCarrierName;
	private String loadingCity;
	private String loadingArea;
	private String unloadCity;
	private String unloadArea;

	private BigDecimal payFare;
	private Integer status;
	private Integer resource;
	private String frameworkContractCode;

}
