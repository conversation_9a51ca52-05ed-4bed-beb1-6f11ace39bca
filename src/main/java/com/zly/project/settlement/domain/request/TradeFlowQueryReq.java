package com.zly.project.settlement.domain.request;

import com.zly.framework.web.domain.QueryCommon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class TradeFlowQueryReq extends QueryCommon {
	// 平台端托运人端字段不一样，上面托运人用下面平台用
	@ApiModelProperty("运单号")
	private String waybillCode;

	@ApiModelProperty("合同名称")
	private String contractName;

	@ApiModelProperty("合同名称Id")
	private String contractId;
	private String frameworkContractId;

	@ApiModelProperty("托运人名称")
	private String customerName;

	@ApiModelProperty("托运人id")
	private Long customerId;

	@ApiModelProperty("账户名称")
	private String payeeName;
	private String payName;

	@ApiModelProperty("银行卡号")
	private String payeeAccountNo;
	private String collectBankNo;

	@ApiModelProperty("身份证")
	private String payeeCartNo;

	@ApiModelProperty("付款状态")
	private String state;

	@ApiModelProperty("付款时间")
	private String startTime;

	@ApiModelProperty("付款时间")
	private String endTime;

	@ApiModelProperty("网络货运人id")
	private Long freightForwarderId;

	@ApiModelProperty("结算状态  0:待结算  1:已结算")
	private Long settleStatus;

	private Integer type;

	private String filterItems;
	private Integer resource;
	private String originalDocumentNumber;
	private String waybillRemark;
	@ApiModelProperty("运单号多个")
	private List<String> shippingNoteNumbers;

	private List<String> list;
}
