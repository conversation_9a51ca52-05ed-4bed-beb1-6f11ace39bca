package com.zly.project.settlement.domain;

import com.zly.framework.aspectj.lang.annotation.Excel;
import com.zly.framework.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 商户银行卡对象 customer_bank_card
 *
 * <AUTHOR>
 * @date 2021-10-22
 */
public class CustomerBankCard extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/** ID */
	@ApiModelProperty("ID")
	private Long id;

	/** 用户ID,管理用户ID */
	@Excel(name = "用户ID,管理用户ID")
	@ApiModelProperty("用户ID,管理用户ID")
	private Long customerId;

	/** 银行卡号 */
	@Excel(name = "银行卡号")
	@ApiModelProperty("银行卡号")
	private String bankCardNo;

	/** 开户行联行号 */
	@Excel(name = "开户行联行号")
	@ApiModelProperty("开户行联行号")
	private String bankCode;

	/** 开户行 */
	@Excel(name = "开户行")
	@ApiModelProperty("开户行")
	private String bankName;

	/** 预留手机 */
	@Excel(name = "预留手机")
	@ApiModelProperty("预留手机")
	private String bankMobile;

	/** 对公/对私（:0：对私，1：对公） */
	@Excel(name = "对公/对私", readConverterExp = ":=0：对私，1：对公")
	@ApiModelProperty("对公/对私（:0：对私，1：对公）")
	private Integer isOther;

	/** 状态（0：有效，1：无效 -1：删除） */
	@Excel(name = "合同状态", readConverterExp = "0=：有效，1：无效,-=1：删除")
	@ApiModelProperty("合同状态（0：成功，1：审核中 -1：删除）")
	private Integer state;

	@ApiModelProperty("开户银行编号")
	private Long bankID;

	public Long getBankID() {
		return bankID;
	}

	public void setBankID(Long bankID) {
		this.bankID = bankID;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getId() {
		return id;
	}

	public void setCustomerId(Long customerId) {
		this.customerId = customerId;
	}

	public Long getCustomerId() {
		return customerId;
	}

	public void setBankCardNo(String bankCardNo) {
		this.bankCardNo = bankCardNo;
	}

	public String getBankCardNo() {
		return bankCardNo;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public String getBankCode() {
		return bankCode;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankMobile(String bankMobile) {
		this.bankMobile = bankMobile;
	}

	public String getBankMobile() {
		return bankMobile;
	}

	public void setIsOther(Integer isOther) {
		this.isOther = isOther;
	}

	public Integer getIsOther() {
		return isOther;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getState() {
		return state;
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId()).append("customerId", getCustomerId()).append("bankCardNo", getBankCardNo())
				.append("bankCode", getBankCode()).append("bankName", getBankName()).append("bankMobile", getBankMobile()).append("isOther", getIsOther()).append("remark", getRemark())
				.append("state", getState()).append("createBy", getCreateBy()).append("createTime", getCreateTime()).append("updateBy", getUpdateBy()).append("updateTime", getUpdateTime()).toString();
	}
}
