package com.zly.project.settlement.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: CorporateReq
 * @Description: 添加对公账户请求
 * @Author: xxx
 * @Date: 2025/7/23 09:59
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CorporateReq {

	@ApiModelProperty("托运人ID")
	private Long customerId;

	@ApiModelProperty("对公账户")
	private String corporateAccount;

	@ApiModelProperty("所示银行标ID")
	private Long bankId;
}
