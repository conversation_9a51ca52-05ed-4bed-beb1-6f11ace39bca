package com.zly.project.settlement.mapper;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.zly.project.settlement.domain.CustomerTradeApply;

/**
 * 运单费用申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-05-17
 */
@Repository
public interface CustomerTradeApplyMapper 
{
    /**
     * 查询运单费用申请
     * 
     * @param id 运单费用申请主键
     * @return 运单费用申请
     */
    public CustomerTradeApply selectCustomerTradeApplyById(Long id);

    /**
     * 查询运单费用申请列表
     * 
     * @param customerTradeApply 运单费用申请
     * @return 运单费用申请集合
     */
    public List<CustomerTradeApply> selectCustomerTradeApplyList(CustomerTradeApply customerTradeApply);

    /**
     * 新增运单费用申请
     * 
     * @param customerTradeApply 运单费用申请
     * @return 结果
     */
    public int insertCustomerTradeApply(CustomerTradeApply customerTradeApply);

    /**
     * 修改运单费用申请
     * 
     * @param customerTradeApply 运单费用申请
     * @return 结果
     */
    public int updateCustomerTradeApply(CustomerTradeApply customerTradeApply);

    /**
     * 删除运单费用申请
     * 
     * @param id 运单费用申请主键
     * @return 结果
     */
    public int deleteCustomerTradeApplyById(Long id);

    /**
     * 批量删除运单费用申请
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerTradeApplyByIds(Long[] ids);

    List<CustomerTradeApply> selectCustomerTradeApplyByIds(Long[] ids);

	List<CustomerTradeApply> selectCustomerTradeApplyByWaybillIds(List<Long> waybillIds);

	int insertCustomerTradeApplys(List<CustomerTradeApply> customerTradeApplies);

    int deleteCustomerTradeApplyByWaybillIds(List<Long> waybillIds);
}
