package com.zly.project.settlement.mapper;

import com.zly.project.settlement.domain.CustomerAccount;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 客户账户Mapper接口
 *
 * <AUTHOR>
 * @date 2021-10-22
 */
@Repository
public interface CustomerAccountMapper {
	/**
	 * 查询客户账户
	 *
	 * @param id
	 * 		客户账户主键
	 * @return 客户账户
	 */
	public CustomerAccount selectCustomerAccountById(Long id);

	/**
	 * 查询客户账户列表
	 *
	 * @param customerAccount
	 * 		客户账户
	 * @return 客户账户集合
	 */
	public List<CustomerAccount> selectCustomerAccountList(CustomerAccount customerAccount);

	/**
	 * 通过客户证件号码查询非删除状态的客户账户列表
	 *
	 * @param CreditCode
	 * 		客户证件号码
	 * @return 客户账户集合
	 */
	public List<CustomerAccount> selectCustomerAccountListByCreditCode(String CreditCode);

	/**
	 * 新增客户账户
	 *
	 * @param customerAccount
	 * 		客户账户
	 * @return 结果
	 */
	public int insertCustomerAccount(CustomerAccount customerAccount);

	/**
	 * 修改客户账户
	 *
	 * @param customerAccount
	 * 		客户账户
	 * @return 结果
	 */
	public int updateCustomerAccount(CustomerAccount customerAccount);

	/**
	 * 删除客户账户
	 *
	 * @param id
	 * 		客户账户主键
	 * @return 结果
	 */
	public int deleteCustomerAccountById(Long id);

	/**
	 * 批量删除客户账户
	 *
	 * @param ids
	 * 		需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteCustomerAccountByIds(Long[] ids);

	List<CustomerAccount> selectCustomerAccountByIds(List<Long> customerIds);

	int insertCustomerAccounts(List<CustomerAccount> customerAccounts);

	List<CustomerAccount> selectCustomerAccountByIdCards(List<String> idcards);

}
