package com.zly.project.settlement.mapper;

import com.zly.project.miniprogram.domain.res.IncomeRecordsRes;
import com.zly.project.settlement.domain.CustomerTradeApply;
import com.zly.project.settlement.domain.request.TradeFlowQueryReq;
import com.zly.project.settlement.domain.response.FlowRes;
import com.zly.project.settlement.domain.response.FlowResEx;
import com.zly.project.statistics.domain.BillStatisticsInfo;
import com.zly.project.taxUpload.domain.PdsPayment;
import com.zly.project.transport.waybill.domain.res.WaybillApplyRes;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 运单费用申请Mapper接口
 *
 * <AUTHOR>
 * @date 2022-05-17
 */
@Repository
public interface CustomerTradeApplyMapperEx {

	/**
	 * 付款审核
	 *
	 * @param waybillIds
	 *            运单ID集合
	 * @param auditStatus
	 *            审核状态(1:审核通过 2:审核不通过)
	 * @param userName
	 *            审核人
	 * @return
	 */
	int tradeAudit(Long[] waybillIds, int auditStatus, Date auditTime, String userName, String remark);

	int huaXiaTradeAudit(Long[] waybillIds, int auditStatus, Date auditTime, String userName, String remark);

	/**
	 * 付款审核
	 *
	 * @param ids
	 *            ID集合
	 * @param userName
	 *            审核人
	 * @return
	 */
	int tradeRevoke(Long[] ids, String userName, String remark);

	/**
	 * 根据ID查询付款申请
	 *
	 * @param ids
	 * @return
	 */
	List<CustomerTradeApply> selectCustomerTradeApplyListByIds(List<String> ids);

	/**
	 * 运单是否有已申请付款的（0:待审核 1:审核通过（待付款）3:已付款 4:付款失败 5:付款中）
	 *
	 * @param ids
	 *            运单ID
	 * @return
	 */
	boolean existPaymentApply(List<Long> ids);

	/**
	 * 检查运单的申请付款状态
	 *
	 * @param ids
	 *            运单ID集合
	 * @return 结果
	 */
	int tradeApplyVerify(@Param("ids") Long[] ids);

	/**
	 * 更新运单付款状态
	 *
	 * @param ids
	 * @return
	 */
	int updateCustomerTradeApplyByWaybillId(Long[] ids);

	List<FlowRes> queryCustomerFlow(TradeFlowQueryReq req);

	List<FlowRes> queryplatformFlow(TradeFlowQueryReq req);

	List<FlowResEx> queryCustomerFlowWithWaybill(TradeFlowQueryReq req);

	List<FlowResEx> queryCustomerFlowWithWaybillSP(TradeFlowQueryReq req);

	List<FlowResEx> queryplatformFlowWithWaybill(TradeFlowQueryReq req);

	/**
	 * 查询收款人
	 *
	 * @param payeeId
	 * @param driverId
	 * @return
	 */
	List<IncomeRecordsRes> selectCustomerTradeApplyListByPayee(Long payeeId, Long driverId);

	/**
	 * 查询运单的支付记录
	 * @param id
	 * @return
	 */
	List<WaybillApplyRes> queryCustomerFlowByWaybillId(@NotNull Long id);

	String queryReasonByWaybillId(Long id);

	Long countCustomerFlow(TradeFlowQueryReq req);

	void mergeShippersCommit(@Param("fromId") Long fromId, @Param("toId") Long toId);

	void mergeShippersAudit(@Param("fromId") Long fromId, @Param("toId") Long toId);

	int updateFinancingStatusByWaybillIds(@Param("waybillIds") List<Long> waybillIds, @Param("financingStatus") Integer financingStatus);

	List<PdsPayment> selectPaymentList(Long waybillId);

	List<BillStatisticsInfo> billStatisticsListByCustomerIds(Map<String, Object> map);

	List<BillStatisticsInfo> billStatisticsListByContractIds(Map<String, Object> map);

	BigDecimal sumByTime(@Param("start") String thisMonth, @Param("end") String today, @Param("freightForwarderId") Long freightForwarderId);

}
