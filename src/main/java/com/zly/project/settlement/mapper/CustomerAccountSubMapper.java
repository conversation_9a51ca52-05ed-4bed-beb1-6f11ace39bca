package com.zly.project.settlement.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.zly.project.settlement.domain.CustomerAccountSub;

/**
 * 客户账户Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-07-04
 */
@Repository
public interface CustomerAccountSubMapper 
{
    /**
     * 查询客户账户
     * 
     * @param id 客户账户主键
     * @return 客户账户
     */
    public CustomerAccountSub selectCustomerAccountSubById(Long id);

    /**
     * 查询客户账户列表
     * 
     * @param customerAccountSub 客户账户
     * @return 客户账户集合
     */
    public List<CustomerAccountSub> selectCustomerAccountSubList(CustomerAccountSub customerAccountSub);

    /**
     * 新增客户账户
     * 
     * @param customerAccountSub 客户账户
     * @return 结果
     */
    public int insertCustomerAccountSub(CustomerAccountSub customerAccountSub);

    /**
     * 修改客户账户
     * 
     * @param customerAccountSub 客户账户
     * @return 结果
     */
    public int updateCustomerAccountSub(CustomerAccountSub customerAccountSub);

    /**
     * 删除客户账户
     * 
     * @param id 客户账户主键
     * @return 结果
     */
    public int deleteCustomerAccountSubById(Long id);

    /**
     * 批量删除客户账户
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerAccountSubByIds(Long[] ids);

	List<CustomerAccountSub> selectCustomerAccountSubByContractIds(List<Long> contractIds);

	int insertCustomerAccountSubs(List<CustomerAccountSub> customerAccountSubs);

    int updateContractNameByContractId(@Param("contractId") Long contractId, @Param("contractName")String contractName);
}
