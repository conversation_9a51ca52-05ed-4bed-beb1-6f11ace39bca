package com.zly.project.settlement.mapper;

import com.zly.project.settlement.domain.CustomerBankCard;

import java.util.List;

/**
 * 商户银行卡Mapper接口
 *
 * <AUTHOR>
 * @date 2021-10-22
 */
public interface CustomerBankCardMapper {
	/**
	 * 查询商户银行卡
	 *
	 * @param id
	 * 		商户银行卡主键
	 * @return 商户银行卡
	 */
	public CustomerBankCard selectCustomerBankCardById(Long id);

	/**
	 * 查询商户银行卡列表
	 *
	 * @param customerBankCard
	 * 		商户银行卡
	 * @return 商户银行卡集合
	 */
	public List<CustomerBankCard> selectCustomerBankCardList(CustomerBankCard customerBankCard);

	/**
	 * 通过托运人ID查询非删除状态的商户银行卡列表
	 *
	 * @param customerId
	 * 		托运人ID
	 * @return 商户银行卡集合
	 */
	public List<CustomerBankCard> selectCustomerBankCardListByCustomerId(Long customerId);

	/**
	 * 新增商户银行卡
	 *
	 * @param customerBankCard
	 * 		商户银行卡
	 * @return 结果
	 */
	public int insertCustomerBankCard(CustomerBankCard customerBankCard);

	/**
	 * 修改商户银行卡
	 *
	 * @param customerBankCard
	 * 		商户银行卡
	 * @return 结果
	 */
	public int updateCustomerBankCard(CustomerBankCard customerBankCard);

	/**
	 * 删除商户银行卡
	 *
	 * @param id
	 * 		商户银行卡主键
	 * @return 结果
	 */
	public int deleteCustomerBankCardById(Long id);

	/**
	 * 批量删除商户银行卡
	 *
	 * @param ids
	 * 		需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteCustomerBankCardByIds(Long[] ids);
}
