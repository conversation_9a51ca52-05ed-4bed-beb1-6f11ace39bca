# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080，8081，生产。。8089 准生产.. 每次发要改端口
  port: 8081
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100
########## logback-{xxx}.xml 文件打包在项目内部 ##########
logging:
  config: classpath:logback/logback-test.xml

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: false
  # 请求前缀
  pathMapping:
  username: zly-swagger
  password: 51f9CmbDih%B4EcVLlI!$@

# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        # url: jdbc:mysql://************:9010/zly_freight_network?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=Asia/Shanghai
        # username: cltx
        # password: cltxCzzlyTms1qaz@WSX
        url: jdbc:mysql://**************:9010/zly_freight_network?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=Asia/Shanghai
        username: cltx
        password: cltxCzzlyTms1qaz@WSX
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 4.0集团数据库（善道使用中数据库）
      shandaofrom:
        enabled: true
        url: jdbc:mysql://************:9010/zly_freight_network_test?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=Asia/Shanghai
        username: cltx
        password: cltxCzzlyTms1qaz@WSX
      # 初始连接数
      initialSize: 20
      # 最小连接池数量
      minIdle: 20
      # 最大连接池数量
      maxActive: 500
      # 配置获取连接等待超时的时间
      maxWait: 5000
      # 启用连接泄漏检测
      # 自动回收未关闭的连接
      removeAbandoned: true
      # 300秒未关闭则强制回收（单位：秒）
      removeAbandonedTimeout: 300
      # 记录泄漏堆栈，定位问题代码
      logAbandoned: true
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 60000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 120000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 30000
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: false
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: zly-druid-u
        login-password: Qt&%rKFwAxo8#eE3
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 3000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # redis 配置
  redis:
    # 地址
    host: **************
    # 端口，默认为6379
    port: 5438
    # 数据库索引
    database: 15
    # 密码
    password: cltx456/*-
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  #对于rabbitMQ的配置
  rabbitmq:
    host: **************
    port: 5672
    username: admin
    password: admin1qaz@WSX
    virtual-host: /
    # publisher-confirms: true
    publisher-confirm-type: correlated
    publisher-returns: true

#以下是自定义配置
custom:
  #网络货运经营者信息
  wlhy:
    #托运人端地址
    loginUrl: http://freightshipper.51zlyun.cn/
  #网络货运数据上传配置
  wlhy2:
    #发送电子运单报文地址
    sendWaybillUrl: http://motupload.56zly.cn/mot/upload/waybill
    #发送资金流水单报文地址
    sendPayFlowUrl: http://motupload.56zly.cn/mot/upload/payFlow
    #发送车辆基本信息报文地址
    sendVehicleUrl: http://motupload.56zly.cn/mot/upload/vehicle
    #发送驾驶员基本信息报文地址
    sendDriverUrl: http://motupload.56zly.cn/mot/upload/driver
    #江苏省发送运单信息报文地址
    sendJsWaybillUrl: http://motupload.56zly.cn/mot/upload/js/waybill
  #七牛相关配置
  qiniu:
    accessKey: hffeLiXlirnt_lyYEVkyo1UMpBm9v7WF7zjDYpxT
    secretKey: tVWGHsoWlqOfN_5VkzGhmKfFd3Bt5FU49bA9AiHc
    domain: https://wlhyfile.56zly.com/
    bucket: wlhy
    #存储区域 华东:0 华北:1 华南:2
    region: 0
  #gps接口相关配置
  gps:
    acc: tms
    pas: 123
    url: http://gps.56zly.cn/gps
    lastLocation: /findNewLocationV3
    historyTrack: /vHisTrackV3
    checkExist: /verifyV2
    historyLocation: /findHistoryTrackV3
    trackList: /list
  #ocr接口相关配置
  ocr:
    acc: tms
    pas: 123
    url: http://ocr.56zly.cn/ocr/picRecognition
    picTypeUrl: http://ocr.56zly.cn/ocr/picTypeRecognition
  #微信小程序 APPID
  vx:
    appID: 'wxaffcecf100c075e4'
    appSecret: '3f40fc8ab19df1eb0c1eb5ae3edca944'
  vxs:
    appID: 'wxa1a5fdb68d4d22f6'
    appSecret: '3336392d65e6fac50fac786b155ba1c5'
  #善道的司机端
  sdvx:
    appID: 'wxd841f6613608485f'
    appSecret: '2add784395f1abe1eeacb482c6108387'
  #善道的托运人端
  sdvxs:
    appID: 'wxbb1f9a7306f50df5'
    appSecret: 'fd1260644dc51bff2bab2c79f5f58481'
  # 实人认证百分比例
  realPerson:
    realPersonPercentage: 0
  #高德地图API配置
  amap:
    webServerKey: b4ef1b65f0ca5eb684a70952d1c56210
    distanceUrl: https://restapi.amap.com/v3/distance
    geocodeUrl: https://restapi.amap.com/v3/geocode/regeo
    geoUrl: https://restapi.amap.com/v3/geocode/geo
    truckDirectionUrl: https://restapi.amap.com/v4/direction/truck
    drivingDirectionUrl: https://restapi.amap.com/v5/direction/driving
    placeTextUrl: https://restapi.amap.com/v5/place/text
  iphone:
    url: https://sms.56zly.com/sms/send
    acc: tms
    pas: 123
  juhe:
    url: http://v.juhe.cn/verifybankcard/query
    key: ec038abe77a935e083907838a819997a
  juhe3:
    url: http://v.juhe.cn/verifybankcard3/query
    key: b0adbcc367d2ec45cfef85aac6b58946
  identity:
    Url: http://op.juhe.cn/idcard/query
    key: 7ee5bebe0387cba24efbdb4803ce7c97
  # 智联云服务平台
  scsp:
    acc: wlhy
    pas: wlhy1qaz@WSX
    # 服务费账户余额
    balanceUrl: http://**************:8080/wlhy/balance
    # 账户服务费充值记录
    payRankUrl: http://**************:8080/wlhy/payRank
    # 姓名身份证号校验服务
    idcardVerifyUrl: http://verify.56zly.cn/verify/idcard
    # 小程序实人认证
    personAuthenticationUrl: http://verify.56zly.cn/verify/face
    # 银行卡二要素校验服务（暂未提供）
    bankcard2VerifyUrl:
    # 银行卡三要素校验服务
    bankcard3VerifyUrl: http://verify.56zly.cn/verify/bankcard3
    # 银行卡四要素校验服务（暂未使用）
    bankcard4VerifyUrl: http://verify.56zly.cn/verify/bankcard4
  xiaoyudian:
    acc: tms
    pas: 123
    url: http://10.111.107.162:8087
    # 账单信息同步
    billUpload: /xyd/lbfpBillUpload
    # 获取文件上传链接接口
    fileUpload: /xyd/lbfpFileUpload
    # 批量发票信息同步
    invoiceBatchUpload: /xyd/lbfpInvoiceBatchUpload
    # 发票信息同步
    invoiceUpload: /xyd/lbfpInvoiceUpload
    # 获取客户端登录链接
    payUrl: /xyd/lbfpPayUrl
    # 项目合同信息同步
    projectContract: /xyd/lbfpProjectContract
    # 小雨点4.0 项目上传
    projectUpload: /xyd/lbfpProjectContractFour
    # 额度信息查询
    queryQuota: /xyd/lbfpQueryQuota
    # 第三方轨迹信息上传
    tackinfoUpload: /xyd/lbfpTackinfoUpload
    # 运输线路信息同步
    transportLineUpload: /xyd/lbfpTransportLineUpload
    # 运单更新
    waybillUpdate: /xyd/lbfpWaybillUpdate
    # 运单上传
    waybillUpload: /xyd/lbfpWaybillUpload
    # 小雨点查询运单接口
    queryWaybill: /xyd/xydQueryWaybill
    # 小雨点上传数据综合接口
    uploadData: /xyd/xydUploadData
  # 长江航运代开
  cjhyProxy:
    acc: tms
    pas: 123
    url: http://47.97.113.79:8087
    # 一步签署（实人认证、代开协议、运输协议合并成一步签署）
    getOneStepSign: /proxy/invoice/getOneStepSign
    # 实人认证状态查询
    queryRealSignStatus: /proxy/invoice/queryRealSignStatus
    # 代开协议历史记录查询
    queryProxyAgree: /proxy/invoice/queryProxyAgree
    # 运输协议查询
    queryOrderAgree: /proxy/invoice/queryOrderAgree
    # 运单全量信息上传申请开票，批量上传
    uploadWaybillFullInfo: /proxy/invoice/uploadWaybillFullInfo
    # 开票状态查询，按运单批量查询
    getWaybillInvoiceStatus: /proxy/invoice/getWaybillInvoiceStatus
    # 运单审核状态查询，按运单批量查询
    getWaybillAuditStatus: /proxy/invoice/getWaybillAuditStatus
    # 微信端签约完成后回调地址
    wxCallBackUrl:
    # pc端签约完成后回调地址
    pcCallBackUrl:
  license:
    #校验授权码地址  http://**************:9191/license-platform/cipher/checkLicense
    path: http://**************:9191/license-platform/cipher/checkLicense
    #公钥目录 示例（ Windows配置D:/zly/license/license.pub，Linux配置 /home/<USER>/license/license.pub）
    pub: /home/<USER>/license/license.pub
    #授权码 示例（ Windows配置D:/zly/license/license.key，Linux配置 /home/<USER>/license/license.key）
    licensePath: /home/<USER>/license/license.key
  #运单推送
  waybill:
    path: http://service.56zly.cn/truckWaybill/add
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCfgTM0M9nxwBmz6/dMyv0y9DaV7LFk3DKzSgBXBGF99Z3GFZuFIw3edevuBgqLIOYZlKbjcaevD/0ESW8qUZeHqHoGGcntFxLojbmsGVKhfMyviVliXw5LLW6rJX/gyVh2weAACHhidEDMoMDOe7jJycADDcZuWsarqCPXkIkIYwIDAQAB
    acc: yyq
    pas: 123
    #tms商户及密码
    account: lt1_admin
    password: 1qaz@WSX
    #商户id
    tenantryId: *************
    #项目id
    projectId: 532136609798311936
    me:
      publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALSerRlxXTzaHZHv84gHAhW8qHFuEfsFx7Hb4IDOVBtMqXdGcWtD0eyISLNr0gILU7sQEMSoxwFVTgBwzOfAhPMCAwEAAQ==
      privateKey: MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAtJ6tGXFdPNodke/ziAcCFbyocW4R+wXHsdvggM5UG0ypd0Zxa0PR7IhIs2vSAgtTuxAQxKjHAVVOAHDM58CE8wIDAQABAkEAkE6KjF5nmPvHTlcBIrg+sTFG/0Fq+jz2/KJo72BqEFhJ/WBvD1T8Z8gCoSepVv6E4OmBNsiwd/m+Fs/mTiDJcQIhAOkue+b2gujGpMg8VjQgT1JxawfMjyT52q1JwHFAg+4ZAiEAxkty5CS83SL0Aqq7WeFnElQ/UOMMSMtbNbtUKHvDFOsCIH6jCBc++YmBmCxG2Gomwe18OyxZI8RpDSHfU+wtpXjBAiBAc0NrfblZEsRGnaF5TDbJEVLnAnT6sAX8R9/JrLgycwIgZVN1kWaY27si8afwmo+tmeRmWGXp3JHe5+R2HkXdfBA=
  bwy:
    bwyLoginResult: https://invoice.56zly.com/BWY/eInvoice/getLoginResult
    bwyUploadBill: http://invoice.56zly.cn/BWY/eInvoice/createEInvoice
    bwyFindBill: http://invoice.56zly.cn/BWY/eInvoice/queryEInvoice
    bwyAcc: tms
    bwyPas: 123
    bwyTaxNo: 91320411MACKXC2Q5U
    bwyName: 江苏百望云信息技术有限公司
  #etc申请
  etc:
    url: http://etckp.56zly.cn
    etcWaybillStart: /waybill/waybillStart
    etcWaybillEnd: /waybill/waybillEnd
    queryBillInvoice: /baiwang/queryBillInvoice
  #华夏银行数贷通接口相关配置
  hxb:
    acc: tms
    pas: 123
    hxbCustomerInfoPushUrl: http://************:8088/customerApply/push/customerInfo
    hxbCreditFileUploadUrl: http://************:8088/customerApply/push/creditApprovalFileByData
    url: http://************:8088
    # 支用申请信息
    disbursePushApplyInfo: /disburse/push/applyInfo
    # 只用风控信息
    disbursePushRiskInfo: /disburse/push/riskInfo

#es配置
es:
  host: *************
  port: 9329
  username: elastic
  password: Q7HSV#Ykb2xqG+:z?*aV

# 平台业务参数
# 支付通道（ALI-阿里，HUA_XIA-华夏银行）
platform:
  payment:
    channel: HUA_XIA
  pay:
    pds:
      bankCode: ************
      bankName: 华夏银行股份有限公司
      url: http://**************:8192/pds/auto/procees
      # url: http://*************:9043/pds/auto/procees
      #PDS用户名
      header:
        acc: pdsAcc
        #PDS密码
        pas: pdsPas
      pdsApiUrl: http://**************:8192/
      #付款异常查询接口
      payState: /pds/query/payState
      #付款中查询
      payment: /pds/query/MQAG
      #商户绑定银行卡
      bindBankCardUrl: /pds/account/bindBankCard
      #MQ提现通知
      withdrawUrl: /test/withdraw
      #PDS公钥
      publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDD+iPd9+y/fxiGXd19m+OfEzGit0jLtUkcHXhWzsCj/4xp+8fuqysZqiTPOoWJVgI5xnbqz1jPDgs6WpLHzWiAV4VdEM86vLoMaqxwu/KrG+qVzB376mMOQS8+DNDERzRBu5h8iXkOsRFyetlGFPgm40+fmRjZyTNzLoEkCu10ZwIDAQAB
    account:
      #account用户名
      header:
        acc: tms
        #PDS密码
        pas: 123
      url: http://**************:8180/
      # account的retract路径
      retract: /trade/retract?pas=QAZwsx!@%23123&isForcibly=y
      # account的rechargeAccount路径
      rechargeAccount: /account/rechargeAccount
      # account的mqAg路径
      mqAg: /test/mqAg
      #交易撤回操作
      transactionWithdrawalUrl: /trade/retract
      #PDS公钥
      publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDD+iPd9+y/fxiGXd19m+OfEzGit0jLtUkcHXhWzsCj/4xp+8fuqysZqiTPOoWJVgI5xnbqz1jPDgs6WpLHzWiAV4VdEM86vLoMaqxwu/KrG+qVzB376mMOQS8+DNDERzRBu5h8iXkOsRFyetlGFPgm40+fmRjZyTNzLoEkCu10ZwIDAQAB
    task:
      ## 附件下载地址
      downloadUrl: http://**************:8182/
    me:
      privateKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDD+iPd9+y/fxiGXd19m+OfEzGit0jLtUkcHXhWzsCj/4xp+8fuqysZqiTPOoWJVgI5xnbqz1jPDgs6WpLHzWiAV4VdEM86vLoMaqxwu/KrG+qVzB376mMOQS8+DNDERzRBu5h8iXkOsRFyetlGFPgm40+fmRjZyTNzLoEkCu10ZwIDAQAB
      publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDD+iPd9+y/fxiGXd19m+OfEzGit0jLtUkcHXhWzsCj/4xp+8fuqysZqiTPOoWJVgI5xnbqz1jPDgs6WpLHzWiAV4VdEM86vLoMaqxwu/KrG+qVzB376mMOQS8+DNDERzRBu5h8iXkOsRFyetlGFPgm40+fmRjZyTNzLoEkCu10ZwIDAQAB


########ali网商参数配置############

# 网商开户的平台参数
ali:
  platform:
    contact:
      company: 车联天下物流集团有限公司
      identity: 91320400MA1N7PA26G
      mobile: ***********
      name: 王磊

# 测试环境参数（stable）
mybank:
  api:
    # 应用ID
    appId: ****************
    # 合作方机构号
    isvOrgId: 202211000000000000784
    #ISV请求报文签名私钥
    privateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC+DjjKuBYNScrhp7/+yf/vqsZbyWAMuvLZ76rv9wLA3XTCY1Cb3xxm7xvux/OzhzDlQdURRrEhjjsVdP9Bx0Ig5yfGyDec/4Z50dcRgqsu17QU/m2WXHEwFd4yBAGomWXzy/q7+8JJ9YoTraSDfKhfV3qWx7/vBzqk0QNzZ3AgFwSMiP5Azz8K2GhkHWxbVLlji9+/oRL5yj13a8P9bbtIrdUB/PZ+Fk3klxhBL9Vxii0YmX96nwVuJgkmgyiRCwmLhIQBFdL7H63cNgUi9UoCsFC6lV4ErEcHCUFsLqggukKPmTlr4KSnFvf9bZoIYC9I7XBWcM+KDGTSdxeGpXbvAgMBAAECggEABIJr4It7oncUxEPpr071rqcbq8PcbpDlADzKjoUK4K6gfZhDql8h2mNkA0dlReY4R8hHGPDXdRdd2YV8JQBoVkWF0RahEy2Q8EUFWFoEW8ksca8TxJSO7vgl3IPx0iFJpP47BcjUdFLKIutk0uXbTN/Tfc5hhHdkcdKvxUY4B9rZYSNI5+YdIGjJj/nBWNGBndIqKnLfs2AhpCqPkADC3xo73fxiM/77TPB7RiaFeK9jk80kLAwaZeOKArCIY7YSpnyhqvPi4kO9vxTeGGHONhN+mfAsMG94AY83vZ6qhUiZZuofslbUt7beOw2PTmzGi8jXDI8k6WVHKqNnwYt/+QKBgQD/fT8sG3oJyRpvfhc/gPM+ovYGL3MH+6B3Gtq32K+ZyAmtoHnfLFkaa1bOLUEklvr2xViFTU4NlUyEWNE1jGW1JFC9AhU3JUnVKmiG/BSURH8neYsNUQ/yKOmjRyTvQmELp2Qb7h46ZWM6kWOzrJtiXMvkoAjWwgO/4YOxEazJdQKBgQC+b3zZW/yh/cOTCAzfN8AR/luSq9TJ7D8CZxHVOpXRL3YP950SUWEfuyOby4ddhFDwU3b6+BhtOtClMrBKO2Q9wbPVjVD5plGlhhMaLiNJmbFL63hEazrMOE3UVbOY8EYBJyyeUguqiYyRJkUbiVubRsdbFLUbGai8qWg5BvTOUwKBgEFOUoeDvn4h2ZAGOwsQexzXquuJ1W2E9E99ncrAqKI2b8Lh8kUJoP0P0vCAwNYJgbzyVN4+FGWEdDqgOVnmuVjEH58wmRuvfF/wpydZ6Ci+GYKNnu2Yeur7aj1CQj6mSQghkYVSKIfkwqiF4WZcCJvr/HJENf4vOaYijvcD/ZbBAoGBAKoZFyFnIq7m5cvtAuJW/76SveSyiuyZkmZo/erB25PvmrsEZ043VlNrapD8KLsFNu6S/tGIzPiz8i28qu6DQjRPUnxLL6ruPjtlGKbn0ykomM7BUrl6Nhi3qf0hV7wh0cWx4g7AJh97oQz9a/j+pc56WBMo2eOM9cUeZDOb3Qp1AoGAQwB8ndcxkt4UCfC+87Rpy5yrxVuaVKDZfTIy4qpuLrHRqCIHvAl41EnJTE8MPIemXhdTmPeLmGWe7KjGDo0F0GPRqBMVKrLrCnM52FUx/Wp5P2h8ZVXvm7TR5/b3jhCb7qC0gjey+HunyEn78H9Vxty4XzcSlF0zu8uVUSgwIJ8=
    # 网商银行网关公钥
    mybankPublicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDOb4B1dnwONcW0RoJMa0IOq3O6jiqnTGLUpxEw2xJg+c7wsb6DBy5CAoR0w2ZjZ/BjKxGIQ+DoDg3NsHJeyuEjNF0/Ro/R5xVpFC5z4cBVSC2/gddz4a1EoGDJewML/Iv0yIw7ylB86++h23nRd079c5S9RZXurBfnLW2Srhqk2QIDAQAB
    # 交互报文格式 目前固定
    format: XML
    # 交互报文字符编码 目前固定
    charset: UTF-8
    #交互报文签名方式 目前固定
    signType: RSA
    # 以下接口请求地址
    # ant.mybank.merchantprod.merchant.register
    # ant.mybank.merchantprod.merchant.register.query
    # ant.mybank.merchantprod.merchant.updateMerchant
    # ant.mybank.merchantprod.merchant.query
    # ant.mybank.merchantprod.merchant.addMerchantConfig
    # ant.mybank.merchantprod.merchant.queryOpenId
    # ant.mybank.bkmerchanttrade.pay
    # ant.mybank.bkmerchanttrade.prePay
    # ant.mybank.bkmerchanttrade.dynamicOrder
    # ant.mybank.bkmerchanttrade.payQuery
    # ant.mybank.bkmerchanttrade.payClose
    # ant.mybank.bkmerchanttrade.payCancel
    spServerUrl: https://fcsupergwlitedev.dl.szulodev.com/open/api/common/request2.htm
    # 极简网络公共请求地址
    # 平台子户开立
    # 司机代发代发、平台手续费结算
    # 代发、结算查询
    # 交易回单申请
    # 交易回单查询
    bkServerUrl: http://bkgwdev.dl.alipaydev.com/open/lite/api/common/request.htm
    # 图片上传功能网关请求地址
    uploadphotoUrl: http://bkgwdev.dl.alipaydev.com/ant/mybank/merchantprod/merchant/uploadphoto.htm
    # 对公账户打款验证服务 - 打款申请网关请求地址
    kybApplyUrl: http://bkgwdev.dl.alipaydev.com/bkmerchantprod/kyb/apply.htm
    # 对公账户打款验证服务 - 打款验证网关请求地址
    kybMatchUrl: http://bkgwdev.dl.alipaydev.com/open/api/common/request.htm
    # 排除以上API接口、功能之外的其他接口请求地址
    # 平台子户下挂固定收款编号开立
    # 来账退款申请
    # 退款查询
    # 子户余额查询接口
    # 离线对账单文件查询
    serverUrl: https://fcsupergw.mybank.dl.alipaydev.com/open/api/common/request2.htm

#### 附件等生成的路径
file:
  contract:
    waybill: /tmp/app/waybill/


# word转PDF的令牌
aspose:
  license: license/license.xml
  # 字体目录
  fontSettings: /home/<USER>/fonts
  pdf:
    # pdf保存地址
    outPath: /tmp/app/pdf/
  html:
    # html保存地址
    outPath: /tmp/app/html/
  word:
    # word输出地址
    outPath: /tmp/app/word/
    # 三方货源运单签约合约word模板地址
    templatePath3Party: /home/<USER>/template/waybillsign_3party.docx
    # 大宗货源运单签约合约word模板地址
    templatePathBulk: /home/<USER>/template/waybillsign_bulk.docx
    # 大宗货源运单代签约合约word模板地址
    templatePathDai: /home/<USER>/template/waybillsign_dai.docx
    # 承运人大合同word模板地址
    templatePathBig: /home/<USER>/template/waybillsign_big.docx
    # word车队长雇佣模板地址
    templatehirePath: /home/<USER>/template/hiresign.docx
    # word辅助员雇佣模板地址
    templateAuxiliaryHirePath: /home/<USER>/template/auxiliaryHireSign.docx
    # word代开协议地址
    templateProxyAgreementPath: /home/<USER>/template/proxyAgreementSign.docx
    # 善道司机雇佣模板地址
    templateDriverPath: /home/<USER>/template/driver.docx
    # 善道车辆雇佣模板地址
    templateVehiclePath: /home/<USER>/template/vehicle.docx
    # 回单下载模板地址
    templateReceiptPath: /home/<USER>/template/receipt.docx
    # 善道货物运输协议模板地址
    templateJssdzyAgreementPath: /home/<USER>/template/wlhyupload_agreement_template.docx
    # 车主声明模板地址
    templateCarOwnerDeclarationPath: /home/<USER>/template/carOwnerDeclaration.docx
    # 委托付款模板
    templateEntrustmentContractPath: /home/<USER>/template/entrustmentContract.docx
    # 运单运输合同协议模板
    templateAgreementTemplatePath: /home/<USER>/template/agreement_template.docx
    templateAgreementTemplateShuiWuPath: /home/<USER>/template/agreement_template_ShuiWu.docx
    # 亲属代收协议模板
    templateDriverFamilyPath: /home/<USER>/template/driver_family.docx
    # 善道合同
    templatePath3PartySD: /home/<USER>/template/waybillsign_3party_SD.docx
    templatePathBulkSD: /home/<USER>/template/waybillsign_bulk_SD.docx
    templateAgreementTemplatePathSD: /home/<USER>/template/agreement_template_SD.docx
    templateAgreementTemplateShuiWuPathSD: /home/<USER>/template/agreement_template_SD_ShuiWu.docx

dd:
  corpId: ding57e9528ca4e170c2f2c783f7214b6d69
  # appkey: dinghtqrznbhj5auxewf
  # appsecret: 9SPfmHBFNCDmoY9Lr0ND1Mmu2RKlYZdc_7OJxab-keoWnjcmgmm9UG3SCD22Vfut
  # aesKey: LvzmyYjcKQNjANfHdiphZqBbp0eoEoGgyL92AekeBj9
  # token: ksBpQc0jEJdE8ei6uxTwWatraeCpULOm9KqIcXibuj0nOPmOnj
  # agentid: 2742246368
  appkey: dingfiayujemmxnmicvy
  appsecret: zmHFsl244Y30RHRz-cYztPDtw7y2926XkA5Y5K_OIZwW1T4OqlCdVM3_JsgZcVOP
  aesKey: JQf1IBFkKpCoN2K9bWLDv0rQLYvk2kWZbP1qtuvizyR
  token: oCpsNb7ilOfM
  agentid: 2473324312
  localPath: /tmp/freightNetWord
  serverHost: http://127.0.0.1
  url:
    getToken: https://oapi.dingtalk.com/gettoken
    getTicket: https://oapi.dingtalk.com/get_jsapi_ticket
    getByMobile: https://oapi.dingtalk.com/topapi/v2/user/getbymobile
    getDeptList: https://oapi.dingtalk.com/topapi/v2/department/listsub
    getUserList: https://oapi.dingtalk.com/topapi/v2/user/list
    getUserInfo: https://oapi.dingtalk.com/topapi/v2/user/get
    getProcessinstance: https://oapi.dingtalk.com/topapi/processinstance/get
    getSpaceId: https://oapi.dingtalk.com/topapi/processinstance/cspace/info

# 保险配置
policy:
  remotePolicyURL: http://insurance.56zly.cn/insure/add
  policyListURL: http://insurance.56zly.cn/insure/list
  acc: tms
  pas: 123
  policyQueryURL: http://insurance.56zly.cn/insure/find
  policyNo: TEST
  tenantryNames: 江苏车联天下供应链管理有限公司,常州交投物流产业发展有限公司,湖北车联天下物流有限公司,武汉车联供应链管理有限公司,广州车联天下供应链管理有限公司,江苏车联天下供应链管理有限公司四川分公司,江苏车联居上速运有限公司,深圳车联天下云仓供应链管理有限公司,上海车联沪邦供应链管理有限公司,河南车联天下供应链管理有限公司
  rate: 0.0025
  newPolicyDate: 2018-08-26 00:00:00
  policyTypeList: http://insurance.56zly.cn/insure/list
  typeAndPakage: http://insurance.56zly.cn/insureLingHang/findType
  insurePicc: http://insurance.56zly.cn/insurePicc/insure
  insurePiccTypesId: *****************
  insureCpic: http://insurance.56zly.cn/insureCpic/insure
  insureCpicTypesId: *****************
  payFareTenantryId: *************


## MQ KEY的配置项，区分开发测试，生产
environment:
  config:
    mq:
      paymentMqKey: payment_results_freight_network_routing_key_test
      paymentMqName: payment_results_freight_network_routing_name_test
      exportMqKey: export_freight_network_routing_key_test
      exportMqName: export_freight_network_routing_name_test
      driverCardName: driver_card_push_routing_name_test
      driverCardKey: driver_card_push_routing_key_test
      uploadMqKey: freight_network_batch_upload_of_documents_routing_key_test
      uploadMqName: freight_network_batch_upload_of_documents_queue_name_test
      uploadShippingAgreementsMqKey: freight_network_batch_upload_of_shipping_agreements_routing_key_test
      uploadShippingAgreementsMqName: freight_network_batch_upload_of_shipping_agreements_queue_name_test
      uploginkMqKey: uplogink_routing_key_test
      uploginkMqName: uplogink_routing_name_test
      verifyTrajectoryMqKey: verify_trajectory_routing_key_test
      verifyTrajectoryMqName: verify_trajectory_routing_name_test
      ddSendKey: dd_send_routing_key_test
      ddSendName: dd_send_routing_name_test
      ddCallBackKey: dd_call_back_routing_key_test
      ddCallBackName: dd_call_back_routing_name_test
      fourToShanDaoKey: four_to_shan_dao_push_routing_key_test
      fourToShanDaoName: four_to_shan_dao_routing_name_test
      waybillRiskValidateKey: waybill_risk_validate_key_test
      waybillRiskValidateName: waybill_risk_validate_name_test
      orderAuditCallbackKey: order.audit.callback.key_test
      orderAuditCallbackName: order.audit.callback.name_test
      invoiceStatusCallbackKey: invoice.status.callback.key_test
      invoiceStatusCallbackName: invoice.status.callback.name_test

msg:
  url: http://freightshipper.51zlyun.cn/sign/contract/driver?id=
  ## 运单运输协议
  waybillContractUrl: http://c.51zlyun.cn/a/
  ## 雇佣合同
  employmentContractUrl: http://freightshipper.51zlyun.cn/sign/employmentContract?waybillId=
  ## 委托合同
  entrustmentContractUrl: http://c.51zlyun.cn/c/
  ## 车主声明
  carOwnerDeclarationUrl: http://c.51zlyun.cn/b/
  ## 亲属代收声明
  driverFamilyUrl: http://c.51zlyun.cn/e/

transfer:
  databaseName: tms_model2
  shaoDaoDBName: zly_freight_network_shandao
  fourDBName: zly_freight_network
openAccount:
  import: true

taxUpload:
  url:
    firstUpload: http://freightshipper.51zlyun.cn/tax/upload/truck/uploadStart
    secondUpload: http://freightshipper.51zlyun.cn/tax/upload/truck/uploadPayment
    thirdUpload: http://freightshipper.51zlyun.cn/tax/upload/truck/uploadInvoice
    arriverUpload: http://freightshipper.51zlyun.cn/tax/upload/truck/uploadArrive
    firstUpdateUpload: http://freightshipper.51zlyun.cn/tax/upload/truck/modifyStartField
    contractUpload: http://freightshipper.51zlyun.cn/tax/upload/truck/uploadShipperContract
    contractUpdateUpload: http://freightshipper.51zlyun.cn/tax/upload/truck/modifyShipperContract

shanDao:
  url: freightapi2.51zlyun.cn

screen:
  customerId: *************

# 请求服务端白名单IP，一些接口会判断如果不是白名单的IP，会进行拦截
services:
  ipWhiteList:
    # PDS服务器公网ip
    - "*************"
    - "127.0.0.1"