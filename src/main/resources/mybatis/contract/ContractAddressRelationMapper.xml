<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.consignor.contract.mapper.ContractAddressRelationMapper">

    <resultMap type="ContractAddressRelation" id="ContractAddressRelationResult">
        <result property="addressId"    column="address_id"    />
        <result property="contractId"    column="contract_id"    />
        <result property="type"    column="type"    />
    </resultMap>

    <sql id="selectContractAddressRelationVo">
        select address_id, contract_id, type from contract_address_relation
    </sql>

    <select id="selectContractAddressRelationList" parameterType="ContractAddressRelation" resultMap="ContractAddressRelationResult">
        <include refid="selectContractAddressRelationVo"/>
        <where>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="addressId != null  and addressId != ''"> and address_id = #{addressId}</if>
            <if test="contractId != null  and contractId != ''"> and contract_id = #{contractId}</if>
        </where>
    </select>

    <select id="selectContractAddressRelationByAddressId" parameterType="Long" resultMap="ContractAddressRelationResult">
        <include refid="selectContractAddressRelationVo"/>
        where address_id = #{addressId}
    </select>

    <insert id="insertContractAddressRelation" parameterType="ContractAddressRelation">
        insert into contract_address_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="addressId != null">address_id,</if>
            <if test="contractId != null">contract_id,</if>
            <if test="type != null and type != ''">type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="addressId != null">#{addressId},</if>
            <if test="contractId != null">#{contractId},</if>
            <if test="type != null and type != ''">#{type},</if>
         </trim>
    </insert>

    <update id="updateContractAddressRelation" parameterType="ContractAddressRelation">
        update contract_address_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="type != null and type != ''">type = #{type},</if>
        </trim>
        where address_id = #{addressId}
    </update>

    <delete id="deleteContractAddressRelationByAddressId" parameterType="Long">
        delete from contract_address_relation where address_id = #{addressId}
    </delete>

    <delete id="deleteContractAddressRelationByAddressIds" parameterType="String">
        delete from contract_address_relation where address_id in
        <foreach item="addressId" collection="array" open="(" separator="," close=")">
            #{addressId}
        </foreach>
    </delete>
</mapper>
