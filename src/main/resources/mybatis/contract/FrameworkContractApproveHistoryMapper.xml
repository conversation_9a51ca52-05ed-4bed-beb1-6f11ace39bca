<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.contract.mapper.FrameworkContractApproveHistoryMapper">

    <resultMap type="FrameworkContractApproveHistory" id="FrameworkContractApproveHistoryResult">
        <result property="id"    column="id"    />
        <result property="frameworkContractId"    column="framework_contract_id"    />
        <result property="approveState"    column="approve_state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="remark"    column="remark"    />
        <result property="state"    column="state"    />
        <result property="rate"    column="rate"    />
        <result property="preApproveState"    column="pre_approve_state"    />
        <result property="operateType"    column="operate_type"    />
        <result property="preRate"    column="pre_rate"    />
        <result property="preSubChain"    column="pre_sub_chain"    />
        <result property="createCompanyId"    column="create_company_id"    />
        <result property="createClient"    column="create_client"    />
    </resultMap>

    <sql id="selectFrameworkContractApproveHistoryVo">
        select id, framework_contract_id, approve_state, create_by, create_time, remark, state, rate, pre_approve_state,operate_type,pre_rate,pre_sub_chain,create_company_id,create_client from framework_contract_approve_history
    </sql>

    <select id="selectFrameworkContractApproveHistoryList" parameterType="FrameworkContractApproveHistory" resultMap="FrameworkContractApproveHistoryResult">
        <include refid="selectFrameworkContractApproveHistoryVo"/>
        <where>
            <if test="frameworkContractId != null "> and framework_contract_id = #{frameworkContractId}</if>
            <if test="approveState != null "> and approve_state = #{approveState}</if>
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="rate != null "> and rate = #{rate}</if>
            <if test="preApproveState != null "> and pre_approve_state = #{preApproveState}</if>
            <if test="operateType != null "> and operate_type = #{operateType}</if>
            <if test="preRate != null "> and pre_rate = #{preRate}</if>
            <if test="preSubChain != null "> and pre_sub_chain = #{preSubChain}</if>
            <if test="createCompanyId != null "> and create_company_id = #{createCompanyId}</if>
            <if test="createClient != null "> and create_client = #{createClient}</if>
        </where>
        order by  create_time desc
    </select>

    <select id="selectFrameworkContractApproveHistoryById" parameterType="Long" resultMap="FrameworkContractApproveHistoryResult">
        <include refid="selectFrameworkContractApproveHistoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertFrameworkContractApproveHistory" parameterType="FrameworkContractApproveHistory" useGeneratedKeys="true" keyProperty="id">
        insert into framework_contract_approve_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="frameworkContractId != null">framework_contract_id,</if>
            <if test="approveState != null">approve_state,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
            <if test="state != null">state,</if>
            <if test="rate != null">rate,</if>
            <if test="preApproveState != null">pre_approve_state,</if>
            <if test="operateType != null">operate_type,</if>
            <if test="preRate != null">pre_rate,</if>
            <if test="preSubChain != null">pre_sub_chain,</if>
            <if test="createCompanyId != null">create_company_id,</if>
            <if test="createClient != null">create_client,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="frameworkContractId != null">#{frameworkContractId},</if>
            <if test="approveState != null">#{approveState},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="state != null">#{state},</if>
            <if test="rate != null">#{rate},</if>
            <if test="preApproveState != null">#{preApproveState},</if>
            <if test="operateType != null">#{operateType},</if>
            <if test="preRate != null">#{preRate},</if>
            <if test="preSubChain != null">#{preSubChain},</if>
            <if test="createCompanyId != null">#{createCompanyId},</if>
            <if test="createClient != null">#{createClient},</if>
         </trim>
    </insert>

    <update id="updateFrameworkContractApproveHistory" parameterType="FrameworkContractApproveHistory">
        update framework_contract_approve_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="frameworkContractId != null">framework_contract_id = #{frameworkContractId},</if>
            <if test="approveState != null">approve_state = #{approveState},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="state != null">state = #{state},</if>
            <if test="rate != null">rate = #{rate},</if>
            <if test="preApproveState != null">pre_approve_state = #{preApproveState},</if>
            <if test="operateType != null">operate_type = #{operateType},</if>
            <if test="preRate != null">pre_rate = #{preRate},</if>
            <if test="preSubChain != null">pre_sub_chain = #{preSubChain},</if>
            <if test="createCompanyId != null">create_company_id = #{createCompanyId},</if>
            <if test="createClient != null">create_client = #{createClient},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrameworkContractApproveHistoryById" parameterType="Long">
        delete from framework_contract_approve_history where id = #{id}
    </delete>

    <delete id="deleteFrameworkContractApproveHistoryByIds" parameterType="String">
        delete from framework_contract_approve_history where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteFrameworkContractApproveHistoryByContractIds">
        delete from framework_contract_approve_history where framework_contract_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
