<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.contract.mapper.FrameworkContractMapper">

    <resultMap type="com.zly.project.contract.domain.FrameworkContract" id="FrameworkContractResult">
        <result property="id" column="id"/>
        <result property="contractCode" column="contract_code"/>
        <result property="contractName" column="contract_name"/>
        <result property="responsiblePerson" column="responsible_person"/>
        <result property="contactPerson" column="contact_person"/>
        <result property="contractState" column="contract_state"/>
        <result property="rate" column="rate"/>
        <result property="state" column="state"/>
        <result property="lastCustomerId" column="last_customer_id"/>
        <result property="lastCustomerName" column="last_customer_name"/>
        <result property="legalPerson" column="legal_person"/>
        <result property="registerAddr" column="register_addr"/>
        <result property="abstracts" column="abstracts"/>
        <result property="signingTime" column="signing_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="approveState" column="approve_state"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="customerContractId" column="customer_contract_id"/>
        <result property="customerContractName" column="customer_contract_name"/>
        <result property="contractType" column="contract_type"/>
        <result property="freightForwarderId" column="freight_forwarder_id"/>
        <result property="isPrepay" column="is_prepay"/>
        <result property="needAuditing" column="need_auditing"/>
        <result property="insuranceState" column="insurance_state"/>
        <result property="effectTime" column="effect_time"/>
        <result property="subchainState" column="subchain_state"/>
        <result property="businessModelId" column="business_model_id"/>
        <result property="xydUploadState" column="xyd_upload_state"/>
        <result property="xydApproveState" column="xyd_approve_state"/>
        <result property="isTaxUploaded" column="is_tax_uploaded"/>
        <result property="effectiveTime" column="effective_time"/>
        <result property="timeUnit" column="time_unit"/>
    </resultMap>

    <resultMap type="com.zly.project.contract.domain.FrameworkContractRes" id="FrameworkContractResultRes"
               extends="FrameworkContractResult">
        <result property="customerName" column="customer_name"/>
    </resultMap>

    <sql id="selectFrameworkContractVo">
        select id,
               contract_code,
               contract_name,
               responsible_person,
               contact_person,
               contract_state,
               rate,
               state,
               last_customer_id,
               last_customer_name,
               legal_person,
               register_addr,
               abstracts,
               signing_time,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               contact_phone,
               tenant_id,
               approve_state,
               start_time,
               end_time,
               customer_contract_id,
               customer_contract_name,
               contract_type,
               freight_forwarder_id,
               is_prepay,
               need_auditing,
               insurance_state,
               effect_time,
               subchain_state,
               business_model_id,
               xyd_upload_state,
               xyd_approve_state,
               is_tax_uploaded,
               effective_time,
               time_unit
        from framework_contract
    </sql>

    <select id="selectFrameworkContractList" parameterType="com.zly.project.contract.domain.FrameworkContract" resultMap="FrameworkContractResult">
        <include refid="selectFrameworkContractVo"/>
        where state != -1
            <if test="contractCode != null  and contractCode != ''"> and contract_code = #{contractCode}</if>
            <if test="contractName != null  and contractName != ''"> and contract_name like concat('%', #{contractName}, '%')</if>
            <if test="responsiblePerson != null  and responsiblePerson != ''"> and responsible_person = #{responsiblePerson}</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person = #{contactPerson}</if>
            <if test="contractState != null "> and contract_state = #{contractState}</if>
            <if test="rate != null "> and rate = #{rate}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="lastCustomerId != null "> and last_customer_id = #{lastCustomerId}</if>
            <if test="lastCustomerName != null and lastCustomerName != ''"> and last_customer_name = #{lastCustomerName}</if>
            <if test="legalPerson != null  and legalPerson != ''"> and legal_person = #{legalPerson}</if>
            <if test="registerAddr != null  and registerAddr != ''"> and register_addr = #{registerAddr}</if>
            <if test="abstracts != null  and abstracts != ''"> and abstracts = #{abstracts}</if>
            <if test="signingTime != null "> and signing_time = #{signingTime}</if>
            <if test="contactPhone != null and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="tenantId != null"> and tenant_id = #{tenantId}</if>
            <if test="approveState != null"> and approve_state = #{approveState}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="customerContractId != null "> and customer_contract_id = #{customerContractId}</if>
            <if test="contractType != null "> and contract_type = #{contractType}</if>
            <if test="freightForwarderId != null "> and freight_forwarder_id = #{freightForwarderId}</if>
            <if test="freightForwarderId != null "> and is_prepay = #{isPrepay}</if>
            <if test="freightForwarderId != null "> and need_auditing = #{needAuditing}</if>
            <if test="insuranceState != null "> and insurance_state = #{insuranceState}</if>
            <if test="effectTime != null "> and effect_time = #{effectTime}</if>
            <if test="subchainState != null "> and subchain_state = #{subchainState}</if>
            <if test="businessModelId != null "> and business_model_id = #{businessModelId}</if>
            <if test="customerContractName != null  and customerContractName != ''"> and customer_contract_name like concat('%', #{customerContractName}, '%')</if>
            <if test="isTaxUploaded != null ">and is_tax_uploaded = #{isTaxUploaded}</if>
        ORDER BY id DESC
    </select>

    <select id="selectAuthFrameworkContractList" parameterType="com.zly.project.contract.domain.FrameworkContract" resultMap="FrameworkContractResult">
        <include refid="selectFrameworkContractVo"/>
        <where>
            <if test="contractCode != null  and contractCode != ''"> and contract_code = #{contractCode}</if>
            <if test="contractName != null  and contractName != ''"> and contract_name like concat('%', #{contractName}, '%')</if>
            <if test="responsiblePerson != null  and responsiblePerson != ''"> and responsible_person = #{responsiblePerson}</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person = #{contactPerson}</if>
            <if test="contractState != null "> and contract_state = #{contractState}</if>
            <if test="rate != null "> and rate = #{rate}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="lastCustomerId != null "> and last_customer_id = #{lastCustomerId}</if>
            <if test="lastCustomerName != null and lastCustomerName != ''"> and last_customer_name = #{lastCustomerName}</if>
            <if test="legalPerson != null  and legalPerson != ''"> and legal_person = #{legalPerson}</if>
            <if test="registerAddr != null  and registerAddr != ''"> and register_addr = #{registerAddr}</if>
            <if test="abstracts != null  and abstracts != ''"> and abstracts = #{abstracts}</if>
            <if test="signingTime != null "> and signing_time = #{signingTime}</if>
            <if test="contactPhone != null and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="tenantId != null"> and tenant_id = #{tenantId}</if>
            <if test="approveState != null"> and approve_state = #{approveState}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="customerContractId != null "> and customer_contract_id = #{customerContractId}</if>
            <if test="contractType != null "> and contract_type = #{contractType}</if>
            <if test="freightForwarderId != null "> and freight_forwarder_id = #{freightForwarderId}</if>
            <if test="freightForwarderId != null "> and is_prepay = #{isPrepay}</if>
            <if test="freightForwarderId != null "> and need_auditing = #{needAuditing}</if>
            <if test="insuranceState != null "> and insurance_state = #{insuranceState}</if>
            <if test="effectTime != null "> and effect_time = #{effectTime}</if>
            <if test="subchainState != null "> and subchain_state = #{subchainState}</if>
            <if test="businessModelId != null "> and business_model_id = #{businessModelId}</if>
            <if test="customerContractName != null  and customerContractName != ''"> and customer_contract_name like concat('%', #{customerContractName}, '%')</if>
        </where>
        ORDER BY id DESC
    </select>

    <select id="selectFrameworkContractSubcontractedList" parameterType="com.zly.project.contract.domain.FrameworkContract" resultMap="FrameworkContractResult">
        select fc.id,
        fc.contract_code,
        fc.contract_name,
        fc.responsible_person,
        fc.contact_person,
        fc.contract_state,
        fc.rate,
        fc.state,
        fc.last_customer_id,
        fc.last_customer_name,
        fc.legal_person,
        fc.register_addr,
        fc.abstracts,
        fc.signing_time,
        fc.create_by,
        fc.create_time,
        fc.update_by,
        fc.update_time,
        fc.remark,
        fc.contact_phone,
        fc.tenant_id,
        fc.approve_state,
        fc.start_time,
        fc.end_time,
        fc.customer_contract_id,
        fc.customer_contract_name,
        fc.contract_type,
        fc.freight_forwarder_id,
        fc.is_prepay,
        fc.need_auditing,
        fc.insurance_state,
        fc.effect_time,
        fc.subchain_state,
        fc.business_model_id,
        fc.xyd_upload_state,
        fc.xyd_approve_state
        from framework_contract fc
        left join framework_contract_subchain fcs on fc.id = fcs.contract_id
        where fc.state != -1 and fc.subchain_state = 0
            <if test="state != null ">and fc.state = #{state}</if>
            <if test="tenantId != null">and fcs.customer_id = #{tenantId} and fcs.is_recorder = 0</if>
            <if test="approveState != null">and fc.approve_state = #{approveState}</if>
        GROUP BY fc.id
        ORDER BY fc.create_time DESC
    </select>

    <select id="selectFrameworkContractById" parameterType="Long" resultMap="FrameworkContractResult">
        <include refid="selectFrameworkContractVo"/>
        where id = #{id}
    </select>
    <select id="selectContractList" resultMap="FrameworkContractResultRes">
        select fc.id, fc.contract_code, fc.contract_name, fc.responsible_person, fc.contact_person, fc.contract_state, fc.rate, fc.state,
        fc.last_customer_id,fc.last_customer_name, fc.legal_person, fc.register_addr, fc.abstracts, fc.signing_time, fc.create_by, fc.create_time, fc.update_by,
        fc.update_time, fc.remark, fc.contact_phone,fc.tenant_id,fc.approve_state,ci.customer_name,fc.end_time,fw.name freightForwarderName,fc.subchain_state,
        fc.contract_type,fc.is_prepay,fc.xyd_upload_state,fc.xyd_approve_state,fc.business_model_id,bm.model_name businessModelName,fc.effect_time,fc.is_tax_uploaded
        from framework_contract fc
        left join customer_info ci on fc.tenant_id = ci.id
        LEFT JOIN freight_forwarder_info fw on fc.freight_forwarder_id = fw.id
        LEFT JOIN business_model bm on fc.business_model_id = bm.id
        where fc.state != -1
        and fw.name != '江苏善道智运科技有限公司'
        <if test="id != null">and fc.id = #{id}</if>
            <if test="contractCode != null  and contractCode != ''">and fc.contract_code = #{contractCode}</if>
            <if test="contractName != null  and contractName != ''">and fc.contract_name like concat('%', #{contractName}, '%')</if>
            <if test="responsiblePerson != null  and responsiblePerson != ''">and fc.responsible_person = #{responsiblePerson}</if>
            <if test="contactPerson != null  and contactPerson != ''">and fc.contact_person = #{contactPerson}</if>
            <if test="contractState != null ">and fc.contract_state = #{contractState}</if>
            <if test="rate != null ">and fc.rate = #{rate}</if>
            <if test="state != null ">and fc.state = #{state}</if>
            <if test="lastCustomerId != null ">and fc.last_customer_id = #{lastCustomerId}</if>
            <if test="lastCustomerName != null and lastCustomerName != ''">and fc.last_customer_name = #{lastCustomerName}</if>
            <if test="legalPerson != null  and legalPerson != ''">and fc.legal_person = #{legalPerson}</if>
            <if test="registerAddr != null  and registerAddr != ''">and fc.register_addr = #{registerAddr}</if>
            <if test="abstracts != null  and abstracts != ''">and fc.abstracts = #{abstracts}</if>
            <if test="signingTime != null ">and fc.signing_time = #{signingTime}</if>
            <if test="contactPhone != null and contactPhone != ''">and fc.contact_phone = #{contactPhone}</if>
            <if test="tenantId != null">and fc.tenant_id = #{tenantId}</if>
            <if test="approveState != null">and fc.approve_state = #{approveState}</if>
            <if test="customerName != null and customerName != ''">and ci.customer_name like concat('%',#{customerName},'%')</if>
            <if test="freightForwarderId != null">and fc.freight_forwarder_id = #{freightForwarderId}</if>
            <if test="contractType != null">and fc.contract_type = #{contractType}</if>
            <if test="insuranceState != null">and fc.insurance_state = #{insuranceState}</if>
            <if test="businessModelId != null">and fc.business_model_id = #{businessModelId}</if>
        <if test="isTaxUploaded != null">and fc.is_tax_uploaded = #{isTaxUploaded}</if>
        order by state asc,id desc
    </select>

    <select id="frameworkContractStateFromAWeekExpired" resultMap="FrameworkContractResult">
        <include refid="selectFrameworkContractVo"/>
        where end_time &gt; now() and end_time &lt;= now() + interval 1 week
    </select>
    <select id="selectFrameworkContractByIds" resultMap="FrameworkContractResult">
        <include refid="selectFrameworkContractVo"/>
        where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectAuthContractList" resultMap="FrameworkContractResult">
        select fc.id,
        fc.contract_code,
        fc.contract_name,
        fc.responsible_person,
        fc.contact_person,
        fc.contract_state,
        fc.rate,
        fc.state,
        fc.last_customer_id,
        fc.last_customer_name,
        fc.legal_person,
        fc.register_addr,
        fc.abstracts,
        fc.signing_time,
        fc.create_by,
        fc.create_time,
        fc.update_by,
        fc.update_time,
        fc.remark,
        fc.contact_phone,
        fc.tenant_id,
        fc.approve_state,
        fc.start_time,
        fc.end_time,
        fc.customer_contract_id,
        fc.customer_contract_name,
        fc.contract_type,
        fc.freight_forwarder_id,
        fc.is_prepay,
        fc.need_auditing,
        fc.insurance_state,
        fc.effect_time,
        fc.subchain_state,
        fc.business_model_id,
        fc.xyd_upload_state,
        fc.xyd_approve_state
        from framework_contract_subchain fcs
        left join framework_contract fc on fc.id = fcs.contract_id
        where fcs.is_recorder = 0 and fcs.customer_id = #{tenantId} and fc.state != -1
        <if test="contractCode != null  and contractCode != ''"> and fc.contract_code = #{contractCode}</if>
        <if test="contractName != null  and contractName != ''"> and fc.contract_name like concat('%', #{contractName}, '%')</if>
        <if test="responsiblePerson != null  and responsiblePerson != ''"> and fc.responsible_person = #{responsiblePerson}</if>
        <if test="contractState != null "> and fc.contract_state = #{contractState}</if>
        <if test="state != null "> and fc.state = #{state}</if>
        <if test="lastCustomerId != null "> and fc.last_customer_id = #{lastCustomerId}</if>
        <if test="lastCustomerName != null and lastCustomerName != ''"> and fc.last_customer_name = #{lastCustomerName}</if>
        <if test="signingTime != null "> and fc.signing_time = #{signingTime}</if>
        <if test="approveState != null"> and fc.approve_state = #{approveState}</if>
        <if test="startTime != null "> and fc.start_time = #{startTime}</if>
        <if test="endTime != null "> and fc.end_time = #{endTime}</if>
        <if test="customerContractId != null "> and fc.customer_contract_id = #{customerContractId}</if>
        <if test="contractType != null "> and fc.contract_type = #{contractType}</if>
        <if test="customerContractName != null  and customerContractName != ''"> and fc.customer_contract_name like concat('%', #{customerContractName}, '%')</if>
        <if test="projectGroupIds != null and projectGroupIds.size() > 0">
            and fc.id IN (
            SELECT DISTINCT contract_id FROM project_group_list WHERE create_client = 3 AND group_id IN
            <foreach collection="projectGroupIds" item="groupId" open="(" close=")" separator=",">
                #{groupId}
            </foreach>
            )
        </if>
        ORDER BY fc.id DESC
    </select>
    <select id="selectSearchValueByForwarderId" resultType="com.zly.project.contract.domain.ContractSimpleRes">
        select distinct fc.id,fc.contract_code contractCode ,fc.contract_name contractName
        from framework_contract fc
        left join framework_contract_subchain fcs on fc.id = fcs.contract_id
        <if test="isWhm != null and isWhm == true">
        INNER JOIN business_model bm on fc.business_model_id = bm.id and bm.model_name = '网络货运模式'
        </if>
        where fcs.freight_forwarder_id = #{freightForwarderId} and fc.state != -1
        <if test="contractName != null  and contractName != ''">and fc.contract_name like concat('%', #{contractName},
            '%')
        </if>
        <if test="customerId != null">and fcs.parent_customer_id = #{customerId}</if>
        <if test="selfCustomerId != null">and fcs.customer_id = #{selfCustomerId}</if>
    </select>
    <select id="selectContractListHasLimit" resultMap="FrameworkContractResultRes">
        select fc.id, fc.contract_code, fc.contract_name, fc.responsible_person, fc.contact_person, fc.contract_state, fc.rate, fc.state,
        fc.last_customer_id,fc.last_customer_name, fc.legal_person, fc.register_addr, fc.abstracts, fc.signing_time, fc.create_by, fc.create_time, fc.update_by,
        fc.update_time, fc.remark, fc.contact_phone,fc.tenant_id,fc.approve_state,ci.customer_name,fc.end_time,fw.name freightForwarderName,fc.subchain_state,
        fc.contract_type,fc.is_prepay,fc.xyd_upload_state,fc.xyd_approve_state
        from framework_contract fc
        left join customer_info ci on fc.tenant_id = ci.id
        LEFT JOIN freight_forwarder_info fw on fc.freight_forwarder_id = fw.id
        LEFT JOIN business_model bm on fc.business_model_id = bm.id
        where fc.state != -1
        <if test="id != null">and fc.id = #{id}</if>
        <if test="contractCode != null  and contractCode != ''">and fc.contract_code = #{contractCode}</if>
        <if test="contractName != null  and contractName != ''">and fc.contract_name like concat('%', #{contractName}, '%')</if>
        <if test="state != null ">and fc.state = #{state}</if>
        <if test="tenantId != null">and fc.tenant_id = #{tenantId}</if>
        <if test="freightForwarderId != null "> and fc.freight_forwarder_id = #{freightForwarderId}</if>
        <if test="contractType != null "> and fc.contract_type = #{contractType}</if>
        <if test="businessModelId != null "> and fc.business_model_id = #{businessModelId}</if>
        <if test="isAgentShow != null "> and bm.is_agent_show = #{isAgentShow}</if>
        <if test="contractIds != null and contractIds.size() > 0">
            AND fc.id IN
            <foreach collection="contractIds" item="contractId" open="(" close=")" separator=",">
                #{contractId}
            </foreach>
        </if>
        order by fc.state asc,fc.create_time desc
    </select>

    <select id="selectFinanceContractList" resultType="com.zly.project.financeOperation.domain.res.FinanceContractQueryRes">
        select fc.id,
        fc.contract_code contractCode,
        fc.contract_name contractName,
        fc.responsible_person responsiblePerson,
        fc.last_customer_id lastCustomerId,
        fc.last_customer_name lastCustomerName,
        fc.create_by createBy,
        fc.create_time createTime,
        fc.business_model_id businessModelId,
        fc.tenant_id tenantId,
        fc.approve_state approveState,
        fc.contract_type contractType,
        fc.freight_forwarder_id freightForwarderId,
        fc.insurance_state insuranceState,
        fc.state state,
        fcs.loan_rate loanRate,
        ci.customer_name carrierCustomerName,
        ffi.name freightForwarderName
        from framework_contract_subchain fcs
        left join framework_contract fc on fc.id = fcs.contract_id
        left join customer_info ci on fcs.customer_id = ci.id
        left join freight_forwarder_info ffi on fc.freight_forwarder_id = ffi.id
        where fcs.customer_id = #{tenantId} and fcs.is_recorder = 1 and fcs.state = 0 and fc.state != -1
        <if test="businessModelId != null ">
            and fc.business_model_id = #{businessModelId}
        </if>
        <if test="contractName != null  and contractName != ''"> and fc.contract_name like concat('%', #{contractName}, '%')</if>
        <if test="state != null "> and fc.state = #{state}</if>
        <if test="lastCustomerId != null "> and fc.last_customer_id = #{lastCustomerId}</if>
        <if test="lastCustomerName != null and lastCustomerName != ''"> and fc.last_customer_name = #{lastCustomerName}</if>
        <if test="customerContractId != null "> and fc.customer_contract_id = #{customerContractId}</if>
        <if test="contractType != null "> and fc.contract_type = #{contractType}</if>
        <if test="insuranceState != null "> and fc.insurance_state = #{insuranceState}</if>
        <if test="customerContractName != null  and customerContractName != ''"> and fc.customer_contract_name like concat('%', #{customerContractName}, '%')</if>
        <if test="contractIds != null and contractIds.size() > 0">
            and fc.id IN
            <foreach collection="contractIds" item="contractId" open="(" close=")" separator=",">
                #{contractId}
            </foreach>
        </if>
        ORDER BY fc.id DESC
    </select>
    <select id="selectAuthContractResList" resultType="com.zly.project.contract.domain.FrameworkContractRes">
        select fc.id,
        fc.contract_code contractCode,
        fc.contract_name contractName,
        fc.responsible_person responsiblePerson,
        fc.last_customer_id lastCustomerId,
        fc.last_customer_name lastCustomerName,
        fc.create_by createBy,
        fc.create_time createTime,
        fc.business_model_id businessModelId,
        fc.tenant_id tenantId,
        fc.approve_state approveState,
        fc.contract_type contractType,
        fc.freight_forwarder_id freightForwarderId,
        fc.insurance_state insuranceState,
        fc.state state,
        ci.customer_name carrierCustomerName,
        ffi.name freightForwarderName
        from framework_contract_subchain fcs
        left join framework_contract fc on fc.id = fcs.contract_id
        left join customer_info ci on fcs.customer_id = ci.id
        left join freight_forwarder_info ffi on fc.freight_forwarder_id = ffi.id
        where fcs.is_recorder = 0 and fcs.state = 0 and fc.state != -1
        <if test="contractName != null  and contractName != ''">and fc.contract_name like concat('%', #{contractName},
            '%')
        </if>
        <if test="state != null ">and fc.state = #{state}</if>
        <if test="approveState != null ">and fc.approve_state = #{approveState}</if>
        <if test="contractCode != null and contractCode != ''">and fc.contract_code = #{contractCode}</if>
        <if test="lastCustomerId != null ">and fc.last_customer_id = #{lastCustomerId}</if>
        <if test="lastCustomerName != null and lastCustomerName != ''">and fc.last_customer_name = #{lastCustomerName}
        </if>
        <if test="tenantId != null">and fcs.customer_id = #{tenantId}</if>
        <if test="customerContractId != null ">and fc.customer_contract_id = #{customerContractId}</if>
        <if test="contractType != null ">and fc.contract_type = #{contractType}</if>
        <if test="insuranceState != null ">and fc.insurance_state = #{insuranceState}</if>
        <if test="businessModelId != null ">and fc.business_model_id = #{businessModelId}</if>
        <if test="customerContractName != null  and customerContractName != ''">and fc.customer_contract_name like
            concat('%', #{customerContractName}, '%')
        </if>
        <if test="projectGroupIds != null and projectGroupIds.size() > 0">
            and fc.id IN (
            SELECT DISTINCT contract_id FROM project_group_list WHERE create_client = 8 AND group_id IN
            <foreach collection="projectGroupIds" item="groupId" open="(" close=")" separator=",">
                #{groupId}
            </foreach>
            )
        </if>
        <if test="contractIds != null and contractIds.size() > 0">
            and fc.id IN
            <foreach collection="contractIds" item="contractId" open="(" close=")" separator=",">
                #{contractId}
            </foreach>
        </if>
        group by fc.id
        ORDER BY
        CASE
        WHEN fc.approve_state = 2 THEN 1
        WHEN fc.approve_state = 4 THEN 2
        ELSE 3
        END,
        CASE
        WHEN fc.state = 1 THEN 1
        WHEN fc.state = 2 THEN 2
        ELSE 3
        END,
        fc.create_time DESC
    </select>
    <select id="selectAgentProjectList" resultMap="FrameworkContractResultRes">
        select fc.id, fc.contract_code, fc.contract_name, fc.responsible_person, fc.contact_person, fc.contract_state, fc.rate, fc.state,
        fc.last_customer_id,fc.last_customer_name, fc.legal_person, fc.register_addr, fc.abstracts, fc.signing_time, fc.create_by, fc.create_time, fc.update_by,
        fc.update_time, fc.remark, fc.contact_phone,fc.tenant_id,fc.approve_state,ci.customer_name,fc.end_time,fw.name freightForwarderName,fc.subchain_state,
        fc.contract_type,fc.is_prepay,fc.contract_type,fc.xyd_upload_state,fc.xyd_approve_state
        from framework_contract fc
        left join customer_info ci on fc.tenant_id = ci.id
        LEFT JOIN freight_forwarder_info fw on fc.freight_forwarder_id = fw.id
        where fc.state != -1
            <if test="id != null">and fc.id = #{id}</if>
            <if test="contractCode != null  and contractCode != ''">and fc.contract_code = #{contractCode}</if>
            <if test="contractName != null  and contractName != ''">and fc.contract_name like concat('%', #{contractName}, '%')</if>
            <if test="state != null ">and fc.state = #{state}</if>
            <if test="tenantId != null">and fc.tenant_id = #{tenantId}</if>
            <if test="freightForwarderId != null "> and fc.freight_forwarder_id = #{freightForwarderId}</if>
            <if test="contractType != null "> and contract_type = #{contractType}</if>
            <if test="businessModelId != null "> and business_model_id = #{businessModelId}</if>
            <if test="contractIds != null and contractIds.size() > 0">
                AND fc.id IN
                <foreach collection="contractIds" item="contractId" open="(" close=")" separator=",">
                    #{contractId}
                </foreach>
            </if>

        order by state asc,id desc
    </select>
    <select id="selectContractListTotal" resultType="java.lang.Integer">
        select count(1)
        from framework_contract fc
        left join customer_info ci on fc.tenant_id = ci.id
        LEFT JOIN freight_forwarder_info fw on fc.freight_forwarder_id = fw.id
        LEFT JOIN business_model bm on fc.business_model_id = bm.id
        where fc.state != -1
        <if test="id != null">and fc.id = #{id}</if>
        <if test="contractCode != null  and contractCode != ''">and fc.contract_code = #{contractCode}</if>
        <if test="contractName != null  and contractName != ''">and fc.contract_name like concat('%', #{contractName}, '%')</if>
        <if test="responsiblePerson != null  and responsiblePerson != ''">and fc.responsible_person = #{responsiblePerson}</if>
        <if test="contactPerson != null  and contactPerson != ''">and fc.contact_person = #{contactPerson}</if>
        <if test="contractState != null ">and fc.contract_state = #{contractState}</if>
        <if test="rate != null ">and fc.rate = #{rate}</if>
        <if test="state != null ">and fc.state = #{state}</if>
        <if test="lastCustomerId != null ">and fc.last_customer_id = #{lastCustomerId}</if>
        <if test="lastCustomerName != null and lastCustomerName != ''">and fc.last_customer_name = #{lastCustomerName}</if>
        <if test="legalPerson != null  and legalPerson != ''">and fc.legal_person = #{legalPerson}</if>
        <if test="registerAddr != null  and registerAddr != ''">and fc.register_addr = #{registerAddr}</if>
        <if test="abstracts != null  and abstracts != ''">and fc.abstracts = #{abstracts}</if>
        <if test="signingTime != null ">and fc.signing_time = #{signingTime}</if>
        <if test="contactPhone != null and contactPhone != ''">and fc.contact_phone = #{contactPhone}</if>
        <if test="tenantId != null">and fc.tenant_id = #{tenantId}</if>
        <if test="approveState != null">and fc.approve_state = #{approveState}</if>
        <if test="customerName != null">and ci.customer_name like concat('%',#{customerName},'%')</if>
        <if test="freightForwarderId != null">and fc.freight_forwarder_id = #{freightForwarderId}</if>
        <if test="contractType != null">and fc.contract_type = #{contractType}</if>
        <if test="insuranceState != null">and fc.insurance_state = #{insuranceState}</if>
        <if test="businessModelId != null">and fc.business_model_id = #{businessModelId}</if>
    </select>
    <select id="selectForwarderContractList" resultType="com.zly.project.contract.domain.FrameworkContract">
        select fc.id,
        fc.contract_name contractName
        from framework_contract fc
        where fc.freight_forwarder_id = #{freightForwarderId} and fc.state != -1
        <if test="contractIds != null and contractIds.size() > 0">
            and fc.id IN
            <foreach collection="contractIds" item="contractId" open="(" close=")" separator=",">
                #{contractId}
            </foreach>
        </if>
    </select>
    <select id="selectByIdsAndModelId" resultMap="FrameworkContractResult">
        <include refid="selectFrameworkContractVo"/>
        where id in
        <foreach item="id" collection="contractIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="modelId != null">
            and business_model_id = #{modelId}
        </if>
    </select>

    <insert id="insertFrameworkContract" parameterType="com.zly.project.contract.domain.FrameworkContract">
        insert into framework_contract
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="contractCode != null and contractCode != ''">contract_code,</if>
            <if test="contractName != null and contractName != ''">contract_name,</if>
            <if test="responsiblePerson != null and responsiblePerson != ''">responsible_person,</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="contractState != null">contract_state,</if>
            <if test="rate != null">rate,</if>
            <if test="state != null">state,</if>
            <if test="lastCustomerId != null">last_customer_id,</if>
            <if test="lastCustomerName != null and lastCustomerName != ''">last_customer_name,</if>
            <if test="legalPerson != null and legalPerson != ''">legal_person,</if>
            <if test="registerAddr != null and registerAddr != ''">register_addr,</if>
            <if test="abstracts != null and abstracts != ''">abstracts,</if>
            <if test="signingTime != null">signing_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="approveState != null">approve_state,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="customerContractId != null">customer_contract_id,</if>
            <if test="customerContractName != null">customer_contract_name,</if>
            <if test="contractType != null">contract_type,</if>
            <if test="freightForwarderId != null">freight_forwarder_id,</if>
            <if test="isPrepay != null">is_prepay,</if>
            <if test="needAuditing != null">need_auditing,</if>
            <if test="insuranceState != null">insurance_state,</if>
            <if test="effectTime != null">effect_time,</if>
            <if test="subchainState != null">subchain_state,</if>
            <if test="businessModelId != null">business_model_id,</if>
            <if test="xydUploadState != null">xyd_upload_state,</if>
            <if test="xydApproveState != null">xyd_approve_state,</if>
            <if test="isTaxUploaded != null">is_tax_uploaded,</if>
            <if test="effectiveTime != null">effective_time,</if>
            <if test="timeUnit != null">time_unit,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="contractCode != null and contractCode != ''">#{contractCode},</if>
            <if test="contractName != null and contractName != ''">#{contractName},</if>
            <if test="responsiblePerson != null and responsiblePerson != ''">#{responsiblePerson},</if>
            <if test="contactPerson != null and contactPerson != ''">#{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="contractState != null">#{contractState},</if>
            <if test="rate != null">#{rate},</if>
            <if test="state != null">#{state},</if>
            <if test="lastCustomerId != null">#{lastCustomerId},</if>
            <if test="lastCustomerName != null and lastCustomerName != ''">#{lastCustomerName},</if>
            <if test="legalPerson != null and legalPerson != ''">#{legalPerson},</if>
            <if test="registerAddr != null and registerAddr != ''">#{registerAddr},</if>
            <if test="abstracts != null and abstracts != ''">#{abstracts},</if>
            <if test="signingTime != null">#{signingTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="approveState != null">#{approveState},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="customerContractId != null">#{customerContractId},</if>
            <if test="customerContractName != null">#{customerContractName},</if>
            <if test="contractType != null">#{contractType},</if>
            <if test="freightForwarderId != null">#{freightForwarderId},</if>
            <if test="isPrepay != null">#{isPrepay},</if>
            <if test="needAuditing != null">#{needAuditing},</if>
            <if test="insuranceState != null">#{insuranceState},</if>
            <if test="effectTime != null">#{effectTime},</if>
            <if test="subchainState != null">#{subchainState},</if>
            <if test="businessModelId != null">#{businessModelId},</if>
            <if test="xydUploadState != null">#{xydUploadState},</if>
            <if test="xydApproveState != null">#{xydApproveState},</if>
            <if test="isTaxUploaded != null">#{isTaxUploaded},</if>
            <if test="effectiveTime != null">#{effectiveTime},</if>
            <if test="timeUnit != null">#{timeUnit},</if>
        </trim>
    </insert>
    <insert id="insertFrameworkContracts">
        insert IGNORE into framework_contract
        (id,
        contract_code,
        contract_name,
        responsible_person,
        contact_person,
        contact_phone,
        contract_state,
        rate,
        state,
        last_customer_id,
        last_customer_name,
        legal_person,
        register_addr,
        abstracts,
        signing_time,
        create_by,
        create_time,
        update_by,
        update_time,
        remark,
        tenant_id,
        approve_state,
        start_time,
        end_time,
        customer_contract_id,
        customer_contract_name,
        contract_type,
        freight_forwarder_id,
        is_prepay,
        need_auditing,
        insurance_state,
        effect_time,
        subchain_state,
        business_model_id,
        xyd_upload_state,
        xyd_approve_state,
        is_tax_uploaded,
        effective_time,
        time_unit
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            <if test="item.id != null">#{item.id},</if>
            <if test="item.contractCode != null">#{item.contractCode},</if>
            <if test="item.contractName != null">#{item.contractName},</if>
            <if test="item.responsiblePerson != null">#{item.responsiblePerson},</if>
            <if test="item.contactPerson != null">#{item.contactPerson},</if>
            <if test="item.contactPhone != null">#{item.contactPhone},</if>
            <if test="item.contractState != null">#{item.contractState},</if>
            <if test="item.rate != null">#{item.rate},</if>
            <if test="item.state != null">#{item.state},</if>
            <if test="item.lastCustomerId != null">#{item.lastCustomerId},</if>
            <if test="item.lastCustomerName != null">#{item.lastCustomerName},</if>
            <if test="item.legalPerson != null">#{item.legalPerson},</if>
            <if test="item.registerAddr != null ">#{item.registerAddr},</if>
            <if test="item.abstracts != null ">#{item.abstracts},</if>
            <if test="item.signingTime != null">#{item.signingTime},</if>
            <if test="item.createBy != null ">#{item.createBy},</if>
            <if test="item.createTime != null">#{item.createTime},</if>
            <if test="item.updateBy != null ">#{item.updateBy},</if>
            <if test="item.updateTime != null">#{item.updateTime},</if>
            <if test="item.remark != null ">#{item.remark},</if>
            <if test="item.tenantId != null">#{item.tenantId},</if>
            <if test="item.approveState != null">#{item.approveState},</if>
            <if test="item.startTime != null">#{item.startTime},</if>
            <if test="item.startTime == null ">default,</if>
            #{item.endTime},
            <if test="item.customerContractId != null">#{item.customerContractId},</if>
            <if test="item.customerContractName != null">#{item.customerContractName},</if>
            <if test="item.contractType != null">#{item.contractType},</if>
            <if test="item.freightForwarderId != null">#{item.freightForwarderId},</if>
            <if test="item.isPrepay != null">#{item.isPrepay},</if>
            <if test="item.needAuditing != null">#{item.needAuditing},</if>
            <if test="item.insuranceState != null">#{item.insuranceState},</if>
            #{item.effectTime},
            <if test="item.subchainState != null">#{item.subchainState},</if>
            <if test="item.businessModelId != null">#{item.businessModelId},</if>
            <if test="item.xydUploadState != null">#{item.xydUploadState},</if>
            <if test="item.xydApproveState != null">#{item.xydApproveState},</if>
            <if test="item.isTaxUploaded != null">#{item.isTaxUploaded},</if>
            <if test="item.effectiveTime != null">#{item.effectiveTime},</if>
            #{item.timeUnit}
        </foreach>
    </insert>

    <update id="updateFrameworkContract" parameterType="com.zly.project.contract.domain.FrameworkContract">
        update framework_contract
        <trim prefix="SET" suffixOverrides=",">
            <if test="contractCode != null and contractCode != ''">contract_code = #{contractCode},</if>
            <if test="contractName != null and contractName != ''">contract_name = #{contractName},</if>
            <if test="responsiblePerson != null and responsiblePerson != ''">responsible_person =
                #{responsiblePerson},
            </if>
            <if test="contractState != null">contract_state = #{contractState},</if>
            <if test="rate != null">rate = #{rate},</if>
            <if test="state != null">state = #{state},</if>
            <if test="lastCustomerId != null">last_customer_id = #{lastCustomerId},</if>
            <if test="lastCustomerName != null and lastCustomerName != ''">last_customer_name = #{lastCustomerName},</if>
            <if test="legalPerson != null and legalPerson != ''">legal_person = #{legalPerson},</if>
            <if test="registerAddr != null and registerAddr != ''">register_addr = #{registerAddr},</if>
            <if test="abstracts != null and abstracts != ''">abstracts = #{abstracts},</if>
            <if test="signingTime != null">signing_time = #{signingTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="approveState != null">approve_state = #{approveState},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="customerContractId != null">customer_contract_id = #{customerContractId},</if>
            <if test="customerContractName != null">customer_contract_name = #{customerContractName},</if>
            <if test="contractType != null">contract_type = #{contractType},</if>
            <if test="freightForwarderId != null">freight_forwarder_id = #{freightForwarderId},</if>
            <if test="isPrepay != null">is_prepay = #{isPrepay},</if>
            <if test="needAuditing != null">need_auditing = #{needAuditing},</if>
            <if test="insuranceState != null">insurance_state = #{insuranceState},</if>
            <if test="effectTime != null">effect_time = #{effectTime},</if>
            <if test="subchainState != null">subchain_state = #{subchainState},</if>
            <if test="isTaxUploaded != null">is_tax_uploaded = #{isTaxUploaded},</if>
            <if test="effectiveTime != null">effective_time = #{effectiveTime},</if>
            <if test="timeUnit != null">time_unit = #{timeUnit},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrameworkContractById" parameterType="Long">
        delete
        from framework_contract
        where id = #{id}
    </delete>

    <delete id="deleteFrameworkContractByIds" parameterType="String">
        delete from framework_contract where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
