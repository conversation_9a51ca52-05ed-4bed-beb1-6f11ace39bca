<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.freightforwarder.mapper.FreightForwarderInfoMapper">

    <resultMap type="FreightForwarderInfo" id="FreightForwarderInfoResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="creditCode" column="credit_code"/>
        <result property="contact" column="contact"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="state" column="state"/>
        <result property="invoiceHeader" column="invoice_header"/>
        <result property="socialCreditCode" column="social_credit_code"/>
        <result property="address" column="address"/>
        <result property="phone" column="phone"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankCard" column="bank_card"/>
        <result property="wlhyPermitNumber" column="wlhy_permit_number"/>
        <result property="wlhyUploadAccount" column="wlhy_upload_account"/>
        <result property="wlhyUploadPassword" column="wlhy_upload_password"/>
        <result property="wlhyUploadProvince" column="wlhy_upload_province"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="insuranceValidPeriodTo" column="insurance_valid_period_to"/>
        <result property="licenseValidPeriodTo" column="license_valid_period_to"/>
        <result property="customerId" column="customer_id"/>
        <result property="isUpetc" column="is_upetc"/>
        <result property="limitAmount" column="limit_amount"/>
        <result property="effectiveTime" column="effective_time"/>
        <result property="timeUnit" column="time_unit"/>
        <result property="businessType" column="business_type"/>
        <result property="trajectoryDistance" column="trajectory_distance"/>
        <result property="trajectoryTime" column="trajectory_time"/>
        <result property="taxUploadRule" column="tax_upload_rule"/>
        <result property="isTaxUpload" column="is_tax_upload"/>
        <result property="taxUploadAccount" column="tax_upload_account"/>
        <result property="taxUploadPassword" column="tax_upload_password"/>
        <result property="waybillIsImport" column="waybill_is_import"/>
    </resultMap>

    <sql id="selectFreightForwarderInfoVo">
        select id,
               name,
               credit_code,
               contact,
               contact_phone,
               state,
               invoice_header,
               social_credit_code,
               address,
               phone,
               bank_name,
               bank_card,
               wlhy_permit_number,
               wlhy_upload_account,
               wlhy_upload_password,
               wlhy_upload_province,
               create_by,
               create_time,
               update_by,
               update_time,
               insurance_valid_period_to,
               license_valid_period_to,
               customer_id,
               is_upetc,
               limit_amount,
               effective_time,
               time_unit,
               business_type,
               trajectory_distance,
               trajectory_time,
               tax_upload_rule,
               is_tax_upload,
               tax_upload_account,
               tax_upload_password,
               waybill_is_import
        from freight_forwarder_info
    </sql>

    <select id="selectFreightForwarderInfoList" parameterType="FreightForwarderInfo"
            resultMap="FreightForwarderInfoResult">
        <include refid="selectFreightForwarderInfoVo"/>
        <where>
            <if test="name != null and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="creditCode != null and creditCode != ''">and credit_code = #{creditCode}</if>
            <if test="contact != null and contact != ''">and contact = #{contact}</if>
            <if test="contactPhone != null and contactPhone != ''">and contact_phone = #{contactPhone}</if>
            <if test="state != null">and state = #{state}</if>
            <if test="invoiceHeader != null and invoiceHeader != ''">and invoice_header = #{invoiceHeader}</if>
            <if test="socialCreditCode != null and socialCreditCode != ''">and social_credit_code = #{socialCreditCode}</if>
            <if test="address != null and address != ''">and address = #{address}</if>
            <if test="phone != null and phone != ''">and phone = #{phone}</if>
            <if test="bankName != null and bankName != ''">and bank_name like concat('%', #{bankName}, '%')</if>
            <if test="bankCard != null and bankCard != ''">and bank_card = #{bankCard}</if>
            <if test="wlhyPermitNumber != null and wlhyPermitNumber != ''">and wlhy_permit_number = #{wlhyPermitNumber}</if>
            <if test="wlhyUploadAccount != null and wlhyUploadAccount != ''">and wlhy_upload_account = #{wlhyUploadAccount}</if>
            <if test="wlhyUploadPassword != null and wlhyUploadPassword != ''">and wlhy_upload_password = #{wlhyUploadPassword}</if>
            <if test="wlhyUploadProvince != null">and wlhy_upload_province = #{wlhyUploadProvince}</if>
            <if test="insuranceValidPeriodTo != null">and insurance_valid_period_to = #{insuranceValidPeriodTo}</if>
            <if test="licenseValidPeriodTo != null">and license_valid_period_to = #{licenseValidPeriodTo}</if>
            <if test="customerId != null">and customer_id = #{customerId}</if>
            <if test="businessType != null">and business_type = #{businessType}</if>
            <if test="trajectoryDistance != null">and trajectory_distance = #{trajectoryDistance}</if>
            <if test="taxUploadRule != null">and tax_upload_rule = #{taxUploadRule}</if>
            <if test="isTaxUpload != null">and is_tax_upload = #{isTaxUpload}</if>
            <if test="taxUploadAccount != null and taxUploadAccount != ''">and tax_upload_account = #{taxUploadAccount}</if>
            <if test="taxUploadPassword != null and taxUploadPassword != ''">and tax_upload_password = #{taxUploadPassword}</if>
            <if test="waybillIsImport != null and waybillIsImport != ''">and waybill_is_import = #{waybillIsImport}</if>
        </where>
    </select>

    <select id="selectFreightForwarderInfoById" parameterType="Long" resultMap="FreightForwarderInfoResult">
        <include refid="selectFreightForwarderInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectFreightForwarderInfoByIds" resultMap="FreightForwarderInfoResult">
        <include refid="selectFreightForwarderInfoVo"/>
        where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFreightForwarderInfo" parameterType="FreightForwarderInfo">
        insert into freight_forwarder_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="creditCode != null and creditCode != ''">credit_code,</if>
            <if test="contact != null and contact != ''">contact,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="state != null">state,</if>
            <if test="invoiceHeader != null and invoiceHeader != ''">invoice_header,</if>
            <if test="socialCreditCode != null and socialCreditCode != ''">social_credit_code,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="bankName != null and bankName != ''">bank_name,</if>
            <if test="bankCard != null and bankCard != ''">bank_card,</if>
            <if test="wlhyPermitNumber != null and wlhyPermitNumber != ''">wlhy_permit_number,</if>
            <if test="wlhyUploadAccount != null and wlhyUploadAccount != ''">wlhy_upload_account,</if>
            <if test="wlhyUploadPassword != null and wlhyUploadPassword != ''">wlhy_upload_password,</if>
            <if test="wlhyUploadProvince != null">wlhy_upload_province,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="insuranceValidPeriodTo != null">insurance_valid_period_to,</if>
            <if test="licenseValidPeriodTo != null">license_valid_period_to,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="businessType != null">business_type,</if>
            <if test="trajectoryDistance != null">trajectory_distance,</if>
            <if test="taxUploadRule != null">tax_upload_rule,</if>
            <if test="isTaxUpload != null">is_tax_upload,</if>
            <if test="taxUploadAccount != null and taxUploadAccount != ''">tax_upload_account,</if>
            <if test="taxUploadPassword != null and taxUploadPassword != ''">tax_upload_password</if>
            <if test="waybillIsImport != null">waybill_is_import</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="creditCode != null and creditCode != ''">#{creditCode},</if>
            <if test="contact != null and contact != ''">#{contact},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="state != null">#{state},</if>
            <if test="invoiceHeader != null and invoiceHeader != ''">#{invoiceHeader},</if>
            <if test="socialCreditCode != null and socialCreditCode != ''">#{socialCreditCode},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="bankName != null and bankName != ''">#{bankName},</if>
            <if test="bankCard != null and bankCard != ''">#{bankCard},</if>
            <if test="wlhyPermitNumber != null and wlhyPermitNumber != ''">#{wlhyPermitNumber},</if>
            <if test="wlhyUploadAccount != null and wlhyUploadAccount != ''">#{wlhyUploadAccount},</if>
            <if test="wlhyUploadPassword != null and wlhyUploadPassword != ''">#{wlhyUploadPassword},</if>
            <if test="wlhyUploadProvince != null">#{wlhyUploadProvince},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="insuranceValidPeriodTo != null">#{insuranceValidPeriodTo},</if>
            <if test="licenseValidPeriodTo != null">#{licenseValidPeriodTo},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="trajectoryDistance != null">#{trajectoryDistance},</if>
            <if test="taxUploadRule != null">#{taxUploadRule},</if>
            <if test="isTaxUpload != null">#{isTaxUpload},</if>
            <if test="taxUploadAccount != null and taxUploadAccount != ''">#{taxUploadAccount},</if>
            <if test="taxUploadPassword != null and taxUploadPassword != ''">#{taxUploadPassword},</if>
            <if test="waybillIsImport != null">#{waybillIsImport},</if>
        </trim>
    </insert>

    <update id="updateFreightForwarderInfo" parameterType="FreightForwarderInfo">
        update freight_forwarder_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="creditCode != null and creditCode != ''">credit_code = #{creditCode},</if>
            <if test="contact != null and contact != ''">contact = #{contact},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="state != null">state = #{state},</if>
            <if test="invoiceHeader != null and invoiceHeader != ''">invoice_header = #{invoiceHeader},</if>
            <if test="socialCreditCode != null and socialCreditCode != ''">social_credit_code = #{socialCreditCode},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="bankName != null and bankName != ''">bank_name = #{bankName},</if>
            <if test="bankCard != null and bankCard != ''">bank_card = #{bankCard},</if>
            <if test="wlhyPermitNumber != null and wlhyPermitNumber != ''">wlhy_permit_number = #{wlhyPermitNumber},</if>
            <if test="wlhyUploadAccount != null and wlhyUploadAccount != ''">wlhy_upload_account = #{wlhyUploadAccount},</if>
            <if test="wlhyUploadPassword != null and wlhyUploadPassword != ''">wlhy_upload_password = #{wlhyUploadPassword},</if>
            <if test="wlhyUploadProvince != null">wlhy_upload_province = #{wlhyUploadProvince},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="insuranceValidPeriodTo != null">insurance_valid_period_to = #{insuranceValidPeriodTo},</if>
            <if test="licenseValidPeriodTo != null">license_valid_period_to = #{licenseValidPeriodTo},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="limitAmount != null">limit_amount = #{limitAmount},</if>
            <if test="effectiveTime != null">effective_time = #{effectiveTime},</if>
            <if test="timeUnit != null">time_unit = #{timeUnit},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="trajectoryDistance != null">trajectory_distance = #{trajectoryDistance},</if>
            <if test="taxUploadRule != null">tax_upload_rule = #{taxUploadRule},</if>
            <if test="isTaxUpload != null">is_tax_upload = #{isTaxUpload},</if>
            <if test="taxUploadAccount != null and taxUploadAccount != ''">tax_upload_account = #{taxUploadAccount},
            </if>
            <if test="taxUploadPassword != null and taxUploadPassword != ''">tax_upload_password = #{taxUploadPassword},</if>
            <if test="waybillIsImport != null">waybill_is_import = #{waybillIsImport},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="disableShipFreightForwarderInfoById">
        update freight_forwarder_info
        set state = 1
        where id = #{id}
    </update>

    <delete id="deleteFreightForwarderInfoById" parameterType="Long">
        delete
        from freight_forwarder_info
        where id = #{id}
    </delete>

    <delete id="deleteFreightForwarderInfoByIds" parameterType="String">
        delete from freight_forwarder_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCustomer" resultType="customerVo">
        select id, name
        from freight_forwarder_info
    </select>

    <select id="selectShipperWithFreightForwarder" resultMap="FreightForwarderInfoResult">
        select id, name
        from freight_forwarder_info ffi
        join freight_forwarder_customer_relation ffcr on ffi.id = ffcr.freight_forwarder_id and ffcr.customer_id = #{shipperId}
        group by ffcr.freight_forwarder_id
        order by ffcr.create_time desc
    </select>

    <select id="queryIds" resultType="java.lang.Long">
        select id
        from freight_forwarder_info
        group by id
    </select>

    <select id="getFreightForwarderInfoIds" resultType="long">
        select id
        from freight_forwarder_info
    </select>
    <select id="selectFreightForwarderInfoByName" resultMap="FreightForwarderInfoResult">
        select id, name, credit_code
        from freight_forwarder_info
        where name = #{name}
          and state = 0 limit 1
    </select>

    <!-- 查询所有网络货运人信息列表 -->
    <select id="getAllList" resultMap="FreightForwarderInfoResult">
        SELECT id,
               name
        FROM freight_forwarder_info
        ORDER BY create_time DESC
    </select>

    <select id="selectFreightForwarderInfoByTenantId" resultMap="FreightForwarderInfoResult">
        select *
        from freight_forwarder_info
        where customer_id = #{freightForwarderId}
    </select>
</mapper>
