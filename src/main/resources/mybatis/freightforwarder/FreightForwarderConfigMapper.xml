<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zly.project.freightforwarder.mapper.FreightForwarderConfigMapper">

    <!-- 结果映射 -->
    <resultMap id="FreightForwarderConfigResult" type="com.zly.project.freightforwarder.domain.FreightForwarderConfig">
        <id property="freightForwarderId" column="freight_forwarder_id"/>
        <result property="freightForwarderName" column="freight_forwarder_name"/>
        <result property="freightForwarderLoginLogo" column="freight_forwarder_login_logo"/>
        <result property="freightForwarderLogo" column="freight_forwarder_logo"/>
		<result property="freightForwarderMiniLogo" column="freight_forwarder_mini_logo"/>
		<result property="freightForwarderDomain" column="freight_forwarder_domain"/>
		<result property="isAuditFile" column="is_audit_file"/>
		<result property="isAuditWaybill" column="is_audit_waybill"/>
		<result property="shortCode" column="short_code"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="icpCode" column="icp_code"/>
		<result property="vehicleUseCharacter" column="vehicle_use_character"/>
		<result property="familyReceivePayment" column="family_receive_payment"/>
		<result property="supportColors" column="support_colors"/>
		<result property="towTrajectoryVerification" column="tow_trajectory_verification"/>
		<result property="isEmail" column="is_email"/>
		<result property="email" column="email"/>
		<result property="password" column="password"/>
	</resultMap>

    <!-- 插入操作 -->
    <insert id="insertFreightForwarderConfig" parameterType="FreightForwarderConfig">
        INSERT INTO freight_forwarder_config (
        <if test="freightForwarderId != null">freight_forwarder_id,</if>
        <if test="freightForwarderName != null">freight_forwarder_name,</if>
		<if test="freightForwarderLoginLogo != null">freight_forwarder_login_logo,</if>
		<if test="freightForwarderLogo != null">freight_forwarder_logo,</if>
		<if test="freightForwarderMiniLogo != null">freight_forwarder_mini_logo,</if>
		<if test="freightForwarderDomain != null">freight_forwarder_domain,</if>
		<if test="isAuditFile != null">is_audit_file,</if>
		<if test="isAuditWaybill != null">is_audit_waybill,</if>
		<if test="shortCode != null">short_code,</if>
		<if test="icpCode != null">icp_code,</if>
		<if test="vehicleUseCharacter != null">vehicle_use_character,</if>
		<if test="familyReceivePayment != null">family_receive_payment,</if>
		<if test="supportColors != null">support_colors,</if>
		<if test="towTrajectoryVerification != null">tow_trajectory_verification,</if>
		create_time,
		update_time
		) VALUES (
		<if test="freightForwarderId != null">#{freightForwarderId},</if>
		<if test="freightForwarderName != null">#{freightForwarderName},</if>
		<if test="freightForwarderLoginLogo != null">#{freightForwarderLoginLogo},</if>
		<if test="freightForwarderLogo != null">#{freightForwarderLogo},</if>
		<if test="freightForwarderMiniLogo != null">#{freightForwarderMiniLogo},</if>
		<if test="freightForwarderDomain != null">#{freightForwarderDomain},</if>
		<if test="isAuditFile != null">#{isAuditFile},</if>
		<if test="isAuditWaybill != null">#{isAuditWaybill},</if>
		<if test="shortCode != null">#{shortCode},</if>
		<if test="icpCode != null">#{icpCode},</if>
		<if test="vehicleUseCharacter != null">#{vehicleUseCharacter},</if>
		<if test="familyReceivePayment != null">#{familyReceivePayment},</if>
		<if test="supportColors != null">#{supportColors},</if>
		<if test="towTrajectoryVerification != null">#{towTrajectoryVerification},</if>
		#{createTime},
		#{updateTime}
		)
	</insert>

    <!-- 删除操作 -->
    <delete id="deleteFreightForwarderConfigById" parameterType="Long">
        DELETE
        FROM freight_forwarder_config
        WHERE freight_forwarder_id = #{id}
    </delete>

    <!-- 更新操作 -->
    <update id="updateFreightForwarderConfig" parameterType="FreightForwarderConfig">
        UPDATE freight_forwarder_config
        <set>
            <if test="freightForwarderName != null and freightForwarderName != ''">
                freight_forwarder_name = #{freightForwarderName},
            </if>
            <if test="freightForwarderLoginLogo != null and freightForwarderLoginLogo != ''">
                freight_forwarder_login_logo = #{freightForwarderLoginLogo},
            </if>
            <if test="freightForwarderLogo != null and freightForwarderLogo != ''">
                freight_forwarder_logo = #{freightForwarderLogo},
            </if>
            <if test="freightForwarderMiniLogo != null and freightForwarderMiniLogo != ''">
                freight_forwarder_mini_logo = #{freightForwarderMiniLogo},
            </if>
            <!--<if test="freightForwarderDomain != null and freightForwarderDomain != ''">
                freight_forwarder_domain = #{freightForwarderDomain},
            </if>-->
            <if test="icpCode != null and icpCode != ''">
                icp_code = #{icpCode},
            </if>
            <if test="isAuditFile != null">
				is_audit_file = #{isAuditFile},
			</if>
			<if test="isAuditWaybill != null">
				is_audit_waybill = #{isAuditWaybill},
			</if>
			<if test="vehicleUseCharacter != null">
				vehicle_use_character = #{vehicleUseCharacter},
			</if>
			<if test="familyReceivePayment != null">
				family_receive_payment = #{familyReceivePayment},
			</if>
			<if test="supportColors != null">
				support_colors = #{supportColors},
			</if>
			<if test="towTrajectoryVerification != null">
				tow_trajectory_verification = #{towTrajectoryVerification},
			</if>
			<if test="isEmail != null">
				is_email = #{isEmail},
			</if>
			<if test="email != null">
				email = #{email},
			</if>
			<if test="password != null">
				password = #{password},
			</if>
			update_time = #{updateTime}
		</set>
        WHERE freight_forwarder_id = #{freightForwarderId}
    </update>

    <!-- 根据ID查询操作 -->
    <select id="selectFreightForwarderConfigById" resultMap="FreightForwarderConfigResult" parameterType="Long">
        SELECT freight_forwarder_id,
			   freight_forwarder_name,
			   freight_forwarder_login_logo,
			   freight_forwarder_logo,
			   freight_forwarder_mini_logo,
			   freight_forwarder_domain,
			   update_time,
			   create_time,
			   is_audit_file,
			   is_audit_waybill,
			   short_code,
			   icp_code,
			   vehicle_use_character,
			   family_receive_payment,
			   support_colors,
			   tow_trajectory_verification,
			   is_email,
			   email,
			   password
		FROM freight_forwarder_config
		WHERE freight_forwarder_id = #{id}
	</select>

    <!-- 查询所有操作 -->
    <select id="selectAllFreightForwarderConfig" resultMap="FreightForwarderConfigResult">
        SELECT *
        FROM freight_forwarder_config
    </select>

    <select id="selectFreightForwarderConfig" resultMap="FreightForwarderConfigResult" parameterType="com.zly.project.freightforwarder.domain.FreightForwarderConfig">
        SELECT
        freight_forwarder_id,
        freight_forwarder_name,
		freight_forwarder_login_logo,
		freight_forwarder_logo,
		freight_forwarder_mini_logo,
		freight_forwarder_domain,
		is_audit_waybill,
		is_audit_file,
		icp_code,
		vehicle_use_character,
		family_receive_payment,
		create_time,
		update_time,
		is_email,
		email,
		password
		FROM
		freight_forwarder_config
		<where>
            <if test="freightForwarderId != null">
                AND freight_forwarder_id = #{freightForwarderId}
            </if>
            <if test="freightForwarderName != null and freightForwarderName != ''">
                AND freight_forwarder_name = #{freightForwarderName}
            </if>
            <if test="freightForwarderLoginLogo != null and freightForwarderLoginLogo != ''">
                AND freight_forwarder_login_logo = #{freightForwarderLoginLogo}
            </if>
            <if test="freightForwarderLogo != null and freightForwarderLogo != ''">
                AND freight_forwarder_logo = #{freightForwarderLogo}
            </if>
            <if test="freightForwarderMiniLogo != null and freightForwarderMiniLogo != ''">
                AND freight_forwarder_mini_logo = #{freightForwarderMiniLogo}
            </if>
            <if test="freightForwarderDomain != null and freightForwarderDomain != ''">
                AND freight_forwarder_domain = #{freightForwarderDomain}
            </if>
            <if test="isAuditWaybill != null">
                AND is_audit_waybill = #{isAuditWaybill}
            </if>
            <if test="isAuditFile != null">
                AND is_audit_file = #{isAuditFile}
            </if>
            <if test="vehicleUseCharacter != null">
                AND vehicle_use_character = #{vehicleUseCharacter}
            </if>
            <if test="familyReceivePayment != null">
                AND family_receive_payment = #{familyReceivePayment}
            </if>
        </where>
        limit 1
    </select>

    <select id="selectAuditConfigByFreightForwarderId" resultMap="FreightForwarderConfigResult">
        select *
        from freight_forwarder_config
        where freight_forwarder_id = #{freightForwarderId}
    </select>

    <update id="updateWaybillConfig">
        update freight_forwarder_config
        set is_audit_waybill = #{parameter}
        where freight_forwarder_id = #{freightForwarderId}
    </update>

    <update id="updateFileConfig">
        update freight_forwarder_config
        set is_audit_file = #{parameter}
        where freight_forwarder_id = #{freightForwarderId}
    </update>
</mapper>
