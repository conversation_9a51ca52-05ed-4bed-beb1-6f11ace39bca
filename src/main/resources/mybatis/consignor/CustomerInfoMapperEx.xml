<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.consignor.customer.mapper.CustomerInfoMapperEx">
    <resultMap type="com.zly.project.consignor.customer.domain.CustomerInfo" id="BaseResultMap"
               extends="com.zly.project.consignor.customer.mapper.CustomerInfoMapper.CustomerInfoResult">
    </resultMap>
    <!-- 将到期合同中客户的合同状态变更为'已到期' -->
    <update id="customerContractStateExpired">
        UPDATE customer_info
        SET contract_state = 2
        WHERE id IN (SELECT customer_id
                     FROM customer_contract t
                     WHERE NOT EXISTS(SELECT 1
                                      FROM customer_contract
                                      WHERE customer_id = t.customer_id
                                        AND t.create_time <![CDATA[<]]> create_time)
                       AND end_time <![CDATA[<=]]> curdate())
          AND contract_state = 1
    </update>
    <update id="updateCustomerInfo">
        update customer_info
        set approve_state = #{approveState}
        where id = #{id}
    </update>
    <update id="updateCustomerInfoStateByFreightForwarderId">
        update customer_info
        set state = 0
        where freight_forwarder_id = #{id}
    </update>
    <update id="disableCustomerInfoByFreightForwarderId">
        update customer_info
        set state = 1
        where id in (select customer_id from freight_forwarder_customer_relation where freight_forwarder_id = #{id})
    </update>
    <update id="updateCustomerInfoStateByIds">
        update customer_info set state = 0 where id in
        <foreach item="id" collection="customerIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateCustomerInfoEx">
        update customer_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerCode != null and customerCode != ''">customer_code = #{customerCode},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="creditCode != null and creditCode != ''">credit_code = #{creditCode},</if>
            <if test="legal != null and legal != ''">legal = #{legal},</if>
            <if test="legalIdNo != null and legalIdNo != ''">legal_id_no = #{legalIdNo},</if>
            <!--            <if test="contact != null and contact != ''">contact = #{contact},</if>-->
            <!--            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>-->
            <if test="accountNo != null and accountNo != ''">account_no = #{accountNo},</if>
            <if test="bankCode != null and bankCode != ''">bank_code = #{bankCode},</if>
            <if test="bankName != null and bankName != ''">bank_name = #{bankName},</if>
            <if test="state != null">state = #{state},</if>
            <if test="address != null">address = #{address},</if>
            <if test="contractState != null">contract_state = #{contractState},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="billHead != null and billHead != ''">bill_head = #{billHead},</if>
            <if test="taxNo != null and taxNo != ''">tax_no = #{taxNo},</if>
            <if test="unitAddress != null and unitAddress != ''">unit_address = #{unitAddress},</if>
            <if test="telephone != null and telephone != ''">telephone = #{telephone},</if>
            <if test="bank != null and bank != ''">bank = #{bank},</if>
            <if test="bankAccount != null and bankAccount != ''">bank_account = #{bankAccount},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="approveState != null">approve_state = #{approveState},</if>
            <if test="resourceType != null">resource_type = #{resourceType},</if>
            <if test="freightForwarderId != null">freight_forwarder_id = #{freightForwarderId},</if>
            <if test="groupType != null">group_type = #{groupType},</if>
            <if test="remainingLocateTimes != null">remaining_locate_times = #{remainingLocateTimes},</if>
            <if test="province != null">province = #{province},</if>
            <if test="provinceCode != null">province_code = #{provinceCode},</if>
            <if test="city != null">city = #{city},</if>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="area != null">area = #{area},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
        </trim>
        where id = #{id}
    </update>
    <select id="selectCustomerInfoList" resultType="com.zly.project.consignor.customer.domain.CustomerInfo">
        select id, customer_code, customer_name, credit_code, legal, legal_id_no, contact, contact_phone,
        account_no, bank_code, bank_name, state, address, contract_state, remark, bill_head, tax_no, unit_address,
        telephone, bank, bank_account, create_by, create_time, update_by, update_time, group_type
        from customer_info
        <where>
            <if test="customerName != null  and customerName != ''">and customer_name = #{customerName}</if>
        </where>
    </select>
    <select id="selectCustomerGrage" resultType="com.zly.project.consignor.customer.domain.res.CustomerInfoGradeRes">
        SELECT tmp1.grade,
               ifnull(tmp2.gradeSum, 0) as gradeSum
        FROM (SELECT 1 AS grade
              UNION ALL
              SELECT 2 AS grade
              UNION ALL
              SELECT 3 AS grade
              UNION ALL
              SELECT 4 AS grade
              UNION ALL
              SELECT 5 AS grade) as tmp1
                 LEFT JOIN (SELECT appraise.grade AS grade,
                                   count(*)       AS gradeSum
                            FROM customer_appraise as appraise
                            WHERE appraise.evaluator_id = #{id}
                              AND appraise.state = 0
                            GROUP BY grade) as tmp2 on tmp2.grade = tmp1.grade
    </select>

    <select id="findById" resultType="com.zly.project.consignor.customer.domain.CustomerInfoEx">
        select ci.id                   as id,
               ci.customer_code        as customerCode,
               ci.customer_name        as customerName,
               ci.credit_code          as creditCode,
               ci.contact,
               ci.contact_phone        as contactPhone,
               ci.account_no           as accountNo,
               ci.bank_code            as bankCode,
               ci.bank_name            as bankName,
               ci.state,
               ci.address,
               ci.contract_state       as contractState,
               ci.remark,
               ci.bill_head            as billHead,
               ci.tax_no               as taxNo,
               ci.unit_address         as unitAddress,
               ci.telephone,
               ci.bank,
               ci.bank_account         as bankAccount,
               ci.resource_type        as resourceType,
               ci.freight_forwarder_id as freightForwarderId,
               ci.group_type           as groupType,
               ci.legal                as legal,
               ci.legal_id_no          as legalIdNo,
               ci.province             as province,
               ci.province_code        as provinceCode,
               ci.city                 as city,
               ci.city_code            as cityCode,
               ci.area                 as area,
               ci.area_code            as areaCode,
               tu.user_name            as UserName,
               tu.user_id              as userId
        from customer_info ci
                 left join tenant_user tu
                           on ci.id = tu.tenant_id
                 left join tenant_user_role tur on tu.user_id = tur.user_id
                 left join tenant_role tr on tur.role_id = tr.role_id
        where tr.role_name = '管理员'
          and tr.role_key = 'admin'
          and tr.status = 0
          and ci.id = #{id}
          and tu.tenant_id = #{id}
          and tu.del_flag = 0
          and tr.tenant_id = #{id}
        order by tu.create_time
        limit 1
    </select>

    <!-- <select id="getGroupCustomerInfoList" resultType="com.zly.project.consignor.customer.domain.CustomerInfoEx">
         select a.serviceName,
         ci.id as id,
         ci.customer_code as customerCode,
         ci.customer_name as customerName,
         ci.credit_code as creditCode,
         ci.contact,
         ci.contact_phone as contactPhone,
         ci.account_no as accountNo,
         ci.bank_code as bankCode,
         ci.bank_name as bankName,
         ci.state,
         ci.address,
         ci.contract_state as contractState,
         ci.remark,
         ci.bill_head as billHead,
         ci.tax_no as taxNo,
         ci.unit_address as unitAddress,
         ci.telephone,
         ci.bank,
         ci.bank_account as bankAccount,
         ci.approve_state as approveState,
         ci.resource_type as resourceType,
         ci.freight_forwarder_id as freightForwarderId,
         ci.group_type as groupType,
         tu.user_name as UserName,
         tu.user_id as userId
         from customer_info ci
         inner join tenant_user tu on ci.id = tu.tenant_id
         inner join tenant_user_role tur on tu.user_id = tur.user_id
         inner join tenant_role tr on tur.role_id = tr.role_id
         inner join (SELECT
         group_concat(title_content) as serviceName,customer_id,group_concat(customer_service_id)
         FROM
         customer_service_info csi
         right JOIN home_service hs ON csi.customer_service_id = hs.id
         where csi.open_state = 1
         <if test="serviceIds != null">
             and hs.id in
             <foreach item="serviceId" collection="serviceIds" open="(" separator="," close=")">
                 #{serviceId}
             </foreach>
         </if>
         group by
         customer_id) b on b.customer_id=ci.id
         left join (SELECT
         group_concat(title_content order by customer_service_id) as serviceName,customer_id,group_concat(customer_service_id order by customer_service_id) as serviceIds
         FROM
         customer_service_info csi
         LEFT JOIN home_service hs ON csi.customer_service_id = hs.id
         where csi.open_state = 1
         group by
         customer_id) a on a.customer_id=b.customer_id
         where ci.freight_forwarder_id = 0
         and tr.role_name = '管理员'
         and tr.role_key = 'admin'
         and tr.status = 0
         <if test="state != null">
             and ci.state = #{state}
         </if>
         <if test="approveState != null">
             and ci.approve_state = #{approveState}
         </if>
         <if test="customerName != null and customerName !='' ">
             and ci.customer_name = #{customerName}
         </if>
         <if test="userName != null and userName !='' ">
             and tu.user_name = #{userName}
         </if>
         <if test="serviceIds != null ">
             and a.serviceIds like concat('%', #{serviceName}, '%')
         </if>
         group by ci.id
     </select>-->

    <select id="getGroupCustomerInfoList" resultType="com.zly.project.consignor.customer.domain.CustomerInfoEx">
        SELECT
        b.serviceName,
        ci.id AS id,
        ci.customer_code AS customerCode,
        ci.customer_name AS customerName,
        ci.credit_code AS creditCode,
        ci.contact,
        ci.contact_phone AS contactPhone,
        ci.account_no AS accountNo,
        ci.bank_code AS bankCode,
        ci.bank_name AS bankName,
        ci.state,
        ci.address,
        ci.contract_state AS contractState,
        ci.remark,
        ci.bill_head AS billHead,
        ci.tax_no AS taxNo,
        ci.unit_address AS unitAddress,
        ci.telephone,
        ci.bank,
        ci.bank_account AS bankAccount,
        ci.approve_state AS approveState,
        ci.resource_type AS resourceType,
        ci.freight_forwarder_id AS freightForwarderId,
        ci.group_type AS groupType,
        ci.create_time AS createTime,
        tu.user_name AS UserName,
        tu.user_id AS userId
        FROM
        customer_info ci
        INNER JOIN tenant_user tu ON ci.id = tu.tenant_id
        INNER JOIN tenant_user_role tur ON tu.user_id = tur.user_id
        INNER JOIN tenant_role tr ON tur.role_id = tr.role_id
        INNER JOIN (
        SELECT
        group_concat(title_content) AS serviceName,
        customer_id
        FROM
        customer_service_info csi
        RIGHT JOIN home_service hs ON csi.customer_service_id = hs.id
        WHERE
        csi.open_state = 1
        and hs.parent_id = 0
        and hs.id != 7
        GROUP BY
        customer_id
        ) b ON b.customer_id = ci.id
        WHERE
        ci.freight_forwarder_id = 0
        AND tr.role_name = '管理员'
        AND tr.role_key = 'admin'
        AND tr. STATUS = 0
        and tu.del_flag = 0
        and tu.status = 0
        <if test="id != null">
            and ci.id = #{id}
        </if>
        <if test="state != null">
            and ci.state = #{state}
        </if>
        <if test="approveState != null">
            and ci.approve_state = #{approveState}
        </if>
        <if test="customerName != null and customerName !='' ">
            and ci.customer_name like concat ('%', #{customerName}, '%')
        </if>
        <if test="userName != null and userName !='' ">
            and tu.user_name like concat (#{userName},'%')
        </if>
        group by ci.id
    </select>

    <select id="getCustomerInfoList" resultType="com.zly.project.consignor.customer.domain.CustomerInfoEx">
        SELECT *
        FROM (
        SELECT
        a.serviceName,
        ci.id AS id,
        ci.customer_code AS customerCode,
        ci.customer_name AS customerName,
        ci.credit_code AS creditCode,
        ci.contact,
        ci.contact_phone AS contactPhone,
        ci.account_no AS customerAccount,
        ci.bank_code AS bankCode,
        ci.bank_name AS bankName,
        ci.state,
        ci.address,
        ci.contract_state AS contractState,
        ci.remark,
        ci.bill_head AS billHead,
        ci.tax_no AS taxNo,
        ci.unit_address AS unitAddress,
        ci.telephone,
        ci.bank,
        ci.bank_account AS bankAccount,
        ci.approve_state AS approveState,
        ci.resource_type AS resourceType,
        ci.freight_forwarder_id AS freightForwarderId,
        ci.group_type AS groupType,
        ci.create_time,
        COALESCE( MIN(CASE WHEN contract.state = 1 THEN 1 END), MIN(contract.state), -1 ) AS
        customerForwarderContractState
        FROM freight_forwarder_customer_relation relation
        LEFT JOIN customer_info ci ON relation.customer_id = ci.id
        LEFT JOIN customer_forwarder_contract contract ON relation.freight_forwarder_id = contract.freight_forwarder_id
        AND relation.customer_id = contract.customer_id AND contract.state > 0
        LEFT JOIN (
        SELECT
        GROUP_CONCAT(title_content ORDER BY customer_service_id) AS serviceName,
        customer_id,
        GROUP_CONCAT(customer_service_id ORDER BY customer_service_id) AS serviceIds
        FROM customer_service_info csi
        LEFT JOIN home_service hs ON csi.customer_service_id = hs.id
        WHERE csi.open_state = 1 AND hs.parent_id = 0
        GROUP BY customer_id
        ) a ON a.customer_id = ci.id
        WHERE relation.freight_forwarder_id = #{freightForwarderId}
        <if test="state != null">
            AND ci.state = #{state}
        </if>
        <if test="approveState != null">
            AND ci.approve_state = #{approveState}
        </if>
        <if test="customerName != null and customerName != ''">
            AND ci.customer_name LIKE CONCAT('%', #{customerName}, '%')
        </if>
        GROUP BY ci.id
        ) temp
        <if test="customerForwarderContractState != null">
            <if test="customerForwarderContractState > 0">
                WHERE temp.customerForwarderContractState = #{customerForwarderContractState}
            </if>
            <if test="customerForwarderContractState == -1">
                WHERE temp.customerForwarderContractState = -1
            </if>
        </if>
        order by temp.create_time desc
    </select>

    <select id="selectCustomerList" resultMap="BaseResultMap">
        SELECT * FROM customer_info
        WHERE id IN
        <foreach collection="customerIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectCustomerInfoListByFreightForwarderId" resultMap="BaseResultMap">
        select ci.* from customer_info ci
        left join freight_forwarder_customer_relation r on r.customer_id = ci.id
        <where>
            <if test="customerName != null and customerName != ''">and ci.customer_name like concat('%',
                #{customerName}, '%')
            </if>
            <if test="state != null ">and ci.state = #{state}</if>
            <if test="approveState != null ">and ci.approve_state = #{approveState}</if>
            <if test="freightForwarderId != null  and freightForwarderId != ''">and r.freight_forwarder_id =
                #{freightForwarderId}
            </if>
        </where>
    </select>

    <select id="getSocialCodeCustomerInfoList" resultType="com.zly.project.consignor.customer.domain.CustomerInfoEx">
        SELECT
        b.serviceName,
        ci.id AS id,
        ci.customer_code AS customerCode,
        ci.customer_name AS customerName,
        ci.credit_code AS creditCode,
        ci.contact,
        ci.contact_phone AS contactPhone,
        ci.account_no AS accountNo,
        ci.bank_code AS bankCode,
        ci.bank_name AS bankName,
        ci.state,
        ci.address,
        ci.contract_state AS contractState,
        ci.remark,
        ci.bill_head AS billHead,
        ci.tax_no AS taxNo,
        ci.unit_address AS unitAddress,
        ci.telephone,
        ci.bank,
        ci.bank_account AS bankAccount,
        ci.approve_state AS approveState,
        ci.resource_type AS resourceType,
        ci.freight_forwarder_id AS freightForwarderId,
        ci.group_type AS groupType,
        tu.user_name AS UserName,
        tu.user_id AS userId
        FROM
        customer_info ci
        INNER JOIN tenant_user tu ON ci.id = tu.tenant_id
        INNER JOIN tenant_user_role tur ON tu.user_id = tur.user_id
        INNER JOIN tenant_role tr ON tur.role_id = tr.role_id
        INNER JOIN (
        SELECT
        group_concat(title_content) AS serviceName,
        customer_id
        FROM
        customer_service_info csi
        RIGHT JOIN home_service hs ON csi.customer_service_id = hs.id
        WHERE
        csi.open_state = 1
        and hs.parent_id = 0
        GROUP BY
        customer_id
        ) b ON b.customer_id = ci.id
        WHERE
        ci.freight_forwarder_id = 0
        and ci.credit_code != ''
        AND tr.role_name = '管理员'
        AND tr.role_key = 'admin'
        AND tr. STATUS = 0
        and tu.del_flag = 0
        <if test="state != null">
            and ci.state = #{state}
        </if>
        <if test="approveState != null">
            and ci.approve_state = #{approveState}
        </if>
        <if test="customerName != null and customerName !='' ">
            and ci.customer_name = #{customerName}
        </if>
        <if test="userName != null and userName !='' ">
            and tu.user_name like concat (#{userName},'%')
        </if>
        group by ci.id
    </select>
    <select id="optionCustomerList"
            resultType="com.zly.project.consignor.customer.domain.res.OptionCustomerRes">
        select
        ci.id as id,
        ci.customer_name as customerName
        from customer_info ci
        left join (SELECT
        group_concat(title_content) as serviceName,customer_id,group_concat(customer_service_id order by
        customer_service_id) as serviceIds
        FROM
        customer_service_info csi
        LEFT JOIN home_service hs ON csi.customer_service_id = hs.id
        where csi.open_state = 1
        and hs.parent_id = 0
        group by
        customer_id) a on a.customer_id=ci.id
        left join freight_forwarder_customer_relation ffcr on ffcr.customer_id = ci.id
        where
        ffcr.freight_forwarder_id = #{freightForwarderId}
        <if test="customerName != null and customerName !='' ">
            and ci.customer_name like concat('%', #{customerName}, '%')
        </if>
        group by ci.id
    </select>
    <select id="getShipGroupCustomerInfoList"
            resultType="com.zly.project.consignor.customer.domain.CustomerInfoEx">
        SELECT
        ci.id AS id,
        ci.customer_code AS customerCode,
        ci.customer_name AS customerName,
        ci.credit_code AS creditCode,
        ci.contact,
        ci.contact_phone AS contactPhone,
        ci.account_no AS accountNo,
        ci.bank_code AS bankCode,
        ci.bank_name AS bankName,
        ci.state,
        ci.address,
        ci.contract_state AS contractState,
        ci.remark,
        ci.bill_head AS billHead,
        ci.tax_no AS taxNo,
        ci.unit_address AS unitAddress,
        ci.telephone,
        ci.bank,
        ci.bank_account AS bankAccount,
        ci.approve_state AS approveState,
        ci.resource_type AS resourceType,
        ci.freight_forwarder_id AS freightForwarderId,
        ci.group_type AS groupType,
        tu.user_name AS UserName,
        tu.user_id AS userId
        FROM
        customer_info ci
        INNER JOIN tenant_user tu ON ci.id = tu.tenant_id
        INNER JOIN tenant_user_role tur ON tu.user_id = tur.user_id
        INNER JOIN tenant_role tr ON tur.role_id = tr.role_id
        WHERE
        ci.freight_forwarder_id = 0
        AND tr.role_name = '管理员'
        AND tr.role_key = 'admin'
        AND tr. STATUS = 0
        and tu.del_flag = 0
        and tu.status = 0
        <if test="id != null">
            and ci.id = #{id}
        </if>
        <if test="state != null">
            and ci.state = #{state}
        </if>
        <if test="approveState != null">
            and ci.approve_state = #{approveState}
        </if>
        <if test="customerName != null and customerName !='' ">
            and ci.customer_name like concat ('%', #{customerName}, '%')
        </if>
        <if test="userName != null and userName !='' ">
            and tu.user_name like concat (#{userName},'%')
        </if>
        group by ci.id
    </select>
    <select id="selectWaybillCustomerInfoListByFreightForwarderId"
            resultMap="BaseResultMap">
        select ci.id,ci.customer_name from customer_info ci
        left join freight_forwarder_customer_relation r on r.customer_id = ci.id
        inner join framework_contract_subchain fcs on ci.id = fcs.parent_customer_id AND fcs.is_leaf = 0
        <where>
            <if test="customerName != null and customerName != ''">and ci.customer_name like concat('%',
                #{customerName}, '%')
            </if>
            <if test="state != null ">and ci.state = #{state}</if>
            <if test="approveState != null ">and ci.approve_state = #{approveState}</if>
            <if test="freightForwarderId != null  and freightForwarderId != ''">and r.freight_forwarder_id =
                #{freightForwarderId}
            </if>
        </where>
        group by ci.id
    </select>

    <select id="selectCustomerServiceInfoByCustomerIds" resultMap="BaseResultMap">
        select * from customer_info where id in
        <foreach item="id" collection="customerIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getReviewedGroupCustomerInfoList"
            resultType="com.zly.project.consignor.customer.domain.CustomerInfoEx">
        SELECT
        ci.id AS id,
        ci.customer_code AS customerCode,
        ci.customer_name AS customerName,
        ci.credit_code AS creditCode,
        ci.contact AS contact,
        ci.legal AS legal,
        ci.contact_phone AS contactPhone,
        ci.account_no AS accountNo,
        ci.bank_code AS bankCode,
        ci.bank_name AS bankName,
        ci.state,
        ci.address,
        ci.contract_state AS contractState,
        ci.remark,
        ci.bill_head AS billHead,
        ci.tax_no AS taxNo,
        ci.unit_address AS unitAddress,
        ci.telephone,
        ci.bank,
        ci.bank_account AS bankAccount,
        ci.approve_state AS approveState,
        ci.resource_type AS resourceType,
        ci.freight_forwarder_id AS freightForwarderId,
        ci.group_type AS groupType,
        ci.create_time AS createTime
        FROM
        customer_info ci
        WHERE
        ci.freight_forwarder_id = 0
        <if test="id != null">
            and ci.id = #{id}
        </if>
        <if test="state != null">
            and ci.state = #{state}
        </if>
        <if test="approveState != null">
            and ci.approve_state = #{approveState}
        </if>
        <if test="customerName != null and customerName !='' ">
            and ci.customer_name like concat ('%', #{customerName}, '%')
        </if>
        <if test="creditCode != null and creditCode !='' ">
            and ci.credit_code = #{creditCode}
        </if>
        <if test="resourceType != null and resourceType !='' ">
            and ci.resource_type = #{resourceType}
        </if>
        group by ci.id
        ORDER BY ci.approve_state = 1 DESC,
        CASE WHEN ci.approve_state = 1 THEN ci.create_time END ASC,
        CASE WHEN ci.approve_state != 1 THEN ci.create_time END DESC
    </select>

    <select id="selectTaxCustomerInfoList" resultMap="BaseResultMap">
        select ci.id,ci.customer_name from customer_info ci
        left join freight_forwarder_customer_relation r on r.customer_id = ci.id
        inner join framework_contract_subchain fcs on ci.id = fcs.parent_customer_id AND fcs.is_leaf = 0
        join customer_forwarder_contract cfc on cfc.customer_id = ci.id and cfc.state = 1 and cfc.freight_forwarder_id =
        #{freightForwarderId}
        <where>
            <if test="customerName != null and customerName != ''">and ci.customer_name like concat('%',
                #{customerName}, '%')
            </if>
            <if test="state != null ">and ci.state = #{state}</if>
            <if test="approveState != null ">and ci.approve_state = #{approveState}</if>
            <if test="freightForwarderId != null  and freightForwarderId != ''">and r.freight_forwarder_id =
                #{freightForwarderId}
            </if>
        </where>
        group by ci.id
    </select>

    <select id="selectNewCustomerCount" resultType="integer">
        SELECT COUNT(1)
        FROM customer_info ci
                 INNER JOIN freight_forwarder_customer_relation ffc
                            on ci.id = ffc.customer_id and ffc.freight_forwarder_id = #{freightForwarderId}
        WHERE ci.create_time BETWEEN #{start} AND #{end}
    </select>

    <select id="selectActiveCustomerCount" resultType="integer">
        SELECT COUNT(DISTINCT ci.id)
        FROM waybill w
                 JOIN framework_contract_subchain fcs ON w.framework_contract_id = fcs.contract_id
            AND fcs.is_leaf = 0
            AND fcs.state = 0
                 JOIN customer_info ci ON ci.id = fcs.parent_customer_id
                 JOIN freight_forwarder_customer_relation ffc ON ci.id = ffc.customer_id
            AND w.freight_forwarder_id = #{freightForwarderId}
        WHERE w.create_time BETWEEN #{start} AND #{end}
    </select>
</mapper>
