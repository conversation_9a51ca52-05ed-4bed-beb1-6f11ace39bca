<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.consignor.customer.mapper.CustomerInfoMapper">

    <resultMap type="com.zly.project.consignor.customer.domain.CustomerInfo" id="CustomerInfoResult">
        <result property="id" column="id"/>
        <result property="customerCode" column="customer_code"/>
        <result property="customerName" column="customer_name"/>
        <result property="creditCode" column="credit_code"/>
        <result property="legal" column="legal"/>
        <result property="legalIdNo" column="legal_id_no"/>
        <result property="contact" column="contact"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="accountNo" column="account_no"/>
        <result property="bankCode" column="bank_code"/>
        <result property="bankName" column="bank_name"/>
        <result property="state" column="state"/>
        <result property="address" column="address"/>
        <result property="contractState" column="contract_state"/>
        <result property="remark" column="remark"/>
        <result property="billHead" column="bill_head"/>
        <result property="taxNo" column="tax_no"/>
        <result property="unitAddress" column="unit_address"/>
        <result property="telephone" column="telephone"/>
        <result property="bank" column="bank"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="approveState" column="approve_state"/>
        <result property="resourceType" column="resource_type"/>
        <result property="freightForwarderId" column="freight_forwarder_id"/>
        <result property="groupType" column="group_type"/>
        <result property="remainingLocateTimes" column="remaining_locate_times"/>
        <result property="province" column="province"/>
        <result property="provinceCode" column="province_code"/>
        <result property="city" column="city"/>
        <result property="cityCode" column="city_code"/>
        <result property="area" column="area"/>
        <result property="areaCode" column="area_code"/>
        <result property="confirmReceiptState" column="confirm_receipt_state"/>
        <result property="driverReceiptState" column="driver_receipt_state"/>
    </resultMap>

    <sql id="selectCustomerInfoVo">
        select id,
               customer_code,
               customer_name,
               credit_code,
               legal,
               legal_id_no,
               contact,
               contact_phone,
               account_no,
               bank_code,
               bank_name,
               state,
               address,
               contract_state,
               remark,
               bill_head,
               tax_no,
               unit_address,
               telephone,
               bank,
               bank_account,
               create_by,
               create_time,
               update_by,
               update_time,
               approve_state,
               resource_type,
               freight_forwarder_id,
               group_type,
               remaining_locate_times,
               province,
               province_code,
               city,
               city_code,
               area,
               area_code,
               confirm_receipt_state,
               driver_receipt_state
        from customer_info
    </sql>

    <select id="selectCustomerInfoList" parameterType="com.zly.project.consignor.customer.domain.CustomerInfo"
            resultMap="CustomerInfoResult">
        <include refid="selectCustomerInfoVo"/>
        <where>
            <if test="id != null ">and id = #{id}</if>
            <if test="customerCode != null  and customerCode != ''">and customer_code = #{customerCode}</if>
            <if test="customerName != null  and customerName != ''">and customer_name like concat('%', #{customerName},
                '%')
            </if>
            <if test="creditCode != null  and creditCode != ''">and credit_code = #{creditCode}</if>
            <if test="legal != null  and legal != ''">and legal = #{legal}</if>
            <if test="legalIdNo != null  and legalIdNo != ''">and legal_id_no = #{legalIdNo}</if>
            <if test="contact != null  and contact != ''">and contact = #{contact}</if>
            <if test="contactPhone != null  and contactPhone != ''">and contact_phone = #{contactPhone}</if>
            <if test="accountNo != null  and accountNo != ''">and account_no = #{accountNo}</if>
            <if test="bankCode != null  and bankCode != ''">and bank_code = #{bankCode}</if>
            <if test="bankName != null  and bankName != ''">and bank_name like concat('%', #{bankName}, '%')</if>
            <if test="state != null ">and state = #{state}</if>
            <if test="address != null  and address != ''">and address = #{address}</if>
            <if test="contractState != null ">and contract_state = #{contractState}</if>
            <if test="billHead != null  and billHead != ''">and bill_head = #{billHead}</if>
            <if test="taxNo != null  and taxNo != ''">and tax_no = #{taxNo}</if>
            <if test="unitAddress != null  and unitAddress != ''">and unit_address = #{unitAddress}</if>
            <if test="telephone != null  and telephone != ''">and telephone = #{telephone}</if>
            <if test="bank != null  and bank != ''">and bank = #{bank}</if>
            <if test="bankAccount != null  and bankAccount != ''">and bank_account = #{bankAccount}</if>
            <if test="createBy != null  and createBy != ''">and create_by = #{createBy}</if>
            <if test="approveState != null">and approve_state = #{approveState}</if>
            <if test="resourceType != null  and resourceType != ''">and resource_type = #{resourceType}</if>
            <if test="freightForwarderId != null  and freightForwarderId != ''">and freight_forwarder_id =
                #{freightForwarderId}
            </if>
            <if test="groupType != null  and groupType != ''">and group_type = #{groupType}</if>
            <if test="remainingLocateTimes != null  and remainingLocateTimes != ''">and remaining_locate_times =
                #{remainingLocateTimes}
            </if>
            <if test="confirmReceiptState != null">and confirm_receipt_state = #{confirmReceiptState}</if>
            <if test="driverReceiptState != null">and driver_receipt_state = #{driverReceiptState}</if>
            <if test="params != null and params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between
                #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
            <if test="params != null and params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between
                #{params.beginUpdateTime} and #{params.endUpdateTime}
            </if>
        </where>
    </select>

    <select id="getCustomerInfoList" parameterType="com.zly.project.consignor.customer.domain.CustomerInfo"
            resultMap="CustomerInfoResult">
        <include refid="selectCustomerInfoVo"/>
        <where>
            <if test="id != null ">and id = #{id}</if>
            <if test="customerCode != null  and customerCode != ''">and customer_code = #{customerCode}</if>
            <if test="customerName != null  and customerName != ''">and customer_name like concat('%', #{customerName},
                '%')
            </if>
            <if test="creditCode != null  and creditCode != ''">and credit_code = #{creditCode}</if>
            <if test="legal != null  and legal != ''">and legal = #{legal}</if>
            <if test="legalIdNo != null  and legalIdNo != ''">and legal_id_no = #{legalIdNo}</if>
            <if test="contact != null  and contact != ''">and contact = #{contact}</if>
            <if test="contactPhone != null  and contactPhone != ''">and contact_phone = #{contactPhone}</if>
            <if test="accountNo != null  and accountNo != ''">and account_no = #{accountNo}</if>
            <if test="bankCode != null  and bankCode != ''">and bank_code = #{bankCode}</if>
            <if test="bankName != null  and bankName != ''">and bank_name like concat('%', #{bankName}, '%')</if>
            <if test="state != null ">and state = #{state}</if>
            <if test="address != null  and address != ''">and address = #{address}</if>
            <if test="contractState != null ">and contract_state = #{contractState}</if>
            <if test="billHead != null  and billHead != ''">and bill_head = #{billHead}</if>
            <if test="taxNo != null  and taxNo != ''">and tax_no = #{taxNo}</if>
            <if test="unitAddress != null  and unitAddress != ''">and unit_address = #{unitAddress}</if>
            <if test="telephone != null  and telephone != ''">and telephone = #{telephone}</if>
            <if test="bank != null  and bank != ''">and bank = #{bank}</if>
            <if test="bankAccount != null  and bankAccount != ''">and bank_account = #{bankAccount}</if>
            <if test="createBy != null  and createBy != ''">and create_by = #{createBy}</if>
            <if test="approveState != null and approveState !=4">and approve_state = #{approveState}</if>
            <if test="approveState != null and approveState == 4">and (account_no is not null and account_no != 0)</if>
            <if test="approveState != null and approveState == 2">and (account_no is null or account_no = 0)</if>
            <if test="resourceType != null  and resourceType != ''">and resource_type = #{resourceType}</if>
            <if test="freightForwarderId != null  and freightForwarderId != ''">and freight_forwarder_id =
                #{freightForwarderId}
            </if>
            <if test="groupType != null  and groupType != ''">and group_type = #{groupType}</if>
            <if test="remainingLocateTimes != null  and remainingLocateTimes != ''">and remaining_locate_times =
                #{remainingLocateTimes}
            </if>
            <if test="confirmReceiptState != null">and confirm_receipt_state = #{confirmReceiptState}</if>
            <if test="driverReceiptState != null">and driver_receipt_state = #{driverReceiptState}</if>
            <if test="params != null and params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between
                #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
            <if test="params != null and params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between
                #{params.beginUpdateTime} and #{params.endUpdateTime}
            </if>
        </where>
        ORDER BY approve_state,create_time desc
    </select>


    <select id="selectCustomerInfoById" parameterType="Long" resultMap="CustomerInfoResult">
        <include refid="selectCustomerInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertCustomerInfo" parameterType="com.zly.project.consignor.customer.domain.CustomerInfo">
        insert IGNORE into customer_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerCode != null and customerCode != ''">customer_code,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="creditCode != null and creditCode != ''">credit_code,</if>
            <if test="legal != null and legal != ''">legal,</if>
            <if test="legalIdNo != null and legalIdNo != ''">legal_id_no,</if>
            <if test="contact != null and contact != ''">contact,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="accountNo != null and accountNo != ''">account_no,</if>
            <if test="bankCode != null and bankCode != ''">bank_code,</if>
            <if test="bankName != null and bankName != ''">bank_name,</if>
            <if test="state != null">state,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="contractState != null">contract_state,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="billHead != null and billHead != ''">bill_head,</if>
            <if test="taxNo != null and taxNo != ''">tax_no,</if>
            <if test="unitAddress != null and unitAddress != ''">unit_address,</if>
            <if test="telephone != null and telephone != ''">telephone,</if>
            <if test="bank != null and bank != ''">bank,</if>
            <if test="bankAccount != null and bankAccount != ''">bank_account,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="approveState != null">approve_state,</if>
            <if test="resourceType != null">resource_type,</if>
            <if test="freightForwarderId != null">freight_forwarder_id,</if>
            <if test="groupType != null">group_type,</if>
            <if test="remainingLocateTimes != null">remaining_locate_times,</if>
            <if test="province != null and province != ''">province,</if>
            <if test="provinceCode != null and provinceCode != ''">province_code,</if>
            <if test="city != null and city != ''">city,</if>
            <if test="cityCode != null and cityCode != ''">city_code,</if>
            <if test="area != null and area != ''">area,</if>
            <if test="areaCode != null and areaCode != ''">area_code,</if>
            <if test="confirmReceiptState != null">confirm_receipt_state,</if>
            <if test="driverReceiptState != null">driver_receipt_state,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerCode != null and customerCode != ''">#{customerCode},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="creditCode != null and creditCode != ''">#{creditCode},</if>
            <if test="legal != null and legal != ''">#{legal},</if>
            <if test="legalIdNo != null and legalIdNo != ''">#{legalIdNo},</if>
            <if test="contact != null and contact != ''">#{contact},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="accountNo != null and accountNo != ''">#{accountNo},</if>
            <if test="bankCode != null and bankCode != ''">#{bankCode},</if>
            <if test="bankName != null and bankName != ''">#{bankName},</if>
            <if test="state != null">#{state},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="contractState != null">#{contractState},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="billHead != null and billHead != ''">#{billHead},</if>
            <if test="taxNo != null and taxNo != ''">#{taxNo},</if>
            <if test="unitAddress != null and unitAddress != ''">#{unitAddress},</if>
            <if test="telephone != null and telephone != ''">#{telephone},</if>
            <if test="bank != null and bank != ''">#{bank},</if>
            <if test="bankAccount != null and bankAccount != ''">#{bankAccount},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="approveState != null">#{approveState},</if>
            <if test="resourceType != null">#{resourceType},</if>
            <if test="freightForwarderId != null">#{freightForwarderId},</if>
            <if test="groupType != null">#{groupType},</if>
            <if test="remainingLocateTimes != null">#{remainingLocateTimes},</if>
            <if test="province != null and province != ''">#{province},</if>
            <if test="provinceCode != null and provinceCode != ''">#{provinceCode},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="cityCode != null and cityCode != ''">#{cityCode},</if>
            <if test="area != null and area != ''">#{area},</if>
            <if test="areaCode != null and areaCode != ''">#{areaCode},</if>
            <if test="confirmReceiptState != null">#{confirmReceiptState},</if>
            <if test="driverReceiptState != null">#{driverReceiptState},</if>
        </trim>
    </insert>

    <update id="updateCustomerInfo" parameterType="com.zly.project.consignor.customer.domain.CustomerInfo">
        update customer_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerCode != null and customerCode != ''">customer_code = #{customerCode},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="creditCode != null and creditCode != ''">credit_code = #{creditCode},</if>
            <if test="legal != null and legal != ''">legal = #{legal},</if>
            <if test="legalIdNo != null and legalIdNo != ''">legal_id_no = #{legalIdNo},</if>
            <!--            <if test="contact != null and contact != ''">contact = #{contact},</if>-->
            <!--            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>-->
            <if test="accountNo != null and accountNo != ''">account_no = #{accountNo},</if>
            <if test="bankCode != null and bankCode != ''">bank_code = #{bankCode},</if>
            <if test="bankName != null and bankName != ''">bank_name = #{bankName},</if>
            <if test="state != null">state = #{state},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="contractState != null">contract_state = #{contractState},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="billHead != null and billHead != ''">bill_head = #{billHead},</if>
            <if test="taxNo != null and taxNo != ''">tax_no = #{taxNo},</if>
            <if test="unitAddress != null and unitAddress != ''">unit_address = #{unitAddress},</if>
            <if test="telephone != null and telephone != ''">telephone = #{telephone},</if>
            <if test="bank != null and bank != ''">bank = #{bank},</if>
            <if test="bankAccount != null and bankAccount != ''">bank_account = #{bankAccount},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="approveState != null">approve_state = #{approveState},</if>
            <if test="resourceType != null">resource_type = #{resourceType},</if>
            <if test="freightForwarderId != null">freight_forwarder_id = #{freightForwarderId},</if>
            <if test="groupType != null">group_type = #{groupType},</if>
            <if test="remainingLocateTimes != null">remaining_locate_times = #{remainingLocateTimes},</if>
            <if test="province != null and province != ''">province = #{province},</if>
            <if test="provinceCode != null and provinceCode != ''">province_code = #{provinceCode},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="cityCode != null and cityCode != ''">city_code = #{cityCode},</if>
            <if test="area != null and area != ''">area = #{area},</if>
            <if test="areaCode != null and areaCode != ''">area_code = #{areaCode},</if>
            <if test="confirmReceiptState != null">confirm_receipt_state = #{confirmReceiptState},</if>
            <if test="driverReceiptState != null">driver_receipt_state = #{driverReceiptState},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateCustomerInfoByProduct">
        update customer_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerCode != null and customerCode != ''">customer_code = #{customerCode},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="creditCode != null and creditCode != ''">credit_code = #{creditCode},</if>
            <if test="legal != null and legal != ''">legal = #{legal},</if>
            <if test="legalIdNo != null and legalIdNo != ''">legal_id_no = #{legalIdNo},</if>
            <if test="contact != null and contact != ''">contact = #{contact},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="accountNo != null and accountNo != ''">account_no = #{accountNo},</if>
            <if test="bankCode != null and bankCode != ''">bank_code = #{bankCode},</if>
            <if test="bankName != null and bankName != ''">bank_name = #{bankName},</if>
            <if test="state != null">state = #{state},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="contractState != null">contract_state = #{contractState},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="billHead != null and billHead != ''">bill_head = #{billHead},</if>
            <if test="taxNo != null and taxNo != ''">tax_no = #{taxNo},</if>
            <if test="unitAddress != null and unitAddress != ''">unit_address = #{unitAddress},</if>
            <if test="telephone != null and telephone != ''">telephone = #{telephone},</if>
            <if test="bank != null and bank != ''">bank = #{bank},</if>
            <if test="bankAccount != null and bankAccount != ''">bank_account = #{bankAccount},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="approveState != null">approve_state = #{approveState},</if>
            <if test="resourceType != null">resource_type = #{resourceType},</if>
            <if test="freightForwarderId != null">freight_forwarder_id = #{freightForwarderId},</if>
            <if test="groupType != null">group_type = #{groupType},</if>
            <if test="remainingLocateTimes != null">remaining_locate_times = #{remainingLocateTimes},</if>
            <if test="province != null and province != ''">province = #{province},</if>
            <if test="provinceCode != null and provinceCode != ''">province_code = #{provinceCode},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="cityCode != null and cityCode != ''">city_code = #{cityCode},</if>
            <if test="area != null and area != ''">area = #{area},</if>
            <if test="areaCode != null and areaCode != ''">area_code = #{areaCode},</if>
            <if test="confirmReceiptState != null">confirm_receipt_state = #{confirmReceiptState},</if>
            <if test="driverReceiptState != null">driver_receipt_state = #{driverReceiptState},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerInfoById" parameterType="Long">
        delete
        from customer_info
        where id = #{id}
    </delete>

    <delete id="deleteCustomerInfoByIds" parameterType="String">
        delete from customer_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCustomer" resultType="customerVo">
        select id, customer_name as name
        from customer_info
    </select>
    <select id="selectTransferCustomerInfoList" resultMap="CustomerInfoResult">
        <include refid="selectCustomerInfoVo"/>
        <where>
            <if test="id != null ">and id = #{id}</if>
            <if test="customerCode != null  and customerCode != ''">and customer_code = #{customerCode}</if>
            <if test="customerName != null  and customerName != ''">and customer_name = #{customerName}</if>
            <if test="creditCode != null  and creditCode != ''">and credit_code = #{creditCode}</if>
            <if test="legal != null  and legal != ''">and legal = #{legal}</if>
            <if test="legalIdNo != null  and legalIdNo != ''">and legal_id_no = #{legalIdNo}</if>
            <if test="contact != null  and contact != ''">and contact = #{contact}</if>
            <if test="contactPhone != null  and contactPhone != ''">and contact_phone = #{contactPhone}</if>
            <if test="accountNo != null  and accountNo != ''">and account_no = #{accountNo}</if>
            <if test="bankCode != null  and bankCode != ''">and bank_code = #{bankCode}</if>
            <if test="bankName != null  and bankName != ''">and bank_name like concat('%', #{bankName}, '%')</if>
            <if test="state != null ">and state = #{state}</if>
            <if test="address != null  and address != ''">and address = #{address}</if>
            <if test="contractState != null ">and contract_state = #{contractState}</if>
            <if test="billHead != null  and billHead != ''">and bill_head = #{billHead}</if>
            <if test="taxNo != null  and taxNo != ''">and tax_no = #{taxNo}</if>
            <if test="unitAddress != null  and unitAddress != ''">and unit_address = #{unitAddress}</if>
            <if test="telephone != null  and telephone != ''">and telephone = #{telephone}</if>
            <if test="bank != null  and bank != ''">and bank = #{bank}</if>
            <if test="bankAccount != null  and bankAccount != ''">and bank_account = #{bankAccount}</if>
            <if test="createBy != null  and createBy != ''">and create_by = #{createBy}</if>
            <if test="approveState != null">and approve_state = #{approveState}</if>
            <if test="resourceType != null  and resourceType != ''">and resource_type = #{resourceType}</if>
            <if test="freightForwarderId != null  and freightForwarderId != ''">and freight_forwarder_id =
                #{freightForwarderId}
            </if>
            <if test="groupType != null  and groupType != ''">and group_type = #{groupType}</if>
            <if test="remainingLocateTimes != null  and remainingLocateTimes != ''">and remaining_locate_times =
                #{remainingLocateTimes}
            </if>
            <if test="params != null and params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
            <if test="params != null and params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''">
                and update_time between #{params.beginUpdateTime} and #{params.endUpdateTime}
            </if>
        </where>
    </select>
    <select id="selectCustomerInfoByIds" resultType="com.zly.project.consignor.customer.domain.CustomerInfo">
        select id,customer_name customerName,credit_code creditCode from customer_info
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectCustomerInfoListByCreditCodeOrCustomerName"
            parameterType="com.zly.project.consignor.customer.domain.CustomerInfo" resultMap="CustomerInfoResult">
        <include refid="selectCustomerInfoVo"/>
        <where>
            <if test="customerName != null  and customerName != ''">and customer_name = #{customerName}</if>
            <if test="creditCode != null  and creditCode != ''">or credit_code = #{creditCode}</if>
        </where>
    </select>

</mapper>
