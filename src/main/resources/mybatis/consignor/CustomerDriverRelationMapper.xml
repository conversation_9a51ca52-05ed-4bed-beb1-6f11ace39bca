<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.consignor.customer.mapper.CustomerDriverRelationMapper">

    <resultMap type="com.zly.project.consignor.customer.domain.CustomerDriverRelation"
               id="CustomerDriverRelationResult">
        <result property="customerId" column="customer_id"/>
        <result property="driverId" column="driver_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="drivingLicense" column="driving_license"/>
        <result property="telephone" column="telephone"/>
    </resultMap>

    <sql id="selectCustomerDriverRelationVo">
        SELECT customer_id,
               driver_id,
               create_by,
               create_time,
               update_by,
               update_time,
               driving_license,
               telephone
        FROM customer_driver_relation
    </sql>

    <select id="selectCustomerDriverRelationList"
            parameterType="com.zly.project.consignor.customer.domain.CustomerDriverRelation"
            resultMap="CustomerDriverRelationResult">
        <include refid="selectCustomerDriverRelationVo"/>
        <where>
            <if test="customerId != null">and customer_id = #{customerId}</if>
            <if test="driverId != null">and driver_id = #{driverId}</if>
            <if test="drivingLicense != null and drivingLicense != ''">and driving_license = #{drivingLicense}</if>
            <if test="telephone != null  and telephone != ''">and telephone = #{telephone}</if>
        </where>
    </select>

    <select id="selectCustomerDriverRelationByCustomerId" parameterType="Long" resultMap="CustomerDriverRelationResult">
        <include refid="selectCustomerDriverRelationVo"/>
        where customer_id = #{customerId} and driver_id = #{driverId}
    </select>

    <select id="selectCustomerDriverRelationByDrivingLicense" resultMap="CustomerDriverRelationResult">
        SELECT a.customer_id, a.driver_id, a.create_by, a.create_time, a.update_by, a.update_time
        FROM customer_driver_relation a
                 inner join driver d on a.driver_id = d.id
        where a.customer_id = #{customerId}
          and d.driving_license = #{drivingLicense}
    </select>


    <select id="selectCustomerDriverRelationByDrivingLicenseWithoutCustomerId" resultMap="CustomerDriverRelationResult">
        SELECT a.customer_id,a.driver_id
        FROM customer_driver_relation a
        INNER JOIN driver d ON a.driver_id = d.id
        WHERE d.driving_license = #{drivingLicense}
    </select>


    <insert id="insertCustomerDriverRelation"
            parameterType="com.zly.project.consignor.customer.domain.CustomerDriverRelation">
        insert into customer_driver_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="driverId != null">driver_id,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="drivingLicense != null  and drivingLicense != ''">driving_license,</if>
            <if test="telephone != null  and telephone != ''">telephone,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="driverId != null">#{driverId},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="drivingLicense != null  and drivingLicense != ''">#{drivingLicense},</if>
            <if test="telephone != null  and telephone != ''">#{telephone},</if>
        </trim>
    </insert>
    <insert id="insertCustomerDriverRelations">
        insert IGNORE into customer_driver_relation
        (customer_id,
        driver_id,
        create_by,
        create_time,
        update_by,
        update_time,
        driving_license,
        telephone
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.customerId},
            #{item.driverId},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime},
            #{item.drivingLicense},
            #{item.telephone}
        </foreach>
    </insert>

    <update id="updateCustomerDriverRelation"
            parameterType="com.zly.project.consignor.customer.domain.CustomerDriverRelation">
        update customer_driver_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="driverId != null">driver_id = #{driverId},</if>
            <if test="drivingLicense != null  and drivingLicense != ''">driving_license = #{drivingLicense},</if>
            <if test="telephone != null  and telephone != ''">telephone = #{telephone},</if>
        </trim>
        where customer_id = #{customerId} and driver_id = #{driverId}
    </update>
    <update id="updateDriverByShipperIdAndOldId">
        update customer_driver_relation
        set driver_id = #{newId}
        where customer_id = #{customerId}
          and driver_id = #{oldId}
    </update>
    <update id="updateDriverByNewIdAndOldId">
        update customer_driver_relation
        set driver_id = #{newId}
        where driver_id = #{oldId}
    </update>

    <update id="updateDriverByNewIdAndOldDrivingLicense">
        update customer_driver_relation set driver_id = #{id}
        where driver_id in
        <foreach item="driverId" collection="driverIds" open="(" separator="," close=")">
            #{driverId}
        </foreach>
    </update>
    <update id="updateNewDriverIdByOldDriverId">
        update customer_driver_relation
        set driver_id = #{newDriverId}
        where driver_id = #{oldDriverId}
    </update>

    <delete id="deleteCustomerDriverRelationByCustomerId" parameterType="Long">
        DELETE
        FROM customer_driver_relation
        WHERE customer_id = #{customerId}
          AND driver_id = #{driverId}
    </delete>

    <delete id="deleteCustomerDriverRelationByCustomerIds" parameterType="String">
        delete from customer_driver_relation where customer_id in
        <foreach item="customerId" collection="array" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </delete>


    <select id="selectCustomerDriverRelationListByIds" resultMap="CustomerDriverRelationResult">
        <include refid="selectCustomerDriverRelationVo"/>
        where driver_id in
        <foreach item="driverId" collection="ids" open="(" separator="," close=")">
            #{driverId}
        </foreach>
        <if test="customerId != null">and customer_id = #{customerId}</if>
    </select>
    <select id="selectListLimit" resultMap="CustomerDriverRelationResult">
        <include refid="selectCustomerDriverRelationVo"/>
        limit #{offset}, #{pageSize}
    </select>
    <select id="countAll" resultType="java.lang.Integer">
        select count(*)
        from customer_driver_relation
    </select>
    <select id="checkCustomerDriverRelationTelephone" resultType="java.lang.Long">
        select customer_id from customer_driver_relation where telephone = #{telephone} and customer_id in
        <foreach item="customerId" collection="customerIds" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </select>
    <select id="selectByCustomerIdsAndDriverIds" resultMap="CustomerDriverRelationResult">
        <include refid="selectCustomerDriverRelationVo"/>
        where (customer_id,driver_id) in
        <foreach item="relation" collection="relations" open="(" separator="," close=")">
            (#{relation.customerId},#{relation.driverId})
        </foreach>
    </select>

    <delete id="deleteCustomerDriverRelationByDriverIds">
        delete from customer_driver_relation where driver_id in
        <foreach item="driverId" collection="driverIds" open="(" separator="," close=")">
            #{driverId}
        </foreach>
    </delete>

    <delete id="deleteRepeatDriverByDrivingLicenseAndId">
        DELETE
        from customer_driver_relation
        where customer_id in (select a.customer_id
                              from (SELECT customer_id
                                    from customer_driver_relation
                                    where driving_license = #{drivingLicense}
                                    GROUP BY customer_id
                                    HAVING count(*) > 1) a)
          and driver_id = #{driverId}
    </delete>
</mapper>
