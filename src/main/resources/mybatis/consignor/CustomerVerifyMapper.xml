<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.consignor.customer.mapper.CustomerVerifyMapper">

    <resultMap type="com.zly.project.consignor.customer.domain.CustomerVerify" id="CustomerVerifyResult">
        <result property="customerId"    column="customer_id"    />
        <result property="iphone"    column="iphone"    />
        <result property="openId"    column="open_id"    />
        <result property="deviceType"    column="device_type"    />
        <result property="wxCountry"    column="wx_country"    />
        <result property="wxProvince"    column="wx_province"    />
        <result property="wxCity"    column="wx_city"    />
        <result property="wxNickName"    column="wx_nick_name"    />
        <result property="wxSex"    column="wx_sex"    />
        <result property="wxHeadImg"    column="wx_head_img"    />
        <result property="state"    column="state"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectCustomerVerifyVo">
        select customer_id, iphone, open_id, device_type, wx_country, wx_province, wx_city, wx_nick_name, wx_sex,
                wx_head_img, state, create_time, create_by, update_time, update_by
        from customer_verify
    </sql>

    <select id="selectCustomerVerifyList" parameterType="com.zly.project.consignor.customer.domain.CustomerVerify" resultMap="CustomerVerifyResult">
        <include refid="selectCustomerVerifyVo"/>
        <where>
            <if test="iphone != null  and iphone != ''"> and iphone = #{iphone}</if>
            <if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
            <if test="deviceType != null "> and device_type = #{deviceType}</if>
            <if test="wxCountry != null  and wxCountry != ''"> and wx_country = #{wxCountry}</if>
            <if test="wxProvince != null  and wxProvince != ''"> and wx_province = #{wxProvince}</if>
            <if test="wxCity != null  and wxCity != ''"> and wx_city = #{wxCity}</if>
            <if test="wxNickName != null  and wxNickName != ''"> and wx_nick_name like concat('%', #{wxNickName}, '%')</if>
            <if test="wxSex != null "> and wx_sex = #{wxSex}</if>
            <if test="wxHeadImg != null  and wxHeadImg != ''"> and wx_head_img = #{wxHeadImg}</if>
            <if test="state != null "> and state = #{state}</if>
        </where>
    </select>

    <select id="selectCustomerVerifyByCustomerId" parameterType="Long" resultMap="CustomerVerifyResult">
        <include refid="selectCustomerVerifyVo"/>
        where customer_id = #{customerId}
    </select>

    <insert id="insertCustomerVerify" parameterType="com.zly.project.consignor.customer.domain.CustomerVerify">
        insert into customer_verify
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="iphone != null and iphone != ''">iphone,</if>
            <if test="openId != null and openId != ''">open_id,</if>
            <if test="deviceType != null">device_type,</if>
            <if test="wxCountry != null and wxCountry != ''">wx_country,</if>
            <if test="wxProvince != null and wxProvince != ''">wx_province,</if>
            <if test="wxCity != null and wxCity != ''">wx_city,</if>
            <if test="wxNickName != null and wxNickName != ''">wx_nick_name,</if>
            <if test="wxSex != null">wx_sex,</if>
            <if test="wxHeadImg != null and wxHeadImg != ''">wx_head_img,</if>
            <if test="state != null">state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="iphone != null and iphone != ''">#{iphone},</if>
            <if test="openId != null and openId != ''">#{openId},</if>
            <if test="deviceType != null">#{deviceType},</if>
            <if test="wxCountry != null and wxCountry != ''">#{wxCountry},</if>
            <if test="wxProvince != null and wxProvince != ''">#{wxProvince},</if>
            <if test="wxCity != null and wxCity != ''">#{wxCity},</if>
            <if test="wxNickName != null and wxNickName != ''">#{wxNickName},</if>
            <if test="wxSex != null">#{wxSex},</if>
            <if test="wxHeadImg != null and wxHeadImg != ''">#{wxHeadImg},</if>
            <if test="state != null">#{state},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateCustomerVerify" parameterType="com.zly.project.consignor.customer.domain.CustomerVerify">
        update customer_verify
        <trim prefix="SET" suffixOverrides=",">
            <if test="iphone != null and iphone != ''">iphone = #{iphone},</if>
            <if test="openId != null and openId != ''">open_id = #{openId},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="wxCountry != null and wxCountry != ''">wx_country = #{wxCountry},</if>
            <if test="wxProvince != null and wxProvince != ''">wx_province = #{wxProvince},</if>
            <if test="wxCity != null and wxCity != ''">wx_city = #{wxCity},</if>
            <if test="wxNickName != null and wxNickName != ''">wx_nick_name = #{wxNickName},</if>
            <if test="wxSex != null">wx_sex = #{wxSex},</if>
            <if test="wxHeadImg != null and wxHeadImg != ''">wx_head_img = #{wxHeadImg},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where customer_id = #{customerId}
    </update>

    <delete id="deleteCustomerVerifyByCustomerId" parameterType="Long">
        delete from customer_verify where customer_id = #{customerId}
    </delete>

    <delete id="deleteCustomerVerifyByCustomerIds" parameterType="String">
        delete from customer_verify where customer_id in
        <foreach item="customerId" collection="array" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </delete>
</mapper>