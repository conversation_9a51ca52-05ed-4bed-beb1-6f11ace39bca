<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.consignor.contract.mapper.CustomerContractMapper">

    <resultMap type="com.zly.project.consignor.contract.domain.CustomerContract" id="CustomerContractResult">
        <result property="id"    column="id"    />
        <result property="contractCode"    column="contract_code"    />
        <result property="contractName"    column="contract_name"    />
        <result property="responsiblePerson"    column="responsible_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="contractTime"    column="contract_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="remark"    column="remark"    />
        <result property="state"    column="state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="legalPerson"    column="legal_person"    />
        <result property="registerAddr"    column="register_addr"    />
        <result property="resource"    column="resource"/>
        <result property="contact"    column="contact"/>
    </resultMap>

    <sql id="selectCustomerContractVo">
        select id, contract_code, contract_name, responsible_person, contact_phone, customer_id, customer_name, contract_time, start_time, end_time, remark, state, create_by, create_time, update_by, update_time, tenant_id,legal_person, register_addr,resource,contact from customer_contract
    </sql>

    <select id="selectCustomerContractList" parameterType="com.zly.project.consignor.contract.domain.CustomerContract" resultMap="CustomerContractResult">
        <include refid="selectCustomerContractVo"/>
        <where>
            <if test="contractCode != null  and contractCode != ''"> and contract_code = #{contractCode}</if>
            <if test="contractName != null  and contractName != ''"> and contract_name like concat('%', #{contractName}, '%')</if>
            <if test="responsiblePerson != null and responsiblePerson != ''">and responsible_person = #{responsiblePerson}</if>
            <if test="contactPhone != null and contactPhone != ''">and contact_phone = #{contactPhone}</if>
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="contractTime != null "> and contract_time = #{contractTime}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time >= #{endTime}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
            <if test="legalPerson != null  and legalPerson != ''"> and legal_person = #{legalPerson}</if>
            <if test="registerAddr != null  and registerAddr != ''"> and register_addr = #{registerAddr}</if>
            <if test="resource"> and resource = #{resource}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectCustomerContractById" parameterType="Long" resultMap="CustomerContractResult">
        <include refid="selectCustomerContractVo"/>
        where id = #{id}
    </select>


    <select id="customerContractStateFromAWeekExpired" resultMap="CustomerContractResult">
        <include refid="selectCustomerContractVo"/>
        where end_time &gt; now() and end_time &lt;= now() + interval 1 week
    </select>
    <select id="selectCustomerContractBycustomerIds" resultMap="CustomerContractResult">
        <include refid="selectCustomerContractVo"/>
        where customer_id in
        <foreach item="customerId" collection="customerIds" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </select>

    <insert id="insertCustomerContract" parameterType="CustomerContract">
        insert IGNORE into customer_contract
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="contractCode != null and contractCode != ''">contract_code,</if>
            <if test="contractName != null and contractName != ''">contract_name,</if>
            <if test="responsiblePerson != null and responsiblePerson != ''">responsible_person,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="contractTime != null">contract_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="state != null">state,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="legalPerson != null and legalPerson != ''">legal_person,</if>
            <if test="registerAddr != null and registerAddr != ''">register_addr,</if>
            <if test="resource != null ">resource,</if>
            <if test="contact != null and contact != '' ">contact,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="contractCode != null and contractCode != ''">#{contractCode},</if>
            <if test="contractName != null and contractName != ''">#{contractName},</if>
            <if test="responsiblePerson != null and responsiblePerson != ''">#{responsiblePerson},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="contractTime != null">#{contractTime},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="state != null">#{state},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="legalPerson != null and legalPerson != ''">#{legalPerson},</if>
            <if test="registerAddr != null and registerAddr != ''">#{registerAddr},</if>
            <if test="resource != null ">#{resource},</if>
            <if test="contact != null and contact != '' ">#{contact},</if>
         </trim>
    </insert>

    <update id="updateCustomerContract" parameterType="com.zly.project.consignor.contract.domain.CustomerContract">
        update customer_contract
        <trim prefix="SET" suffixOverrides=",">
            <if test="contractCode != null and contractCode != ''">contract_code = #{contractCode},</if>
            <if test="contractName != null and contractName != ''">contract_name = #{contractName},</if>
            <if test="responsiblePerson != null and responsiblePerson != ''">responsible_person = #{responsiblePerson},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="contractTime != null">contract_time = #{contractTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="legalPerson != null and legalPerson != ''">legal_person = #{legalPerson},</if>
            <if test="registerAddr != null and registerAddr != ''">register_addr = #{registerAddr},</if>
            <if test="resource != null">register_addr = #{resource},</if>
            <if test="contact != null and contact != ''">contact = #{contact},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerContractById" parameterType="Long">
        delete from customer_contract where id = #{id}
    </delete>

    <delete id="deleteCustomerContractByIds" parameterType="String">
        delete from customer_contract where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
