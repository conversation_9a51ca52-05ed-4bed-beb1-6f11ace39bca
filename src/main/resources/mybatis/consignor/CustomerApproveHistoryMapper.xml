<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.consignor.customer.mapper.CustomerApproveHistoryMapper">

    <resultMap type="com.zly.project.consignor.customer.domain.CustomerApproveHistory" id="CustomerApproveHistoryResult">
        <result property="id"    column="id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="rejectReason"    column="reject_reason"    />
        <result property="rejectTime"    column="reject_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCustomerApproveHistoryVo">
        select id, customer_id, reject_reason, reject_time, create_time, create_by, update_time, update_by, remark from customer_approve_history
    </sql>

    <select id="selectCustomerApproveHistoryList" parameterType="com.zly.project.consignor.customer.domain.CustomerApproveHistory" resultMap="CustomerApproveHistoryResult">
        <include refid="selectCustomerApproveHistoryVo"/>
        <where>
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="rejectReason != null  and rejectReason != ''"> and reject_reason = #{rejectReason}</if>
            <if test="rejectTime != null "> and reject_time = #{rejectTime}</if>
        </where>
    </select>

    <select id="selectCustomerApproveHistoryById" parameterType="Long" resultMap="CustomerApproveHistoryResult">
        <include refid="selectCustomerApproveHistoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertCustomerApproveHistory" parameterType="com.zly.project.consignor.customer.domain.CustomerApproveHistory">
        insert into customer_approve_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="rejectReason != null and rejectReason != ''">reject_reason,</if>
            <if test="rejectTime != null">reject_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="rejectReason != null and rejectReason != ''">#{rejectReason},</if>
            <if test="rejectTime != null">#{rejectTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCustomerApproveHistory" parameterType="com.zly.project.consignor.customer.domain.CustomerApproveHistory">
        update customer_approve_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="rejectReason != null and rejectReason != ''">reject_reason = #{rejectReason},</if>
            <if test="rejectTime != null">reject_time = #{rejectTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerApproveHistoryById" parameterType="Long">
        delete from customer_approve_history where id = #{id}
    </delete>

    <delete id="deleteCustomerApproveHistoryByIds" parameterType="String">
        delete from customer_approve_history where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>