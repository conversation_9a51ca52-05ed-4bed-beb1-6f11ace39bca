<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.consignor.customer.mapper.CustomerAuxiliaryRelationMapper">

    <resultMap type="CustomerAuxiliaryRelation" id="CustomerAuxiliaryRelationResult">
        <result property="customerId" column="customer_id"/>
        <result property="auxiliaryStaffId" column="auxiliary_staff_id"    />
        <result property="auxiliaryStaffIdentityCard"    column="auxiliary_staff_identity_card"    />
        <result property="auxiliaryStaffContactPhone"    column="auxiliary_staff_contact_phone"    />
        <result property="isOrder"    column="is_order"    />
        <result property="lastOrderTime"    column="last_order_time"    />
        <result property="state"    column="state"    />
    </resultMap>

    <sql id="selectCustomerAuxiliaryRelationVo">
        select customer_id, auxiliary_staff_id, auxiliary_staff_identity_card, auxiliary_staff_contact_phone, is_order, last_order_time, state from customer_auxiliary_relation
    </sql>

    <select id="selectCustomerAuxiliaryRelationList" parameterType="CustomerAuxiliaryRelation" resultMap="CustomerAuxiliaryRelationResult">
        <include refid="selectCustomerAuxiliaryRelationVo"/>
        <where>
            <if test="customerId != null">and customer_id = #{customerId}</if>
            <if test="auxiliaryStaffId != null">and auxiliary_staff_id = #{auxiliaryStaffId}</if>
            <if test="auxiliaryStaffIdentityCard != null  and auxiliaryStaffIdentityCard != ''">and auxiliary_staff_identity_card = #{auxiliaryStaffIdentityCard}</if>
            <if test="auxiliaryStaffContactPhone != null  and auxiliaryStaffContactPhone != ''">and auxiliary_staff_contact_phone = #{auxiliaryStaffContactPhone}</if>
            <if test="isOrder != null ">and is_order = #{isOrder}</if>
            <if test="lastOrderTime != null ">and last_order_time = #{lastOrderTime}</if>
            <if test="state != null ">and state = #{state}</if>
        </where>
    </select>

    <select id="selectCustomerAuxiliaryRelationByCustomerId" parameterType="Long" resultMap="CustomerAuxiliaryRelationResult">
        <include refid="selectCustomerAuxiliaryRelationVo"/>
        where customer_id = #{customerId}
    </select>

    <insert id="insertCustomerAuxiliaryRelation" parameterType="CustomerAuxiliaryRelation">
        insert into customer_auxiliary_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="auxiliaryStaffId != null">auxiliary_staff_id,</if>
            <if test="auxiliaryStaffIdentityCard != null and auxiliaryStaffIdentityCard != ''">auxiliary_staff_identity_card,</if>
            <if test="auxiliaryStaffContactPhone != null and auxiliaryStaffContactPhone != ''">auxiliary_staff_contact_phone,</if>
            <if test="isOrder != null">is_order,</if>
            <if test="lastOrderTime != null">last_order_time,</if>
            <if test="state != null">state,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="auxiliaryStaffId != null">#{auxiliaryStaffId},</if>
            <if test="auxiliaryStaffIdentityCard != null and auxiliaryStaffIdentityCard != ''">#{auxiliaryStaffIdentityCard},</if>
            <if test="auxiliaryStaffContactPhone != null and auxiliaryStaffContactPhone != ''">#{auxiliaryStaffContactPhone},</if>
            <if test="isOrder != null">#{isOrder},</if>
            <if test="lastOrderTime != null">#{lastOrderTime},</if>
            <if test="state != null">#{state},</if>
         </trim>
    </insert>

    <update id="updateCustomerAuxiliaryRelation" parameterType="CustomerAuxiliaryRelation">
        update customer_auxiliary_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="auxiliaryStaffId != null">auxiliary_staff_id = #{auxiliaryStaffId},</if>
            <if test="auxiliaryStaffIdentityCard != null and auxiliaryStaffIdentityCard != ''">
                auxiliary_staff_identity_card = #{auxiliaryStaffIdentityCard},
            </if>
            <if test="auxiliaryStaffContactPhone != null and auxiliaryStaffContactPhone != ''">
                auxiliary_staff_contact_phone = #{auxiliaryStaffContactPhone},
            </if>
            <if test="isOrder != null">is_order = #{isOrder},</if>
            <if test="lastOrderTime != null">last_order_time = #{lastOrderTime},</if>
            <if test="state != null">state = #{state},</if>
        </trim>
        where customer_id = #{customerId} and auxiliary_staff_id = #{auxiliaryStaffId}
    </update>
    <update id="updateNewAuxiliaryIdByOldAuxiliaryId">
        update customer_auxiliary_relation
        set auxiliary_staff_id = #{newAuxiliaryId}
        where auxiliary_staff_id = #{oldAuxiliaryId}
    </update>

    <delete id="deleteCustomerAuxiliaryRelationByCustomerId" parameterType="Long">
        delete
        from customer_auxiliary_relation
        where customer_id = #{customerId}
    </delete>

    <delete id="deleteCustomerAuxiliaryRelationByCustomerIds" parameterType="String">
        delete from customer_auxiliary_relation where customer_id in
        <foreach item="customerId" collection="array" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </delete>

    <insert id="insertCustomerAuxiliaryRelationList">
        insert ignore into customer_auxiliary_relation(customer_id, auxiliary_staff_id, auxiliary_staff_identity_card, auxiliary_staff_contact_phone, is_order, state)
        values
        <foreach item="item" index="index" collection="list" open="" close="" separator=",">
            (#{item.customerId}, #{item.auxiliaryStaffId}, #{item.auxiliaryStaffIdentityCard}, #{item.auxiliaryStaffContactPhone}, #{item.isOrder}, #{item.state})
        </foreach>
    </insert>

    <select id="updateCustomerAuxiliaryRelationPhone">
        update customer_auxiliary_relation
        set auxiliary_staff_contact_phone = #{phone}
        where customer_id = #{customerId}
          and auxiliary_staff_identity_card = #{identityCard}
    </select>

    <select id="selectCustomerAuxiliaryRelationListByIdentityCard"
            resultMap="CustomerAuxiliaryRelationResult">
        <include refid="selectCustomerAuxiliaryRelationVo"/>
        where auxiliary_staff_identity_card in
        <foreach item="identityCard" collection="identityCards" open="(" separator="," close=")">
            #{identityCard}
        </foreach>
    </select>

    <delete id="deleteCustomerAuxiliaryRelationByAuxiliaryStaffIds">
        delete from customer_auxiliary_relation where auxiliary_staff_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteRepeatAuxiliaryByIdentityCardAndId">
        DELETE
        from customer_auxiliary_relation
        where customer_id in (select a.customer_id
                              from (SELECT customer_id
                                    from customer_auxiliary_relation
                                    where auxiliary_staff_identity_card = #{identityCard}
                                    GROUP BY customer_id
                                    HAVING count(*) > 1) a)
          and auxiliary_staff_id = #{auxiliaryId}
    </delete>
</mapper>
