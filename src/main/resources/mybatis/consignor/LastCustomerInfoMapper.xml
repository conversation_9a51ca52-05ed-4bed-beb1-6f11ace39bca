<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.consignor.customer.mapper.LastCustomerInfoMapper">
    <resultMap type="com.zly.project.consignor.customer.domain.LastCustomerInfo" id="LastCustomerInfoResult">
        <result property="id"    column="id"    />
        <result property="customerCode"    column="customer_code"    />
        <result property="customerName"    column="customer_name"    />
        <result property="creditCode"    column="credit_code"    />
        <result property="legal"    column="legal"    />
        <result property="legalIdNo"    column="legal_id_no"    />
        <result property="contact"    column="contact"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="state"    column="state"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="establishDate"    column="establish_date"    />
        <result property="registerAddress"    column="register_address"    />
        <result property="customerId"    column="customer_id"    />
        <result property="resource"    column="resource"    />
        <result property="billHead"    column="bill_head"    />
        <result property="taxNo"    column="tax_no"    />
        <result property="bank"    column="bank"    />
        <result property="bankAccount"    column="bank_account"    />
        <result property="telephone"    column="telephone"    />
    </resultMap>

    <sql id="selectLastCustomerInfoVo">
        select id, customer_code, customer_name, credit_code, legal, legal_id_no, contact, contact_phone, state, remark, create_by, create_time, update_by, update_time, establish_date, register_address, customer_id,resource,bill_head,tax_no,bank,bank_account,telephone from last_customer_info
    </sql>

    <select id="selectLastCustomerInfoList" parameterType="LastCustomerInfo" resultMap="LastCustomerInfoResult">
        <include refid="selectLastCustomerInfoVo"/>
        <where>
            <if test="customerCode != null  and customerCode != ''"> and customer_code = #{customerCode}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="creditCode != null  and creditCode != ''"> and credit_code = #{creditCode}</if>
            <if test="legal != null  and legal != ''"> and legal = #{legal}</if>
            <if test="legalIdNo != null  and legalIdNo != ''"> and legal_id_no = #{legalIdNo}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="establishDate != null  and establishDate != ''"> and establish_date = #{establishDate}</if>
            <if test="registerAddress != null  and registerAddress != ''"> and register_address = #{registerAddress}</if>
            <if test="customerId != null"> and customer_id = #{customerId}</if>
            <if test="billHead != null and billHead != ''"> and bill_head = #{billHead}</if>
            <if test="taxNo != null and taxNo != ''"> and tax_no = #{taxNo}</if>
            <if test="bank != null and bank != ''"> and bank = #{bank}</if>
            <if test="bankAccount != null and bankAccount != ''"> and bank_account = #{bankAccount}</if>
            <if test="telephone != null and telephone != ''"> and telephone = #{telephone}</if>
            and (resource = #{resource} or resource = 10)
        </where>
        order by create_time desc
    </select>

    <select id="selectLastCustomerInfoById" parameterType="Long" resultMap="LastCustomerInfoResult">
        <include refid="selectLastCustomerInfoVo"/>
        where id = #{id}
    </select>
    <select id="checkCustomerExistence" resultType="java.lang.Integer">
        select count(0)
        from last_customer_info
        <where>
            <if test="customerName != null  and customerName != ''">and customer_name = #{customerName}</if>
            <if test="creditCode != null  and creditCode != ''">and credit_code = #{creditCode}</if>
            <if test="customerId != null  and customerId != ''">and customer_id = #{customerId}</if>
        </where>
    </select>
    <select id="selectByCustomerIds" resultMap="LastCustomerInfoResult">
        <include refid="selectLastCustomerInfoVo"/>
        where customer_id in
        <foreach item="customerId" collection="customerIds" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </select>

    <insert id="insertLastCustomerInfo" parameterType="LastCustomerInfo">
        insert IGNORE into last_customer_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerCode != null and customerCode != ''">customer_code,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="creditCode != null and creditCode != ''">credit_code,</if>
            <if test="legal != null and legal != ''">legal,</if>
            <if test="legalIdNo != null and legalIdNo != ''">legal_id_no,</if>
            <if test="contact != null and contact != ''">contact,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="state != null">state,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="establishDate != null and establishDate != ''">establish_date,</if>
            <if test="registerAddress != null and registerAddress != ''">register_address,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="resource != null">resource,</if>
            <if test="billHead != null and billHead != ''">bill_head,</if>
            <if test="taxNo != null and taxNo != ''">tax_no,</if>
            <if test="bank != null and bank != ''">bank,</if>
            <if test="bankAccount != null and bankAccount != ''">bank_account,</if>
            <if test="telephone != null and telephone != ''">telephone,</if>
            </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerCode != null and customerCode != ''">#{customerCode},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="creditCode != null and creditCode != ''">#{creditCode},</if>
            <if test="legal != null and legal != ''">#{legal},</if>
            <if test="legalIdNo != null and legalIdNo != ''">#{legalIdNo},</if>
            <if test="contact != null and contact != ''">#{contact},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="state != null">#{state},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="establishDate != null and establishDate != ''">#{establishDate},</if>
            <if test="registerAddress != null and registerAddress != ''">#{registerAddress},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="resource != null">#{resource},</if>
            <if test="billHead != null and billHead != ''">#{billHead},</if>
            <if test="taxNo != null and taxNo != ''">#{taxNo},</if>
            <if test="bank != null and bank != ''">#{bank},</if>
            <if test="bankAccount != null and bankAccount != ''">#{bankAccount},</if>
            <if test="telephone != null and telephone != ''">#{telephone},</if>
            </trim>
    </insert>

    <update id="updateLastCustomerInfo" parameterType="LastCustomerInfo">
        update last_customer_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerCode != null and customerCode != ''">customer_code = #{customerCode},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="creditCode != null and creditCode != ''">credit_code = #{creditCode},</if>
            <if test="legal != null and legal != ''">legal = #{legal},</if>
            <if test="legalIdNo != null and legalIdNo != ''">legal_id_no = #{legalIdNo},</if>
            <if test="contact != null and contact != ''">contact = #{contact},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="state != null">state = #{state},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="establishDate != null and establishDate != ''">establish_date = #{establishDate},</if>
            <if test="registerAddress != null and registerAddress != ''">register_address = #{registerAddress},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="resource != null">customer_id = #{resource},</if>
            <if test="billHead != null and billHead != ''"> bill_head = #{billHead},</if>
            <if test="taxNo != null and taxNo != ''">tax_no = #{taxNo},</if>
            <if test="bank != null and bank != ''">bank = #{bank},</if>
            <if test="bankAccount != null and bankAccount != ''">bank_account = #{bankAccount},</if>
            <if test="telephone != null and telephone != ''">telephone = #{telephone},</if>
            </trim>
        where id = #{id}
    </update>

    <delete id="deleteLastCustomerInfoById" parameterType="Long">
        delete from last_customer_info where id = #{id}
    </delete>

    <delete id="deleteLastCustomerInfoByIds" parameterType="String">
        delete from last_customer_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
