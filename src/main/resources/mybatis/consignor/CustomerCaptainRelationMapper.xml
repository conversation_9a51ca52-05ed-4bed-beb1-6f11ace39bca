<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.consignor.customer.mapper.CustomerCaptainRelationMapper">

    <resultMap type="CustomerCaptainRelation" id="CustomerCaptainRelationResult">
        <result property="customerId"    column="customer_id"    />
        <result property="carCaptainId"    column="car_captain_id"    />
        <result property="actualIdentityCard"    column="actual_identity_card"    />
        <result property="carCaptainContactPhone"    column="car_captain_contact_phone"    />
        <result property="isOrder"    column="is_order"    />
        <result property="lastOrderTime"    column="last_order_time"    />
        <result property="state"    column="state"    />
    </resultMap>

    <sql id="selectCustomerCaptainRelationVo">
        select customer_id, car_captain_id, actual_identity_card, car_captain_contact_phone, is_order, last_order_time, state from customer_captain_relation
    </sql>

    <select id="selectCustomerCaptainRelationList" parameterType="CustomerCaptainRelation" resultMap="CustomerCaptainRelationResult">
        <include refid="selectCustomerCaptainRelationVo"/>
        <where>
            <if test="customerId != null">and customer_id = #{customerId}</if>
            <if test="carCaptainId != null">and car_captain_id = #{carCaptainId}</if>
            <if test="actualIdentityCard != null  and actualIdentityCard != ''">and actual_identity_card = #{actualIdentityCard}</if>
            <if test="carCaptainContactPhone != null  and carCaptainContactPhone != ''">and car_captain_contact_phone = #{carCaptainContactPhone}</if>
            <if test="isOrder != null ">and is_order = #{isOrder}</if>
            <if test="lastOrderTime != null ">and last_order_time = #{lastOrderTime}</if>
            <if test="state != null ">and state = #{state}</if>
        </where>
    </select>

    <select id="selectCustomerCaptainRelationByCustomerId" parameterType="Long" resultMap="CustomerCaptainRelationResult">
        <include refid="selectCustomerCaptainRelationVo"/>
        where customer_id = #{customerId}
    </select>

    <insert id="insertCustomerCaptainRelation" parameterType="CustomerCaptainRelation">
        insert into customer_captain_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="carCaptainId != null">car_captain_id,</if>
            <if test="actualIdentityCard != null and actualIdentityCard != ''">actual_identity_card,</if>
            <if test="carCaptainContactPhone != null and carCaptainContactPhone != ''">car_captain_contact_phone,</if>
            <if test="isOrder != null">is_order,</if>
            <if test="lastOrderTime != null">last_order_time,</if>
            <if test="state != null">state,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="carCaptainId != null">#{carCaptainId},</if>
            <if test="actualIdentityCard != null and actualIdentityCard != ''">#{actualIdentityCard},</if>
            <if test="carCaptainContactPhone != null and carCaptainContactPhone != ''">#{carCaptainContactPhone},</if>
            <if test="isOrder != null">#{isOrder},</if>
            <if test="lastOrderTime != null">#{lastOrderTime},</if>
            <if test="state != null">#{state},</if>
         </trim>
    </insert>

    <update id="updateCustomerCaptainRelation" parameterType="CustomerCaptainRelation">
        update customer_captain_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="carCaptainId != null">car_captain_id = #{carCaptainId},</if>
            <if test="actualIdentityCard != null and actualIdentityCard != ''">actual_identity_card = #{actualIdentityCard},</if>
            <if test="carCaptainContactPhone != null and carCaptainContactPhone != ''">car_captain_contact_phone = #{carCaptainContactPhone},</if>
            <if test="isOrder != null">is_order = #{isOrder},</if>
            <if test="lastOrderTime != null">last_order_time = #{lastOrderTime},</if>
            <if test="state != null">state = #{state},</if>
        </trim>
        where customer_id = #{customerId} and car_captain_id = #{carCaptainId}
    </update>

    <delete id="deleteCustomerCaptainRelationByCustomerId" parameterType="Long">
        delete from customer_captain_relation where customer_id = #{customerId}
    </delete>

    <delete id="deleteCustomerCaptainRelationByCustomerIds" parameterType="String">
        delete from customer_captain_relation where customer_id in
        <foreach item="customerId" collection="array" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </delete>

    <insert id="insertCustomerCaptainRelationList">
        insert ignore into customer_captain_relation(customer_id, car_captain_id, actual_identity_card, car_captain_contact_phone, is_order, state)
        values
        <foreach item="item" index="index" collection="list" open="" close="" separator=",">
            (#{item.customerId}, #{item.carCaptainId}, #{item.actualIdentityCard}, #{item.carCaptainContactPhone}, #{item.isOrder}, #{item.state})
        </foreach>
    </insert>

    <update id="updateCustomerCaptainRelationPhone">
        update customer_captain_relation
        set car_captain_contact_phone = #{phone}
        where customer_id = #{customerId}
          and actual_identity_card = #{identityCard}
    </update>

    <select id="selectCustomerCaptainRelationListByIdentityCards"
            resultMap="CustomerCaptainRelationResult">
        <include refid="selectCustomerCaptainRelationVo"/>
        where actual_identity_card in
        <foreach item="identityCard" collection="identityCards" open="(" separator="," close=")">
            #{identityCard}
        </foreach>
    </select>

    <delete id="deleteCustomerCaptainRelationByCaptainIds">
        delete from customer_captain_relation where car_captain_id in
        <foreach item="captainId" collection="captainIds" open="(" separator="," close=")">
            #{captainId}
        </foreach>
    </delete>
</mapper>
