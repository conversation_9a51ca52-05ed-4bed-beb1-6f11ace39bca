<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.financeOperation.mapper.FinanceUserMapper">

    <resultMap type="com.zly.project.financeOperation.domain.FinanceUser" id="FinanceUserResult">
        <id property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="password" column="password"/>
        <result property="pwd" column="pwd"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="freightForwarderId" column="freight_forwarder_id"/>
        <result property="customerId" column="customer_id"/>
        <result property="defaultProject" column="default_project"/>
    </resultMap>

    <resultMap id="RoleResult" type="com.zly.project.financeOperation.domain.FinanceRole">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="status" column="role_status"/>
    </resultMap>

    <sql id="selectUserVo">
        SELECT u.user_id,
               u.user_name,
               u.nick_name,
               u.email,
               u.last_email,
               u.avatar,
               u.phonenumber,
               u.password,
               u.pwd,
               u.sex,
               u.status,
               u.del_flag,
               u.login_ip,
               u.login_date,
               u.create_by,
               u.create_time,
               u.remark,
               u.freight_forwarder_id,
               u.customer_id,
               u.default_project
        FROM finance_user u
    </sql>

    <select id="selectUserList" parameterType="com.zly.project.financeOperation.domain.FinanceUser" resultMap="FinanceUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0'
        <if test="freightForwarderId != null and freightForwarderId != 0">
            AND u.freight_forwarder_id = #{freightForwarderId}
        </if>
        <if test="customerId != null and customerId != 0">
            AND u.customer_id = #{customerId}
        </if>
        <if test="userId != null and userId != 0">
            AND u.user_id = #{userId}
        </if>
        <if test="nickName != null and nickName != ''">
            AND u.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND u.status = #{status}
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <if test="createTimeStart != null and createTimeStart != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{createTimeStart},'%y%m%d')
        </if>
        <if test="createTimeEnd != null and createTimeEnd != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{createTimeEnd},'%y%m%d')
        </if>
    </select>

    <select id="selectUserByUserName" parameterType="String" resultMap="FinanceUserResult">
        <include refid="selectUserVo"/>
        where u.user_name = #{userName} and u.del_flag = '0' LIMIT 1
    </select>

    <select id="selectUserById" parameterType="Long" resultMap="FinanceUserResult">
        <include refid="selectUserVo"/>
        where u.user_id = #{userId}
    </select>

    <select id="checkUserNameUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM finance_user
        WHERE user_name = #{userName}
          AND del_flag = '0'
        LIMIT 1
    </select>

    <select id="checkPhoneUnique" parameterType="String" resultMap="FinanceUserResult">
        SELECT user_id, phonenumber
        FROM finance_user
        WHERE phonenumber = #{phonenumber}
          AND del_flag = '0'
        LIMIT 1
    </select>

    <select id="checkEmailUnique" parameterType="String" resultMap="FinanceUserResult">
        SELECT user_id, email
        FROM finance_user
        WHERE email = #{email}
          AND del_flag = '0'
        LIMIT 1
    </select>
    <select id="selectUserListByCustomerId" resultMap="FinanceUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and u.customer_id = #{customerId}
    </select>

    <select id="selectAdminUsersByCustomerId" resultMap="FinanceUserResult">
        <include refid="selectUserVo"/>
        left join finance_user_role ur on u.user_id = ur.user_id
        left join finance_role r on r.role_id = ur.role_id
        where u.del_flag = '0'
        <if test="customerId != null and customerId != 0">
            and u.customer_id = #{customerId}
        </if>
        and r.role_key = "admin"
        order by u.create_time desc
    </select>

    <insert id="insertUser" parameterType="com.zly.project.financeOperation.domain.FinanceUser" useGeneratedKeys="true" keyProperty="userId">
        insert into finance_user (
        <if test="userId != null and userId != 0">user_id,</if>
        <if test="userName != null and userName != ''">user_name,</if>
        <if test="nickName != null and nickName != ''">nick_name,</if>
        <if test="email != null and email != ''">email,</if>
        <if test="avatar != null and avatar != ''">avatar,</if>
        <if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
        <if test="sex != null and sex != ''">sex,</if>
        <if test="password != null and password != ''">password,</if>
        <if test="pwd != null and pwd != ''">pwd,</if>
        <if test="status != null and status != ''">status,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="freightForwarderId != null and freightForwarderId != ''">freight_forwarder_id,</if>
        <if test="customerId != null and customerId != ''">customer_id,</if>
        <if test="defaultProject != null ">default_project,</if>
        create_time
        ) values (
        <if test="userId != null and userId != ''">#{userId},</if>
        <if test="userName != null and userName != ''">#{userName},</if>
        <if test="nickName != null and nickName != ''">#{nickName},</if>
        <if test="email != null and email != ''">#{email},</if>
        <if test="avatar != null and avatar != ''">#{avatar},</if>
        <if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
        <if test="sex != null and sex != ''">#{sex},</if>
        <if test="password != null and password != ''">#{password},</if>
        <if test="pwd != null and pwd != ''">#{pwd},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="freightForwarderId != null and freightForwarderId != ''">#{freightForwarderId},</if>
        <if test="customerId != null and customerId != ''">#{customerId},</if>
        <if test="defaultProject != null ">#{defaultProject},</if>
        sysdate()
        )
    </insert>

    <update id="updateUser" parameterType="com.zly.project.financeOperation.domain.FinanceUser">
        update finance_user
        <set>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="email != null ">email = #{email},</if>
            <if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
            <if test="sex != null and sex != ''">sex = #{sex},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="pwd != null and pwd != ''">pwd = #{pwd},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="freightForwarderId != null">freight_forwarder_id = #{freightForwarderId},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="defaultProject != null">default_project = #{defaultProject},</if>
            update_time = sysdate()
        </set>
        where user_id = #{userId}
    </update>

    <update id="updateUserStatus" parameterType="com.zly.project.financeOperation.domain.FinanceUser">
        UPDATE finance_user
        SET status = #{status}
        WHERE user_id = #{userId}
    </update>

    <update id="updateUserAvatar" parameterType="com.zly.project.financeOperation.domain.FinanceUser">
        UPDATE finance_user
        SET avatar = #{avatar}
        WHERE user_name = #{userName}
    </update>
    <update id="resetUserPwd">
        update finance_user set password = #{password},pwd = #{pwd} where user_name = #{userName}
    </update>
    <update id="updateUserDelFlag">
        UPDATE finance_user
        SET del_flag = 2
        WHERE user_id IN (SELECT user_id from finance_user_role where role_id=(SELECT role_id FROM finance_role WHERE role_key = 'admin' AND customer_id =  #{customerId})) and user_id != #{userId}
    </update>

    <delete id="deleteUserById" parameterType="Long">
        UPDATE finance_user
        SET del_flag = '2'
        WHERE user_id = #{userId}
    </delete>

    <delete id="deleteUserByIds" parameterType="Long">
        update finance_user set del_flag = '2' where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <select id="queryDelFlagUser"  resultType="java.lang.Long">
        SELECT
            user_id
        FROM
            finance_user
        WHERE
            user_id IN (
            SELECT
            user_id
            FROM
            finance_user_role
            WHERE
            role_id = (
            SELECT
            role_id
            FROM
            finance_role
            WHERE
            role_key = 'admin'
          AND customer_id = #{customerId})) and user_id != #{userId}
    </select>

    <select id="queryUser" resultType="clickLoginRes">
        SELECT
        fu.user_id AS userId,
        fu.user_name AS userName,
        fu.nick_name AS nickName,
        fu.phonenumber AS phone,
        fu.status AS state,
        fu.login_date AS date,
        fr.role_id AS roleId,
        fr.role_name AS roleName
        FROM
        finance_user fu
        JOIN
        finance_user_role fur ON fu.user_id = fur.user_id
        JOIN
        finance_role fr ON fur.role_id = fr.role_id
        WHERE
        fu.status = '0' AND
        fu.del_flag = '0'
        <if test="customerId != null">AND fu.customer_id = #{customerId}</if>
        <if test="userName != null and userName != ''">AND fu.user_name LIKE '%${userName}%'</if>
        <if test="nickName != null and nickName != ''">AND fu.nick_name LIKE '%${nickName}%'</if>
        ORDER BY
        CASE
        WHEN fr.role_name = '管理员' THEN 0
        ELSE 1
        END, fr.role_name

    </select>
    <select id="getEnterpriseInfo" resultType="com.zly.project.tenant.domain.res.EnterpriseInfoRes">
        select ci.id customerId, ci.customer_name customerName
        from finance_user fu
                 left join customer_info ci on fu.customer_id = ci.id
        where fu.phonenumber = #{phone}
          and fu.status = 0
          and ci.state = 0
          and fu.del_flag = 0
          and ci.id not in (select customer_id from freight_forwarder_info where credit_code not in ('91420981MA7MPL9C92'))
        order by login_date desc
    </select>
    <select id="selectFinanceUserList" resultType="com.zly.project.financeOperation.domain.FinanceUser">
        select user_id userId, user_name userName, customer_id customerId
        from finance_user
    </select>
    <update id="updateUserLastEmail">
        update finance_user
        set last_email = #{email}
        where user_id = #{userId}
    </update>

    <select id="getLastEmail" resultType="string">
        select last_email
        from finance_user
        where user_id = #{userId}
    </select>
</mapper>
