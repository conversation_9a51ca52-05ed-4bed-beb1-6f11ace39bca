<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.financeOperation.mapper.FinanceRoleMenuMapper">

    <resultMap type="com.zly.project.financeOperation.domain.FinanceRoleMenu" id="FinanceRoleMenuResult">
        <result property="roleId" column="role_id"/>
        <result property="menuId" column="menu_id"/>
    </resultMap>

    <select id="checkMenuExistRole" resultType="Integer">
        SELECT COUNT(1)
        FROM finance_role_menu
        WHERE menu_id = #{menuId}
    </select>

    <delete id="deleteRoleMenuByRoleId" parameterType="Long">
        DELETE
        FROM finance_role_menu
        WHERE role_id = #{roleId}
    </delete>

    <delete id="deleteRoleMenu" parameterType="Long">
        delete from finance_role_menu where role_id in
        <foreach collection="array" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>
    <delete id="deleteRoleMenuByMenuIds">
        delete from finance_role_menu where menu_id in
        <foreach collection="menuIds" item="menuId" open="(" separator="," close=")">
            #{menuId}
        </foreach>
    </delete>

    <insert id="batchRoleMenu">
        INSERT INTO finance_role_menu(role_id, menu_id) VALUES
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.roleId},#{item.menuId})
        </foreach>
    </insert>

    <select id="selectRoleMenuIds" resultType="java.lang.Long">
        SELECT menu_id
        FROM finance_role_menu
        WHERE role_id = #{roleId}
    </select>

    <select id="selectRoleMenuList" resultMap="com.zly.project.financeOperation.mapper.FinanceMenuMapper.FinanceMenuResult">
        SELECT m.*
        FROM finance_role_menu rm
        inner JOIN finance_model_menu fmm on fmm.id = rm.menu_id
        LEFT JOIN finance_menu m ON m.menu_id = fmm.menu_id
        WHERE role_id = #{roleId}
    </select>
    <select id="selectRoleMenuByMenuId" resultMap="FinanceRoleMenuResult">
        select * from  finance_role_menu where menu_id = #{menuId}
    </select>

    <select id="selectRoleParentMenuListAdmin" resultType="com.zly.project.financeOperation.domain.FinanceMenu">
        SELECT fmm.id as menuId
        FROM finance_role_menu rm
                 LEFT JOIN finance_model_menu fmm ON rm.menu_id = fmm.id
                 LEFT JOIN finance_menu m ON m.menu_id = fmm.menu_id
        WHERE m.parent_id = 0 and rm.role_id = #{roleId}
    </select>
</mapper>
