<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.financeOperation.mapper.FinanceUserRoleMapper">

    <resultMap type="com.zly.project.financeOperation.domain.FinanceUserRole" id="FinanceUserRoleResult">
        <result property="userId" column="user_id"/>
        <result property="roleId" column="role_id"/>
    </resultMap>

    <select id="selectUserRoleIds" resultType="java.lang.Long">
        SELECT role_id
        FROM finance_user_role
        WHERE user_id = #{userId}
    </select>

    <select id="selectRoleRelatedUserIds" resultType="java.lang.Long">
        SELECT user_id
        FROM finance_user_role
        WHERE role_id = #{roleId}
    </select>

    <delete id="deleteUserRoleByUserId" parameterType="Long">
        DELETE
        FROM finance_user_role
        WHERE user_id = #{userId}
    </delete>
    <select id="selectRoleIdsByUserId" resultType="java.lang.Long">
		select role_id
		from finance_user_role
		where user_id = #{userId}
	</select>


    <delete id="deleteUserRoleByRoleId" parameterType="Long">
        DELETE
        FROM finance_user_role
        WHERE role_id = #{roleId}
    </delete>

    <select id="countUserRoleByRoleId" resultType="Integer">
        SELECT COUNT(1)
        FROM finance_user_role
        WHERE role_id = #{roleId}
    </select>
    <select id="selectAdminByUserIds" resultType="java.lang.Long">
        select  fur.user_id from finance_user_role fur
        left join finance_role fr on fur.role_id = fr.role_id
        <where>
            <if test="userIds != null and userIds.size() >0">
                and fur.user_id in
                <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            and fr.role_name = "管理员"
        </where>
    </select>

    <delete id="deleteUserRoleByUserIds" parameterType="Long">
        delete from finance_user_role where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <insert id="batchUserRole">
        insert into finance_user_role(user_id, role_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.userId},#{item.roleId})
        </foreach>
    </insert>

    <delete id="deleteUserRoleInfo" parameterType="com.zly.project.financeOperation.domain.FinanceUserRole">
        DELETE
        FROM finance_user_role
        WHERE user_id = #{userId}
          AND role_id = #{roleId}
    </delete>

    <delete id="deleteUserRoleInfos">
        delete from finance_user_role where role_id=#{roleId} and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

</mapper>
