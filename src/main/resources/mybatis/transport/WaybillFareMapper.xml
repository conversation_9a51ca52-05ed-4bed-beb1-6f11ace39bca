<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.transport.waybill.mapper.WaybillFareMapper">

    <resultMap type="WaybillFare" id="WaybillFareResult">
        <result property="id"    column="id"    />
        <result property="waybillId"    column="waybill_id"    />
        <result property="shippingNoteNumber"    column="shipping_note_number"    />
        <result property="fareType"    column="fare_type"    />
        <result property="fareStage"    column="fare_stage"    />
        <result property="fareMoney"    column="fare_money"    />
        <result property="fareRemark"    column="fare_remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWaybillFareVo">
        select id, waybill_id, shipping_note_number, fare_type, fare_stage, fare_money, fare_remark, create_by, create_time, update_by, update_time,state from waybill_fare
    </sql>

    <select id="selectWaybillFareList" parameterType="WaybillFare" resultMap="WaybillFareResult">
        <include refid="selectWaybillFareVo"/>
        where state = 0
            <if test="waybillId != null "> and waybill_id = #{waybillId}</if>
            <if test="shippingNoteNumber != null  and shippingNoteNumber != ''"> and shipping_note_number = #{shippingNoteNumber}</if>
        <if test="fareType != null ">and fare_type = #{fareType}</if>
        <if test="fareStage != null ">and fare_stage = #{fareStage}</if>
        <if test="fareMoney != null ">and fare_money = #{fareMoney}</if>
        <if test="fareRemark != null  and fareRemark != ''">and fare_remark = #{fareRemark}</if>
    </select>

    <select id="selectWaybillFareById" parameterType="Long" resultMap="WaybillFareResult">
        <include refid="selectWaybillFareVo"/>
        where id = #{id} and state = 0
    </select>
    <select id="selectWaybillFareByWaybillIds" resultMap="WaybillFareResult">
        <include refid="selectWaybillFareVo"/>
        where state = 0 and waybill_id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </select>

    <insert id="insertWaybillFare" parameterType="WaybillFare">
        insert into waybill_fare
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="waybillId != null">waybill_id,</if>
            <if test="shippingNoteNumber != null and shippingNoteNumber != ''">shipping_note_number,</if>
            <if test="fareType != null">fare_type,</if>
            <if test="fareStage != null">fare_stage,</if>
            <if test="fareMoney != null">fare_money,</if>
            <if test="fareRemark != null and fareRemark != ''">fare_remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="waybillId != null">#{waybillId},</if>
            <if test="shippingNoteNumber != null and shippingNoteNumber != ''">#{shippingNoteNumber},</if>
            <if test="fareType != null">#{fareType},</if>
            <if test="fareStage != null">#{fareStage},</if>
            <if test="fareMoney != null">#{fareMoney},</if>
            <if test="fareRemark != null and fareRemark != ''">#{fareRemark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>
    <insert id="insertWaybillFares">
        insert IGNORE into waybill_fare
        (id,
        waybill_id,
        shipping_note_number,
        fare_type,
        fare_stage,
        fare_money,
        fare_remark,
        create_by,
        create_time,
        update_by,
        update_time
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.id},
            #{item.waybillId},
            #{item.shippingNoteNumber},
            #{item.fareType},
            #{item.fareStage},
            #{item.fareMoney},
            #{item.fareRemark},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime}
        </foreach>

    </insert>

    <update id="updateWaybillFare" parameterType="WaybillFare">
        update waybill_fare
        <trim prefix="SET" suffixOverrides=",">
            <if test="waybillId != null">waybill_id = #{waybillId},</if>
            <if test="shippingNoteNumber != null and shippingNoteNumber != ''">shipping_note_number =
                #{shippingNoteNumber},
            </if>
            <if test="fareType != null">fare_type = #{fareType},</if>
            <if test="fareStage != null">fare_stage = #{fareStage},</if>
            <if test="fareMoney != null">fare_money = #{fareMoney},</if>
            <if test="fareRemark != null and fareRemark != ''">fare_remark = #{fareRemark},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="state != null">state = #{state},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteWaybillFareById" parameterType="Long">
        update waybill_fare set state = 1 where id = #{id}
    </update>

    <delete id="deleteWaybillFareByIds" parameterType="String">
        delete from waybill_fare where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteWaybillFareByWaybillIds">
        delete from waybill_fare where waybill_id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </delete>
</mapper>
