<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.transport.waybill.mapper.WaybillUploadTransportationMapper">

    <resultMap type="WaybillUploadTransportation" id="WaybillUploadTransportationResult">
        <result property="id" column="id"/>
        <result property="waybillId" column="waybill_id"/>
        <result property="auditState" column="audit_state"/>
        <result property="auditRemark" column="audit_remark"/>
        <result property="auditTime" column="audit_time"/>
        <result property="createTime" column="create_time"/>
        <result property="fileIds" column="file_ids"/>
        <result property="protocolCode" column="protocol_code"/>
        <result property="state" column="state"/>
    </resultMap>

    <sql id="selectWaybillUploadTransportationVo">
        select id,
               waybill_id,
               audit_state,
               audit_remark,
               audit_time,
               create_time,
               file_ids,
               protocol_code,
               state
        from waybill_upload_transportation
    </sql>

    <select id="selectWaybillUploadTransportationList" parameterType="WaybillUploadTransportation" resultMap="WaybillUploadTransportationResult">
        <include refid="selectWaybillUploadTransportationVo"/>
        <where>
            <if test="waybillId != null ">and waybill_id = #{waybillId}</if>
            <if test="auditState != null ">and audit_state = #{auditState}</if>
            <if test="auditRemark != null  and auditRemark != ''">and audit_remark = #{auditRemark}</if>
            <if test="auditTime != null ">and audit_time = #{auditTime}</if>
            <if test="state != null ">and state = #{state}</if>
            <if test="fileIds != null  and fileIds != ''">and file_ids = #{fileIds}</if>
            <if test="protocolCode != null  and protocolCode != ''">and protocol_code = #{protocolCode}</if>
        </where>
    </select>

    <select id="selectWaybillUploadTransportationById" parameterType="Long" resultMap="WaybillUploadTransportationResult">
        <include refid="selectWaybillUploadTransportationVo"/>
        where id = #{id}
    </select>

    <insert id="insertWaybillUploadTransportation" parameterType="WaybillUploadTransportation">
        insert into waybill_upload_transportation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="waybillId != null">waybill_id,</if>
            <if test="auditState != null">audit_state,</if>
            <if test="auditRemark != null and auditRemark != ''">audit_remark,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="state != null">state,</if>
            <if test="fileIds != null and fileIds != ''">file_ids,</if>
            <if test="protocolCode != null and protocolCode != ''">protocol_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="waybillId != null">#{waybillId},</if>
            <if test="auditState != null">#{auditState},</if>
            <if test="auditRemark != null and auditRemark != ''">#{auditRemark},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="state != null">#{state},</if>
            <if test="fileIds != null and fileIds != ''">#{fileIds},</if>
            <if test="protocolCode != null and protocolCode != ''">#{protocolCode},</if>
        </trim>
    </insert>

    <update id="updateWaybillUploadTransportation" parameterType="WaybillUploadTransportation">
        update waybill_upload_transportation
        <trim prefix="SET" suffixOverrides=",">
            <if test="waybillId != null">waybill_id = #{waybillId},</if>
            <if test="auditState != null">audit_state = #{auditState},</if>
            <if test="auditRemark != null and auditRemark != ''">audit_remark = #{auditRemark},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="state != null">state = #{state},</if>
            <if test="fileIds != null and fileIds != ''">file_ids = #{fileIds},</if>
            <if test="protocolCode != null and protocolCode != ''">protocol_code = #{protocolCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWaybillUploadTransportationById" parameterType="Long">
        delete
        from waybill_upload_transportation
        where id = #{id}
    </delete>

    <delete id="deleteWaybillUploadTransportationByIds" parameterType="String">
        delete from waybill_upload_transportation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="transportationProtocolList" resultType="TransportionProtocolRes">
        SELECT
        a.id AS id,
        b.create_time AS uploadTime,
        b.shipping_note_number AS shippingNoteNumber,
        b.customer_name AS customerName,
        b.framework_contract_name AS contractName,
        c. NAME AS freightForwarderName,
        c.credit_code AS freightForwarderCode,
        b.driver_name AS driverName,
        b.telephone AS driverPhone,
        b.driving_license AS driverLicense,
        b.vehicle_number AS vehicleNumber,
        v.vehicle_type_name AS vehicleType,
        b.description_of_goods AS goodsName,
        b.fee_amount AS feeAmount,
        b.fee_unit AS feeUnit,
        b.despatch_actual_date_time AS despatchActualDateTime,
        b.goods_receipt_date_time AS goodsReceiptDateTime,
        b.pay_fare AS payFare,
        b.payee_name AS payeeName,
        e.cargo_owner_name AS senderName,
        CONCAT(
        e.province,
        e.city,
        e.area,
        e.place_of_loading
        ) AS senderAddress,
        d.consignee AS receiverName,
        CONCAT(
        d.province,
        d.city,
        d.area,
        d.goods_receipt_place
        ) AS receiverAddress
        FROM
        waybill_upload_transportation a
        INNER JOIN waybill b ON a.waybill_id = b.id
        INNER JOIN freight_forwarder_info c ON b.freight_forwarder_id = c.id
        INNER JOIN vehicle v ON b.vehicle_id = v.id
        INNER JOIN consignee_info d ON b.consignee_id = d.id
        INNER JOIN consignor_info e ON b.consignor_id = e.id
        INNER JOIN freight_forwarder_config f ON f.freight_forwarder_id = b.freight_forwarder_id
        inner join freight_forwarder_config g on b.freight_forwarder_id = g.freight_forwarder_id
        WHERE
        a.audit_state = 0 and a.state = 0 and g.is_audit_file = 0
        and b.freight_forwarder_id = #{freightForwarderId}
        <if test="customerId != null ">and b.customer_id = #{customerId}</if>
        <if test="contractId != null ">and b.framework_contract_id = #{contractId}</if>
        <if test="shippingNoteNumber != null and shippingNoteNumber != '' ">and b.shipping_note_number =
            #{shippingNoteNumber}
        </if>
        order by a.create_time
    </select>
    <select id="selectJoinAttachmentsByWaybillIds" resultMap="WaybillUploadTransportationResult">
        SELECT
        attachment.relation_id waybill_id,
        IFNULL( upload.audit_state, 2 ) audit_state,
        upload.audit_remark,
        upload.audit_time
        FROM
        waybill_attachment_info attachment
        LEFT JOIN waybill_upload_transportation upload ON attachment.relation_id = upload.waybill_id and upload.state =
        0
        WHERE
        attachment.state = 0
        AND attachment.relation_id IN
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
        <if test="fileNames != null and fileNames.size() > 0">
            and file_name in
            <foreach item="fileName" collection="fileNames" open="(" separator="," close=")">
                #{fileName}
            </foreach>
        </if>
        GROUP BY attachment.relation_id;
    </select>

    <select id="transportationProtocolCount" resultType="integer">
        SELECT count(distinct a.id)
        FROM waybill_upload_transportation a
                 INNER JOIN waybill b ON a.waybill_id = b.id and b.freight_forwarder_id = #{freightForwarderId}
                 inner join freight_forwarder_config g on b.freight_forwarder_id = g.freight_forwarder_id
        WHERE a.audit_state = 0
          and g.is_audit_file = 0
          and a.state = 0
    </select>

    <update id="deleteOld">
        update waybill_upload_transportation
        set state = 1
        where waybill_id = #{waybillId}
    </update>

    <update id="auditFile">
        UPDATE waybill_upload_transportation a
            INNER JOIN waybill b
        ON a.waybill_id = b.id
            SET audit_state = 2
        WHERE
            b.freight_forwarder_id = #{freightForwarderId}
          AND audit_state = 0;
    </update>

    <select id="auditWaybillIds" resultType="long">
        SELECT a.waybill_id
        FROM waybill_upload_transportation a
                 INNER JOIN waybill b ON a.waybill_id = b.id
        where b.freight_forwarder_id = #{freightForwarderId}
          AND audit_state = 0
    </select>
</mapper>
