<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.transport.waybill.mapper.DriverElectronicContractMapper">

    <resultMap type="DriverElectronicContract" id="DriverElectronicContractResult">
        <result property="id" column="id"/>
        <result property="fileUrl" column="file_url"/>
        <result property="driverName" column="driver_name"/>
        <result property="driverIdentityCard" column="driver_identity_card"/>
        <result property="freightForwarderId" column="freight_forwarder_id"/>
        <result property="createTime" column="create_time"/>
        <result property="takeTime" column="take_time"/>
        <result property="loseTime" column="lose_time"/>
        <result property="msgUrl" column="msg_url"/>
        <result property="customerId" column="customer_id"/>
        <result property="sendMsg" column="send_msg"/>
        <result property="phone" column="phone"/>
        <result property="state" column="state"/>
        <result property="isEffective" column="is_effective"/>
        <result property="source" column="source"/>
    </resultMap>

    <sql id="selectDriverElectronicContractVo">
        select id,
               file_url,
               driver_name,
               driver_identity_card,
               freight_forwarder_id,
               create_time,
               take_time,
               lose_time,
               msg_url,
               customer_id,
               send_msg,
               phone,
               state,
               is_effective,
               source
        from driver_electronic_contract
    </sql>

    <select id="selectDriverElectronicContractList" parameterType="DriverElectronicContract" resultMap="DriverElectronicContractResult">
        <include refid="selectDriverElectronicContractVo"/>
        <where>
            <if test="fileUrl != null  and fileUrl != ''">and file_url = #{fileUrl}</if>
            <if test="driverName != null  and driverName != ''">and driver_name like concat('%', #{driverName}, '%')</if>
            <if test="driverIdentityCard != null  and driverIdentityCard != ''">and driver_identity_card = #{driverIdentityCard}</if>
            <if test="freightForwarderId != null ">and freight_forwarder_id = #{freightForwarderId}</if>
            <if test="takeTime != null ">and take_time = #{takeTime}</if>
            <if test="loseTime != null ">and lose_time = #{loseTime}</if>
            <if test="msgUrl != null  and msgUrl != ''">and msg_url = #{msgUrl}</if>
            <if test="customerId != null ">and customer_id = #{customerId}</if>
            <if test="sendMsg != null  and sendMsg != ''">and send_msg = #{sendMsg}</if>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>
            <if test="isEffective != null ">and is_effective = #{isEffective}</if>
            <if test="state == 1">and state in (1,3)</if>
            <if test="state == 5">and state &gt; 0</if>
            <if test="source != null ">and source = #{source}</if>
        </where>
    </select>

    <select id="selectDriverElectronicContractById" parameterType="Long" resultMap="DriverElectronicContractResult">
        <include refid="selectDriverElectronicContractVo"/>
        where id = #{id}
    </select>

    <insert id="insertDriverElectronicContract" parameterType="DriverElectronicContract">
        insert into driver_electronic_contract
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="driverName != null">driver_name,</if>
            <if test="driverIdentityCard != null">driver_identity_card,</if>
            <if test="freightForwarderId != null">freight_forwarder_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="takeTime != null">take_time,</if>
            <if test="loseTime != null">lose_time,</if>
            <if test="msgUrl != null">msg_url,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="sendMsg != null">send_msg,</if>
            <if test="phone != null">phone,</if>
            <if test="state != null">state,</if>
            <if test="isEffective != null">is_effective,</if>
            <if test="source != null">source,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="driverName != null">#{driverName},</if>
            <if test="driverIdentityCard != null">#{driverIdentityCard},</if>
            <if test="freightForwarderId != null">#{freightForwarderId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="takeTime != null">#{takeTime},</if>
            <if test="loseTime != null">#{loseTime},</if>
            <if test="msgUrl != null">#{msgUrl},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="sendMsg != null">#{sendMsg},</if>
            <if test="phone != null">#{phone},</if>
            <if test="state != null">#{state},</if>
            <if test="isEffective != null">#{isEffective},</if>
            <if test="source != null">#{source},</if>
        </trim>
    </insert>

    <insert id="insertDriverElectronicContractList">
        insert into driver_electronic_contract
        <foreach collection="list" item="item" index="i">
            <if test="i == 0">
                <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="item.id != null">id,</if>
                    <if test="item.fileUrl != null">file_url,</if>
                    <if test="item.driverName != null">driver_name,</if>
                    <if test="item.driverIdentityCard != null">driver_identity_card,</if>
                    <if test="item.freightForwarderId != null">freight_forwarder_id,</if>
                    <if test="item.createTime != null">create_time,</if>
                    <if test="item.takeTime != null">take_time,</if>
                    <if test="item.loseTime != null">lose_time,</if>
                    <if test="item.msgUrl != null">msg_url,</if>
                    <if test="item.customerId != null">customer_id,</if>
                    <if test="item.sendMsg != null">send_msg,</if>
                    <if test="item.phone != null">phone,</if>
                    <if test="item.state != null">state,</if>
                    <if test="item.isEffective != null">is_effective,</if>
                    <if test="item.source != null">source,</if>
                </trim>
            </if>
        </foreach>
        values
        <foreach collection="list" item="item" separator=",">
            <trim prefix=" (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">#{item.id},</if>
                <if test="item.fileUrl != null">#{item.fileUrl},</if>
                <if test="item.driverName != null">#{item.driverName},</if>
                <if test="item.driverIdentityCard != null">#{item.driverIdentityCard},</if>
                <if test="item.freightForwarderId != null">#{item.freightForwarderId},</if>
                <if test="item.createTime != null">#{item.createTime},</if>
                <if test="item.takeTime != null">#{item.takeTime},</if>
                <if test="item.loseTime != null">#{item.loseTime},</if>
                <if test="item.msgUrl != null">#{item.msgUrl},</if>
                <if test="item.customerId != null">#{item.customerId},</if>
                <if test="item.sendMsg != null">#{item.sendMsg},</if>
                <if test="item.phone != null">#{item.phone},</if>
                <if test="item.state != null">#{item.state},</if>
                <if test="item.isEffective != null">#{item.isEffective},</if>
                <if test="item.source != null">#{item.source},</if>
            </trim>
        </foreach>
    </insert>

    <update id="updateDriverElectronicContract" parameterType="DriverElectronicContract">
        update driver_electronic_contract
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="driverName != null">driver_name = #{driverName},</if>
            <if test="driverIdentityCard != null">driver_identity_card = #{driverIdentityCard},</if>
            <if test="freightForwarderId != null">freight_forwarder_id = #{freightForwarderId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="takeTime != null">take_time = #{takeTime},</if>
            <if test="loseTime != null">lose_time = #{loseTime},</if>
            <if test="msgUrl != null">msg_url = #{msgUrl},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="sendMsg != null">send_msg = #{sendMsg},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="state != null">state = #{state},</if>
            <if test="isEffective != null">is_effective = #{isEffective},</if>
            <if test="source != null">source = #{source},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDriverElectronicContractById" parameterType="Long">
        delete
        from driver_electronic_contract
        where id = #{id}
    </delete>

    <delete id="deleteDriverElectronicContractByIds" parameterType="String">
        delete from driver_electronic_contract where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectEffectiveDriverElectronicContractByFreightForwarderId"  resultMap="DriverElectronicContractResult">
        select *
        from driver_electronic_contract
        where driver_identity_card = #{driverIdentityCard}
          and freight_forwarder_id = #{freightForwarderId}
          and is_effective = 1 limit 1
    </select>

    <select id="selectEffectiveDriverElectronicContract"  resultMap="DriverElectronicContractResult">
        select *
        from driver_electronic_contract
        where driver_identity_card = #{driverIdentityCard}
          and is_effective = 1
    </select>

    <select id="queryByState" resultMap="DriverElectronicContractResult">
        select *
        from driver_electronic_contract
        where state = #{state}
    </select>
    <select id="queryBydriverIdCardAndFreightForwarderId" resultMap="DriverElectronicContractResult">
        select *
        from driver_electronic_contract
        where driver_identity_card in
        <foreach collection="keysList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and freight_forwarder_id = #{freightForwarderId}
        and is_effective = 1
    </select>

    <select id="queryBydriverIdCardAndFreightForwarderIdAndState" resultMap="DriverElectronicContractResult">
        select *
        from driver_electronic_contract
        where driver_identity_card in
        <foreach collection="keysList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and freight_forwarder_id = #{freightForwarderId}
        and state = #{state}
    </select>

    <update id="updateWillExpire">
        update driver_electronic_contract set state = 2
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateExpire">
        update driver_electronic_contract set state = -1 , is_effective = -1
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateTake">
        update driver_electronic_contract set state = 1 , is_effective = 1
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateStateByIds">
        update driver_electronic_contract set state = #{state}
        where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="selectEffectiveDriverElectronicContractByFreightForwarderIds" resultMap="DriverElectronicContractResult">
        SELECT *
        FROM driver_electronic_contract
        WHERE driver_identity_card = #{driverIdentityCard} and is_effective = 1
        <if test="freightForwarderIdList != null and freightForwarderIdList.size() > 0">
            and freight_forwarder_id in
            <foreach collection="freightForwarderIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectByDrivingLicensesAndForwarderId" resultMap="DriverElectronicContractResult">
        SELECT *
        FROM driver_electronic_contract
        WHERE freight_forwarder_id = #{freightForwarderId} and is_effective = 1
        <if test="drivingLicenses != null and drivingLicenses.size() > 0">
            and driver_identity_card in
            <foreach collection="drivingLicenses" item="drivingLicense" open="(" close=")" separator=",">
                #{drivingLicense}
            </foreach>
        </if>
    </select>
</mapper>
