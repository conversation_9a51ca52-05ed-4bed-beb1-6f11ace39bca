<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.transport.waybill.mapper.WaybillMapper">

    <resultMap type="com.zly.project.transport.waybill.domain.Waybill" id="WaybillResult">
        <result property="id" column="id"/>
        <result property="resource" column="resource"/>
        <result property="shippingNoteNumber" column="shipping_note_number"/>
        <result property="orderCreateTime" column="order_create_time"/>
        <result property="makeCodeId" column="make_code_id"/>
        <result property="customerId" column="customer_id"/>
        <result property="customerName" column="customer_name"/>
        <result property="originalDocumentNumber" column="original_document_number"/>
        <result property="billDate" column="bill_date"/>
        <result property="operationPlainId" column="operation_plain_id"/>
        <result property="frameworkContractId" column="framework_contract_id"/>
        <result property="frameworkContractCode" column="framework_contract_code"/>
        <result property="frameworkContractName" column="framework_contract_name"/>
        <result property="manifestNo" column="manifest_no"/>
        <result property="despatchActualDateTime" column="despatch_actual_date_time"/>
        <result property="goodsReceiptDateTime" column="goods_receipt_date_time"/>
        <result property="businessTypeCode" column="business_type_code"/>
        <result property="businessTypeName" column="business_type_name"/>
        <result property="consignorId" column="consignor_id"/>
        <result property="consigneeId" column="consignee_id"/>
        <result property="haulway" column="haulway"/>
        <result property="mileage" column="mileage"/>
        <result property="vehicleId" column="vehicle_id"/>
        <result property="vehicleNumber" column="vehicle_number"/>
        <result property="vehiclePlateColorCode" column="vehicle_plate_color_code"/>
        <result property="driverId" column="driver_id"/>
        <result property="driverName" column="driver_name"/>
        <result property="drivingLicense" column="driving_license"/>
        <result property="telephone" column="telephone"/>
        <result property="actualCarrierId" column="actual_carrier_id"/>
        <result property="actualCarrierName" column="actual_carrier_name"/>
        <result property="actualCarrierContactPhone" column="actual_carrier_contact_phone"/>
        <result property="payeeId" column="payee_id"/>
        <result property="payeeName" column="payee_name"/>
        <result property="descriptionOfGoods" column="description_of_goods"/>
        <result property="weightCube" column="weight_cube"/>
        <result property="receiveFare" column="receive_fare"/>
        <result property="feePrice" column="fee_price"/>
        <result property="feeAmount" column="fee_amount"/>
        <result property="feeUnit" column="fee_unit"/>
        <result property="statementReceiveFare" column="statement_receive_fare"/>
        <result property="payApplyStatus" column="pay_apply_status"/>
        <result property="totalFare" column="total_fare"/>
        <result property="totalPaidFare" column="total_paid_fare"/>
        <result property="payType" column="pay_type"/>
        <result property="payFare" column="pay_fare"/>
        <result property="actualFare" column="actual_fare"/>
        <result property="unpaidFare" column="unpaid_fare"/>
        <result property="prepayType" column="prepay_type"/>
        <result property="prepayMoney" column="prepay_money"/>
        <result property="prepayActualMoney" column="prepay_actual_money"/>
        <result property="prepayUnpaidMoney" column="prepay_unpaid_money"/>
        <result property="oilCardNo" column="oil_card_no"/>
        <result property="arriveMoney" column="arrive_money"/>
        <result property="arriveActualMoney" column="arrive_actual_money"/>
        <result property="arriveUnpaidMoney" column="arrive_unpaid_money"/>
        <result property="receiptMoney" column="receipt_money"/>
        <result property="receiptActualMoney" column="receipt_actual_money"/>
        <result property="receiptUnpaidMoney" column="receipt_unpaid_money"/>
        <result property="locateType" column="locate_type"/>
        <result property="locateStatus" column="locate_status"/>
        <result property="electronicContractStatus" column="electronic_contract_status"/>
        <result property="settleStatus" column="settle_status"/>
        <result property="billStatus" column="bill_status"/>
        <result property="status" column="status"/>
        <result property="signTime" column="sign_time"/>
        <result property="loadTime" column="load_time"/>
        <result property="unloadTime" column="unload_time"/>
        <result property="payTime" column="pay_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="submitBy" column="submit_by"/>
        <result property="submitTime" column="submit_time"/>
        <result property="returnId" column="return_id"/>
        <result property="unloadingName" column="unloading_name"/>
        <result property="unloadingPhone" column="unloading_phone"/>
        <result property="isCompleteTrajectory" column="is_complete_trajectory"/>
        <result property="driverInfoIsComplete" column="driver_info_is_complete"/>
        <result property="vehicleInfoIsComplete" column="vehicle_info_is_complete"/>
        <result property="actualCarrierInfoIsComplete" column="actual_carrier_info_is_complete"/>
        <result property="infoIsComplete" column="info_is_complete"/>
        <result property="freightForwarderId" column="freight_forwarder_id"/>
        <result property="electronicContractState" column="electronic_contract_state"/>
        <result property="remark" column="remark"/>
        <result property="receiptStatus" column="receipt_status"/>
        <result property="waybillPrice" column="waybill_price"/>
        <result property="actualCarrierIdentityCard" column="actual_carrier_identity_card"/>
        <result property="payeeBankCardNo" column="payee_bank_card_no"/>
        <result property="verifyTrajectoryState" column="verify_trajectory_state"/>
        <result property="taxFirstUploadState" column="tax_first_upload_state"/>
        <result property="taxFirstResState" column="tax_first_res_state"/>
        <result property="taxArriveUploadState" column="tax_arrive_upload_state"/>
        <result property="taxArriveResState" column="tax_arrive_res_state"/>
        <result property="taxSecondUploadState" column="tax_second_upload_state"/>
        <result property="taxSecondResState" column="tax_second_res_state"/>
        <result property="taxThirdUploadState" column="tax_third_upload_state"/>
        <result property="taxThirdResState" column="tax_third_res_state"/>
        <result property="taxUploadLastTime" column="tax_upload_last_time"/>
        <result property="isTaxUpload" column="is_tax_upload"/>
        <result property="taxUploadVerifyLastTime" column="tax_upload_verify_last_time"/>
        <result property="taxUploadFailReason" column="tax_upload_fail_reason"/>
        <result property="zeroingMoney" column="zeroing_money"/>
        <result property="beforeZeroingMoney" column="before_zeroing_money"/>
        <result property="carCaptainId" column="car_captain_id"/>
        <result property="carCaptainName" column="car_captain_name"/>
        <result property="carCaptainPhone" column="car_captain_phone"/>
        <result property="carCaptainIdentityCard" column="car_captain_identity_card"/>
        <result property="auxiliaryStaffId" column="auxiliary_staff_id"/>
        <result property="auxiliaryStaffName" column="auxiliary_staff_name"/>
        <result property="auxiliaryStaffIdentityCard" column="auxiliary_staff_identity_card"/>
        <result property="auxiliaryStaffPhone" column="auxiliary_staff_phone"/>
        <result property="fareState" column="fare_state"/>
        <result property="deductMoney" column="deduct_money"/>
        <result property="confirmReceiptState" column="confirm_receipt_state"/>
        <result property="payeeType" column="payee_type"/>
        <result property="riskResultGrade" column="risk_result_grade"/>
        <result property="transportationAgreementState" column="transportation_agreement_state"/>
        <result property="proxyInvoiceState" column="proxy_invoice_state"/>
        <result property="artificialAuditState" column="artificial_audit_state"/>
        <result property="payeeBankCardNo" column="payee_bank_card_no"/>
        <result property="payeeIdentityCard" column="payee_identity_card"/>
        <association property="waybillSlave" javaType="com.zly.project.transport.waybill.domain.WaybillSlave">
            <result property="id" column="id"/>
            <result property="loadingWeight" column="loading_weight"/>
            <result property="loadingCube" column="loading_cube"/>
            <result property="unloadWeight" column="unload_weight"/>
            <result property="unloadCube" column="unload_cube"/>
            <result property="policyType" column="policy_type"/>
            <result property="policyNumber" column="policy_number"/>
            <result property="insuranceCompanyCode" column="insurance_company_code"/>
            <result property="uploginkDesc" column="uplogink_desc"/>
            <result property="upetcStatus" column="upetc_status"/>
            <result property="upetcTime" column="upetc_time"/>
            <result property="upetcDesc" column="upetc_desc"/>
            <result property="uploginkStatus" column="uplogink_status"/>
            <result property="uploginkTime" column="uplogink_time"/>
            <result property="remark" column="remark"/>
            <result property="makeCodeRuleId" column="make_code_rule_id"/>
            <result property="zeroingRule" column="zeroing_rule"/>
        </association>
    </resultMap>

    <resultMap type="com.zly.project.transport.waybill.domain.WaybillSlave" id="WaybillSlaveResult">
        <result property="id" column="sub_id"/>
        <result property="loadingWeight" column="sub_loading_weight"/>
        <result property="loadingCube" column="sub_loading_cube"/>
        <result property="unloadWeight" column="sub_unload_weight"/>
        <result property="unloadCube" column="sub_unload_cube"/>
        <result property="policyType" column="sub_policy_type"/>
        <result property="policyStatus" column="sub_policy_status"/>
        <result property="policyFare" column="sub_policy_fare"/>
        <result property="policyNumber" column="sub_policy_number"/>
        <result property="insuranceCompanyCode" column="sub_insurance_company_code"/>
        <result property="uploginkDesc" column="sub_uplogink_desc"/>
        <result property="upetcStatus" column="sub_upetc_status"/>
        <result property="upetcTime" column="sub_upetc_time"/>
        <result property="upetcDesc" column="sub_upetc_desc"/>
        <result property="uploginkStatus" column="sub_uplogink_status"/>
        <result property="uploginkTime" column="sub_uplogink_time"/>
        <result property="remark" column="sub_remark"/>
    </resultMap>

    <sql id="selectWaybillVo">
        SELECT id,
               resource,
               shipping_note_number,
               order_create_time,
               make_code_id,
               customer_id,
               customer_name,
               original_document_number,
               bill_date,
               operation_plain_id,
               framework_contract_id,
               framework_contract_code,
               framework_contract_name,
               manifest_no,
               despatch_actual_date_time,
               goods_receipt_date_time,
               business_type_code,
               business_type_name,
               consignor_id,
               consignee_id,
               haulway,
               mileage,
               vehicle_id,
               vehicle_number,
               vehicle_plate_color_code,
               driver_id,
               driver_name,
               driving_license,
               telephone,
               actual_carrier_id,
               actual_carrier_name,
               actual_carrier_contact_phone,
               payee_id,
               payee_name,
               payee_identity_card,
               description_of_goods,
               weight_cube,
               receive_fare,
               fee_price,
               fee_amount,
               fee_unit,
               statement_receive_fare,
               pay_apply_status,
               pay_type,
               pay_fare,
               prepay_money,
               prepay_unpaid_money,
               arrive_money,
               arrive_unpaid_money,
               receipt_money,
               receipt_unpaid_money,
               actual_fare,
               unpaid_fare,
               prepay_type,
               oil_card_no,
               locate_type,
               locate_status,
               electronic_contract_status,
               settle_status,
               bill_status,
               status,
               sign_time,
               load_time,
               unload_time,
               pay_time,
               create_by,
               create_time,
               update_by,
               update_time,
               submit_by,
               submit_time,
               is_complete_trajectory,
               driver_info_is_complete,
               vehicle_info_is_complete,
               actual_carrier_info_is_complete,
               info_is_complete,
               freight_forwarder_id,
               electronic_contract_state,
               remark,
               receipt_status,
               waybill_price,
               actual_carrier_identity_card,
               payee_bank_card_no,
               verify_trajectory_state,
               tax_first_upload_state,
               tax_first_res_state,
               tax_arrive_upload_state,
               tax_arrive_res_state,
               tax_second_upload_state,
               tax_second_res_state,
               tax_third_upload_state,
               tax_third_res_state,
               tax_upload_last_time,
               tax_upload_verify_last_time,
               is_tax_upload,
               tax_upload_fail_reason,
               zeroing_money,
               before_zeroing_money,
               confirm_receipt_state,
               deduct_money,
               car_captain_id,
               car_captain_name,
               car_captain_phone,
               car_captain_identity_card,
               auxiliary_staff_id,
               auxiliary_staff_name,
               auxiliary_staff_identity_card,
               auxiliary_staff_phone,
               fare_state,
               payee_type,
               risk_result_grade,
               transportation_agreement_state,
               proxy_invoice_state,
               artificial_audit_state
        FROM waybill
    </sql>

    <sql id="selectWaybillVoOld">
        SELECT id,
               resource,
               shipping_note_number,
               order_create_time,
               make_code_id,
               customer_id,
               customer_name,
               original_document_number,
               bill_date,
               operation_plain_id,
               framework_contract_id,
               framework_contract_code,
               framework_contract_name,
               manifest_no,
               despatch_actual_date_time,
               goods_receipt_date_time,
               business_type_code,
               business_type_name,
               consignor_id,
               consignee_id,
               haulway,
               mileage,
               vehicle_id,
               vehicle_number,
               driver_id,
               driver_name,
               driving_license,
               telephone,
               actual_carrier_id,
               actual_carrier_name,
               actual_carrier_contact_phone,
               payee_id,
               payee_name,
               payee_identity_card,
               description_of_goods,
               weight_cube,
               receive_fare,
               fee_price,
               fee_amount,
               fee_unit,
               statement_receive_fare,
               pay_apply_status,
               pay_type,
               pay_fare,
               prepay_money,
               prepay_unpaid_money,
               arrive_money,
               arrive_unpaid_money,
               receipt_money,
               receipt_unpaid_money,
               actual_fare,
               unpaid_fare,
               prepay_type,
               oil_card_no,
               locate_type,
               locate_status,
               electronic_contract_status,
               settle_status,
               bill_status,
               status,
               sign_time,
               load_time,
               unload_time,
               pay_time,
               create_by,
               create_time,
               update_by,
               update_time,
               submit_by,
               submit_time,
               is_complete_trajectory,
               driver_info_is_complete,
               vehicle_info_is_complete,
               actual_carrier_info_is_complete,
               info_is_complete,
               freight_forwarder_id,
               electronic_contract_state,
               remark,
               receipt_status,
               waybill_price,
               actual_carrier_identity_card,
               payee_bank_card_no,
               verify_trajectory_state,
               tax_first_upload_state,
               tax_first_res_state,
               tax_arrive_upload_state,
               tax_arrive_res_state,
               tax_second_upload_state,
               tax_second_res_state,
               tax_third_upload_state,
               tax_third_res_state,
               tax_upload_last_time,
               tax_upload_verify_last_time,
               is_tax_upload,
               tax_upload_fail_reason,
               zeroing_money,
               before_zeroing_money,
               deduct_money,
               arrive_actual_money,
               prepay_actual_money,
               receipt_actual_money,
               return_id,
               total_fare,
               total_paid_fare,
               unloading_name,
               unloading_phone,
               fare_state
        FROM waybill
    </sql>

    <sql id="selectWaybillVoShanDao">
        SELECT id,
               resource,
               shipping_note_number,
               order_create_time,
               make_code_id,
               customer_id,
               customer_name,
               original_document_number,
               bill_date,
               operation_plain_id,
               framework_contract_code,
               manifest_no,
               despatch_actual_date_time,
               goods_receipt_date_time,
               business_type_code,
               business_type_name,
               consignor_id,
               consignee_id,
               haulway,
               mileage,
               vehicle_id,
               vehicle_number,
               vehicle_plate_color_code,
               driver_id,
               driver_name,
               driving_license,
               payee_identity_card,
               telephone,
               actual_carrier_id,
               actual_carrier_name,
               actual_carrier_contact_phone,
               actual_carrier_identity_card,
               payee_id,
               payee_name,
               description_of_goods,
               weight_cube,
               receive_fare,
               fee_price,
               fee_amount,
               fee_unit,
               statement_receive_fare,
               pay_apply_status,
               total_fare,
               total_paid_fare,
               pay_type,
               pay_fare,
               prepay_money,
               prepay_actual_money,
               prepay_unpaid_money,
               arrive_money,
               arrive_actual_money,
               arrive_unpaid_money,
               receipt_money,
               receipt_actual_money,
               receipt_unpaid_money,
               actual_fare,
               unpaid_fare,
               prepay_type,
               oil_card_no,
               locate_type,
               locate_status,
               electronic_contract_status,
               settle_status,
               bill_status,
               status,
               sign_time,
               load_time,
               unload_time,
               pay_time,
               create_by,
               create_time,
               update_by,
               update_time,
               submit_by,
               submit_time,
               receipt_status,
               is_complete_trajectory,
               payee_type,
               freight_forwarder_id,
               electronic_contract_state,
               remark,
               framework_contract_id,
               framework_contract_name,
               waybill_price,
               verify_trajectory_state,
               tax_first_upload_state,
               tax_first_res_state,
               tax_arrive_upload_state,
               tax_arrive_res_state,
               tax_second_upload_state,
               tax_second_res_state,
               tax_third_upload_state,
               tax_third_res_state,
               tax_upload_last_time,
               is_tax_upload,
               zeroing_money,
               before_zeroing_money,
               deduct_money,
               make_code_rule_id,
               zeroing_rule,
               before_zeroing_money,
               car_captain_id,
               car_captain_name,
               car_captain_phone,
               car_captain_identity_card,
               auxiliary_staff_id,
               auxiliary_staff_name,
               auxiliary_staff_identity_card,
               auxiliary_staff_phone,
               payee_type,
               risk_result_grade
        from waybill
    </sql>
    <select id="selectWaybillById" parameterType="Long" resultMap="WaybillResult">
        SELECT a.id,
               a.resource,
               a.shipping_note_number,
               a.order_create_time,
               a.make_code_id,
               a.customer_id,
               a.customer_name,
               a.original_document_number,
               a.bill_date,
               a.operation_plain_id,
               a.framework_contract_code,
               a.manifest_no,
               a.despatch_actual_date_time,
               a.goods_receipt_date_time,
               a.business_type_code,
               a.business_type_name,
               a.consignor_id,
               a.consignee_id,
               a.haulway,
               a.mileage,
               a.vehicle_id,
               a.vehicle_number,
               a.vehicle_plate_color_code,
               a.driver_id,
               a.driver_name,
               a.driving_license,
               a.telephone,
               a.actual_carrier_id,
               a.actual_carrier_name,
               a.actual_carrier_contact_phone,
               a.actual_carrier_identity_card,
               a.payee_id,
               a.payee_name,
               a.payee_identity_card,
               a.payee_bank_card_no,
               a.description_of_goods,
               a.weight_cube,
               a.receive_fare,
               a.fee_price,
               a.fee_amount,
               a.fee_unit,
               a.statement_receive_fare,
               a.pay_apply_status,
               a.total_fare,
               a.total_paid_fare,
               a.pay_type,
               a.pay_fare,
               a.prepay_money,
               a.prepay_actual_money,
               a.prepay_unpaid_money,
               a.arrive_money,
               a.arrive_actual_money,
               a.arrive_unpaid_money,
               a.receipt_money,
               a.receipt_actual_money,
               a.receipt_unpaid_money,
               a.actual_fare,
               a.unpaid_fare,
               a.prepay_type,
               a.oil_card_no,
               a.locate_type,
               a.locate_status,
               a.electronic_contract_status,
               a.settle_status,
               a.bill_status,
               a.status,
               a.sign_time,
               a.load_time,
               a.unload_time,
               a.pay_time,
               a.create_by,
               a.create_time,
               a.update_by,
               a.update_time,
               a.submit_by,
               a.submit_time,
               a.receipt_status,
               a.is_complete_trajectory,
               a.payee_type,
               a.freight_forwarder_id,
               a.electronic_contract_state,
               b.id                     AS sub_id,
               b.loading_weight         AS sub_loading_weight,
               b.loading_cube           AS sub_loading_cube,
               b.unload_weight          AS sub_unload_weight,
               b.unload_cube            AS sub_unload_cube,
               b.policy_type            AS sub_policy_type,
               b.policy_number          AS sub_policy_number,
               b.insurance_company_code AS sub_insurance_company_code,
               b.uplogink_desc          AS sub_uplogink_desc,
               b.upetc_status           AS sub_upetc_status,
               b.upetc_time             AS sub_upetc_time,
               b.upetc_desc             AS sub_upetc_desc,
               b.uplogink_status        AS sub_uplogink_status,
               b.uplogink_time          AS sub_uplogink_time,
               b.remark                 AS sub_remark,
               a.remark,
               a.framework_contract_id,
               a.framework_contract_name,
               a.waybill_price,
               a.verify_trajectory_state,
               a.tax_first_upload_state,
               a.tax_first_res_state,
               a.tax_arrive_upload_state,
               a.tax_arrive_res_state,
               a.tax_second_upload_state,
               a.tax_second_res_state,
               a.tax_third_upload_state,
               a.tax_third_res_state,
               a.tax_upload_last_time,
               tax_upload_verify_last_time,
               a.is_tax_upload,
               a.zeroing_money,
               a.before_zeroing_money,
               a.deduct_money,
               a.confirm_receipt_state,
               b.make_code_rule_id,
               b.zeroing_rule,
               a.before_zeroing_money,
               a.car_captain_id,
               a.car_captain_name,
               a.car_captain_phone,
               a.car_captain_identity_card,
               a.auxiliary_staff_id,
               a.auxiliary_staff_name,
               a.auxiliary_staff_identity_card,
               a.auxiliary_staff_phone,
               a.payee_type,
               a.fare_state,
               a.risk_result_grade,
               a.transportation_agreement_state,
               a.proxy_invoice_state,
               a.artificial_audit_state
        FROM waybill a
                 LEFT JOIN waybill_slave b ON b.id = a.id
        WHERE a.id = #{id}
    </select>

    <select id="selectWaybillList" parameterType="com.zly.project.transport.waybill.domain.Waybill"
            resultMap="WaybillResult">
        <include refid="selectWaybillVo"/>
        <where>
            <if test="shippingNoteNumber != null  and shippingNoteNumber != ''">
                and shipping_note_number = #{shippingNoteNumber}
            </if>
            <if test="params != null and params.beginOrderCreateTime != null and params.beginOrderCreateTime != '' and params.endOrderCreateTime != null and params.endOrderCreateTime != ''">
                and order_create_time between #{params.beginOrderCreateTime} and #{params.endOrderCreateTime}
            </if>
            <if test="makeCodeId != null ">and make_code_id = #{makeCodeId}</if>
            <if test="customerId != null ">and customer_id = #{customerId}</if>
            <if test="customerName != null  and customerName != ''">
                and customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="originalDocumentNumber != null  and originalDocumentNumber != ''">
                and original_document_number = #{originalDocumentNumber}
            </if>
            <if test="billDate != null  and billDate != ''">
                and bill_date = #{billDate}
            </if>
            <if test="operationPlainId != null">and operation_plain_id = #{operationPlainId}</if>
            <if test="frameworkContractId != null ">and framework_contract_id = #{frameworkContractId}</if>
            <if test="frameworkContractIds != null and frameworkContractIds.size() > 0">
                AND frameworkContractId IN
                <foreach collection="frameworkContractIds" item="frameworkContractId" open="(" close=")" separator=",">
                    #{frameworkContractId}
                </foreach>
            </if>
            <if test="frameworkContractCode != null  and frameworkContractCode != ''">
                and framework_contract_code = #{frameworkContractCode}
            </if>
            <if test="frameworkContractName != null  and frameworkContractName != ''">
                and framework_contract_name like concat('%', #{frameworkContractName}, '%')
            </if>
            <if test="manifestNo != null  and manifestNo != ''">
                and manifest_no like concat('%', #{manifestNo}, '%')
            </if>
            <if test="params != null and params.beginDespatchActualDateTime != null and params.beginDespatchActualDateTime != '' and params.endDespatchActualDateTime != null and params.endDespatchActualDateTime != ''">
                and despatch_actual_date_time between #{params.beginDespatchActualDateTime} and
                #{params.endDespatchActualDateTime}
            </if>
            <if test="params != null and params.beginGoodsReceiptDateTime != null and params.beginGoodsReceiptDateTime != '' and params.endGoodsReceiptDateTime != null and params.endGoodsReceiptDateTime != ''">
                and goods_receipt_date_time between #{params.beginGoodsReceiptDateTime} and
                #{params.endGoodsReceiptDateTime}
            </if>
            <if test="businessTypeCode != null  and businessTypeCode != ''">
                and business_type_code = #{businessTypeCode}
            </if>
            <if test="businessTypeName != null  and businessTypeName != ''">
                and business_type_name like concat('%', #{businessTypeName}, '%')
            </if>
            <if test="consignorId != null ">
                and consignor_id = #{consignorId}
            </if>
            <if test="consigneeId != null ">
                and consignee_id = #{consigneeId}
            </if>
            <if test="haulway != null  and haulway != ''">
                and haulway = #{haulway}
            </if>
            <if test="mileage != null ">
                and mileage = #{mileage}
            </if>
            <if test="vehicleId != null ">
                and vehicle_id = #{vehicleId}
            </if>
            <if test="vehicleNumber != null  and vehicleNumber != ''">
                and vehicle_number = #{vehicleNumber}
            </if>
            <if test="driverId != null ">
                and driver_id = #{driverId}
            </if>
            <if test="driverName != null  and driverName != ''">
                and driver_name like concat('%', #{driverName}, '%')
            </if>
            <if test="drivingLicense != null  and drivingLicense != ''">
                and driving_license = #{drivingLicense}
            </if>
            <if test="telephone != null  and telephone != ''">
                and telephone = #{telephone}
            </if>
            <if test="actualCarrierId != null ">
                and actual_carrier_id = #{actualCarrierId}
            </if>
            <if test="actualCarrierName != null  and actualCarrierName != ''">
                and actual_carrier_name like concat('%', #{actualCarrierName}, '%')
            </if>
            <if test="actualCarrierContactPhone != null  and actualCarrierContactPhone != ''">
                and actual_carrier_contact_phone = #{actualCarrierContactPhone}
            </if>
            <if test="payeeId != null ">
                and payee_id = #{payeeId}
            </if>
            <if test="payeeName != null  and payeeName != ''">
                and payee_name like concat('%', #{payeeName}, '%')
            </if>
            <if test="descriptionOfGoods != null  and descriptionOfGoods != ''">
                and description_of_goods = #{descriptionOfGoods}
            </if>
            <if test="weightCube != null  and weightCube != ''">
                and weight_cube = #{weightCube}
            </if>
            <if test="receiveFare != null ">
                and receive_fare = #{receiveFare}
            </if>
            <if test="feePrice != null ">
                and fee_price = #{feePrice}
            </if>
            <if test="feeAmount != null ">
                and fee_amount = #{feeAmount}
            </if>
            <if test="feeUnit != null ">
                and fee_unit = #{feeUnit}
            </if>
            <if test="statementReceiveFare != null ">
                and statement_receive_fare = #{statementReceiveFare}
            </if>
            <if test="payApplyStatus != null ">
                and pay_apply_status = #{payApplyStatus}
            </if>
            <if test="totalFare != null ">
                and total_fare = #{totalFare}
            </if>
            <if test="totalPaidFare != null ">
                and total_paid_fare = #{totalPaidFare}
            </if>
            <if test="payType != null ">
                and pay_type = #{payType}
            </if>
            <if test="payFare != null ">
                and pay_fare = #{payFare}
            </if>
            <if test="prepayMoney != null ">
                and prepay_money = #{prepayMoney}
            </if>
            <if test="prepayUnpaidMoney != null ">
                and prepay_unpaid_money = #{prepayUnpaidMoney}
            </if>
            <if test="arriveMoney != null ">
                and arrive_money = #{arriveMoney}
            </if>
            <if test="arriveUnpaidMoney != null ">
                and arrive_unpaid_money = #{arriveUnpaidMoney}
            </if>
            <if test="receiptMoney != null ">
                and receipt_money = #{receiptMoney}
            </if>
            <if test="receiptUnpaidMoney != null ">
                and receipt_unpaid_money = #{receiptUnpaidMoney}
            </if>
            <if test="actualFare != null ">
                and actual_fare = #{actualFare}
            </if>
            <if test="unpaidFare != null ">
                and unpaid_fare = #{unpaidFare}
            </if>
            <if test="prepayType != null ">
                and prepay_type = #{prepayType}
            </if>
            <if test="oilCardNo != null  and oilCardNo != ''">
                and oil_card_no = #{oilCardNo}
            </if>
            <if test="locateType != null ">
                and locate_type = #{locateType}
            </if>
            <if test="locateStatus != null ">
                and locate_status = #{locateStatus}
            </if>
            <if test="electronicContractStatus != null ">
                and electronic_contract_status = #{electronicContractStatus}
            </if>
            <if test="settleStatus != null ">
                and settle_status = #{settleStatus}
            </if>
            <if test="billStatus != null ">
                and bill_status = #{billStatus}
            </if>
            <if test="status != null and status != -2">
                and `status` = #{status}
            </if>
            <if test="status != null and status == -2">
                and `status` &gt; 1
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND `status` IN
                <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="params != null and params.beginSignTime != null and params.beginSignTime != '' and params.endSignTime != null and params.endSignTime != ''">
                and sign_time between #{params.beginSignTime} and #{params.endSignTime}
            </if>
            <if test="params != null and params.beginLoadTime != null and params.beginLoadTime != '' and params.endLoadTime != null and params.endLoadTime != ''">
                and load_time between #{params.beginLoadTime} and #{params.endLoadTime}
            </if>
            <if test="params != null and params.beginUnloadTime != null and params.beginUnloadTime != '' and params.endUnloadTime != null and params.endUnloadTime != ''">
                and unload_time between #{params.beginUnloadTime} and #{params.endUnloadTime}
            </if>
            <if test="payTime != null ">
                and pay_time = #{payTime}
            </if>
            <if test="submitBy != null  and submitBy != ''">
                and submit_by = #{submitBy}
            </if>
            <if test="params != null and params.beginSubmitTime != null and params.beginSubmitTime != '' and params.endSubmitTime != null and params.endSubmitTime != ''">
                and submit_time between #{params.beginSubmitTime} and #{params.endSubmitTime}
            </if>
            <if test="params != null and params.createTimeStart != null and params.createTimeStart != ''">
                and create_time &gt; #{params.createTimeStart}
            </if>
            <if test="params != null and params.createTimeEnd != null and params.createTimeEnd != ''">
                and create_time &lt; #{params.createTimeEnd}
            </if>
            <if test="createTiemStart != null and createTiemStart != ''">
                and create_time &gt; #{createTiemStart}
            </if>
            <if test="driverInfoIsComplete != null">and driver_info_is_complete=#{driverInfoIsComplete}</if>
            <if test="vehicleInfoIsComplete != null">and vehicle_info_is_complete=#{vehicleInfoIsComplete}</if>
            <if test="actualCarrierInfoIsComplete != null">and
                actual_carrier_info_is_complete=#{actualCarrierInfoIsComplete}
            </if>
            <if test="infoIsComplete != null">and info_is_complete=#{infoIsComplete}</if>
            <if test="freightForwarderId != null">and freight_forwarder_id=#{freightForwarderId}</if>
            <if test="actualCarrierIds != null and actualCarrierIds.size() > 0">
                and actual_carrier_id in
                <foreach item="id" collection="actualCarrierIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="actualCarrierIdentityCard != null and actualCarrierIdentityCard != ''">and
                actual_carrier_identity_card=#{actualCarrierIdentityCard}
            </if>
            <if test="payeeBankCardNo != null and payeeBankCardNo != ''">and payee_bank_card_no=#{payeeBankCardNo}</if>
            <if test="isTaxUpload != null">
                and is_tax_upload = #{isTaxUpload}
            </if>

            <if test="carCaptainId != null">
                and car_captain_id = #{carCaptainId}
            </if>
            <if test="carCaptainName != null and carCaptainName != ''">
                and car_captain_name = #{carCaptainName}
            </if>
            <if test="carCaptainPhone != null and carCaptainPhone != ''">
                and car_captain_phone = #{carCaptainPhone}
            </if>
            <if test="carCaptainIdentityCard != null and carCaptainIdentityCard != ''">
                and car_captain_identity_card = #{carCaptainIdentityCard}
            </if>
            <if test="auxiliaryStaffId != null">
                and auxiliary_staff_id = #{auxiliaryStaffId}
            </if>
            <if test="auxiliaryStaffName != null and auxiliaryStaffName !=''">
                and auxiliary_staff_name = #{auxiliaryStaffName}
            </if>
            <if test="auxiliaryStaffIdentityCard != null and auxiliaryStaffIdentityCard != ''">
                and auxiliary_staff_identity_card = #{auxiliaryStaffIdentityCard}
            </if>
            <if test="auxiliaryStaffPhone != null and auxiliaryStaffPhone !=''">
                and auxiliary_staff_phone = #{auxiliaryStaffPhone}
            </if>
            <if test="riskResultGrade != null">
                and risk_result_grade = #{riskResultGrade}
            </if>
            <if test="transportationAgreementState != null">
                and transportation_agreement_state = #{transportationAgreementState}
            </if>
            <if test="proxyInvoiceState != null">
                and proxy_invoice_state = #{proxyInvoiceState}
            </if>
            <if test="artificialAuditState != null">
                and artificial_audit_state = #{artificialAuditState}
            </if>
        </where>
    </select>

    <insert id="insertWaybill" parameterType="com.zly.project.transport.waybill.domain.Waybill">
        insert into waybill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="resource != null">resource,</if>
            <if test="shippingNoteNumber != null and shippingNoteNumber != ''">shipping_note_number,</if>
            <if test="orderCreateTime != null">order_create_time,</if>
            <if test="makeCodeId != null">make_code_id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="originalDocumentNumber != null and originalDocumentNumber != ''">original_document_number,</if>
            <if test="billDate != null">bill_date,</if>
            <if test="operationPlainId != null">operation_plain_id,</if>
            <if test="frameworkContractId != null">framework_contract_id,</if>
            <if test="frameworkContractCode != null and frameworkContractCode != ''">framework_contract_code,</if>
            <if test="frameworkContractName != null and frameworkContractName != ''">framework_contract_name,</if>
            <if test="manifestNo != null and manifestNo != ''">manifest_no,</if>
            <if test="despatchActualDateTime != null">despatch_actual_date_time,</if>
            <if test="goodsReceiptDateTime != null">goods_receipt_date_time,</if>
            <if test="businessTypeCode != null and businessTypeCode != ''">business_type_code,</if>
            <if test="businessTypeName != null and businessTypeName != ''">business_type_name,</if>
            <if test="consignorId != null">consignor_id,</if>
            <if test="consigneeId != null">consignee_id,</if>
            <if test="haulway != null and haulway != ''">haulway,</if>
            <if test="mileage != null">mileage,</if>
            <if test="vehicleId != null">vehicle_id,</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">vehicle_number,</if>
            <if test="vehiclePlateColorCode != null and vehiclePlateColorCode != ''">vehicle_plate_color_code,</if>
            <if test="driverId != null">driver_id,</if>
            <if test="driverName != null and driverName != ''">driver_name,</if>
            <if test="drivingLicense != null and drivingLicense != ''">driving_license,</if>
            <if test="telephone != null and telephone != ''">telephone,</if>
            <if test="actualCarrierId != null">actual_carrier_id,</if>
            <if test="actualCarrierName != null and actualCarrierName != ''">actual_carrier_name,</if>
            <if test="actualCarrierContactPhone != null and actualCarrierContactPhone != ''">
                actual_carrier_contact_phone,
            </if>
            <if test="payeeId != null">payee_id,</if>
            <if test="payeeName != null and payeeName != ''">payee_name,</if>
            <if test="descriptionOfGoods != null and descriptionOfGoods != ''">description_of_goods,</if>
            <if test="weightCube != null and weightCube != ''">weight_cube,</if>
            <if test="receiveFare != null">receive_fare,</if>
            <if test="feePrice != null">fee_price,</if>
            <if test="feeAmount != null">fee_amount,</if>
            <if test="feeUnit != null">fee_unit,</if>
            <if test="statementReceiveFare != null">statement_receive_fare,</if>
            <if test="payApplyStatus != null">pay_apply_status,</if>
            <if test="totalFare != null">total_fare,</if>
            <if test="totalPaidFare != null">total_paid_fare,</if>
            <if test="payType != null">pay_type,</if>
            <if test="payFare != null">pay_fare,</if>
            <if test="prepayMoney != null">prepay_money,</if>
            <if test="prepayActualMoney != null">prepay_actual_money,</if>
            <if test="prepayUnpaidMoney != null">prepay_unpaid_money,</if>
            <if test="arriveMoney != null">arrive_money,</if>
            <if test="arriveActualMoney != null">arrive_actual_money,</if>
            <if test="arriveUnpaidMoney != null">arrive_unpaid_money,</if>
            <if test="receiptMoney != null">receipt_money,</if>
            <if test="receiptActualMoney != null">receipt_actual_money,</if>
            <if test="receiptUnpaidMoney != null">receipt_unpaid_money,</if>
            <if test="actualFare != null">actual_fare,</if>
            <if test="unpaidFare != null">unpaid_fare,</if>
            <if test="prepayType != null">prepay_type,</if>
            <if test="oilCardNo != null and oilCardNo != ''">oil_card_no,</if>
            <if test="locateType != null">locate_type,</if>
            <if test="locateStatus != null">locate_status,</if>
            <if test="electronicContractStatus != null">electronic_contract_status,</if>
            <if test="settleStatus != null">settle_status,</if>
            <if test="billStatus != null">bill_status,</if>
            <if test="status != null">status,</if>
            <if test="signTime != null">sign_time,</if>
            <if test="loadTime != null">load_time,</if>
            <if test="unloadTime != null">unload_time,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="submitBy != null and submitBy != ''">submit_by,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="isCompleteTrajectory != null">is_complete_trajectory,</if>
            <if test="driverInfoIsComplete != null">driver_info_is_complete,</if>
            <if test="vehicleInfoIsComplete != null">vehicle_info_is_complete,</if>
            <if test="actualCarrierInfoIsComplete != null">actual_carrier_info_is_complete,</if>
            <if test="infoIsComplete != null">info_is_complete,</if>
            <if test="freightForwarderId != null">freight_forwarder_id,</if>
            <if test="electronicContractState != null">electronic_contract_state,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="waybillPrice != null ">waybill_price,</if>
            <if test="receiptStatus != null ">receipt_status,</if>
            <if test="actualCarrierIdentityCard != null and actualCarrierIdentityCard != ''">
                actual_carrier_identity_card,
            </if>
            <if test="payeeBankCardNo != null and payeeBankCardNo != ''">payee_bank_card_no,</if>
            <if test="verifyTrajectoryState != null">verify_trajectory_state,</if>
            <if test="taxFirstUploadState != null">tax_first_upload_state,</if>
            <if test="taxFirstResState != null">tax_first_res_state,</if>
            <if test="taxArriveUploadState != null">tax_arrive_upload_state,</if>
            <if test="taxArriveResState != null">tax_arrive_res_state,</if>
            <if test="taxSecondUploadState != null">tax_second_upload_state,</if>
            <if test="taxSecondResState != null">tax_second_res_state,</if>
            <if test="taxThirdUploadState != null">tax_third_upload_state,</if>
            <if test="taxThirdResState != null">tax_third_res_state,</if>
            <if test="taxUploadLastTime != null">tax_upload_last_time,</if>
            <if test="isTaxUpload != null">is_tax_upload,</if>
            <if test="taxUploadFailReason != null and taxUploadFailReason != ''">tax_upload_fail_reason,</if>
            <if test="zeroingMoney != null">zeroing_money,</if>
            <if test="beforeZeroingMoney != null">before_zeroing_money,</if>
            <if test="deductMoney != null">deduct_money,</if>
            <if test="confirmReceiptState != null">confirm_receipt_state,</if>
            <if test="carCaptainId != null">car_captain_id,</if>
            <if test="carCaptainName != null">car_captain_name,</if>
            <if test="carCaptainPhone != null">car_captain_phone,</if>
            <if test="carCaptainIdentityCard != null">car_captain_identity_card,</if>
            <if test="auxiliaryStaffId != null">auxiliary_staff_id,</if>
            <if test="auxiliaryStaffName != null">auxiliary_staff_name,</if>
            <if test="auxiliaryStaffIdentityCard != null">auxiliary_staff_identity_card,</if>
            <if test="auxiliaryStaffPhone != null">auxiliary_staff_phone,</if>
            <if test="fareState != null">fare_state,</if>
            <if test="payeeType != null">payee_type,</if>
            <if test="riskResultGrade != null">risk_result_grade,</if>
            <if test="transportationAgreementState != null">transportation_agreement_state,</if>
            <if test="proxyInvoiceState != null">proxy_invoice_state,</if>
            <if test="artificialAuditState != null">artificial_audit_state,</if>
            <if test="payeeIdentityCard != null">payee_identity_card,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="resource != null">#{resource},</if>
            <if test="shippingNoteNumber != null and shippingNoteNumber != ''">#{shippingNoteNumber},</if>
            <if test="orderCreateTime != null">#{orderCreateTime},</if>
            <if test="makeCodeId != null">#{makeCodeId},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="originalDocumentNumber != null and originalDocumentNumber != ''">#{originalDocumentNumber},</if>
            <if test="billDate != null">#{billDate},</if>
            <if test="operationPlainId != null">#{operationPlainId},</if>
            <if test="frameworkContractId != null">#{frameworkContractId},</if>
            <if test="frameworkContractCode != null and frameworkContractCode != ''">#{frameworkContractCode},</if>
            <if test="frameworkContractName != null and frameworkContractName != ''">#{frameworkContractName},</if>
            <if test="manifestNo != null and manifestNo != ''">#{manifestNo},</if>
            <if test="despatchActualDateTime != null">#{despatchActualDateTime},</if>
            <if test="goodsReceiptDateTime != null">#{goodsReceiptDateTime},</if>
            <if test="businessTypeCode != null and businessTypeCode != ''">#{businessTypeCode},</if>
            <if test="businessTypeName != null and businessTypeName != ''">#{businessTypeName},</if>
            <if test="consignorId != null">#{consignorId},</if>
            <if test="consigneeId != null">#{consigneeId},</if>
            <if test="haulway != null and haulway != ''">#{haulway},</if>
            <if test="mileage != null">#{mileage},</if>
            <if test="vehicleId != null">#{vehicleId},</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">#{vehicleNumber},</if>
            <if test="vehiclePlateColorCode != null and vehiclePlateColorCode != ''">#{vehiclePlateColorCode},</if>
            <if test="driverId != null">#{driverId},</if>
            <if test="driverName != null and driverName != ''">#{driverName},</if>
            <if test="drivingLicense != null and drivingLicense != ''">#{drivingLicense},</if>
            <if test="telephone != null and telephone != ''">#{telephone},</if>
            <if test="actualCarrierId != null">#{actualCarrierId},</if>
            <if test="actualCarrierName != null and actualCarrierName != ''">#{actualCarrierName},</if>
            <if test="actualCarrierContactPhone != null and actualCarrierContactPhone != ''">
                #{actualCarrierContactPhone},
            </if>
            <if test="payeeId != null">#{payeeId},</if>
            <if test="payeeName != null and payeeName != ''">#{payeeName},</if>
            <if test="descriptionOfGoods != null and descriptionOfGoods != ''">#{descriptionOfGoods},</if>
            <if test="weightCube != null and weightCube != ''">#{weightCube},</if>
            <if test="receiveFare != null">#{receiveFare},</if>
            <if test="feePrice != null">#{feePrice},</if>
            <if test="feeAmount != null">#{feeAmount},</if>
            <if test="feeUnit != null">#{feeUnit},</if>
            <if test="statementReceiveFare != null">#{statementReceiveFare},</if>
            <if test="payApplyStatus != null">#{payApplyStatus},</if>
            <if test="totalFare != null">#{totalFare},</if>
            <if test="totalPaidFare != null">#{totalPaidFare},</if>
            <if test="payType != null">#{payType},</if>
            <if test="payFare != null">#{payFare},</if>
            <if test="prepayMoney != null">#{prepayMoney},</if>
            <if test="prepayActualMoney != null">#{prepayActualMoney},</if>
            <if test="prepayUnpaidMoney != null">#{prepayUnpaidMoney},</if>
            <if test="arriveMoney != null">#{arriveMoney},</if>
            <if test="arriveActualMoney != null">#{arriveActualMoney},</if>
            <if test="arriveUnpaidMoney != null">#{arriveUnpaidMoney},</if>
            <if test="receiptMoney != null">#{receiptMoney},</if>
            <if test="receiptActualMoney != null">#{receiptActualMoney},</if>
            <if test="receiptUnpaidMoney != null">#{receiptUnpaidMoney},</if>
            <if test="actualFare != null">#{actualFare},</if>
            <if test="unpaidFare != null">#{unpaidFare},</if>
            <if test="prepayType != null">#{prepayType},</if>
            <if test="oilCardNo != null and oilCardNo != ''">#{oilCardNo},</if>
            <if test="locateType != null">#{locateType},</if>
            <if test="locateStatus != null">#{locateStatus},</if>
            <if test="electronicContractStatus != null">#{electronicContractStatus},</if>
            <if test="settleStatus != null">#{settleStatus},</if>
            <if test="billStatus != null">#{billStatus},</if>
            <if test="status != null">#{status},</if>
            <if test="signTime != null">#{signTime},</if>
            <if test="loadTime != null">#{loadTime},</if>
            <if test="unloadTime != null">#{unloadTime},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="submitBy != null and submitBy != ''">#{submitBy},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="isCompleteTrajectory != null">#{isCompleteTrajectory},</if>
            <if test="driverInfoIsComplete != null">#{driverInfoIsComplete},</if>
            <if test="vehicleInfoIsComplete != null">#{vehicleInfoIsComplete},</if>
            <if test="actualCarrierInfoIsComplete != null">#{actualCarrierInfoIsComplete},</if>
            <if test="infoIsComplete != null">#{infoIsComplete},</if>
            <if test="freightForwarderId != null">#{freightForwarderId},</if>
            <if test="electronicContractState != null">#{electronicContractState},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="waybillPrice != null ">#{waybillPrice},</if>
            <if test="receiptStatus != null ">#{receiptStatus},</if>
            <if test="actualCarrierIdentityCard != null and actualCarrierIdentityCard != ''">
                #{actualCarrierIdentityCard},
            </if>
            <if test="payeeBankCardNo != null and payeeBankCardNo != ''">#{payeeBankCardNo},</if>
            <if test="verifyTrajectoryState != null">#{verifyTrajectoryState},</if>
            <if test="taxFirstUploadState != null">#{taxFirstUploadState},</if>
            <if test="taxFirstResState != null">#{taxFirstResState},</if>
            <if test="taxArriveUploadState != null">#{taxArriveUploadState},</if>
            <if test="taxArriveResState != null">#{taxArriveResState},</if>
            <if test="taxSecondUploadState != null">#{taxSecondUploadState},</if>
            <if test="taxSecondResState != null">#{taxSecondResState},</if>
            <if test="taxThirdUploadState != null">#{taxThirdUploadState},</if>
            <if test="taxThirdResState != null">#{taxThirdResState},</if>
            <if test="taxUploadLastTime != null">#{taxUploadLastTime},</if>
            <if test="taxUploadVerifyLastTime != null">#{taxUploadVerifyLastTime},</if>
            <if test="isTaxUpload != null">#{isTaxUpload},</if>
            <if test="taxUploadFailReason != null and taxUploadFailReason != ''">#{taxUploadFailReason},</if>
            <if test="zeroingMoney != null">#{zeroingMoney},</if>
            <if test="beforeZeroingMoney != null">#{beforeZeroingMoney},</if>
            <if test="deductMoney != null">#{deductMoney},</if>
            <if test="confirmReceiptState != null">#{confirmReceiptState},</if>
            <if test="carCaptainId != null">#{carCaptainId},</if>
            <if test="carCaptainName != null">#{carCaptainName},</if>
            <if test="carCaptainPhone != null">#{carCaptainPhone},</if>
            <if test="carCaptainIdentityCard != null">#{carCaptainIdentityCard},</if>
            <if test="auxiliaryStaffId != null">#{auxiliaryStaffId},</if>
            <if test="auxiliaryStaffName != null">#{auxiliaryStaffName},</if>
            <if test="auxiliaryStaffIdentityCard != null">#{auxiliaryStaffIdentityCard},</if>
            <if test="auxiliaryStaffPhone != null">#{auxiliaryStaffPhone},</if>
            <if test="fareState != null">#{fareState},</if>
            <if test="payeeType != null">#{payeeType},</if>
            <if test="riskResultGrade != null">#{riskResultGrade},</if>
            <if test="transportationAgreementState != null">#{transportationAgreementState},</if>
            <if test="proxyInvoiceState != null">#{proxyInvoiceState},</if>
            <if test="artificialAuditState != null">#{artificialAuditState},</if>
            <if test="payeeIdentityCard != null">#{payeeIdentityCard},</if>
        </trim>
    </insert>
    <insert id="insertWaybills">
        insert IGNORE into waybill
        (id,
        resource,
        shipping_note_number,
        order_create_time,
        make_code_id,
        customer_id,
        customer_name,
        original_document_number,
        bill_date,
        operation_plain_id,
        framework_contract_id,
        framework_contract_code,
        framework_contract_name,
        manifest_no,
        despatch_actual_date_time,
        goods_receipt_date_time,
        business_type_code,
        business_type_name,
        consignor_id,
        consignee_id,
        haulway,
        mileage,
        vehicle_id,
        vehicle_number,
        vehicle_plate_color_code,
        driver_id,
        driver_name,
        driving_license,
        telephone,
        actual_carrier_id,
        actual_carrier_name,
        actual_carrier_contact_phone,
        payee_id,
        payee_name,
        payee_identity_card,
        description_of_goods,
        weight_cube,
        receive_fare,
        fee_price,
        fee_amount,
        fee_unit,
        statement_receive_fare,
        pay_apply_status,
        total_fare,
        total_paid_fare,
        pay_type,
        pay_fare,
        actual_fare,
        unpaid_fare,
        prepay_type,
        prepay_money,
        prepay_actual_money,
        prepay_unpaid_money,
        oil_card_no,
        arrive_money,
        arrive_actual_money,
        arrive_unpaid_money,
        receipt_money,
        receipt_actual_money,
        receipt_unpaid_money,
        locate_type,
        locate_status,
        electronic_contract_status,
        settle_status,
        bill_status,
        status,
        sign_time,
        load_time,
        unload_time,
        pay_time,
        create_by,
        create_time,
        update_by,
        update_time,
        submit_by,
        submit_time,
        return_id,
        unloading_name,
        unloading_phone,
        is_complete_trajectory,
        driver_info_is_complete,
        vehicle_info_is_complete,
        actual_carrier_info_is_complete,
        info_is_complete,
        freight_forwarder_id,
        electronic_contract_state,
        remark,
        receipt_status,
        waybill_price,
        actual_carrier_identity_card,
        payee_bank_card_no,
        verify_trajectory_state,
        tax_first_upload_state,
        tax_first_res_state,
        tax_arrive_upload_state,
        tax_arrive_res_state,
        tax_second_upload_state,
        tax_second_res_state,
        tax_third_upload_state,
        tax_third_res_state,
        tax_upload_last_time,
        is_tax_upload,
        tax_upload_verify_last_time,
        tax_upload_fail_reason,
        zeroing_money,
        before_zeroing_money,
        car_captain_id,
        car_captain_name,
        car_captain_phone,
        car_captain_identity_card,
        auxiliary_staff_id,
        auxiliary_staff_name,
        auxiliary_staff_identity_card,
        auxiliary_staff_phone,
        fare_state,
        deduct_money,
        payee_type,
        confirm_receipt_state,
        risk_result_grade
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.id},
            #{item.resource},
            #{item.shippingNoteNumber},
            <if test="item.orderCreateTime != null">#{item.orderCreateTime},</if>
            <if test="item.orderCreateTime == null">default,</if>
            #{item.makeCodeId},
            #{item.customerId},
            #{item.customerName},
            #{item.originalDocumentNumber},
            #{item.billDate},
            #{item.operationPlainId},
            #{item.frameworkContractId},
            #{item.frameworkContractCode},
            #{item.frameworkContractName},
            #{item.manifestNo},
            <if test="item.despatchActualDateTime != null">#{item.despatchActualDateTime},</if>
            <if test="item.despatchActualDateTime == null">default,</if>
            <if test="item.goodsReceiptDateTime != null">#{item.goodsReceiptDateTime},</if>
            <if test="item.goodsReceiptDateTime == null">default,</if>
            #{item.businessTypeCode},
            #{item.businessTypeName},
            #{item.consignorId},
            #{item.consigneeId},
            #{item.haulway},
            #{item.mileage},
            #{item.vehicleId},
            #{item.vehicleNumber},
            <if test="item.vehiclePlateColorCode != null">#{item.vehiclePlateColorCode},</if>
            <if test="item.vehiclePlateColorCode == null">default,</if>
            #{item.driverId},
            #{item.driverName},
            #{item.drivingLicense},
            #{item.telephone},
            #{item.actualCarrierId},
            #{item.actualCarrierName},
            #{item.actualCarrierContactPhone},
            #{item.payeeId},
            #{item.payeeName},
            #{item.payeeIdentityCard},
            #{item.descriptionOfGoods},
            #{item.weightCube},
            #{item.receiveFare},
            #{item.feePrice},
            #{item.feeAmount},
            #{item.feeUnit},
            #{item.statementReceiveFare},
            #{item.payApplyStatus},
            #{item.totalFare},
            #{item.totalPaidFare},
            #{item.payType},
            #{item.payFare},
            #{item.actualFare},
            #{item.unpaidFare},
            #{item.prepayType},
            #{item.prepayMoney},
            #{item.prepayActualMoney},
            #{item.prepayUnpaidMoney},
            #{item.oilCardNo},
            #{item.arriveMoney},
            #{item.arriveActualMoney},
            #{item.arriveUnpaidMoney},
            #{item.receiptMoney},
            #{item.receiptActualMoney},
            #{item.receiptUnpaidMoney},
            #{item.locateType},
            #{item.locateStatus},
            #{item.electronicContractStatus},
            #{item.settleStatus},
            #{item.billStatus},
            #{item.status},
            <if test="item.signTime != null">#{item.signTime},</if>
            <if test="item.signTime == null">default,</if>
            <if test="item.loadTime != null">#{item.loadTime},</if>
            <if test="item.loadTime == null">default,</if>
            <if test="item.unloadTime != null">#{item.unloadTime},</if>
            <if test="item.unloadTime == null">default,</if>
            <if test="item.payTime != null">#{item.payTime},</if>
            <if test="item.payTime == null">default,</if>
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime},
            #{item.submitBy},
            <if test="item.submitTime != null">#{item.submitTime},</if>
            <if test="item.submitTime == null">default,</if>
            #{item.returnId},
            #{item.unloadingName},
            #{item.unloadingPhone},
            #{item.isCompleteTrajectory},
            #{item.driverInfoIsComplete},
            #{item.vehicleInfoIsComplete},
            #{item.actualCarrierInfoIsComplete},
            #{item.infoIsComplete},
            #{item.freightForwarderId},
            #{item.electronicContractState},
            #{item.remark},
            #{item.receiptStatus},
            #{item.waybillPrice},
            #{item.actualCarrierIdentityCard},
            #{item.payeeBankCardNo},
            #{item.verifyTrajectoryState},
            #{item.taxFirstUploadState},
            #{item.taxFirstResState},
            #{item.taxArriveUploadState},
            #{item.taxArriveResState},
            #{item.taxSecondUploadState},
            #{item.taxSecondResState},
            #{item.taxThirdUploadState},
            #{item.taxThirdResState},
            <if test="item.taxUploadLastTime != null">#{item.taxUploadLastTime},</if>
            <if test="item.taxUploadLastTime == null">default,</if>
            #{item.isTaxUpload},
            <if test="item.taxUploadVerifyLastTime != null">#{item.taxUploadVerifyLastTime},</if>
            <if test="item.taxUploadVerifyLastTime == null">default,</if>
            #{item.taxUploadFailReason},
            #{item.zeroingMoney},
            #{item.beforeZeroingMoney},
            #{item.carCaptainId},
            #{item.carCaptainName},
            #{item.carCaptainPhone},
            #{item.carCaptainIdentityCard},
            #{item.auxiliaryStaffId},
            #{item.auxiliaryStaffName},
            #{item.auxiliaryStaffIdentityCard},
            #{item.auxiliaryStaffPhone},
            #{item.fareState},
            #{item.deductMoney},
            #{item.payeeType}
            <if test="item.confirmReceiptState != null and item.confirmReceiptState != ''">
                ,#{item.confirmReceiptState}
            </if>
            <if test="item.confirmReceiptState == null">,default</if>
            <if test="item.riskResultGrade != null and item.riskResultGrade != ''">
                ,#{item.riskResultGrade}
            </if>
            <if test="item.riskResultGrade == null">,default</if>
        </foreach>

    </insert>

    <update id="updateWaybill" parameterType="com.zly.project.transport.waybill.domain.Waybill">
        update waybill
        <trim prefix="SET" suffixOverrides=",">
            <if test="shippingNoteNumber != null and shippingNoteNumber != ''">
                shipping_note_number = #{shippingNoteNumber},
            </if>
            <if test="orderCreateTime != null">
                order_create_time = #{orderCreateTime},
            </if>
            <if test="makeCodeId != null">
                make_code_id = #{makeCodeId},
            </if>
            <if test="customerId != null">
                customer_id = #{customerId},
            </if>
            <if test="customerName != null and customerName != ''">
                customer_name = #{customerName},
            </if>
            <if test="originalDocumentNumber != null and originalDocumentNumber != ''">
                original_document_number = #{originalDocumentNumber},
            </if>
            <if test="billDate != null">
                bill_date = #{billDate},
            </if>
            <if test="operationPlainId != null">
                operation_plain_id = #{operationPlainId},
            </if>
            <if test="frameworkContractId != null">
                framework_contract_id = #{frameworkContractId},
            </if>
            <if test="frameworkContractCode != null and frameworkContractCode != ''">
                framework_contract_code = #{frameworkContractCode},
            </if>
            <if test="frameworkContractName != null and frameworkContractName != ''">
                framework_contract_name = #{frameworkContractName},
            </if>
            <if test="manifestNo != null">
                manifest_no = #{manifestNo},
            </if>
            <if test="despatchActualDateTime != null">
                despatch_actual_date_time = #{despatchActualDateTime},
            </if>
            <if test="goodsReceiptDateTime != null">
                goods_receipt_date_time = #{goodsReceiptDateTime},
            </if>
            <if test="businessTypeCode != null and businessTypeCode != ''">
                business_type_code = #{businessTypeCode},
            </if>
            <if test="businessTypeName != null and businessTypeName != ''">
                business_type_name = #{businessTypeName},
            </if>
            <if test="consignorId != null">
                consignor_id = #{consignorId},
            </if>
            <if test="consigneeId != null">
                consignee_id = #{consigneeId},
            </if>
            <if test="haulway != null and haulway != ''">
                haulway = #{haulway},
            </if>
            <if test="mileage != null">
                mileage = #{mileage},
            </if>
            <if test="vehicleId != null">
                vehicle_id = #{vehicleId},
            </if>
            <if test="vehicleNumber != null and vehicleNumber != ''">vehicle_number = #{vehicleNumber},</if>
            <if test="driverId != null">driver_id = #{driverId},</if>
            <if test="driverName != null and driverName != ''">driver_name = #{driverName},</if>
            <if test="drivingLicense != null and drivingLicense != ''">driving_license = #{drivingLicense},</if>
            <if test="telephone != null and telephone != ''">telephone = #{telephone},</if>
            <if test="actualCarrierId != null">actual_carrier_id = #{actualCarrierId},</if>
            <if test="actualCarrierName != null and actualCarrierName != ''">actual_carrier_name =
                #{actualCarrierName},
            </if>
            <if test="actualCarrierContactPhone != null and actualCarrierContactPhone != ''">
                actual_carrier_contact_phone = #{actualCarrierContactPhone},
            </if>
            <if test="payeeId != null">payee_id = #{payeeId},</if>
            <if test="payeeName != null and payeeName != ''">payee_name = #{payeeName},</if>
            <if test="descriptionOfGoods != null and descriptionOfGoods != ''">description_of_goods =
                #{descriptionOfGoods},
            </if>
            <if test="weightCube != null and weightCube != ''">weight_cube = #{weightCube},</if>
            <if test="receiveFare != null">receive_fare = #{receiveFare},</if>
            <if test="feePrice != null">fee_price = #{feePrice},</if>
            <if test="feeAmount != null">fee_amount = #{feeAmount},</if>
            <if test="feeUnit != null">fee_unit = #{feeUnit},</if>
            <if test="statementReceiveFare != null">statement_receive_fare = #{statementReceiveFare},</if>
            <if test="payApplyStatus != null">pay_apply_status = #{payApplyStatus},</if>
            <if test="totalFare != null">total_fare = #{totalFare},</if>
            <if test="totalPaidFare != null">total_paid_fare = #{totalPaidFare},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="payFare != null">pay_fare = #{payFare},</if>
            <if test="prepayMoney != null">prepay_money = #{prepayMoney},</if>
            <if test="prepayActualMoney != null">prepay_actual_money = #{prepayActualMoney},</if>
            <if test="prepayUnpaidMoney != null">prepay_unpaid_money = #{prepayUnpaidMoney},</if>
            <if test="arriveMoney != null">arrive_money = #{arriveMoney},</if>
            <if test="arriveActualMoney != null">arrive_actual_money = #{arriveActualMoney},</if>
            <if test="arriveUnpaidMoney != null">arrive_unpaid_money = #{arriveUnpaidMoney},</if>
            <if test="receiptMoney != null">receipt_money = #{receiptMoney},</if>
            <if test="receiptActualMoney != null">receipt_actual_money = #{receiptActualMoney},</if>
            <if test="receiptUnpaidMoney != null">receipt_unpaid_money = #{receiptUnpaidMoney},</if>
            <if test="actualFare != null">actual_fare = #{actualFare},</if>
            <if test="unpaidFare != null">unpaid_fare = #{unpaidFare},</if>
            <if test="prepayType != null">prepay_type = #{prepayType},</if>
            <if test="oilCardNo != null and oilCardNo != ''">oil_card_no = #{oilCardNo},</if>
            <if test="locateType != null">locate_type = #{locateType},</if>
            <if test="locateStatus != null">locate_status = #{locateStatus},</if>
            <if test="electronicContractStatus != null">electronic_contract_status = #{electronicContractStatus},</if>
            <if test="settleStatus != null">settle_status = #{settleStatus},</if>
            <if test="billStatus != null">bill_status = #{billStatus},</if>
            <if test="status != null">status = #{status},</if>
            <if test="signTime != null">sign_time = #{signTime},</if>
            <if test="loadTime != null">load_time = #{loadTime},</if>
            <if test="unloadTime != null">unload_time = #{unloadTime},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="submitBy != null and submitBy != ''">submit_by = #{submitBy},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="isCompleteTrajectory != null">is_complete_trajectory = #{isCompleteTrajectory},</if>
            <if test="driverInfoIsComplete != null">driver_info_is_complete=#{driverInfoIsComplete},</if>
            <if test="vehicleInfoIsComplete != null">vehicle_info_is_complete=#{vehicleInfoIsComplete},</if>
            <if test="actualCarrierInfoIsComplete != null">
                actual_carrier_info_is_complete=#{actualCarrierInfoIsComplete},
            </if>
            <if test="infoIsComplete != null">info_is_complete=#{infoIsComplete},</if>
            <if test="freightForwarderId != null">freight_forwarder_id=#{freightForwarderId},</if>
            <if test="electronicContractState != null">electronic_contract_state=#{electronicContractState},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="receiptStatus != null">receipt_status =#{receiptStatus},</if>
            <if test="actualCarrierIdentityCard != null and actualCarrierIdentityCard != ''">
                actual_carrier_identity_card =#{actualCarrierIdentityCard},
            </if>
            <if test="payeeBankCardNo != null and payeeBankCardNo != ''">payee_bank_card_no =#{payeeBankCardNo},</if>
            <if test="verifyTrajectoryState != null">verify_trajectory_state =#{verifyTrajectoryState},</if>
            <if test="taxFirstUploadState != null">tax_first_upload_state = #{taxFirstUploadState},</if>
            <if test="taxFirstResState != null">tax_first_res_state = #{taxFirstResState},</if>
            <if test="taxArriveUploadState != null">tax_arrive_upload_state = #{taxArriveUploadState},</if>
            <if test="taxArriveResState != null">tax_arrive_res_state = #{taxArriveResState},</if>
            <if test="taxSecondUploadState != null">tax_second_upload_state = #{taxSecondUploadState},</if>
            <if test="taxSecondResState != null">tax_second_res_state = #{taxSecondResState},</if>
            <if test="taxThirdUploadState != null">tax_third_upload_state = #{taxThirdUploadState},</if>
            <if test="taxThirdResState != null">tax_third_res_state = #{taxThirdResState},</if>
            <if test="taxUploadLastTime != null">tax_upload_last_time = #{taxUploadLastTime},</if>
            <if test="taxUploadVerifyLastTime != null">tax_upload_verify_last_time = #{taxUploadVerifyLastTime},</if>
            <if test="isTaxUpload != null">is_tax_upload = #{isTaxUpload},</if>
            <if test="taxUploadFailReason != null">tax_upload_fail_reason =
                #{taxUploadFailReason},
            </if>
            <if test="zeroingMoney != null">zeroing_money = #{zeroingMoney},</if>
            <if test="carCaptainId != null">car_captain_id = #{carCaptainId},</if>
            <if test="carCaptainName != null">car_captain_name = #{carCaptainName},</if>
            <if test="carCaptainPhone != null">car_captain_phone = #{carCaptainPhone},</if>
            <if test="carCaptainIdentityCard != null">car_captain_identity_card = #{carCaptainIdentityCard},</if>
            <if test="auxiliaryStaffId != null">auxiliary_staff_id = #{auxiliaryStaffId},</if>
            <if test="auxiliaryStaffName != null">auxiliary_staff_name = #{auxiliaryStaffName},</if>
            <if test="auxiliaryStaffIdentityCard != null">auxiliary_staff_identity_card =
                #{auxiliaryStaffIdentityCard},
            </if>
            <if test="auxiliaryStaffPhone != null">auxiliary_staff_phone = #{auxiliaryStaffPhone},</if>
            <if test="beforeZeroingMoney != null">before_zeroing_money = #{beforeZeroingMoney},</if>
            <if test="deductMoney != null">deduct_money = #{deductMoney},</if>
            <if test="confirmReceiptState != null">confirm_receipt_state = #{confirmReceiptState},</if>
            <if test="waybillPrice != null">waybill_price = #{waybillPrice},</if>
            <if test="fareState != null">fare_state = #{fareState},</if>
            <if test="payeeType != null">payee_type = #{payeeType},</if>
            <if test="riskResultGrade != null">risk_result_grade = #{riskResultGrade},</if>
            <if test="transportationAgreementState != null">transportation_agreement_state =
                #{transportationAgreementState},
            </if>
            <if test="proxyInvoiceState != null">proxy_invoice_state = #{proxyInvoiceState},</if>
            <if test="artificialAuditState != null">artificial_audit_state = #{artificialAuditState},</if>
            <if test="payeeIdentityCard != null">payee_identity_card = #{payeeIdentityCard},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateWaybillByIdsAndDriverInfo">
        update waybill
        set driver_id = #{driver.id},
        driver_name = #{driver.driverName},
        driving_license = #{driver.drivingLicense},
        telephone = #{driver.telephone},
        update_by = #{nickname},
        update_time = now()
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateWaybillDriverId">
        update waybill
        set driver_id = #{newDriverId}
        where driver_id in
        <foreach item="oldDriverId" collection="oldDriverIds" open="(" separator="," close=")">
            #{oldDriverId}
        </foreach>
    </update>

    <update id="updateWaybillByIdsAndCarrierInfo">
        update waybill
        set actual_carrier_id = #{carrier.id},
        actual_carrier_name = #{carrier.actualCarrierName},
        actual_carrier_contact_phone = #{carrier.contactPhone},
        actual_carrier_identity_card = #{carrier.identityCard},
        update_by = #{nickname},
        update_time = NOW()
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateWaybillByIdsAndVehicleInfo">
        update waybill
        set vehicle_id = #{vehicle.id},
        vehicle_number = #{vehicle.vehicleNumber},
        vehicle_plate_color_code = #{vehicle.vehiclePlateColorCode},
        update_by = #{nickname},
        update_time = NOW()
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateWaybillVehicleId">
        update waybill
        set vehicle_id = #{newVehicleId}
        where vehicle_id in
        <foreach item="oldVehicleId" collection="oldVehicleIds" open="(" separator="," close=")">
            #{oldVehicleIds}
        </foreach>
    </update>
    <update id="updateWaybillCarrierId">
        update waybill
        set actual_carrier_id = #{newCarrierId}
        where actual_carrier_id in
        <foreach item="oldCarrierId" collection="oldCarrierIds" open="(" separator="," close=")">
            #{oldCarrierId}
        </foreach>
    </update>
    <update id="updateWaybillPayeeId">
        update waybill
        set payee_id = #{newPayeeId}
        where payee_id in
        <foreach item="oldPayeeId" collection="oldPayeeIds" open="(" separator="," close=")">
            #{oldPayeeId}
        </foreach>
    </update>
    <update id="updateWaybillByIdsAndPayeeInfo">
        update waybill
        set payee_id = #{payee.id},
        payee_name = #{payee.payeeName},
        payee_bank_card_no = #{payee.bankCardNo},
        payee_identity_card = #{payee.identityCard}
        where id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </update>

    <delete id="deleteWaybillById" parameterType="Long">
        DELETE
        FROM waybill
        WHERE id = #{id}
    </delete>

    <delete id="deleteWaybillSlaveByWaybillId" parameterType="Long">
        DELETE
        FROM waybill_slave
        WHERE id = #{id}
    </delete>
    <select id="selectWaybillByAttribute" resultType="java.lang.Long">
        SELECT id
        FROM waybill
        WHERE resource = 4
          AND status = 3
    </select>
    <select id="selectNotPayApproveWaybillIdsByContractId" parameterType="Long" resultType="java.lang.Long">
        SELECT id
        FROM waybill
        WHERE framework_contract_id = #{contractId}
        <!--&#45;&#45; '付款申请状态(0:待申请 1:申请中 2:部分通过 3:已通过 4:已完结 9:被驳回)' &#45;&#45;-->
        -- '结算状态(0:未结算 1:部分结算 2:已结算)' --
        <!--未结算未申请 或者 预付已付'-->
        AND ((settle_status = 0 AND pay_apply_status IN (0, 9))
        or (settle_status in (0,1) and pay_type = 1 AND pay_apply_status IN (0, 9) and arrive_actual_money = 0))
    </select>
    <select id="selectNotPayWaybillIdsByDriverIdAndCustomerId" resultType="java.lang.Long">
        SELECT id
        FROM waybill
        WHERE driving_license = #{oldDrivingLicense}
        <if test="customerId != null ">
            and customer_id = #{customerId}
        </if>
        <!--&#45;&#45; '付款申请状态(0:待申请 1:申请中 2:部分通过 3:已通过 4:已完结 9:被驳回)' &#45;&#45;-->
        -- '结算状态(0:未结算 1:部分结算 2:已结算)' --
        <!--未结算未申请 或者 预付已付'-->
        AND ((settle_status = 0 AND pay_apply_status IN (0, 9))
        or (settle_status in (0,1) and pay_type = 1 AND pay_apply_status IN (0, 9) and arrive_actual_money = 0))
        AND artificial_audit_state != 3
    </select>

    <select id="selectNotPayWaybillIdsByPayeeIdentityCardAndCustomerId" resultType="java.lang.Long">
        SELECT w.id
        FROM waybill w
        inner join payee_info b on w.payee_id= b.id
        WHERE b.identity_card = #{oldDrivingLicense}
        <if test="customerId != null ">
            and w.customer_id = #{customerId}
        </if>
        <!--&#45;&#45; '付款申请状态(0:待申请 1:申请中 2:部分通过 3:已通过 4:已完结 9:被驳回)' &#45;&#45;-->
        -- '结算状态(0:未结算 1:部分结算 2:已结算)' --
        <!--未结算未申请 或者 预付已付'-->
        AND ((settle_status = 0 AND pay_apply_status IN (0, 9))
        or (settle_status in (0,1) and pay_type = 1 AND pay_apply_status IN (0, 9) and arrive_actual_money = 0))
        AND artificial_audit_state != 3
    </select>

    <select id="selectNotPayWaybillIdsByCarrierIdentityCardAndCustomerId" resultType="java.lang.Long">
        SELECT id
        FROM waybill
        WHERE actual_carrier_identity_card in
        <foreach item="identityCard" collection="identityCards" open="(" separator="," close=")">
            #{identityCard}
        </foreach>
        <if test="customerId != null ">
            and customer_id = #{customerId}
        </if>
        <!--&#45;&#45; '付款申请状态(0:待申请 1:申请中 2:部分通过 3:已通过 4:已完结 9:被驳回)' &#45;&#45;-->
        -- '结算状态(0:未结算 1:部分结算 2:已结算)' --
        <!--未结算未申请 或者 预付已付'-->
        AND ((settle_status = 0 AND pay_apply_status IN (0, 9))
        or (settle_status in (0,1) and pay_type = 1 AND pay_apply_status IN (0, 9) and arrive_actual_money = 0))
    </select>
    <select id="selectNotPayWaybillIdsByVehicleNumberAndCustomerId" resultType="java.lang.Long">
        SELECT id
        FROM waybill
        WHERE vehicle_number = #{vehicleNumber}
        <if test="customerId != null ">
            and customer_id = #{customerId}
        </if>
        <!--&#45;&#45; '付款申请状态(0:待申请 1:申请中 2:部分通过 3:已通过 4:已完结 9:被驳回)' &#45;&#45;-->
        -- '结算状态(0:未结算 1:部分结算 2:已结算)' --
        <!--未结算未申请 或者 预付已付'-->
        AND ((settle_status = 0 AND pay_apply_status IN (0, 9))
        or (settle_status in (0,1) and pay_type = 1 AND pay_apply_status IN (0, 9) and arrive_actual_money = 0))
        AND artificial_audit_state != 3
    </select>

    <select id="selectNotPayWaybillIdsByVehicleIdAndCustomerId" resultType="java.lang.Long">
        SELECT id
        FROM waybill
        WHERE vehicle_id = #{vehicleId}
        <if test="customerId != null ">
            and customer_id = #{customerId}
        </if>
        <!--&#45;&#45; '付款申请状态(0:待申请 1:申请中 2:部分通过 3:已通过 4:已完结 9:被驳回)' &#45;&#45;-->
        -- '结算状态(0:未结算 1:部分结算 2:已结算)' --
        <!--未结算未申请 或者 预付已付'-->
        AND ((settle_status = 0 AND pay_apply_status IN (0, 9))
        or (settle_status in (0,1) and pay_type = 1 AND pay_apply_status IN (0, 9) and arrive_actual_money = 0))
    </select>

    <select id="selectListLimit" resultMap="WaybillResult">
        <include refid="selectWaybillVo"/>
        limit #{offset},#{pageSize}
    </select>
    <select id="countAll" resultType="java.lang.Integer">
        select count(*)
        from waybill
    </select>
    <select id="selectWaybillByIds" resultMap="WaybillResult">
        <include refid="selectWaybillVo"/>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectHandleNonAssociatedTransporterOperation" resultMap="WaybillResult">
        select id, customer_id
        from waybill
        where framework_contract_id in (select id from framework_contract where business_model_id = 168981728998800015)
          and resource in (10, 11)
    </select>
    <select id="selectNotPayWaybillIdsByPayeeIdAndCustomerId" resultType="java.lang.Long">
        SELECT id
        FROM waybill
        WHERE payee_bank_card_no in
        <foreach item="bankCardNo" collection="bankCardNos" open="(" separator="," close=")">
            #{bankCardNo}
        </foreach>
        <if test="customerId != null ">
            and customer_id = #{customerId}
        </if>
        <!--&#45;&#45; '付款申请状态(0:待申请 1:申请中 2:部分通过 3:已通过 4:已完结 9:被驳回)' &#45;&#45;-->
        -- '结算状态(0:未结算 1:部分结算 2:已结算)' --
        <!--未结算未申请 或者 预付已付'-->
        AND ((settle_status = 0 AND pay_apply_status IN (0, 9))
        or (settle_status in (0,1) and pay_type = 1 AND pay_apply_status IN (0, 9) and arrive_actual_money = 0))
        AND artificial_audit_state != 3
    </select>
    <select id="selectShippingNoteNumberList" resultType="java.lang.Long">
        select id
        from waybill where shipping_note_number in
        <foreach item="shippingNoteNumber" collection="waybillNos" open="(" separator="," close=")">
            #{shippingNoteNumber}
        </foreach>
    </select>

    <select id="selectNotPayWaybillIdsByCaptainIdAndCustomerId" resultType="java.lang.Long">
        SELECT id
        FROM waybill
        WHERE car_captain_identity_card = #{oldIdentityCard}
        <if test="customerId != null ">
            and customer_id = #{customerId}
        </if>
        <!--&#45;&#45; '付款申请状态(0:待申请 1:申请中 2:部分通过 3:已通过 4:已完结 9:被驳回)' &#45;&#45;-->
        -- '结算状态(0:未结算 1:部分结算 2:已结算)' --
        <!--未结算未申请 或者 预付已付'-->
        AND ((settle_status = 0 AND pay_apply_status IN (0, 9))
        or (settle_status in (0,1) and pay_type = 1 AND pay_apply_status IN (0, 9) and arrive_actual_money = 0))
    </select>

    <update id="updateWaybillByIdsAndCarCaptainInfo">
        update waybill
        set car_captain_id = #{carCaptainInfo.id},
        car_captain_name = #{carCaptainInfo.carCaptainName},
        car_captain_identity_card = #{carCaptainInfo.identityCard},
        car_captain_phone = #{carCaptainInfo.carCaptainPhone},
        update_by = #{nickname},
        update_time = now()
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectNotPayWaybillIdsByAuxiliaryIdAndCustomerId" resultType="java.lang.Long">
        SELECT id
        FROM waybill
        WHERE auxiliary_staff_identity_card = #{oldIdentityCard}
        <if test="customerId != null ">
            and customer_id = #{customerId}
        </if>
        <!--&#45;&#45; '付款申请状态(0:待申请 1:申请中 2:部分通过 3:已通过 4:已完结 9:被驳回)' &#45;&#45;-->
        -- '结算状态(0:未结算 1:部分结算 2:已结算)' --
        <!--未结算未申请 或者 预付已付'-->
        AND ((settle_status = 0 AND pay_apply_status IN (0, 9))
        or (settle_status in (0,1) and pay_type = 1 AND pay_apply_status IN (0, 9) and arrive_actual_money = 0))
    </select>

    <update id="updateWaybillByIdsAndAuxiliaryInfo">
        update waybill
        set auxiliary_staff_id = #{auxiliaryStaffInfo.id},
        auxiliary_staff_name = #{auxiliaryStaffInfo.auxiliaryStaffName},
        auxiliary_staff_identity_card = #{auxiliaryStaffInfo.identityCard},
        auxiliary_staff_phone = #{auxiliaryStaffInfo.auxiliaryStaffTelephone},
        update_by = #{nickname},
        update_time = now()
        where payee_type != 1 and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateWaybillRiskGradeByWaybillIdsAndGrade">
        update waybill
        set risk_result_grade = #{grade}
        where id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </update>
    <update id="updateWaybillStatementReceiveFareAndBillStatusByWaybillIds">
        update waybill
        set bill_status = #{billStatus},
        statement_receive_fare = case
        <foreach item="item" collection="waybills">
            when id = #{item.id} then #{item.statementReceiveFare}
        </foreach>
        end
        where id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </update>

    <select id="selectWaybillByAuxiliaryIdentityCards" resultMap="WaybillResult">
        <include refid="selectWaybillVo"/>
        where auxiliary_staff_identity_card in
        <foreach item="identityCard" collection="identityCards" open="(" separator="," close=")">
            #{identityCard}
        </foreach>
    </select>

    <select id="selectWaybillByPayeeType" resultMap="WaybillResult">
        select auxiliary_staff_id,
        auxiliary_staff_identity_card,
        auxiliary_staff_name,
        car_captain_id,
        car_captain_identity_card,
        car_captain_name,
        driver_id,
        driving_license,
        driver_name
        from waybill
        where payee_type = #{payeeType}
        and status != -1
        <if test="freightForwarderId != null ">
            and freight_forwarder_id = #{freightForwarderId}
        </if>
    </select>
    <select id="selectWaybillListOld" resultMap="WaybillResult">
        <include refid="selectWaybillVoOld"/>
        <where>
            <if test="freightForwarderId != null">
                and freight_forwarder_id = #{freightForwarderId}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                and status in
                <foreach item="status" collection="statusList" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectWaybillByCaptainIdentityCards" resultMap="WaybillResult">
        <include refid="selectWaybillVo"/>
        where car_captain_identity_card in
        <foreach item="identityCard" collection="identityCards" open="(" separator="," close=")">
            #{identityCard}
        </foreach>
    </select>
    <select id="selectWaybillAllFieldsById" resultMap="WaybillResult">
        select *
        from waybill
        where id = #{id}
    </select>
    <select id="selectWaybillIds" resultType="java.lang.Long">
        <include refid="selectWaybillVo"/>
        <where>
            <if test="shippingNoteNumber != null  and shippingNoteNumber != ''">
                and shipping_note_number = #{shippingNoteNumber}
            </if>
            <if test="makeCodeId != null ">and make_code_id = #{makeCodeId}</if>
            <if test="customerId != null ">and customer_id = #{customerId}</if>
            <if test="frameworkContractId != null ">and framework_contract_id = #{frameworkContractId}</if>
            <if test="frameworkContractIds != null and frameworkContractIds.size() > 0">
                AND frameworkContractId IN
                <foreach collection="frameworkContractIds" item="frameworkContractId" open="(" close=")" separator=",">
                    #{frameworkContractId}
                </foreach>
            </if>
            <if test="consignorId != null ">
                and consignor_id = #{consignorId}
            </if>
            <if test="consigneeId != null ">
                and consignee_id = #{consigneeId}
            </if>
            <if test="vehicleId != null ">
                and vehicle_id = #{vehicleId}
            </if>
            <if test="vehicleNumber != null  and vehicleNumber != ''">
                and vehicle_number = #{vehicleNumber}
            </if>
            <if test="driverId != null ">
                and driver_id = #{driverId}
            </if>
            <if test="driverName != null  and driverName != ''">
                and driver_name like concat('%', #{driverName}, '%')
            </if>
            <if test="drivingLicense != null  and drivingLicense != ''">
                and driving_license = #{drivingLicense}
            </if>
            <if test="telephone != null  and telephone != ''">
                and telephone = #{telephone}
            </if>
            <if test="actualCarrierId != null ">
                and actual_carrier_id = #{actualCarrierId}
            </if>
            <if test="payeeId != null ">
                and payee_id = #{payeeId}
            </if>
            <if test="payApplyStatus != null ">
                and pay_apply_status = #{payApplyStatus}
            </if>
            <if test="payType != null ">
                and pay_type = #{payType}
            </if>
            <if test="settleStatus != null ">
                and settle_status = #{settleStatus}
            </if>
            <if test="billStatus != null ">
                and bill_status = #{billStatus}
            </if>
            <if test="status != null and status != -2">
                and `status` = #{status}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND `status` IN
                <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="freightForwarderId != null">and freight_forwarder_id=#{freightForwarderId}</if>
        </where>
    </select>
    <select id="selectRiskCheckFailWaybillIds" resultType="java.lang.Long">
        select w.id from waybill w
        left join waybill_risk_info risk on w.id = risk.waybill_id
        <where>
            risk.waybill_id is null
            <if test="shippingNoteNumber != null  and shippingNoteNumber != ''">
                and w.shipping_note_number = #{shippingNoteNumber}
            </if>
            <if test="frameworkContractId != null ">and w.framework_contract_id = #{frameworkContractId}</if>
            <if test="frameworkContractIds != null and frameworkContractIds.size() > 0">
                AND w.frameworkContractId IN
                <foreach collection="frameworkContractIds" item="frameworkContractId" open="(" close=")" separator=",">
                    #{frameworkContractId}
                </foreach>
            </if>
            <if test="settleStatus != null ">
                and w.settle_status = #{settleStatus}
            </if>
            <if test="status != null and status != -2">
                and w.status = #{status}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND w.status IN
                <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="freightForwarderId != null">and w.freight_forwarder_id=#{freightForwarderId}</if>
        </where>
    </select>

    <delete id="deleteWaybillByIds">
        delete from waybill where id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </delete>

    <select id="selectShanDaoWaybillByIds" resultMap="WaybillResult">
        select * from waybill
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectConflictWaybillByDriverIdAndTime" resultType="java.lang.String">
        select shipping_note_number
        from waybill
        where id <![CDATA[<>]]> #{waybillId}
          AND driver_id = #{driverId}
          and despatch_actual_date_time <![CDATA[<]]> #{goodsReceiptDateTime}
          and goods_receipt_date_time <![CDATA[>]]> #{despatchActualDateTime}
          and freight_forwarder_id = #{freightForwarderId}
          and status <![CDATA[>]]> 1
    </select>

    <select id="selectConflictWaybillByVehicleIdAndTime" resultType="java.lang.String">
        select shipping_note_number
        from waybill
        where id <![CDATA[<>]]> #{waybillId}
          AND vehicle_id = #{vehicleId}
          and despatch_actual_date_time <![CDATA[<]]> #{goodsReceiptDateTime}
          and goods_receipt_date_time <![CDATA[>]]> #{despatchActualDateTime}
          and freight_forwarder_id = #{freightForwarderId}
          and status <![CDATA[>]]> 1
    </select>

    <delete id="updateWaybillStatusForDeleteByIds">
        update waybill set status = -1 where id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </delete>

    <select id="selectWaybillIdsByQueryWrapper" resultType="java.lang.Long">
        select id from waybill
        where status = 4 and settle_status &lt;2
        <if test="proxyInvoiceState != null">
            and proxy_invoice_state = #{proxyInvoiceState}
        </if>
        <if test="vehicleId != null ">
            and vehicle_id = #{vehicleId}
        </if>
        <if test="vehicleNumber != null  and vehicleNumber != ''">
            and vehicle_number = #{vehicleNumber}
        </if>
        <if test="driverId != null ">
            and driver_id = #{driverId}
        </if>
        <if test="driverName != null  and driverName != ''">
            and driver_name like concat('%', #{driverName}, '%')
        </if>
        <if test="drivingLicense != null  and drivingLicense != ''">
            and driving_license = #{drivingLicense}
        </if>
        <if test="freightForwarderId != null">and freight_forwarder_id=#{freightForwarderId}</if>
        <if test="carCaptainId != null">
            and car_captain_id = #{carCaptainId}
        </if>
        <if test="carCaptainIdentityCard != null and carCaptainIdentityCard != ''">
            and car_captain_identity_card = #{carCaptainIdentityCard}
        </if>
        <if test="auxiliaryStaffId != null">
            and auxiliary_staff_id = #{auxiliaryStaffId}
        </if>

        <if test="auxiliaryStaffIdentityCard != null and auxiliaryStaffIdentityCard != ''">
            and auxiliary_staff_identity_card = #{auxiliaryStaffIdentityCard}
        </if>

        <if test="riskResultGrade != null">
            and risk_result_grade = #{riskResultGrade}
        </if>
        <if test="transportationAgreementState != null">
            and transportation_agreement_state = #{transportationAgreementState}
        </if>
    </select>

    <update id="updateWaybillTransportationAgreementState">
        update waybill set transportation_agreement_state = 2 where id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </update>
    <update id="updateWXOrdersCarrierInfo">
        update waybill
        set actual_carrier_id               = #{id},
            actual_carrier_name             = #{actualCarrierName},
            actual_carrier_identity_card    = #{identityCard},
            actual_carrier_info_is_complete = #{infoIsComplete}
        where resource = 6
          and status = 1
          and actual_carrier_contact_phone = #{contactPhone}
          and actual_carrier_id = 0
    </update>
    <update id="updateFillWxPayeeInfoByPayeeIdentityCard">
        update waybill
        set payee_id           = #{id},
            payee_name         = #{payeeName},
            payee_bank_card_no = #{bankCardNo}
        where resource = 6
          and payee_id = 0
          and payee_identity_card = #{identityCard}
    </update>
    <update id="updateWXWaybill">
        update waybill
        set payee_identity_card = #{identityCard}
        where resource = 6
          and status = 1
          and actual_carrier_contact_phone = #{contactPhone}
    </update>
    <update id="updatePayeeTypeByIds">
        update waybill
        set payee_type = #{payeeType}
        where id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </update>
    <update id="disposeWaybillPayeeIdentityCard">
        UPDATE waybill
        SET payee_identity_card = CASE
                                      WHEN payee_type = 1 THEN driving_license
                                      WHEN payee_type = 2 THEN car_captain_identity_card
                                      WHEN payee_type = 3 THEN auxiliary_staff_identity_card
                                      ELSE payee_identity_card
            END
        where customer_id = #{customerId}
          AND (payee_identity_card IS NULL OR payee_identity_card = '')
    </update>

    <select id="selectWaybillIdsByAuxiliaryStaffIdentityCard" resultType="long">
        select id from waybill
        where status = 4 and settle_status &lt;2
        and freight_forwarder_id=#{freightForwarderId}
        and auxiliary_staff_identity_card in
        <foreach item="identityCard" collection="identityCards" open="(" separator="," close=")">
            #{identityCard}
        </foreach>
    </select>
    <select id="selectNeedChangeFamilyPayeeWaybillIdsByDriver" resultType="java.lang.Long">
        select id from waybill
        where status <![CDATA[>]]> -1
        and settle_status = 0
        and resource = 6
        and pay_apply_status in (0,9)
        and driving_license = #{drivingLicense}
        and payee_type = 1
        and freight_forwarder_id in
        <foreach item="freightForwarderId" collection="freightForwarderIds" open="(" separator="," close=")">
            #{freightForwarderId}
        </foreach>
    </select>
    <select id="selectNeedChangeFamilyPayeeFareByDriver" resultType="com.zly.project.transport.waybill.domain.Waybill">
        select freight_forwarder_id freightForwarderId, sum(pay_fare) payFare
        from waybill
        where status <![CDATA[>=]]> 2
        and settle_status = 0
        and resource = 6
        and pay_apply_status in (0,9)
        and driving_license = #{drivingLicense}
        and freight_forwarder_id in
        <foreach item="freightForwarderId" collection="freightForwarderIds" open="(" separator="," close=")">
            #{freightForwarderId}
        </foreach>
        group by freight_forwarder_id
    </select>

    <select id="selectNeedChangeFamilyPayeeOriginalPayeeFareByDriver"
            resultType="com.zly.project.transport.waybill.domain.Waybill">
        select freight_forwarder_id freightForwarderId, payee_identity_card payeeIdentityCard, sum(pay_fare) payFare
        from waybill
        where status <![CDATA[>=]]> 2
        and settle_status = 0
        and resource = 6
        and pay_apply_status in (0, 9)
        and driving_license = #{drivingLicense}
        and payee_identity_card <![CDATA[<>]]> #{drivingLicense}
        and freight_forwarder_id in
        <foreach item="freightForwarderId" collection="freightForwarderIds" open="(" separator="," close=")">
            #{freightForwarderId}
        </foreach>
        group by freight_forwarder_id, payee_identity_card
    </select>
    <select id="selectUnBindWaybillVerify" resultType="java.lang.Long">
        select id
        from waybill
        where status <![CDATA[>]]> 1
          and settle_status <![CDATA[<]]> 2
          and resource = 6
          and driving_license = #{drivingLicense}
          and payee_type = 4
    </select>
    <select id="selectFillWxPayeeInfoByPayeeIdentityCard" resultType="java.lang.Long">
        SELECT id
        from waybill
        where resource = 6
          and payee_id = 0
          and payee_identity_card = #{payeeIdentityCard}
    </select>
    <select id="selectCustomerInfoByWaybillIds" resultType="java.lang.Long">
        select DISTINCT customer_id
        from waybill
        WHERE id in
        <foreach item="id" collection="waybillIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectUnBindWaybillOrder" resultType="java.lang.Long">
        select id
        from waybill
        where status in (0, 1)
          and settle_status <![CDATA[<]]> 2
          and resource = 6
          and driving_license = #{drivingLicense}
          and payee_type = 4
    </select>

</mapper>
