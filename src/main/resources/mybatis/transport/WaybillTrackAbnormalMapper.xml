<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.transport.waybill.mapper.WaybillTrackAbnormalMapper">

    <resultMap type="WaybillTrackAbnormal" id="WaybillTrackAbnormalResult">
        <result property="id" column="id"/>
        <result property="waybillId" column="waybill_id"/>
        <result property="shippingNoteNumber" column="shipping_note_number"/>
        <result property="vehicleId" column="vehicle_id"/>
        <result property="vehicleNumber" column="vehicle_number"/>
        <result property="driverId" column="driver_id"/>
        <result property="driverName" column="driver_name"/>
        <result property="customerId" column="customer_id"/>
        <result property="customerName" column="customer_name"/>
        <result property="frameworkContractId" column="framework_contract_id"/>
        <result property="frameworkContractName" column="framework_contract_name"/>
        <result property="freightForwarderId" column="freight_forwarder_id"/>
        <result property="waybillCreateBy" column="waybill_create_by"/>
        <result property="waybillCreateTime" column="waybill_create_time"/>
        <result property="loadTime" column="load_time"/>
        <result property="loadLongitude" column="load_longitude"/>
        <result property="loadLatitude" column="load_latitude"/>
        <result property="placeOfLoading" column="place_of_loading"/>
        <result property="firstSuggestLocation" column="first_suggest_location"/>
        <result property="firstReferenceTrack" column="first_reference_track"/>
        <result property="firstTake" column="first_take"/>
        <result property="unloadTime" column="unload_time"/>
        <result property="unloadLongitude" column="unload_longitude"/>
        <result property="unloadLatitude" column="unload_latitude"/>
        <result property="goodsReceiptPlace" column="goods_receipt_place"/>
        <result property="lastSuggestLocation" column="last_suggest_location"/>
        <result property="lastReferenceTrack" column="last_reference_track"/>
        <result property="lastTake" column="last_take"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="verifyResult" column="verify_result"/>
        <result property="verifyDesc" column="verify_desc"/>
        <result property="disposeRecord" column="dispose_record"/>
    </resultMap>

    <sql id="selectWaybillTrackAbnormalVo">
        select id,
               waybill_id,
               shipping_note_number,
               vehicle_id,
               vehicle_number,
               driver_id,
               driver_name,
               customer_id,
               customer_name,
               framework_contract_id,
               framework_contract_name,
               freight_forwarder_id,
               waybill_create_by,
               waybill_create_time,
               load_time,
               load_longitude,
               load_latitude,
               place_of_loading,
               first_suggest_location,
               first_reference_track,
               first_take,
               unload_time,
               unload_longitude,
               unload_latitude,
               goods_receipt_place,
               last_suggest_location,
               last_reference_track,
               last_take,
               create_by,
               create_time,
               status,
               verify_result,
               verify_desc,
               dispose_record
        from waybill_track_abnormal
    </sql>

    <select id="selectWaybillTrackAbnormalList" parameterType="WaybillTrackAbnormal" resultMap="WaybillTrackAbnormalResult">
        <include refid="selectWaybillTrackAbnormalVo"/>
        <where>
            <if test="waybillId != null ">and waybill_id = #{waybillId}</if>
            <if test="shippingNoteNumber != null  and shippingNoteNumber != ''">and shipping_note_number =
                #{shippingNoteNumber}
            </if>
            <if test="vehicleId != null ">and vehicle_id = #{vehicleId}</if>
            <if test="vehicleNumber != null  and vehicleNumber != ''">and vehicle_number = #{vehicleNumber}</if>
            <if test="driverId != null ">and driver_id = #{driverId}</if>
            <if test="driverName != null  and driverName != ''">and driver_name like concat('%', #{driverName}, '%')
            </if>
            <if test="customerId != null ">and customer_id = #{customerId}</if>
            <if test="customerName != null  and customerName != ''">and customer_name like concat('%', #{customerName},
                '%')
            </if>
            <if test="frameworkContractId != null ">and framework_contract_id = #{frameworkContractId}</if>
            <if test="frameworkContractName != null  and frameworkContractName != ''">and framework_contract_name like
                concat('%', #{frameworkContractName}, '%')
            </if>
            <if test="freightForwarderId != null ">and freight_forwarder_id = #{freightForwarderId}</if>
            <if test="waybillCreateBy != null  and waybillCreateBy != ''">and waybill_create_by = #{waybillCreateBy}
            </if>
            <if test="waybillCreateTime != null ">and waybill_create_time = #{waybillCreateTime}</if>
            <if test="loadTime != null ">and load_time = #{loadTime}</if>
            <if test="loadLongitude != null  and loadLongitude != ''">and load_longitude = #{loadLongitude}</if>
            <if test="loadLatitude != null  and loadLatitude != ''">and load_latitude = #{loadLatitude}</if>
            <if test="placeOfLoading != null  and placeOfLoading != ''">and place_of_loading = #{placeOfLoading}</if>
            <if test="firstSuggestLocation != null  and firstSuggestLocation != ''">and first_suggest_location =
                #{firstSuggestLocation}
            </if>
            <if test="firstReferenceTrack != null  and firstReferenceTrack != ''">and first_reference_track =
                #{firstReferenceTrack}
            </if>
            <if test="firstTake != null ">and first_take = #{firstTake}</if>
            <if test="unloadTime != null ">and unload_time = #{unloadTime}</if>
            <if test="unloadLongitude != null  and unloadLongitude != ''">and unload_longitude = #{unloadLongitude}</if>
            <if test="unloadLatitude != null  and unloadLatitude != ''">and unload_latitude = #{unloadLatitude}</if>
            <if test="goodsReceiptPlace != null  and goodsReceiptPlace != ''">and goods_receipt_place =
                #{goodsReceiptPlace}
            </if>
            <if test="lastSuggestLocation != null  and lastSuggestLocation != ''">and last_suggest_location =
                #{lastSuggestLocation}
            </if>
            <if test="lastReferenceTrack != null  and lastReferenceTrack != ''">and last_reference_track =
                #{lastReferenceTrack}
            </if>
            <if test="lastTake != null ">and last_take = #{lastTake}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="verifyResult != null ">and verify_result = #{verifyResult}</if>
            <if test="verifyDesc != null  and verifyDesc != ''">and verify_desc = #{verifyDesc}</if>
        </where>
    </select>

    <select id="selectWaybillTrackAbnormalById" parameterType="Long" resultMap="WaybillTrackAbnormalResult">
        <include refid="selectWaybillTrackAbnormalVo"/>
        where id = #{id}
    </select>

    <insert id="insertWaybillTrackAbnormal" parameterType="WaybillTrackAbnormal">
        insert into waybill_track_abnormal
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="waybillId != null">waybill_id,</if>
            <if test="shippingNoteNumber != null and shippingNoteNumber != ''">shipping_note_number,</if>
            <if test="vehicleId != null">vehicle_id,</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">vehicle_number,</if>
            <if test="driverId != null">driver_id,</if>
            <if test="driverName != null and driverName != ''">driver_name,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="frameworkContractId != null">framework_contract_id,</if>
            <if test="frameworkContractName != null and frameworkContractName != ''">framework_contract_name,</if>
            <if test="freightForwarderId != null">freight_forwarder_id,</if>
            <if test="waybillCreateBy != null and waybillCreateBy != ''">waybill_create_by,</if>
            <if test="waybillCreateTime != null">waybill_create_time,</if>
            <if test="loadTime != null">load_time,</if>
            <if test="loadLongitude != null and loadLongitude != ''">load_longitude,</if>
            <if test="loadLatitude != null and loadLatitude != ''">load_latitude,</if>
            <if test="placeOfLoading != null and placeOfLoading != ''">place_of_loading,</if>
            <if test="firstSuggestLocation != null">first_suggest_location,</if>
            <if test="firstReferenceTrack != null">first_reference_track,</if>
            <if test="firstTake != null">first_take,</if>
            <if test="unloadTime != null">unload_time,</if>
            <if test="unloadLongitude != null and unloadLongitude != ''">unload_longitude,</if>
            <if test="unloadLatitude != null and unloadLatitude != ''">unload_latitude,</if>
            <if test="goodsReceiptPlace != null and goodsReceiptPlace != ''">goods_receipt_place,</if>
            <if test="lastSuggestLocation != null">last_suggest_location,</if>
            <if test="lastReferenceTrack != null">last_reference_track,</if>
            <if test="lastTake != null">last_take,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="status != null">status,</if>
            <if test="verifyResult != null">verify_result,</if>
            <if test="verifyDesc != null and verifyDesc != ''">verify_desc,</if>
            <if test="disposeRecord != null and disposeRecord != ''">dispose_record,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="waybillId != null">#{waybillId},</if>
            <if test="shippingNoteNumber != null and shippingNoteNumber != ''">#{shippingNoteNumber},</if>
            <if test="vehicleId != null">#{vehicleId},</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">#{vehicleNumber},</if>
            <if test="driverId != null">#{driverId},</if>
            <if test="driverName != null and driverName != ''">#{driverName},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="frameworkContractId != null">#{frameworkContractId},</if>
            <if test="frameworkContractName != null and frameworkContractName != ''">#{frameworkContractName},</if>
            <if test="freightForwarderId != null">#{freightForwarderId},</if>
            <if test="waybillCreateBy != null and waybillCreateBy != ''">#{waybillCreateBy},</if>
            <if test="waybillCreateTime != null">#{waybillCreateTime},</if>
            <if test="loadTime != null">#{loadTime},</if>
            <if test="loadLongitude != null and loadLongitude != ''">#{loadLongitude},</if>
            <if test="loadLatitude != null and loadLatitude != ''">#{loadLatitude},</if>
            <if test="placeOfLoading != null and placeOfLoading != ''">#{placeOfLoading},</if>
            <if test="firstSuggestLocation != null">#{firstSuggestLocation},</if>
            <if test="firstReferenceTrack != null">#{firstReferenceTrack},</if>
            <if test="firstTake != null">#{firstTake},</if>
            <if test="unloadTime != null">#{unloadTime},</if>
            <if test="unloadLongitude != null and unloadLongitude != ''">#{unloadLongitude},</if>
            <if test="unloadLatitude != null and unloadLatitude != ''">#{unloadLatitude},</if>
            <if test="goodsReceiptPlace != null and goodsReceiptPlace != ''">#{goodsReceiptPlace},</if>
            <if test="lastSuggestLocation != null">#{lastSuggestLocation},</if>
            <if test="lastReferenceTrack != null">#{lastReferenceTrack},</if>
            <if test="lastTake != null">#{lastTake},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="status != null">#{status},</if>
            <if test="verifyResult != null">#{verifyResult},</if>
            <if test="verifyDesc != null and verifyDesc != ''">#{verifyDesc},</if>
            <if test="disposeRecord != null and disposeRecord != ''">#{disposeRecord},</if>
        </trim>
    </insert>

    <update id="updateWaybillTrackAbnormal" parameterType="WaybillTrackAbnormal">
        update waybill_track_abnormal
        <trim prefix="SET" suffixOverrides=",">
            <if test="waybillId != null">waybill_id = #{waybillId},</if>
            <if test="shippingNoteNumber != null and shippingNoteNumber != ''">shipping_note_number =
                #{shippingNoteNumber},
            </if>
            <if test="vehicleId != null">vehicle_id = #{vehicleId},</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">vehicle_number = #{vehicleNumber},</if>
            <if test="driverId != null">driver_id = #{driverId},</if>
            <if test="driverName != null and driverName != ''">driver_name = #{driverName},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="frameworkContractId != null">framework_contract_id = #{frameworkContractId},</if>
            <if test="frameworkContractName != null and frameworkContractName != ''">framework_contract_name =
                #{frameworkContractName},
            </if>
            <if test="freightForwarderId != null">freight_forwarder_id = #{freightForwarderId},</if>
            <if test="waybillCreateBy != null and waybillCreateBy != ''">waybill_create_by = #{waybillCreateBy},</if>
            <if test="waybillCreateTime != null">waybill_create_time = #{waybillCreateTime},</if>
            <if test="loadTime != null">load_time = #{loadTime},</if>
            <if test="loadLongitude != null and loadLongitude != ''">load_longitude = #{loadLongitude},</if>
            <if test="loadLatitude != null and loadLatitude != ''">load_latitude = #{loadLatitude},</if>
            <if test="placeOfLoading != null and placeOfLoading != ''">place_of_loading = #{placeOfLoading},</if>
            <if test="firstSuggestLocation != null">first_suggest_location = #{firstSuggestLocation},</if>
            <if test="firstReferenceTrack != null">first_reference_track = #{firstReferenceTrack},</if>
            <if test="firstTake != null">first_take = #{firstTake},</if>
            <if test="unloadTime != null">unload_time = #{unloadTime},</if>
            <if test="unloadLongitude != null and unloadLongitude != ''">unload_longitude = #{unloadLongitude},</if>
            <if test="unloadLatitude != null and unloadLatitude != ''">unload_latitude = #{unloadLatitude},</if>
            <if test="goodsReceiptPlace != null and goodsReceiptPlace != ''">goods_receipt_place =
                #{goodsReceiptPlace},
            </if>
            <if test="lastSuggestLocation != null">last_suggest_location = #{lastSuggestLocation},</if>
            <if test="lastReferenceTrack != null">last_reference_track = #{lastReferenceTrack},</if>
            <if test="lastTake != null">last_take = #{lastTake},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="verifyResult != null">verify_result = #{verifyResult},</if>
            <if test="verifyDesc != null">verify_desc = #{verifyDesc},</if>
            <if test="disposeRecord != null">dispose_record = #{disposeRecord},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWaybillTrackAbnormalById" parameterType="Long">
        delete
        from waybill_track_abnormal
        where id = #{id}
    </delete>

    <delete id="deleteWaybillTrackAbnormalByIds" parameterType="String">
        delete from waybill_track_abnormal where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectList" resultMap="WaybillTrackAbnormalResult">
        select a.id,a.waybill_id,a.shipping_note_number,a.verify_desc,a.framework_contract_name,a.customer_name,a.driver_name,a.vehicle_number,a.create_time,a.waybill_create_time,a.dispose_record
        from waybill_track_abnormal a
        join waybill b on a.waybill_id = b.id
        where
        <choose>
            <when test="status == 0">
                a.status = 0 and a.verify_result = 0
            </when>
            <otherwise>
                a.status = #{status}
            </otherwise>
        </choose>
        and b.status <![CDATA[<>]]> -1
        <if test="freightForwarderId != null">and a.freight_forwarder_id = #{freightForwarderId}</if>
        <if test="lastCustomerId != null">and a.customer_id = #{lastCustomerId}</if>
        <if test="customerId != null">and b.customer_id = #{customerId}</if>
        <if test="contractIds != null and contractIds.size() > 0">
            and a.framework_contract_id in
            <foreach collection="contractIds" item="contractId" open="(" close=")" separator=",">
                #{contractId}
            </foreach>
        </if>
        <if test="frameworkContractId != null">and a.framework_contract_id = #{frameworkContractId}</if>
        <if test="createTimeStart != null">and a.waybill_create_time &gt;=
            #{createTimeStart}
        </if>
        <if test="createTimeEnd != null">and a.waybill_create_time &lt;= #{createTimeEnd}</if>
        <if test="verifyResult != null">and a.verify_result = #{verifyResult}</if>
        <if test="shippingNoteNumber != null">and a.shipping_note_number = #{shippingNoteNumber}</if>
        order by a.create_time desc
    </select>

    <select id="projectAnalysis" resultType="com.zly.project.transport.waybill.domain.res.WaybillTrackAbnormalRes">
        SELECT w.customer_name AS customerName,
        w.framework_contract_name AS frameworkContractName,
        COUNT(w.id) AS totalWaybills,
        SUM(CASE WHEN w.verify_trajectory_state > 0 THEN 1 ELSE 0 END) AS verifiedWaybills,
        COUNT(wta.id) AS accumulatedAbnormalities,
        SUM(CASE WHEN wta.status = 1 THEN 1 ELSE 0 END) AS currentAbnormalities,
        SUM(CASE WHEN wta.status = 4 THEN 1 ELSE 0 END) AS noNeedDisposeWaybills,
		SUM(CASE WHEN wta.verify_result = 4 THEN 1 ELSE 0 END) AS noExists,
		SUM(CASE WHEN wta.verify_result IN (1,2,3) AND INSTR(wta.verify_desc, '无轨迹') > 0 THEN 1 ELSE 0 END) AS noTracks
        FROM waybill w LEFT JOIN waybill_track_abnormal wta ON wta.waybill_id = w.id
        <where>
            w.status IN (4,5) AND w.freight_forwarder_id = #{freightForwarderId}
            AND w.create_time BETWEEN #{createTimeStart} AND #{createTimeEnd}
            <if test="frameworkContractId != null">AND w.framework_contract_id = #{frameworkContractId}</if>
            <if test="customerId != null">AND w.customer_id = #{customerId}</if>
        </where>
        GROUP BY w.customer_id,w.framework_contract_id
        ORDER BY w.customer_id,totalWaybills DESC,w.framework_contract_id
    </select>
    <select id="projectAnalysisSum" resultType="com.zly.project.transport.waybill.domain.res.WaybillTrackAbnormalRes">
        SELECT w.customer_name AS customerName,
        w.framework_contract_name AS frameworkContractName,
        COUNT(w.id) AS totalWaybills,
        SUM(CASE WHEN w.verify_trajectory_state > 0 THEN 1 ELSE 0 END) AS verifiedWaybills,
        COUNT(wta.id) AS accumulatedAbnormalities,
        SUM(CASE WHEN wta.status = 1 THEN 1 ELSE 0 END) AS currentAbnormalities,
        SUM(CASE WHEN wta.status = 4 THEN 1 ELSE 0 END) AS noNeedDisposeWaybills,
		SUM(CASE WHEN wta.verify_result = 4 THEN 1 ELSE 0 END) AS noExists,
		SUM(CASE WHEN wta.verify_result IN (1,2,3) AND INSTR(wta.verify_desc, '无轨迹') > 0 THEN 1 ELSE 0 END) AS noTracks
        FROM waybill w LEFT JOIN waybill_track_abnormal wta ON wta.waybill_id = w.id
        <where>
            w.status IN (4,5) AND w.freight_forwarder_id = #{freightForwarderId}
            AND w.create_time BETWEEN #{createTimeStart} AND #{createTimeEnd}
            <if test="frameworkContractId != null">AND w.framework_contract_id = #{frameworkContractId}</if>
            <if test="customerId != null">AND w.customer_id = #{customerId}</if>
        </where>
    </select>

    <update id="delByWaybillId">
        update waybill_track_abnormal
        set status = 0
        where waybill_id = #{waybillId}
    </update>

    <update id="manual">
        update waybill_track_abnormal set status = #{status},dispose_record = CONCAT(COALESCE(dispose_record, ''), #{disposeRecord}) where waybill_id in
        <foreach collection="waybillIds" item="waybillId" open="(" close=")" separator=",">
            #{waybillId}
        </foreach>
        and status = 1
    </update>

    <select id="trajectoryVerificationCount" resultType="trajectoryVerificationCount">
        SELECT ifnull(COUNT(waybill_id), 0)                                                          AS verifyCount,
               ifnull(SUM(CASE WHEN verify_result != 0 and verify_result != 4 THEN 1 ELSE 0 END), 0) AS abnormalTrajectoryCount,
               ifnull(SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END), 0)                                AS manualCorrectionCount,
               ifnull(SUM(CASE WHEN verify_result = 4 THEN 1 ELSE 0 END), 0)                         AS vehicleCount
        FROM waybill_track_abnormal
        WHERE freight_forwarder_id = #{freightForwarderId}
          AND create_time BETWEEN #{startTime} AND #{endTime}
    </select>
</mapper>
