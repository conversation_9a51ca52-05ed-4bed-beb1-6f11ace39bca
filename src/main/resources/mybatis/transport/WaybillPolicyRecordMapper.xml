<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.transport.waybill.mapper.WaybillPolicyRecordMapper">
    <resultMap type="WaybillPolicyRecord" id="WaybillPolicyRecordResult">
        <result property="id" column="id"/>
        <result property="waybillId" column="waybill_id"/>
        <result property="waybillCode" column="waybill_code"/>
        <result property="waybillType" column="waybill_type"/>
        <result property="transMoney" column="trans_money"/>
        <result property="premium" column="premium"/>
        <result property="policyFare" column="policy_fare"/>
        <result property="insuranceIntermediary" column="insurance_intermediary"/>
        <result property="policyStatus" column="policy_status"/>
        <result property="policyNumber" column="policy_number"/>
        <result property="waybillJson" column="waybill_json"/>
        <result property="policyRequestJson" column="policy_request_json"/>
        <result property="policyResponseJson" column="policy_response_json"/>
        <result property="policyResult" column="policy_result"/>
        <result property="policyDownloadUrl" column="policy_download_url"/>
        <result property="policyCheckUrl" column="policy_check_url"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectWaybillPolicyRecordVo">
        SELECT id,
               waybill_id,
               waybill_code,
               waybill_type,
               trans_money,
               premium,
               policy_fare,
               insurance_intermediary,
               policy_status,
               policy_number,
               waybill_json,
               policy_request_json,
               policy_response_json,
               policy_result,
               policy_download_url,
               policy_check_url,
               create_by,
               create_time,
               update_by,
               update_time
        FROM waybill_policy_record
    </sql>

    <select id="selectWaybillPolicyRecordList" parameterType="WaybillPolicyRecord"
            resultMap="WaybillPolicyRecordResult">
        <include refid="selectWaybillPolicyRecordVo"/>
        <where>
            <if test="waybillId != null ">
                and waybill_id = #{waybillId}
            </if>
            <if test="waybillCode != null  and waybillCode != ''">
                and waybill_code = #{waybillCode}
            </if>
            <if test="waybillType != null ">
                and waybill_type = #{waybillType}
            </if>
            <if test="transMoney != null ">
                and trans_money = #{transMoney}
            </if>
            <if test="premium != null ">
                and premium = #{premium}
            </if>
            <if test="policyFare != null ">
                and policy_fare = #{policyFare}
            </if>
            <if test="insuranceIntermediary != null ">
                and insurance_intermediary = #{insuranceIntermediary}
            </if>
            <if test="policyStatus != null ">
                and policy_status = #{policyStatus}
            </if>
            <if test="policyNumber != null  and policyNumber != ''">
                and policy_number = #{policyNumber}
            </if>
            <if test="waybillJson != null  and waybillJson != ''">
                and waybill_json = #{waybillJson}
            </if>
            <if test="policyRequestJson != null  and policyRequestJson != ''">
                and policy_request_json = #{policyRequestJson}
            </if>
            <if test="policyResponseJson != null  and policyResponseJson != ''">
                and policy_response_json = #{policyResponseJson}
            </if>
            <if test="policyResult != null  and policyResult != ''">
                and policy_result = #{policyResult}
            </if>
            <if test="policyDownloadUrl != null  and policyDownloadUrl != ''">
                and policy_download_url = #{policyDownloadUrl}
            </if>
            <if test="policyCheckUrl != null  and policyCheckUrl != ''">
                and policy_check_url = #{policyCheckUrl}
            </if>
        </where>
    </select>

    <select id="selectWaybillPolicyRecordById" parameterType="Long" resultMap="WaybillPolicyRecordResult">
        <include refid="selectWaybillPolicyRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertWaybillPolicyRecord" parameterType="WaybillPolicyRecord">
        insert into waybill_policy_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="waybillId != null">
                waybill_id,
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                waybill_code,
            </if>
            <if test="waybillType != null">
                waybill_type,
            </if>
            <if test="transMoney != null">
                trans_money,
            </if>
            <if test="premium != null">
                premium,
            </if>
            <if test="policyFare != null">
                policy_fare,
            </if>
            <if test="insuranceIntermediary != null">
                insurance_intermediary,
            </if>
            <if test="policyStatus != null">
                policy_status,
            </if>
            <if test="policyNumber != null and policyNumber != ''">
                policy_number,
            </if>
            <if test="waybillJson != null and waybillJson != ''">
                waybill_json,
            </if>
            <if test="policyRequestJson != null and policyRequestJson != ''">
                policy_request_json,
            </if>
            <if test="policyResponseJson != null and policyResponseJson != ''">
                policy_response_json,
            </if>
            <if test="policyResult != null and policyResult != ''">
                policy_result,
            </if>
            <if test="policyDownloadUrl != null and policyDownloadUrl != ''">
                policy_download_url,
            </if>
            <if test="policyCheckUrl != null and policyCheckUrl != ''">
                policy_check_url,
            </if>
            <if test="createBy != null and createBy != ''">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="waybillId != null">
                #{waybillId},
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                #{waybillCode},
            </if>
            <if test="waybillType != null">
                #{waybillType},
            </if>
            <if test="transMoney != null">
                #{transMoney},
            </if>
            <if test="premium != null">
                #{premium},
            </if>
            <if test="policyFare != null">
                #{policyFare},
            </if>
            <if test="insuranceIntermediary != null">
                #{insuranceIntermediary},
            </if>
            <if test="policyStatus != null">
                #{policyStatus},
            </if>
            <if test="policyNumber != null and policyNumber != ''">
                #{policyNumber},
            </if>
            <if test="waybillJson != null and waybillJson != ''">
                #{waybillJson},
            </if>
            <if test="policyRequestJson != null and policyRequestJson != ''">
                #{policyRequestJson},
            </if>
            <if test="policyResponseJson != null and policyResponseJson != ''">
                #{policyResponseJson},
            </if>
            <if test="policyResult != null and policyResult != ''">
                #{policyResult},
            </if>
            <if test="policyDownloadUrl != null and policyDownloadUrl != ''">
                #{policyDownloadUrl},
            </if>
            <if test="policyCheckUrl != null and policyCheckUrl != ''">
                #{policyCheckUrl},
            </if>
            <if test="createBy != null and createBy != ''">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null and updateBy != ''">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>
    <insert id="insertWaybillPolicyRecords">
        insert IGNORE into waybill_policy_record
        (id,
        waybill_id,
        waybill_code,
        waybill_type,
        trans_money,
        premium,
        policy_fare,
        insurance_intermediary,
        policy_status,
        policy_number,
        waybill_json,
        policy_request_json,
        policy_response_json,
        policy_result,
        policy_download_url,
        policy_check_url,
        create_by,
        create_time,
        update_by,
        update_time
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.id},
            #{item.waybillId},
            #{item.waybillCode},
            #{item.waybillType},
            #{item.transMoney},
            #{item.premium},
            #{item.policyFare},
            #{item.insuranceIntermediary},
            #{item.policyStatus},
            #{item.policyNumber},
            #{item.waybillJson},
            #{item.policyRequestJson},
            #{item.policyResponseJson},
            #{item.policyResult},
            #{item.policyDownloadUrl},
            #{item.policyCheckUrl},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime}
        </foreach>

    </insert>

    <update id="updateWaybillPolicyRecord" parameterType="WaybillPolicyRecord">
        update waybill_policy_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="waybillId != null">
                waybill_id = #{waybillId},
            </if>
            <if test="waybillCode != null and waybillCode != ''">
                waybill_code = #{waybillCode},
            </if>
            <if test="waybillType != null">
                waybill_type = #{waybillType},
            </if>
            <if test="transMoney != null">
                trans_money = #{transMoney},
            </if>
            <if test="premium != null">
                premium = #{premium},
            </if>
            <if test="policyFare != null">
                policy_fare = #{policyFare},
            </if>
            <if test="insuranceIntermediary != null">
                insurance_intermediary = #{insuranceIntermediary},
            </if>
            <if test="policyStatus != null">
                policy_status = #{policyStatus},
            </if>
            <if test="policyNumber != null and policyNumber != ''">
                policy_number = #{policyNumber},
            </if>
            <if test="waybillJson != null and waybillJson != ''">
                waybill_json = #{waybillJson},
            </if>
            <if test="policyRequestJson != null and policyRequestJson != ''">
                policy_request_json = #{policyRequestJson},
            </if>
            <if test="policyResponseJson != null and policyResponseJson != ''">
                policy_response_json = #{policyResponseJson},
            </if>
            <if test="policyResult != null and policyResult != ''">
                policy_result = #{policyResult},
            </if>
            <if test="policyDownloadUrl != null and policyDownloadUrl != ''">
                policy_download_url = #{policyDownloadUrl},
            </if>
            <if test="policyCheckUrl != null and policyCheckUrl != ''">
                policy_check_url = #{policyCheckUrl},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWaybillPolicyRecordById" parameterType="Long">
        DELETE
        FROM waybill_policy_record
        WHERE id = #{id}
    </delete>

    <delete id="deleteWaybillPolicyRecordByIds" parameterType="String">
        delete from waybill_policy_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="queryPolicyList" resultType="com.zly.project.settlement.domain.CustomerServicesFlowEx">
        SELECT
        ifnull(SUM(a.policy_fare),0) as money,
        w.framework_contract_name as projectName,
        COUNT(1) as count,
        w.framework_contract_id as contractId,
        w.customer_name as customerName
        FROM
        (
        SELECT
        *
        FROM
        `waybill_policy_record`
        WHERE
        insurance_intermediary = 3
        and policy_status in (5,7)
        ) a
        LEFT JOIN waybill w ON a.waybill_id = w.id
        where 1=1
        <if test="contractId != null ">
            and w.framework_contract_id =  #{contractId}
        </if>
        <if test="customerId != null ">
            and w.customer_id = #{customerId}
        </if>
        GROUP BY
        w.framework_contract_id
    </select>

    <select id="queryPolicyFlow" resultType="com.zly.project.settlement.domain.CustomerServicesFlowEx">
        SELECT
        a.policy_number as payCode,
        w.customer_name as customerName,
        w.framework_contract_name as projectName,
        w.shipping_note_number as waybillCode,
        a.policy_fare as money,
        a.policy_status as state,
        a.create_time as createTime
        FROM
        (
        SELECT
        *
        FROM
        `waybill_policy_record`
        WHERE
        insurance_intermediary = 3
        ) a
        LEFT JOIN waybill w ON a.waybill_id = w.id
        where 1=1
        <if test="contractId != null ">
            and w.framework_contract_id =  #{contractId}
        </if>
        <if test="customerId != null ">
            and w.customer_id = #{customerId}
        </if>
        <if test="waybillCode != null and waybillCode != ''">
            and w.shipping_note_number = #{waybillCode}
        </if>
        <if test="state != null ">
            and a.policy_status = #{state}
        </if>
        <if test="createTimeStart != null ">
            and a.create_time &gt; #{createTimeStart}
        </if>
        <if test="createTimeEnd != null ">
            and a.create_time &lt; #{createTimeEnd}
        </if>
    </select>

    <select id="queryPolicySum" resultType="java.math.BigDecimal">
        SELECT
            ifnull(sum(a.policy_fare),0)
        FROM
        (
        SELECT
        *
        FROM
        `waybill_policy_record`
        WHERE
        insurance_intermediary = 3
        ) a
        LEFT JOIN waybill w ON a.waybill_id = w.id
        where 1=1
        <if test="contractId != null ">
            and w.framework_contract_id =  #{contractId}
        </if>
        <if test="customerId != null ">
            and w.customer_id = #{customerId}
        </if>
        <if test="waybillCode != null and waybillCode != ''">
            and w.shipping_note_number = #{waybillCode}
        </if>
        <if test="state != null ">
            and a.policy_status = #{state}
        </if>
        <if test="state == null ">
            and a.policy_status in (5,7)
        </if>
        <if test="createTimeStart != null ">
            and a.create_time &gt; #{createTimeStart}
        </if>
        <if test="createTimeEnd != null ">
            and a.create_time &lt; #{createTimeEnd}
        </if>
    </select>
    <select id="selectWaybillPolicyRecordByWaybillIds" resultMap="WaybillPolicyRecordResult">
        <include refid="selectWaybillPolicyRecordVo"/>
        where waybill_id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </select>

    <delete id="deleteWaybillPolicyRecordByWaybillIds">
        delete from waybill_policy_record where waybill_id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </delete>
</mapper>
