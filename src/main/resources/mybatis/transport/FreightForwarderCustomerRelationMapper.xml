<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.transport.waybill.mapper.FreightForwarderCustomerRelationMapper">

    <resultMap type="FreightForwarderCustomerRelation" id="FreightForwarderCustomerRelationResult">
        <result property="customerId"    column="customer_id"    />
        <result property="freightForwarderId"    column="freight_forwarder_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectFreightForwarderCustomerRelationVo">
        select customer_id, freight_forwarder_id, create_by, create_time, update_by, update_time from freight_forwarder_customer_relation
    </sql>

    <select id="selectFreightForwarderCustomerRelationList" parameterType="FreightForwarderCustomerRelation" resultMap="FreightForwarderCustomerRelationResult">
        <include refid="selectFreightForwarderCustomerRelationVo"/>
        <where>
            <if test="customerId != null ">and customer_id = #{customerId}</if>
            <if test="freightForwarderId != null ">and freight_forwarder_id = #{freightForwarderId}</if>
        </where>
    </select>

    <select id="selectFreightForwarderCustomerRelationByCustomerId" parameterType="Long" resultMap="FreightForwarderCustomerRelationResult">
        <include refid="selectFreightForwarderCustomerRelationVo"/>
        where customer_id = #{customerId}
    </select>

    <select id="selectCustomerIdsByForwarderId" resultType="java.lang.Long">
        select customer_id from freight_forwarder_customer_relation where freight_forwarder_id = #{forwarderId}
    </select>

    <insert id="insertFreightForwarderCustomerRelation" parameterType="FreightForwarderCustomerRelation">
        insert IGNORE into freight_forwarder_customer_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="freightForwarderId != null">freight_forwarder_id,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="freightForwarderId != null">#{freightForwarderId},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateFreightForwarderCustomerRelation" parameterType="FreightForwarderCustomerRelation">
        update freight_forwarder_customer_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="freightForwarderId != null">freight_forwarder_id = #{freightForwarderId},</if>
        </trim>
        where customer_id = #{customerId}
    </update>

    <delete id="deleteFreightForwarderCustomerRelationByCustomerId" parameterType="Long">
        delete from freight_forwarder_customer_relation where customer_id = #{customerId}
    </delete>

    <delete id="deleteFreightForwarderCustomerRelationByCustomerIds" parameterType="String">
        delete from freight_forwarder_customer_relation where customer_id in
        <foreach item="customerId" collection="array" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </delete>

    <update id="mergeShippers">
        update freight_forwarder_customer_relation set customer_id = #{toId} where customer_id = #{fromId}
    </update>

    <select id="queryRepeatInfo" resultType="java.lang.Long">
        SELECT
            a.freight_forwarder_id
        FROM
            freight_forwarder_customer_relation a
                LEFT JOIN freight_forwarder_info b ON a.freight_forwarder_id = b.id
        WHERE
                b.id IN (
                SELECT
                    b.id
                FROM
                    freight_forwarder_customer_relation a
                        LEFT JOIN freight_forwarder_info b ON a.freight_forwarder_id = b.id
                WHERE
                    a.customer_id = #{fromId} or a.customer_id = #{toId}
                GROUP BY
                    b.id
                HAVING
                    COUNT(1) > 1
            )
          AND a.customer_id = #{fromId}
    </select>

    <delete id="delRepeatInfo">
        delete
        from freight_forwarder_customer_relation
        where customer_id = #{fromId} and freight_forwarder_id in
        <foreach collection="delIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <select id="selectForwarderInfoByCustomerId" resultType="freightForwarderInfo">
        SELECT b.*
        FROM freight_forwarder_customer_relation a
                 LEFT JOIN freight_forwarder_info b ON a.freight_forwarder_id = b.id
        WHERE a.customer_id = #{shipperId}
        <if test="freightForwarderIdList != null and freightForwarderIdList.size() > 0">
            and a.freight_forwarder_id not in
            <foreach collection="freightForwarderIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectFreightForwarderCustomerRelationListByMap"
            resultMap="FreightForwarderCustomerRelationResult">
        select ffcr.customer_id,
        ffcr.freight_forwarder_id,
        ffcr.create_by,
        ffcr.create_time,
        ffcr.update_by,
        ffcr.update_time
        from freight_forwarder_customer_relation ffcr
        left join customer_info ci on ffcr.customer_id = ci.id
        where ci.state = 0
        <if test="customerId != null ">and ffcr.customer_id = #{customerId}</if>
        <if test="freightForwarderId != null ">and ffcr.freight_forwarder_id = #{freightForwarderId}</if>
        <if test="creditCode != null ">and ci.credit_code = #{creditCode}</if>
    </select>
    <select id="selectByCustomerIdsAndForwarderId" resultMap="FreightForwarderCustomerRelationResult">
        <include refid="selectFreightForwarderCustomerRelationVo"/>
        where freight_forwarder_id = #{forwarderId}
        AND customer_id in
        <foreach item="customerId" collection="customerIds" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </select>
</mapper>
