<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.transport.waybill.mapper.WaybillSlaveMapper">

    <resultMap type="com.zly.project.transport.waybill.domain.WaybillSlave" id="WaybillSlaveResult">
        <result property="id" column="id"/>
        <result property="loadingWeight" column="loading_weight"/>
        <result property="loadingCube" column="loading_cube"/>
        <result property="unloadWeight" column="unload_weight"/>
        <result property="unloadCube" column="unload_cube"/>
        <result property="policyType" column="policy_type"/>
        <result property="policyStatus" column="policy_status"/>
        <result property="policyFare" column="policy_fare"/>
        <result property="policyNumber" column="policy_number"/>
        <result property="insuranceCompanyCode" column="insurance_company_code"/>
        <result property="uploginkDesc" column="uplogink_desc"/>
        <result property="upetcStatus" column="upetc_status"/>
        <result property="upetcTime" column="upetc_time"/>
        <result property="upetcDesc" column="upetc_desc"/>
        <result property="uploginkStatus" column="uplogink_status"/>
        <result property="uploginkTime" column="uplogink_time"/>
        <result property="remark" column="remark"/>
        <result property="financingAuditStatus" column="financing_audit_status"/>
        <result property="financingAuditTime" column="financing_audit_time"/>
        <result property="financingAuditRemark" column="financing_audit_remark"/>
        <result property="pushStatus" column="push_status"/>
        <result property="pushTime" column="push_time"/>
        <result property="financingStatus" column="financing_status"/>
        <result property="upstreamStatementStatus" column="upstream_statement_status"/>
        <result property="upstreamStatementTime" column="upstream_statement_time"/>
        <result property="agreementGenerateStatus" column="agreement_generate_status"/>
        <result property="agreementGenerateTime" column="agreement_generate_time"/>
        <result property="agreementGenerateRemark" column="agreement_generate_remark"/>
        <result property="checkState" column="check_state"/>
        <result property="electronicContractState" column="electronic_contract_state"/>
        <result property="electronicContractUrl" column="electronic_contract_url"/>
        <result property="electronicContractCode" column="electronic_contract_code"/>
        <result property="overruleTime" column="overrule_time"/>
        <result property="shudaitongFinancingStatus" column="shudaitong_financing_status"/>
        <result property="vehicleDetail" column="vehicle_detail"/>
        <result property="driverDetail" column="driver_detail"/>
        <result property="payeeDetail" column="payee_detail"/>
        <result property="carrierDetail" column="carrier_detail"/>
        <result property="makeCodeRuleId" column="make_code_rule_id"/>
        <result property="zeroingRule" column="zeroing_rule"/>
        <result property="makeCodeRuleDetailed" column="make_code_rule_detailed"/>
        <result property="receiveFareCalculationType" column="receive_fare_calculation_type"/>
        <result property="waybillPaymentType" column="waybill_payment_type"/>
        <result property="carCaptainDetail" column="car_captain_detail"/>
        <result property="auxiliaryStaffDetail" column="auxiliary_staff_detail"/>
        <result property="driverUpdateReceiptState" column="driver_update_receipt_state"/>
        <result property="transportationProtocolCode" column="transportation_protocol_code"/>
        <result property="etcTaxAmount" column="etc_tax_amount"/>
        <result property="upetcInvoiceStatus" column="upetc_invoice_status"/>
        <result property="loadClickTime" column="load_click_time"/>
        <result property="unloadClickTime" column="unload_click_time"/>
    </resultMap>

    <sql id="selectWaybillSlaveVo">
        SELECT id,
               loading_weight,
               loading_cube,
               unload_weight,
               unload_cube,
               policy_type,
               policy_status,
               policy_fare,
               policy_number,
               insurance_company_code,
               uplogink_desc,
               upetc_status,
               upetc_time,
               upetc_desc,
               uplogink_status,
               uplogink_time,
               remark,
               financing_audit_status,
               financing_audit_time,
               financing_audit_remark,
               push_status,
               push_time,
               financing_status,
               upstream_statement_status,
               upstream_statement_time,
               check_state,
               agreement_generate_status,
               agreement_generate_time,
               agreement_generate_remark,
               electronic_contract_state,
               electronic_contract_url,
               electronic_contract_code,
               overrule_time,
               shudaitong_financing_status,
               vehicle_detail,
               driver_detail,
               payee_detail,
               carrier_detail,
               make_code_rule_id,
               zeroing_rule,
               make_code_rule_detailed,
               receive_fare_calculation_type,
               car_captain_detail,
               auxiliary_staff_detail,
               waybill_payment_type,
               driver_update_receipt_state,
               transportation_protocol_code,
               etc_tax_amount,
               upetc_invoice_status,
               load_click_time,
               unload_click_time
        FROM waybill_slave
    </sql>

    <sql id="selectWaybillSlaveOldVo">
        SELECT id,
               loading_weight,
               loading_cube,
               unload_weight,
               unload_cube,
               policy_type,
               policy_status,
               policy_fare,
               policy_number,
               insurance_company_code,
               uplogink_desc,
               upetc_status,
               upetc_time,
               upetc_desc,
               uplogink_status,
               uplogink_time,
               remark,
               financing_audit_status,
               financing_audit_time,
               financing_audit_remark,
               push_status,
               push_time,
               financing_status,
               upstream_statement_status,
               upstream_statement_time,
               check_state,
               agreement_generate_status,
               agreement_generate_time,
               agreement_generate_remark,
               electronic_contract_state,
               electronic_contract_url,
               electronic_contract_code,
               overrule_time,
               shudaitong_financing_status,
               vehicle_detail,
               driver_detail,
               payee_detail,
               carrier_detail,
               make_code_rule_id,
               zeroing_rule,
               make_code_rule_detailed,
               receive_fare_calculation_type,
               waybill_payment_type,
               driver_update_receipt_state,
               transportation_protocol_code,
               etc_tax_amount,
               upetc_invoice_status,
               load_click_time,
               unload_click_time
        FROM waybill_slave
    </sql>

    <select id="selectWaybillSlaveList" parameterType="com.zly.project.transport.waybill.domain.WaybillSlave"
            resultMap="WaybillSlaveResult">
        <include refid="selectWaybillSlaveVo"/>
        <where>
            <if test="loadingWeight != null ">
                AND loading_weight = #{loadingWeight}
            </if>
            <if test="loadingCube != null ">
                AND loading_cube = #{loadingCube}
            </if>
            <if test="unloadWeight != null ">
                AND unload_weight = #{unloadWeight}
            </if>
            <if test="unloadCube != null ">
                AND unload_cube = #{unloadCube}
            </if>
            <if test="policyType != null ">
                AND policy_type = #{policyType}
            </if>
            <if test="policyStatus != null">
                AND policy_status = #{policyStatus}
            </if>
            <if test="policyFare != null">
                AND policy_fare = #{policyFare}
            </if>
            <if test="policyNumber != null  and policyNumber != ''">
                AND policy_number = #{policyNumber}
            </if>
            <if test="insuranceCompanyCode != null  and insuranceCompanyCode != ''">
                AND insurance_company_code = #{insuranceCompanyCode}
            </if>
            <if test="uploginkDesc != null  and uploginkDesc != ''">
                AND uplogink_desc = #{uploginkDesc}
            </if>
            <if test="upetcStatus != null ">
                AND upetc_status = #{upetcStatus}
            </if>
            <if test="upetcTime != null ">
                AND upetc_time = #{upetcTime}
            </if>
            <if test="upetcDesc != null  and upetcDesc != ''">
                AND upetc_desc = #{upetcDesc}
            </if>
            <if test="uploginkStatus != null ">
                AND uplogink_status = #{uploginkStatus}
            </if>
            <if test="uploginkTime != null ">
                AND uplogink_time = #{uploginkTime}
            </if>
            <if test="checkState != null ">
                AND check_state = #{checkState}
            </if>
            <if test="electronicContractState != null ">
                AND electronic_contract_state = #{electronicContractState}
            </if>
            <if test="electronicContractUrl != null ">
                AND electronic_contract_url = #{electronicContractUrl}
            </if>
            <if test="electronicContractCode != null ">
                AND electronic_contract_code = #{electronicContractCode}
            </if>
            <if test="shudaitongFinancingStatus != null ">
                AND shudaitong_financing_status = #{shudaitongFinancingStatus}
            </if>
            <if test="vehicleDetail != null and vehicleDetail != ''">
                AND vehicle_detail = #{vehicleDetail}
            </if>
            <if test="driverDetail != null and driverDetail != ''">
                AND driver_detail = #{driverDetail}
            </if>
            <if test="payeeDetail != null and payeeDetail != ''">
                AND payee_detail = #{payeeDetail}
            </if>
            <if test="carrierDetail != null and carrierDetail != ''">
                AND carrier_detail = #{carrierDetail}
            </if>
            <if test="makeCodeRuleId != null">
                AND make_code_rule_id = #{makeCodeRuleId}
            </if>
            <if test="zeroingRule != null">
                AND zeroing_rule = #{zeroingRule}
            </if>
            <if test="makeCodeRuleDetailed != null">
                AND make_code_rule_detailed = #{makeCodeRuleDetailed}
            </if>
            <if test="receiveFareCalculationType != null">
                AND receive_fare_calculation_type = #{receiveFareCalculationType}
            </if>
            <if test="driverUpdateReceiptState != null">
                AND driver_update_receipt_state = #{driverUpdateReceiptState}
            </if>
            <if test="transportationProtocolCode != null transportationProtocolCode != ''">
                AND transportation_protocol_code = #{transportationProtocolCode}
            </if>
            <if test="etcTaxAmount != null">
                AND etc_tax_amount = #{etcTaxAmount}
            </if>
            <if test="upetcInvoiceStatus != null">
                upetc_invoice_status = #{upetcInvoiceStatus}
            </if>
        </where>
    </select>

    <select id="selectWaybillSlaveById" parameterType="Long" resultMap="WaybillSlaveResult">
        <include refid="selectWaybillSlaveVo"/>
        where id = #{id}
    </select>
    <select id="selectWaybillSlaveByWaybillIdsAndRemarkNotEmpty"
            resultType="com.zly.project.transport.waybill.domain.WaybillSlave">
        select id, remark FROM waybill_slave
        WHERE length(remark) &gt; 0
        and id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectWaybillSlaveByIds" resultMap="WaybillSlaveResult">
        <include refid="selectWaybillSlaveOldVo"/>
        where id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </select>


    <insert id="insertWaybillSlave" parameterType="com.zly.project.transport.waybill.domain.WaybillSlave">
        INSERT INTO waybill_slave
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="loadingWeight != null">loading_weight,</if>
            <if test="loadingCube != null">loading_cube,</if>
            <if test="unloadWeight != null">unload_weight,</if>
            <if test="unloadCube != null">unload_cube,</if>
            <if test="policyType != null">policy_type,</if>
            <if test="policyStatus != null">policy_status,</if>
            <if test="policyFare != null">policy_fare,</if>
            <if test="policyNumber != null and policyNumber != ''">policy_number,</if>
            <if test="insuranceCompanyCode != null and insuranceCompanyCode != ''">insurance_company_code,</if>
            <if test="uploginkDesc != null and uploginkDesc != ''">uplogink_desc,</if>
            <if test="upetcStatus != null">upetc_status,</if>
            <if test="upetcTime != null">upetc_time,</if>
            <if test="upetcDesc != null and upetcDesc != ''">upetc_desc,</if>
            <if test="uploginkStatus != null">uplogink_status,</if>
            <if test="uploginkTime != null">uplogink_time,</if>
            <if test="remark != null">remark,</if>
            <if test="checkState != null ">check_state,</if>
            <if test="electronicContractState != null ">electronic_contract_state,</if>
            <if test="electronicContractUrl != null ">electronic_contract_url,</if>
            <if test="electronicContractCode != null ">electronic_contract_code,</if>
            <if test="shudaitongFinancingStatus != null ">shudaitong_financing_status,</if>
            <if test="vehicleDetail != null">vehicle_detail,</if>
            <if test="driverDetail != null">driver_detail,</if>
            <if test="payeeDetail != null">payee_detail,</if>
            <if test="carrierDetail != null">carrier_detail,</if>
            <if test="makeCodeRuleId != null">make_code_rule_id,</if>
            <if test="zeroingRule != null">zeroing_rule,</if>
            <if test="makeCodeRuleDetailed != null">make_code_rule_detailed,</if>
            <if test="receiveFareCalculationType != null">receive_fare_calculation_type,</if>
            <if test="waybillPaymentType != null">waybill_payment_type,</if>
            <if test="carCaptainDetail != null">car_captain_detail,</if>
            <if test="auxiliaryStaffDetail != null">auxiliary_staff_detail,</if>
            <if test="driverUpdateReceiptState != null">driver_update_receipt_state,</if>
            <if test="transportationProtocolCode != null and transportationProtocolCode != ''">
                transportation_protocol_code,
            </if>
            <if test="etcTaxAmount != null">etc_tax_amount,</if>
            <if test="upetcInvoiceStatus != null">upetc_invoice_status,</if>
            <if test="loadClickTime != null">load_click_time,</if>
            <if test="unloadClickTime != null">unload_click_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="loadingWeight != null">#{loadingWeight},</if>
            <if test="loadingCube != null">#{loadingCube},</if>
            <if test="unloadWeight != null">#{unloadWeight},</if>
            <if test="unloadCube != null">#{unloadCube},</if>
            <if test="policyType != null">#{policyType},</if>
            <if test="policyStatus != null">#{policyStatus},</if>
            <if test="policyFare != null">#{policyFare},</if>
            <if test="policyNumber != null and policyNumber != ''">#{policyNumber},</if>
            <if test="insuranceCompanyCode != null and insuranceCompanyCode != ''">#{insuranceCompanyCode},</if>
            <if test="uploginkDesc != null and uploginkDesc != ''">#{uploginkDesc},</if>
            <if test="upetcStatus != null">#{upetcStatus},</if>
            <if test="upetcTime != null">#{upetcTime},</if>
            <if test="upetcDesc != null and upetcDesc != ''">#{upetcDesc},</if>
            <if test="uploginkStatus != null">#{uploginkStatus},</if>
            <if test="uploginkTime != null">#{uploginkTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="checkState != null">#{checkState},</if>
            <if test="electronicContractState != null">#{electronicContractState},</if>
            <if test="electronicContractUrl != null">#{electronicContractUrl},</if>
            <if test="electronicContractCode != null">#{electronicContractCode},</if>
            <if test="shudaitongFinancingStatus != null">#{shudaitongFinancingStatus},</if>
            <if test="vehicleDetail != null">#{vehicleDetail},</if>
            <if test="driverDetail != null">#{driverDetail},</if>
            <if test="payeeDetail != null">#{payeeDetail},</if>
            <if test="carrierDetail != null">#{carrierDetail},</if>
            <if test="makeCodeRuleId != null">#{makeCodeRuleId},</if>
            <if test="zeroingRule != null">#{zeroingRule},</if>
            <if test="makeCodeRuleDetailed != null">#{makeCodeRuleDetailed},</if>
            <if test="receiveFareCalculationType != null">#{receiveFareCalculationType},</if>
            <if test="waybillPaymentType != null">#{waybillPaymentType},</if>
            <if test="carCaptainDetail != null">#{carCaptainDetail},</if>
            <if test="auxiliaryStaffDetail != null">#{auxiliaryStaffDetail},</if>
            <if test="driverUpdateReceiptState != null">#{driverUpdateReceiptState},</if>
            <if test="transportationProtocolCode != null and transportationProtocolCode != ''">
                #{transportationProtocolCode},
            </if>
            <if test="etcTaxAmount != null">#{etcTaxAmount},</if>
            <if test="upetcInvoiceStatus != null">#{upetcInvoiceStatus},</if>
            <if test="loadClickTime != null">#{loadClickTime},</if>
            <if test="unloadClickTime != null">#{unloadClickTime},</if>
        </trim>
    </insert>
    <insert id="insertWaybillSlaves">
        insert IGNORE into waybill_slave
        (id,
        loading_weight,
        loading_cube,
        unload_weight,
        unload_cube,
        policy_type,
        policy_status,
        policy_fare,
        policy_number,
        insurance_company_code,
        uplogink_desc,
        upetc_status,
        upetc_time,
        upetc_desc,
        uplogink_status,
        uplogink_time,
        remark,
        financing_audit_status,
        financing_audit_time,
        financing_audit_remark,
        push_status,
        push_time,
        financing_status,
        upstream_statement_status,
        upstream_statement_time,
        agreement_generate_status,
        agreement_generate_time,
        agreement_generate_remark,
        check_state,
        electronic_contract_state,
        electronic_contract_url,
        electronic_contract_code,
        overrule_time,
        shudaitong_financing_status,
        vehicle_detail,
        driver_detail,
        payee_detail,
        carrier_detail,
        make_code_rule_id,
        zeroing_rule,
        make_code_rule_detailed,
        receive_fare_calculation_type,
        car_captain_detail,
        auxiliary_staff_detail,
        waybill_payment_type,
        load_click_time,
        unload_click_time
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.id},
            #{item.loadingWeight},
            #{item.loadingCube},
            #{item.unloadWeight},
            #{item.unloadCube},
            #{item.policyType},
            #{item.policyStatus},
            #{item.policyFare},
            #{item.policyNumber},
            #{item.insuranceCompanyCode},
            #{item.uploginkDesc},
            #{item.upetcStatus},
            <if test="item.upetcTime != null">#{item.upetcTime},</if>
            <if test="item.upetcTime == null">default,</if>
            #{item.upetcDesc},
            #{item.uploginkStatus},
            <if test="item.uploginkTime != null">#{item.uploginkTime},</if>
            <if test="item.uploginkTime == null">default,</if>
            #{item.remark},
            #{item.financingAuditStatus},
            <if test="item.financingAuditTime != null">#{item.financingAuditTime},</if>
            <if test="item.financingAuditTime == null">default,</if>
            #{item.financingAuditRemark},
            #{item.pushStatus},
            <if test="item.pushTime != null">#{item.pushTime},</if>
            <if test="item.pushTime == null">default,</if>
            #{item.financingStatus},
            #{item.upstreamStatementStatus},
            <if test="item.upstreamStatementTime != null">#{item.upstreamStatementTime},</if>
            <if test="item.upstreamStatementTime == null">default,</if>
            #{item.agreementGenerateStatus},
            <if test="item.agreementGenerateTime != null">#{item.agreementGenerateTime},</if>
            <if test="item.agreementGenerateTime == null">default,</if>
            #{item.agreementGenerateRemark},
            #{item.checkState},
            #{item.electronicContractState},
            #{item.electronicContractUrl},
            #{item.electronicContractCode},
            <if test="item.overruleTime != null">#{item.overruleTime},</if>
            <if test="item.overruleTime == null">default,</if>
            #{item.shudaitongFinancingStatus},
            #{item.vehicleDetail},
            #{item.driverDetail},
            #{item.payeeDetail},
            #{item.carrierDetail},
            #{item.makeCodeRuleId},
            #{item.zeroingRule},
            #{item.makeCodeRuleDetailed},
            #{item.receiveFareCalculationType},
            #{item.carCaptainDetail},
            #{item.auxiliaryStaffDetail},
            #{item.waybillPaymentType},
            <if test="item.loadClickTime != null">#{item.loadClickTime},</if>
            <if test="item.loadClickTime == null">default,</if>
            <if test="item.unloadClickTime != null">#{item.unloadClickTime}</if>
            <if test="item.unloadClickTime == null">default</if>
        </foreach>
    </insert>

    <update id="updateWaybillSlave" parameterType="com.zly.project.transport.waybill.domain.WaybillSlave">
        UPDATE waybill_slave
        <set>
            <if test="loadingWeight != null">
                loading_weight = #{loadingWeight},
            </if>
            <if test="loadingCube != null">
                loading_cube = #{loadingCube},
            </if>
            <if test="unloadWeight != null">
                unload_weight = #{unloadWeight},
            </if>
            <if test="unloadCube != null">
                unload_cube = #{unloadCube},
            </if>
            <if test="policyType != null">
                policy_type = #{policyType},
            </if>
            <if test="policyStatus != null">
                policy_status = #{policyStatus},
            </if>
            <if test="policyFare != null">
                policy_fare = #{policyFare},
            </if>
            <if test="policyNumber != null and policyNumber != ''">
                policy_number = #{policyNumber},
            </if>
            <if test="insuranceCompanyCode != null and insuranceCompanyCode != ''">
                insurance_company_code = #{insuranceCompanyCode},
            </if>
            <if test="uploginkDesc != null and uploginkDesc != ''">
                uplogink_desc = #{uploginkDesc},
            </if>
            <if test="upetcStatus != null">
                upetc_status = #{upetcStatus},
            </if>
            <if test="upetcTime != null">
                upetc_time = #{upetcTime},
            </if>
            <if test="upetcDesc != null and upetcDesc != ''">
                upetc_desc = #{upetcDesc},
            </if>
            <if test="uploginkStatus != null">
                uplogink_status = #{uploginkStatus},
            </if>
            <if test="uploginkTime != null">
                uplogink_time = #{uploginkTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="financingAuditStatus != null and financingAuditStatus != ''">
                financing_audit_status = #{financingAuditStatus},
            </if>
            <if test="financingAuditTime != null">
                financing_audit_time = #{financingAuditTime},
            </if>
            <if test="pushStatus != null and pushStatus != ''">
                push_status = #{pushStatus},
            </if>
            <if test="pushTime != null">
                push_time = #{pushTime},
            </if>
            <if test="financingStatus != null and financingStatus != ''">
                financing_status = #{financingStatus},
            </if>
            <if test="financingAuditRemark != null and financingAuditRemark != ''">
                financing_audit_remark = #{financingAuditRemark},
            </if>
            <if test="upstreamStatementStatus != null and upstreamStatementStatus != ''">
                upstream_statement_status = #{upstreamStatementStatus},
            </if>
            <if test="upstreamStatementTime != null">
                upstream_statement_time = #{upstreamStatementTime},
            </if>
            <if test="checkState != null">
                check_state = #{checkState},
            </if>
            <if test="electronicContractCode != null">
                electronic_contract_code = #{electronicContractCode},
            </if>
            <if test="electronicContractState != null">
                electronic_contract_state = #{electronicContractState},
            </if>
            <if test="electronicContractUrl != null">
                electronic_contract_url = #{electronicContractUrl},
            </if>
            <if test="agreementGenerateStatus != null and agreementGenerateStatus != ''">
                agreement_generate_status = #{agreementGenerateStatus},
            </if>
            <if test="agreementGenerateTime != null">
                agreement_generate_time = #{agreementGenerateTime},
            </if>
            <if test="agreementGenerateRemark != null and agreementGenerateRemark != ''">
                agreement_generate_remark = #{agreementGenerateRemark},
            </if>
            <if test="shudaitongFinancingStatus != null ">
                shudaitong_financing_status = #{shudaitongFinancingStatus},
            </if>
            <if test="vehicleDetail != null and vehicleDetail != ''">
                vehicle_detail = #{vehicleDetail},
            </if>
            <if test="driverDetail != null and driverDetail != ''">
                driver_detail = #{driverDetail},
            </if>
            <if test="payeeDetail != null and payeeDetail != ''">
                payee_detail = #{payeeDetail},
            </if>
            <if test="carrierDetail != null and carrierDetail != ''">
                carrier_detail = #{carrierDetail},
            </if>
            <if test="makeCodeRuleId != null ">
                make_code_rule_id = #{makeCodeRuleId},
            </if>
            <if test="zeroingRule != null ">
                zeroing_rule = #{zeroingRule},
            </if>
            <if test="makeCodeRuleDetailed != null ">
                make_code_rule_detailed = #{makeCodeRuleDetailed},
            </if>
            <if test="receiveFareCalculationType != null ">
                receive_fare_calculation_type = #{receiveFareCalculationType},
            </if>
            <if test="waybillPaymentType != null ">
                waybill_payment_type = #{waybillPaymentType},
            </if>
            <if test="carCaptainDetail != null ">
                car_captain_detail = #{carCaptainDetail},
            </if>
            <if test="auxiliaryStaffDetail != null ">
                auxiliary_staff_detail = #{auxiliaryStaffDetail},
            </if>
            <if test="driverUpdateReceiptState != null ">
                driver_update_receipt_state = #{driverUpdateReceiptState},
            </if>
            <if test="transportationProtocolCode != null and transportationProtocolCode != ''">
                transportation_protocol_code = #{transportationProtocolCode},
            </if>
            <if test="etcTaxAmount != null ">
                etc_tax_amount = #{etcTaxAmount},
            </if>
            <if test="upetcInvoiceStatus != null ">
                upetc_invoice_status = #{upetcInvoiceStatus},
            </if>
            <if test="loadClickTime != null ">
                load_click_time = #{loadClickTime},
            </if>
            <if test="unloadClickTime != null ">
                unload_click_time = #{unloadClickTime},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteWaybillSlaveById" parameterType="Long">
        DELETE
        FROM waybill_slave
        WHERE id = #{id}
    </delete>

    <delete id="deleteWaybillSlaveByIds" parameterType="String">
        DELETE
        FROM waybill_slave
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateWaybillSlaveUnPush">
        update waybill_slave
        set push_time = #{pushTime}, push_status = #{pushStatus}, push_reason = #{pushReason}
        <if test="financingStatus != null ">
            ,financing_status = #{financingStatus}
        </if>
        where push_status in (0,2,4) and id = #{id}
    </update>
    <update id="pushWaybillOverrule">
        update waybill_slave set push_status = 4,overrule_time = now() where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="pushWaybillOverruleByXyd">
        update waybill_slave set push_status = 4,overrule_time = now(),financing_audit_remark = '小雨点运单退回' where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
