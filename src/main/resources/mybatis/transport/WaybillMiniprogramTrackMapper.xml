<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.transport.waybill.mapper.WaybillMiniprogramTrackMapper">
    
    <resultMap type="WaybillMiniprogramTrack" id="WaybillMiniprogramTrackResult">
        <result property="id"    column="id"    />
        <result property="waybillId"    column="waybill_id"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="country"    column="country"    />
        <result property="adr"    column="adr"    />
        <result property="lat"    column="lat"    />
        <result property="lon"    column="lon"    />
        <result property="spd"    column="spd"    />
        <result property="accuracy"    column="accuracy"    />
        <result property="altitude"    column="altitude"    />
        <result property="verticalAccuracy"    column="vertical_accuracy"    />
        <result property="horizontalAccuracy"    column="horizontal_accuracy"    />
        <result property="distance"    column="distance"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectWaybillMiniprogramTrackVo">
        select id, waybill_id, province, city, country, adr, lat, lon, spd, accuracy, altitude, vertical_accuracy, horizontal_accuracy, distance, create_time from waybill_miniprogram_track
    </sql>

    <select id="selectWaybillMiniprogramTrackList" parameterType="WaybillMiniprogramTrack" resultMap="WaybillMiniprogramTrackResult">
        <include refid="selectWaybillMiniprogramTrackVo"/>
        <where>  
            <if test="waybillId != null "> and waybill_id = #{waybillId}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="country != null  and country != ''"> and country = #{country}</if>
            <if test="adr != null  and adr != ''"> and adr = #{adr}</if>
            <if test="lat != null  and lat != ''"> and lat = #{lat}</if>
            <if test="lon != null  and lon != ''"> and lon = #{lon}</if>
            <if test="spd != null "> and spd = #{spd}</if>
            <if test="accuracy != null "> and accuracy = #{accuracy}</if>
            <if test="altitude != null "> and altitude = #{altitude}</if>
            <if test="verticalAccuracy != null "> and vertical_accuracy = #{verticalAccuracy}</if>
            <if test="horizontalAccuracy != null ">and horizontal_accuracy = #{horizontalAccuracy}</if>
            <if test="distance != null ">and distance = #{distance}</if>
            <if test="params != null and params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
        </where>
    </select>

    <select id="selectWaybillMiniprogramTrackById" parameterType="Long" resultMap="WaybillMiniprogramTrackResult">
        <include refid="selectWaybillMiniprogramTrackVo"/>
        where id = #{id}
    </select>
    <select id="selectWaybillMiniprogramTrackByWaybillIds" resultMap="WaybillMiniprogramTrackResult">
        <include refid="selectWaybillMiniprogramTrackVo"/>
        where waybill_id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </select>

    <insert id="insertWaybillMiniprogramTrack" parameterType="WaybillMiniprogramTrack">
        insert into waybill_miniprogram_track
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="waybillId != null">waybill_id,</if>
            <if test="province != null and province != ''">province,</if>
            <if test="city != null and city != ''">city,</if>
            <if test="country != null and country != ''">country,</if>
            <if test="adr != null and adr != ''">adr,</if>
            <if test="lat != null and lat != ''">lat,</if>
            <if test="lon != null and lon != ''">lon,</if>
            <if test="spd != null">spd,</if>
            <if test="accuracy != null">accuracy,</if>
            <if test="altitude != null">altitude,</if>
            <if test="verticalAccuracy != null">vertical_accuracy,</if>
            <if test="horizontalAccuracy != null">horizontal_accuracy,</if>
            <if test="distance != null">distance,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="waybillId != null">#{waybillId},</if>
            <if test="province != null and province != ''">#{province},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="country != null and country != ''">#{country},</if>
            <if test="adr != null and adr != ''">#{adr},</if>
            <if test="lat != null and lat != ''">#{lat},</if>
            <if test="lon != null and lon != ''">#{lon},</if>
            <if test="spd != null">#{spd},</if>
            <if test="accuracy != null">#{accuracy},</if>
            <if test="altitude != null">#{altitude},</if>
            <if test="verticalAccuracy != null">#{verticalAccuracy},</if>
            <if test="horizontalAccuracy != null">#{horizontalAccuracy},</if>
            <if test="distance != null">#{distance},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>
    <insert id="insertWaybillMiniprogramTracks">
        insert IGNORE into waybill_miniprogram_track
        (id,
        waybill_id,
        province,
        city,
        country,
        adr,
        lat,
        lon,
        spd,
        accuracy,
        altitude,
        vertical_accuracy,
        horizontal_accuracy,
        distance,
        create_time
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.id},
            #{item.waybillId},
            #{item.province},
            #{item.city},
            #{item.country},
            #{item.adr},
            #{item.lat},
            #{item.lon},
            #{item.spd},
            #{item.accuracy},
            #{item.altitude},
            #{item.verticalAccuracy},
            #{item.horizontalAccuracy},
            #{item.distance},
            #{item.createTime}
        </foreach>
    </insert>

    <update id="updateWaybillMiniprogramTrack" parameterType="WaybillMiniprogramTrack">
        update waybill_miniprogram_track
        <trim prefix="SET" suffixOverrides=",">
            <if test="waybillId != null">waybill_id = #{waybillId},</if>
            <if test="province != null and province != ''">province = #{province},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="country != null and country != ''">country = #{country},</if>
            <if test="adr != null and adr != ''">adr = #{adr},</if>
            <if test="lat != null and lat != ''">lat = #{lat},</if>
            <if test="lon != null and lon != ''">lon = #{lon},</if>
            <if test="spd != null">spd = #{spd},</if>
            <if test="accuracy != null">accuracy = #{accuracy},</if>
            <if test="altitude != null">altitude = #{altitude},</if>
            <if test="verticalAccuracy != null">vertical_accuracy = #{verticalAccuracy},</if>
            <if test="horizontalAccuracy != null">horizontal_accuracy = #{horizontalAccuracy},</if>
            <if test="distance != null">distance = #{distance},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWaybillMiniprogramTrackById" parameterType="Long">
        delete from waybill_miniprogram_track where id = #{id}
    </delete>

    <delete id="deleteWaybillMiniprogramTrackByIds" parameterType="String">
        delete from waybill_miniprogram_track where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteWaybillMiniprogramTrackByWaybillIds">
        delete from waybill_miniprogram_track where waybill_id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </delete>
</mapper>