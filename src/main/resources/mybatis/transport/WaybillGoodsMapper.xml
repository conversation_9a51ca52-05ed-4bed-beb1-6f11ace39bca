<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.transport.waybill.mapper.WaybillGoodsMapper">

    <resultMap type="WaybillGoods" id="WaybillGoodsResult">
        <result property="id" column="id"/>
        <result property="waybillId" column="waybill_id"/>
        <result property="descriptionOfGoods" column="description_of_goods"/>
        <result property="cargoTypeClassificationCode" column="cargo_type_classification_code"/>
        <result property="cargoTypeClassificationName" column="cargo_type_classification_name"/>
        <result property="goodsItemGrossWeight" column="goods_item_gross_weight"/>
        <result property="cube" column="cube"/>
        <result property="numberOfPackages" column="number_of_packages"/>
        <result property="packagingCode" column="packaging_code"/>
        <result property="packagingName" column="packaging_name"/>
        <result property="monetaryAmount" column="monetary_amount"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="cargoInsuranceTypeClassificationCode" column="cargo_insurance_type_classification_code"/>
        <result property="cargoInsuranceTypeClassificationName" column="cargo_insurance_type_classification_name"/>
    </resultMap>

    <sql id="selectWaybillGoodsVo">
        SELECT id,
               waybill_id,
               description_of_goods,
               cargo_type_classification_code,
               cargo_type_classification_name,
               goods_item_gross_weight,
               `cube`,
               number_of_packages,
               packaging_code,
               packaging_name,
               monetary_amount,
               remark,
               create_by,
               create_time,
               update_by,
               update_time,
               cargo_insurance_type_classification_code,
               cargo_insurance_type_classification_name
        FROM waybill_goods
    </sql>

    <select id="selectWaybillGoodsList" parameterType="WaybillGoods" resultMap="WaybillGoodsResult">
        <include refid="selectWaybillGoodsVo"/>
        <where>
            <if test="waybillId != null ">and waybill_id = #{waybillId}</if>
            <if test="descriptionOfGoods != null  and descriptionOfGoods != ''">and description_of_goods =
                #{descriptionOfGoods}
            </if>
            <if test="cargoTypeClassificationCode != null  and cargoTypeClassificationCode != ''">and
                cargo_type_classification_code = #{cargoTypeClassificationCode}
            </if>
            <if test="cargoTypeClassificationName != null  and cargoTypeClassificationName != ''">and
                cargo_type_classification_name like concat('%', #{cargoTypeClassificationName}, '%')
            </if>
            <if test="goodsItemGrossWeight != null ">and goods_item_gross_weight = #{goodsItemGrossWeight}</if>
            <if test="cube != null ">and `cube` = #{cube}</if>
            <if test="numberOfPackages != null ">and number_of_packages = #{numberOfPackages}</if>
            <if test="packagingCode != null  and packagingCode != ''">and packaging_code = #{packagingCode}</if>
            <if test="packagingName != null  and packagingName != ''">and packaging_name like concat('%',
                #{packagingName}, '%')
            </if>
            <if test="monetaryAmount != null ">and monetary_amount = #{monetaryAmount}</if>
            <if test="cargoInsuranceTypeClassificationCode != null  and cargoInsuranceTypeClassificationCode != ''">and
                cargo_insurance_type_classification_code = #{cargoInsuranceTypeClassificationCode}
            </if>
            <if test="cargoInsuranceTypeClassificationName != null  and cargoInsuranceTypeClassificationName != ''">and
                cargo_insurance_type_classification_name like concat('%', #{cargoInsuranceTypeClassificationName}, '%')
            </if>
        </where>
    </select>

    <select id="selectWaybillGoodsById" parameterType="Long" resultMap="WaybillGoodsResult">
        <include refid="selectWaybillGoodsVo"/>
        where id = #{id}
    </select>
    <select id="selectWaybillGoodsListByWaybillIds"
            resultMap="WaybillGoodsResult">
        <include refid="selectWaybillGoodsVo"/>
        where waybill_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <insert id="insertWaybillGoods" parameterType="WaybillGoods">
        insert into waybill_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="waybillId != null">waybill_id,</if>
            <if test="descriptionOfGoods != null and descriptionOfGoods != ''">description_of_goods,</if>
            <if test="cargoTypeClassificationCode != null and cargoTypeClassificationCode != ''">
                cargo_type_classification_code,
            </if>
            <if test="cargoTypeClassificationName != null and cargoTypeClassificationName != ''">
                cargo_type_classification_name,
            </if>
            <if test="goodsItemGrossWeight != null">goods_item_gross_weight,</if>
            <if test="cube != null">`cube`,</if>
            <if test="numberOfPackages != null">number_of_packages,</if>
            <if test="packagingCode != null and packagingCode != ''">packaging_code,</if>
            <if test="packagingName != null and packagingName != ''">packaging_name,</if>
            <if test="monetaryAmount != null">monetary_amount,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="cargoInsuranceTypeClassificationCode != null and cargoInsuranceTypeClassificationCode != ''">
                cargo_insurance_type_classification_code,
            </if>
            <if test="cargoInsuranceTypeClassificationName != null and cargoInsuranceTypeClassificationName != ''">
                cargo_insurance_type_classification_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="waybillId != null">#{waybillId},</if>
            <if test="descriptionOfGoods != null and descriptionOfGoods != ''">#{descriptionOfGoods},</if>
            <if test="cargoTypeClassificationCode != null and cargoTypeClassificationCode != ''">
                #{cargoTypeClassificationCode},
            </if>
            <if test="cargoTypeClassificationName != null and cargoTypeClassificationName != ''">
                #{cargoTypeClassificationName},
            </if>
            <if test="goodsItemGrossWeight != null">#{goodsItemGrossWeight},</if>
            <if test="cube != null">#{cube},</if>
            <if test="numberOfPackages != null">#{numberOfPackages},</if>
            <if test="packagingCode != null and packagingCode != ''">#{packagingCode},</if>
            <if test="packagingName != null and packagingName != ''">#{packagingName},</if>
            <if test="monetaryAmount != null">#{monetaryAmount},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="cargoInsuranceTypeClassificationCode != null and cargoInsuranceTypeClassificationCode != ''">
                #{cargoInsuranceTypeClassificationCode},
            </if>
            <if test="cargoInsuranceTypeClassificationName != null and cargoInsuranceTypeClassificationName != ''">
                #{cargoInsuranceTypeClassificationName},
            </if>
        </trim>
    </insert>
    <insert id="insertWaybillGoodsList">
        insert IGNORE into waybill_goods
        (id,
        waybill_id,
        description_of_goods,
        cargo_type_classification_code,
        cargo_type_classification_name,
        goods_item_gross_weight,
        cube,
        number_of_packages,
        packaging_code,
        packaging_name,
        monetary_amount,
        remark,
        create_by,
        create_time,
        update_by,
        update_time,
        cargo_insurance_type_classification_code,
        cargo_insurance_type_classification_name
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.id},
            #{item.waybillId},
            #{item.descriptionOfGoods},
            #{item.cargoTypeClassificationCode},
            #{item.cargoTypeClassificationName},
            #{item.goodsItemGrossWeight},
            #{item.cube},
            #{item.numberOfPackages},
            #{item.packagingCode},
            #{item.packagingName},
            #{item.monetaryAmount},
            #{item.remark},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime},
            #{item.cargoInsuranceTypeClassificationCode},
            #{item.cargoInsuranceTypeClassificationName}
        </foreach>

    </insert>

    <update id="updateWaybillGoods" parameterType="WaybillGoods">
        update waybill_goods
        <trim prefix="SET" suffixOverrides=",">
            <if test="waybillId != null">waybill_id = #{waybillId},</if>
            <if test="descriptionOfGoods != null and descriptionOfGoods != ''">description_of_goods =
                #{descriptionOfGoods},
            </if>
            <if test="cargoTypeClassificationCode != null and cargoTypeClassificationCode != ''">
                cargo_type_classification_code = #{cargoTypeClassificationCode},
            </if>
            <if test="cargoTypeClassificationName != null and cargoTypeClassificationName != ''">
                cargo_type_classification_name = #{cargoTypeClassificationName},
            </if>
            <if test="goodsItemGrossWeight != null">goods_item_gross_weight = #{goodsItemGrossWeight},</if>
            <if test="cube != null">`cube` = #{cube},</if>
            <if test="numberOfPackages != null">number_of_packages = #{numberOfPackages},</if>
            <if test="packagingCode != null and packagingCode != ''">packaging_code = #{packagingCode},</if>
            <if test="packagingName != null and packagingName != ''">packaging_name = #{packagingName},</if>
            <if test="monetaryAmount != null">monetary_amount = #{monetaryAmount},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="cargoInsuranceTypeClassificationCode != null and cargoInsuranceTypeClassificationCode != ''">
                cargo_insurance_type_classification_code = #{cargoInsuranceTypeClassificationCode},
            </if>
            <if test="cargoInsuranceTypeClassificationName != null and cargoInsuranceTypeClassificationName != ''">
                cargo_insurance_type_classification_name = #{cargoInsuranceTypeClassificationName},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWaybillGoodsById" parameterType="Long">
        DELETE
        FROM waybill_goods
        WHERE id = #{id}
    </delete>

    <delete id="deleteWaybillGoodsByIds" parameterType="String">
        delete from waybill_goods where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteWaybillGoodsByWaybillIds">
        delete from waybill_goods where waybill_id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </delete>
</mapper>