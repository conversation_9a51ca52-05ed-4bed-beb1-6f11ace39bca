<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.tenant.mapper.TenantUserRoleMapper">

    <resultMap type="TenantUserRole" id="TenantUserRoleResult">
        <result property="userId" column="user_id"/>
        <result property="roleId" column="role_id"/>
    </resultMap>

    <delete id="deleteUserRoleByUserId" parameterType="Long">
        DELETE
        FROM tenant_user_role
        WHERE user_id = #{userId}
    </delete>

    <select id="countUserRoleByRoleId" resultType="Integer">
        SELECT COUNT(1)
        FROM tenant_user_role
        WHERE role_id = #{roleId}
    </select>
    <select id="selectRoleIdsByUserId" resultType="java.lang.Long">
        SELECT role_id
        FROM tenant_user_role
        WHERE user_id = #{userId}
    </select>
    <select id="selectAdminByUserIds" resultType="java.lang.Long">
        select  tur.user_id from tenant_user_role tur
        left join tenant_role tr on tur.role_id = tr.role_id
        where tur.user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and tr.role_name = "管理员"
    </select>



    <delete id="deleteUserRole" parameterType="Long">
        delete from tenant_user_role where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <insert id="batchUserRole">
        insert ignore into tenant_user_role(user_id, role_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.userId},#{item.roleId})
        </foreach>
    </insert>

    <delete id="deleteUserRoleInfo" parameterType="TenantUserRole">
        DELETE
        FROM tenant_user_role
        WHERE user_id = #{userId}
          AND role_id = #{roleId}
    </delete>

    <delete id="deleteUserRoleInfos">
        delete from tenant_user_role where role_id=#{roleId} and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <update id="editRoleByRoleId" parameterType="java.lang.Long">
        UPDATE tenant_user_role
        SET role_id = 0
        WHERE role_id IN (SELECT role_id FROM tenant_role WHERE role_key = 'admin' AND tenant_id = #{tenantId})
    </update>

    <delete id="deleteUserRoleByRoleId">
        DELETE
        FROM tenant_user_role
        WHERE role_id = #{roleId}
    </delete>
</mapper>
