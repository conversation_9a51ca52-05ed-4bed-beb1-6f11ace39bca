<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.tenant.mapper.TenantRoleMapper">

	<resultMap type="TenantRole" id="TenantRoleResult">
		<id property="roleId" column="role_id" />
		<result property="roleName" column="role_name" />
		<result property="roleKey" column="role_key" />
		<result property="roleSort" column="role_sort" />
		<result property="dataScope" column="data_scope" />
		<result property="menuCheckStrictly" column="menu_check_strictly" />
		<result property="deptCheckStrictly" column="dept_check_strictly" />
		<result property="status" column="status" />
		<result property="delFlag" column="del_flag" />
		<result property="createBy" column="create_by" />
		<result property="createTime" column="create_time" />
		<result property="updateBy" column="update_by" />
		<result property="updateTime" column="update_time" />
		<result property="remark" column="remark" />
		<result property="tenantId" 	 column="tenant_id" />
		<result property="presetRoleId" column="preset_role_id"/>
	</resultMap>

	<sql id="selectRoleVo">
		select distinct r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.menu_check_strictly, r.dept_check_strictly,
		r.status, r.del_flag, r.create_time, r.remark, r.tenant_id,  r.preset_role_id,r.create_by
		from tenant_role r
		left join tenant_user_role ur on ur.role_id = r.role_id
		left join tenant_user u on u.user_id = ur.user_id
		left join tenant_dept d on u.dept_id = d.dept_id
	</sql>

	<select id="selectRoleList" parameterType="TenantRole" resultMap="TenantRoleResult">
		<include refid="selectRoleVo" />
		where r.del_flag = '0'
		<if test="roleId != null and roleId != 0">
			AND r.role_id = #{roleId}
		</if>
		<if test="roleName != null and roleName != ''">
			AND r.role_name like concat('%', #{roleName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND r.status = #{status}
		</if>
		<if test="roleKey != null and roleKey != ''">
			AND r.role_key = #{roleKey}
		</if>
		<if test="params != null and params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			and date_format(r.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
		</if>
		<if test="params != null and params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			and date_format(r.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
		</if>
		<if test="tenantId != null and tenantId != ''">
			AND r.tenant_id = #{tenantId}
		</if>
		<!-- 数据范围过滤 -->
		<if test="params != null and params.dataScope!=null and params.dataScope!=''">
			AND r.data_scope = #{params.dataScope}
		</if>
		<if test="presetRoleId != null and presetRoleId != 0">
			AND r.preset_role_id = #{presetRoleId}
		</if>
		order by r.role_sort
	</select>

	<select id="selectRoleListEx" resultMap="TenantRoleResult">
		<include refid="selectRoleVo"/>
		where r.del_flag = '0'
		<if test="roleId != null and roleId != 0">
			AND r.role_id = #{roleId}
		</if>
		<if test="roleName != null and roleName != ''">
			AND r.role_name like concat('%', #{roleName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND r.status = #{status}
		</if>
		<if test="roleKey != null and roleKey != ''">
			AND r.role_key = #{roleKey}
		</if>
		<if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
			and date_format(r.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
		</if>
		<if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
			and date_format(r.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
		</if>
		<if test="tenantId != null and tenantId != ''">
			AND r.tenant_id = #{tenantId}
		</if>
		order by r.role_sort
	</select>

	<select id="selectRolePermissionByUserId" parameterType="Long" resultMap="TenantRoleResult">
		<include refid="selectRoleVo"/>
		WHERE r.del_flag = '0' and ur.user_id = #{userId} and r.tenant_id = #{tenantId}
	</select>

	<select id="selectRoleAll" resultMap="TenantRoleResult">
		<include refid="selectRoleVo"/>
	</select>

	<select id="selectRoleListByUserId" parameterType="Long" resultType="Long">
		select r.role_id
		from tenant_role r
		left join tenant_user_role ur on ur.role_id = r.role_id
		left join tenant_user u on u.user_id = ur.user_id
		where u.user_id = #{userId}
	</select>

	<select id="selectRoleById" parameterType="Long" resultMap="TenantRoleResult">
		<include refid="selectRoleVo" />
		where r.role_id = #{roleId}
	</select>

	<select id="selectRolesByUserName" parameterType="String" resultMap="TenantRoleResult">
		<include refid="selectRoleVo" />
		WHERE r.del_flag = '0' and u.user_name = #{userName}
	</select>

	<select id="checkRoleNameUnique" resultMap="TenantRoleResult">
		<include refid="selectRoleVo" />
		where r.role_name=#{roleName} and r.tenant_id=#{tenantId} limit 1
	</select>

	<select id="checkRoleKeyUnique" parameterType="String" resultMap="TenantRoleResult">
		<include refid="selectRoleVo" />
		where r.role_key=#{roleKey} and r.tenant_id=#{tenantId} limit 1
	</select>
    <select id="selectRoleByPresetId" resultMap="TenantRoleResult">
		select r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.menu_check_strictly, r.dept_check_strictly,
						r.status, r.del_flag, r.create_time, r.remark, r.tenant_id
		from tenant_role r where r.preset_role_id=#{presetRoleId}
	</select>
	<select id="selectRoleByUserId" resultMap="TenantRoleResult">
		<include refid="selectRoleVo" />
		where u.user_id = #{userId}
	</select>

	<insert id="insertRole" parameterType="TenantRole" useGeneratedKeys="true" keyProperty="roleId">
		insert into tenant_role(
		<if test="roleId != null and roleId != 0">role_id,</if>
		<if test="roleName != null and roleName != ''">role_name,</if>
		<if test="roleKey != null and roleKey != ''">role_key,</if>
		<if test="roleSort != null and roleSort != ''">role_sort,</if>
		<if test="dataScope != null and dataScope != ''">data_scope,</if>
		<if test="menuCheckStrictly != null">menu_check_strictly,</if>
		<if test="deptCheckStrictly != null">dept_check_strictly,</if>
		<if test="status != null and status != ''">status,</if>
		<if test="remark != null and remark != ''">remark,</if>
		<if test="createBy != null and createBy != ''">create_by,</if>
		<if test="tenantId != null and tenantId != ''">tenant_id,</if>
		<if test="presetRoleId != null and presetRoleId != 0">preset_role_id,</if>
		create_time
		)values(
		<if test="roleId != null and roleId != 0">#{roleId},</if>
		<if test="roleName != null and roleName != ''">#{roleName},</if>
		<if test="roleKey != null and roleKey != ''">#{roleKey},</if>
		<if test="roleSort != null and roleSort != ''">#{roleSort},</if>
		<if test="dataScope != null and dataScope != ''">#{dataScope},</if>
		<if test="menuCheckStrictly != null">#{menuCheckStrictly},</if>
		<if test="deptCheckStrictly != null">#{deptCheckStrictly},</if>
		<if test="status != null and status != ''">#{status},</if>
		<if test="remark != null and remark != ''">#{remark},</if>
		<if test="createBy != null and createBy != ''">#{createBy},</if>
		<if test="tenantId != null and tenantId != ''">#{tenantId},</if>
		<if test="presetRoleId != null and presetRoleId != 0">#{presetRoleId},</if>
		sysdate()
		)
	</insert>
    <insert id="insertDefaultRole">
		insert into tenant_user_role(user_id,role_id)
		values(#{userId},1)
	</insert>

    <update id="updateRole" parameterType="TenantRole">
		update tenant_role
		<set>
			<if test="roleName != null and roleName != ''">role_name = #{roleName},</if>
			<if test="roleKey != null and roleKey != ''">role_key = #{roleKey},</if>
			<if test="roleSort != null and roleSort != ''">role_sort = #{roleSort},</if>
			<if test="dataScope != null and dataScope != ''">data_scope = #{dataScope},</if>
			<if test="menuCheckStrictly != null">menu_check_strictly = #{menuCheckStrictly},</if>
			<if test="deptCheckStrictly != null">dept_check_strictly = #{deptCheckStrictly},</if>
			<if test="status != null and status != ''">status = #{status},</if>
			<if test="remark != null">remark = #{remark},</if>
			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
			<if test="presetRoleId != null and presetRoleId != ''">preset_role_id =
				#{presetRoleId},
			</if>
			update_time = sysdate()
		</set>
		where role_id = #{roleId}
	</update>

	<delete id="deleteRoleById" parameterType="Long">
		update tenant_role set del_flag = '2' where role_id = #{roleId}
	</delete>

	<delete id="deleteRoleByIds" parameterType="Long">
		update tenant_role set del_flag = '2' where role_id in
		<foreach collection="array" item="roleId" open="(" separator="," close=")">
			#{roleId}
		</foreach>
	</delete>

	<select id="selectRoleListByIds" resultType="TenantRole">
		SELECT role_id AS roleId, role_name AS roleName, role_key AS roleKey, role_sort AS roleSort,
		data_scope AS dataScope, menu_check_strictly AS menuCheckStrictly, dept_check_strictly AS deptCheckStrictly,
		status, create_by AS createBy, create_time AS createTime, update_by AS updateBy, update_time AS updateTime,
		remark, tenant_id AS tenantId, preset_role_id AS presetRoleId
		FROM tenant_role
		WHERE del_flag = 0
		AND role_id IN
		<foreach collection="roleIds" item="roleId" open="(" close=")" separator=",">
			#{roleId}
		</foreach>
	</select>
	<select id="selectRoleListByCustomerIds" resultMap="TenantRoleResult">
		select role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly,
			   status, del_flag, create_time, remark, tenant_id
		from tenant_role  where tenant_id in
		<foreach collection="customerIds" item="customerId" open="(" close=")" separator=",">
			#{customerId}
		</foreach>
	</select>
</mapper> 