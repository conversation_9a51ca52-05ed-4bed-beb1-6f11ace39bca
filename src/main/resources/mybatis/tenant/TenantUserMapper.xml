<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.tenant.mapper.TenantUserMapper">

    <resultMap type="TenantUser" id="TenantUserResult">
        <id     property="userId"       column="user_id"      />
        <result property="deptId"       column="dept_id"      />
        <result property="userName"     column="user_name"    />
        <result property="nickName"     column="nick_name"    />
        <result property="email"        column="email"        />
        <result property="phonenumber"  column="phonenumber"  />
        <result property="sex"          column="sex"          />
        <result property="avatar"       column="avatar"       />
        <result property="password"     column="password"     />
        <result property="pwd"     column="pwd"     />
        <result property="status"       column="status"       />
        <result property="delFlag"      column="del_flag"     />
        <result property="loginIp"      column="login_ip"     />
        <result property="loginDate"    column="login_date"   />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="remark"       column="remark"       />
		<result property="tenantId" 	 column="tenant_id" />
		<result property="tenantName" 	 column="customer_name" />
		<result property="defaultProject" 	 column="default_project" />
		<result property="waybillCopyType" 	 column="waybill_copy_type" />
		<result property="isAllMakeCode" column="is_all_make_code"/>
        <association property="dept"    column="dept_id" javaType="TenantDept" resultMap="deptResult" />
        <collection  property="roles"   javaType="java.util.List"           resultMap="RoleResult" />
    </resultMap>

    <resultMap id="deptResult" type="TenantDept">
        <id     property="deptId"    column="dept_id"     />
        <result property="parentId"  column="parent_id"   />
        <result property="deptName"  column="dept_name"   />
        <result property="ancestors" column="ancestors"   />
        <result property="orderNum"  column="order_num"   />
        <result property="leader"    column="leader"      />
        <result property="status"    column="dept_status" />
    </resultMap>

    <resultMap id="RoleResult" type="TenantRole">
        <id     property="roleId"       column="role_id"        />
        <result property="roleName"     column="role_name"      />
        <result property="roleKey"      column="role_key"       />
        <result property="roleSort"     column="role_sort"      />
        <result property="dataScope"     column="data_scope"    />
        <result property="status"       column="role_status"    />
    </resultMap>

	<sql id="selectUserVo">
		select u.user_id,
			   u.dept_id,
			   u.user_name,
			   u.nick_name,
			   u.email,
			   u.last_email,
			   u.avatar,
			   u.phonenumber,
			   u.password,
			   u.pwd,
			   u.sex,
			   u.status,
			   u.del_flag,
			   u.login_ip,
			   u.login_date,
			   u.create_by,
			   u.create_time,
			   u.remark,
			   u.tenant_id,
			   u.default_project,
			   u.is_all_make_code,
			   d.dept_id,
			   d.parent_id,
			   d.ancestors,
			   d.dept_name,
			   d.order_num,
			   d.leader,
			   d.status as dept_status,
			   u.waybill_copy_type,
			   r.role_id,
			   r.role_name,
			   r.role_key,
			   r.role_sort,
			   r.data_scope,
			   r.status as role_status
		from tenant_user u
				 left join tenant_dept d on u.dept_id = d.dept_id
				 left join tenant_user_role ur on u.user_id = ur.user_id
				 left join tenant_role r on r.role_id = ur.role_id
	</sql>

	<select id="selectAdminUsersByTenantId" resultMap="TenantUserResult">
		<include refid="selectUserVo"/>
		where u.del_flag = '0'
		<if test="tenantId != null and tenantId != 0">
			and u.tenant_id = #{tenantId}
		</if>
		and r.role_key = "admin"
		order by u.create_time desc
	</select>

    <select id="selectUserList" parameterType="TenantUser" resultMap="TenantUserResult">
		select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.pwd, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time,
		u.remark, u.tenant_id, u.is_all_make_code,
		d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status
		from tenant_user u
		left join tenant_dept d on u.dept_id = d.dept_id
		where u.del_flag = '0'
		<if test="userId != null and userId != 0">
			AND u.user_id = #{userId}
		</if>
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="nickName != null and nickName != ''">
			AND u.nick_name like concat('%', #{nickName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="params != null and params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
		</if>
		<if test="params != null and params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
		</if>
		<if test="deptId != null and deptId != 0">
			AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM tenant_dept t WHERE
			find_in_set(#{deptId}, ancestors) ))
		</if>
		<if test="tenantId != null and tenantId != ''">
			AND u.tenant_id = #{tenantId}
		</if>
		<!--<if test="startTime != null and startTime != ''">
			and u.create_time >= #{startTime}
		</if>
		<if test="endTime != null and endTime != ''">
			and u.create_time &lt;= #{endTime}
		</if>-->
		<!-- 数据范围过滤 -->
		<if test="params != null and params.dataScope != null">
			${params.dataScope}
		</if>
	</select>

	<select id="selectAllocatedList" parameterType="TenantUser" resultMap="TenantUserResult">
		select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time, u.tenant_id, u.is_all_make_code,
		from tenant_user u
		left join tenant_dept d on u.dept_id = d.dept_id
		left join tenant_user_role ur on u.user_id = ur.user_id
		left join tenant_role r on r.role_id = ur.role_id
		where u.del_flag = '0' and r.role_id = #{roleId}
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>

	<select id="selectUnallocatedList" parameterType="TenantUser" resultMap="TenantUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time, u.tenant_id
	    from tenant_user u
			 left join tenant_dept d on u.dept_id = d.dept_id
			 left join tenant_user_role ur on u.user_id = ur.user_id
			 left join tenant_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
	    and u.user_id not in (select u.user_id from tenant_user u inner join tenant_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="tenantId != null and tenantId != ''">
			AND u.tenant_id = #{tenantId}
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>

	<select id="selectUserByUserName" parameterType="String" resultMap="TenantUserResult">
		select u.user_id,
			   u.dept_id,
			   u.user_name,
			   u.nick_name,
			   u.email,
			   u.avatar,
			   u.phonenumber,
			   u.password,
			   u.pwd,
			   u.sex,
			   u.status,
			   u.del_flag,
			   u.login_ip,
			   u.login_date,
			   u.create_by,
			   u.create_time,
			   u.remark,
			   u.tenant_id,
			   u.default_project,
			   u.is_all_make_code,
			   d.dept_id,
			   d.parent_id,
			   d.ancestors,
			   d.dept_name,
			   d.order_num,
			   d.leader,
			   d.status as dept_status,
			   r.role_id,
			   r.role_name,
			   r.role_key,
			   r.role_sort,
			   r.data_scope,
			   r.status as role_status,
			   ci.customer_name
		from tenant_user u
				 left join tenant_dept d on u.dept_id = d.dept_id
				 left join tenant_user_role ur on u.user_id = ur.user_id
				 left join tenant_role r on r.role_id = ur.role_id
				 left join customer_info ci on ci.id = u.tenant_id
		where u.user_name = #{userName}
		and u.del_flag = '0'
		limit 1
	</select>

	<select id="selectUserById" parameterType="Long" resultMap="TenantUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>

	<select id="checkUserNameUnique" parameterType="String" resultType="int">
		select count(1) from tenant_user where user_name = #{userName} and tenant_id &gt; 0 and del_flag = 0 limit 1
	</select>

	<select id="checkNickNameUnique" resultType="int">
		select count(1) from tenant_user where nick_name = #{nickName} and tenant_id &gt; 0 and del_flag = 0
		<if test="userId != null and userId != 0 and userId != -1">and user_id != #{userId}</if>
		and tenant_id = #{tenantId}
		limit 1
	</select>

	<select id="checkPhoneUnique" parameterType="String" resultMap="TenantUserResult">
		select user_id, phonenumber,tenant_id from tenant_user where phonenumber = #{phonenumber} and tenant_id &gt; 0 and del_flag = 0 limit 1
	</select>

	<select id="checkPhoneAbsoluteUnique" parameterType="String" resultMap="TenantUserResult">
		<include refid="selectUserVo"/>
		 where u.phonenumber = #{phonenumber}  and u.del_flag = 0 limit 1
	</select>

	<select id="checkEmailUnique" parameterType="String" resultMap="TenantUserResult">
		select user_id, email from tenant_user where email = #{email} and del_flag = 0 limit 1
	</select>

	<select id="selectUserByTenantId" resultType="com.zly.project.tenant.domain.TenantUser">
		select user_id as userId, tenant_id as tenantId,phonenumber from tenant_user where tenant_id = #{customerId} and del_flag = 0 limit 1
	</select>

	<select id="selectUserByUserId" parameterType="Long" resultMap="TenantUserResult">
		select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.pwd, u.sex, u.status, u.del_flag,
		 	   u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,u.tenant_id,u.default_project
		from tenant_user u
		where u.user_id = #{userId}
		<if test="delFlag == null">
			and u.del_flag = 0
		</if>
		<if test="delFlag != null">
			and u.del_flag = #{delFlag}
		</if>
	</select>
	<select id="selectAllUserList" resultType="com.zly.project.tenant.domain.TenantUser">
		select u.user_id userId
		from tenant_user u
		where u.del_flag = '0'
	</select>

	<select id="getAllUserList" parameterType="TenantUser" resultMap="TenantUserResult">
		select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.pwd, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time,
		u.remark,u.is_all_make_code,
		u.tenant_id, d.dept_name, d.leader
		from tenant_user u
		left join tenant_dept d on u.dept_id = d.dept_id
		<where>
		<if test="userId != null and userId != 0">
			AND u.user_id = #{userId}
		</if>
		<if test="userName != null and userName != ''">
			AND u.user_name = #{userName}
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber = #{phonenumber}
		</if>
		<if test="deptId != null and deptId != 0">
			AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM tenant_dept t WHERE find_in_set(#{deptId}, ancestors) ))
		</if>
		<if test="tenantId != null and tenantId != ''">
			AND u.tenant_id = #{tenantId}
		</if>
		<if test="startTime != null and startTime != ''">
			and u.create_time >= #{startTime}
		</if>
		<if test="endTime != null and endTime != ''">
			and u.create_time &lt;= #{endTime}
		</if>
		<if test="delFlag != null and delFlag != ''">
			and u.del_flag = #{delFlag}
		</if>

		</where>
	</select>
	<select id="selectUserByPhoneNumber" resultType="com.zly.project.tenant.domain.TenantUser">
		select u.user_id userId
		from tenant_user u
		where u.del_flag = '0' and u.phonenumber = #{phoneNumber}
	</select>
	<select id="selectUserListByTenantId" resultType="com.zly.project.tenant.domain.TenantUser">
		select u.user_id userId,u.nick_name nickName, u.user_name userName
		from tenant_user u
		where u.del_flag = '0' and u.tenant_id = #{tenantId}
	</select>
	<select id="countUser" resultType="java.lang.Integer">
		select count(1) from tenant_user u
		where u.del_flag = '0'
		<if test="userId != null and userId != 0">
			AND u.user_id = #{userId}
		</if>
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="nickName != null and nickName != ''">
			AND u.nick_name like concat('%', #{nickName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="params != null and params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
		</if>
		<if test="params != null and params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
		</if>
		<if test="tenantId != null and tenantId != ''">
			AND u.tenant_id = #{tenantId}
		</if>
		<if test="startTime != null and startTime != ''">
			and u.create_time >= #{startTime}
		</if>
		<if test="endTime != null and endTime != ''">
			and u.create_time &lt;= #{endTime}
		</if>
	</select>
	<select id="selectUsersByTenantId" resultMap="TenantUserResult">
		<include refid="selectUserVo"/>
		where u.del_flag = '0' and u.tenant_id = #{tenantId}
		order by u.create_time desc
	</select>
	<select id="selectHasAllProjectsUserByIds" resultType="java.lang.Long">
		select  user_id from tenant_user
		where default_project = 1  and user_id in
		<foreach collection="userIds" item="userId" open="(" separator="," close=")">
			#{userId}
		</foreach>
	</select>

	<insert id="insertUser" parameterType="TenantUser" useGeneratedKeys="true" keyProperty="userId">
 		insert into tenant_user(
 			<if test="userId != null and userId != 0">user_id,</if>
 			<if test="deptId != null and deptId != 0">dept_id,</if>
 			<if test="userName != null and userName != ''">user_name,</if>
 			<if test="nickName != null and nickName != ''">nick_name,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="avatar != null and avatar != ''">avatar,</if>
 			<if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
 			<if test="sex != null and sex != ''">sex,</if>
 			<if test="password != null and password != ''">password,</if>
 			<if test="pwd != null and pwd != ''">pwd,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="remark != null and remark != ''">remark,</if>
			<if test="tenantId != null and tenantId != ''">tenant_id,</if>
			<if test="defaultProject != null">default_project,</if>
			<if test="waybillCopyType != null">waybill_copy_type,</if>
 			create_time
 		)values(
 			<if test="userId != null and userId != ''">#{userId},</if>
 			<if test="deptId != null and deptId != ''">#{deptId},</if>
 			<if test="userName != null and userName != ''">#{userName},</if>
 			<if test="nickName != null and nickName != ''">#{nickName},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			<if test="avatar != null and avatar != ''">#{avatar},</if>
 			<if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
 			<if test="sex != null and sex != ''">#{sex},</if>
 			<if test="password != null and password != ''">#{password},</if>
 			<if test="pwd != null and pwd != ''">#{pwd},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
			<if test="tenantId != null and tenantId != ''">#{tenantId},</if>
			<if test="defaultProject != null">#{defaultProject},</if>
			<if test="waybillCopyType != null">#{waybillCopyType},</if>
 			sysdate()
 		)
	</insert>

	<update id="updateUser" parameterType="TenantUser">
 		update tenant_user
 		<set>
 			<if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
 			<if test="userName != null and userName != ''">user_name = #{userName},</if>
 			<if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
 			<if test="email != null ">email = #{email},</if>
 			<if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
 			<if test="sex != null and sex != ''">sex = #{sex},</if>
 			<if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
 			<if test="password != null and password != ''">password = #{password},</if>
 			<if test="pwd != null and pwd != ''">pwd = #{pwd},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
 			<if test="loginDate != null">login_date = #{loginDate},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			<if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			<if test="tenantId != null">tenant_id = #{tenantId},</if>
 			<if test="delFlag != null">del_flag = #{delFlag},</if>
			<if test="createTime != null"> create_time = #{createTime},</if>
			<if test="defaultProject != null"> default_project = #{defaultProject},</if>
			<if test="waybillCopyType != null"> waybill_copy_type = #{waybillCopyType},</if>
 			update_time = sysdate()
 		</set>
 		where user_id = #{userId}
	</update>

	<update id="updateUserStatus" parameterType="TenantUser">
 		update tenant_user set status = #{status} where user_id = #{userId}
	</update>

	<update id="updateUserAvatar" parameterType="TenantUser">
 		update tenant_user set avatar = #{avatar} where user_name = #{userName}
	</update>

	<update id="resetUserPwd" parameterType="TenantUser">
 		update tenant_user set password = #{password},pwd = #{pwd} where user_name = #{userName}
	</update>

	<delete id="deleteUserById" parameterType="Long">
 		update tenant_user set del_flag = '2' where user_id = #{userId}
 	</delete>

 	<delete id="deleteUserByIds" parameterType="Long">
 		update tenant_user set del_flag = '2' where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach>
 	</delete>

	<update id="updateUserDelFlag">
		UPDATE tenant_user
		SET del_flag = 2
		WHERE user_id IN (SELECT user_id from tenant_user_role where role_id=(SELECT role_id FROM tenant_role WHERE role_key = 'admin' AND tenant_id =  #{customerId}))
		and user_id !=#{userId}
	</update>
	<update id="updateUserStatusByUserIds">
		update tenant_user set status = 0 where user_id in
		<foreach collection="shipTenantUserIds" item="userId" open="(" separator="," close=")">
			#{userId}
		</foreach>
	</update>

	<select id="queryDelFlagUser" resultType="java.lang.Long">
		SELECT
			user_id
		FROM
			tenant_user
		WHERE
				user_id IN (
				SELECT
					user_id
				FROM
					tenant_user_role
				WHERE
						role_id = (
						SELECT
							role_id
						FROM
							tenant_role
						WHERE
							role_key = 'admin'
						  AND tenant_id = #{customerId}))
		  AND user_id != #{userId}

	</select>

	<select id="queryUser" resultType="clickLoginRes">
		SELECT
		tu.user_id AS userId,
		tu.user_name AS userName,
		tu.nick_name AS nickName,
		tu.phonenumber AS phone,
		tu.status AS state,
		tu.login_date AS date,
		GROUP_CONCAT(tr.role_id) AS roleId,
		GROUP_CONCAT(tr.role_name) AS roleName
		FROM
		tenant_user tu
		JOIN
		tenant_user_role tur ON tu.user_id = tur.user_id
		JOIN
		tenant_role tr ON tur.role_id = tr.role_id
		WHERE
		tu.status = '0' AND
		tu.del_flag = '0'
		<if test="customerId != null">AND tu.tenant_id = #{customerId}</if>
		<if test="userName != null and userName != ''">AND tu.user_name LIKE '%${userName}%'</if>
		<if test="nickName != null and nickName != ''">AND tu.nick_name LIKE '%${nickName}%'</if>
        GROUP BY
        tu.user_id, tu.user_name, tu.nick_name, tu.phonenumber, tu.status, tu.login_date
		ORDER BY
		CASE
		WHEN tr.role_name = '管理员' THEN 0
		ELSE 1
		END, tr.role_name
	</select>
    <select id="getEnterpriseInfo" resultType="com.zly.project.tenant.domain.res.EnterpriseInfoRes">
		select ci.id customerId,ci.customer_name customerName,ci.freight_forwarder_id freightForwarderId
		from tenant_user tu
		left join customer_info ci on tu.tenant_id = ci.id
		where tu.phonenumber = #{phone}
		  and tu.status = 0 and ci.state = 0 and tu.del_flag = 0 and ci.id not in (select customer_id from freight_forwarder_info where credit_code not in ('91420981MA7MPL9C92'))
		order by login_date desc
	</select>

	<select id="checkPhoneAndCustomerID" resultMap="TenantUserResult">
		<include refid="selectUserVo"/>
		where u.phonenumber = #{phonenumber}  and u.tenant_id = #{tenantId} and u.del_flag = 0 limit 1
	</select>
	<select id="selectTenantUserByCustomerIds" resultMap="TenantUserResult">
		<include refid="selectUserVo"/>
		where u.tenant_id in
		<foreach collection="customerIds" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</select>
    <select id="selectUserByIds" resultMap="TenantUserResult">
		<include refid="selectUserVo"/>
		where u.user_id in
		<foreach collection="userIds" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</select>
	<select id="selectUserAdminByCustomerId" resultMap="TenantUserResult">
		<include refid="selectUserVo"/>
		where u.tenant_id = #{customerId}
		and r.role_key = 'admin' and u.del_flag = 0
	</select>
	<select id="selectUserListEx" resultMap="TenantUserResult">
		select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.pwd, u.sex,
		u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, u.tenant_id,
		d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status
		from tenant_user u
		left join tenant_dept d on u.dept_id = d.dept_id
		where u.del_flag = '0'
		<if test="userId != null and userId != 0">
			AND u.user_id = #{userId}
		</if>
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="nickName != null and nickName != ''">
			AND u.nick_name like concat('%', #{nickName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="params != null and params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
		</if>
		<if test="params != null and params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
		</if>
		<if test="deptId != null and deptId != 0">
			AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM tenant_dept t WHERE
			find_in_set(#{deptId}, ancestors) ))
		</if>
		<if test="tenantId != null and tenantId != ''">
			AND u.tenant_id = #{tenantId}
		</if>
		<if test="startTime != null and startTime != ''">
			and u.create_time >= #{startTime}
		</if>
		<if test="endTime != null and endTime != ''">
			and u.create_time &lt;= #{endTime}
		</if>
		<!-- 数据范围过滤 -->
		<if test="params != null and params.dataScope != null">
			${params.dataScope}
		</if>
		order by u.create_time desc
	</select>

	<delete id="mergeShippers">
		delete from tenant_user where tenant_id = #{fromId}
	</delete>

	<select id="selectHasAllProjectsUserByTenantId" resultType="long">
		select user_id
		from tenant_user
		where tenant_id = #{customerId}
		  and default_project = 1
		  and status = 0
		  and del_flag = 0
	</select>

	<update id="updateIsAllMakeCode">
		update tenant_user
		set is_all_make_code = #{type}
		where user_id = #{userId}
	</update>

	<update id="updateUserLastEmail">
		update tenant_user
		set last_email = #{email}
		where user_id = #{userId}
	</update>

	<select id="getLastEmail" resultType="string">
		select last_email
		from tenant_user
		where user_id = #{userId}
	</select>
</mapper>
