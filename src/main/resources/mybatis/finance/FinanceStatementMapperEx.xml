<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.finance.statement.mapper.FinanceStatementMapperEx">

    <!-- 开票申请单列表 -->
    <select id="selectStatementList" resultType="com.zly.project.finance.statement.domain.res.FinanceStatementRes">
        SELECT
        f.id,
        f.statement_no AS statementNo,
        f.customer_id AS customerId,
        c.customer_name AS customerName,
        seller.customer_name AS sellCustomerName,
        f.waybill_amount AS waybillAmount,
        f.invoice_fare AS invoiceFare,
        f.state,
        f.remark,
        f.mark_status AS markStatus,
        f.profit_state AS profitState,
        f.create_by AS createBy,
        f.create_time AS createTime,
        f.is_sales AS isSales,
        f.update_by AS updateBy,
        f.update_time AS updateTime,
        f.audit_customer_id AS sellCustomerId,
        f.email,
        f.payment_date as paymentDate,
        f.operation_reject_reason as operationRejectReason,
        f.finance_reject_reason as financeRejectReason,
        f.operation_audit_time as operationAuditTime,
        f.finance_audit_time as financeAuditTime,
        f.operation_audit_by as operationAuditBy,
        f.finance_audit_by as financeAuditBy,
        f.payment_type as paymentType,
        f.send_dd_state as sendDdState,
        f.pay_return_state as payReturnState,
        c.bill_head AS billHead,
        c.tax_no AS taxNo,
        c.unit_address AS unitAddress,
        c.telephone,
        c.bank,
        c.bank_account AS bankAccount,
        f.contract_name AS contractName,
        GROUP_CONCAT(DISTINCT fc.id) AS contractId,
        GROUP_CONCAT(DISTINCT fsw.waybill_id) AS waybillIds,
        f.audit_time AS auditTime,
        f.audit_type AS auditType,
        f.instance_id AS instanceId,
        f.err_msg AS errMsg,
        f.file_up_state AS fileUpState,
        f.risk_result_grade AS riskResultGrade,
        fsb.invoice_number AS invoiceNumber,
        COALESCE(fsb.invoice_period, f.mark_time) AS invoicePeriod
        FROM
        finance_statement f
        LEFT JOIN finance_statement_contract_relation scr ON f.id = scr.statement_id
        INNER JOIN framework_contract fc ON scr.contract_id = fc.id
        LEFT JOIN framework_contract_subchain fcs ON fc.id = fcs.contract_id
        LEFT JOIN customer_info c ON f.customer_id = c.id
        LEFT JOIN customer_info seller ON f.audit_customer_id = seller.id
        LEFT JOIN finance_statement_bill fsb ON fsb.statement_id = f.id
        LEFT JOIN finance_statement_waybill fsw ON fsw.statement_id = f.id
        WHERE f.statement_direction = 1
        <if test="contractIds != null and contractIds.size() > 0">
            and fc.id IN
            <foreach collection="contractIds" item="contractId" open="(" close=")" separator=",">
                #{contractId}
            </foreach>
        </if>
        <if test="statementIds != null and statementIds.size() > 0">
            and f.id IN
            <foreach collection="statementIds" item="statementId" open="(" close=")" separator=",">
                #{statementId}
            </foreach>
        </if>
        <if test="invoiceNumber != null and invoiceNumber != ''">
            and fsb.invoice_number = #{invoiceNumber}
        </if>
        <if test="invoicePeriodStart != null">
            AND DATE(COALESCE(fsb.invoice_period, f.mark_time)) >= #{invoicePeriodStart}
        </if>
        <if test="invoicePeriodEnd != null">
            AND DATE(COALESCE(fsb.invoice_period, f.mark_time)) &lt;= #{invoicePeriodEnd}
        </if>
        <if test="businessModelId != null">
            and fc.business_model_id = #{businessModelId}
        </if>
        <if test="customerId != null">
            and f.customer_id = #{customerId}
        </if>
        <if test="auditCustomerId != null">
            and f.audit_customer_id = #{auditCustomerId}
            and fcs.customer_id = #{auditCustomerId}
        </if>
        <if test="auditFreightForwarderId != null">
            and f.audit_freight_forwarder_id = #{auditFreightForwarderId} and profit_state in (1,2,3) and send_dd_state
            != 1
        </if>
        <if test="frameworkContractId != null">
            and scr.contract_id = #{frameworkContractId}
        </if>
        <if test="billHead != null and billHead != ''">
            and c.customer_name like concat('%', #{billHead}, '%')
        </if>
        <if test="statementNo != null and statementNo != ''">
            and f.statement_no = #{statementNo}
        </if>
        <if test="markStatus != null ">
            and f.mark_status = #{markStatus}
        </if>
        <if test="createTimeStart != null">
            and f.create_time &gt;= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null">
            and f.create_time &lt;= #{createTimeEnd}
        </if>
        <if test="state != null">
            AND f.state = #{state}
        </if>
        <if test="auditStartTime != null and auditStartTime != ''">
            and DATE(f.audit_time) &gt;= #{auditStartTime}
        </if>
        <if test="auditEndTime != null and auditEndTime != ''">
            and DATE(f.audit_time) &lt;= #{auditEndTime}
        </if>
        <if test="operationAuditTimeStart != null and operationAuditTimeStart != ''">
            and DATE(f.operation_audit_time) &gt;= #{operationAuditTimeStart}
        </if>
        <if test="operationAuditTimeEnd != null and operationAuditTimeEnd != ''">
            and DATE(f.operation_audit_time) &lt;= #{operationAuditTimeEnd}
        </if>
        <if test="financeAuditTimeStart != null and financeAuditTimeStart != ''">
            and DATE(f.finance_audit_time) &gt;= #{financeAuditTimeStart}
        </if>
        <if test="financeAuditTimeEnd != null and financeAuditTimeEnd != ''">
            and DATE(f.finance_audit_time) &lt;= #{financeAuditTimeEnd}
        </if>
        <if test="operationAuditBy != null and operationAuditBy != ''">
            and f.operation_audit_by = #{operationAuditBy}
        </if>
        <if test="financeAuditBy != null and financeAuditBy != ''">
            and f.finance_audit_by = #{financeAuditBy}
        </if>
        <if test="profitState != null">
            and f.profit_State = #{profitState}
        </if>
        <if test="stateList != null">
            AND f.state IN
            <foreach collection="stateList" separator="," open="(" close=")" item="s">
                #{s}
            </foreach>
        </if>
        <if test="resource != null">
            <choose>
                <when test="resource == 3">
                    and fcs.is_recorder = 0 <!-- 托运人查看自己入单的 -->
                    -- and f.customer_id=fcs.customer_id
                </when>
                <otherwise>
                    <choose>
                        <when test="resource == 8">
                            and fcs.is_recorder = 1 <!-- 金融端查看非自己录单的 -->
                            -- and f.customer_id=fcs.customer_id
                        </when>
                        <otherwise>
                            and fcs.is_recorder = 0 <!-- 网络货运端看全部 -->
                        </otherwise>
                    </choose>
                </otherwise>
            </choose>
        </if>
        GROUP BY f.id
        ORDER BY f.create_time DESC
    </select>

    <select id="countStatement" resultType="int">
        select count(1)
        from finance_statement f
        WHERE f.statement_direction = 1
          AND f.audit_freight_forwarder_id = #{freightForwarderId}
          AND (f.state = 3 or f.state = 9)
    </select>

    <!-- 开票列表 -->
    <select id="financeStatementList" parameterType="FinanceStatementReq"
            resultType="com.zly.project.finance.statement.domain.res.FinanceStatementRes">
        SELECT
        f.id,
        f.statement_no statementNo,
        f.customer_id customerId,
        c.customer_name customerName,
        f.waybill_amount waybillAmount,
        f.receive_fare receiveFare,
        f.invoice_fare invoiceFare,
        f.state,
        f.mark_status markStatus,
        f.profit_state profitState,
        f.create_by createBy,
        f.create_time createTime,
        f.update_by updateBy,
        f.update_time updateTime,
        c.bill_head billHead,
        c.tax_no taxNo,
        c.unit_address unitAddress,
        c.telephone,
        c.bank,
        c.bank_account bankAccount,
        f.contract_name contractName,
        f.contract_id contractId,
        fc.last_customer_id lastCustomerId,
        f.audit_time auditTime,
        fc.last_customer_name lastCustomerName
        FROM finance_statement f
        LEFT JOIN customer_info c ON f.customer_id = c.id
        LEFT JOIN framework_contract fc ON f.contract_id = fc.id
        WHERE f.statement_direction = 1
        <if test="id != null">
            and f.id = #{id}
        </if>
        <if test="customerId != null">
            and f.customer_id = #{customerId}
        </if>
        <if test="auditCustomerId != null">
            and f.audit_customer_id = #{auditCustomerId}
        </if>
        <if test="auditFreightForwarderId != null">
            and f.audit_freight_forwarder_id = #{auditFreightForwarderId}
        </if>
        <if test="frameworkContractId != null">
            and f.contract_id = #{frameworkContractId}
        </if>
        <if test="billHead != null and billHead != ''">
            and c.bill_head like concat('%',#{billHead},'%')
        </if>
        <if test="statementNo != null and statementNo != ''">
            and f.statement_no = #{statementNo}
        </if>
        <if test="taxNo != null and taxNo != ''">
            and c.tax_no = #{taxNo}
        </if>
        <if test="markStatus != null ">
            and f.mark_status = #{markStatus}
        </if>
        <if test="createTimeStart != null and createTimeStart != ''">
            and f.create_time <![CDATA[>=]]> #{createTimeStart}
        </if>
        <if test="createTimeEnd != null and createTimeEnd != ''">
            and f.create_time <![CDATA[<=]]> #{createTimeEnd}
        </if>
        <if test="state != null">
            <choose>
                <when test="state == 2">
                    and f.state in (2,6)<!-- 被驳回、审批中 -->
                </when>
                <otherwise>
                    <choose>
                        <when test="state == 1">
                            and f.state in (0,1)<!-- 被驳回、已完成(待申请开票) -->
                        </when>
                        <otherwise>
                            and f.state = #{state}
                        </otherwise>
                    </choose>
                </otherwise>
            </choose>
        </if>
    </select>

    <update id="invoiceApproval">
        <if test="ids != null and ids.length > 0">
            update finance_statement set state = #{state},update_by = #{userName},audit_time = #{auditTime} where id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <update id="profitSign">
        <if test="ids != null and ids.size > 0">
            update finance_statement set profit_state = 1,remark = #{remark} where id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <!-- 待审批的开票申请单（未审批） -->
    <select id="countStatementForAudit" resultType="java.lang.Long">
        SELECT COUNT(f.id)
        FROM finance_statement f
        WHERE f.state = 2
        AND f.statement_direction = 1
        <if test="customerId != null">
            AND f.customer_id = #{customerId}
        </if>
        <if test="auditCustomerId != null">
            AND f.audit_customer_id = #{auditCustomerId}
        </if>
        <if test="auditFreightForwarderId != null">
            AND f.audit_freight_forwarder_id = #{auditFreightForwarderId}
        </if>
    </select>

    <select id="selectFinanceStatementInfoById"
            resultType="com.zly.project.finance.statement.domain.FinanceStatementEx">
        SELECT fs.id                  AS id,
               fs.statement_no        AS statementNo,
               fs.customer_id         AS customerId,
               fs.customer_name       AS customerName,
               fs.contract_id         AS contractid,
               fs.contract_name       AS contractName,
               fs.waybill_amount      AS waybillAmount,
               fs.receive_fare        AS receiveFare,
               fs.invoice_fare        AS invoiceFare,
               fs.state               AS state,
               fs.mark_status         AS markStatus,
               fs.profit_state        AS profitState,
               fs.remark              AS remark,
               fs.create_by           AS createBy,
               fs.create_time         AS createTime,
               fs.update_by           AS updateBy,
               fs.update_time         AS updateTime,
               fs.audit_type          AS auditType,
               fs.instance_id         AS instanceId,
               fs.is_sales            AS isSales,
               fs.sales_name          AS salesName,
               fs.transportation_name AS transportationName,
               ci.bill_head           AS billHead,
               ci.tax_no              AS taxNo,
               ci.telephone           AS telephone,
               ci.bank                AS bank,
               ci.bank_account        AS bankAccount,
               ci.unit_address        AS unitAaddress
        FROM finance_statement fs
                 LEFT JOIN customer_info ci ON ci.id = fs.customer_id
        WHERE fs.id = #{id}
    </select>

    <!-- 获取签约主体托运人与上游客户对账记录 -->
    <select id="selectUpstreamStatementList" resultType="StatementRecordRes">
        SELECT fs.id,
        fs.statement_no AS statementCode,
        fs.contract_name AS contractName,
        fs.waybill_amount AS waybillCount,
        fs.receive_fare AS beforeReceiveFare,
        fs.invoice_fare AS afterReceiveFare,
        fs.remark AS remark,
        fs.state as state,
        fs.contract_id as contractId,
        fs.create_time AS statementTime
        FROM finance_statement fs
        LEFT JOIN framework_contract fc ON fc.id = fs.contract_id
        WHERE fs.statement_direction = 2 and fs.state != -1
        <if test="customerId != null">
            AND fs.customer_id = #{customerId}
            AND fc.tenant_id = #{customerId}
        </if>
        <if test="businessModelId != null">
            AND fc.business_model_id = #{businessModelId}
        </if>
        <if test="contractId != null">
            AND fs.contract_id = #{contractId}
        </if>
        <if test="statementNo != null and statementNo !=''">
            AND fs.statement_no = #{statementNo}
        </if>
        <if test="statementId != null ">
            and fs.id = #{statementId}
        </if>
        ORDER BY fs.create_time DESC
    </select>

    <!-- 获取签约主体托运人与上游客户开票标记记录 -->
    <select id="selectUpstreamMarkStatementList" resultType="billMarkRecordRes">
        SELECT fs.id,
        fs.statement_no AS statementCode,
        fs.contract_name AS contractName,
        fs.mark_status AS markStatus,
        fs.waybill_amount AS waybillCount,
        fs.invoice_fare AS invoiceFare,
        lc.bill_head AS billHead,
        lc.tax_no AS taxNo,
        fs.create_time AS statementTime
        FROM finance_statement fs
        LEFT JOIN framework_contract fc ON fc.id = fs.contract_id
        LEFT JOIN last_customer_info lc ON lc.id = fc.last_customer_id
        WHERE fs.statement_direction = 2 and fs.state > 0
        <if test="customerId != null">
            AND fs.customer_id = #{customerId}
            AND fc.tenant_id = #{customerId}
        </if>
        <if test="businessModelId != null">
            AND fc.business_model_id = #{businessModelId}
        </if>
        <if test="contractId != null">
            AND fs.contract_id = #{contractId}
        </if>
        <if test="markStatus != null ">
            and fs.mark_status = #{markStatus}
        </if>
        <if test="statementNo != null and statementNo !='' ">
            and fs.statement_no = #{statementNo}
        </if>
        <if test="statementId != null ">
            and fs.id = #{statementId}
        </if>
        ORDER BY fs.mark_status asc,fs.create_time asc
    </select>

    <select id="findWaybillByStatementId" resultType="com.zly.project.transport.waybill.domain.res.WaybillRes">
        SELECT w.shipping_note_number                                    as shippingNoteNumber,
               fsw.vehicle_number                                        AS vehicleNumber,
               fsw.description_of_goods                                  as descriptionOfGoods,
               CONCAT(a.province, a.city, a.area, a.place_of_loading)    as consignorAddress,
               CONCAT(b.province, b.city, b.area, b.goods_receipt_place) as consigneeAddress
        from finance_statement_waybill fsw
                 inner join waybill w on fsw.waybill_id = w.id
                 inner join consignor_info a on w.consignor_id = a.id
                 inner join consignee_info b on w.consignee_id = b.id
        where fsw.statement_id = #{statementId}
    </select>

    <!--    <select id="getbillCount" resultType="integer">-->
    <!--        SELECT COUNT(1)-->
    <!--        FROM (SELECT f.id-->
    <!--              FROM finance_statement f-->
    <!--                       INNER JOIN (SELECT fsw.statement_id,-->
    <!--                                          SUM(w.pay_fare) AS total_pay_fare-->
    <!--                                   FROM finance_statement_waybill fsw-->
    <!--                                            INNER JOIN waybill w ON fsw.waybill_id = w.id-->
    <!--                                   GROUP BY fsw.statement_id) subq ON f.id = subq.statement_id-->
    <!--                       INNER JOIN finance_statement_contract_relation scr ON f.id = scr.statement_id-->
    <!--                       INNER JOIN framework_contract fc ON scr.contract_id = fc.id-->
    <!--                       INNER JOIN framework_contract_subchain fcs ON fc.id = fcs.contract_id-->
    <!--              WHERE f.statement_direction = 1-->
    <!--                AND f.audit_freight_forwarder_id = #{freightForwarderId}-->
    <!--                AND (f.state = 2 or f.state = 7)-->
    <!--              GROUP BY f.id) a-->
    <!--    </select>-->

    <select id="getbillCount" resultType="integer">
        SELECT count(0)
        FROM (SELECT f.id,
                     f.statement_no                            AS statementNo,
                     f.customer_id                             AS customerId,
                     c.customer_name                           AS customerName,
                     seller.customer_name                      AS sellCustomerName,
                     f.waybill_amount                          AS waybillAmount,
                     f.invoice_fare                            AS invoiceFare,
                     f.state,
                     f.remark,
                     f.mark_status                             AS markStatus,
                     f.profit_state                            AS profitState,
                     f.create_by                               AS createBy,
                     f.create_time                             AS createTime,
                     f.update_by                               AS updateBy,
                     f.update_time                             AS updateTime,
                     f.audit_customer_id                       AS sellCustomerId,
                     f.email,
                     f.payment_date                            AS paymentDate,
                     f.operation_reject_reason                 AS operationRejectReason,
                     f.finance_reject_reason                   AS financeRejectReason,
                     f.operation_audit_time                    AS operationAuditTime,
                     f.finance_audit_time                      AS financeAuditTime,
                     f.operation_audit_by                      AS operationAuditBy,
                     f.finance_audit_by                        AS financeAuditBy,
                     f.payment_type                            AS paymentType,
                     f.send_dd_state                           AS sendDdState,
                     f.pay_return_state                        AS payReturnState,
                     c.bill_head                               AS billHead,
                     c.tax_no                                  AS taxNo,
                     c.unit_address                            AS unitAddress,
                     c.telephone,
                     c.bank,
                     c.bank_account                            AS bankAccount,
                     f.contract_name                           AS contractName,
                     GROUP_CONCAT(DISTINCT fc.id)              AS contractId,
                     GROUP_CONCAT(DISTINCT fsw.waybill_id)     AS waybillIds,
                     f.audit_time                              AS auditTime,
                     f.audit_type                              AS auditType,
                     f.instance_id                             AS instanceId,
                     f.err_msg                                 AS errMsg,
                     f.file_up_state                           AS fileUpState,
                     f.risk_result_grade                       AS riskResultGrade,
                     fsb.invoice_number                        AS invoiceNumber,
                     COALESCE(fsb.invoice_period, f.mark_time) AS invoicePeriod
              FROM finance_statement f
                       LEFT JOIN finance_statement_contract_relation scr ON f.id = scr.statement_id
                       INNER JOIN framework_contract fc ON scr.contract_id = fc.id
                       LEFT JOIN framework_contract_subchain fcs ON fc.id = fcs.contract_id
                       LEFT JOIN customer_info c ON f.customer_id = c.id
                       LEFT JOIN customer_info seller ON f.audit_customer_id = seller.id
                       LEFT JOIN finance_statement_bill fsb ON fsb.statement_id = f.id
                       LEFT JOIN finance_statement_waybill fsw ON fsw.statement_id = f.id
              WHERE f.statement_direction = 1
                AND f.audit_freight_forwarder_id = #{freightForwarderId}
                AND profit_state IN (1, 2, 3)
                AND send_dd_state != 1
                AND f.state IN (2, 7)
                AND fcs.is_recorder = 0
              GROUP BY f.id) table_count
    </select>


    <select id="selectStatemenMoneyTotal"
            resultType="com.zly.project.finance.statement.domain.res.FinanceStatementRes">
        SELECT
        SUM(a.receiveFare) totalReceiveFare,
        SUM(a.profitFare) totalProfitFare,
        SUM(a.invoiceFare) totalInvoiceFare
        FROM
        (
        SELECT
        COALESCE (subq.total_pay_fare, 0) AS receiveFare,
        (
        f.invoice_fare - COALESCE (subq.total_pay_fare, 0)
        ) AS profitFare,
        f.invoice_fare AS invoiceFare
        FROM
        finance_statement f
        LEFT JOIN (
        SELECT
        fsw.statement_id,
        SUM(w.pay_fare) AS total_pay_fare
        FROM
        finance_statement_waybill fsw
        INNER JOIN waybill w ON fsw.waybill_id = w.id
        <where>
            <if test="statementIds != null and statementIds.size() > 0">
                and fsw.statement_id IN
                <foreach collection="statementIds" item="statementId" open="(" close=")" separator=",">
                    #{statementId}
                </foreach>
            </if>
        </where>
        GROUP BY
        fsw.statement_id
        ) subq ON f.id = subq.statement_id
        LEFT JOIN finance_statement_contract_relation scr ON f.id = scr.statement_id
        INNER JOIN framework_contract fc ON scr.contract_id = fc.id
        LEFT JOIN framework_contract_subchain fcs ON fc.id = fcs.contract_id
        LEFT JOIN finance_statement_bill fsb ON fsb.statement_id = f.id
        WHERE
        f.statement_direction = 1
        <if test="statementId != null">
            AND f.id = #{statementId}
        </if>
        <if test="invoiceNumber != null and invoiceNumber != ''">
            and fsb.invoice_number = #{invoiceNumber}
        </if>
        <if test="invoicePeriodStart != null">
            AND DATE(COALESCE(fsb.invoice_period, f.mark_time)) >= #{invoicePeriodStart}
        </if>
        <if test="invoicePeriodEnd != null">
            AND DATE(COALESCE(fsb.invoice_period, f.mark_time)) &lt;= #{invoicePeriodEnd}
        </if>
        <if test="contractIds != null and contractIds.size() > 0">
            and fc.id IN
            <foreach collection="contractIds" item="contractId" open="(" close=")" separator=",">
                #{contractId}
            </foreach>
        </if>
        <if test="businessModelId != null">
            and fc.business_model_id = #{businessModelId}
        </if>
        <if test="customerId != null">
            and f.customer_id = #{customerId}
        </if>
        <if test="auditCustomerId != null">
            and f.audit_customer_id = #{auditCustomerId}
            and fcs.customer_id = #{auditCustomerId}
        </if>
        <if test="auditFreightForwarderId != null">
            and f.audit_freight_forwarder_id = #{auditFreightForwarderId}
        </if>
        <if test="frameworkContractId != null">
            and scr.contract_id = #{frameworkContractId}
        </if>
        <if test="billHead != null and billHead != ''">
            and f.customer_name like concat('%', #{billHead}, '%')
        </if>
        <if test="statementNo != null">
            and f.statement_no = #{statementNo}
        </if>
        <if test="markStatus != null ">
            and f.mark_status = #{markStatus}
        </if>
        <if test="createTimeStart != null">
            and f.create_time &gt;= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null">
            and f.create_time &lt;= #{createTimeEnd}
        </if>
        <if test="state != null">
            AND f.state = #{state}
        </if>
        <if test="auditStartTime != null">
            and DATE(f.audit_time) >= #{auditStartTime}
        </if>
        <if test="auditEndTime != null">
            and DATE(f.audit_time) &lt;= #{auditEndTime}
        </if>
        <if test="operationAuditTimeStart != null and operationAuditTimeStart != ''">
            and DATE(f.operation_audit_time) &gt;= #{operationAuditTimeStart}
        </if>
        <if test="operationAuditTimeEnd != null and operationAuditTimeEnd != ''">
            and DATE(f.operation_audit_time) &lt;= #{operationAuditTimeEnd}
        </if>
        <if test="financeAuditTimeStart != null and financeAuditTimeStart != ''">
            and DATE(f.finance_audit_time) &gt;= #{financeAuditTimeStart}
        </if>
        <if test="financeAuditTimeEnd != null and financeAuditTimeEnd != ''">
            and DATE(f.finance_audit_time) &lt;= #{financeAuditTimeEnd}
        </if>
        <if test="stateList != null">
            AND f.state IN
            <foreach collection="stateList" separator="," open="(" close=")" item="s">
                #{s}
            </foreach>
        </if>
        <if test="resource != null">
            <choose>
                <when test="resource == 3">
                    and fcs.is_recorder = 0 <!-- 托运人查看自己入单的 -->
                    -- and f.customer_id=fcs.customer_id
                </when>
                <otherwise>
                    <choose>
                        <when test="resource == 8">
                            and fcs.is_recorder = 1 <!-- 金融端查看非自己录单的 -->
                            -- and f.customer_id=fcs.customer_id
                        </when>
                        <otherwise>
                            and fcs.is_recorder = 0 <!-- 网络货运端看全部 -->
                        </otherwise>
                    </choose>
                </otherwise>
            </choose>
        </if>
        GROUP BY
        f.id
        ) a
    </select>

    <select id="selectStatementIdList" resultType="long">
        select id from finance_statement
        <where>
            <if test="auditFreightForwarderId != null">
                and audit_freight_forwarder_id = #{auditFreightForwarderId}
            </if>
            <if test="auditCustomerId != null">
                and audit_customer_id = #{auditCustomerId}
            </if>
        </where>
    </select>


    <select id="invoiceStatistics" resultType="invoiceStatistics">
        SELECT *
        FROM (SELECT DATE_FORMAT(IFNULL(b.invoice_period, a.mark_time), '%Y-%m') AS yearMonth,
                     ROUND(IFNULL(SUM(a.receive_fare), 0) / 10000, 2)            AS receiveFare
              FROM finance_statement a
                       LEFT JOIN finance_statement_bill b ON a.id = b.statement_id
              WHERE a.audit_freight_forwarder_id = #{freightForwarderId}
                AND mark_status = 1
              GROUP BY yearMonth) c
        where c.yearMonth BETWEEN DATE_FORMAT(#{startTime}, '%Y-%m') AND DATE_FORMAT(#{endTime}, '%Y-%m')
    </select>

    <select id="invoiceStatisticsAll" resultType="invoiceStatistics">
        SELECT *
        FROM (SELECT DATE_FORMAT(IFNULL(b.invoice_period, a.mark_time), '%Y-%m') AS yearMonth,
                     IFNULL(SUM(a.receive_fare), 0)                              AS receiveFare
              FROM finance_statement a
                       LEFT JOIN finance_statement_bill b ON a.id = b.statement_id
              WHERE a.audit_freight_forwarder_id = #{freightForwarderId}
                AND mark_status = 1
              GROUP BY yearMonth) c
        where c.yearMonth BETWEEN DATE_FORMAT(#{startTime}, '%Y-%m') AND DATE_FORMAT(#{endTime}, '%Y-%m')
    </select>

    <select id="billStatisticsListByCustomerId" resultType="billStatisticsInfo">
        SELECT
        IFNULL(SUM(fare), 0) AS billMoney,
        ci.customer_name customerName,
        ci.id customerId
        FROM
        customer_info ci
        LEFT JOIN freight_forwarder_customer_relation ffcr ON ffcr.customer_id = ci.id
        LEFT JOIN (
        SELECT DISTINCT
        w.id AS wid,
        IFNULL(
        b.invoice_period,
        a.mark_time
        ) AS markTime,
        a.mark_status AS markStatus,
        w.customer_id AS customerId,
        IFNULL(fsw.receive_fare, 0) AS fare -- 如果没有 fare 数据，默认设为 0
        FROM
        finance_statement_contract_relation fscr
        LEFT JOIN finance_statement a ON a.id = fscr.statement_id
        AND a.mark_status = 1
        LEFT JOIN finance_statement_bill b ON a.id = b.statement_id and b.state = 0
        LEFT JOIN finance_statement_waybill fsw ON fsw.statement_id = a.id
        LEFT JOIN waybill w ON fsw.waybill_id = w.id
        WHERE
        a.audit_freight_forwarder_id = #{freightForwarderId}
        ORDER BY
        a.id DESC
        ) c ON ci.id = c.customerId
        WHERE
        (
        c.markTime BETWEEN #{startTime} AND #{endTime}
        OR c.markTime IS NULL
        ) AND ffcr.freight_forwarder_id = #{freightForwarderId}
        <if test="customerId != null">
            and ci.id = #{customerId}
        </if>
        GROUP BY
        ci.id
        ORDER BY
        billMoney DESC
    </select>

    <select id="billStatisticsListByContractId" resultType="billStatisticsInfo">
        SELECT
        IFNULL(SUM(fare), 0) AS billMoney,
        b.customer_name as customerName,
        a.contract_name as contractName,
        a.id as contractId
        FROM
        framework_contract a
        LEFT JOIN customer_info b ON a.tenant_id = b.id
        LEFT JOIN (
        SELECT DISTINCT
        w.id AS wid,
        IFNULL(
        b.invoice_period,
        a.mark_time
        ) AS markTime,
        a.mark_status AS markStatus,
        w.framework_contract_id AS contractId,
        IFNULL(fsw.receive_fare, 0) AS fare -- 如果没有 fare 数据，默认设为 0
        FROM
        finance_statement_contract_relation fscr
        LEFT JOIN finance_statement a ON a.id = fscr.statement_id
        AND a.mark_status = 1
        LEFT JOIN finance_statement_bill b ON a.id = b.statement_id and b.state = 0
        LEFT JOIN finance_statement_waybill fsw ON fsw.statement_id = a.id
        LEFT JOIN waybill w ON fsw.waybill_id = w.id
        WHERE
        a.audit_freight_forwarder_id = #{freightForwarderId}
        ORDER BY
        a.id DESC
        ) c ON a.id = c.contractId
        WHERE
        (
        c.markTime BETWEEN #{startTime} AND #{endTime}
        OR c.markTime IS NULL
        )
        and a.freight_forwarder_id = #{freightForwarderId}
        <if test="contractId != null">
            and a.id = #{contractId}
        </if>
        GROUP BY
        a.id
        ORDER BY
        billMoney DESC
    </select>
    <select id="selectStatementListCount" resultType="java.lang.Integer">
        SELECT
        COUNT(DISTINCT f.Id)
        FROM
        finance_statement f
        LEFT JOIN (
        SELECT
        fsw.statement_id,
        SUM(w.pay_fare) AS total_pay_fare
        FROM
        finance_statement_waybill fsw
        INNER JOIN
        waybill w ON fsw.waybill_id = w.id
        <where>
            <if test="statementIds != null and statementIds.size() > 0">
                and fsw.statement_id IN
                <foreach collection="statementIds" item="statementId" open="(" close=")" separator=",">
                    #{statementId}
                </foreach>
            </if>
        </where>
        GROUP BY
        fsw.statement_id
        ) subq ON f.id = subq.statement_id
        LEFT JOIN finance_statement_contract_relation scr ON f.id = scr.statement_id
        INNER JOIN framework_contract fc ON scr.contract_id = fc.id
        LEFT JOIN framework_contract_subchain fcs ON fc.id = fcs.contract_id
        LEFT JOIN customer_info c ON f.customer_id = c.id
        LEFT JOIN finance_statement_bill fsb ON fsb.statement_id = f.id
        WHERE f.statement_direction = 1
        <if test="statementId != null">
            AND f.id = #{statementId}
        </if>
        <if test="contractIds != null and contractIds.size() > 0">
            and fc.id IN
            <foreach collection="contractIds" item="contractId" open="(" close=")" separator=",">
                #{contractId}
            </foreach>
        </if>
        <if test="invoiceNumber != null and invoiceNumber != ''">
            and fsb.invoice_number = #{invoiceNumber}
        </if>
        <if test="invoicePeriodStart != null">
            and DATE(fsb.invoice_period) >= #{invoicePeriodStart}
        </if>
        <if test="invoicePeriodEnd != null">
            and DATE(fsb.invoice_period) &lt;= #{invoicePeriodEnd}
        </if>
        <if test="businessModelId != null">
            and fc.business_model_id = #{businessModelId}
        </if>
        <if test="customerId != null">
            and f.customer_id = #{customerId}
        </if>
        <if test="auditCustomerId != null">
            and f.audit_customer_id = #{auditCustomerId}
            and fcs.customer_id = #{auditCustomerId}
        </if>
        <if test="auditFreightForwarderId != null">
            and f.audit_freight_forwarder_id = #{auditFreightForwarderId}
        </if>
        <if test="frameworkContractId != null">
            and scr.contract_id = #{frameworkContractId}
        </if>
        <if test="billHead != null and billHead != ''">
            and c.customer_name like concat('%', #{billHead}, '%')
        </if>
        <if test="statementNo != null">
            and f.statement_no = #{statementNo}
        </if>
        <if test="markStatus != null ">
            and f.mark_status = #{markStatus}
        </if>
        <if test="createTimeStart != null">
            and f.create_time &gt;= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null">
            and f.create_time &lt;= #{createTimeEnd}
        </if>
        <if test="state != null">
            AND f.state = #{state}
        </if>
        <if test="auditStartTime != null">
            and DATE(f.audit_time) >= #{auditStartTime}
        </if>
        <if test="auditEndTime != null">
            and DATE(f.audit_time) &lt;= #{auditEndTime}
        </if>
        <if test="stateList != null">
            AND f.state IN
            <foreach collection="stateList" separator="," open="(" close=")" item="s">
                #{s}
            </foreach>
        </if>
        <if test="resource != null">
            <choose>
                <when test="resource == 3">
                    and fcs.is_recorder = 0 <!-- 托运人查看自己入单的 -->
                    -- and f.customer_id=fcs.customer_id
                </when>
                <otherwise>
                    <choose>
                        <when test="resource == 8">
                            and fcs.is_recorder = 1 <!-- 金融端查看非自己录单的 -->
                            -- and f.customer_id=fcs.customer_id
                        </when>
                        <otherwise>
                            and fcs.is_recorder = 0 <!-- 网络货运端看全部 -->
                        </otherwise>
                    </choose>
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="selectUnBillMoney" resultType="java.math.BigDecimal">
        SELECT (
                   COALESCE(
                           (SELECT SUM(receive_upstream) AS unBillMoney
                            FROM waybill w
                                     INNER JOIN waybill_customer_relation wcr ON wcr.waybill_id = w.id
                            WHERE settle_status = 2
                              and `status` = 4
                              and is_freight_forwarder = 0
                              AND w.freight_forwarder_id = #{freightForwarderId}),
                           0
                   ) - COALESCE(
                           (SELECT SUM(a.receive_fare) AS receiveFare
                            FROM finance_statement a
                            WHERE a.audit_freight_forwarder_id = #{freightForwarderId}
                              AND mark_status = 1),
                           0
                       )
                   ) AS result
    </select>


    <select id="billStatisticsListByCustomerIdAll" resultType="billStatisticsInfo">
        SELECT SUM(fare) as billMoney,
        customerName,
        customerId
        FROM (
        SELECT DISTINCT
        w.id AS wid,
        IFNULL(
        b.invoice_period,
        a.mark_time
        ) AS markTime,
        a.customer_id AS customerId,
        a.customer_name AS customerName,
        w.framework_contract_id AS contractId,
        w.framework_contract_name AS contractName,
        fsw.receive_fare AS fare
        FROM
        finance_statement_contract_relation fscr
        LEFT JOIN finance_statement a ON a.id = fscr.statement_id
        LEFT JOIN finance_statement_bill b ON a.id = b.statement_id and b.state = 0
        LEFT JOIN finance_statement_waybill fsw ON fsw.statement_id = a.id
        INNER JOIN waybill w ON fsw.waybill_id = w.id
        WHERE
        a.audit_freight_forwarder_id = #{freightForwarderId}
        AND mark_status = 1
        ORDER BY
        a.id DESC
        ) c
        WHERE 1=1
        <if test="customerIds != null and customerIds.size() > 0">
            and customerId in
            <foreach item="customerId" collection="customerIds" open="(" separator="," close=")">
                #{customerId}
            </foreach>
        </if>
        GROUP BY customerId
    </select>

    <select id="billStatisticsListByContractIdAll" resultType="billStatisticsInfo">
        SELECT
        SUM(fare) AS billMoney,
        customerName,
        contractName,
        contractId
        FROM
        (
        SELECT DISTINCT
        w.id AS wid,
        IFNULL(
        b.invoice_period,
        a.mark_time
        ) AS markTime,
        a.customer_name AS customerName,
        w.framework_contract_id AS contractId,
        w.framework_contract_name AS contractName,
        fsw.receive_fare AS fare
        FROM
        finance_statement_contract_relation fscr
        LEFT JOIN finance_statement a ON a.id = fscr.statement_id
        LEFT JOIN finance_statement_bill b ON a.id = b.statement_id and b.state = 0
        LEFT JOIN finance_statement_waybill fsw ON fsw.statement_id = a.id
        INNER JOIN waybill w ON fsw.waybill_id = w.id
        WHERE
        a.audit_freight_forwarder_id = #{freightForwarderId}
        AND mark_status = 1
        ORDER BY
        a.id DESC
        ) c
        WHERE
        1 = 1
        <if test="contractIds != null and contractIds.size() > 0">
            and contractId in
            <foreach item="contractId" collection="contractIds" open="(" separator="," close=")">
                #{contractId}
            </foreach>
        </if>
        GROUP BY
        contractId
    </select>

    <select id="yearCount" resultType="string">
        SELECT DATE_FORMAT(
                       IFNULL(
                               b.invoice_period,
                               a.mark_time
                       ),
                       '%Y'
               ) AS yearMonth
        FROM finance_statement a
                 LEFT JOIN finance_statement_bill b ON a.id = b.statement_id
        WHERE a.audit_freight_forwarder_id = #{freightForwarderId}
          AND a.mark_status = 1
        GROUP BY yearMonth
    </select>

    <select id="selectStatementRate" resultType="com.zly.project.finance.statement.domain.res.FinanceStatementRes">
        SELECT
        f.id,
        GROUP_CONCAT(DISTINCT TRIM(TRAILING '.0' FROM TRIM(TRAILING '0' FROM fcs.rate)) * 100) AS rate
        FROM
        finance_statement f
        LEFT JOIN finance_statement_contract_relation scr ON f.id = scr.statement_id
        INNER JOIN framework_contract fc ON scr.contract_id = fc.id
        LEFT JOIN framework_contract_subchain fcs ON fc.id = fcs.contract_id
        WHERE f.statement_direction = 1 and fcs.parent_customer_id = #{customerId}
        <if test="statementIds != null and statementIds.size() > 0">
            AND f.id IN
            <foreach collection="statementIds" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
        GROUP BY f.id
    </select>
</mapper>
