<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.finance.dingDing.mapper.FinanceDdInfoMapper">

    <resultMap type="FinanceDdInfo" id="FinanceDdInfoResult">
        <result property="id"    column="id"    />
        <result property="applicantName"    column="applicant_name"    />
        <result property="businessOwnership"    column="business_ownership"    />
        <result property="purchaserName"    column="purchaser_name"    />
        <result property="sellerName"    column="seller_name"    />
        <result property="billData"    column="bill_data"    />
        <result property="billInvoicing"    column="bill_invoicing"    />
        <result property="profitDate"    column="profit_date"    />
        <result property="fileInfo"    column="file_info"    />
        <result property="processCode"    column="process_code"    />
        <result property="instanceId"    column="instance_id"    />
        <result property="financeStatementId"    column="finance_statement_id"    />
        <result property="selectApproveList"    column="select_approve_list"    />
        <result property="approveList"    column="approve_list"    />
        <result property="remark"    column="remark"    />
        <result property="cclist"    column="ccList"    />
        <result property="fileurl"    column="fileUrl"    />
        <result property="isApprovalPlatform"    column="is_approval_platform"    />
        <result property="creator"    column="creator"    />
        <result property="createTime"    column="create_time"    />
        <result property="tenantryId"    column="tenantry_id"    />
    </resultMap>

    <sql id="selectFinanceDdInfoVo">
        select id, applicant_name, business_ownership, purchaser_name, seller_name, bill_data, bill_invoicing, profit_date, file_info, process_code, instance_id, finance_statement_id, select_approve_list, approve_list, remark, ccList, fileUrl, is_approval_platform, creator, create_time, tenantry_id from finance_dd_info
    </sql>

    <select id="selectFinanceDdInfoList" parameterType="FinanceDdInfo" resultMap="FinanceDdInfoResult">
        <include refid="selectFinanceDdInfoVo"/>
        <where>
            <if test="applicantName != null  and applicantName != ''"> and applicant_name like concat('%', #{applicantName}, '%')</if>
            <if test="businessOwnership != null  and businessOwnership != ''"> and business_ownership = #{businessOwnership}</if>
            <if test="purchaserName != null  and purchaserName != ''"> and purchaser_name like concat('%', #{purchaserName}, '%')</if>
            <if test="sellerName != null  and sellerName != ''"> and seller_name like concat('%', #{sellerName}, '%')</if>
            <if test="billData != null  and billData != ''"> and bill_data = #{billData}</if>
            <if test="billInvoicing != null  and billInvoicing != ''"> and bill_invoicing = #{billInvoicing}</if>
            <if test="profitDate != null  and profitDate != ''"> and profit_date = #{profitDate}</if>
            <if test="fileInfo != null  and fileInfo != ''"> and file_info = #{fileInfo}</if>
            <if test="processCode != null  and processCode != ''"> and process_code = #{processCode}</if>
            <if test="instanceId != null  and instanceId != ''"> and instance_id = #{instanceId}</if>
            <if test="financeStatementId != null "> and finance_statement_id = #{financeStatementId}</if>
            <if test="selectApproveList != null  and selectApproveList != ''"> and select_approve_list = #{selectApproveList}</if>
            <if test="approveList != null  and approveList != ''"> and approve_list = #{approveList}</if>
            <if test="cclist != null  and cclist != ''"> and ccList = #{cclist}</if>
            <if test="fileurl != null  and fileurl != ''"> and fileUrl = #{fileurl}</if>
            <if test="isApprovalPlatform != null "> and is_approval_platform = #{isApprovalPlatform}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="tenantryId != null "> and tenantry_id = #{tenantryId}</if>
        </where>
    </select>

    <select id="selectFinanceDdInfoById" parameterType="Long" resultMap="FinanceDdInfoResult">
        <include refid="selectFinanceDdInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertFinanceDdInfo" parameterType="FinanceDdInfo">
        insert into finance_dd_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="applicantName != null and applicantName != ''">applicant_name,</if>
            <if test="businessOwnership != null and businessOwnership != ''">business_ownership,</if>
            <if test="purchaserName != null and purchaserName != ''">purchaser_name,</if>
            <if test="sellerName != null and sellerName != ''">seller_name,</if>
            <if test="billData != null and billData != ''">bill_data,</if>
            <if test="billInvoicing != null and billInvoicing != ''">bill_invoicing,</if>
            <if test="profitDate != null and profitDate != ''">profit_date,</if>
            <if test="fileInfo != null and fileInfo != ''">file_info,</if>
            <if test="processCode != null and processCode != ''">process_code,</if>
            <if test="instanceId != null and instanceId != ''">instance_id,</if>
            <if test="financeStatementId != null">finance_statement_id,</if>
            <if test="selectApproveList != null and selectApproveList != ''">select_approve_list,</if>
            <if test="approveList != null and approveList != ''">approve_list,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="cclist != null and cclist != ''">ccList,</if>
            <if test="fileurl != null and fileurl != ''">fileUrl,</if>
            <if test="isApprovalPlatform != null">is_approval_platform,</if>
            <if test="creator != null">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="tenantryId != null">tenantry_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="applicantName != null and applicantName != ''">#{applicantName},</if>
            <if test="businessOwnership != null and businessOwnership != ''">#{businessOwnership},</if>
            <if test="purchaserName != null and purchaserName != ''">#{purchaserName},</if>
            <if test="sellerName != null and sellerName != ''">#{sellerName},</if>
            <if test="billData != null and billData != ''">#{billData},</if>
            <if test="billInvoicing != null and billInvoicing != ''">#{billInvoicing},</if>
            <if test="profitDate != null and profitDate != ''">#{profitDate},</if>
            <if test="fileInfo != null and fileInfo != ''">#{fileInfo},</if>
            <if test="processCode != null and processCode != ''">#{processCode},</if>
            <if test="instanceId != null and instanceId != ''">#{instanceId},</if>
            <if test="financeStatementId != null">#{financeStatementId},</if>
            <if test="selectApproveList != null and selectApproveList != ''">#{selectApproveList},</if>
            <if test="approveList != null and approveList != ''">#{approveList},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="cclist != null and cclist != ''">#{cclist},</if>
            <if test="fileurl != null and fileurl != ''">#{fileurl},</if>
            <if test="isApprovalPlatform != null">#{isApprovalPlatform},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="tenantryId != null">#{tenantryId},</if>
        </trim>
    </insert>
    <insert id="insertFinanceDdInfos">
        insert IGNORE  into finance_dd_info
        (id,
        applicant_name,
        business_ownership,
        purchaser_name,
        seller_name,
        bill_data,
        bill_invoicing,
        profit_date,
        file_info,
        process_code,
        instance_id,
        finance_statement_id,
        select_approve_list,
        approve_list,
        remark,
        ccList,
        fileUrl,
        is_approval_platform,
        creator,
        create_time,
        tenantry_id
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.id},
            #{item.applicantName},
            #{item.businessOwnership},
            #{item.purchaserName},
            #{item.sellerName},
            #{item.billData},
            #{item.billInvoicing},
            #{item.profitDate},
            #{item.fileInfo},
            #{item.processCode},
            #{item.instanceId},
            #{item.financeStatementId},
            #{item.selectApproveList},
            #{item.approveList},
            #{item.remark},
            #{item.cclist},
            #{item.fileurl},
            #{item.isApprovalPlatform},
            #{item.creator},
            #{item.createTime},
            #{item.tenantryId}
        </foreach>
    </insert>

    <update id="updateFinanceDdInfo" parameterType="FinanceDdInfo">
        update finance_dd_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="applicantName != null and applicantName != ''">applicant_name = #{applicantName},</if>
            <if test="businessOwnership != null and businessOwnership != ''">business_ownership =
                #{businessOwnership},
            </if>
            <if test="purchaserName != null and purchaserName != ''">purchaser_name = #{purchaserName},</if>
            <if test="sellerName != null and sellerName != ''">seller_name = #{sellerName},</if>
            <if test="billData != null and billData != ''">bill_data = #{billData},</if>
            <if test="billInvoicing != null and billInvoicing != ''">bill_invoicing = #{billInvoicing},</if>
            <if test="profitDate != null and profitDate != ''">profit_date = #{profitDate},</if>
            <if test="fileInfo != null and fileInfo != ''">file_info = #{fileInfo},</if>
            <if test="processCode != null and processCode != ''">process_code = #{processCode},</if>
            <if test="instanceId != null and instanceId != ''">instance_id = #{instanceId},</if>
            <if test="financeStatementId != null">finance_statement_id = #{financeStatementId},</if>
            <if test="selectApproveList != null and selectApproveList != ''">select_approve_list = #{selectApproveList},</if>
            <if test="approveList != null and approveList != ''">approve_list = #{approveList},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="cclist != null and cclist != ''">ccList = #{cclist},</if>
            <if test="fileurl != null and fileurl != ''">fileUrl = #{fileurl},</if>
            <if test="isApprovalPlatform != null">is_approval_platform = #{isApprovalPlatform},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="tenantryId != null">tenantry_id = #{tenantryId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFinanceDdInfoById" parameterType="Long">
        delete from finance_dd_info where id = #{id}
    </delete>

    <delete id="deleteFinanceDdInfoByIds" parameterType="String">
        delete from finance_dd_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="queryByinstanceId" resultMap="FinanceDdInfoResult">
        select *
        from finance_dd_info
        where instance_id = #{processInstanceId}
    </select>
    <select id="selectFinanceDdInfoByStatementIds" resultMap="FinanceDdInfoResult">
        <include refid="selectFinanceDdInfoVo"/>
        where finance_statement_id in
        <foreach item="statementId" collection="statementIds" open="(" separator="," close=")">
            #{statementId}
        </foreach>
    </select>
</mapper>
