<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.finance.statement.mapper.FinanceStatementMapper">

	<resultMap type="FinanceStatement" id="FinanceStatementResult">
		<result property="id" column="id"/>
		<result property="statementNo" column="statement_no"/>
		<result property="statementDirection" column="statement_direction"/>
		<result property="customerId" column="customer_id"/>
		<result property="customerName" column="customer_name"/>
		<result property="contractId" column="contract_id"/>
		<result property="contractName" column="contract_name"/>
		<result property="waybillAmount" column="waybill_amount"/>
		<result property="receiveFare" column="receive_fare"/>
		<result property="invoiceFare" column="invoice_fare"/>
		<result property="state" column="state"/>
		<result property="markStatus" column="mark_status"/>
		<result property="profitState" column="profit_state"/>
		<result property="remark" column="remark"/>
		<result property="createBy" column="create_by"/>
		<result property="createTime" column="create_time"/>
		<result property="updateBy" column="update_by"/>
		<result property="updateTime" column="update_time"/>
		<result property="auditCustomerId" column="audit_customer_id"/>
		<result property="auditFreightForwarderId" column="audit_freight_forwarder_id"/>
		<result property="auditType" column="audit_type"/>
		<result property="auditTime" column="audit_time"/>
		<result property="instanceId" column="instance_id"/>
		<result property="errMsg" column="err_msg"/>
		<result property="markType" column="mark_type"/>
		<result property="markTime" column="mark_time"/>
		<result property="fileUpState" column="file_up_state"/>
		<result property="applyBillMsg" column="apply_bill_msg"/>
		<result property="invoicingBillMsg" column="invoicing_bill_msg"/>
		<result property="isSales" column="is_sales"/>
		<result property="salesName" column="sales_name"/>
		<result property="transportationName" column="transportation_name"/>
		<result property="statementSource" column="statement_source"/>
		<result property="rejectReason" column="reject_reason"/>
		<result property="bwyNo" column="bwy_no"/>
		<result property="buyerInfo" column="buyer_info"/>
		<result property="sellerInfo" column="seller_info"/>
		<result property="riskResultGrade" column="risk_result_grade"/>
		<result property="markUserId" column="mark_user_id"/>
		<result property="clientType" column="client_type"/>
		<result property="email" column="email"/>
		<result property="paymentDate" column="payment_date"/>
		<result property="operationRejectReason" column="operation_reject_reason"/>
		<result property="financeRejectReason" column="finance_reject_reason"/>
		<result property="operationAuditTime" column="operation_audit_time"/>
		<result property="financeAuditTime" column="finance_audit_time"/>
		<result property="operationAuditBy" column="operation_audit_by"/>
		<result property="financeAuditBy" column="finance_audit_by"/>
		<result property="paymentType" column="payment_type"/>
		<result property="markBy" column="mark_by"/>
		<result property="createUserId" column="create_user_id"/>
		<result property="sendDdState" column="send_dd_state"/>
		<result property="payReturnState" column="pay_return_state"/>
		<result property="payReturnTime" column="pay_return_time"/>
		<result property="billRemark" column="bill_remark"/>
	</resultMap>

	<sql id="selectFinanceStatementVo">
		SELECT id,
			   statement_no,
			   statement_direction,
			   customer_id,
			   customer_name,
			   contract_id,
			   contract_name,
			   waybill_amount,
			   receive_fare,
			   invoice_fare,
			   state,
			   mark_status,
			   profit_state,
			   remark,
			   create_by,
			   create_time,
			   update_by,
			   update_time,
			   audit_type,
			   audit_customer_id,
			   audit_freight_forwarder_id,
			   instance_id,
			   err_msg,
			   mark_type,
			   apply_bill_msg,
			   invoicing_bill_msg,
			   is_sales,
			   sales_name,
			   transportation_name,
			   statement_source,
			   reject_reason,
			   bwy_no,
			   buyer_info,
			   seller_info,
			   audit_time,
			   mark_time,
			   file_up_state,
			   risk_result_grade,
			   mark_user_id,
			   client_type,
			   email,
			   payment_date,
			   operation_reject_reason,
			   finance_reject_reason,
			   operation_audit_time,
			   finance_audit_time,
			   operation_audit_by,
			   finance_audit_by,
			   payment_type,
			   mark_by,
			   create_user_id,
			   send_dd_state,
			   pay_return_state,
			   pay_return_time,
			   bill_remark
		FROM finance_statement
	</sql>

	<select id="selectFinanceStatementList" parameterType="FinanceStatement" resultMap="FinanceStatementResult">
		<include refid="selectFinanceStatementVo"/>
		<where>
			<if test="statementNo != null  and statementNo != ''">
				and statement_no = #{statementNo}
			</if>
			<if test="statementDirection != null  and statementDirection != ''">
				and statement_direction = #{statementDirection}
			</if>
			<if test="customerId != null ">
				and customer_id = #{customerId}
			</if>
			<if test="customerName != null  and customerName != ''">
				and customer_name like concat('%', #{customerName}, '%')
			</if>
			<if test="contractId != null ">
				and contract_id = #{contractId}
			</if>
			<if test="contractName != null  and contractName != ''">
				and contract_name like concat('%', #{contractName}, '%')
			</if>
			<if test="waybillAmount != null ">
				and waybill_amount = #{waybillAmount}
			</if>
			<if test="receiveFare != null ">
				and receive_fare = #{receiveFare}
			</if>
			<if test="invoiceFare != null ">
				and invoice_fare = #{invoiceFare}
			</if>
			<if test="state != null ">
				and state = #{state}
			</if>
			<if test="markStatus != null ">
				and mark_status = #{markStatus}
			</if>
			<if test="profitState != null ">
				and profit_state = #{profitState}
			</if>
			<if test="auditCustomerId != null ">
				and audit_customer_id = #{auditCustomerId}
			</if>
			<if test="auditFreightForwarderId != null ">
				and audit_freight_forwarder_id = #{auditFreightForwarderId}
			</if>
			<if test="auditType != null ">
				and audit_type = #{auditType}
			</if>
			<if test="instanceId != null ">
				and instance_id = #{instanceId}
			</if>
			<if test="errMsg != null ">
				and err_msg = #{errMsg}
			</if>
			<if test="markType != null ">
				and mark_type = #{markType}
			</if>
			<if test="applyBillMsg != null ">
				and apply_bill_msg = #{applyBillMsg}
			</if>
			<if test="invoicingBillMsg != null ">
				and invoicing_bill_msg = #{invoicingBillMsg}
			</if>
			<if test="isSales != null ">
				and is_sales = #{isSales}
			</if>
			<if test="statementSource != null ">
				and statement_source = #{statementSource}
			</if>
			<if test="statementSource != null and bwyNo != ''">
				and bwy_no = #{bwyNo}
			</if>
			<if test="buyerInfo != null and buyerInfo != ''">
				and buyer_info = #{buyerInfo}
			</if>
			<if test="sellerInfo != null and sellerInfo != ''">
				and seller_info = #{sellerInfo}
			</if>
			<if test="riskResultGrade != null">
				and risk_result_grade = #{riskResultGrade}
			</if>
			<if test="markUserId != null">
				and mark_user_id = #{markUserId}
			</if>
			<if test="clientType != null">
				and client_type = #{clientType}
			</if>
			<if test="email != null">
				and email = #{email}
			</if>
			<if test="paymentDate != null">
				and payment_date = #{paymentDate}
			</if>
			<if test="operationRejectReason != null">
				and operation_reject_reason = #{operationRejectReason}
			</if>
			<if test="financeRejectReason != null">
				and finance_reject_reason = #{financeRejectReason}
			</if>
			<if test="operationAuditTime != null">
				and operation_audit_time = #{operationAuditTime}
			</if>
			<if test="financeAuditTime != null">
				and finance_audit_time = #{financeAuditTime}
			</if>
			<if test="paymentType != null">
				and payment_type = #{paymentType}
			</if>
		</where>
	</select>

	<select id="selectFinanceStatementById" parameterType="Long" resultMap="FinanceStatementResult">
		<include refid="selectFinanceStatementVo"/>
		where id = #{id}
	</select>
	<select id="selectInvoicedStatementIdsByWaybillId" resultMap="FinanceStatementResult">
		select s.*
		from finance_statement_waybill sw
				 left join finance_statement s on sw.statement_id = s.id
		where waybill_id = #{waybillId}
		  and s.mark_status = 1
	</select>
	<select id="selectFinanceStatementByStatementNo" resultMap="FinanceStatementResult">
		<include refid="selectFinanceStatementVo"/>
		where statement_no = #{statementNo} order by create_time desc
	</select>
	<select id="selectFinanceStatementByIds" resultMap="FinanceStatementResult">
		<include refid="selectFinanceStatementVo"/>
		where id in
		<foreach item="id" collection="statementIds" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>

	<insert id="insertFinanceStatement" parameterType="FinanceStatement">
		insert into finance_statement
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">id,</if>
			<if test="statementNo != null and statementNo != ''">statement_no,</if>
			<if test="statementDirection != null and statementDirection != ''">statement_direction,</if>
			<if test="customerId != null">customer_id,</if>
			<if test="customerName != null and customerName != ''">customer_name,</if>
			<if test="contractId != null">contract_id,</if>
			<if test="contractName != null and contractName != ''">contract_name,</if>
			<if test="waybillAmount != null">waybill_amount,</if>
			<if test="receiveFare != null">receive_fare,</if>
			<if test="invoiceFare != null">invoice_fare,</if>
			<if test="state != null">state,</if>
			<if test="markStatus != null">mark_status,</if>
			<if test="profitState != null">profit_state,</if>
			<if test="remark != null and remark != ''">remark,</if>
			<if test="createBy != null and createBy != ''">create_by,</if>
			<if test="createTime != null">create_time,</if>
			<if test="updateBy != null and updateBy != ''">update_by,</if>
			<if test="updateTime != null">update_time,</if>
			<if test="auditCustomerId != null">audit_customer_id,</if>
			<if test="auditFreightForwarderId != null">audit_freight_forwarder_id,</if>
			<if test="auditTime != null">audit_time,</if>
			<if test="auditType != null ">audit_type,</if>
			<if test="instanceId != null ">instance_id,</if>
			<if test="errMsg != null ">err_msg,</if>
			<if test="markType != null ">mark_type,</if>
			<if test="applyBillMsg != null ">apply_bill_msg,</if>
			<if test="invoicingBillMsg != null ">invoicing_bill_msg,</if>
			<if test="isSales != null ">is_sales,</if>
			<if test="salesName != null ">sales_name,</if>
			<if test="transportationName != null ">transportation_name,</if>
			<if test="statementSource != null ">statement_source,</if>
			<if test="rejectReason != null ">reject_reason,</if>
			<if test="bwyNo != null ">bwy_no,</if>
			<if test="buyerInfo != null ">buyer_info,</if>
			<if test="sellerInfo != null ">seller_info,</if>
			<if test="riskResultGrade != null ">risk_result_grade,</if>
			<if test="markUserId != null ">mark_user_id,</if>
			<if test="clientType != null ">client_type,</if>
			<if test="email != null ">email,</if>
			<if test="paymentDate != null ">payment_date,</if>
			<if test="operationRejectReason != null ">operation_reject_reason,</if>
			<if test="financeRejectReason != null ">finance_reject_reason,</if>
			<if test="operationAuditTime != null ">operation_audit_time,</if>
			<if test="financeAuditTime != null ">finance_audit_time,</if>
			<if test="paymentType != null ">payment_type,</if>
			<if test="markBy != null and markBy != ''">mark_by,</if>
			<if test="createUserId != null ">create_user_id,</if>
			<if test="sendDdState != null ">send_dd_state,</if>
			<if test="payReturnState != null ">pay_return_state,</if>
			<if test="payReturnTime != null ">pay_return_time,</if>
			<if test="billRemark != null and billRemark != ''">bill_remark,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">#{id},</if>
			<if test="statementNo != null and statementNo != ''">#{statementNo},</if>
			<if test="statementDirection != null and statementDirection != ''">#{statementDirection},</if>
			<if test="customerId != null">#{customerId},</if>
			<if test="customerName != null and customerName != ''">#{customerName},</if>
			<if test="contractId != null">#{contractId},</if>
			<if test="contractName != null and contractName != ''">#{contractName},</if>
			<if test="waybillAmount != null">#{waybillAmount},</if>
			<if test="receiveFare != null">#{receiveFare},</if>
			<if test="invoiceFare != null">#{invoiceFare},</if>
			<if test="state != null">#{state},</if>
			<if test="markStatus != null">#{markStatus},</if>
			<if test="profitState != null">#{profitState},</if>
			<if test="remark != null and remark != ''">#{remark},</if>
			<if test="createBy != null and createBy != ''">#{createBy},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="updateBy != null and updateBy != ''">#{updateBy},</if>
			<if test="updateTime != null">#{updateTime},</if>
			<if test="auditCustomerId != null">#{auditCustomerId},</if>
			<if test="auditFreightForwarderId != null">#{auditFreightForwarderId},</if>
			<if test="auditTime != null">#{auditTime},</if>
			<if test="auditType != null ">#{auditType},</if>
			<if test="instanceId != null ">#{instanceId},</if>
			<if test="errMsg != null ">#{errMsg},</if>
			<if test="markType != null ">#{markType},</if>
			<if test="applyBillMsg != null ">#{applyBillMsg},</if>
			<if test="invoicingBillMsg != null ">#{invoicingBillMsg},</if>
			<if test="isSales != null ">#{isSales},</if>
			<if test="salesName != null ">#{salesName},</if>
			<if test="transportationName != null ">#{transportationName},</if>
			<if test="statementSource != null ">#{statementSource},</if>
			<if test="rejectReason != null ">#{rejectReason},</if>
			<if test="bwyNo != null ">#{bwyNo},</if>
			<if test="buyerInfo != null ">#{buyerInfo},</if>
			<if test="sellerInfo != null ">#{sellerInfo},</if>
			<if test="riskResultGrade != null ">#{riskResultGrade},</if>
			<if test="markUserId != null ">#{markUserId},</if>
			<if test="clientType != null ">#{clientType},</if>
			<if test="email != null ">#{email},</if>
			<if test="paymentDate != null ">#{paymentDate},</if>
			<if test="operationRejectReason != null ">#{operationRejectReason},</if>
			<if test="financeRejectReason != null ">#{financeRejectReason},</if>
			<if test="operationAuditTime != null ">#{operationAuditTime},</if>
			<if test="financeAuditTime != null ">#{financeAuditTime},</if>
			<if test="paymentType != null ">#{paymentType},</if>
			<if test="markBy != null and markBy != ''">#{markBy},</if>
			<if test="createUserId != null ">#{createUserId},</if>
			<if test="sendDdState != null ">#{sendDdState},</if>
			<if test="payReturnState != null ">#{payReturnState},</if>
			<if test="payReturnTime != null ">#{payReturnTime},</if>
			<if test="billRemark != null and billRemark != ''">#{billRemark},</if>
		</trim>
	</insert>
	<insert id="insertFinanceStatements">
		insert IGNORE into finance_statement
		(id,
		statement_no,
		statement_direction,
		customer_id,
		customer_name,
		contract_id,
		contract_name,
		waybill_amount,
		receive_fare,
		invoice_fare,
		state,
		mark_status,
		profit_state,
		remark,
		create_by,
		create_time,
		update_by,
		update_time,
		audit_customer_id,
		audit_freight_forwarder_id,
		audit_type,
		audit_time,
		instance_id,
		err_msg,
		mark_type,
		mark_time,
		file_up_state,
		apply_bill_msg,
		invoicing_bill_msg,
		is_sales,
		sales_name,
		transportation_name,
		statement_source,
		reject_reason,
		bwy_no,
		buyer_info,
		seller_info,
		risk_result_grade
		)
		VALUES
		<foreach collection="list" item="item" open="(" close=")" separator="),(">
			#{item.id},
			#{item.statementNo},
			#{item.statementDirection},
			#{item.customerId},
			#{item.customerName},
			#{item.contractId},
			#{item.contractName},
			#{item.waybillAmount},
			#{item.receiveFare},
			#{item.invoiceFare},
			#{item.state},
			#{item.markStatus},
			#{item.profitState},
			#{item.remark},
			#{item.createBy},
			#{item.createTime},
			#{item.updateBy},
			#{item.updateTime},
			#{item.auditCustomerId},
			#{item.auditFreightForwarderId},
			#{item.auditType},
			<if test="item.auditTime != null">#{item.auditTime},</if>
			<if test="item.auditTime == null">default,</if>
			#{item.instanceId},
			#{item.errMsg},
			#{item.markType},
			<if test="item.markTime != null">#{item.markTime},</if>
			<if test="item.markTime == null">default,</if>
			#{item.fileUpState},
			#{item.applyBillMsg},
			#{item.invoicingBillMsg},
			#{item.isSales},
			#{item.salesName},
			#{item.transportationName},
			#{item.statementSource},
			#{item.rejectReason},
			#{item.bwyNo},
			#{item.buyerInfo},
			#{item.sellerInfo},
			#{item.riskResultGrade}
		</foreach>
	</insert>

	<update id="updateFinanceStatement" parameterType="FinanceStatement">
		update finance_statement
		<trim prefix="SET" suffixOverrides=",">
			<if test="statementNo != null and statementNo != ''">statement_no = #{statementNo},</if>
			<if test="statementDirection != null and statementDirection != ''">statement_direction =
				#{statementDirection},
			</if>
			<if test="customerId != null">customer_id = #{customerId},</if>
			<if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
			<if test="contractId != null">contract_id = #{contractId},</if>
			<if test="contractName != null and contractName != ''">contract_name = #{contractName},</if>
			<if test="waybillAmount != null">waybill_amount = #{waybillAmount},</if>
			<if test="receiveFare != null">receive_fare = #{receiveFare},</if>
			<if test="invoiceFare != null">invoice_fare = #{invoiceFare},</if>
			<if test="state != null">state = #{state},</if>
			<if test="markStatus != null">mark_status = #{markStatus},</if>
			<if test="markTime != null">mark_time = #{markTime},</if>
			<if test="profitState != null">profit_state = #{profitState},</if>
			<if test="remark != null and remark != ''">remark = #{remark},</if>
			<if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
			<if test="createTime != null">create_time = #{createTime},</if>
			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
			<if test="updateTime != null">update_time = #{updateTime},</if>
			<if test="auditCustomerId != null">audit_customer_id = #{auditCustomerId},</if>
			<if test="auditFreightForwarderId != null">audit_freight_forwarder_id = #{auditFreightForwarderId},</if>
			<if test="auditType != null ">audit_type = #{auditType},</if>
			<if test="auditTime != null ">audit_time = #{auditTime},</if>
			<if test="instanceId != null ">instance_id = #{instanceId},</if>
			<if test="errMsg != null ">err_msg = #{errMsg},</if>
			<if test="markType != null ">mark_type = #{markType},</if>
			<if test="fileUpState != null ">file_up_state = #{fileUpState},</if>
			<if test="applyBillMsg != null ">apply_bill_msg = #{applyBillMsg},</if>
			<if test="invoicingBillMsg != null ">invoicing_bill_msg = #{invoicingBillMsg},</if>
			<if test="isSales != null ">is_sales = #{isSales},</if>
			<if test="statementSource != null ">statement_source = #{statementSource},</if>
			<if test="rejectReason != null ">reject_reason = #{rejectReason},</if>
			<if test="bwyNo != null and bwyNo != ''">bwy_no = #{bwyNo},</if>
			<if test="buyerInfo != null and buyerInfo != ''">buyer_info = #{buyerInfo},</if>
			<if test="sellerInfo != null and sellerInfo != ''">seller_info = #{sellerInfo},</if>
			<if test="riskResultGrade != null">risk_result_grade = #{riskResultGrade},</if>
			<if test="markUserId != null">mark_user_id = #{markUserId},</if>
			<if test="clientType != null">client_type = #{clientType},</if>
			<if test="email != null">email = #{email},</if>
			<if test="paymentDate != null">payment_date = #{paymentDate},</if>
			<if test="operationRejectReason != null">operation_reject_reason = #{operationRejectReason},</if>
			<if test="financeRejectReason != null">finance_reject_reason = #{financeRejectReason},</if>
			<if test="operationAuditTime != null">operation_audit_time = #{operationAuditTime},</if>
			<if test="financeAuditTime != null">finance_audit_time = #{financeAuditTime},</if>
			<if test="operationAuditBy != null">operation_audit_by = #{operationAuditBy},</if>
			<if test="financeAuditBy != null">finance_audit_by = #{financeAuditBy},</if>
			<if test="paymentType != null">payment_type = #{paymentType},</if>
			<if test="markBy != null and markBy != ''">mark_by = #{markBy},</if>
			<if test="createUserId != null">create_user_id = #{createUserId},</if>
			<if test="sendDdState != null">send_dd_state = #{sendDdState},</if>
			<if test="payReturnState != null">pay_return_state = #{payReturnState},</if>
			<if test="payReturnTime != null">pay_return_time = #{payReturnTime},</if>
		</trim>
		where id = #{id}
	</update>

	<update id="updateFinanceStatementOverruleByXyd">
		update finance_statement set state = 0,reject_reason = '小雨点账单校验驳回' where state != -1 and statement_no in
		<foreach item="statementNo" collection="updateBillNos" open="(" separator="," close=")">
			#{statementNo}
		</foreach>
	</update>

	<delete id="deleteFinanceStatementById" parameterType="Long">
		DELETE
		FROM finance_statement
		WHERE id = #{id}
	</delete>

	<delete id="deleteFinanceStatementByIds" parameterType="String">
		delete from finance_statement where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>

	<select id="getStatementIdAndRiskLevel" resultType="com.zly.project.finance.statement.domain.FinanceStatement">
		select fsw.statement_id id,MIN(w.risk_result_grade) riskResultGrade
		from finance_statement_waybill fsw
		left join waybill w on fsw.waybill_id = w.id
		where fsw.statement_id in
		<foreach item="id" collection="statementIds" open="(" separator="," close=")">
			#{id}
		</foreach>
		and w.risk_result_grade in(1,2,3,4)
		group by fsw.statement_id
	</select>

	<update id="batchUpdateFinanceStatement">
		update finance_statement set risk_result_grade = #{riskResultGrade} where id in
		<foreach item="id" collection="ids" open="(" separator="," close=")">
			#{id}
		</foreach>
	</update>

	<select id="selectFinanceStatementIdsByWaybillIds" resultType="java.lang.Long">
		select statement_id from finance_statement_waybill where waybill_id in
		<foreach item="id" collection="waybillIds" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>

	<select id="selectActualFareByStatementId" resultType="java.math.BigDecimal">
		select sum(w.actual_fare)
		from finance_statement_waybill fsw
				 inner join waybill w on fsw.waybill_id = w.id
		where fsw.statement_id = #{id}
	</select>

	<select id="approveInfo" resultType="financeStatementApproveInfo">
		select create_by               as createBy,
			   create_time             as createTime,
			   operation_reject_reason as operationRejectReason,
			   operation_audit_time    as operationAuditTime,
			   operation_audit_by      as operationAuditBy,
			   finance_audit_time      as financeAuditTime,
			   finance_audit_by        as financeAuditBy,
			   finance_reject_reason   as financeRejectReason,
			   mark_status             as markStatus,
			   mark_time               as markTime,
			   mark_by                 as markBy,
			   payment_date            as paymentDate,
			   profit_state            as profitState,
			   state
		from finance_statement
		where id = #{id}
	</select>

	<select id="financeStatementPayQuery" resultMap="FinanceStatementResult">
		select id, statement_no
		from finance_statement
		where profit_state = 4
		  and DATE_SUB(now(), INTERVAL 10 MINUTE) &gt; payment_date
		  and payment_date != "0000-00-00 00:00:00"
	</select>

	<select id="financeStatementPayReturnQuery" resultMap="FinanceStatementResult">
		select id, statement_no
		from finance_statement
		where pay_return_state = 2
		  and DATE_SUB(now(), INTERVAL 10 MINUTE) &gt; pay_return_time
		  and pay_return_time != "0000-00-00 00:00:00"
	</select>
</mapper>
