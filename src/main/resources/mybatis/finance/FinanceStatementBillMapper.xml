<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.finance.statement.mapper.FinanceStatementBillMapper">

    <resultMap type="FinanceStatementBill" id="FinanceStatementBillResult">
        <result property="id" column="id"/>
        <result property="statementId" column="statement_id"/>
        <result property="invoiceNo" column="invoice_no"/>
        <result property="invoiceNumber" column="invoice_number"/>
        <result property="invoiceMoney" column="invoice_money"/>
        <result property="invoicePeriod" column="invoice_period"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="hxPushState" column="hx_push_state"/>
        <result property="subtotalAmount" column="subtotal_amount"/>
        <result property="subtotalTax" column="subtotal_tax"/>
        <result property="state" column="state"/>
        <result property="taxUploadState" column="tax_upload_state"/>
        <result property="taxLastUploadTime" column="tax_last_upload_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectFinanceStatementBillVo">
        SELECT id,
               statement_id,
               invoice_no,
               invoice_number,
               invoice_money,
               invoice_period,
               create_by,
               create_time,
               hx_push_state,
               subtotal_amount,
               subtotal_tax,
               state,
               tax_upload_state,
               tax_last_upload_time,
               update_by,
               update_time
        FROM finance_statement_bill
    </sql>

    <select id="selectFinanceStatementBillList" parameterType="FinanceStatementBill"
            resultMap="FinanceStatementBillResult">
        <include refid="selectFinanceStatementBillVo"/>
        <where>
            <if test="statementId != null ">and statement_id = #{statementId}</if>
            <if test="invoiceNo != null  and invoiceNo != ''">and invoice_no = #{invoiceNo}</if>
            <if test="invoiceNumber != null  and invoiceNumber != ''">and invoice_number = #{invoiceNumber}</if>
            <if test="invoiceMoney != null ">and invoice_money = #{invoiceMoney}</if>
            <if test="invoicePeriod != null ">and invoice_period = #{invoicePeriod}</if>
            <if test="createBy != null  and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null ">and create_time = #{createTime}</if>
            <if test="hxPushState != null ">and hx_push_state = #{hxPushState}</if>
            <if test="subtotalAmount != null ">and subtotal_amount = #{subtotalAmount}</if>
            <if test="subtotalTax != null ">and subtotal_tax = #{subtotalTax}</if>
            <if test="state != null ">and state = #{state}</if>
            <if test="taxUploadState != null ">and tax_upload_state = #{taxUploadState}</if>
            <if test="taxLastUploadTime != null ">and tax_last_upload_time = #{taxLastUploadTime}</if>
        </where>
    </select>

    <select id="selectFinanceStatementBillByStatementId" parameterType="Long" resultMap="FinanceStatementBillResult">
        <include refid="selectFinanceStatementBillVo"/>
        WHERE statement_id = #{statementId} and state = 0
    </select>
    <select id="selectFinanceStatementBillByStatementIds" resultMap="FinanceStatementBillResult">
        <include refid="selectFinanceStatementBillVo"/>
        where statement_id in
        <foreach item="statementId" collection="statementIds" open="(" separator="," close=")">
            #{statementId}
        </foreach>
    </select>

    <insert id="insertFinanceStatementBill" parameterType="FinanceStatementBill">
        INSERT INTO finance_statement_bill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="statementId != null">statement_id,</if>
            <if test="invoiceNo != null and invoiceNo != ''">invoice_no,</if>
            <if test="invoiceNumber != null and invoiceNumber != ''">invoice_number,</if>
            <if test="invoiceMoney != null">invoice_money,</if>
            <if test="invoicePeriod != null">invoice_period,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="hxPushState != null">hx_push_state,</if>
            <if test="subtotalAmount != null">subtotal_amount,</if>
            <if test="subtotalTax != null">subtotal_tax,</if>
            <if test="state != null">state,</if>
            <if test="taxUploadState != null">tax_upload_state,</if>
            <if test="taxLastUploadTime != null">tax_last_upload_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="statementId != null">#{statementId},</if>
            <if test="invoiceNo != null and invoiceNo != ''">#{invoiceNo},</if>
            <if test="invoiceNumber != null and invoiceNumber != ''">#{invoiceNumber},</if>
            <if test="invoiceMoney != null">#{invoiceMoney},</if>
            <if test="invoicePeriod != null">#{invoicePeriod},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="hxPushState != null">#{hxPushState},</if>
            <if test="subtotalAmount != null">#{subtotalAmount},</if>
            <if test="subtotalTax != null">#{subtotalTax},</if>
            <if test="state != null">#{state},</if>
            <if test="taxUploadState != null">#{taxUploadState},</if>
            <if test="taxLastUploadTime != null">#{taxLastUploadTime},</if>
        </trim>
    </insert>
    <insert id="insertFinanceStatementBills">
        insert IGNORE  into finance_statement_bill
        (id,
        statement_id,
        invoice_no,
        invoice_number,
        invoice_money,
        invoice_period,
        create_by,
        create_time,
        hx_push_state,
        subtotal_amount,
        subtotal_tax,
        state,
        tax_upload_state,
        tax_last_upload_time,
        update_by,
        update_time
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.id},
            #{item.statementId},
            #{item.invoiceNo},
            #{item.invoiceNumber},
            #{item.invoiceMoney},
            #{item.invoicePeriod},
            #{item.createBy},
            #{item.createTime},
            #{item.hxPushState},
            #{item.subtotalAmount},
            #{item.subtotalTax},
            #{item.state},
            #{item.taxUploadState},
            #{item.taxLastUploadTime},
            #{item.updateBy},
            #{item.updateTime}
        </foreach>
    </insert>

    <update id="updateFinanceStatementBill" parameterType="FinanceStatementBill">
        UPDATE finance_statement_bill
        <trim prefix="SET" suffixOverrides=",">
            <if test="statementId != null">statement_id = #{statementId},</if>
            <if test="invoiceNo != null and invoiceNo != ''">invoice_no = #{invoiceNo},</if>
            <if test="invoiceNumber != null and invoiceNumber != ''">invoice_number = #{invoiceNumber},</if>
            <if test="invoiceMoney != null">invoice_money = #{invoiceMoney},</if>
            <if test="invoicePeriod != null">invoice_period = #{invoicePeriod},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="hxPushState != null">hx_push_state = #{hxPushState},</if>
            <if test="subtotalAmount != null">subtotal_amount = #{subtotalAmount},</if>
            <if test="subtotalTax != null">subtotal_tax = #{subtotalTax},</if>
            <if test="state != null">state = #{state},</if>
            <if test="taxUploadState != null">tax_upload_state = #{taxUploadState},</if>
            <if test="taxLastUploadTime != null">tax_last_upload_time = #{taxLastUploadTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        WHERE id = #{id}
    </update>
    <update id="logicDeleteFinanceStatementBillById">
        UPDATE finance_statement_bill
        SET state = -1
        WHERE id = #{id}
    </update>
    <update id="logicDeleteFinanceStatementBillByIds">
        UPDATE finance_statement_bill
        SET state = -1,update_time = now(),update_by = #{updateBy}
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and state = 0
    </update>

    <delete id="deleteFinanceStatementBillById" parameterType="Long">
        DELETE
        FROM finance_statement_bill
        WHERE id = #{id}
    </delete>

    <delete id="deleteFinanceStatementBillByIds" parameterType="String">
        DELETE
        FROM finance_statement_bill
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
