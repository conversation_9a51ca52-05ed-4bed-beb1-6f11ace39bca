<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.finance.statement.mapper.FinanceStatementWaybillMapper">

    <resultMap type="FinanceStatementWaybill" id="FinanceStatementWaybillResult">
        <result property="id"    column="id"    />
        <result property="statementId"    column="statement_id"    />
        <result property="waybillId"    column="waybill_id"    />
        <result property="shippingNoteNumber"    column="shipping_note_number"    />
        <result property="orderCreateTime"    column="order_create_time"    />
        <result property="waybillCreateTime"    column="waybill_create_time"    />
        <result property="payTime"    column="pay_time"    />
        <result property="descriptionOfGoods"    column="description_of_goods"    />
        <result property="vehicleNumber"    column="vehicle_number"    />
        <result property="haulway"    column="haulway"    />
        <result property="driverName"    column="driver_name"    />
        <result property="feePrice"    column="fee_price"    />
        <result property="feeAmount"    column="fee_amount"    />
        <result property="feeUnit"    column="fee_unit"    />
        <result property="receiveFare"    column="receive_fare"    />
        <result property="despatchActualDateTime"    column="despatch_actual_date_time"    />
        <result property="goodsReceiptDateTime"    column="goods_receipt_date_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectFinanceStatementWaybillVo">
        select id, statement_id, waybill_id, shipping_note_number, order_create_time, waybill_create_time, pay_time, description_of_goods, vehicle_number, haulway, driver_name, fee_price, fee_amount, fee_unit, receive_fare, despatch_actual_date_time, goods_receipt_date_time, create_by, create_time from finance_statement_waybill
    </sql>

    <select id="selectFinanceStatementWaybillList" parameterType="FinanceStatementWaybill" resultMap="FinanceStatementWaybillResult">
        <include refid="selectFinanceStatementWaybillVo"/>
        <where>
            <if test="statementId != null "> and statement_id = #{statementId}</if>
            <if test="waybillId != null "> and waybill_id = #{waybillId}</if>
            <if test="shippingNoteNumber != null  and shippingNoteNumber != ''"> and shipping_note_number = #{shippingNoteNumber}</if>
            <if test="orderCreateTime != null "> and order_create_time = #{orderCreateTime}</if>
            <if test="waybillCreateTime != null "> and waybill_create_time = #{waybillCreateTime}</if>
            <if test="payTime != null "> and pay_time = #{payTime}</if>
            <if test="descriptionOfGoods != null  and descriptionOfGoods != ''"> and description_of_goods = #{descriptionOfGoods}</if>
            <if test="vehicleNumber != null  and vehicleNumber != ''"> and vehicle_number = #{vehicleNumber}</if>
            <if test="haulway != null  and haulway != ''"> and haulway = #{haulway}</if>
            <if test="driverName != null  and driverName != ''"> and driver_name like concat('%', #{driverName}, '%')</if>
            <if test="feePrice != null "> and fee_price = #{feePrice}</if>
            <if test="feeAmount != null "> and fee_amount = #{feeAmount}</if>
            <if test="feeUnit != null "> and fee_unit = #{feeUnit}</if>
            <if test="receiveFare != null "> and receive_fare = #{receiveFare}</if>
            <if test="despatchActualDateTime != null "> and despatch_actual_date_time = #{despatchActualDateTime}</if>
            <if test="goodsReceiptDateTime != null "> and goods_receipt_date_time = #{goodsReceiptDateTime}</if>
        </where>
    </select>

    <select id="selectFinanceStatementWaybillById" parameterType="Long" resultMap="FinanceStatementWaybillResult">
        <include refid="selectFinanceStatementWaybillVo"/>
        where id = #{id}
    </select>

    <insert id="insertFinanceStatementWaybill" parameterType="FinanceStatementWaybill">
        insert into finance_statement_waybill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="statementId != null">statement_id,</if>
            <if test="waybillId != null">waybill_id,</if>
            <if test="shippingNoteNumber != null and shippingNoteNumber != ''">shipping_note_number,</if>
            <if test="orderCreateTime != null">order_create_time,</if>
            <if test="waybillCreateTime != null">waybill_create_time,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="descriptionOfGoods != null and descriptionOfGoods != ''">description_of_goods,</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">vehicle_number,</if>
            <if test="haulway != null and haulway != ''">haulway,</if>
            <if test="driverName != null and driverName != ''">driver_name,</if>
            <if test="feePrice != null">fee_price,</if>
            <if test="feeAmount != null">fee_amount,</if>
            <if test="feeUnit != null">fee_unit,</if>
            <if test="receiveFare != null">receive_fare,</if>
            <if test="despatchActualDateTime != null">despatch_actual_date_time,</if>
            <if test="goodsReceiptDateTime != null">goods_receipt_date_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="statementId != null">#{statementId},</if>
            <if test="waybillId != null">#{waybillId},</if>
            <if test="shippingNoteNumber != null and shippingNoteNumber != ''">#{shippingNoteNumber},</if>
            <if test="orderCreateTime != null">#{orderCreateTime},</if>
            <if test="waybillCreateTime != null">#{waybillCreateTime},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="descriptionOfGoods != null and descriptionOfGoods != ''">#{descriptionOfGoods},</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">#{vehicleNumber},</if>
            <if test="haulway != null and haulway != ''">#{haulway},</if>
            <if test="driverName != null and driverName != ''">#{driverName},</if>
            <if test="feePrice != null">#{feePrice},</if>
            <if test="feeAmount != null">#{feeAmount},</if>
            <if test="feeUnit != null">#{feeUnit},</if>
            <if test="receiveFare != null">#{receiveFare},</if>
            <if test="despatchActualDateTime != null">#{despatchActualDateTime},</if>
            <if test="goodsReceiptDateTime != null">#{goodsReceiptDateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>
    <insert id="insertFinanceStatementWaybills">
        insert IGNORE  into finance_statement_waybill
        (id,
        statement_id,
        waybill_id,
        shipping_note_number,
        order_create_time,
        waybill_create_time,
        pay_time,
        description_of_goods,
        vehicle_number,
        haulway,
        driver_name,
        fee_price,
        fee_amount,
        fee_unit,
        receive_fare,
        despatch_actual_date_time,
        goods_receipt_date_time,
        create_by,
        create_time
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.id},
            #{item.statementId},
            #{item.waybillId},
            #{item.shippingNoteNumber},
            #{item.orderCreateTime},
            #{item.waybillCreateTime},
            #{item.payTime},
            #{item.descriptionOfGoods},
            #{item.vehicleNumber},
            #{item.haulway},
            #{item.driverName},
            #{item.feePrice},
            #{item.feeAmount},
            #{item.feeUnit},
            #{item.receiveFare},
            <if test="item.despatchActualDateTime != null">#{item.despatchActualDateTime},</if>
            <if test="item.despatchActualDateTime == null">default,</if>
            <if test="item.goodsReceiptDateTime != null">#{item.goodsReceiptDateTime},</if>
            <if test="item.goodsReceiptDateTime == null">default,</if>
            #{item.createBy},
            #{item.createTime}
        </foreach>
    </insert>

    <update id="updateFinanceStatementWaybill" parameterType="FinanceStatementWaybill">
        update finance_statement_waybill
        <trim prefix="SET" suffixOverrides=",">
            <if test="statementId != null">statement_id = #{statementId},</if>
            <if test="waybillId != null">waybill_id = #{waybillId},</if>
            <if test="shippingNoteNumber != null and shippingNoteNumber != ''">shipping_note_number =
                #{shippingNoteNumber},
            </if>
            <if test="orderCreateTime != null">order_create_time = #{orderCreateTime},</if>
            <if test="waybillCreateTime != null">waybill_create_time = #{waybillCreateTime},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="descriptionOfGoods != null and descriptionOfGoods != ''">description_of_goods = #{descriptionOfGoods},</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">vehicle_number = #{vehicleNumber},</if>
            <if test="haulway != null and haulway != ''">haulway = #{haulway},</if>
            <if test="driverName != null and driverName != ''">driver_name = #{driverName},</if>
            <if test="feePrice != null">fee_price = #{feePrice},</if>
            <if test="feeAmount != null">fee_amount = #{feeAmount},</if>
            <if test="feeUnit != null">fee_unit = #{feeUnit},</if>
            <if test="receiveFare != null">receive_fare = #{receiveFare},</if>
            <if test="despatchActualDateTime != null">despatch_actual_date_time = #{despatchActualDateTime},</if>
            <if test="goodsReceiptDateTime != null">goods_receipt_date_time = #{goodsReceiptDateTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFinanceStatementWaybillById" parameterType="Long">
        delete from finance_statement_waybill where id = #{id}
    </delete>

    <delete id="deleteFinanceStatementWaybillByIds" parameterType="String">
        delete from finance_statement_waybill where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectFinanceStatementWaybillIdList" resultType="java.lang.Long">
        select waybill_id from finance_statement_waybill
        <where>
            <if test="statementId != null "> and statement_id = #{statementId}</if>
            <if test="waybillId != null "> and waybill_id = #{waybillId}</if>
            <if test="shippingNoteNumber != null  and shippingNoteNumber != ''"> and shipping_note_number = #{shippingNoteNumber}</if>
            <if test="orderCreateTime != null "> and order_create_time = #{orderCreateTime}</if>
            <if test="waybillCreateTime != null "> and waybill_create_time = #{waybillCreateTime}</if>
            <if test="payTime != null "> and pay_time = #{payTime}</if>
            <if test="descriptionOfGoods != null  and descriptionOfGoods != ''"> and description_of_goods = #{descriptionOfGoods}</if>
            <if test="vehicleNumber != null  and vehicleNumber != ''"> and vehicle_number = #{vehicleNumber}</if>
            <if test="haulway != null  and haulway != ''"> and haulway = #{haulway}</if>
            <if test="driverName != null  and driverName != ''"> and driver_name like concat('%', #{driverName}, '%')</if>
            <if test="feePrice != null "> and fee_price = #{feePrice}</if>
            <if test="feeAmount != null "> and fee_amount = #{feeAmount}</if>
            <if test="feeUnit != null "> and fee_unit = #{feeUnit}</if>
            <if test="receiveFare != null "> and receive_fare = #{receiveFare}</if>
            <if test="despatchActualDateTime != null "> and despatch_actual_date_time = #{despatchActualDateTime}</if>
            <if test="goodsReceiptDateTime != null ">and goods_receipt_date_time = #{goodsReceiptDateTime}</if>
        </where>
    </select>
    <select id="selectOrderListByStatementId" resultType="java.util.Map">
        select fsw.waybill_id taxWaybillId, wr.financing_push_id loanApplyId
        from finance_statement_waybill fsw
                 join huaxia_financing_waybill_record wr on fsw.waybill_id = wr.waybill_id
        where statement_id = #{statementId}
    </select>
    <select id="selectListByStatementId" resultType="java.lang.Long">
        select waybill_id
        from finance_statement_waybill
        where statement_id = #{statementId}
    </select>
    <select id="selectFinanceStatementWaybillByShippingNoteNumber" resultMap="FinanceStatementWaybillResult">
        <include refid="selectFinanceStatementWaybillVo"/>
        where shipping_note_number = #{shippingNoteNumber} order by id desc limit 1
    </select>

    <select id="selectwaybillIdsByStatementNo" resultMap="FinanceStatementWaybillResult">
        <include refid="selectFinanceStatementWaybillVo"/>
        where statement_id = (select id from finance_statement where state != -1 and statement_no = #{statementNo} order
        by create_time desc limit 1 ) group by waybill_id
    </select>
    <select id="selectByWaybillIds" resultMap="FinanceStatementWaybillResult">
        <include refid="selectFinanceStatementWaybillVo"/>
        where waybill_id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </select>

    <select id="selectStatementIdByShippingNoteNumber" resultType="long">
        select fw.statement_id
        from finance_statement_waybill fw
                 join finance_statement fs on fw.statement_id = fs.id
        where fw.shipping_note_number = #{shippingNoteNumber}
        order by fs.create_time desc limit 1
    </select>

    <select id="selectStatementIdByWaybillIds" resultType="long">
        select fw.statement_id
        from finance_statement_waybill fw
                 join finance_statement fs on fw.statement_id = fs.id
        where fw.waybill_id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
        and fs.customer_id = #{customerId}
        and fs.state in (2,3,7,9,11)
    </select>

    <select id="selectStatementIdsByShippingNoteNumber" resultType="long">
        select fw.statement_id
        from finance_statement_waybill fw
                 join finance_statement fs on fw.statement_id = fs.id
        where fw.shipping_note_number = #{shippingNoteNumber}
    </select>

    <select id="stasticWaybillFareByStatementIds"
            resultType="com.zly.project.finance.statement.domain.res.FinanceStatementRes">
        SELECT fsw.statement_id id,SUM(w.pay_fare) AS receiveFare
        FROM finance_statement_waybill fsw
        INNER JOIN waybill w ON fsw.waybill_id = w.id
        where fsw.statement_id in
        <foreach item="statementId" collection="statementIds" open="(" separator="," close=")">
            #{statementId}
        </foreach>
        GROUP BY fsw.statement_id;
    </select>
</mapper>
