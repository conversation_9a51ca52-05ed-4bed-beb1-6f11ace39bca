<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.projectGroup.mapper.ProjectAssociationUserMapper">

    <resultMap type="com.zly.project.projectGroup.domain.ProjectAssociationUser" id="ProjectAssociationUserResult">
        <result property="userId" column="user_id"/>
        <result property="contractId" column="contract_id"/>
        <result property="createClient" column="create_client"/>
    </resultMap>

    <select id="selectProjectAssociationUserList" resultMap="ProjectAssociationUserResult">
        SELECT user_id, contract_id, create_client
        FROM project_association_user
        WHERE 1 = 1
        <if test="contractId != null">AND contract_id = #{contractId}</if>
        <if test="userId != null">AND user_id = #{userId}</if>
        <if test="createClient != null">AND create_client = #{createClient}</if>
    </select>

    <insert id="insertProjectAssociationUser" parameterType="ProjectAssociationUser">
        INSERT INTO project_association_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="contractId != null">contract_id,</if>
            <if test="createClient != null">create_client,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="contractId != null">#{contractId},</if>
            <if test="createClient != null">#{createClient},</if>
        </trim>
    </insert>
    <insert id="batchInsertProjectAssociationUser">
        insert ignore into project_association_user(user_id, contract_id,create_client) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.userId},#{item.contractId},#{item.createClient})
        </foreach>
    </insert>

    <delete id="deleteProjectAssociationUser">
        DELETE
        FROM project_association_user
        WHERE create_client = #{createClient}
          AND user_id = #{userId}
    </delete>


</mapper>
