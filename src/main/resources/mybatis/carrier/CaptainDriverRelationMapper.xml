<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.carrier.carCaptain.mapper.CaptainDriverRelationMapper">

    <resultMap type="CaptainDriverRelation" id="CaptainDriverRelationResult">
        <result property="carCaptainId"    column="car_captain_id"    />
        <result property="carCaptainIdentityCard"    column="car_captain_identity_card"    />
        <result property="driverId"    column="driver_id"    />
        <result property="drivingLicense"    column="driving_license"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="signingState"    column="signing_state"    />
    </resultMap>

    <sql id="selectCaptainDriverRelationVo">
        select car_captain_id, car_captain_identity_card, driver_id, driving_license, create_by, create_time, update_by, update_time, remark, signing_state from captain_driver_relation
    </sql>

    <select id="selectCaptainDriverRelationList" parameterType="CaptainDriverRelation" resultMap="CaptainDriverRelationResult">
        <include refid="selectCaptainDriverRelationVo"/>
        <where>
            <if test="carCaptainIdentityCard != null  and carCaptainIdentityCard != ''">and car_captain_identity_card = #{carCaptainIdentityCard}</if>
            <if test="drivingLicense != null  and drivingLicense != ''">and driving_license = #{drivingLicense}</if>
            <if test="signingState != null ">and signing_state = #{signingState}</if>
            <if test="driverId != null ">and driver_id = #{driverId}</if>
            <if test="carCaptainId != null ">and car_captain_id = #{carCaptainId}</if>
        </where>
    </select>

    <select id="selectCaptainDriverRelationByCarCaptainId" parameterType="Long" resultMap="CaptainDriverRelationResult">
        <include refid="selectCaptainDriverRelationVo"/>
        where car_captain_id = #{carCaptainId}
    </select>

    <insert id="insertCaptainDriverRelation" parameterType="CaptainDriverRelation">
        insert into captain_driver_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="carCaptainId != null">car_captain_id,</if>
            <if test="carCaptainIdentityCard != null and carCaptainIdentityCard != ''">car_captain_identity_card,</if>
            <if test="driverId != null">driver_id,</if>
            <if test="drivingLicense != null and drivingLicense != ''">driving_license,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="signingState != null">signing_state,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="carCaptainId != null">#{carCaptainId},</if>
            <if test="carCaptainIdentityCard != null and carCaptainIdentityCard != ''">#{carCaptainIdentityCard},</if>
            <if test="driverId != null">#{driverId},</if>
            <if test="drivingLicense != null and drivingLicense != ''">#{drivingLicense},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="signingState != null">#{signingState},</if>
         </trim>
    </insert>

    <update id="updateCaptainDriverRelation" parameterType="CaptainDriverRelation">
        update captain_driver_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="carCaptainIdentityCard != null and carCaptainIdentityCard != ''">car_captain_identity_card = #{carCaptainIdentityCard},</if>
            <if test="drivingLicense != null and drivingLicense != ''">driving_license = #{drivingLicense},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="signingState != null">signing_state = #{signingState},</if>
        </trim>
        where car_captain_id = #{carCaptainId} and driver_id = #{driverId}
    </update>

    <delete id="deleteCaptainDriverRelationByCarCaptainId" parameterType="Long">
        delete from captain_driver_relation where car_captain_id = #{carCaptainId}
    </delete>

    <delete id="deleteCaptainDriverRelationByCarCaptainIds" parameterType="String">
        delete from captain_driver_relation where car_captain_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCaptainDriverRelation">
        delete
        from captain_driver_relation
        where car_captain_id = #{carCaptainId}
          and driver_id = #{driverId}
    </delete>

    <select id="checkDriverCaptainBinding" resultMap="CaptainDriverRelationResult">
        select a.*
        from captain_driver_relation a
                 left join driver b on a.driver_id = b.id
        where b.telephone = #{phoneNumber}
          and a.car_captain_identity_card != a.driving_license
            and a.signing_state = 2
    </select>

    <insert id="insertCaptainDriverRelationList">
        insert ignore into captain_driver_relation(car_captain_id, car_captain_identity_card, driver_id, driving_license, create_by, create_time, signing_state)
        values
        <foreach item="item" index="index" collection="list" open="" close="" separator=",">
            (#{item.carCaptainId}, #{item.carCaptainIdentityCard}, #{item.driverId}, #{item.drivingLicense}, #{item.createBy}, #{item.createTime}, #{item.signingState})
        </foreach>
    </insert>

    <select id="selectCaptainDriverRelationListByIdentityCards"
            resultMap="CaptainDriverRelationResult">
        <include refid="selectCaptainDriverRelationVo"/>
        where car_captain_identity_card in
        <foreach item="identityCard" collection="identityCards" open="(" separator="," close=")">
            #{identityCard}
        </foreach>
    </select>
</mapper>
