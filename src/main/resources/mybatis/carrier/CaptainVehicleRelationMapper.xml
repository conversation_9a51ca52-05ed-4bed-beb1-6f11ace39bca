<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.carrier.carCaptain.mapper.CaptainVehicleRelationMapper">

    <resultMap type="CaptainVehicleRelation" id="CaptainVehicleRelationResult">
        <result property="carCaptainId"    column="car_captain_id"    />
        <result property="carCaptainIdentityCard"    column="car_captain_identity_card"    />
        <result property="vehicleId"    column="vehicle_id"    />
        <result property="vehicleNumber"    column="vehicle_number"    />
        <result property="vehiclePlateColorCode"    column="vehicle_plate_color_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="signingState"    column="signing_state"    />
    </resultMap>

    <sql id="selectCaptainVehicleRelationVo">
        select car_captain_id, car_captain_identity_card, vehicle_id, vehicle_number, vehicle_plate_color_code, create_by, create_time, update_by, update_time, remark, signing_state from captain_vehicle_relation
    </sql>

    <select id="selectCaptainVehicleRelationList" parameterType="CaptainVehicleRelation" resultMap="CaptainVehicleRelationResult">
        <include refid="selectCaptainVehicleRelationVo"/>
        <where>
            <if test="carCaptainIdentityCard != null  and carCaptainIdentityCard != ''"> and car_captain_identity_card = #{carCaptainIdentityCard}</if>
            <if test="vehicleNumber != null  and vehicleNumber != ''"> and vehicle_number = #{vehicleNumber}</if>
            <if test="vehiclePlateColorCode != null "> and vehicle_plate_color_code = #{vehiclePlateColorCode}</if>
            <if test="signingState != null "> and signing_state = #{signingState}</if>
            <if test="carCaptainId != null "> and car_captain_id = #{carCaptainId}</if>
            <if test="vehicleId != null "> and vehicle_id = #{vehicleId}</if>
        </where>
    </select>

    <select id="selectCaptainVehicleRelationByCarCaptainId" parameterType="Long" resultMap="CaptainVehicleRelationResult">
        <include refid="selectCaptainVehicleRelationVo"/>
        where car_captain_id = #{carCaptainId}
    </select>

    <insert id="insertCaptainVehicleRelation" parameterType="CaptainVehicleRelation">
        insert into captain_vehicle_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="carCaptainId != null">car_captain_id,</if>
            <if test="carCaptainIdentityCard != null and carCaptainIdentityCard != ''">car_captain_identity_card,</if>
            <if test="vehicleId != null">vehicle_id,</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">vehicle_number,</if>
            <if test="vehiclePlateColorCode != null">vehicle_plate_color_code,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="signingState != null">signing_state,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="carCaptainId != null">#{carCaptainId},</if>
            <if test="carCaptainIdentityCard != null and carCaptainIdentityCard != ''">#{carCaptainIdentityCard},</if>
            <if test="vehicleId != null">#{vehicleId},</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">#{vehicleNumber},</if>
            <if test="vehiclePlateColorCode != null">#{vehiclePlateColorCode},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="signingState != null">#{signingState},</if>
         </trim>
    </insert>

    <update id="updateCaptainVehicleRelation" parameterType="CaptainVehicleRelation">
        update captain_vehicle_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="carCaptainIdentityCard != null and carCaptainIdentityCard != ''">car_captain_identity_card = #{carCaptainIdentityCard},</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">vehicle_number = #{vehicleNumber},</if>
            <if test="vehiclePlateColorCode != null">vehicle_plate_color_code = #{vehiclePlateColorCode},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="signingState != null">signing_state = #{signingState},</if>
        </trim>
        where car_captain_id = #{carCaptainId} and vehicle_id = #{vehicleId}
    </update>

    <delete id="deleteCaptainVehicleRelationByCarCaptainId" parameterType="Long">
        delete from captain_vehicle_relation where car_captain_id = #{carCaptainId}
    </delete>

    <delete id="deleteCaptainVehicleRelationByCarCaptainIds" parameterType="String">
        delete from captain_vehicle_relation where car_captain_id in
        <foreach item="carCaptainId" collection="carCaptainIds" open="(" separator="," close=")">
            #{carCaptainId}
        </foreach>
    </delete>

    <delete id="deleteCaptainVehicleRelation">
        delete from captain_vehicle_relation where car_captain_id = #{carCaptainId} and vehicle_id = #{vehicleId}
    </delete>

    <insert id="insertCaptainVehicleRelationList">
        insert ignore into captain_vehicle_relation (car_captain_id, car_captain_identity_card, vehicle_id, vehicle_number, vehicle_plate_color_code, create_by, create_time, signing_state)
        values
        <foreach item="item" index="index" collection="list" open="" close="" separator=",">
            (#{item.carCaptainId}, #{item.carCaptainIdentityCard}, #{item.vehicleId}, #{item.vehicleNumber}, #{item.vehiclePlateColorCode}, #{item.createBy}, #{item.createTime},
             #{item.signingState})
        </foreach>
    </insert>

</mapper>
