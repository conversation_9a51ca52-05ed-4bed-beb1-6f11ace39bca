<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.carrier.carCaptain.mapper.CaptainDriverProtocolMapper">

    <resultMap type="CaptainDriverProtocol" id="CaptainDriverProtocolResult">
        <result property="id"    column="id"    />
        <result property="carCaptainId"    column="car_captain_id"    />
        <result property="carCaptainIdentityCard"    column="car_captain_identity_card"    />
        <result property="driverId"    column="driver_id"    />
        <result property="drivingLicense"    column="driving_license"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileType"    column="file_type"    />
        <result property="state"    column="state"    />
        <result property="signState"    column="sign_state"    />
        <result property="periodValidity"    column="period_validity"    />
        <result property="resource"    column="resource"    />
        <result property="createCustomerId"    column="create_customer_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="protocolCode" column="protocol_code"/>
    </resultMap>

    <sql id="selectCaptainDriverProtocolVo">
        select id,
               car_captain_id,
               car_captain_identity_card,
               driver_id,
               driving_license,
               file_url,
               file_type,
               state,
               sign_state,
               period_validity,
               resource,
               create_customer_id,
               create_by,
               create_time,
               protocol_code
        from captain_driver_protocol
    </sql>

    <select id="selectCaptainDriverProtocolList" parameterType="CaptainDriverProtocol" resultMap="CaptainDriverProtocolResult">
        <include refid="selectCaptainDriverProtocolVo"/>
        <where>
            <if test="carCaptainId != null "> and car_captain_id = #{carCaptainId}</if>
            <if test="carCaptainIdentityCard != null  and carCaptainIdentityCard != ''"> and car_captain_identity_card = #{carCaptainIdentityCard}</if>
            <if test="driverId != null "> and driver_id = #{driverId}</if>
            <if test="drivingLicense != null  and drivingLicense != ''"> and driving_license = #{drivingLicense}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="signState != null "> and sign_state = #{signState}</if>
            <if test="periodValidity != null "> and period_validity = #{periodValidity}</if>
            <if test="resource != null "> and resource = #{resource}</if>
            <if test="createCustomerId != null "> and create_customer_id = #{createCustomerId}</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
            <if test="protocolCode != null and protocolCode != ''">and protocol_code = #{protocolCode}</if>
        </where>
    </select>

    <select id="selectCaptainDriverProtocolById" parameterType="Long" resultMap="CaptainDriverProtocolResult">
        <include refid="selectCaptainDriverProtocolVo"/>
        where id = #{id}
    </select>

    <insert id="insertCaptainDriverProtocol" parameterType="CaptainDriverProtocol">
        insert into captain_driver_protocol
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="carCaptainId != null">car_captain_id,</if>
            <if test="carCaptainIdentityCard != null and carCaptainIdentityCard != ''">car_captain_identity_card,</if>
            <if test="driverId != null">driver_id,</if>
            <if test="drivingLicense != null and drivingLicense != ''">driving_license,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileType != null and fileType != ''">file_type,</if>
            <if test="state != null">state,</if>
            <if test="signState != null">sign_state,</if>
            <if test="periodValidity != null">period_validity,</if>
            <if test="resource != null">resource,</if>
            <if test="createCustomerId != null">create_customer_id,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="protocolCode != null and protocolCode != ''">protocol_code,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="carCaptainId != null">#{carCaptainId},</if>
            <if test="carCaptainIdentityCard != null and carCaptainIdentityCard != ''">#{carCaptainIdentityCard},</if>
            <if test="driverId != null">#{driverId},</if>
            <if test="drivingLicense != null and drivingLicense != ''">#{drivingLicense},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileType != null and fileType != ''">#{fileType},</if>
            <if test="state != null">#{state},</if>
            <if test="signState != null">#{signState},</if>
            <if test="periodValidity != null">#{periodValidity},</if>
            <if test="resource != null">#{resource},</if>
            <if test="createCustomerId != null">#{createCustomerId},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="protocolCode != null and protocolCode != ''">#{protocolCode},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateCaptainDriverProtocol" parameterType="CaptainDriverProtocol">
        update captain_driver_protocol
        <trim prefix="SET" suffixOverrides=",">
            <if test="carCaptainId != null">car_captain_id = #{carCaptainId},</if>
            <if test="carCaptainIdentityCard != null and carCaptainIdentityCard != ''">car_captain_identity_card = #{carCaptainIdentityCard},</if>
            <if test="driverId != null">driver_id = #{driverId},</if>
            <if test="drivingLicense != null and drivingLicense != ''">driving_license = #{drivingLicense},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="fileType != null and fileType != ''">file_type = #{fileType},</if>
            <if test="state != null">state = #{state},</if>
            <if test="signState != null">sign_state = #{signState},</if>
            <if test="periodValidity != null">period_validity = #{periodValidity},</if>
            <if test="resource != null">resource = #{resource},</if>
            <if test="createCustomerId != null">create_customer_id = #{createCustomerId},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="protocolCode != null and protocolCode != ''">protocol_code = #{protocolCode},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCaptainDriverProtocolById" parameterType="Long">
        delete from captain_driver_protocol where id = #{id}
    </delete>

    <delete id="deleteCaptainDriverProtocolByIds" parameterType="String">
        delete from captain_driver_protocol where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="logicDeleteCaptainDriverProtocol">
        update captain_driver_protocol set state = -1 where car_captain_id = #{carCaptainId} and driver_id = #{driverId} and resource  = #{resource}
    </update>

    <insert id="insertCaptainDriverProtocolList">
        insert ignore into captain_driver_protocol(id, car_captain_id, car_captain_identity_card, driver_id, driving_license, file_url, file_type,state, sign_state, resource, create_by,
        create_time,protocol_code)
        values
        <foreach item="item" index="index" collection="list" open="" close="" separator=",">
            (#{item.id}, #{item.carCaptainId}, #{item.carCaptainIdentityCard}, #{item.driverId}, #{item.drivingLicense}, #{item.fileUrl},#{item.fileType}, #{item.state}, #{item.signState},
            #{item.resource},
            #{item.createBy}, #{item.createTime}, #{item.protocolCode})
        </foreach>
    </insert>

    <select id="selectCaptainDriverProtocolListByIdentityCards"
            resultMap="CaptainDriverProtocolResult">
        <include refid="selectCaptainDriverProtocolVo"/>
        where car_captain_identity_card in
        <foreach item="identityCard" collection="identityCards" open="(" separator="," close=")">
            #{identityCard}
        </foreach>
    </select>

    <delete id="deleteCaptainDriverProtocolByCaptainIds">
        delete from captain_driver_protocol where car_captain_id in
        <foreach item="captainId" collection="captainIds" open="(" separator="," close=")">
            #{captainId}
        </foreach>
    </delete>
</mapper>
