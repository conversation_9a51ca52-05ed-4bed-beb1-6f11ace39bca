<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.carrier.driver.mapper.CarrierUserMapper">
    <resultMap type="com.zly.project.carrier.driver.domain.CarrierUser" id="CarrierUserResult">
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="userType"    column="user_type"    />
        <result property="email"    column="email"    />
        <result property="phonenumber"    column="phonenumber"    />
        <result property="sex"    column="sex"    />
        <result property="avatar"    column="avatar"    />
        <result property="password"    column="password"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="loginIp"    column="login_ip"    />
        <result property="loginDate"    column="login_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="identityCard" column="identity_card" />
        <result property="carrierId"    column="carrier_id"    />
        <result property="auxiliaryStaffId"    column="auxiliary_staff_id"    />
        <result property="carCaptainId"    column="car_captain_id"    />
        <result property="driverId"    column="driver_id"    />
    </resultMap>

    <sql id="selectCarrierUserVo">
        select user_id, dept_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, password, status, del_flag,
               login_ip, login_date, create_by, create_time, update_by, update_time, remark, identity_card,carrier_id,auxiliary_staff_id,car_captain_id,driver_id
        from carrier_user
    </sql>

    <select id="selectCarrierUserList" parameterType="com.zly.project.carrier.driver.domain.CarrierUser" resultMap="CarrierUserResult">
        <include refid="selectCarrierUserVo"/>
        <where>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="phonenumber != null  and phonenumber != ''"> and phonenumber = #{phonenumber}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="delFlag != null  and delFlag != ''"> and del_flag = #{delFlag}</if>
            <if test="loginIp != null  and loginIp != ''"> and login_ip = #{loginIp}</if>
            <if test="loginDate != null "> and login_date = #{loginDate}</if>
            <if test="identityCard != null"> and identity_card = #{identityCard}</if>
            <if test="carrierId != null "> and carrier_id = #{carrierId}</if>
            <if test="auxiliaryStaffId != null "> and auxiliary_staff_id = #{auxiliaryStaffId}</if>
            <if test="carCaptainId != null "> and car_captain_id = #{carCaptainId}</if>
            <if test="driverId != null "> and driver_id = #{driverId}</if>
        </where>
    </select>

    <select id="selectCarrierUserByUserId" parameterType="Long" resultMap="CarrierUserResult">
        <include refid="selectCarrierUserVo"/>
        where user_id = #{userId} and del_flag =0
    </select>

    <insert id="insertCarrierUser" parameterType="com.zly.project.carrier.driver.domain.CarrierUser" useGeneratedKeys="true" keyProperty="userId">
        insert into carrier_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="userType != null">user_type,</if>
            <if test="email != null">email,</if>
            <if test="phonenumber != null">phonenumber,</if>
            <if test="sex != null">sex,</if>
            <if test="avatar != null">avatar,</if>
            <if test="password != null">password,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="loginIp != null">login_ip,</if>
            <if test="loginDate != null">login_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="identityCard != null">identity_card,</if>
            <if test="carrierId != null">carrier_id,</if>
            <if test="auxiliaryStaffId != null">auxiliary_staff_id,</if>
            <if test="carCaptainId != null">car_captain_id,</if>
            <if test="driverId != null">driver_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="userType != null">#{userType},</if>
            <if test="email != null">#{email},</if>
            <if test="phonenumber != null">#{phonenumber},</if>
            <if test="sex != null">#{sex},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="password != null">#{password},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="loginIp != null">#{loginIp},</if>
            <if test="loginDate != null">#{loginDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="identityCard != null">#{identityCard},</if>
            <if test="carrierId != null">#{carrierId},</if>
            <if test="auxiliaryStaffId != null">#{auxiliaryStaffId},</if>
            <if test="carCaptainId != null">#{carCaptainId},</if>
            <if test="driverId != null">#{driverId},</if>
        </trim>
    </insert>

    <update id="updateCarrierUser" parameterType="com.zly.project.carrier.driver.domain.CarrierUser">
        update carrier_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="email != null">email = #{email},</if>
            <if test="phonenumber != null">phonenumber = #{phonenumber},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="password != null">password = #{password},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="loginIp != null">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="identityCard != null">identity_card = #{identityCard},</if>
            <if test="carrierId != null">carrier_id = #{carrierId},</if>
            <if test="auxiliaryStaffId != null">auxiliary_staff_id = #{auxiliaryStaffId},</if>
            <if test="carCaptainId != null">car_captain_id = #{carCaptainId},</if>
            <if test="driverId != null">driver_id = #{driverId},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteCarrierUserByUserId" parameterType="Long">
        delete from carrier_user where user_id = #{userId}
    </delete>

    <delete id="deleteCarrierUserByUserIds" parameterType="String">
        delete from carrier_user where user_id in
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <select id="selectDriverIdByCarCaptainId" parameterType="com.zly.project.carrier.driver.domain.CarrierUser" resultMap="CarrierUserResult">
        select b.driver_id from actual_carrier_info a inner join carrier_user b
        on a.id = b.car_captain_id
        where a.id = #{id}
    </select>
    <select id="selectDriverIdByCarrierId" resultType="java.lang.Long">
        select driver_id
        from carrier_user
        where car_captain_id = #{actualCarrierId}
          and del_flag = 0
    </select>



    <select id="selectCarrierIdUserByDriverId" resultType="java.lang.Long">
        select a.user_id
        from carrier_user a
                 inner join actual_carrier_info b on a.carrier_id = b.id
        where b.driver_id = #{driverId}
          and a.status = 0
          and a.del_flag = 0
    </select>


    <select id="selectCarrierUserIdUserByActualId" resultType="java.lang.Long">
        select user_id
        from carrier_user
        where carrier_id = #{actualCarrierId}
          and status = 0
          and del_flag = 0
    </select>

    <select id="selectCarrierUserListByNotExistsCarAuxiliary" resultMap="CarrierUserResult">
        select cu.user_id, cu.dept_id, cu.user_name, cu.nick_name, cu.user_type, cu.email, cu.phonenumber, cu.sex, cu.avatar, cu.password, cu.status, cu.del_flag,
               cu.login_ip, cu.login_date, cu.create_by, cu.create_time, cu.update_by, cu.update_time, cu.remark, cu.identity_card,cu.carrier_id
        from carrier_user cu
                 join actual_carrier_info cai on cai.identity_card = cu.identity_card
        where cu.status = 0 and cu.del_flag = 0 and cai.auxiliary_staff_id = 0 and cai.car_captain_id = 0
    </select>

    <select id="selectCarrierUserListByIdentityCards" resultMap="CarrierUserResult">
        <include refid="selectCarrierUserVo"/>
        where del_flag != 2 and `status` != 1 and  identity_card in
        <foreach item="identityCard" collection="identityCards" open="(" separator="," close=")">
            #{identityCard}
        </foreach>
    </select>
</mapper>
