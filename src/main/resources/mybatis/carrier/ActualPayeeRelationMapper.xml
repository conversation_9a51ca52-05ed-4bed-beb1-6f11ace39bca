<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.carrier.payee.mapper.ActualPayeeRelationMapper">
    
    <resultMap type="ActualPayeeRelation" id="ActualPayeeRelationResult">
        <result property="actualPayeeRelationId"    column="actual_payee_relation_id"    />
        <result property="actualIdentityCard"    column="actual_identity_card"    />
        <result property="bankCardNo"    column="bank_card_no"    />
        <result property="relationType"    column="relation_type"    />
        <result property="isBind"    column="is_bind"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectActualPayeeRelationVo">
        select actual_payee_relation_id, actual_identity_card, bank_card_no, relation_type,is_bind, remark, create_by, create_time, update_by, update_time from actual_payee_relation
    </sql>

    <select id="selectActualPayeeRelationList" parameterType="ActualPayeeRelation" resultMap="ActualPayeeRelationResult">
        <include refid="selectActualPayeeRelationVo"/>
        <where>  
            <if test="actualIdentityCard != null  and actualIdentityCard != ''"> and actual_identity_card = #{actualIdentityCard}</if>
            <if test="bankCardNo != null  and bankCardNo != ''"> and bank_card_no = #{bankCardNo}</if>
            <if test="relationType != null "> and relation_type = #{relationType}</if>
            <if test="isBind != null "> and is_bind = #{isBind}</if>
        </where>
    </select>
    
    <select id="selectActualPayeeRelationByActualPayeeRelationId" parameterType="Long" resultMap="ActualPayeeRelationResult">
        <include refid="selectActualPayeeRelationVo"/>
        where actual_payee_relation_id = #{actualPayeeRelationId}
    </select>
        
    <insert id="insertActualPayeeRelation" parameterType="ActualPayeeRelation">
        insert into actual_payee_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="actualPayeeRelationId != null">actual_payee_relation_id,</if>
            <if test="actualIdentityCard != null and actualIdentityCard != ''">actual_identity_card,</if>
            <if test="bankCardNo != null and bankCardNo != ''">bank_card_no,</if>
            <if test="relationType != null">relation_type,</if>
            <if test="isBind != null">is_bind,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="actualPayeeRelationId != null">#{actualPayeeRelationId},</if>
            <if test="actualIdentityCard != null and actualIdentityCard != ''">#{actualIdentityCard},</if>
            <if test="bankCardNo != null and bankCardNo != ''">#{bankCardNo},</if>
            <if test="relationType != null">#{relationType},</if>
            <if test="isBind != null">#{isBind},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateActualPayeeRelation" parameterType="ActualPayeeRelation">
        update actual_payee_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="actualIdentityCard != null and actualIdentityCard != ''">actual_identity_card = #{actualIdentityCard},</if>
            <if test="bankCardNo!= null and bankCardNo!= ''">bank_card_no = #{bankCardNo},</if>
            <if test="relationType != null">relation_type = #{relationType},</if>
            <if test="isBind != null">is_bind = #{isBind},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where actual_payee_relation_id = #{actualPayeeRelationId}
    </update>

    <delete id="deleteActualPayeeRelationByActualPayeeRelationId" parameterType="Long">
        delete from actual_payee_relation where actual_payee_relation_id = #{actualPayeeRelationId}
    </delete>

    <delete id="deleteActualPayeeRelationByActualPayeeRelationIds" parameterType="String">
        delete from actual_payee_relation where actual_payee_relation_id in 
        <foreach item="actualPayeeRelationId" collection="array" open="(" separator="," close=")">
            #{actualPayeeRelationId}
        </foreach>
    </delete>
</mapper>