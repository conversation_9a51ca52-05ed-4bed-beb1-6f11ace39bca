<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.carrier.auxiliary.mapper.AuxiliaryDriverProtocolMapper">

    <resultMap type="AuxiliaryDriverProtocol" id="AuxiliaryDriverProtocolResult">
        <result property="id"    column="id"    />
        <result property="auxiliaryStaffId"    column="auxiliary_staff_id"    />
        <result property="auxiliaryStaffIdentityCard"    column="auxiliary_staff_identity_card"    />
        <result property="driverId"    column="driver_id"    />
        <result property="drivingLicense"    column="driving_license"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileType"    column="file_type"    />
        <result property="state"    column="state"    />
        <result property="signState"    column="sign_state"    />
        <result property="periodValidity"    column="period_validity"    />
        <result property="resource"    column="resource"    />
        <result property="customerId"    column="customer_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="protocolCode" column="protocol_code"/>
    </resultMap>

    <sql id="selectAuxiliaryDriverProtocolVo">
        select id,
               auxiliary_staff_id,
               auxiliary_staff_identity_card,
               driver_id,
               driving_license,
               file_url,
               file_type,
               state,
               sign_state,
               period_validity,
               resource,
               customer_id,
               create_by,
               create_time,
               protocol_code
        from auxiliary_driver_protocol
    </sql>

    <select id="selectAuxiliaryDriverProtocolList" parameterType="AuxiliaryDriverProtocol" resultMap="AuxiliaryDriverProtocolResult">
        <include refid="selectAuxiliaryDriverProtocolVo"/>
        <where>
            <if test="auxiliaryStaffId != null "> and auxiliary_staff_id = #{auxiliaryStaffId}</if>
            <if test="auxiliaryStaffIdentityCard != null  and auxiliaryStaffIdentityCard != ''"> and auxiliary_staff_identity_card = #{auxiliaryStaffIdentityCard}</if>
            <if test="driverId != null "> and driver_id = #{driverId}</if>
            <if test="drivingLicense != null  and drivingLicense != ''"> and driving_license = #{drivingLicense}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="signState != null "> and sign_state = #{signState}</if>
            <if test="periodValidity != null ">and period_validity = #{periodValidity}</if>
            <if test="resource != null ">and resource = #{resource}</if>
            <if test="customerId != null ">and customer_id = #{customerId}</if>
            <if test="protocolCode != null and protocolCode != '' ">and protocol_code = #{protocolCode}</if>
        </where>
    </select>

    <select id="selectAuxiliaryDriverProtocolById" parameterType="Long" resultMap="AuxiliaryDriverProtocolResult">
        <include refid="selectAuxiliaryDriverProtocolVo"/>
        where id = #{id}
    </select>
    <select id="selectByAuxiliaryStaffIdAndDriverId" resultMap="AuxiliaryDriverProtocolResult">
        <include refid="selectAuxiliaryDriverProtocolVo"/>
        where state = 0
        <if test="auxiliaryStaffId != null ">and auxiliary_staff_id = #{auxiliaryStaffId}</if>
        <if test="driverId != null ">and driver_id = #{driverId}</if>
    </select>

    <insert id="insertAuxiliaryDriverProtocol" parameterType="AuxiliaryDriverProtocol">
        insert into auxiliary_driver_protocol
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="auxiliaryStaffId != null">auxiliary_staff_id,</if>
            <if test="auxiliaryStaffIdentityCard != null and auxiliaryStaffIdentityCard != ''">
                auxiliary_staff_identity_card,
            </if>
            <if test="driverId != null">driver_id,</if>
            <if test="drivingLicense != null and drivingLicense != ''">driving_license,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileType != null and fileType != ''">file_type,</if>
            <if test="state != null">state,</if>
            <if test="signState != null">sign_state,</if>
            <if test="periodValidity != null">period_validity,</if>
            <if test="resource != null">resource,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="protocolCode != null and protocolCode != ''">protocol_code,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="auxiliaryStaffId != null">#{auxiliaryStaffId},</if>
            <if test="auxiliaryStaffIdentityCard != null and auxiliaryStaffIdentityCard != ''">#{auxiliaryStaffIdentityCard},</if>
            <if test="driverId != null">#{driverId},</if>
            <if test="drivingLicense != null and drivingLicense != ''">#{drivingLicense},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileType != null and fileType != ''">#{fileType},</if>
            <if test="state != null">#{state},</if>
            <if test="signState != null">#{signState},</if>
            <if test="periodValidity != null">#{periodValidity},</if>
            <if test="resource != null">#{resource},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="protocolCode != null and protocolCode != ''">#{protocolCode},</if>
         </trim>
    </insert>

    <update id="updateAuxiliaryDriverProtocol" parameterType="AuxiliaryDriverProtocol">
        update auxiliary_driver_protocol
        <trim prefix="SET" suffixOverrides=",">
            <if test="auxiliaryStaffId != null">auxiliary_staff_id = #{auxiliaryStaffId},</if>
            <if test="auxiliaryStaffIdentityCard != null and auxiliaryStaffIdentityCard != ''">auxiliary_staff_identity_card = #{auxiliaryStaffIdentityCard},</if>
            <if test="driverId != null">driver_id = #{driverId},</if>
            <if test="drivingLicense != null and drivingLicense != ''">driving_license = #{drivingLicense},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="fileType != null and fileType != ''">file_type = #{fileType},</if>
            <if test="state != null">state = #{state},</if>
            <if test="signState != null">sign_state = #{signState},</if>
            <if test="periodValidity != null">period_validity = #{periodValidity},</if>
            <if test="resource != null">resource = #{resource},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="protocolCode != null and protocolCode != ''">protocol_code = #{protocolCode},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAuxiliaryDriverProtocolById" parameterType="Long">
        delete from auxiliary_driver_protocol where id = #{id}
    </delete>

    <delete id="deleteAuxiliaryDriverProtocolByIds" parameterType="String">
        delete from auxiliary_driver_protocol where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertAuxiliaryDriverProtocolList">
        insert ignore into auxiliary_driver_protocol(id, auxiliary_staff_id, auxiliary_staff_identity_card, driver_id, driving_license, file_url,file_type, state, sign_state, resource, create_by,
        create_time,protocol_code)
        values
        <foreach item="item" index="index" collection="list" open="" close="" separator=",">
            (#{item.id}, #{item.auxiliaryStaffId}, #{item.auxiliaryStaffIdentityCard}, #{item.driverId}, #{item.drivingLicense}, #{item.fileUrl},#{item.fileType}, #{item.state}, #{item.signState},
            #{item.resource},
            #{item.createBy}, #{item.createTime}, #{item.protocolCode})
        </foreach>
    </insert>

    <select id="selectAuxiliaryDriverProtocolListByIdentityCards"
            resultMap="AuxiliaryDriverProtocolResult">
        <include refid="selectAuxiliaryDriverProtocolVo"/>
        where auxiliary_staff_identity_card in
        <foreach item="identityCard" collection="identityCards" open="(" separator="," close=")">
            #{identityCard}
        </foreach>
    </select>

    <delete id="deleteAuxiliaryDriverProtocolByIdentityCards">
        delete from auxiliary_driver_protocol where auxiliary_staff_identity_card in
        <foreach item="identityCard" collection="identityCards" open="(" separator="," close=")">
            #{identityCard}
        </foreach>
    </delete>
</mapper>
