<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.carrier.vehicle.mapper.CustomerVehicleRelationMapper">

    <resultMap type="com.zly.project.carrier.vehicle.domain.CustomerVehicleRelation" id="CustomerVehicleRelationResult">
        <result property="customerId" column="customer_id"/>
        <result property="vehicleId" column="vehicle_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="vehicleNumber" column="vehicle_number"/>
        <result property="vehiclePlateColorCode" column="vehicle_plate_color_code"/>
    </resultMap>

    <sql id="selectCustomerVehicleRelationVo">
        SELECT customer_id,
               vehicle_id,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               vehicle_number,
               vehicle_plate_color_code
        FROM customer_vehicle_relation
    </sql>

    <select id="selectCustomerVehicleRelationList"
            parameterType="com.zly.project.carrier.vehicle.domain.CustomerVehicleRelation"
            resultMap="CustomerVehicleRelationResult">
        <include refid="selectCustomerVehicleRelationVo"/>
        <where>
            <if test="customerId != null  and customerId != ''">and customer_id = #{customerId}</if>
            <if test="vehicleId != null  and vehicleId != ''">and vehicle_id = #{vehicleId}</if>
        </where>
    </select>

    <select id="selectCustomerVehicleRelationByCustomerId" parameterType="Long"
            resultMap="CustomerVehicleRelationResult">
        <include refid="selectCustomerVehicleRelationVo"/>
        where customer_id = #{customerId} and vehicle_id = #{vehicleId}
    </select>

    <select id="selectCustomerVehicleRelationByVehicleNumber"  resultMap="CustomerVehicleRelationResult">
        SELECT a.customer_id, a.vehicle_id, a.create_by, a.create_time, a.update_by, a.update_time, a.remark
        FROM customer_vehicle_relation a
        inner join vehicle v on a.vehicle_id = v.id
        where a.customer_id = #{customerId} and v.vehicle_number = #{vehicleNumber}
    </select>

    <insert id="insertCustomerVehicleRelation"
            parameterType="com.zly.project.carrier.vehicle.domain.CustomerVehicleRelation">
        insert into customer_vehicle_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="vehicleId != null">vehicle_id,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">vehicle_number,</if>
            <if test="vehiclePlateColorCode != null and vehiclePlateColorCode != ''">vehicle_plate_color_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="vehicleId != null">#{vehicleId},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">#{vehicleNumber},</if>
            <if test="vehiclePlateColorCode != null and vehiclePlateColorCode != ''">#{vehiclePlateColorCode},</if>
        </trim>
    </insert>
    <insert id="insertCustomerVehicleRelations">
        insert IGNORE into customer_vehicle_relation
        (customer_id,
        vehicle_id,
        create_by,
        create_time,
        update_by,
        update_time,
        remark,
        vehicle_number,
        vehicle_plate_color_code
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.customerId},
            #{item.vehicleId},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime},
            #{item.remark},
            #{item.vehicleNumber},
            #{item.vehiclePlateColorCode}
        </foreach>
    </insert>

    <update id="updateCustomerVehicleRelation"
            parameterType="com.zly.project.carrier.vehicle.domain.CustomerVehicleRelation">
        update customer_vehicle_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">vehicle_number = #{vehicleNumber},</if>
            <if test="vehiclePlateColorCode != null and vehiclePlateColorCode != ''">vehicle_plate_color_code =
                #{vehiclePlateColorCode},
            </if>
        </trim>
        where customer_id = #{customerId} and vehicle_id = #{vehicleId}
    </update>
    <update id="updateVehicleByShipperIdAndVehicleID">
        update customer_vehicle_relation set vehicle_id = #{newId}
        where customer_id = #{customerId} and vehicle_id = #{oldId}
    </update>

    <delete id="deleteCustomerVehicleRelationByCustomerId" parameterType="Long">
        DELETE
        FROM customer_vehicle_relation
        WHERE customer_id = #{customerId}
          AND vehicle_id = #{vehicleId}
    </delete>

    <delete id="deleteCustomerVehicleRelationByCustomerIds" parameterType="String">
        delete from customer_vehicle_relation where customer_id in
        <foreach item="customerId" collection="array" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </delete>

    <select id="selectCustomerVehicleRelationListByIds" resultMap="CustomerVehicleRelationResult">
        <include refid="selectCustomerVehicleRelationVo"/>
        where vehicle_id in
        <foreach item="vehicle_id" collection="ids" open="(" separator="," close=")">
            #{vehicle_id}
        </foreach>
        <if test="customerId != null  and customerId != ''">and customer_id = #{customerId}</if>

    </select>
    <select id="selectListLimit" resultMap="CustomerVehicleRelationResult">
        <include refid="selectCustomerVehicleRelationVo"/>
        limit #{offset},#{pageSize}
    </select>
    <select id="countAll" resultType="java.lang.Integer">
        select count(*)
        from customer_vehicle_relation;
    </select>
    <select id="selectByCustomerIdsAndVehicleIds" resultMap="CustomerVehicleRelationResult">
        <include refid="selectCustomerVehicleRelationVo"/>
        where (customer_id,vehicle_id) in
        <foreach item="relation" collection="relations" open="(" separator="," close=")">
            (#{relation.customerId},#{relation.vehicleId})
        </foreach>
    </select>

    <delete id="deleteCustomerVehicleRelationByVehicleIds">
        delete from customer_vehicle_relation where vehicle_id in
        <foreach item="vehicleId" collection="vehicleIds" open="(" separator="," close=")">
            #{vehicleId}
        </foreach>
    </delete>
    <delete id="deleteRepeatVehicleByNumberAndId">
        DELETE
        from customer_vehicle_relation
        where customer_id in (select a.customer_id
                              from (SELECT customer_id
                                    from customer_vehicle_relation
                                    where vehicle_number = #{vehicleNumber}
                                    GROUP BY customer_id
                                    HAVING count(*) > 1) a)
          and vehicle_id = #{vehicleId}
    </delete>

    <update id="updateNewVehicleIdByOldVehicleId">
        update customer_vehicle_relation
        set vehicle_id = #{newVehicleId}
        where vehicle_id = #{oldVehicleId}
    </update>
</mapper>
