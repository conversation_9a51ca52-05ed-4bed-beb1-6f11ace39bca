<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.carrier.auxiliary.mapper.AuxiliaryDriverRelationMapper">

    <resultMap type="AuxiliaryDriverRelation" id="AuxiliaryDriverRelationResult">
        <result property="auxiliaryStaffId"    column="auxiliary_staff_id"    />
        <result property="auxiliaryStaffIdentityCard"    column="auxiliary_staff_identity_card"    />
        <result property="auxiliaryStaffContactPhone"    column="auxiliary_staff_contact_phone"    />
        <result property="driverId"    column="driver_id"    />
        <result property="drivingLicense"    column="driving_license"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="signingState"    column="signing_state"    />
        <result property="state"    column="state"    />
    </resultMap>

    <sql id="selectAuxiliaryDriverRelationVo">
        select auxiliary_staff_id, auxiliary_staff_identity_card, auxiliary_staff_contact_phone, driver_id, driving_license, create_by, create_time, update_by, update_time, remark, signing_state, state from auxiliary_driver_relation
    </sql>

    <select id="selectAuxiliaryDriverRelationList" parameterType="AuxiliaryDriverRelation" resultMap="AuxiliaryDriverRelationResult">
        <include refid="selectAuxiliaryDriverRelationVo"/>
        <where>
            <if test="auxiliaryStaffIdentityCard != null  and auxiliaryStaffIdentityCard != ''"> and auxiliary_staff_identity_card = #{auxiliaryStaffIdentityCard}</if>
            <if test="auxiliaryStaffContactPhone != null  and auxiliaryStaffContactPhone != ''"> and auxiliary_staff_contact_phone = #{auxiliaryStaffContactPhone}</if>
            <if test="drivingLicense != null  and drivingLicense != ''">and driving_license = #{drivingLicense}</if>
            <if test="signingState != null ">and signing_state = #{signingState}</if>
            <if test="state != null ">and state = #{state}</if>
            <if test="driverId != null ">and driver_id = #{driverId}</if>
            <if test="auxiliaryStaffId != null ">and auxiliary_staff_id = #{auxiliaryStaffId}</if>
        </where>
    </select>

    <select id="selectByAuxiliaryStaffId" parameterType="Long"
            resultMap="AuxiliaryDriverRelationResult">
        <include refid="selectAuxiliaryDriverRelationVo"/>
        where state = 0 and auxiliary_staff_id = #{auxiliaryStaffId}
    </select>

    <select id="selectByAuxiliaryIdAndDriverId" resultMap="AuxiliaryDriverRelationResult">
        <include refid="selectAuxiliaryDriverRelationVo"/>
        where state = 1
        <if test="auxiliaryStaffId != null">and auxiliary_staff_id = #{auxiliaryStaffId}</if>
        <if test="driverId != null">and driver_id = #{driverId}</if>
        <if test="signingStates != null and signingStates.size > 0">
            and signing_state in
            <foreach item="signingState" collection="signingStates" open="(" separator="," close=")">
                #{signingState}
            </foreach>
        </if>
        limit 1
    </select>

    <select id="selectByAuxiliaryStaffIdAndNotSelf" parameterType="Long"
            resultMap="AuxiliaryDriverRelationResult">
        <include refid="selectAuxiliaryDriverRelationVo"/>
        where state = 1 and auxiliary_staff_id = #{auxiliaryStaffId}
        and auxiliary_staff_identity_card <![CDATA[<>]]> driving_license and signing_state != 3
    </select>

    <insert id="insertAuxiliaryDriverRelation" parameterType="AuxiliaryDriverRelation">
        insert into auxiliary_driver_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="auxiliaryStaffId != null">auxiliary_staff_id,</if>
            <if test="auxiliaryStaffIdentityCard != null and auxiliaryStaffIdentityCard != ''">
                auxiliary_staff_identity_card,
            </if>
            <if test="auxiliaryStaffContactPhone != null and auxiliaryStaffContactPhone != ''">
                auxiliary_staff_contact_phone,
            </if>
            <if test="driverId != null">driver_id,</if>
            <if test="drivingLicense != null and drivingLicense != ''">driving_license,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="signingState != null">signing_state,</if>
            <if test="state != null">state,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="auxiliaryStaffId != null">#{auxiliaryStaffId},</if>
            <if test="auxiliaryStaffIdentityCard != null and auxiliaryStaffIdentityCard != ''">#{auxiliaryStaffIdentityCard},</if>
            <if test="auxiliaryStaffContactPhone != null and auxiliaryStaffContactPhone != ''">#{auxiliaryStaffContactPhone},</if>
            <if test="driverId != null">#{driverId},</if>
            <if test="drivingLicense != null and drivingLicense != ''">#{drivingLicense},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="signingState != null">#{signingState},</if>
            <if test="state != null">#{state},</if>
         </trim>
    </insert>

    <update id="updateAuxiliaryDriverRelation" parameterType="AuxiliaryDriverRelation">
        update auxiliary_driver_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="auxiliaryStaffIdentityCard != null and auxiliaryStaffIdentityCard != ''">auxiliary_staff_identity_card = #{auxiliaryStaffIdentityCard},</if>
            <if test="auxiliaryStaffContactPhone != null and auxiliaryStaffContactPhone != ''">auxiliary_staff_contact_phone = #{auxiliaryStaffContactPhone},</if>
            <if test="drivingLicense != null and drivingLicense != ''">driving_license = #{drivingLicense},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="signingState != null">signing_state = #{signingState},</if>
            <if test="state != null">state = #{state},</if>
        </trim>
        where auxiliary_staff_id = #{auxiliaryStaffId} and driver_id = #{driverId}
    </update>

    <delete id="deleteAuxiliaryDriverRelationByAuxiliaryStaffId" parameterType="Long">
        delete
        from auxiliary_driver_relation
        where auxiliary_staff_id = #{auxiliaryStaffId}
    </delete>

    <delete id="deleteAuxiliaryDriverRelationByAuxiliaryStaffIds" parameterType="String">
        delete from auxiliary_driver_relation where auxiliary_staff_id in
        <foreach item="auxiliaryStaffId" collection="auxiliaryStaffIds" open="(" separator="," close=")">
            #{auxiliaryStaffId}
        </foreach>
    </delete>
    <delete id="deleteByAuxiliaryStaffIdAndDriverId">
        delete
        from auxiliary_driver_relation
        where auxiliary_staff_id = #{auxiliaryStaffId}
          and driver_id = #{driverId}
    </delete>

    <insert id="insertAuxiliaryDriverRelationList">
        insert ignore into auxiliary_driver_relation(auxiliary_staff_id, auxiliary_staff_identity_card, auxiliary_staff_contact_phone, driver_id, driving_license, create_by, create_time,
                                                     signing_state, state)
        values
        <foreach item="item" index="index" collection="list" open="" close="" separator=",">
            (#{item.auxiliaryStaffId}, #{item.auxiliaryStaffIdentityCard}, #{item.auxiliaryStaffContactPhone}, #{item.driverId}, #{item.drivingLicense}, #{item.createBy}, #{item.createTime},
             #{item.signingState}, #{item.state})
        </foreach>
    </insert>

    <select id="selectAuxiliaryDriverRelationListByIdentityCards"
            resultMap="AuxiliaryDriverRelationResult">
        <include refid="selectAuxiliaryDriverRelationVo"/>
        where auxiliary_staff_identity_card in
        <foreach item="identityCard" collection="identityCards" open="(" separator="," close=")">
            #{identityCard}
        </foreach>
    </select>
</mapper>
