<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.carrier.carrier.mapper.ActualCarrierInfoMapper">

    <resultMap type="com.zly.project.carrier.carrier.domain.ActualCarrierInfo" id="ActualCarrierInfoResult">
        <result property="id" column="id"/>
        <result property="actualCarrierName" column="actual_carrier_name"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="identityCard" column="identity_card"/>
        <result property="identityValidPeriodFrom" column="identity_valid_period_from"/>
        <result property="identityvalidPeriodTo" column="identityvalid_period_to"/>
        <result property="approveState" column="approve_state"/>
        <result property="state" column="state"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="isComplete" column="is_complete"/>
        <result property="infoIsComplete" column="info_is_complete"/>
        <result property="operation" column="operation"/>
        <result property="isTheIdentityCardValid" column="is_the_identity_card_valid"/>
        <result property="driverId" column="driver_id"/>
        <result property="carCaptainId" column="car_captain_id"/>
        <result property="auxiliaryStaffId" column="auxiliary_staff_id"/>
        <result property="expireState" column="expire_state"/>
        <result property="address" column="address"/>
        <result property="accountOpenStateAPP" column="account_open_state_app"/>
        <result property="accountOpenStatePC" column="account_open_state_pc"/>
        <result property="accountOpenStateFamily" column="account_open_state_family"/>
    </resultMap>

    <resultMap type="com.zly.project.carrier.carrier.domain.res.ActualCarrierInfoList" id="ActualCarrierExtendResult"
               extends="ActualCarrierInfoResult">
        <result property="lastServeTime" column="last_serve_time"/>
        <result property="serveTimes" column="serve_times"/>
        <result property="totalMiles" column="total_miles"/>
    </resultMap>

    <sql id="selectActualCarrierInfoVo">
        select id,
               actual_carrier_name,
               contact_phone,
               identity_card,
               identity_valid_period_from,
               identityvalid_period_to,
               approve_state,
               state,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_complete,
               info_is_complete,
               operation,
               is_the_identity_card_valid,
               driver_id,
               car_captain_id,
               auxiliary_staff_id,
               expire_state,
               address,
               account_open_state_app,
               account_open_state_pc,
               account_open_state_family
        from actual_carrier_info
    </sql>

    <sql id="selectActualCarrierInfoVoOld">
        select id,
               actual_carrier_name,
               contact_phone,
               identity_card,
               identity_valid_period_from,
               identityvalid_period_to,
               approve_state,
               state,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               is_complete,
               info_is_complete,
               operation,
               is_the_identity_card_valid,
               account_open_state_app,
               account_open_state_pc,
               account_open_state_family
        from actual_carrier_info
    </sql>

    <select id="selectActualCarrierInfoList" parameterType="com.zly.project.carrier.carrier.domain.ActualCarrierInfo"
            resultMap="ActualCarrierInfoResult">
        <include refid="selectActualCarrierInfoVo"/>
        <where>
            <if test="actualCarrierName != null  and actualCarrierName != ''">and actual_carrier_name like concat('%',
                #{actualCarrierName}, '%')
            </if>
            <if test="contactPhone != null  and contactPhone != ''">and contact_phone = #{contactPhone}</if>
            <if test="identityCard != null  and identityCard != ''">and identity_card = #{identityCard}</if>
            <if test="identityValidPeriodFrom != null ">and identity_valid_period_from = #{identityValidPeriodFrom}</if>
            <if test="identityvalidPeriodTo != null ">and identityvalid_period_to = #{identityvalidPeriodTo}</if>
            <if test="approveState != null ">and approve_state = #{approveState}</if>
            <if test="state != null ">and state = #{state}</if>
            <if test="isComplete != null ">and is_complete = #{isComplete}</if>
            <if test="infoIsComplete != null ">and info_is_complete = #{infoIsComplete}</if>
            <if test="operation != null ">and operation = #{operation}</if>
            <if test="isTheIdentityCardValid != null ">and is_the_identity_card_valid = #{isTheIdentityCardValid}</if>
            <if test="driverId != null ">and driver_id = #{driverId}</if>
            <if test="carCaptainId != null ">and car_captain_id = #{carCaptainId}</if>
            <if test="auxiliaryStaffId != null ">and auxiliary_staff_id = #{auxiliaryStaffId}</if>
            <if test="isTheQualificationValid != null ">and is_the_qualification_valid = #{isTheQualificationValid}</if>
            <if test="expireState != null and expireState != ''">and expire_state = #{expireState}</if>
            <if test="address != null and address != ''">and address = #{address}</if>
            <if test="accountOpenStateAPP != null ">and account_open_state_app = #{accountOpenStateAPP}</if>
            <if test="accountOpenStatePC != null ">and account_open_state_pc = #{accountOpenStatePC}</if>
            <if test="accountOpenStateFamily != null ">and account_open_state_family = #{accountOpenStateFamily}</if>
        </where>
        order by update_time desc
    </select>

    <select id="selectActualCarrierInfoById" parameterType="Long" resultMap="ActualCarrierInfoResult">
        <include refid="selectActualCarrierInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectCarrierListHasStatistics" resultMap="ActualCarrierExtendResult">
        SELECT
        c.id,
        c.actual_carrier_name,
        c.actual_carrier_id,
        c.contact_phone,
        c.identity_card,
        c.identityvalid_period_to,
        c.create_time,
        MAX(e.last_serve_time) last_serve_time,
        SUM(e.serve_times) serve_times,
        SUM(e.total_miles) total_miles
        FROM
        actual_carrier_info c
        LEFT JOIN actual_carrier_extend e ON c.id = e.carrier_id
        <where>
            <if test="actualCarrierName != null  and actualCarrierName != ''">and c.actual_carrier_name like concat('%', #{actualCarrierName}, '%')</if>
            <if test="actualCarrierId != null  and actualCarrierId != ''">and c.actual_carrier_id = #{actualCarrierId}</if>
            <if test="contactPhone != null  and contactPhone != ''">and c.contact_phone = #{contactPhone}</if>
            <if test="identityCard != null  and identityCard != ''">and c.identity_card = #{identityCard}</if>
            <if test="state != null ">and c.state = #{state}</if>
            <if test="isComplete != null ">and is_complete = #{isComplete}</if>
            <if test="infoIsComplete != null ">and info_is_complete = #{infoIsComplete}</if>
        </where>
        GROUP BY
        c.identity_card
    </select>

    <select id="selectCarrierListHasStatisticss" resultMap="ActualCarrierExtendResult">
        select
        aci.id,
        aci.actual_carrier_name,
        aci.actual_carrier_id,
        aci.contact_phone,
        aci.identity_card,
        aci.identityvalid_period_to,
        ffcr.create_time,
        aci.info_is_complete,
        ffcr.last_order_time last_serve_time,
        ffcr.serve_times,
        ffcr.total_miles
        from freight_forwarder_actual_relation ffcr
        join actual_carrier_info aci on ffcr.actual_id = aci.id
        <where>
            <if test="freightForwarderId != null ">and ffcr.freight_forwarder_id = #{freightForwarderId}</if>
            <if test="state != null ">and aci.state = #{state}</if>
            <if test="actualCarrierName != null  and actualCarrierName != ''">and aci.actual_carrier_name like
                concat('%', #{actualCarrierName}, '%')
            </if>
            <if test="actualCarrierId != null  and actualCarrierId != ''">and aci.actual_carrier_id =
                #{actualCarrierId}
            </if>
            <if test="contactPhone != null  and contactPhone != ''">and aci.contact_phone = #{contactPhone}</if>
            <if test="identityCard != null  and identityCard != ''">and aci.identity_card = #{identityCard}</if>
            <if test="isComplete != null ">and is_complete = #{isComplete}</if>
            <if test="infoIsComplete != null ">and info_is_complete = #{infoIsComplete}</if>
        </where>
        order by ffcr.last_order_time desc limit #{offSet},#{pageSize}
    </select>
    <select id="selectActualCarrierInfoByIdCards" resultMap="ActualCarrierInfoResult">
        <include refid="selectActualCarrierInfoVo"/>
        where state = 0 and identity_card in
        <foreach item="idCard" collection="idCards" open="(" separator="," close=")">
            #{idCard}
        </foreach>
    </select>
    <select id="selectByIds" resultMap="ActualCarrierInfoResult">
        <include refid="selectActualCarrierInfoVo"/>
        where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="countAll" resultType="java.lang.Integer">
        select count(*)
        from actual_carrier_info
    </select>
    <select id="selectCarrierByCarrierPhones"
            resultType="com.zly.project.carrier.carrier.domain.ActualCarrierInfo">
        select DISTINCT aci.contact_phone contactPhone, identity_card identityCard
        FROM actual_carrier_info aci
        left join customer_actual_relation car on car.actual_id = aci.id
        where aci.contact_phone in
        <foreach item="telephone" collection="carrierPhones" open="(" separator="," close=")">
            #{telephone}
        </foreach>
        and car.customer_id = #{customerId}
        and aci.state = 0
    </select>

    <insert id="insertActualCarrierInfo" parameterType="com.zly.project.carrier.carrier.domain.ActualCarrierInfo">
        insert into actual_carrier_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="actualCarrierName != null and actualCarrierName != ''">actual_carrier_name,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="identityCard != null and identityCard != ''">identity_card,</if>
            <if test="identityValidPeriodFrom != null">identity_valid_period_from,</if>
            <if test="identityvalidPeriodTo != null">identityvalid_period_to,</if>
            <if test="approveState != null">approve_state,</if>
            <if test="state != null">state,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="isComplete != null ">is_complete,</if>
            <if test="infoIsComplete != null ">info_is_complete,</if>
            <if test="operation != null ">operation,</if>
            <if test="isTheIdentityCardValid != null ">is_the_identity_card_valid,</if>
            <if test="isTheQualificationValid != null ">is_the_qualification_valid,</if>
            <if test="driverId != null ">driver_id,</if>
            <if test="carCaptainId != null ">car_captain_id,</if>
            <if test="auxiliaryStaffId != null ">auxiliary_staff_id,</if>
            <if test="expireState != null and expireState != ''">expire_state,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="accountOpenStateAPP != null">account_open_state_app,</if>
            <if test="accountOpenStatePC != null">account_open_state_pc,</if>
            <if test="accountOpenStateFamily != null">account_open_state_family,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="actualCarrierName != null and actualCarrierName != ''">#{actualCarrierName},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="identityCard != null and identityCard != ''">#{identityCard},</if>
            <if test="identityValidPeriodFrom != null">#{identityValidPeriodFrom},</if>
            <if test="identityvalidPeriodTo != null">#{identityvalidPeriodTo},</if>
            <if test="approveState != null">#{approveState},</if>
            <if test="state != null">#{state},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="isComplete != null ">#{isComplete},</if>
            <if test="infoIsComplete != null ">#{infoIsComplete},</if>
            <if test="operation != null ">#{operation},</if>
            <if test="isTheIdentityCardValid != null ">#{isTheIdentityCardValid},</if>
            <if test="isTheQualificationValid != null ">#{isTheQualificationValid},</if>
            <if test="driverId != null ">#{driverId},</if>
            <if test="carCaptainId != null ">#{carCaptainId},</if>
            <if test="auxiliaryStaffId != null ">#{auxiliaryStaffId},</if>
            <if test="expireState != null and expireState != ''">#{expireState},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="accountOpenStateAPP != null">#{accountOpenStateAPP},</if>
            <if test="accountOpenStatePC != null">#{accountOpenStatePC},</if>
            <if test="accountOpenStateFamily != null">#{accountOpenStateFamily},</if>
        </trim>
    </insert>
    <insert id="insertActualCarrierInfos">
        insert ignore into actual_carrier_info
        (id, actual_carrier_name, identity_card, approve_state, state, create_by, create_time ,is_complete, info_is_complete,operation )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.id},
            #{item.actualCarrierName},
            #{item.identityCard},
            #{item.approveState},
            #{item.state},
            #{item.createBy},
            #{item.createTime},
            #{item.isComplete},
            #{item.infoIsComplete},
            #{item.operation}
        </foreach>
    </insert>

    <insert id="insertActualCarrierInfoList">
        insert IGNORE into actual_carrier_info
        (id, actual_carrier_name, identity_card,contact_phone, approve_state, state, create_by, create_time ,is_complete, info_is_complete,operation,auxiliary_staff_id,
        car_captain_id,driver_id
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.id},
            #{item.actualCarrierName},
            #{item.identityCard},
            #{item.contactPhone},
            #{item.approveState},
            #{item.state},
            #{item.createBy},
            #{item.createTime},
            #{item.isComplete},
            #{item.infoIsComplete},
            #{item.operation},
            <if test="item.auxiliaryStaffId != null ">#{item.auxiliaryStaffId},</if>
            <if test="item.auxiliaryStaffId == null ">0,</if>
            <if test="item.carCaptainId != null ">#{item.carCaptainId},</if>
            <if test="item.carCaptainId == null ">0,</if>
            <if test="item.driverId != null ">#{item.driverId}</if>
            <if test="item.driverId == null ">0</if>
        </foreach>
    </insert>

    <update id="updateActualCarrierInfo" parameterType="com.zly.project.carrier.carrier.domain.ActualCarrierInfo">
        update actual_carrier_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="actualCarrierName != null and actualCarrierName != ''">actual_carrier_name = #{actualCarrierName},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="identityCard != null and identityCard != ''">identity_card = #{identityCard},</if>
            <if test="identityValidPeriodFrom != null">identity_valid_period_from = #{identityValidPeriodFrom},</if>
            <if test="identityvalidPeriodTo != null">identityvalid_period_to = #{identityvalidPeriodTo},</if>
            <if test="approveState != null">approve_state = #{approveState},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="isComplete != null ">is_complete = #{isComplete},</if>
            <if test="infoIsComplete != null ">info_is_complete = #{infoIsComplete},</if>
            <if test="operation != null ">operation = #{operation},</if>
            <if test="isTheIdentityCardValid != null ">is_the_identity_card_valid = #{isTheIdentityCardValid},</if>
            <if test="isTheQualificationValid != null ">is_the_qualification_valid = #{isTheQualificationValid},</if>
            <if test="driverId != null ">driver_id = #{driverId},</if>
            <if test="carCaptainId != null ">car_captain_id = #{carCaptainId},</if>
            <if test="auxiliaryStaffId != null ">auxiliary_staff_id = #{auxiliaryStaffId},</if>
            <if test="expireState != null and expireState != ''">expire_state = #{expireState},</if>
            <if test="address != null">address = #{address},</if>
            <if test="accountOpenStateAPP != null">account_open_state_app = #{accountOpenStateAPP},</if>
            <if test="accountOpenStatePC != null">account_open_state_pc = #{accountOpenStatePC},</if>
            <if test="accountOpenStateFamily != null">account_open_state_family = #{accountOpenStateFamily},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateActualCarrierInfoEx" parameterType="com.zly.project.carrier.carrier.domain.ActualCarrierInfo">
        update actual_carrier_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="actualCarrierName != null and actualCarrierName != ''">actual_carrier_name = #{actualCarrierName},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="identityCard != null and identityCard != ''">identity_card = #{identityCard},</if>
            <if test="identityValidPeriodFrom != null">identity_valid_period_from = #{identityValidPeriodFrom},</if>
            <if test="identityvalidPeriodTo != null">identityvalid_period_to = #{identityvalidPeriodTo},</if>
            <if test="identityFrom != null">identity_valid_period_from = default,</if>
            <if test="identityTo != null">identityvalid_period_to = default,</if>
            <if test="approveState != null">approve_state = #{approveState},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="isComplete != null ">is_complete = #{isComplete},</if>
            <if test="infoIsComplete != null ">info_is_complete = #{infoIsComplete},</if>
            <if test="operation != null ">operation = #{operation},</if>
            <if test="isTheIdentityCardValid != null ">is_the_identity_card_valid = #{isTheIdentityCardValid},</if>
            <if test="isTheQualificationValid != null ">is_the_qualification_valid = #{isTheQualificationValid},</if>
            <if test="driverId != null ">driver_id = #{driverId},</if>
            <if test="carCaptainId != null ">car_captain_id = #{carCaptainId},</if>
            <if test="auxiliaryStaffId != null ">auxiliary_staff_id = #{auxiliaryStaffId},</if>
            <if test="expireState != null">expire_state = #{expireState},</if>
            <if test="address != null">address = #{address},</if>
            <if test="accountOpenStateAPP != null">account_open_state_app = #{accountOpenStateAPP},</if>
            <if test="accountOpenStatePC != null">account_open_state_pc = #{accountOpenStatePC},</if>
            <if test="accountOpenStateFamily != null">account_open_state_family = #{accountOpenStateFamily},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteActualCarrierInfoById" parameterType="Long">
        delete
        from actual_carrier_info
        where id = #{id}
    </delete>

    <delete id="deleteActualCarrierInfoByIds" parameterType="String">
        delete from actual_carrier_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCarrierIds" resultType="long">
        SELECT DISTINCT actual_id
        FROM freight_forwarder_actual_relation AS relation
                 LEFT JOIN actual_carrier_info AS info ON info.id = relation.actual_id
        WHERE relation.freight_forwarder_id = #{freightForwarderId}
    </select>

    <select id="selectDriverIdByDriver" resultMap="ActualCarrierInfoResult">
        select *
        from actual_carrier_info
        where driver_id = #{driverId}
    </select>

    <update id="updateDriverId" parameterType="com.zly.project.carrier.carrier.domain.ActualCarrierInfo">
        update actual_carrier_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="driverId != null ">driver_id = #{driverId},</if>
            <if test="date != null "> identityvalid_period_to = #{date},</if>
        </trim>
        where id = #{carrierId}
    </update>
    <update id="updateAccountOpenState">
        update actual_carrier_info set account_open_state = 1
        where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateAccountOpenStateApp">
        update actual_carrier_info set account_open_state_app = 1
        where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateAccountOpenStatePC">
        update actual_carrier_info set account_open_state_app_pc = 1
        where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateAccountOpenStateFamily">
        update actual_carrier_info set account_open_state_app_pc_family = 1
        where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="selectQualifiedCarriersByPCList" resultType="String">
        select distinct aci.identity_card
        from actual_carrier_info aci
        where aci.car_captain_id = 0
          and aci.auxiliary_staff_id = 0
    </select>
    <select id="selectByIdentityCard" resultMap="ActualCarrierInfoResult">
        <include refid="selectActualCarrierInfoVo"/>
        where state = 0 and identity_card = #{identityCard} limit 1
    </select>

    <select id="getAuxiliaryAndVaptainPhoneList" resultType="ActualCarrierInfoExx">
        select DISTINCT a.auxiliary_staff_contact_phone contactPhone, a.auxiliary_staff_identity_card identityCard, b.auxiliary_staff_name actualCarrierName, 3 type
        FROM customer_auxiliary_relation a
        inner join auxiliary_staff_info b on a.auxiliary_staff_id = b.id
        where a.auxiliary_staff_contact_phone in
        <foreach item="telephone" collection="carrierPhones" open="(" separator="," close=")">
            #{telephone}
        </foreach>
        and a.customer_id = #{customerId}

        union
        select DISTINCT a.car_captain_contact_phone contactPhone, a.actual_identity_card identityCard, b.car_captain_name actualCarrierName, 2 type
        FROM customer_captain_relation a
        inner join car_captain_info b on a.car_captain_id = b.id
        where a.car_captain_contact_phone in
        <foreach item="telephone" collection="carrierPhones" open="(" separator="," close=")">
            #{telephone}
        </foreach>
        and a.customer_id = #{customerId}

        union
        select DISTINCT a.telephone contactPhone, a.driving_license identityCard, b.driver_name actualCarrierName,1 type
        FROM customer_driver_relation a
        inner join driver b on a.driver_id = b.id
        where a.telephone in
        <foreach item="telephone" collection="carrierPhones" open="(" separator="," close=")">
            #{telephone}
        </foreach>
        and a.customer_id = #{customerId}
    </select>

    <select id="selectByIdentityCardAndCustomerId" resultMap="ActualCarrierInfoResult">
        select a.id,
               a.actual_carrier_name,
               b.actual_contact_phone as contact_phone,
               a.identity_card,
               a.driver_id,
               a.car_captain_id,
               a.auxiliary_staff_id
        from actual_carrier_info a
                 join customer_actual_relation b on a.id = b.actual_id
        where a.state = 0
          and a.identity_card = #{identityCard}
          and b.customer_id = #{customerId}
    </select>
    <select id="selectByIdsOld" resultMap="ActualCarrierInfoResult">
        <include refid="selectActualCarrierInfoVoOld"/>
        where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectActualCarrierInfoByAuxiliaryIds" resultMap="ActualCarrierInfoResult">
        select * from actual_carrier_info
        where auxiliary_staff_id in
        <foreach item="auxiliaryStaffId" collection="auxiliaryStaffIds" open="(" separator="," close=")">
            #{auxiliaryStaffId}
        </foreach>
    </select>
    <select id="selectActualCarrierInfoByCaptainIds" resultMap="ActualCarrierInfoResult">
        select * from actual_carrier_info
        where car_captain_id in
        <foreach item="carCaptainId" collection="carCaptainIds" open="(" separator="," close=")">
            #{carCaptainId}
        </foreach>
    </select>
    <select id="selectActualCarrierInfoByIdentityCards" resultMap="ActualCarrierInfoResult">
        select * from actual_carrier_info
        where identity_card in
        <foreach item="identityCard" collection="identityCards" open="(" separator="," close=")">
            #{identityCard}
        </foreach>
    </select>
    <select id="selectRepeatCarriersByIdentityCards" resultMap="ActualCarrierInfoResult">
        SELECT * from actual_carrier_info where identity_card in
        (
        select identity_card from actual_carrier_info where state = 0 and identity_card in
        <foreach item="identityCard" collection="identityCards" open="(" separator="," close=")">
            #{identityCard}
        </foreach>
        group by identity_card having count(*) > 1
        )
    </select>
    <select id="selectCarrierInfoBySourceType" resultType="java.lang.Long">
        select
            aci.id
            from actual_carrier_info aci
        left join payee_info pi on pi.identity_card = aci.identity_card
        where aci.state = 0
          and pi.state = 0
          and pi.source_type = 0
    </select>
</mapper>
