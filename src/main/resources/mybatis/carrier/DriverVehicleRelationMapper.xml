<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.carrier.driver.mapper.DriverVehicleRelationMapper">

    <resultMap type="DriverVehicleRelation" id="DriverVehicleRelationResult">
        <result property="driverId" column="driver_id"/>
        <result property="drivingLicense" column="driving_license"/>
        <result property="vehicleId" column="vehicle_id"/>
        <result property="vehicleNumber" column="vehicle_number"/>
        <result property="vehiclePlateColorCode" column="vehicle_plate_color_code"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="signingState" column="signing_state"/>
    </resultMap>

    <sql id="selectDriverVehicleRelationVo">
        select driver_id,
               driving_license,
               vehicle_id,
               vehicle_number,
               vehicle_plate_color_code,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               signing_state
        from driver_vehicle_relation
    </sql>

    <select id="selectDriverVehicleRelationList" parameterType="DriverVehicleRelation" resultMap="DriverVehicleRelationResult">
        <include refid="selectDriverVehicleRelationVo"/>
        <where>
            <if test="drivingLicense != null  and drivingLicense != ''">and driving_license = #{drivingLicense}</if>
            <if test="driverId != null">and driver_id = #{driverId}</if>
            <if test="vehicleId != null ">and vehicle_id = #{vehicleId}</if>
            <if test="vehicleNumber != null  and vehicleNumber != ''">and vehicle_number = #{vehicleNumber}</if>
            <if test="vehiclePlateColorCode != null ">and vehicle_plate_color_code = #{vehiclePlateColorCode}</if>
            <if test="signingState != null ">and signing_state = #{signingState}</if>
        </where>
    </select>


    <insert id="insertDriverVehicleRelation" parameterType="DriverVehicleRelation">
        insert into driver_vehicle_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="driverId != null">driver_id,</if>
            <if test="drivingLicense != null and drivingLicense != ''">driving_license,</if>
            <if test="vehicleId != null">vehicle_id,</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">vehicle_number,</if>
            <if test="vehiclePlateColorCode != null">vehicle_plate_color_code,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="signingState != null">signing_state,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="driverId != null">#{driverId},</if>
            <if test="drivingLicense != null and drivingLicense != ''">#{drivingLicense},</if>
            <if test="vehicleId != null">#{vehicleId},</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">#{vehicleNumber},</if>
            <if test="vehiclePlateColorCode != null">#{vehiclePlateColorCode},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="signingState != null">#{signingState},</if>
        </trim>
    </insert>

    <update id="updateDriverVehicleRelation" parameterType="DriverVehicleRelation">
        update captain_vehicle_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="drivingLicense != null and drivingLicense != ''">driving_license = #{drivingLicense},</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">vehicle_number = #{vehicleNumber},</if>
            <if test="vehiclePlateColorCode != null">vehicle_plate_color_code = #{vehiclePlateColorCode},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="signingState != null">signing_state = #{signingState},</if>
        </trim>
        where driver_id = #{driverId} and vehicle_id = #{vehicleId}
    </update>

    <insert id="insertDriverVehicleRelationList">
        INSERT IGNORE INTO driver_vehicle_relation (
        vehicle_id,
        vehicle_number,
        vehicle_plate_color_code,
        driver_id,
        driving_license,
        create_by,
        create_time,
        signing_state
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.vehicleId},
            #{item.vehicleNumber},
            #{item.vehiclePlateColorCode},
            #{item.driverId},
            #{item.drivingLicense},
            #{item.createBy},
            #{item.createTime},
            #{item.signingState}
            )
        </foreach>
    </insert>

    <select id="selectCustomerDriverVehicleList" resultType="com.zly.project.carrier.vehicle.domain.res.VehicleRes">
        select pvp.vehicle_number           as vehicleNumber,
               pvp.vehicle_plate_color_code as vehiclePlateColorCode,
               v.owner                      as owner,
               pvp.file_url                 as fileUrl,
               ifnull(dvr.signing_state, 2) as signingState,
               pvp.sign_type                as signType,
               pvp.create_time              as updateTime
        from person_vehicle_protocol pvp
                 left join driver_vehicle_relation dvr on dvr.driving_license = pvp.identity_card
                 join vehicle v on pvp.vehicle_id = v.id
                 join customer_vehicle_relation cvr on v.id = cvr.vehicle_id
                 inner join (select vehicle_id, max(create_time) as maxTime from person_vehicle_protocol where identity_card = #{identityCard} group by vehicle_id) c
                            on c.maxTime = pvp.create_time and
                               c.vehicle_id = pvp.vehicle_id
        where pvp.identity_card = #{identityCard}
          and cvr.customer_id = #{customerId}
          and pvp.state = 0
          and pvp.audit_state in (0, 1)
        group by pvp.id
    </select>
    <select id="selectFreightForwarderIdDriverVehicleList"
            resultType="com.zly.project.carrier.vehicle.domain.res.VehicleRes">
        select pvp.vehicle_number as vehicleNumber,
        pvp.vehicle_plate_color_code as vehiclePlateColorCode,
        pvp.sign_state as signState,
        pvp.create_time as updateTime,
        v.owner as owner,
        pvp.file_url as fileUrl,
        pvp.sign_type as signType,
        pvp.file_type as fileType
        from person_vehicle_protocol pvp
        join vehicle v on pvp.vehicle_id = v.id
        join freight_forwarder_driver_relation ffdr on ffdr.driving_license = pvp.identity_card
        inner join (select vehicle_id , max(create_time) as maxTime from person_vehicle_protocol where identity_card = #{identityCard} group by vehicle_id ) c on c.maxTime = pvp.create_time and
        c.vehicle_id = pvp.vehicle_id
        where pvp.identity_card = #{identityCard}
        and ffdr.freight_forwarder_id = #{freightForwarderId}
        and pvp.state = 0
        and pvp.audit_state in (0, 1)
        <if test="signType != null">and (pvp.sign_type = #{signType} or pvp.sign_type = 0)</if>
        group by pvp.id
    </select>
    <select id="selectDriverVehicleList" resultType="com.zly.project.carrier.vehicle.domain.res.VehicleRes">
        select pvp.vehicle_number as vehicleNumber,
        pvp.vehicle_plate_color_code as vehiclePlateColorCode,
        v.owner as owner,
        pvp.file_url as fileUrl,
        dvr.signing_state as signingState,
        dvr.update_time as updateTime,
        pvp.sign_type as signType,
        pvp.sign_state as signState
        from person_vehicle_protocol pvp
        left join driver_vehicle_relation dvr on dvr.driving_license = pvp.identity_card
        join vehicle v on pvp.vehicle_id = v.id
        inner join (select vehicle_id , max(create_time) as maxTime from person_vehicle_protocol where identity_card = #{identityCard} group by vehicle_id ) c on c.maxTime = pvp.create_time and
        c.vehicle_id = pvp.vehicle_id
        where pvp.identity_card = #{identityCard}
        and pvp.state = 0
        and pvp.audit_state in (0, 1)
        <if test="signType != null ">and (pvp.sign_type = #{signType} or pvp.sign_type = 0)</if>
        group by pvp.id
    </select>

    <delete id="deleteDriverVehicleRelation">
        delete
        from driver_vehicle_relation
        where vehicle_id = #{vehicleId}
          and driver_id = #{driverId}
    </delete>
</mapper>
