<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.carrier.payee.mapper.CustomerPayeeRelationMapper">
    <resultMap type="com.zly.project.carrier.payee.domain.CustomerPayeeRelation" id="CustomerPayeeRelationResult">
        <result property="customerId" column="customer_id"/>
        <result property="payeeId" column="payee_id"/>
        <result property="actualIdentityCard" column="actual_identity_card"/>
        <result property="payeeBankCardNo" column="payee_bank_card_no"/>
    </resultMap>

    <sql id="selectCustomerPayeeRelationVo">
        SELECT customer_id, payee_id, actual_identity_card, payee_bank_card_no
        FROM customer_payee_relation
    </sql>
    <update id="updateNewPayeeIdByOldPayeeId">
        update customer_payee_relation
        set payee_id = #{newPayeeId}
        where payee_id = #{oldPayeeId}
    </update>

    <select id="selectCustomerPayeeRelationList"
            parameterType="com.zly.project.carrier.payee.domain.CustomerPayeeRelation"
            resultMap="CustomerPayeeRelationResult">
        <include refid="selectCustomerPayeeRelationVo"/>
        <where>
            <if test="payeeId != null">and payee_id = #{payeeId}</if>
            <if test="customerId != null">and customer_id = #{customerId}</if>
            <if test="actualIdentityCard != null and actualIdentityCard != ''">and actual_identity_card =
                #{actualIdentityCard}
            </if>
            <if test="payeeBankCardNo != null and payeeBankCardNo != ''">and payee_bank_card_no = #{payeeBankCardNo}
            </if>
        </where>
    </select>

    <select id="selectCustomerPayeeRelation" parameterType="Long" resultMap="CustomerPayeeRelationResult">
        <include refid="selectCustomerPayeeRelationVo"/>
        where customer_id = #{customerId} and payee_id = #{payeeId}
    </select>

    <select id="selectCustomerPayeeInfo"
            resultMap="com.zly.project.carrier.payee.mapper.PayeeInfoMapper.PayeeInfoResult">
        select pi.id,
               pi.payee_name,
               cpr.payee_bank_card_no   bank_card_no,
               pi.bank_code,
               pi.bank_name,
               cpr.actual_identity_card identity_card,
               pi.bank_mobile,
               pi.is_other,
               pi.is_default,
               pi.check_result,
               pi.source_type,
               pi.system_type,
               pi.state,
               pi.remark,
               pi.create_by,
               pi.create_time,
               pi.update_by,
               pi.update_time
        from payee_info pi
                 left join customer_payee_relation cpr on payee_id = pi.id
        where cpr.customer_id = #{customerId}
          and pi.id = #{payeeId}
    </select>

    <insert id="insertCustomerPayeeRelation" parameterType="com.zly.project.carrier.payee.domain.CustomerPayeeRelation">
        insert into customer_payee_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">customer_id,</if>
            <if test="payeeId != null">payee_id,</if>
            <if test="createBy">create_by,</if>
            <if test="updateBy">update_by,</if>
            <if test="actualIdentityCard != null and actualIdentityCard != ''">actual_identity_card,</if>
            <if test="payeeBankCardNo != null and payeeBankCardNo != ''">payee_bank_card_no,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">#{customerId},</if>
            <if test="payeeId != null">#{payeeId},</if>
            <if test="createBy">#{createBy},</if>
            <if test="updateBy">#{updateBy},</if>
            <if test="actualIdentityCard != null and actualIdentityCard != ''">#{actualIdentityCard},</if>
            <if test="payeeBankCardNo != null and payeeBankCardNo != ''">#{payeeBankCardNo},</if>
        </trim>
    </insert>
    <insert id="insertCustomerPayeeRelations">
        insert IGNORE into customer_payee_relation
        (customer_id,
        payee_id,
        create_by,
        update_by,
        actual_identity_card,
        payee_bank_card_no
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.customerId},
            #{item.payeeId},
            #{item.createBy},
            #{item.updateBy},
            #{item.actualIdentityCard},
            #{item.payeeBankCardNo}
        </foreach>
    </insert>

    <delete id="deleteCustomerPayeeRelation" parameterType="Long">
        DELETE
        FROM customer_payee_relation
        WHERE customer_id = #{customerId}
          AND payee_id = #{payeeId}
    </delete>

    <select id="selectByPayeeIds" resultMap="CustomerPayeeRelationResult">
        SELECT * FROM customer_payee_relation
        WHERE
        payee_id IN
        <foreach collection="payeeIds" item="payeeId" open="(" close=")" separator=",">
            #{payeeId}
        </foreach>
    </select>
    <select id="selectByCustomerIdsAndPayeeIds" resultMap="CustomerPayeeRelationResult">
        <include refid="selectCustomerPayeeRelationVo"/>
        where (customer_id,payee_id) in
        <foreach item="relation" collection="relations" open="(" separator="," close=")">
            (#{relation.customerId},#{relation.payeeId})
        </foreach>
    </select>

    <delete id="deleteCustomerPayeeRelationByPayeeIds">
        DELETE
        FROM customer_payee_relation
        WHERE payee_id in
        <foreach item="payeeId" collection="payeeIds" open="(" separator="," close=")">
            #{payeeId}
        </foreach>
    </delete>
    <delete id="deleteRepeatPayeeByBankCardNoAndId">
        DELETE
        from customer_payee_relation
        where customer_id in (select a.customer_id
                              from (SELECT customer_id
                                    from customer_payee_relation
                                    where payee_bank_card_no = #{bankCardNo}
                                    GROUP BY customer_id
                                    HAVING count(*) > 1) a)
          and payee_id = #{payeeId}
    </delete>
</mapper>
