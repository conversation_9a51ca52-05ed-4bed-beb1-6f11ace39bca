<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.carrier.carCaptain.mapper.PersonVehicleProtocolMapper">

    <resultMap type="PersonVehicleProtocol" id="PersonVehicleProtocolResult">
        <result property="id" column="id"/>
        <result property="identityCard" column="identity_card"/>
        <result property="vehicleId" column="vehicle_id"/>
        <result property="vehicleNumber" column="vehicle_number"/>
        <result property="fileUrl" column="file_url"/>
        <result property="fileType" column="file_type"/>
        <result property="state" column="state"/>
        <result property="signState" column="sign_state"/>
        <result property="signType" column="sign_type"/>
        <result property="periodValidity" column="period_validity"/>
        <result property="resource" column="resource"/>
        <result property="createCustomerId" column="create_customer_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="protocolCode" column="protocol_code"/>
        <result property="auditState" column="audit_state"/>
        <result property="auditRemark" column="audit_remark"/>
        <result property="auditTime" column="audit_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectPersonVehicleProtocolVo">
        select id,
               identity_card,
               vehicle_id,
               vehicle_number,
               file_url,
               file_type,
               state,
               sign_state,
               sign_type,
               period_validity,
               resource,
               create_customer_id,
               create_by,
               create_time,
               protocol_code,
               audit_state,
               audit_remark,
               audit_time,
               update_time
        from person_vehicle_protocol
    </sql>

    <select id="selectPersonVehicleProtocolList" parameterType="PersonVehicleProtocol" resultMap="PersonVehicleProtocolResult">
        <include refid="selectPersonVehicleProtocolVo"/>
        <where>
            <if test="identityCard != null  and identityCard != ''">and identity_card = #{identityCard}</if>
            <if test="vehicleId != null ">and vehicle_id = #{vehicleId}</if>
            <if test="vehicleNumber != null  and vehicleNumber != ''">and vehicle_number = #{vehicleNumber}</if>
            <if test="fileUrl != null  and fileUrl != ''">and file_url = #{fileUrl}</if>
            <if test="fileType != null ">and file_type = #{fileType}</if>
            <if test="state != null ">and state = #{state}</if>
            <if test="signState != null ">and sign_state = #{signState}</if>
            <if test="signType != null ">and sign_type = #{signType}</if>
            <if test="periodValidity != null ">and period_validity = #{periodValidity}</if>
            <if test="resource != null ">and resource = #{resource}</if>
            <if test="createCustomerId != null ">and create_customer_id = #{createCustomerId}</if>
            <if test="protocolCode != null and protocolCode != '' ">and protocol_code = #{contractCode}</if>
            <if test="auditState != null ">and audit_state = #{auditState}</if>
            <if test="auditRemark != null and auditRemark != '' ">and audit_remark = #{auditRemark}</if>
            <if test="auditTime != null ">and audit_time = #{auditTime}</if>
        </where>
    </select>

    <select id="selectPersonVehicleProtocolById" parameterType="Long" resultMap="PersonVehicleProtocolResult">
        <include refid="selectPersonVehicleProtocolVo"/>
        where id = #{id}
    </select>

    <insert id="insertPersonVehicleProtocol" parameterType="PersonVehicleProtocol">
        insert into person_vehicle_protocol
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="identityCard != null and identityCard != ''">identity_card,</if>
            <if test="vehicleId != null">vehicle_id,</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">vehicle_number,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileType != null and fileType != ''">file_type,</if>
            <if test="state != null">state,</if>
            <if test="signState != null">sign_state,</if>
            <if test="signType != null">sign_type,</if>
            <if test="periodValidity != null">period_validity,</if>
            <if test="resource != null">resource,</if>
            <if test="createCustomerId != null">create_customer_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="protocolCode != null and protocolCode != '' ">protocol_code,</if>
            <if test="auditState != null">audit_state,</if>
            <if test="auditRemark != null and auditRemark != ''">audit_remark,</if>
            <if test="auditTime != null">auditTime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="identityCard != null and identityCard != ''">#{identityCard},</if>
            <if test="vehicleId != null">#{vehicleId},</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">#{vehicleNumber},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileType != null and fileType != ''">#{fileType},</if>
            <if test="state != null">#{state},</if>
            <if test="signState != null">#{signState},</if>
            <if test="signType != null">#{signType},</if>
            <if test="periodValidity != null">#{periodValidity},</if>
            <if test="resource != null">#{resource},</if>
            <if test="createCustomerId != null">#{createCustomerId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="protocolCode != null and protocolCode != '' ">#{protocolCode},</if>
            <if test="auditState != null">#{auditState},</if>
            <if test="auditRemark != null and auditRemark != ''">#{auditRemark},</if>
            <if test="auditTime != null">#{auditTime},</if>
        </trim>
    </insert>

    <update id="updatePersonVehicleProtocol" parameterType="PersonVehicleProtocol">
        update person_vehicle_protocol
        <trim prefix="SET" suffixOverrides=",">
            <if test="identityCard != null and identityCard != ''">identity_card = #{identityCard},</if>
            <if test="vehicleId != null">vehicle_id = #{vehicleId},</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">vehicle_number = #{vehicleNumber},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="fileType != null and fileType != ''">file_type = #{fileType},</if>
            <if test="state != null">state = #{state},</if>
            <if test="signState != null">sign_state = #{signState},</if>
            <if test="signType != null">sign_type = #{signType},</if>
            <if test="periodValidity != null">period_validity = #{periodValidity},</if>
            <if test="resource != null">resource = #{resource},</if>
            <if test="createCustomerId != null">create_customer_id = #{createCustomerId},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="protocolCode != null and protocolCode != '' ">protocol_code = #{protocolCode},</if>
            <if test="auditState != null ">audit_state = #{auditState},</if>
            <if test="auditRemark != null and auditRemark != '' ">audit_remark = #{auditRemark},</if>
            <if test="auditTime != null ">audit_time = #{auditTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePersonVehicleProtocolById" parameterType="Long">
        delete
        from person_vehicle_protocol
        where id = #{id}
    </delete>

    <delete id="deletePersonVehicleProtocolByIds" parameterType="String">
        delete from person_vehicle_protocol where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCarPersonVehicleListByCarIdAndCustomerId" resultType="com.zly.project.carrier.vehicle.domain.res.VehicleRes">
        SELECT p.vehicle_number           AS vehicleNumber,
               p.vehicle_plate_color_code AS vehiclePlateColorCode,
               v.owner                    AS owner,
               p.file_url                 AS fileUrl,
               p.state                    AS state,
               p.sign_type                AS signType,
               p.sign_state               AS signingState,
               v.id                       AS id,
               p.create_time              AS updateTime
        FROM person_vehicle_protocol p
                 JOIN
             vehicle v ON p.vehicle_id = v.id
                 join customer_vehicle_relation c on v.id = c.vehicle_id
        WHERE p.identity_card = #{idCard}
          AND p.state = 0
          and c.customer_id = #{customerId}
        group by p.id
    </select>
    <select id="selectCarPersonVehicleListByCarId"
            resultType="com.zly.project.carrier.vehicle.domain.res.VehicleRes">
        select pvp.vehicle_number as vehicleNumber,
        pvp.vehicle_id as vehicleId,
        pvp.vehicle_plate_color_code as vehiclePlateColorCode,
        v.owner as owner,
        pvp.file_url as fileUrl,
        pvp.state as state,
        v.id as id,
        pvp.create_time as updateTime,
        pvp.sign_type as signType,
        pvp.sign_state as signState
        from person_vehicle_protocol pvp
        join vehicle v on pvp.vehicle_id = v.id
        where pvp.identity_card = #{idCard}
        and pvp.state = 0
        <if test="signType != null ">and (pvp.sign_type = #{signType} or pvp.sign_type = 0)</if>
        group by pvp.id
    </select>
    <select id="selectCarPersonVehicleListByCarIdAndFreightForwardId"
            resultType="com.zly.project.carrier.vehicle.domain.res.VehicleRes">
        select cvp.vehicle_number as vehicleNumber,
        cvp.vehicle_plate_color_code as vehiclePlateColorCode,
        v.owner as owner,
        cvp.file_url as fileUrl,
        cvp.state as state,
        v.id as id,
        cvp.sign_type as signType,
        cvp.create_time as updateTime,
        cvp.sign_state as signState
        from person_vehicle_protocol cvp
        join vehicle v on cvp.vehicle_id = v.id
        join freight_forwarder_vehicle_relation ffvr on ffvr.vehicle_id = v.id
        where ffvr.freight_forwarder_id = #{freightForwarderId}
        and cvp.identity_card = #{idCard}
        and cvp.state = 0
        <if test="signType != null">and (cvp.sign_type = #{signType} or cvp.sign_type = 0)</if>
        group by cvp.id
    </select>
    <select id="selectByList" resultMap="PersonVehicleProtocolResult">
        select id,
        identity_card,
        vehicle_id,
        vehicle_number,
        file_url,
        file_type,
        state,
        sign_state,
        sign_type,
        period_validity,
        resource,
        create_customer_id,
        create_by,
        create_time,
        protocol_code,
        audit_state,
        audit_remark,
        audit_time,
        update_time
        from person_vehicle_protocol
        where (identity_card,vehicle_number) in
        <foreach item="protocol" collection="list" open="(" separator="," close=")">
            (#{protocol.identityCard},#{protocol.vehicleNumber})
        </foreach>
    </select>

    <update id="logicDeletePersonVehicleProtocol">
        update person_vehicle_protocol
        set state = -1
        where vehicle_id = #{vehicleId}
          and identity_card = #{identityCard}
          and resource = #{resource}
          and sign_type = #{signType}
    </update>
    <update id="updatePersonVehicleProtocolState">
        update person_vehicle_protocol
        set state = -1
        where id = #{id}
    </update>

    <insert id="insertPersonVehicleProtocolList">
        insert ignore into person_vehicle_protocol(id, identity_card, vehicle_id, vehicle_number, vehicle_plate_color_code, sign_state, sign_type, period_validity, resource, create_time)
        values
        <foreach collection="insertPersonVehicleProtocolList" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.id}, #{item.identityCard}, #{item.vehicleId}, #{item.vehicleNumber}, #{item.vehiclePlateColorCode}, #{item.signState}, #{item.signType}, #{item.periodValidity},
                #{item.resource}, #{item.createTime}
            </trim>
        </foreach>
    </insert>

    <insert id="insertPersonVehicleProtocolUrlList">
        insert ignore into person_vehicle_protocol(id, identity_card, vehicle_id, vehicle_number, vehicle_plate_color_code, sign_state, sign_type, period_validity, file_url, file_type, resource,
        create_time)
        values
        <foreach collection="personVehicleProtocolUrlList" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.id}, #{item.identityCard}, #{item.vehicleId}, #{item.vehicleNumber}, #{item.vehiclePlateColorCode}, #{item.signState}, #{item.signType}, #{item.periodValidity},
                #{item.fileUrl}, #{item.fileType}, #{item.resource}, #{item.createTime}
            </trim>
        </foreach>
    </insert>

    <select id="vehicleProtocolList" resultType="VehicleProtocolRes">
        select
        distinct a.id as id,
        c.actual_carrier_name as name,
        c.identity_card as identityCard,
        v.vehicle_number as vehicleNumber,
        a.create_time as uploadTime,
        v.owner as owner
        from person_vehicle_protocol a
        inner join waybill b on a.vehicle_id = b.vehicle_id and (a.identity_card = b.driving_license or a.identity_card = car_captain_identity_card) and b.freight_forwarder_id = #{freightForwarderId}
        inner join actual_carrier_info c on a.identity_card = c.identity_card
        inner join vehicle v on a.vehicle_id = v.id
        inner join freight_forwarder_config g on b.freight_forwarder_id = g.freight_forwarder_id
        where a.audit_state = 0 and g.is_audit_file = 0 and a.state = 0 and a.identity_card != ""
        <if test="vehicleNumber != null and vehicleNumber != ''">and a.vehicle_number = #{vehicleNumber}</if>
        <if test="userId != null and userId != ''">and (c.car_captain_id = #{userId} or c.auxiliary_staff_id = #{userId} or c.driver_id = #{userId})</if>
        order by a.create_time
    </select>

    <select id="vehicleProtocolCount" resultType="integer">
        select COUNT(DISTINCT a.id)
        from person_vehicle_protocol a
                 inner join waybill b
                            on a.vehicle_id = b.vehicle_id and
                               (a.identity_card = b.driving_license or a.identity_card = car_captain_identity_card) and
                               b.freight_forwarder_id = #{freightForwarderId}
                 inner join actual_carrier_info c on a.identity_card = c.identity_card
                 inner join vehicle v on a.vehicle_id = v.id
                 inner join freight_forwarder_config g on b.freight_forwarder_id = g.freight_forwarder_id
        where a.audit_state = 0
          and g.is_audit_file = 0
          and a.state = 0
          and a.identity_card != ""
    </select>
    <select id="selectByIdCardAndVehicleNumber" resultMap="PersonVehicleProtocolResult">
        <include refid="selectPersonVehicleProtocolVo"/>
        where state = 0 and identity_card = #{identityCard} and vehicle_number = #{vehicleNumber}
    </select>

    <update id="deleteOldProtocol">
        update person_vehicle_protocol
        set state = -1
        where identity_card = #{identityCard}
          and vehicle_id = #{vehicleId}
    </update>
</mapper>
