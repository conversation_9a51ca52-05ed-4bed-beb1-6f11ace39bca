<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.riskManagement.mapper.FreightForwarderRiskSensitiveWordsMapper">
    
    <resultMap type="FreightForwarderRiskSensitiveWords" id="FreightForarderRiskSensitiveWordsResult">
        <result property="id"    column="id"    />
        <result property="freightForwarderId"    column="freight_forwarder_id"    />
        <result property="sensitiveWord"    column="sensitive_word"    />
        <result property="riskId"    column="risk_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="effectiveTime"    column="effective_time"    />
        <result property="state"    column="state"    />
    </resultMap>

    <sql id="selectFreightForwarderRiskSensitiveWordsVo">
        select id, freight_forwarder_id, sensitive_word, risk_id, create_time, create_by, update_time, update_by, effective_time,state from freight_forwarder_risk_sensitive_words
    </sql>

    <select id="selectFreightForarderRiskSensitiveWordsList" parameterType="FreightForwarderRiskSensitiveWords" resultMap="FreightForarderRiskSensitiveWordsResult">
        <include refid="selectFreightForwarderRiskSensitiveWordsVo"/>
        <where>  
            <if test="freightForwarderId != null "> and freight_forwarder_id = #{freightForwarderId}</if>
            <if test="sensitiveWord != null  and sensitiveWord != ''"> and sensitive_word like concat('%', #{sensitiveWord}, '%')</if>
            <if test="riskId != null "> and risk_id = #{riskId}</if>
            <if test="effectiveTime != null "> and effective_time = #{effectiveTime}</if>
            <if test="state != null "> and state = #{state}</if>
        </where>
    </select>
    
    <select id="selectFreightForarderRiskSensitiveWordsById" parameterType="Long" resultMap="FreightForarderRiskSensitiveWordsResult">
        <include refid="selectFreightForwarderRiskSensitiveWordsVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertFreightForarderRiskSensitiveWords" parameterType="FreightForwarderRiskSensitiveWords">
        insert into freight_forwarder_risk_sensitive_words
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="freightForwarderId != null">freight_forwarder_id,</if>
            <if test="sensitiveWord != null">sensitive_word,</if>
            <if test="riskId != null">risk_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="effectiveTime != null">effective_time,</if>
            <if test="state != null">state,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="freightForwarderId != null">#{freightForwarderId},</if>
            <if test="sensitiveWord != null">#{sensitiveWord},</if>
            <if test="riskId != null">#{riskId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="effectiveTime != null">#{effectiveTime},</if>
            <if test="state != null">#{state},</if>
         </trim>
    </insert>

    <update id="updateFreightForarderRiskSensitiveWords" parameterType="FreightForwarderRiskSensitiveWords">
        update freight_forwarder_risk_sensitive_words
        <trim prefix="SET" suffixOverrides=",">
            <if test="freightForwarderId != null">freight_forwarder_id = #{freightForwarderId},</if>
            <if test="sensitiveWord != null">sensitive_word = #{sensitiveWord},</if>
            <if test="riskId != null">risk_id = #{riskId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="effectiveTime != null">effective_time = #{effectiveTime},</if>
            <if test="state != null">state = #{state},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFreightForarderRiskSensitiveWordsById" parameterType="Long">
        delete from freight_forwarder_risk_sensitive_words where id = #{id}
    </delete>

    <delete id="deleteFreightForarderRiskSensitiveWordsByIds" parameterType="String">
        delete from freight_forwarder_risk_sensitive_words where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertRiskSensitiveWords">
        insert into freight_forwarder_risk_sensitive_words (id, freight_forwarder_id, sensitive_word, risk_id, create_time, create_by, update_time, update_by, effective_time,state)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
            #{item.freightForwarderId},
            #{item.sensitiveWord},
            #{item.riskId},
            #{item.createTime},
            #{item.createBy},
            #{item.updateTime},
            #{item.updateBy},
            #{item.effectiveTime},
            #{item.state})
        </foreach>
    </insert>

    <select id="selectFreightForwarderRiskSensitiveWordsByEffectiveTime"
            resultMap="FreightForarderRiskSensitiveWordsResult">
        <include refid="selectFreightForwarderRiskSensitiveWordsVo"/>
        where DATE(effective_time) = DATE(NOW()) and state in (1,2);
    </select>

    <delete id="deleteFreightForwarderRiskSensitiveWordsByFreightForwarderIds">
        delete from freight_forwarder_risk_sensitive_words where state = 0 and freight_forwarder_id in
        <foreach item="id" collection="payeeFreightForwarderIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateSensitiveWordsStates">
        update freight_forwarder_risk_sensitive_words set state = #{state} where id in
        <foreach item="id" collection="sensitiveWordIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectFreightForwarderRiskSensitiveWordsByRiskAndFreightForwarderId"
            resultMap="FreightForarderRiskSensitiveWordsResult">
        <include refid="selectFreightForwarderRiskSensitiveWordsVo"/>
        where risk_id = #{riskId} and freight_forwarder_id = #{freightForwarderId}
    </select>

    <select id="selectRiskSensitiveWordsByRiskAndSensitiveWords"
            resultMap="FreightForarderRiskSensitiveWordsResult">
        <include refid="selectFreightForwarderRiskSensitiveWordsVo"/>
        where risk_id = #{riskId} and freight_forwarder_id = #{freightForwarderId} and sensitive_word in
        <foreach item="sensitiveWord" collection="sensitiveWords" open="(" separator="," close=")">
            #{sensitiveWord}
        </foreach>
    </select>
</mapper>

