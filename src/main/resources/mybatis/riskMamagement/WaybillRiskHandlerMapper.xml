<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.riskManagement.mapper.WaybillRiskHandlerMapper">

    <resultMap type="WaybillRiskHandler" id="WaybillRiskHandlerResult">
        <result property="id"    column="id"    />
        <result property="actionType"    column="action_type"    />
        <result property="actionContent"    column="action_content"    />
        <result property="waybillId"    column="waybill_id"    />
        <result property="riskIds"    column="risk_ids"    />
        <result property="customerId"    column="customer_id"    />
        <result property="freightForwarderId"    column="freight_forwarder_id"    />
        <result property="frameworkContractName"    column="framework_contract_name"    />
        <result property="ip"    column="ip"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="url"    column="url"    />
        <result property="fileType"    column="file_type"    />
        <result property="remark"    column="remark"    />

    </resultMap>

    <sql id="selectWaybillRiskHandlerRecordsVo">
        select id, action_type, action_content, freight_forwarder_id, ip, create_time, create_by, waybill_id, risk_ids,customer_id,framework_contract_name ,url,file_type,remark from waybill_risk_handler
    </sql>

    <select id="selectWaybillRiskHandlerList" parameterType="WaybillRiskHandler" resultMap="WaybillRiskHandlerResult">
        <include refid="selectWaybillRiskHandlerRecordsVo"/>
        <where>
            <if test="actionType != null ">and action_type = #{actionType}</if>
            <if test="actionContent != null  and actionContent != ''">and action_content = #{actionContent}</if>
            <if test="freightForwarderId != null ">and freight_forwarder_id = #{freightForwarderId}</if>
            <if test="ip != null  and ip != ''">and ip = #{ip}</if>
            <if test="waybillId != null ">and waybill_id = #{waybillId}</if>
            <if test="riskIds != null ">and risk_ids = #{riskIds}</if>
            <if test="customerId != null ">and customer_id = #{customerId}</if>
            <if test="frameworkContractName != null and frameworkContractName !=''">and framework_contract_name = #{frameworkContractName}</if>
            <if test="url != null and url != ''">and url = #{url}</if>
            <if test="fileType != null and fileType != ''">and file_type = #{fileType}</if>
            <if test="remark != null and remark != ''"> and remark = #{remark}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectWaybillRiskHandlerById" parameterType="Long" resultMap="WaybillRiskHandlerResult">
        <include refid="selectWaybillRiskHandlerRecordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertWaybillRiskHandler" parameterType="WaybillRiskHandler">
        insert into waybill_risk_handler
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="actionType != null">action_type,</if>
            <if test="actionContent != null">action_content,</if>
            <if test="freightForwarderId != null">freight_forwarder_id,</if>
            <if test="ip != null">ip,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="waybillId != null">waybill_id,</if>
            <if test="riskIds != null">risk_ids,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="frameworkContractName != null and frameworkContractName !=''">framework_contract_name,</if>
            <if test="url != null and url != ''"> url, </if>
            <if test="fileType != null and fileType != ''"> file_type ,</if>
            <if test="remark != null and remark != ''"> remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="actionType != null">#{actionType},</if>
            <if test="actionContent != null">#{actionContent},</if>
            <if test="freightForwarderId != null">#{freightForwarderId},</if>
            <if test="ip != null">#{ip},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="waybillId != null">#{waybillId},</if>
            <if test="riskIds != null">#{riskIds},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="frameworkContractName != null and frameworkContractName !=''">#{frameworkContractName},</if>
            <if test="url != null and url != ''"> #{url},</if>
            <if test="fileType != null and fileType != ''"> #{fileType},</if>
            <if test="remark != null and remark != ''"> #{remark},</if>
         </trim>
    </insert>

    <update id="updateWaybillRiskHandler" parameterType="WaybillRiskHandler">
        update waybill_risk_handler
        <trim prefix="SET" suffixOverrides=",">
            <if test="actionType != null">action_type = #{actionType},</if>
            <if test="actionContent != null">action_content = #{actionContent},</if>
            <if test="freightForwarderId != null">freight_forwarder_id = #{freightForwarderId},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="waybillId != null">waybill_id = #{waybillId},</if>
            <if test="riskIds != null">risk_ids = #{riskIds},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="frameworkContractName != null and frameworkContractName !=''"> framework_contract_name = #{frameworkContractName},</if>
            <if test="url != null and url != ''"> url = #{url},</if>
            <if test="fileType != null and fileType != ''">  file_type = #{fileType},</if>
            <if test="remark != null and remark != ''"> remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWaybillRiskHandlerById" parameterType="Long">
        delete from waybill_risk_handler where id = #{id}
    </delete>

    <delete id="deleteWaybillRiskHandlerByIds" parameterType="String">
        delete from waybill_risk_handler where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertWaybillRiskHandler">
        insert into waybill_risk_handler(id, action_type, action_content, freight_forwarder_id, ip, create_time, create_by, waybill_id, risk_ids,customer_id,framework_contract_name ,url,file_type,remark) values
        <foreach item="item" index="index" collection="handlers" separator=",">
            (#{item.id},
            #{item.actionType},
            #{item.actionContent},
            #{item.freightForwarderId},
            #{item.ip},
            #{item.createTime},
            #{item.createBy},
            #{item.waybillId},
            #{item.riskIds},
            #{item.customerId},
            #{item.frameworkContractName} ,
            #{item.url},
            #{item.fileType},
            #{item.remark})
        </foreach>
    </insert>

    <select id="selectWaybillRiskHandlerProcessedList"
            resultType="com.zly.project.riskManagement.domain.res.WaybillRiskQueryRes">
        select wrh.waybill_id waybillId,
        w.shipping_note_number shippingNoteNumber,
        wrh.framework_contract_name frameworkContractName,
        wri.risk_result_grade riskGrade,
        wrh.create_time ignoreTime,
        wrh.create_by updateBy
        from waybill_risk_handler wrh
        left join waybill w on w.id = wrh.waybill_id and w.status != -1
        left join waybill_risk_info wri on wri.waybill_id = wrh.waybill_id
        where wrh.freight_forwarder_id = #{query.freightForwarderId}
        <if test="query.shippingNoteNumbers != null and query.shippingNoteNumbers.size !=0">
            and w.shipping_note_number in
            <foreach item="shippingNoteNumber" collection="query.shippingNoteNumbers" open="(" separator="," close=")">
                #{shippingNoteNumber}
            </foreach>
        </if>
        <if test="query.shippingNoteNumber !=null and query.shippingNoteNumber !=''">
            and w.shipping_note_number = #{query.shippingNoteNumber}
        </if>
        GROUP BY wrh.waybill_id ORDER BY wrh.create_time DESC
    </select>
</mapper>


