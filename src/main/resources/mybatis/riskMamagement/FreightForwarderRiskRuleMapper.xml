<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.riskManagement.mapper.FreightForwarderRiskRuleMapper">

    <resultMap type="FreightForwarderRiskRule" id="FreightForwarderRiskRuleResult">
        <result property="freightForwarderId" column="freight_forwarder_id"/>
        <result property="riskId" column="risk_id"/>
        <result property="riskName" column="risk_name"/>
        <result property="riskKey" column="risk_key"/>
        <result property="riskConditions" column="risk_conditions"/>
        <result property="riskDescription" column="risk_description"/>
        <result property="riskPhase" column="risk_phase"/>
        <result property="riskGrade" column="risk_grade"/>
        <result property="parentRiskId" column="parent_risk_id"/>
        <result property="deepNum" column="deep_num"/>
        <result property="orderNum" column="order_num"/>
        <result property="riskPriority" column="risk_priority"/>
        <result property="needConfigure" column="need_configure"/>
        <result property="needVerification" column="need_verification"/>
        <result property="status" column="status"/>
        <result property="visible" column="visible"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="riskValue" column="risk_value"/>
        <result property="riskPhases" column="risk_phases"/>
    </resultMap>

    <sql id="selectFreightForwarderRiskRuleVo">
        select freight_forwarder_id,
               risk_id,
               risk_name,
               risk_key,
               risk_conditions,
               risk_description,
               risk_phase,
               risk_grade,
               parent_risk_id,
               deep_num,
               order_num,
               risk_priority,
               need_configure,
               need_verification,
               status,
               visible,
               remark,
               create_by,
               create_time,
               update_by,
               update_time,risk_value,risk_phases
        from freight_forwarder_risk_rule
    </sql>

    <select id="selectFreightForwarderRiskRuleList" parameterType="FreightForwarderRiskRule"
            resultMap="FreightForwarderRiskRuleResult">
        <include refid="selectFreightForwarderRiskRuleVo"/>
        <where>
        	<if test="freightForwarderId != null">and freight_forwarder_id = #{freightForwarderId}</if>
            <if test="riskName != null  and riskName != ''">and risk_name like concat('%', #{riskName}, '%')</if>
            <if test="riskKey != null  and riskKey != ''">and risk_key = #{riskKey}</if>
            <if test="riskConditions != null  and riskConditions != ''">and risk_conditions = #{riskConditions}</if>
            <if test="riskDescription != null  and riskDescription != ''">and risk_description = #{riskDescription}</if>
            <if test="riskPhase != null ">and risk_phase = #{riskPhase}</if>
            <if test="riskGrade != null ">and risk_grade = #{riskGrade}</if>
            <if test="parentRiskId != null ">and parent_risk_id = #{parentRiskId}</if>
            <if test="deepNum != null ">and deep_num = #{deepNum}</if>
            <if test="orderNum != null ">and order_num = #{orderNum}</if>
            <if test="riskPriority != null ">and risk_priority = #{riskPriority}</if>
            <if test="needConfigure != null ">and need_configure = #{needConfigure}</if>
            <if test="needVerification != null ">and need_verification = #{needVerification}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="visible != null ">and visible = #{visible}</if>
            <if test="riskValue != null ">and risk_value = #{riskValue}</if>
            <if test="riskPhases != null ">and risk_phases = #{riskPhases}</if>
            <if test="riskId != null ">and risk_id = #{riskId}</if>
        </where>
    </select>

    <select id="selectByFreightForwarderIdOrderByDeepDesc" parameterType="Long"
            resultMap="FreightForwarderRiskRuleResult">
        <include refid="selectFreightForwarderRiskRuleVo"/>
        where freight_forwarder_id = #{freightForwarderId}
        and status = 1 and visible = 1
        order by deep_num desc
    </select>

    <insert id="insertFreightForwarderRiskRule" parameterType="FreightForwarderRiskRule">
        insert into freight_forwarder_risk_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="freightForwarderId != null">freight_forwarder_id,</if>
            <if test="riskId != null">risk_id,</if>
            <if test="riskName != null and riskName != ''">risk_name,</if>
            <if test="riskKey != null and riskKey != ''">risk_key,</if>
            <if test="riskConditions != null and riskConditions != ''">risk_conditions,</if>
            <if test="riskDescription != null and riskDescription != ''">risk_description,</if>
            <if test="riskPhase != null">risk_phase,</if>
            <if test="riskGrade != null">risk_grade,</if>
            <if test="parentRiskId != null">parent_risk_id,</if>
            <if test="deepNum != null">deep_num,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="riskPriority != null">risk_priority,</if>
            <if test="needConfigure != null">need_configure,</if>
            <if test="needVerification != null">need_verification,</if>
            <if test="status != null">status,</if>
            <if test="visible != null">visible,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="riskValue != null">risk_value,</if>
            <if test="riskPhases != null">risk_phases,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="freightForwarderId != null">#{freightForwarderId},</if>
            <if test="riskId != null">#{riskId},</if>
            <if test="riskName != null and riskName != ''">#{riskName},</if>
            <if test="riskKey != null and riskKey != ''">#{riskKey},</if>
            <if test="riskConditions != null and riskConditions != ''">#{riskConditions},</if>
            <if test="riskDescription != null and riskDescription != ''">#{riskDescription},</if>
            <if test="riskPhase != null">#{riskPhase},</if>
            <if test="riskGrade != null">#{riskGrade},</if>
            <if test="parentRiskId != null">#{parentRiskId},</if>
            <if test="deepNum != null">#{deepNum},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="riskPriority != null">#{riskPriority},</if>
            <if test="needConfigure != null">#{needConfigure},</if>
            <if test="needVerification != null">#{needVerification},</if>
            <if test="status != null">#{status},</if>
            <if test="visible != null">#{visible},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="riskValue != null">#{riskValue},</if>
            <if test="riskPhases != null">#{riskPhases},</if>
        </trim>
    </insert>

    <update id="updateFreightForwarderRiskRule" parameterType="FreightForwarderRiskRule">
        update freight_forwarder_risk_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="riskId != null">risk_id = #{riskId},</if>
            <if test="riskName != null and riskName != ''">risk_name = #{riskName},</if>
            <if test="riskKey != null and riskKey != ''">risk_key = #{riskKey},</if>
            <if test="riskConditions != null and riskConditions != ''">risk_conditions = #{riskConditions},</if>
            <if test="riskDescription != null and riskDescription != ''">risk_description = #{riskDescription},</if>
            <if test="riskPhase != null">risk_phase = #{riskPhase},</if>
            <if test="riskGrade != null">risk_grade = #{riskGrade},</if>
            <if test="parentRiskId != null">parent_risk_id = #{parentRiskId},</if>
            <if test="deepNum != null">deep_num = #{deepNum},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="riskPriority != null">risk_priority = #{riskPriority},</if>
            <if test="needConfigure != null">need_configure = #{needConfigure},</if>
            <if test="needVerification != null">need_verification = #{needVerification},</if>
            <if test="status != null">status = #{status},</if>
            <if test="visible != null">visible = #{visible},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="riskValue != null">risk_value = #{riskValue},</if>
            <if test="riskPhases != null">risk_phases = #{riskPhases},</if>
        </trim>
        where freight_forwarder_id = #{freightForwarderId}
    </update>

    <delete id="deleteFreightForwarderRiskRuleByFreightForwarderId" parameterType="Long">
        delete
        from freight_forwarder_risk_rule
        where freight_forwarder_id = #{freightForwarderId}
    </delete>

    <delete id="deleteFreightForwarderRiskRuleByFreightForwarderIds" parameterType="String">
        delete from freight_forwarder_risk_rule where freight_forwarder_id in
        <foreach item="freightForwarderId" collection="array" open="(" separator="," close=")">
            #{freightForwarderId}
        </foreach>
    </delete>

    <update id="updateForwarderRiskRuleByFreightForwarderIdsAndRiskIds">
        update freight_forwarder_risk_rule set status = 0,update_time = now()
        where  freight_forwarder_id in
        <foreach item="freightForwarderId" collection="freightForwarderIds" open="(" separator="," close=")">
            #{freightForwarderId}
        </foreach>
        and risk_id in
        <foreach item="riskId" collection="riskIds" open="(" separator="," close=")">
            #{riskId}
        </foreach>
    </update>

    <insert id="batchInsertFreightRiskRules">
        insert into freight_forwarder_risk_rule (
        freight_forwarder_id,
        risk_id,
        risk_name,
        risk_key,
        risk_conditions,
        risk_description,
        risk_phase,
        risk_grade,
        parent_risk_id,
        deep_num,
        order_num,
        risk_priority,
        need_configure,
        need_verification,
        status,
        visible,
        remark,
        create_by,
        create_time,
        update_by,
        update_time,risk_value,risk_phases) values
        <foreach item="item" index="index" collection="list" open="" close="" separator=",">
            (#{item.freightForwarderId},
            #{item.riskId},
            #{item.riskName},
            #{item.riskKey},
            #{item.riskConditions},
            #{item.riskDescription},
            #{item.riskPhase},
            #{item.riskGrade},
            #{item.parentRiskId},
            #{item.deepNum},
            #{item.orderNum},
            #{item.riskPriority},
            #{item.needConfigure},
            #{item.needVerification},
            #{item.status},
            #{item.visible},
            #{item.remark},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime},
            #{item.riskValue},
            #{item.riskPhases})
        </foreach>
    </insert>

    <select id="selectFreightForwarderRiskRuleListByRiskIdsAndFreightForwarderId"
            resultMap="FreightForwarderRiskRuleResult">
        <include refid="selectFreightForwarderRiskRuleVo"/>
        where freight_forwarder_id = #{freightForwarderId}
        and risk_id in
        <foreach item="riskId" collection="riskIds" open="(" separator="," close=")">
            #{riskId}
        </foreach>
    </select>
    <select id="selectByfreightForwarderIds"
            resultType="com.zly.project.riskManagement.domain.res.FreightForwarderRiskRuleRes">
        select
        risk_id AS riskId,
        risk_name AS riskName
        from freight_forwarder_risk_rule
        where status = 1 and deep_num = 3 and visible = 1 and freight_forwarder_id in
        <foreach item="freightForwarderId" collection="freightForwarderIds" open="(" separator="," close=")">
            #{freightForwarderId}
        </foreach>
        GROUP BY risk_id
    </select>


    <delete id="deleteForwarderRiskRuleByFreightForwarderIdsAndRiskIds">
        delete from  freight_forwarder_risk_rule
        where  freight_forwarder_id in
        <foreach item="freightForwarderId" collection="freightForwarderIds" open="(" separator="," close=")">
            #{freightForwarderId}
        </foreach>
        and risk_id in
        <foreach item="riskId" collection="riskIds" open="(" separator="," close=")">
            #{riskId}
        </foreach>
    </delete>

    <delete id="deleteForwarderRiskRuleByRiskId">
        delete from freight_forwarder_risk_rule where risk_id = #{riskId}
    </delete>
</mapper>

