<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.settlement.mapper.CustomerAccountMapper">

    <resultMap type="CustomerAccount" id="CustomerAccountResult">
        <result property="id" column="id"/>
        <result property="idNumber" column="id_number"/>
        <result property="customerName" column="customer_name"/>
        <result property="password" column="password"/>
        <result property="accountNo" column="account_no"/>
        <result property="bankCode" column="bank_code"/>
        <result property="bankName" column="bank_name"/>
        <result property="money" column="money"/>
        <result property="contractMoney" column="contract_money"/>
        <result property="sumMoney" column="sum_money"/>
        <result property="remark" column="remark"/>
        <result property="state" column="state"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCustomerAccountVo">
        select id,
               id_number,
               customer_name,
               password,
               account_no,
               bank_code,
               bank_name,
               money,
               contract_money,
               sum_money,
               remark,
               state,
               create_by,
               create_time,
               update_by,
               update_time
        from customer_account
    </sql>

    <select id="selectCustomerAccountList" parameterType="CustomerAccount" resultMap="CustomerAccountResult">
        <include refid="selectCustomerAccountVo"/>
        <where>
            <if test="idNumber != null  and idNumber != ''">and id_number = #{idNumber}</if>
            <if test="customerName != null  and customerName != ''">and customer_name like concat('%', #{customerName},
                '%')
            </if>
            <if test="password != null  and password != ''">and password = #{password}</if>
            <if test="accountNo != null  and accountNo != ''">and account_no = #{accountNo}</if>
            <if test="bankCode != null  and bankCode != ''">and bank_code = #{bankCode}</if>
            <if test="bankName != null  and bankName != ''">and bank_name like concat('%', #{bankName}, '%')</if>
            <if test="money != null ">and money = #{money}</if>
            <if test="contractMoney != null ">and contract_money = #{contractMoney}</if>
            <if test="sumMoney != null ">and sum_money = #{sumMoney}</if>
            <if test="state != null ">and state = #{state}</if>
        </where>
    </select>

    <select id="selectCustomerAccountListByCreditCode" parameterType="String" resultMap="CustomerAccountResult">
        <include refid="selectCustomerAccountVo"/>
        <where>
            state != -1
            <if test="customerId != null ">and id_number = #{CreditCode}</if>
        </where>
    </select>

    <select id="selectCustomerAccountById" parameterType="Long" resultMap="CustomerAccountResult">
        <include refid="selectCustomerAccountVo"/>
        where id = #{id}
    </select>
    <select id="selectCustomerAccountByIds" resultMap="CustomerAccountResult">
        <include refid="selectCustomerAccountVo"/>
        where id in
        <foreach item="id" collection="customerIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectCustomerAccountByIdCards" resultMap="CustomerAccountResult">
        <include refid="selectCustomerAccountVo"/>
        where id_number in
        <foreach item="idcard" collection="idcards" open="(" separator="," close=")">
            #{idcard}
        </foreach>
    </select>

    <insert id="insertCustomerAccount" parameterType="CustomerAccount">
        insert into customer_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="idNumber != null and idNumber != ''">id_number,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="accountNo != null and accountNo != ''">account_no,</if>
            <if test="bankCode != null and bankCode != ''">bank_code,</if>
            <if test="bankName != null and bankName != ''">bank_name,</if>
            <if test="money != null">money,</if>
            <if test="contractMoney != null">contract_money,</if>
            <if test="sumMoney != null">sum_money,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="state != null">state,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="idNumber != null and idNumber != ''">#{idNumber},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="accountNo != null and accountNo != ''">#{accountNo},</if>
            <if test="bankCode != null and bankCode != ''">#{bankCode},</if>
            <if test="bankName != null and bankName != ''">#{bankName},</if>
            <if test="money != null">#{money},</if>
            <if test="contractMoney != null">#{contractMoney},</if>
            <if test="sumMoney != null">#{sumMoney},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="state != null">#{state},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>
    <insert id="insertCustomerAccounts">
        insert IGNORE into customer_account
        (id,
        id_number,
        customer_name,
        password,
        account_no,
        bank_code,
        bank_name,
        money,
        contract_money,
        sum_money,
        remark,
        state,
        create_by,
        create_time,
        update_by,
        update_time
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.id},
            #{item.idNumber},
            #{item.customerName},
            #{item.password},
            #{item.accountNo},
            #{item.bankCode},
            #{item.bankName},
            #{item.money},
            #{item.contractMoney},
            #{item.sumMoney},
            #{item.remark},
            #{item.state},
            #{item.createBy},
            <if test="item.createTime != null">#{item.createTime},</if>
            <if test="item.createTime == null">default,</if>
            #{item.updateBy},
            <if test="item.updateTime != null">#{item.updateTime}</if>
            <if test="item.updateTime == null">default</if>
        </foreach>
    </insert>

    <update id="updateCustomerAccount" parameterType="CustomerAccount">
        update customer_account
        <trim prefix="SET" suffixOverrides=",">
            <if test="idNumber != null and idNumber != ''">id_number = #{idNumber},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="accountNo != null and accountNo != ''">account_no = #{accountNo},</if>
            <if test="bankCode != null and bankCode != ''">bank_code = #{bankCode},</if>
            <if test="bankName != null and bankName != ''">bank_name = #{bankName},</if>
            <if test="money != null">money = #{money},</if>
            <if test="contractMoney != null">contract_money = #{contractMoney},</if>
            <if test="sumMoney != null">sum_money = #{sumMoney},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerAccountById" parameterType="Long">
        delete
        from customer_account
        where id = #{id}
    </delete>

    <delete id="deleteCustomerAccountByIds" parameterType="String">
        delete from customer_account where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>