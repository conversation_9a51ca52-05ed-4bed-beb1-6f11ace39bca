<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.settlement.mapper.CustomerBankCardMapper">

    <resultMap type="CustomerBankCard" id="CustomerBankCardResult">
        <result property="id" column="id"/>
        <result property="customerId" column="customer_id"/>
        <result property="bankCardNo" column="bank_card_no"/>
        <result property="bankCode" column="bank_code"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankMobile" column="bank_mobile"/>
        <result property="isOther" column="is_other"/>
        <result property="remark" column="remark"/>
        <result property="state" column="state"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCustomerBankCardVo">
        select id,
               customer_id,
               bank_card_no,
               bank_code,
               bank_name,
               bank_mobile,
               is_other,
               remark,
               state,
               create_by,
               create_time,
               update_by,
               update_time
        from customer_bank_card
    </sql>

    <select id="selectCustomerBankCardList" parameterType="CustomerBankCard" resultMap="CustomerBankCardResult">
        <include refid="selectCustomerBankCardVo"/>
        <where>
            <if test="customerId != null ">and customer_id = #{customerId}</if>
            <if test="bankCardNo != null  and bankCardNo != ''">and bank_card_no = #{bankCardNo}</if>
            <if test="bankCode != null  and bankCode != ''">and bank_code = #{bankCode}</if>
            <if test="bankName != null  and bankName != ''">and bank_name like concat('%', #{bankName}, '%')</if>
            <if test="bankMobile != null  and bankMobile != ''">and bank_mobile = #{bankMobile}</if>
            <if test="isOther != null ">and is_other = #{isOther}</if>
            <if test="state != null ">and state = #{state}</if>
        </where>
    </select>
    <select id="selectCustomerBankCardListByCustomerId" parameterType="Long" resultMap="CustomerBankCardResult">
        <include refid="selectCustomerBankCardVo"/>
        <where>
            state!=-1
            <if test="customerId != null ">and customer_id = #{customerId}</if>
        </where>
    </select>


    <select id="selectCustomerBankCardById" parameterType="Long" resultMap="CustomerBankCardResult">
        <include refid="selectCustomerBankCardVo"/>
        where id = #{id}
    </select>

    <insert id="insertCustomerBankCard" parameterType="CustomerBankCard">
        insert into customer_bank_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="bankCardNo != null and bankCardNo != ''">bank_card_no,</if>
            <if test="bankCode != null and bankCode != ''">bank_code,</if>
            <if test="bankName != null and bankName != ''">bank_name,</if>
            <if test="bankMobile != null and bankMobile != ''">bank_mobile,</if>
            <if test="isOther != null">is_other,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="state != null">state,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="bankCardNo != null and bankCardNo != ''">#{bankCardNo},</if>
            <if test="bankCode != null and bankCode != ''">#{bankCode},</if>
            <if test="bankName != null and bankName != ''">#{bankName},</if>
            <if test="bankMobile != null and bankMobile != ''">#{bankMobile},</if>
            <if test="isOther != null">#{isOther},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="state != null">#{state},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCustomerBankCard" parameterType="CustomerBankCard">
        update customer_bank_card
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="bankCardNo != null and bankCardNo != ''">bank_card_no = #{bankCardNo},</if>
            <if test="bankCode != null and bankCode != ''">bank_code = #{bankCode},</if>
            <if test="bankName != null and bankName != ''">bank_name = #{bankName},</if>
            <if test="bankMobile != null and bankMobile != ''">bank_mobile = #{bankMobile},</if>
            <if test="isOther != null">is_other = #{isOther},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerBankCardById" parameterType="Long">
        delete
        from customer_bank_card
        where id = #{id}
    </delete>

    <delete id="deleteCustomerBankCardByIds" parameterType="String">
        delete from customer_bank_card where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>