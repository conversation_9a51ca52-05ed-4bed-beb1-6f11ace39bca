<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.settlement.mapper.CustomerTradeApplyMapperEx">

    <!-- 审核驳回 -->
    <update id="tradeAudit">
        update customer_trade_apply set audit_status = #{auditStatus},audit_by = #{userName},audit_time = #{auditTime},audit_reason = #{remark}
        where waybill_id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
        and audit_status = 0
    </update>

    <!-- 撤回审核通过的申请单 -->
    <update id="tradeRevoke">
        update customer_trade_apply set audit_status = 2,audit_by = #{userName},audit_time = CURRENT_TIMESTAMP,audit_reason = #{remark}
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and audit_status IN (1,4)
    </update>

    <update id="updateCustomerTradeApplyByWaybillId">
        update customer_trade_apply set audit_status = 3
        where audit_status = 1 and waybill_id in
        <foreach item="waybillId" collection="ids" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </update>

    <select id="selectCustomerTradeApplyListByIds"
            resultMap="com.zly.project.settlement.mapper.CustomerTradeApplyMapper.CustomerTradeApplyResult">
        <include refid="com.zly.project.settlement.mapper.CustomerTradeApplyMapper.selectCustomerTradeApplyVo"/>
        <where>
            audit_status in (1,4)
            <if test="ids != null and ids.size()>0">
                and id in
                <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 运单是否有已申请付款的（0:待审核 1:审核通过（待付款）3:已付款 4:付款失败 5:付款中） -->
    <select id="existPaymentApply" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM customer_trade_apply
        WHERE audit_status IN (0, 1, 3, 4, 5)
        <if test="ids != null and ids.size()>0">
            AND waybill_id IN
            <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <!-- 检查运单的申请付款状态，0:待审核 1:审核通过 5:付款中 不能被申请 -->
    <select id="tradeApplyVerify" parameterType="String" resultType="Integer">
        SELECT count(1) FROM customer_trade_apply WHERE audit_status IN (0,1,5) AND waybill_id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryCustomerFlow" resultType="FlowRes">
        SELECT
        cta.id AS id,
        w.id AS waybillId,
        cta.submit_time AS submitTime,
        w.shipping_note_number AS waybillCode,
        w.shipping_note_number AS shippingNoteNumber,
        cta.fare_stage AS fareStage,
        CASE cta.fare_stage
        WHEN 0 THEN '总费用'
        WHEN 1 THEN '预付'
        WHEN 2 THEN '到付'
        WHEN 3 THEN '回单付'
        END fareStageString,
        w.framework_contract_name AS frameworkContractName,
        w.customer_name AS customerName,
        cta.remark AS remark,
        w.remark AS waybillRemark,
        cta.apply_pay_code AS applyPayCode,
        cta.audit_status AS auditStatus,
        CASE cta.audit_status
        WHEN 3 THEN '已付款'
        WHEN 4 THEN '付款失败'
        WHEN 5 THEN '付款中'
        END auditStatusString,
        cta.apply_money AS applyMoney,
        cta.payee_name AS payeeName,
        pi.bank_card_no AS bankCardNo,
        pi.bank_name AS bankName,
        pi.identity_card AS identityCard,
        cta.pay_time AS payTime,
        w.driver_name driverName,
        w.actual_carrier_name actualCarrierName,
        w.vehicle_number vehicleNumber,
        w.pay_fare payFare,
        w.status status,
        cr.city loadingCity,
        cr.area loadingArea,
        ce.city unloadCity,
        ce.area unloadArea,
        w.resource resource
        FROM
        customer_trade_apply cta
        LEFT JOIN waybill w ON cta.waybill_id = w.id
        LEFT JOIN consignor_info cr ON w.consignor_id = cr.id
        LEFT JOIN consignee_info ce ON w.consignee_id = ce.id
        <if test="customerName != null and customerName != ''">
            JOIN framework_contract fc ON fc.id = cta.contract_id
            and fc.last_customer_name like concat('%', #{customerName}, '%')
        </if>
        JOIN payee_info pi ON cta.payee_id = pi.id
        WHERE cta.commit_customer_id = #{customerId}
        and cta.audit_status in (3,4,5)
        <if test="contractIds != null and contractIds.size() > 0">
            AND cta.contract_id IN
            <foreach collection="contractIds" item="contractId" open="(" close=")" separator=",">
                #{contractId}
            </foreach>

        </if>
        <if test="userMakeCodeIds != null and userMakeCodeIds.size() > 0">
            AND w.make_code_id IN
            <foreach collection="userMakeCodeIds" item="makeCodeId" open="(" close=")" separator=",">
                #{makeCodeId}
            </foreach>
        </if>
        <if test="startTime != null and startTime != ''">
            and cta.pay_time &gt; #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and cta.pay_time &lt; #{endTime}
        </if>
        <if test="waybillCode != null and waybillCode != ''">
            and cta.shipping_note_number = #{waybillCode}
        </if>
        <if test="shippingNoteNumbers != null and shippingNoteNumbers.size() > 0">
            AND w.shipping_note_number IN
            <foreach collection="shippingNoteNumbers" item="shippingNoteNumber" index="index" open="(" close=")"
                     separator=",">
                #{shippingNoteNumber}
            </foreach>
        </if>
        <if test="waybillRemark != null and waybillRemark != ''">
            and w.remark like concat('%', #{waybillRemark}, '%')
        </if>
        <if test="payeeName != null and payeeName != ''">
            and cta.payee_name like concat('%', #{payeeName}, '%')
        </if>
        <if test="payeeAccountNo != null and payeeAccountNo != ''">
            and pi.bank_card_no like concat('%', #{payeeAccountNo}, '%')
        </if>
        <if test="payeeCartNo != null and payeeCartNo != ''">
            and pi.identity_card like concat('%', #{payeeCartNo}, '%')
        </if>
        <if test="state != null and state != ''">
            and cta.audit_status = #{state}
        </if>
        <if test="contractId != null and contractId != ''">
            and cta.contract_id = #{contractId}
        </if>
        <if test="resource != null">
            and w.resource = #{resource}
        </if>
        <if test="originalDocumentNumber != null">and w.original_document_number = #{originalDocumentNumber}</if>
        order by cta.pay_time desc
    </select>

    <select id="queryplatformFlow" resultType="FlowRes">
        SELECT
        cta.id AS id,
        cta.waybill_id AS waybillId,
        cta.submit_time AS submitTime,
        w.shipping_note_number AS waybillCode,
        w.framework_contract_name AS frameworkContractName,
        cta.fare_stage AS fareStage,
        CASE cta.fare_stage
        WHEN 0 THEN '总费用'
        WHEN 1 THEN '预付'
        WHEN 2 THEN '到付'
        WHEN 3 THEN '回单付'
        END fareStageString,
        cta.remark AS remark,
        <if test="customerId != null and customerId != ''">
            ci.customer_name AS customerName,
        </if>
        cta.apply_pay_code AS applyPayCode,
        cta.audit_status AS auditStatus,
        CASE cta.audit_status
        WHEN 3 THEN '已付款'
        WHEN 4 THEN '付款失败'
        WHEN 5 THEN '付款中'
        END auditStatusString,
        cta.apply_money AS applyMoney,
        cta.payee_name AS payeeName,
        pi.bank_card_no AS bankCardNo,
        pi.bank_name AS bankName,
        pi.identity_card AS identityCard,
        cta.pay_time AS payTime
        FROM customer_trade_apply cta
        LEFT JOIN waybill w ON cta.waybill_id = w.id
        LEFT JOIN payee_info pi ON cta.payee_id = pi.id
        <if test="customerId != null and customerId != ''">
            LEFT JOIN framework_contract fc ON fc.id = w.framework_contract_id
            LEFT JOIN waybill_contract_chain wcc ON wcc.waybill_id = cta.waybill_id
            LEFT JOIN framework_contract_subchain fcs ON fcs.chain_id = wcc.chain_id and fcs.is_leaf = 0 and fcs.state = 0
            LEFT JOIN customer_info ci ON ci.id = fcs.parent_customer_id
        </if>
        where w.freight_forwarder_id = #{freightForwarderId}
        and w.settle_status = 2
        and cta.audit_status = 3
        <if test="startTime != null and startTime != ''">
            and cta.submit_time &gt; #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and cta.submit_time &lt; #{endTime}
        </if>
        <if test="waybillCode != null and waybillCode != ''">
            and w.shipping_note_number = #{waybillCode}
        </if>
        <if test="payeeName != null and payeeName != ''">
            and cta.payee_name like concat('%', #{payeeName}, '%')
        </if>
        <if test="collectBankNo != null and collectBankNo != ''">
            and pi.bank_card_no like concat('%', #{collectBankNo}, '%')
        </if>
        <if test="payeeCartNo != null and payeeCartNo != ''">
            and pi.identity_card like concat('%', #{payeeCartNo}, '%')
        </if>
        <if test="contractId != null and contractId != ''">
            and cta.contract_id = #{contractId}
        </if>
        <if test="customerId != null and customerId != ''">
            and ci.id = #{customerId}
        </if>
        order by w.pay_time desc
    </select>

    <select id="queryCustomerFlowWithWaybill" resultType="flowResEx">
        SELECT
        cta.id AS id,
        cta.submit_time AS submitTime,
        w.id waybillId,
        w.shipping_note_number AS waybillCode,
        cta.fare_stage AS fareStage,
        CASE cta.fare_stage
        WHEN 0 THEN '总费用'
        WHEN 1 THEN '预付'
        WHEN 2 THEN '到付'
        WHEN 3 THEN '回单付'
        END fareStageString,
        w.framework_contract_name AS frameworkContractName,
        w.customer_name AS customerName,
        cta.remark AS remark,
        cta.apply_pay_code AS applyPayCode,
        cta.audit_status AS auditStatus,
        CASE cta.audit_status
        WHEN 3 THEN '已付款'
        WHEN 4 THEN '付款失败'
        WHEN 5 THEN '付款中'
        END auditStatusString,
        cta.apply_money AS applyMoney,
        cta.payee_name AS payeeName,
        pi.bank_card_no AS bankCardNo,
        pi.bank_name AS bankName,
        pi.identity_card AS identityCard,
        cta.pay_time AS payTime,
        mc.name_alias as demainName,
        CASE w.status
        WHEN 2 THEN '未开始'
        WHEN 3 THEN '运输中'
        WHEN 4 THEN '已完成'
        END statusString,
        w.description_of_goods as goodsName,
        w.driver_name as driverName,
        w.telephone as driverPhone,
        w.remark as waybillRemark,
        w.vehicle_number as plateNo
        FROM
        customer_trade_apply cta
        LEFT JOIN waybill w ON cta.waybill_id = w.id
        LEFT JOIN framework_contract fc ON fc.id = w.framework_contract_id
        LEFT JOIN payee_info pi ON cta.payee_id = pi.id
        LEFT JOIN make_code mc ON mc.id = w.make_code_id
        WHERE (cta.commit_customer_id = #{customerId}
        or cta.audit_customer_id = #{customerId})
        and cta.audit_status in (3,4,5)
        <if test="projectGroupIds != null and projectGroupIds.size() > 0">
            AND cta.contract_id IN (
            SELECT DISTINCT contract_id FROM project_group_list WHERE create_client = 3 AND group_id IN
            <foreach collection="projectGroupIds" item="groupId" open="(" close=")" separator=",">
                #{groupId}
            </foreach>
            )
        </if>
        <if test="contractIds != null and contractIds.size() > 0">
            AND cta.contract_id IN
            <foreach collection="contractIds" item="contractId" open="(" close=")" separator=",">
                #{contractId}
            </foreach>
        </if>
        <if test="startTime != null and startTime != ''">
            and cta.pay_time &gt; #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and cta.pay_time &lt; #{endTime}
        </if>
        <if test="waybillCode != null and waybillCode != ''">
            and w.shipping_note_number = #{waybillCode}
        </if>
        <if test="payeeName != null and payeeName != ''">
            and cta.payee_name like concat('%', #{payeeName}, '%')
        </if>
        <if test="payeeAccountNo != null and payeeAccountNo != ''">
            and pi.bank_card_no like concat('%', #{payeeAccountNo}, '%')
        </if>
        <if test="payeeCartNo != null and payeeCartNo != ''">
            and pi.identity_card like concat('%', #{payeeCartNo}, '%')
        </if>
        <if test="waybillRemark != null and waybillRemark != ''">
            and w.remark like concat('%', #{waybillRemark}, '%')
        </if>
        <if test="state != null and state != ''">
            and cta.audit_status = #{state}
        </if>
        <if test="contractId != null and contractId != ''">
            and cta.contract_id = #{contractId}
        </if>
        <if test="customerName != null and customerName != ''">
            and fc.last_customer_name like concat('%', #{customerName}, '%')
        </if>
        order by cta.pay_time desc
    </select>


    <select id="queryCustomerFlowWithWaybillSP" resultType="flowResEx">
        SELECT
        cta.id AS id,
        cta.submit_time AS submitTime,
        w.id waybillId,
        w.shipping_note_number AS waybillCode,
        cta.fare_stage AS fareStage,
        CASE cta.fare_stage
        WHEN 0 THEN '总费用'
        WHEN 1 THEN '预付'
        WHEN 2 THEN '到付'
        WHEN 3 THEN '回单付'
        END fareStageString,
        w.framework_contract_name AS frameworkContractName,
        w.customer_name AS customerName,
        cta.remark AS remark,
        cta.apply_pay_code AS applyPayCode,
        cta.audit_status AS auditStatus,
        CASE cta.audit_status
        WHEN 3 THEN '已付款'
        WHEN 4 THEN '付款失败'
        WHEN 5 THEN '付款中'
        END auditStatusString,
        cta.apply_money AS applyMoney,
        cta.payee_name AS payeeName,
        pi.bank_card_no AS bankCardNo,
        pi.bank_name AS bankName,
        pi.identity_card AS identityCard,
        cta.pay_time AS payTime,
        mc.name_alias as demainName,
        CASE w.status
        WHEN 2 THEN '未开始'
        WHEN 3 THEN '运输中'
        WHEN 4 THEN '已完成'
        END statusString,
        w.description_of_goods as goodsName,
        w.driver_name as driverName,
        w.telephone as driverPhone,
        w.remark as waybillRemark,
        w.vehicle_number as plateNo,
        fc.contract_code as frameworkContractCode
        FROM
        customer_trade_apply cta
        LEFT JOIN waybill w ON cta.waybill_id = w.id
        LEFT JOIN framework_contract fc ON fc.id = w.framework_contract_id
        LEFT JOIN payee_info pi ON cta.payee_id = pi.id
        LEFT JOIN make_code mc ON mc.id = w.make_code_id
        WHERE
        (
        cta.commit_customer_id in
        <foreach collection="list" item="customerId" open="(" close=")" separator=",">
            #{customerId}
        </foreach>
        or cta.audit_customer_id in
        <foreach collection="list" item="customerId" open="(" close=")" separator=",">
            #{customerId}
        </foreach>
        )
        and cta.audit_status in (3,4,5)
        and cta.pay_time &gt; #{startTime}
        <if test="freightForwarderId != null">
            and w.freight_forwarder_id = #{freightForwarderId}
        </if>
        order by cta.pay_time
    </select>


    <select id="queryplatformFlowWithWaybill" resultType="flowResEx">
        SELECT
        cta.id AS id,
        cta.submit_time AS submitTime,
        w.shipping_note_number AS waybillCode,
        w.framework_contract_name AS frameworkContractName,
        cta.fare_stage AS fareStage,
        CASE cta.fare_stage
        WHEN 0 THEN '总费用'
        WHEN 1 THEN '预付'
        WHEN 2 THEN '到付'
        WHEN 3 THEN '回单付'
        END fareStageString,
        cta.remark AS remark,
        ci.customer_name AS customerName,
        cta.apply_pay_code AS applyPayCode,
        cta.audit_status AS auditStatus,
        CASE cta.audit_status
        WHEN 3 THEN '已付款'
        WHEN 4 THEN '付款失败'
        WHEN 5 THEN '付款中'
        END auditStatusString,
        cta.apply_money AS applyMoney,
        cta.payee_name AS payeeName,
        pi.bank_card_no AS bankCardNo,
        pi.bank_name AS bankName,
        pi.identity_card AS identityCard,
        cta.pay_time AS payTime,
        mc.name_alias as demainName,
        CASE w.status
        WHEN 2 THEN '未开始'
        WHEN 3 THEN '运输中'
        WHEN 4 THEN '已完成'
        END statusString,
        w.description_of_goods as goodsName,
        w.driver_name as driverName,
        w.telephone as driverPhone,
        w.vehicle_number as plateNo
        FROM
        customer_trade_apply cta
        LEFT JOIN waybill w ON cta.waybill_id = w.id
        LEFT JOIN make_code mc ON mc.id = w.make_code_id
        LEFT JOIN framework_contract fc ON fc.id = w.framework_contract_id
        LEFT JOIN payee_info pi ON cta.payee_id = pi.id
        LEFT JOIN waybill_contract_chain wcc ON wcc.waybill_id = cta.waybill_id
        LEFT JOIN framework_contract_subchain fcs ON fcs.chain_id = wcc.chain_id
        LEFT JOIN customer_info ci ON ci.id = fcs.parent_customer_id
        where fcs.freight_forwarder_id = #{freightForwarderId}
        and fcs.is_leaf = 0
        and cta.audit_status = 3
        <choose>
            <when test="settleStatus == 0">
                and w.settle_status in (0,1)
            </when>
            <otherwise>
                and w.settle_status = 2
            </otherwise>
        </choose>
        <if test="startTime != null and startTime != ''">
            and cta.pay_time &gt; #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and cta.pay_time &lt; #{endTime}
        </if>
        <if test="waybillCode != null and waybillCode != ''">
            and w.shipping_note_number = #{waybillCode}
        </if>
        <if test="payeeName != null and payeeName != ''">
            and cta.payee_name like concat('%', #{payName}, '%')
        </if>
        <if test="payeeAccountNo != null and payeeAccountNo != ''">
            and pi.bank_card_no like concat('%', #{collectBankNo}, '%')
        </if>
        <if test="payeeCartNo != null and payeeCartNo != ''">
            and pi.identity_card like concat('%', #{payeeCartNo}, '%')
        </if>
        <if test="state != null and state != ''">
            and cta.audit_status = #{state}
        </if>
        <if test="contractId != null and contractId != ''">
            and cta.contract_id = #{frameworkContractId}
        </if>
        <if test="customerId != null and customerId != ''">
            and ci.id = #{customerId}
        </if>
        group by id order by cta.pay_time desc
    </select>

    <select id="selectCustomerTradeApplyListByPayee"
            resultType="com.zly.project.miniprogram.domain.res.IncomeRecordsRes">
        SELECT w.shipping_note_number as shippingNoteNumber,a.fare_stage as fareStage,a.apply_money as applyMoney,
        a.pay_time as payTime,w.haulway as haulway,w.payee_name payeeName
        from waybill w
        LEFT JOIN customer_trade_apply a on a.waybill_id = w.id
        where w.resource = 6 and a.audit_status = 3
        <if test="payeeId != null and driverId == null">
            and a.payee_id = #{payeeId} and w.payee_type <![CDATA[<]]>4
        </if>
        <if test="driverId != null and payeeId == null">
            and w.driver_id = #{driverId} and w.payee_type = 4
        </if>
        <if test="driverId != null and payeeId != null">
            and (
            ( a.payee_id = #{payeeId} and w.payee_type <![CDATA[<]]>4)
            or (w.driver_id = #{driverId} and w.payee_type = 4)
            )
        </if>
        ORDER BY a.pay_time desc
    </select>
    <select id="queryCustomerFlowByWaybillId" resultType="com.zly.project.transport.waybill.domain.res.WaybillApplyRes">
        SELECT cta.id                 AS id,
               cta.waybill_id         AS waybillId,
               w.shipping_note_number AS shippingNoteNumber,
               cta.apply_pay_code     AS applyPayCode,
               cta.fare_stage         AS fareStage,
               cta.audit_status       AS auditStatus,
               cta.remark             AS remark,
               cta.apply_money        AS applyMoney,
               cta.payee_name         AS payeeName,
               pi.bank_card_no        AS payeeBankCardNo,
               pi.bank_name           AS bankName,
               pi.identity_card       AS payeeIdentityCard,
               cta.pay_time           AS payTime
        FROM customer_trade_apply cta
                 LEFT JOIN waybill w ON cta.waybill_id = w.id
                 LEFT JOIN payee_info pi ON cta.payee_id = pi.id
        where cta.waybill_id = #{waybillId}
          and audit_status > 2
    </select>

    <select id="queryReasonByWaybillId" resultType="java.lang.String">
        select audit_reason
        from customer_trade_apply
        where waybill_id = #{id}
        order by submit_time desc limit 1
    </select>

    <select id="countCustomerFlow" resultType="java.lang.Long">
        SELECT count(*) FROM customer_trade_apply cta
        left join waybill w on w.id = cta.waybill_id
        <if test="customerName != null and customerName != ''">
            JOIN framework_contract fc ON fc.id = cta.contract_id
            and fc.last_customer_name = #{customerName}
        </if>
        <if test="(payeeCartNo != null and payeeCartNo != '') or (payeeAccountNo != null and payeeAccountNo != '') ">
            JOIN payee_info pi ON cta.payee_id = pi.id
        </if>
        <if test="payeeAccountNo != null and payeeAccountNo != ''">
            and pi.bank_card_no = #{payeeAccountNo}
        </if>
        <if test="payeeCartNo != null and payeeCartNo != ''">
            and pi.identity_card = #{payeeCartNo}
        </if>

        WHERE cta.commit_customer_id = #{customerId} and cta.audit_status in (3,4,5)
        <if test="contractIds != null and contractIds.size() > 0">
            AND cta.contract_id IN
            <foreach collection="contractIds" item="contractId" open="(" close=")" separator=",">
                #{contractId}
            </foreach>

        </if>
        <if test="startTime != null and startTime != ''">
            and cta.pay_time &gt; #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and cta.pay_time &lt; #{endTime}
        </if>
        <if test="waybillCode != null and waybillCode != ''">
            and cta.shipping_note_number = #{waybillCode}
        </if>
        <if test="shippingNoteNumbers != null and shippingNoteNumbers.size() > 0">
            AND w.shipping_note_number IN
            <foreach collection="shippingNoteNumbers" item="shippingNoteNumber" index="index" open="(" close=")"
                     separator=",">
                #{shippingNoteNumber}
            </foreach>
        </if>
        <if test="payeeName != null and payeeName != ''">
            and cta.payee_name like concat('%', #{payeeName}, '%')
        </if>
        <if test="waybillRemark != null and waybillRemark != ''">
            and w.remark like concat('%', #{waybillRemark}, '%')
        </if>
        <if test="state != null and state != ''">
            and cta.audit_status = #{state}
        </if>
        <if test="contractId != null and contractId != ''">
            and cta.contract_id = #{contractId}
        </if>
        <if test="originalDocumentNumber != null">and w.original_document_number = #{originalDocumentNumber}</if>
    </select>

    <update id="mergeShippersCommit">
        UPDATE customer_trade_apply
        set commit_customer_id = #{toId}
        where commit_customer_id = #{fromId}
    </update>

    <update id="mergeShippersAudit">
        UPDATE customer_trade_apply
        set audit_customer_id = #{toId}
        where audit_customer_id = #{fromId}
    </update>

    <update id="updateFinancingStatusByWaybillIds">
        UPDATE customer_trade_apply set financing_status = #{financingStatus} where waybill_id in
        <foreach collection="waybillIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
    <update id="huaXiaTradeAudit">
        update customer_trade_apply set audit_status = #{auditStatus},audit_by = #{userName},audit_time = #{auditTime},audit_reason = #{remark}
        where waybill_id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
        and audit_status = 1
    </update>

    <select id="selectPaymentList" resultType="PdsPayment">
        select pi.payee_name    as receiptName,
               pi.identity_card as receiptIdentity,
               pi.bank_card_no  as receiptCard,
               a.apply_pay_code as payCode,
               a.apply_money    as payMoney,
               a.pay_time       as payBackTime
        from customer_trade_apply a
                 inner join payee_info pi on a.payee_id = pi.id
        where a.audit_status = 3
          and a.waybill_id = #{waybillId}
    </select>

    <select id="billStatisticsListByCustomerIds" resultType="billStatisticsInfo">
        SELECT
        SUM(apply_money) as payMoney,fcs.parent_customer_id as customerId
        FROM
        customer_trade_apply cta
        INNER JOIN waybill w on cta.waybill_id = w.id
        JOIN framework_contract_subchain fcs on w.framework_contract_id = fcs.contract_id and fcs.is_leaf = 0 and
        fcs.state = 0
        WHERE
        cta.audit_status = 3 and cta.pay_time BETWEEN #{startTime} AND #{endTime}
        and w.freight_forwarder_id = #{freightForwarderId}
        <if test="customerIds != null and customerIds.size() > 0">
            and fcs.parent_customer_id in
            <foreach item="customerId" collection="customerIds" open="(" separator="," close=")">
                #{customerId}
            </foreach>
        </if>
        GROUP BY fcs.parent_customer_id
    </select>


    <select id="billStatisticsListByContractIds" resultType="billStatisticsInfo">
        SELECT
        SUM(apply_money) as payMoney,w.framework_contract_id as contractId
        FROM
        customer_trade_apply cta
        INNER JOIN waybill w on cta.waybill_id = w.id
        WHERE
        cta.audit_status = 3 and cta.pay_time BETWEEN #{startTime} AND #{endTime}
        and w.freight_forwarder_id = #{freightForwarderId}
        <if test="contractIds != null and contractIds.size() > 0">
            and w.framework_contract_id in
            <foreach item="contractId" collection="contractIds" open="(" separator="," close=")">
                #{contractId}
            </foreach>
        </if>
        GROUP BY w.framework_contract_id
    </select>

    <select id="sumByTime" resultType="java.math.BigDecimal">
        SELECT SUM(apply_money) AS payMoney
        FROM customer_trade_apply cta
                 INNER JOIN waybill w ON cta.waybill_id = w.id
        WHERE cta.audit_status = 3
          AND cta.pay_time BETWEEN #{start} AND #{end}
          AND w.freight_forwarder_id = #{freightForwarderId}
    </select>
</mapper>
