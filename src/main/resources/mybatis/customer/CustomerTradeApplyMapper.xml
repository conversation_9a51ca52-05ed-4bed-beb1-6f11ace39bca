<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.settlement.mapper.CustomerTradeApplyMapper">

    <resultMap type="CustomerTradeApply" id="CustomerTradeApplyResult">
        <result property="id"    column="id"    />
        <result property="waybillId"    column="waybill_id"    />
        <result property="shippingNoteNumber"    column="shipping_note_number"    />
        <result property="fareStage"    column="fare_stage"    />
        <result property="applyPayCode"    column="apply_pay_code"    />
        <result property="payeeId"    column="payee_id"    />
        <result property="payeeName"    column="payee_name"    />
        <result property="applyMoney"    column="apply_money"    />
        <result property="submitBy"    column="submit_by"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="auditBy"    column="audit_by"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditReason"    column="audit_reason"    />
        <result property="contractId"    column="contract_id"    />
        <result property="contractName"    column="contract_name"    />
        <result property="commitCustomerId"    column="commit_customer_id"    />
        <result property="auditCustomerId"    column="audit_customer_id"    />
        <result property="auditCustomerIsPrepayer"    column="audit_customer_is_prepayer"    />
        <result property="payTime"    column="pay_time"    />
        <result property="remark"    column="remark"    />
        <result property="financingStatus"    column="financing_status"    />
    </resultMap>

    <sql id="selectCustomerTradeApplyVo">
        select id, waybill_id, shipping_note_number, fare_stage, apply_pay_code, payee_id, payee_name,
               apply_money, submit_by, submit_time, audit_status, audit_by, audit_time, audit_reason,
               contract_id, contract_name, commit_customer_id, audit_customer_id, audit_customer_is_prepayer, pay_time, remark,financing_status
        from customer_trade_apply
    </sql>

    <select id="selectCustomerTradeApplyList" parameterType="CustomerTradeApply" resultMap="CustomerTradeApplyResult">
        <include refid="selectCustomerTradeApplyVo"/>
        <where>
            <if test="waybillId != null "> and waybill_id = #{waybillId}</if>
            <if test="shippingNoteNumber != null  and shippingNoteNumber != ''"> and shipping_note_number = #{shippingNoteNumber}</if>
            <if test="fareStage != null "> and fare_stage = #{fareStage}</if>
            <if test="applyPayCode != null  and applyPayCode != ''"> and apply_pay_code = #{applyPayCode}</if>
            <if test="payeeId != null "> and payee_id = #{payeeId}</if>
            <if test="payeeName != null  and payeeName != ''"> and payee_name like concat('%', #{payeeName}, '%')</if>
            <if test="applyMoney != null "> and apply_money = #{applyMoney}</if>
            <if test="submitBy != null  and submitBy != ''"> and submit_by = #{submitBy}</if>
            <if test="params != null and params.beginSubmitTime != null and params.beginSubmitTime != '' and params.endSubmitTime != null and params.endSubmitTime != ''"> and submit_time between #{params.beginSubmitTime} and #{params.endSubmitTime}</if>
            <if test="auditStatus != null "> and audit_status = #{auditStatus}</if>
            <if test="auditBy != null  and auditBy != ''"> and audit_by = #{auditBy}</if>
            <if test="params != null and params.beginAuditTime != null and params.beginAuditTime != '' and params.endAuditTime != null and params.endAuditTime != ''"> and audit_time between #{params.beginAuditTime} and #{params.endAuditTime}</if>
            <if test="auditReason != null  and auditReason != ''"> and audit_reason = #{auditReason}</if>
            <if test="contractId != null  and contractId != ''"> and contract_id = #{contractId}</if>
            <if test="contractName != null  and contractName != ''"> and contract_name = #{contractName}</if>
            <if test="commitCustomerId != null "> and commit_customer_id = #{commitCustomerId}</if>
            <if test="auditCustomerId != null "> and audit_customer_id = #{auditCustomerId}</if>
            <if test="auditCustomerIsPrepayer != null "> and audit_customer_is_prepayer = #{auditCustomerIsPrepayer}</if>
            <if test="remark != null and remark != ''"> and remark = #{remark}</if>
            <if test="financingStatus != null"> and financing_status = #{financingStatus}</if>
        </where>
    </select>

    <select id="selectCustomerTradeApplyById" parameterType="Long" resultMap="CustomerTradeApplyResult">
        <include refid="selectCustomerTradeApplyVo"/>
        where id = #{id}
    </select>
    <select id="selectCustomerTradeApplyByIds" resultMap="CustomerTradeApplyResult">
        <include refid="selectCustomerTradeApplyVo"/>
        where waybill_id in
        <foreach item="waybillId" collection="ids" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
        AND audit_status in (1,4)
        GROUP BY waybill_id
    </select>
    <select id="selectCustomerTradeApplyByWaybillIds" resultMap="CustomerTradeApplyResult">
        <include refid="selectCustomerTradeApplyVo"/>
        where waybill_id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </select>

    <insert id="insertCustomerTradeApply" parameterType="CustomerTradeApply">
        insert into customer_trade_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="waybillId != null">waybill_id,</if>
            <if test="shippingNoteNumber != null and shippingNoteNumber != ''">shipping_note_number,</if>
            <if test="fareStage != null">fare_stage,</if>
            <if test="applyPayCode != null and applyPayCode != ''">apply_pay_code,</if>
            <if test="payeeId != null">payee_id,</if>
            <if test="payeeName != null and payeeName != ''">payee_name,</if>
            <if test="applyMoney != null">apply_money,</if>
            <if test="submitBy != null and submitBy != ''">submit_by,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="auditBy != null and auditBy != ''">audit_by,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditReason != null and auditReason != ''">audit_reason,</if>
            <if test="contractId != null and contractId != ''">contract_id,</if>
            <if test="contractName != null and contractName != ''">contract_name,</if>
            <if test="commitCustomerId != null ">commit_customer_id,</if>
            <if test="auditCustomerId != null ">audit_customer_id,</if>
            <if test="auditCustomerIsPrepayer != null ">audit_customer_is_prepayer,</if>
            <if test="payTime != null ">pay_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="financingStatus != null">financing_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="waybillId != null">#{waybillId},</if>
            <if test="shippingNoteNumber != null and shippingNoteNumber != ''">#{shippingNoteNumber},</if>
            <if test="fareStage != null">#{fareStage},</if>
            <if test="applyPayCode != null and applyPayCode != ''">#{applyPayCode},</if>
            <if test="payeeId != null">#{payeeId},</if>
            <if test="payeeName != null and payeeName != ''">#{payeeName},</if>
            <if test="applyMoney != null">#{applyMoney},</if>
            <if test="submitBy != null and submitBy != ''">#{submitBy},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="auditBy != null and auditBy != ''">#{auditBy},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditReason != null and auditReason != ''">#{auditReason},</if>
            <if test="contractId != null and contractId != ''">#{contractId},</if>
            <if test="contractName != null and contractName != ''">#{contractName},</if>
            <if test="commitCustomerId != null ">#{commitCustomerId},</if>
            <if test="auditCustomerId != null ">#{auditCustomerId},</if>
            <if test="auditCustomerIsPrepayer != null ">#{auditCustomerIsPrepayer},</if>
            <if test="payTime != null ">#{payTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="financingStatus != null">#{financingStatus},</if>
        </trim>
    </insert>
    <insert id="insertCustomerTradeApplys">
        insert IGNORE into customer_trade_apply
        (id,
        waybill_id,
        shipping_note_number,
        fare_stage,
        apply_pay_code,
        payee_id,
        payee_name,
        apply_money,
        submit_by,
        submit_time,
        audit_status,
        audit_by,
        audit_time,
        audit_reason,
        contract_id,
        contract_name,
        commit_customer_id,
        audit_customer_id,
        audit_customer_is_prepayer,
        pay_time,
        remark,
        financing_status
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.id},
            #{item.waybillId},
            #{item.shippingNoteNumber},
            #{item.fareStage},
            #{item.applyPayCode},
            #{item.payeeId},
            #{item.payeeName},
            #{item.applyMoney},
            #{item.submitBy},
            #{item.submitTime},
            #{item.auditStatus},
            #{item.auditBy},
            <if test="item.auditTime != null">#{item.auditTime},</if>
            <if test="item.auditTime == null">default,</if>
            #{item.auditReason},
            #{item.contractId},
            #{item.contractName},
            #{item.commitCustomerId},
            #{item.auditCustomerId},
            #{item.auditCustomerIsPrepayer},
            <if test="item.payTime != null">#{item.payTime},</if>
            <if test="item.payTime == null">default,</if>
            #{item.remark},
            #{item.financingStatus}
        </foreach>

    </insert>

    <update id="updateCustomerTradeApply" parameterType="CustomerTradeApply">
        update customer_trade_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="waybillId != null">waybill_id = #{waybillId},</if>
            <if test="shippingNoteNumber != null and shippingNoteNumber != ''">shipping_note_number =
                #{shippingNoteNumber},
            </if>
            <if test="fareStage != null">fare_stage = #{fareStage},</if>
            <if test="applyPayCode != null and applyPayCode != ''">apply_pay_code = #{applyPayCode},</if>
            <if test="payeeId != null">payee_id = #{payeeId},</if>
            <if test="payeeName != null and payeeName != ''">payee_name = #{payeeName},</if>
            <if test="applyMoney != null">apply_money = #{applyMoney},</if>
            <if test="submitBy != null and submitBy != ''">submit_by = #{submitBy},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditBy != null and auditBy != ''">audit_by = #{auditBy},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditReason != null and auditReason != ''">audit_reason = #{auditReason},</if>
            <if test="contractId != null and contractId != ''">contract_id = #{contractId},</if>
            <if test="contractName != null and contractName != ''">contract_name = #{contractName},</if>
            <if test="commitCustomerId != null ">commit_customer_id = #{commitCustomerId},</if>
            <if test="auditCustomerId != null ">audit_customer_id = #{auditCustomerId},</if>
            <if test="auditCustomerIsPrepayer != null ">audit_customer_is_prepayer = #{auditCustomerIsPrepayer},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="financingStatus != null">  financing_status = #{financingStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerTradeApplyById" parameterType="Long">
        delete from customer_trade_apply where id = #{id}
    </delete>

    <delete id="deleteCustomerTradeApplyByIds" parameterType="String">
        delete from customer_trade_apply where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCustomerTradeApplyByWaybillIds">
        delete from customer_trade_apply where waybill_id in
        <foreach item="waybillId" collection="waybillIds" open="(" separator="," close=")">
            #{waybillId}
        </foreach>
    </delete>
</mapper>
