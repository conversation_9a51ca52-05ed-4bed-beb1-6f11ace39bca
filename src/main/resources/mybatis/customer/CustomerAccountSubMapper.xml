<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.settlement.mapper.CustomerAccountSubMapper">

    <resultMap type="CustomerAccountSub" id="CustomerAccountSubResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="frameworkContractId"    column="framework_contract_id"    />
        <result property="contractName"    column="contract_name"    />
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="password"    column="password"    />
        <result property="accountNo"    column="account_no"    />
        <result property="bankCode"    column="bank_code"    />
        <result property="bankName"    column="bank_name"    />
        <result property="money"    column="money"    />
        <result property="sumMoney"    column="sum_money"    />
        <result property="remark"    column="remark"    />
        <result property="state"    column="state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerAccountSubVo">
        select id, parent_id, framework_contract_id, contract_name, customer_id, customer_name, password, account_no, bank_code, bank_name, money, sum_money, remark, state, create_by, create_time, update_by, update_time from customer_account_sub
    </sql>

    <select id="selectCustomerAccountSubList" parameterType="CustomerAccountSub" resultMap="CustomerAccountSubResult">
        <include refid="selectCustomerAccountSubVo"/>
        <where>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="frameworkContractId != null "> and framework_contract_id = #{frameworkContractId}</if>
            <if test="contractName != null  and contractName != ''"> and contract_name like concat('%', #{contractName}, '%')</if>
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="password != null and password != ''"> and password = #{password}</if>
            <if test="accountNo != null and accountNo != ''"> and account_no = #{accountNo}</if>
            <if test="bankCode != null  and bankCode != ''"> and bank_code = #{bankCode}</if>
            <if test="bankName != null  and bankName != ''"> and bank_name like concat('%', #{bankName}, '%')</if>
            <if test="money != null ">and money = #{money}</if>
            <if test="sumMoney != null ">and sum_money = #{sumMoney}</if>
            <if test="state != null ">and state = #{state}</if>
        </where>
    </select>

    <select id="selectCustomerAccountSubById" parameterType="Long" resultMap="CustomerAccountSubResult">
        <include refid="selectCustomerAccountSubVo"/>
        where id = #{id}
    </select>
    <select id="selectCustomerAccountSubByContractIds" resultMap="CustomerAccountSubResult">
        <include refid="selectCustomerAccountSubVo"/>
        where framework_contract_id in
        <foreach item="contractId" collection="contractIds" open="(" separator="," close=")">
            #{contractId}
        </foreach>
    </select>

    <insert id="insertCustomerAccountSub" parameterType="CustomerAccountSub">
        insert into customer_account_sub
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="frameworkContractId != null">framework_contract_id,</if>
            <if test="contractName != null and contractName != ''">contract_name,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="accountNo != null and accountNo != ''">account_no,</if>
            <if test="bankCode != null and bankCode != ''">bank_code,</if>
            <if test="bankName != null and bankName != ''">bank_name,</if>
            <if test="money != null">money,</if>
            <if test="sumMoney != null">sum_money,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="state != null">state,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="frameworkContractId != null">#{frameworkContractId},</if>
            <if test="contractName != null and contractName != ''">#{contractName},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="accountNo != null and accountNo != ''">#{accountNo},</if>
            <if test="bankCode != null and bankCode != ''">#{bankCode},</if>
            <if test="bankName != null and bankName != ''">#{bankName},</if>
            <if test="money != null">#{money},</if>
            <if test="sumMoney != null">#{sumMoney},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="state != null">#{state},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>
    <insert id="insertCustomerAccountSubs">
        insert IGNORE into customer_account_sub
        (id,
        parent_id,
        framework_contract_id,
        contract_name,
        customer_id,
        customer_name,
        password,
        account_no,
        bank_code,
        bank_name,
        money,
        sum_money,
        remark,
        state,
        create_by,
        create_time,
        update_by,
        update_time
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.id},
            #{item.parentId},
            #{item.frameworkContractId},
            #{item.contractName},
            #{item.customerId},
            #{item.customerName},
            #{item.password},
            #{item.accountNo},
            #{item.bankCode},
            #{item.bankName},
            #{item.money},
            #{item.sumMoney},
            #{item.remark},
            #{item.state},
            #{item.createBy},
            <if test="item.createTime != null">#{item.createTime},</if>
            <if test="item.createTime == null">default,</if>
            #{item.updateBy},
            <if test="item.updateTime != null">#{item.updateTime}</if>
            <if test="item.updateTime == null">default</if>
        </foreach>

    </insert>

    <update id="updateCustomerAccountSub" parameterType="CustomerAccountSub">
        update customer_account_sub
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="frameworkContractId != null">framework_contract_id = #{frameworkContractId},</if>
            <if test="contractName != null and contractName != ''">contract_name = #{contractName},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="accountNo != null and accountNo != ''">account_no = #{accountNo},</if>
            <if test="bankCode != null and bankCode != ''">bank_code = #{bankCode},</if>
            <if test="bankName != null and bankName != ''">bank_name = #{bankName},</if>
            <if test="money != null">money = #{money},</if>
            <if test="sumMoney != null">sum_money = #{sumMoney},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerAccountSubById" parameterType="Long">
        delete from customer_account_sub where id = #{id}
    </delete>

    <delete id="deleteCustomerAccountSubByIds" parameterType="String">
        delete from customer_account_sub where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateContractNameByContractId">
        update customer_account_sub set contract_name = #{contractName} where framework_contract_id = #{contractId}
    </update>
</mapper>