<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.customerForwarderContract.mapper.CustomerForwarderContractMapper">

    <resultMap type="CustomerForwarderContract" id="CustomerForwarderContractResult">
        <result property="id" column="id"/>
        <result property="customerId" column="customer_id"/>
        <result property="customerCreditCode" column="customer_credit_code"/>
        <result property="customerName" column="customer_name"/>
        <result property="freightForwarderId" column="freight_forwarder_id"/>
        <result property="forwarderCreditCode" column="forwarder_credit_code"/>
        <result property="forwarderName" column="forwarder_name"/>
        <result property="contractNumber" column="contract_number"/>
        <result property="contractName" column="contract_name"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="state" column="state"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="signDate" column="sign_date"/>
    </resultMap>

    <sql id="selectCustomerForwarderContractVo">
        select id,
               customer_id,
               customer_credit_code,
               customer_name,
               freight_forwarder_id,
               forwarder_credit_code,
               forwarder_name,
               contract_number,
               contract_name,
               start_time,
               end_time,
               state,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               sign_date
        from customer_forwarder_contract
    </sql>

    <select id="selectCustomerForwarderContractList" parameterType="CustomerForwarderContract" resultMap="CustomerForwarderContractResult">
        <include refid="selectCustomerForwarderContractVo"/>
        <where>
            <if test="id != null ">and id = #{id}</if>
            <if test="customerId != null ">and customer_id = #{customerId}</if>
            <if test="customerCreditCode != null  and customerCreditCode != ''">and customer_credit_code = #{customerCreditCode}</if>
            <if test="customerName != null  and customerName != ''">and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="freightForwarderId != null ">and freight_forwarder_id = #{freightForwarderId}</if>
            <if test="forwarderCreditCode != null  and forwarderCreditCode != ''">and forwarder_credit_code = #{forwarderCreditCode}</if>
            <if test="forwarderName != null  and forwarderName != ''">and forwarder_name like concat('%', #{forwarderName}, '%')</if>
            <if test="contractNumber != null  and contractNumber != ''">and contract_number = #{contractNumber}</if>
            <if test="contractName != null  and contractName != ''">and contract_name like concat('%', #{contractName}, '%')</if>
            <if test="startTime != null ">and start_time = #{startTime}</if>
            <if test="endTime != null ">and end_time = #{endTime}</if>
            <if test="state != null ">and state = #{state}</if>
            <if test="createBy != null  and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null ">and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null ">and update_time = #{updateTime}</if>
            <if test="remark != null  and remark != ''">and remark = #{remark}</if>
            <if test="signDate != null">and sign_date = #{signDate}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectCustomerForwarderContractById" resultMap="CustomerForwarderContractResult">
        <include refid="selectCustomerForwarderContractVo"/>
        where id = #{id}
    </select>
    <select id="selectContractByCustomerAndState" resultMap="CustomerForwarderContractResult">
        <include refid="selectCustomerForwarderContractVo"/>
        where customer_id = #{customerId} and state = #{state} and freight_forwarder_id = #{freightForwarderId}
    </select>

    <select id="selectCustomerForwarderContractByContractCode" resultMap="CustomerForwarderContractResult">
        <include refid="selectCustomerForwarderContractVo"/>
        where contract_number = #{contractNumber}
    </select>

    <select id="selectContractListByFile" resultMap="CustomerForwarderContractResult">
        select cfc.id,
        cfc.customer_id,
        cfc.customer_credit_code,
        cfc.customer_name,
        cfc.freight_forwarder_id,
        cfc.forwarder_credit_code,
        cfc.forwarder_name,
        cfc.contract_number,
        cfc.contract_name,
        cfc.start_time,
        cfc.end_time,
        cfc.state,
        cfc.create_by,
        cfc.create_time,
        cfc.update_by,
        cfc.update_time,
        cfc.remark,
        cfc.sign_date
        from customer_forwarder_contract cfc join customer_forwarder_contract_attachment_info cfcai on cfcai.relation_id
        = cfc.id and cfcai.file_url!=''
        <where>
            <if test="id != null ">and cfc.id = #{id}</if>
            <if test="customerId != null ">and cfc.customer_id = #{customerId}</if>
            <if test="customerCreditCode != null  and customerCreditCode != ''">and cfc.customer_credit_code =
                #{customerCreditCode}
            </if>
            <if test="customerName != null  and customerName != ''">and cfc.customer_name like concat('%',
                #{customerName}, '%')
            </if>
            <if test="freightForwarderId != null ">and cfc.freight_forwarder_id = #{freightForwarderId}</if>
            <if test="forwarderCreditCode != null  and forwarderCreditCode != ''">and cfc.forwarder_credit_code =
                #{forwarderCreditCode}
            </if>
            <if test="forwarderName != null  and forwarderName != ''">and cfc.forwarder_name like concat('%',
                #{forwarderName}, '%')
            </if>
            <if test="contractNumber != null  and contractNumber != ''">and cfc.contract_number = #{contractNumber}</if>
            <if test="contractName != null  and contractName != ''">and cfc.contract_name like concat('%',
                #{contractName}, '%')
            </if>
            <if test="startTime != null ">and cfc.start_time = #{startTime}</if>
            <if test="endTime != null ">and cfc.end_time = #{endTime}</if>
            <if test="state != null ">and cfc.state = #{state}</if>
            <if test="createBy != null  and createBy != ''">and cfc.create_by = #{createBy}</if>
            <if test="createTime != null ">and cfc.create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''">and cfc.update_by = #{updateBy}</if>
            <if test="updateTime != null ">and cfc.update_time = #{updateTime}</if>
            <if test="remark != null  and remark != ''">and cfc.remark = #{remark}</if>
            <if test="signDate != null">and cfc.sign_date = #{signDate}</if>
        </where>
        group by cfc.id order by cfc.create_time desc
    </select>
    <select id="selectContractByCustomerIdsAndForwarderId" resultMap="CustomerForwarderContractResult">
        <include refid="selectCustomerForwarderContractVo"/>
        where freight_forwarder_id = #{forwarderId}
        and customer_id in
        <foreach item="customerId" collection="customerIds" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </select>
    <select id="selectContractList"
            resultType="com.zly.project.customerForwarderContract.domain.req.CustomerForwarderContractReq">
        select cfc.id AS id,
        cfc.customer_id AS customerId,
        cfc.customer_credit_code AS customerCreditCode,
        cfc.customer_name AS customerName,
        cfc.freight_forwarder_id AS freightForwarderId,
        cfc.forwarder_credit_code AS forwarderCreditCode,
        cfc.forwarder_name AS forwarderName,
        cfc.contract_number AS contractNumber,
        cfc.contract_name AS contractName,
        cfc.start_time AS startTime,
        cfc.end_time AS endTime,
        cfc.state AS state,
        cfc.create_by AS createBy,
        cfc.create_time AS createTime,
        cfc.update_by AS updateBy,
        cfc.update_time AS updateTime,
        cfc.remark AS remark,
        cfc.sign_date AS signDate
        from customer_forwarder_contract cfc join customer_forwarder_contract_attachment_info cfcai on cfcai.relation_id
        = cfc.id and cfcai.file_url!=''
        <where>
            <if test="id != null ">and cfc.id = #{id}</if>
            <if test="customerId != null ">and cfc.customer_id = #{customerId}</if>
            <if test="customerCreditCode != null  and customerCreditCode != ''">and cfc.customer_credit_code =
                #{customerCreditCode}
            </if>
            <if test="customerName != null  and customerName != ''">and cfc.customer_name like concat('%',
                #{customerName}, '%')
            </if>
            <if test="freightForwarderId != null ">and cfc.freight_forwarder_id = #{freightForwarderId}</if>
            <if test="forwarderCreditCode != null  and forwarderCreditCode != ''">and cfc.forwarder_credit_code =
                #{forwarderCreditCode}
            </if>
            <if test="forwarderName != null  and forwarderName != ''">and cfc.forwarder_name like concat('%',
                #{forwarderName}, '%')
            </if>
            <if test="contractNumber != null  and contractNumber != ''">and cfc.contract_number = #{contractNumber}</if>
            <if test="contractName != null  and contractName != ''">and cfc.contract_name like concat('%',
                #{contractName}, '%')
            </if>
            <if test="startTime != null ">and cfc.start_time = #{startTime}</if>
            <if test="endTime != null ">and cfc.end_time = #{endTime}</if>
            <if test="state != null ">and cfc.state = #{state}</if>
            <if test="createBy != null  and createBy != ''">and cfc.create_by = #{createBy}</if>
            <if test="createTime != null ">and cfc.create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''">and cfc.update_by = #{updateBy}</if>
            <if test="updateTime != null ">and cfc.update_time = #{updateTime}</if>
            <if test="remark != null  and remark != ''">and cfc.remark = #{remark}</if>
            <if test="signDate != null">and cfc.sign_date = #{signDate}</if>
        </where>
        group by cfc.id order by cfc.create_time desc
    </select>
    <select id="selectContractListByTime"
            resultType="com.zly.project.customerForwarderContract.domain.req.CustomerForwarderContractReq">
        select cfc.id AS id,
        cfc.customer_id AS customerId,
        cfc.customer_credit_code AS customerCreditCode,
        cfc.customer_name AS customerName,
        cfc.freight_forwarder_id AS freightForwarderId,
        cfc.forwarder_credit_code AS forwarderCreditCode,
        cfc.forwarder_name AS forwarderName,
        cfc.contract_number AS contractNumber,
        cfc.contract_name AS contractName,
        cfc.start_time AS startTime,
        cfc.end_time AS endTime,
        cfc.state AS state,
        cfc.create_by AS createBy,
        cfc.create_time AS createTime,
        cfc.update_by AS updateBy,
        cfc.update_time AS updateTime,
        cfc.remark AS remark,
        cfc.sign_date AS signDate
        from customer_forwarder_contract cfc join customer_forwarder_contract_attachment_info cfcai on cfcai.relation_id
        = cfc.id and cfcai.file_url!=''
        <where>
            <if test="id != null ">and cfc.id = #{id}</if>
            <if test="customerId != null ">and cfc.customer_id = #{customerId}</if>
            <if test="customerCreditCode != null  and customerCreditCode != ''">and cfc.customer_credit_code =
                #{customerCreditCode}
            </if>
            <if test="customerName != null  and customerName != ''">and cfc.customer_name like concat('%',
                #{customerName}, '%')
            </if>
            <if test="freightForwarderId != null ">and cfc.freight_forwarder_id = #{freightForwarderId}</if>
            <if test="forwarderCreditCode != null  and forwarderCreditCode != ''">and cfc.forwarder_credit_code =
                #{forwarderCreditCode}
            </if>
            <if test="forwarderName != null  and forwarderName != ''">and cfc.forwarder_name like concat('%',
                #{forwarderName}, '%')
            </if>
            <if test="contractNumber != null  and contractNumber != ''">and cfc.contract_number = #{contractNumber}</if>
            <if test="contractName != null  and contractName != ''">and cfc.contract_name like concat('%',
                #{contractName}, '%')
            </if>
            <if test="startTime != null ">and cfc.start_time = #{startTime}</if>
            <if test="endTime != null ">and cfc.end_time = #{endTime}</if>
            <if test="state != null ">and cfc.state = #{state}</if>
            <if test="createBy != null  and createBy != ''">and cfc.create_by = #{createBy}</if>
            <if test="createTime != null ">and cfc.create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''">and cfc.update_by = #{updateBy}</if>
            <if test="updateTime != null ">and cfc.update_time = #{updateTime}</if>
            <if test="remark != null  and remark != ''">and cfc.remark = #{remark}</if>
            <if test="signDate != null">and cfc.sign_date = #{signDate}</if>
            <if test="waybillCreateTime != null">and cfc.start_time &lt;= #{waybillCreateTime} and cfc.end_time &gt;=
                #{waybillCreateTime}
            </if>
        </where>
        group by cfc.id order by cfc.create_time desc
    </select>

    <insert id="insertCustomerForwarderContract" parameterType="CustomerForwarderContract">
        insert IGNORE into customer_forwarder_contract
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerCreditCode != null and customerCreditCode != ''">customer_credit_code,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="freightForwarderId != null">freight_forwarder_id,</if>
            <if test="forwarderCreditCode != null and forwarderCreditCode != ''">forwarder_credit_code,</if>
            <if test="forwarderName != null and forwarderName != ''">forwarder_name,</if>
            <if test="contractNumber != null and contractNumber != ''">contract_number,</if>
            <if test="contractName != null and contractName != ''">contract_name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="state != null">state,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="signDate != null">sign_date,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerCreditCode != null and customerCreditCode != ''">#{customerCreditCode},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="freightForwarderId != null">#{freightForwarderId},</if>
            <if test="forwarderCreditCode != null and forwarderCreditCode != ''">#{forwarderCreditCode},</if>
            <if test="forwarderName != null and forwarderName != ''">#{forwarderName},</if>
            <if test="contractNumber != null and contractNumber != ''">#{contractNumber},</if>
            <if test="contractName != null and contractName != ''">#{contractName},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="state != null">#{state},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="signDate != null">#{signDate},</if>
        </trim>
    </insert>

    <update id="updateCustomerForwarderContract" parameterType="CustomerForwarderContract">
        update customer_forwarder_contract
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerCreditCode != null and customerCreditCode != ''">customer_credit_code = #{customerCreditCode},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="freightForwarderId != null">freight_forwarder_id = #{freightForwarderId},</if>
            <if test="forwarderCreditCode != null and forwarderCreditCode != ''">forwarder_credit_code = #{forwarderCreditCode},</if>
            <if test="forwarderName != null and forwarderName != ''">forwarder_name = #{forwarderName},</if>
            <if test="contractNumber != null and contractNumber != ''">contract_number = #{contractNumber},</if>
            <if test="contractName != null and contractName != ''">contract_name = #{contractName},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="signDate != null">sign_date = #{signDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerForwarderContractById" parameterType="Long">
        delete
        from customer_forwarder_contract
        where id = #{id}
    </delete>

    <delete id="deleteCustomerForwarderContractByIds" parameterType="String">
        delete from customer_forwarder_contract where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
