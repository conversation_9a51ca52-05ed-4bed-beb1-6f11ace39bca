<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.customerForwarderContract.mapper.CustomerForwarderContractAttachmentInfoMapper">

    <resultMap type="CustomerForwarderContractAttachmentInfo" id="CustomerForwarderContractAttachmentInfoResult">
        <result property="id" column="id"/>
        <result property="relationId" column="relation_id"/>
        <result property="fileName" column="file_name"/>
        <result property="originalFileName" column="original_file_name"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileType" column="file_type"/>
        <result property="fileUrl" column="file_url"/>
        <result property="keyValue" column="key_value"/>
        <result property="state" column="state"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCustomerForwarderContractAttachmentInfoVo">
        select id,
               relation_id,
               file_name,
               original_file_name,
               file_size,
               file_type,
               file_url,
               key_value,
               state,
               create_by,
               create_time,
               update_by,
               update_time
        from customer_forwarder_contract_attachment_info
    </sql>

    <select id="selectCustomerForwarderContractAttachmentInfoList" parameterType="CustomerForwarderContractAttachmentInfo" resultMap="CustomerForwarderContractAttachmentInfoResult">
        <include refid="selectCustomerForwarderContractAttachmentInfoVo"/>
        <where>
            state = 0
            <if test="id != null ">and id = #{id}</if>
            <if test="relationId != null ">and relation_id = #{relationId}</if>
            <if test="fileName != null  and fileName != ''">and file_name like concat('%', #{fileName}, '%')</if>
            <if test="originalFileName != null  and originalFileName != ''">and original_file_name like concat('%', #{originalFileName}, '%')</if>
            <if test="fileSize != null and fileSize != ''">and file_size = #{fileSize}</if>
            <if test="fileType != null  and fileType != ''">and file_type = #{fileType}</if>
            <if test="fileUrl != null  and fileUrl != ''">and file_url = #{fileUrl}</if>
            <if test="keyValue != null  and keyValue != ''">and key_value = #{keyValue}</if>
            <if test="createBy != null  and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null ">and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null ">and update_time = #{updateTime}</if>
        </where>
    </select>

    <select id="selectCustomerForwarderContractAttachmentInfoById" parameterType="Long"
            resultMap="CustomerForwarderContractAttachmentInfoResult">
        <include refid="selectCustomerForwarderContractAttachmentInfoVo"/>
        where id = #{id} and state = 0
    </select>
    <select id="selectByRelationIds" resultMap="CustomerForwarderContractAttachmentInfoResult">
        <include refid="selectCustomerForwarderContractAttachmentInfoVo"/>
        where state = 0 and
        relation_id in
        <foreach item="relationId" collection="relationIds" open="(" separator="," close=")">
            #{relationIds}
        </foreach>
    </select>

    <insert id="insertCustomerForwarderContractAttachmentInfo" parameterType="CustomerForwarderContractAttachmentInfo">
        insert IGNORE into customer_forwarder_contract_attachment_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="relationId != null">relation_id,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="originalFileName != null and originalFileName != ''">original_file_name,</if>
            <if test="fileSize != null and fileSize != ''">file_size,</if>
            <if test="fileType != null and fileType != ''">file_type,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="keyValue != null and keyValue != ''">key_value,</if>
            <if test="state != null">state,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="relationId != null">#{relationId},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="originalFileName != null and originalFileName != ''">#{originalFileName},</if>
            <if test="fileSize != null and fileSize != ''">#{fileSize},</if>
            <if test="fileType != null and fileType != ''">#{fileType},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="keyValue != null and keyValue != ''">#{keyValue},</if>
            <if test="state != null">#{state},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCustomerForwarderContractAttachmentInfo" parameterType="CustomerForwarderContractAttachmentInfo">
        update customer_forwarder_contract_attachment_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="relationId != null">relation_id = #{relationId},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="originalFileName != null and originalFileName != ''">original_file_name = #{originalFileName},</if>
            <if test="fileSize != null and fileSize != ''">file_size = #{fileSize},</if>
            <if test="fileType != null and fileType != ''">file_type = #{fileType},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="keyValue != null and keyValue != ''">key_value = #{keyValue},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerForwarderContractAttachmentInfoById" parameterType="Long">
        delete
        from customer_forwarder_contract_attachment_info
        where id = #{id}
    </delete>

    <delete id="deleteCustomerForwarderContractAttachmentInfoByIds" parameterType="String">
        delete from customer_forwarder_contract_attachment_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCustomerForwarderContractAttachmentInfoByRelationIds">
        delete from customer_forwarder_contract_attachment_info where relation_id in
        <foreach item="id" collection="forwarderContractIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectFileNameAndUrlListByRelationId"
            resultMap="CustomerForwarderContractAttachmentInfoResult">
        select file_name,file_url from customer_forwarder_contract_attachment_info where relation_id = #{relationId} and state = 0
    </select>
</mapper>
