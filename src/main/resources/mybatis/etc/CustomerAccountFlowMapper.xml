<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.etc.mapper.WaybillUpetcRecordMapper">



    <insert id="insert" >
        insert into waybill_upetc_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="waybillId != null">waybill_id,</if>
            <if test="waybillCode != null and waybillCode != ''">waybill_code,</if>
            <if test="waybillType != null and waybillType != ''">waybill_type,</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">vehicle_number,</if>
            <if test="requestUrl != null and requestUrl != ''" >request_url,</if>
            <if test="requestText != null and requestText != ''">request_text,</if>
            <if test="responseStatus != null and responseStatus != ''">response_status,</if>
            <if test="responseMessage != null and responseMessage != ''">response_message,</if>
            <if test="responseText != null and responseText != ''">response_text,</if>
            <if test="createTime != null">create_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="freightForwarderId != null">freight_forwarder_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="waybillId != null">#{waybillId},</if>
            <if test="waybillCode != null and waybillCode != ''">#{waybillCode},</if>
            <if test="waybillType != null and waybillType != ''">#{waybillType},</if>
            <if test="vehicleNumber != null and vehicleNumber != ''">#{vehicleNumber},</if>
            <if test="requestUrl != null and requestUrl != ''">#{requestUrl},</if>
            <if test="requestText != null and requestText != ''">#{requestText},</if>
            <if test="responseStatus != null and responseStatus != ''">#{responseStatus},</if>
            <if test="responseMessage != null and responseMessage != ''">#{responseMessage},</if>
            <if test="responseText != null and responseText != ''">#{responseText},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="modifyTime != null ">#{modifyTime},</if>
            <if test="freightForwarderId != null">#{freightForwarderId},</if>
        </trim>
    </insert>


</mapper>