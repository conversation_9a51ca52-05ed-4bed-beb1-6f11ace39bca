<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.group.mapper.FrameworkContractSubchainMapper">

    <resultMap type="FrameworkContractSubchain" id="FrameworkContractSubchainResult">
        <result property="id" column="id"/>
        <result property="contractId" column="contract_id"/>
        <result property="rate" column="rate"/>
        <result property="customerId" column="customer_id"/>
        <result property="parentCustomerId" column="parent_customer_id"/>
        <result property="deep" column="deep"/>
        <result property="isLeaf" column="is_leaf"/>
        <result property="isRecorder" column="is_recorder"/>
        <result property="isPayer" column="is_payer"/>
        <result property="isPrepayer" column="is_prepayer"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="freightForwarderId" column="freight_forwarder_id"/>
        <result property="chainId" column="chain_id"/>
        <result property="loanRate" column="loan_rate"/>
        <result property="loanTerm" column="loan_term"/>
        <result property="loanRateToCustomerId" column="loan_rate_to_customer_id"/>
    </resultMap>

    <sql id="selectFrameworkContractSubchainVo">
        select id,
               contract_id,
               rate,
               customer_id,
               parent_customer_id,
               deep,
               is_leaf,
               is_recorder,
               is_payer,
               is_prepayer,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               freight_forwarder_id,
               chain_id,
               loan_rate,
               loan_term,
               loan_rate_to_customer_id
        from framework_contract_subchain
    </sql>

    <select id="selectFrameworkContractSubchainList" parameterType="FrameworkContractSubchain"
            resultMap="FrameworkContractSubchainResult">
        <include refid="selectFrameworkContractSubchainVo"/>
        <where>
            <if test="contractId != null ">and contract_id = #{contractId}</if>
            <if test="rate != null ">and rate = #{rate}</if>
            <if test="customerId != null ">and customer_id = #{customerId}</if>
            <if test="parentCustomerId != null ">and parent_customer_id = #{parentCustomerId}</if>
            <if test="deep != null ">and deep = #{deep}</if>
            <if test="isLeaf != null ">and is_leaf = #{isLeaf}</if>
            <if test="isRecorder != null ">and is_recorder = #{isRecorder}</if>
            <if test="isPayer != null ">and is_payer = #{isPayer}</if>
            <if test="isPrepayer != null ">and is_prepayer = #{isPrepayer}</if>
            <if test="state != null ">and state = #{state}</if>
            <if test="freightForwarderId != null ">and freight_forwarder_id = #{freightForwarderId}</if>
            <if test="chainId != null ">and chain_id = #{chainId}</if>
            <if test="loanRate != null ">and loan_rate = #{loanRate}</if>
            <if test="loanTerm != null ">and loan_term = #{loanTerm}</if>
            <if test="loanRateToCustomerId != null ">and loan_rate_to_customer_id = #{loanRateToCustomerId}</if>
        </where>
    </select>

    <select id="selectFrameworkContractSubchainById" parameterType="Long" resultMap="FrameworkContractSubchainResult">
        <include refid="selectFrameworkContractSubchainVo"/>
        where id = #{id} and state = 0
    </select>
    <select id="selectFrameworkContractSubchainByContractId" resultMap="FrameworkContractSubchainResult">
        <include refid="selectFrameworkContractSubchainVo"/>
        where contract_id = #{contractId} and state = 0 order by deep asc
    </select>
    <select id="selectForwarderUpCustomerSubchainsByContractIds" resultMap="FrameworkContractSubchainResult">
        <include refid="selectFrameworkContractSubchainVo"/>
        where state = 0 and is_leaf = 0 and contract_id in
        <foreach item="contractId" collection="contractIds" open="(" separator="," close=")">
            #{contractId}
        </foreach>
    </select>
    <select id="selectSubchainMaxDeepByContractId" resultMap="FrameworkContractSubchainResult">
        <include refid="selectFrameworkContractSubchainVo"/>
        WHERE deep = (SELECT MAX(deep) FROM framework_contract_subchain WHERE contract_id = #{contractId} and state = 0
        ) and contract_id = #{contractId} and state = 0 ;
    </select>
    <select id="selectByContractIds" resultMap="FrameworkContractSubchainResult">
        <include refid="selectFrameworkContractSubchainVo"/>
        where state = 0 and contract_id in
        <foreach item="contractId" collection="contractIds" open="(" separator="," close=")">
            #{contractId}
        </foreach>
    </select>
    <select id="selectCustomerNamesByContractIds"
            resultType="com.zly.project.group.domain.FrameworkContractSubchainEx">
        select s.contract_id contractId , c.customer_name customerName
        from framework_contract_subchain s
        left join customer_info c on s.customer_id = c.id
        where s.contract_id in
        <foreach item="contractId" collection="contractIds" open="(" separator="," close=")">
            #{contractId}
        </foreach>
        and s.state = 0 ;
    </select>
    <select id="selectForwarderCustomerSubchainsByContractIds"
            resultMap="FrameworkContractSubchainResult">
        <include refid="selectFrameworkContractSubchainVo"/>
        where state = 0 and is_leaf = 0
        <if test="contractIds != null and contractIds.size() > 0">
            and contract_id in
            <foreach item="contractId" collection="contractIds" open="(" separator="," close=")">
                #{contractId}
            </foreach>
        </if>
    </select>

    <insert id="insertFrameworkContractSubchain" parameterType="FrameworkContractSubchain">
        insert into framework_contract_subchain
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="contractId != null">contract_id,</if>
            <if test="rate != null">rate,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="parentCustomerId != null">parent_customer_id,</if>
            <if test="deep != null">deep,</if>
            <if test="isLeaf != null">is_leaf,</if>
            <if test="isRecorder != null">is_recorder,</if>
            <if test="isPayer != null">is_payer,</if>
            <if test="isPrepayer != null">is_prepayer,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="state != null ">state,</if>
            <if test="freightForwarderId != null and freightForwarderId != ''">freight_forwarder_id,</if>
            <if test="chainId != null ">chain_id,</if>
            <if test="loanRate != null ">loan_rate,</if>
            <if test="loanTerm != null ">loan_term,</if>
            <if test="loanRateToCustomerId != null ">loan_rate_to_customer_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="contractId != null">#{contractId},</if>
            <if test="rate != null">#{rate},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="parentCustomerId != null">#{parentCustomerId},</if>
            <if test="deep != null">#{deep},</if>
            <if test="isLeaf != null">#{isLeaf},</if>
            <if test="isRecorder != null">#{isRecorder},</if>
            <if test="isPayer != null">#{isPayer},</if>
            <if test="isPrepayer != null">#{isPrepayer},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="state != null ">#{state},</if>
            <if test="freightForwarderId != null and freightForwarderId != ''">#{freightForwarderId},</if>
            <if test="chainId != null ">#{chainId},</if>
            <if test="loanRate != null ">#{loanRate},</if>
            <if test="loanTerm != null ">#{loanTerm},</if>
            <if test="loanRateToCustomerId != null ">#{loanRateToCustomerId},</if>
        </trim>
    </insert>
    <insert id="insertFrameworkContractSubchains">
        insert IGNORE into framework_contract_subchain
        (id,
        contract_id,
        rate,
        customer_id,
        parent_customer_id,
        deep,
        is_leaf,
        is_recorder,
        is_payer,
        is_prepayer,
        create_by,
        create_time,
        update_by,
        update_time,
        remark,
        freight_forwarder_id,
        chain_id,
        loan_rate,
        loan_term,
        loan_rate_to_customer_id
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.id},
            #{item.contractId},
            #{item.rate},
            #{item.customerId},
            #{item.parentCustomerId},
            #{item.deep},
            #{item.isLeaf},
            #{item.isRecorder},
            #{item.isPayer},
            #{item.isPrepayer},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime},
            #{item.remark},
            #{item.freightForwarderId},
            #{item.chainId},
            #{item.loanRate},
            #{item.loanTerm},
            #{item.loanRateToCustomerId}
        </foreach>
    </insert>

    <update id="updateFrameworkContractSubchain" parameterType="FrameworkContractSubchain">
        update framework_contract_subchain
        <trim prefix="SET" suffixOverrides=",">
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="rate != null">rate = #{rate},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="parentCustomerId != null">parent_customer_id = #{parentCustomerId},</if>
            <if test="deep != null">deep = #{deep},</if>
            <if test="isLeaf != null">is_leaf = #{isLeaf},</if>
            <if test="isRecorder != null">is_recorder = #{isRecorder},</if>
            <if test="isPayer != null">is_payer = #{isPayer},</if>
            <if test="isPrepayer != null">is_prepayer = #{isPrepayer},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="state != null ">state = #{state},</if>
            <if test="freightForwarderId != null and freightForwarderId != ''">freight_forwarder_id =
                #{freightForwarderId},
            </if>
            <if test="chainId != null ">chain_id = #{chainId},</if>
            <if test="loanRate != null ">loan_rate = #{loanRate},</if>
            <if test="loanTerm != null ">loan_term = #{loanTerm},</if>
            <if test="loanRateToCustomerId != null ">loan_rate_to_customer_id = #{loanRateToCustomerId}</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrameworkContractSubchainById" parameterType="Long">
        delete
        from framework_contract_subchain
        where id = #{id}
    </delete>

    <delete id="deleteFrameworkContractSubchainByIds" parameterType="String">
        delete from framework_contract_subchain where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="deleteFrameworkContractSubchainByContractId">
        update framework_contract_subchain set state = -1 where contract_id = #{contractId}
    </update>
</mapper>
