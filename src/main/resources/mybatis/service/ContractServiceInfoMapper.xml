<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.service.customerservice.mapper.ContractServiceInfoMapper">

    <resultMap type="ContractServiceInfo" id="ContractServiceInfoResult">
        <result property="id" column="id"/>
        <result property="contractId" column="contract_id"/>
        <result property="contractName" column="contract_name"/>
        <result property="openState" column="open_state"/>
        <result property="serviceId" column="service_id"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="auditingBy" column="auditing_by"/>
        <result property="rate" column="rate"/>
        <result property="auditingTime" column="auditing_time"/>
    </resultMap>

    <sql id="selectContractServiceInfoVo">
        SELECT id,
               contract_id,
               contract_name,
               open_state,
               service_id,
               remark,
               create_by,
               create_time,
               update_by,
               update_time,
               auditing_by,
               rate,
               auditing_time
        FROM contract_service_info
    </sql>

    <select id="selectContractServiceInfoList" parameterType="ContractServiceInfo" resultMap="ContractServiceInfoResult">
        <include refid="selectContractServiceInfoVo"/>
        <where>
            <if test="contractId != null ">and contract_id = #{contractId}</if>
            <if test="contractName != null  and contractName != ''">and contract_name like concat('%', #{contractName}, '%')</if>
            <if test="openState != null ">and open_state = #{openState}</if>
            <if test="openState == null ">and open_state in (0,1)</if>
            <if test="serviceId != null ">and service_id = #{serviceId}</if>
        </where>
    </select>

    <select id="selectContractServiceInfoById" parameterType="Long" resultMap="ContractServiceInfoResult">
        <include refid="selectContractServiceInfoVo"/>
        where id = #{id}
    </select>
    <select id="selectByContractIds" resultMap="ContractServiceInfoResult">
        <include refid="selectContractServiceInfoVo"/>
        where contract_id in
        <foreach item="contractId" collection="contractIds" open="(" separator="," close=")">
            #{contractId}
        </foreach>
    </select>
    <select id="selectByContractIdsAndServiceIds" resultMap="ContractServiceInfoResult">
        <include refid="selectContractServiceInfoVo"/>
        where contract_id in
        <foreach item="contractId" collection="contractIds" open="(" separator="," close=")">
            #{contractId}
        </foreach>
        and service_id in
        <foreach item="serviceId" collection="serviceIds" open="(" separator="," close=")">
            #{serviceId}
        </foreach>
        and open_state = 1
    </select>

    <insert id="insertContractServiceInfo" parameterType="ContractServiceInfo">
        insert into contract_service_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="contractId != null">contract_id,</if>
            <if test="contractName != null and contractName != ''">contract_name,</if>
            <if test="openState != null">open_state,</if>
            <if test="serviceId != null">service_id,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="auditingBy != null">auditing_by,</if>
            <if test="rate != null">rate,</if>
            <if test="auditingTime != null">auditing_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="contractId != null">#{contractId},</if>
            <if test="contractName != null and contractName != ''">#{contractName},</if>
            <if test="openState != null">#{openState},</if>
            <if test="serviceId != null">#{serviceId},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="auditingBy != null">#{auditingBy},</if>
            <if test="rate != null">#{rate},</if>
            <if test="auditingTime != null">#{auditingTime},</if>
        </trim>
    </insert>
    <insert id="insertContractServiceInfos">
        insert IGNORE into contract_service_info
        (id,
        contract_id,
        contract_name,
        open_state,
        service_id,
        remark,
        create_by,
        create_time,
        update_by,
        update_time,
        auditing_by,
        rate,
        auditing_time
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            <if test="item.id != null">#{item.id},</if>
            <if test="item.contractId != null">#{item.contractId},</if>
            <if test="item.contractName != null ">#{item.contractName},</if>
            <if test="item.openState != null">#{item.openState},</if>
            #{item.serviceId},
            <if test="item.remark != null ">#{item.remark},</if>
            <if test="item.createBy != null ">#{item.createBy},</if>
            <if test="item.createTime != null">#{item.createTime},</if>
            #{item.updateBy},
            #{item.updateTime},
            #{item.auditingBy},
            #{item.rate},
            #{item.auditingTime}
        </foreach>
    </insert>

    <update id="updateContractServiceInfo" parameterType="ContractServiceInfo">
        update contract_service_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="contractName != null and contractName != ''">contract_name = #{contractName},</if>
            <if test="openState != null">open_state = #{openState},</if>
            <if test="serviceId != null">service_id = #{serviceId},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="auditingBy != null">auditing_by = #{auditingBy},</if>
            <if test="rate != null">rate = #{rate},</if>
            <if test="auditingTime != null">auditing_time = #{auditingTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteContractServiceInfoById" parameterType="Long">
        DELETE
        FROM contract_service_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteContractServiceInfoByIds" parameterType="String">
        DELETE
        FROM contract_service_info
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>