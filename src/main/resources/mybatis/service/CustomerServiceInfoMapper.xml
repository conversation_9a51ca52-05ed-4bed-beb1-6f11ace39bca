<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.service.customerservice.mapper.CustomerServiceInfoMapper">

    <resultMap type="CustomerServiceInfo" id="CustomerServiceInfoResult">
        <result property="id" column="id"/>
        <result property="customerId" column="customer_id"/>
        <result property="customerName" column="customer_name"/>
        <result property="openState" column="open_state"/>
        <result property="customerServiceId" column="customer_service_id"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCustomerServiceInfoVo">
        select id,
               customer_id,
               customer_name,
               open_state,
               customer_service_id,
               remark,
               create_by,
               create_time,
               update_by,
               update_time
        from customer_service_info
    </sql>

    <select id="selectCustomerServiceInfoList" parameterType="CustomerServiceInfo"
            resultMap="CustomerServiceInfoResult">
        <include refid="selectCustomerServiceInfoVo"/>
        <where>
            <if test="customerId != null ">and customer_id = #{customerId}</if>
            <if test="customerName != null  and customerName != ''">and customer_name like concat('%', #{customerName},
                '%')
            </if>
            <if test="openState != null ">and open_state = #{openState}</if>
            <if test="customerServiceId != null ">and customer_service_id = #{customerServiceId}</if>
        </where>
    </select>

    <select id="selectCustomerServiceInfoById" parameterType="Long" resultMap="CustomerServiceInfoResult">
        <include refid="selectCustomerServiceInfoVo"/>
        where id = #{id}
    </select>
    <select id="selectByCustomerIds" resultMap="CustomerServiceInfoResult">
        <include refid="selectCustomerServiceInfoVo"/>
        where customer_id in
        <foreach item="customerId" collection="customerIds" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </select>

    <insert id="insertCustomerServiceInfo" parameterType="CustomerServiceInfo">
        insert IGNORE into customer_service_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="openState != null">open_state,</if>
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="openState != null">#{openState},</if>
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCustomerServiceInfo" parameterType="CustomerServiceInfo">
        update customer_service_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="openState != null">open_state = #{openState},</if>
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerServiceInfoById" parameterType="Long">
        delete
        from customer_service_info
        where id = #{id}
    </delete>

    <delete id="deleteCustomerServiceInfoByIds" parameterType="String">
        delete from customer_service_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCustomerServiceInfoByCustomerId">
        delete
        from customer_service_info
        where customer_id = #{customerId}
    </delete>
</mapper>