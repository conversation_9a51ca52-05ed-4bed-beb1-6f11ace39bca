<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.system.mapper.FreightForwarderCapacityRelationMapper">

    <resultMap type="FreightForwarderCapacityRelation" id="FreightForwarderCapacityRelationResult">
        <result property="id" column="id"/>
        <result property="freightForwarderId" column="freight_forwarder_id"/>
        <result property="identityCard" column="identity_card"/>
        <result property="agreementState" column="agreement_state"/>
        <result property="fileUrl" column="file_url"/>
        <result property="signType" column="sign_type"/>
        <result property="signTime" column="sign_time"/>
        <result property="createTime" column="create_time"/>
        <result property="state" column="state"/>
        <result property="protocolCode" column="protocol_code"/>
        <result property="auditState" column="audit_state"/>
        <result property="auditRemark" column="audit_remark"/>
        <result property="auditTime" column="audit_time"/>
        <result property="expirationTime" column="expiration_time"/>
        <result property="resource" column="resource"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectFreightForwarderCapacityRelationVo">
        select id,
               freight_forwarder_id,
               identity_card,
               agreement_state,
               file_url,
               sign_time,
               create_time,
               sign_type,
               state,
               protocol_code,
               audit_state,
               audit_remark,
               audit_time,
               expiration_time,
               resource,
               update_time
        from freight_forwarder_capacity_relation
    </sql>

    <select id="selectFreightForwarderCapacityRelationList" parameterType="FreightForwarderCapacityRelation" resultMap="FreightForwarderCapacityRelationResult">
        <include refid="selectFreightForwarderCapacityRelationVo"/>
        <where>
            <if test="freightForwarderId != null ">and freight_forwarder_id = #{freightForwarderId}</if>
            <if test="identityCard != null ">and identity_card = #{identityCard}</if>
            <if test="agreementState != null ">and agreement_state = #{agreementState}</if>
            <if test="fileUrl != null  and fileUrl != ''">and file_url = #{fileUrl}</if>
            <if test="signTime != null ">and sign_time = #{signTime}</if>
            <if test="state != null ">and state = #{state}</if>
            <if test="protocolCode != null and protocolCode != '' ">and protocol_code = #{protocolCode}</if>
            <if test="auditState != null ">and audit_state = #{auditState}</if>
            <if test="auditRemark != null and auditRemark != '' ">and audit_remark = #{auditRemark}</if>
            <if test="auditTime != null ">and audit_time = #{auditTime}</if>
            <if test="expirationTime != null ">and expiration_time = #{expirationTime}</if>
            <if test="resource != null ">and resource = #{resource}</if>
            <if test="updateTime != null ">and update_time = #{updateTime}</if>
        </where>
    </select>

    <select id="selectFreightForwarderCapacityRelationById" parameterType="Long" resultMap="FreightForwarderCapacityRelationResult">
        <include refid="selectFreightForwarderCapacityRelationVo"/>
        where id = #{id}
    </select>

    <insert id="insertFreightForwarderCapacityRelation" parameterType="FreightForwarderCapacityRelation">
        insert into freight_forwarder_capacity_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="freightForwarderId != null">freight_forwarder_id,</if>
            <if test="identityCard != null and identityCard != ''">identity_card,</if>
            <if test="agreementState != null">agreement_state,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="signType != null">sign_type,</if>
            <if test="signTime != null">sign_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="state != null">state,</if>
            <if test="protocolCode != null and protocolCode != '' ">protocol_code,</if>
            <if test="auditState != null">audit_state,</if>
            <if test="auditRemark != null and auditRemark != ''">audit_remark,</if>
            <if test="auditTime != null">auditTime,</if>
            <if test="expirationTime != null">expiration_time,</if>
            <if test="resource != null">resource,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="freightForwarderId != null">#{freightForwarderId},</if>
            <if test="identityCard != null and identityCard != ''">#{identityCard},</if>
            <if test="agreementState != null">#{agreementState},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="signType != null">#{signType},</if>
            <if test="signTime != null">#{signTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="state != null">#{state},</if>
            <if test="protocolCode != null and protocolCode != '' ">#{protocolCode},</if>
            <if test="auditState != null">#{auditState},</if>
            <if test="auditRemark != null and auditRemark != ''">#{auditRemark},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="expirationTime != null">#{expirationTime},</if>
            <if test="resource != null">#{resource},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateFreightForwarderCapacityRelation" parameterType="FreightForwarderCapacityRelation">
        update freight_forwarder_capacity_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="identityCard != null and identityCard != ''">identity_card = #{identityCard},</if>
            <if test="agreementState != null">agreement_state = #{agreementState},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="signType != null">sign_type = #{signType},</if>
            <if test="signTime != null">sign_time = #{signTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="state != null">state = #{state},</if>
            <if test="protocolCode != null and protocolCode != '' ">protocol_code = #{protocolCode},</if>
            <if test="auditState != null ">audit_state = #{auditState},</if>
            <if test="auditRemark != null and auditRemark != '' ">audit_remark = #{auditRemark},</if>
            <if test="auditTime != null ">audit_time = #{auditTime},</if>
            <if test="expirationTime != null ">expiration_time = #{expirationTime},</if>
            <if test="resource != null ">resource = #{resource},</if>
            <if test="updateTime != null ">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFreightForwarderCapacityRelationById" parameterType="Long">
        delete
        from freight_forwarder_capacity_relation
        where id = #{id}
    </delete>

    <delete id="deleteFreightForwarderCapacityRelationByIds" parameterType="String">
        delete from freight_forwarder_capacity_relation where freight_forwarder_id in
        <foreach item="ids" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectFreightNameByCapacityIdCardAndCustomerId" resultType="com.zly.project.system.domain.FreightForwarderCapacityRelation">
        select ffcr.freight_forwarder_id as freightForwarderId,
               ffcr.identity_card        as identityCard,
               ffcr.agreement_state      as agreementState,
               ffcr.file_url             as fileUrl,
               ffcr.sign_time            as signTime,
               ffcr.create_time          as createTime,
               ffcr.state                as state,
               ffcr.expiration_time      as expirationTime,
               ffi.name                  as remark
        from freight_forwarder_capacity_relation ffcr
                 join freight_forwarder_info ffi on ffcr.freight_forwarder_id = ffi.id
        where ffcr.identity_card = #{idCard}
          and ffcr.sign_type = #{type}
          and ffcr.agreement_state = 1
          and ffcr.state != -1
    </select>

    <select id="selectByCapacityIdCardAndForwarderId"
            resultType="com.zly.project.system.domain.FreightForwarderCapacityRelation">
        select relation.freight_forwarder_id as freightForwarderId,
        relation.identity_card as identityCard,
        relation.agreement_state as agreementState,
        relation.file_url as fileUrl,
        relation.sign_time as signTime,
        relation.create_time as createTime,
        relation.state as state,
        relation.expiration_time as expirationTime,
        relation.update_time as updateTime,
        info.name as remark
        from freight_forwarder_capacity_relation relation
        join freight_forwarder_info info on relation.freight_forwarder_id = info.id
        where relation.identity_card = #{idCard}
        and relation.sign_type = #{type}
        <if test="agreementState != null ">
            and relation.agreement_state = #{agreementState}
        </if>
        <if test="freightForwarderId != null ">
            and relation.freight_forwarder_id = #{freightForwarderId}
        </if>
        order by relation.create_time desc
    </select>

    <select id="selectFreightNameByCapacityIdCardAndFreightForwarderId"
            resultType="com.zly.project.system.domain.FreightForwarderCapacityRelation">
        select freight_forwarder_id as freightForwarderId,
               identity_card        as identityCard,
               agreement_state      as agreementState,
               file_url             as fileUrl,
               sign_time            as signTime,
               create_time          as createTime,
               expiration_time      as expirationTime,
               state                as state
        from freight_forwarder_capacity_relation
        where identity_card = #{idCard}
          and sign_type = #{type}
          and freight_forwarder_id = #{freightForwarderId}
          and agreement_state = 1
          and state > 0
    </select>

    <select id="selectFreightForwarderCapacityRelationResListByIdcard" resultType="com.zly.project.system.domain.res.FreightForwarderCapacityRelationRes">
        select ffcr.id                   as id,
               ffcr.freight_forwarder_id as freightForwarderId,
               ffcr.identity_card        as identityCard,
               ffcr.agreement_state      as agreementState,
               ffcr.file_url             as fileUrl,
               ffcr.sign_type            as signType,
               ffcr.sign_time            as signTime,
               ffcr.create_time          as createTime,
               ffcr.expiration_time      as expirationTime,
               ffi.name                  as freightForwarderName
        from freight_forwarder_capacity_relation ffcr
                 join freight_forwarder_info ffi on ffcr.freight_forwarder_id = ffi.id
        where ffcr.identity_card = #{identityCard}
          and ffcr.sign_type = 1
    </select>

    <select id="selectFreightNameByCapacityVehicleAndCustomerId" resultType="com.zly.project.carrier.driver.domain.res.DriverRes">
        select aci.actual_carrier_name as driverName,
               pvp.identity_card       as identityCard,
               pvp.file_url            as fileUrl,
               pvp.create_time         as createTime,
               pvp.sign_state          as signingState,
               pvp.sign_type           as signType
        from person_vehicle_protocol pvp
                 join actual_carrier_info aci on pvp.identity_card = aci.identity_card
                 join customer_vehicle_relation cvr on pvp.vehicle_id = cvr.vehicle_id
                 left join customer_driver_relation cdr on cdr.driving_license = pvp.identity_card
                 left join customer_auxiliary_relation car on car.auxiliary_staff_identity_card = pvp.identity_card
                 left join customer_captain_relation ccr on ccr.actual_identity_card = pvp.identity_card
                 inner join (select identity_card, max(create_time) as maxTime from person_vehicle_protocol where vehicle_id = #{vehicleId} group by identity_card) c
                            on c.maxTime = pvp.create_time and c.identity_card = pvp.identity_card
        where pvp.vehicle_id = #{vehicleId}
          and cvr.customer_id = #{customerId}
          and pvp.state = 0
          and pvp.audit_state in (0, 1)
          AND (
                (pvp.resource != 2)
                OR
                (pvp.resource = 2 AND pvp.sign_state = 2)
            )
          and ((cdr.driver_id is not null) or (car.auxiliary_staff_id != 0) or (ccr.car_captain_id != 0))
        group by pvp.id
    </select>
    <select id="selectFreightNameByCapacityVehicleAndFreightForwarderId"
            resultType="com.zly.project.carrier.driver.domain.res.DriverRes">
        select aci.actual_carrier_name as driverName,
        pvp.identity_card as identityCard,
        pvp.file_url as fileUrl,
        pvp.create_time as createTime,
        pvp.sign_state as signState,
        pvp.sign_type as signType
        from person_vehicle_protocol pvp
        join actual_carrier_info aci on pvp.identity_card = aci.identity_card
        join freight_forwarder_vehicle_relation ffvr on ffvr.vehicle_id = pvp.vehicle_id
        inner join (select identity_card, max(create_time) as maxTime from person_vehicle_protocol where vehicle_id = #{vehicleId} group by identity_card) c on c.maxTime = pvp.create_time and
        c.identity_card = pvp.identity_card
        where pvp.vehicle_id = #{vehicleId}
        and ffvr.freight_forwarder_id = #{freightForwarderId}
        and pvp.state = 0
        and pvp.audit_state in (0,1)
        <if test="signType != null">and (pvp.sign_type = #{signType} or pvp.sign_type = 0)</if>
        group by pvp.id
    </select>
    <select id="selectFreightNameByCapacityIdCard"
            resultType="com.zly.project.system.domain.FreightForwarderCapacityRelation">
        select relation.freight_forwarder_id as freightForwarderId,
               relation.identity_card        as identityCard,
               relation.agreement_state      as agreementState,
               relation.file_url             as fileUrl,
               relation.sign_time            as signTime,
               relation.create_time          as createTime,
               relation.expiration_time      as expirationTime,
               ffi.name                      as remark
        from freight_forwarder_capacity_relation relation
                 join freight_forwarder_info ffi on relation.freight_forwarder_id = ffi.id
        where relation.identity_card = #{idCard}
          and relation.sign_type = #{type}
    </select>
    <select id="selectFreightNameByCapacityVehicle"
            resultType="com.zly.project.carrier.driver.domain.res.DriverRes">
        select aci.actual_carrier_name as driverName,
        pvp.identity_card as identityCard,
        pvp.file_url as fileUrl,
        pvp.create_time as createTime,
        pvp.sign_state as signState,
        pvp.sign_type as signType
        from person_vehicle_protocol pvp
        join actual_carrier_info aci on pvp.identity_card = aci.identity_card
        inner join (select identity_card, max(create_time) as maxTime from person_vehicle_protocol where vehicle_id = #{vehicleId} group by identity_card) c on c.maxTime = pvp.create_time and
        c.identity_card = pvp.identity_card
        where pvp.vehicle_id = #{vehicleId}
        and (pvp.resource = 1
        or (pvp.resource = 2 and pvp.file_url != ''))
        and pvp.state = 0
        and pvp.audit_state in (0,1)
        <if test="signType != null">and (pvp.sign_type = #{signType} or pvp.sign_type = 0)</if>
        group by pvp.id
    </select>
    <select id="selectByIdcardsAndForwarderId" resultMap="FreightForwarderCapacityRelationResult">
        <include refid="selectFreightForwarderCapacityRelationVo"/>
        where identity_card in
        <foreach item="idcard" collection="idcards" open="(" separator="," close=")">
            #{idcard}
        </foreach>
        and freight_forwarder_id = #{freightForwarderId}
        and agreement_state = 1
    </select>

    <insert id="insertFreightForwarderCapacityRelations">
        insert ignore into freight_forwarder_capacity_relation(id,freight_forwarder_id, identity_card, agreement_state,
        file_url, sign_time, create_time,sign_type)
        values
        <foreach item="item" index="index" collection="list" open="" close="" separator=",">
            (#{item.id}, #{item.freightForwarderId}, #{item.identityCard}, #{item.agreementState}, #{item.fileUrl},
            #{item.signType}, #{item.signTime}, #{item.createTime})
        </foreach>
    </insert>

    <select id="queryByState" resultMap="FreightForwarderCapacityRelationResult">
        select *
        from freight_forwarder_capacity_relation
        where state = #{state}
    </select>

    <select id="selectEffectiveContract" resultMap="FreightForwarderCapacityRelationResult">
        select *
        from freight_forwarder_capacity_relation
        where agreement_state = 1
          and state in (1, 2, 3)
          and freight_forwarder_id = #{freightForwarderId}
          and identity_card = #{identityCard} limit 1
    </select>
    <select id="selectEffectiveProxyContractByResource" resultMap="FreightForwarderCapacityRelationResult">
        select *
        from freight_forwarder_capacity_relation
        where agreement_state = 1
        and state in (1, 2, 3)
        and freight_forwarder_id = #{freightForwarderId}
        and identity_card = #{identityCard}
        <if test="resource != null">and resource = #{resource}</if>
        limit 1
    </select>

    <update id="updateTake">
        update freight_forwarder_capacity_relation set state = 1
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateWillExpire">
        update freight_forwarder_capacity_relation set state = 2
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateExpire">
        update freight_forwarder_capacity_relation set state = -1 and agreement_state = 2
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="entrustedProtocolList" resultType="EntrustedProtocolRes">
        select a.id as id,
        c.name as freightForwarderName,
        b.auxiliary_staff_name as auxiliaryStaffName,
        a.identity_card as auxiliaryStaffIdentityCard,
        a.create_time as uploadTime,
        f.contact_phone as auxiliaryStaffPhone
        from freight_forwarder_capacity_relation a
        inner join auxiliary_staff_info b on a.identity_card = b.identity_card
        inner join freight_forwarder_info c on a.freight_forwarder_id = c.id
        inner join freight_forwarder_auxiliary_relation d on a.freight_forwarder_id = d.freight_forwarder_id and d.auxiliary_id = b.id
        inner join freight_forwarder_config e on a.freight_forwarder_id = e.freight_forwarder_id
        inner join actual_carrier_info f on f.identity_card = a.identity_card
        where audit_state = 0
        and a.sign_type = 2
        and e.is_audit_file = 0
        and a.freight_forwarder_id = #{freightForwarderId}
        <if test="auxiliaryId != null">
            and b.id = #{auxiliaryId}
        </if>
        order by a.create_time
    </select>

    <select id="entrustedProtocolCount" resultType="integer">
        select count(distinct a.id)
        from freight_forwarder_capacity_relation a
                 inner join auxiliary_staff_info b on a.identity_card = b.identity_card
                 inner join freight_forwarder_info c on a.freight_forwarder_id = c.id
                 inner join freight_forwarder_auxiliary_relation d on a.freight_forwarder_id = d.freight_forwarder_id and d.auxiliary_id = b.id
                 inner join freight_forwarder_config g on a.freight_forwarder_id = g.freight_forwarder_id
        where audit_state = 0
          and g.is_audit_file = 0
          and a.sign_type = 2
          and a.freight_forwarder_id = #{freightForwarderId}
    </select>

    <update id="auditCapacityRelation">
        update freight_forwarder_capacity_relation
        set audit_state = 2
        where freight_forwarder_id = #{freightForwarderId}
          and audit_state = 0
    </update>

    <select id="auditIdentityCards" resultType="string">
        select identity_card
        from freight_forwarder_capacity_relation
        where freight_forwarder_id = #{freightForwarderId}
          and audit_state = 0
    </select>
</mapper>
