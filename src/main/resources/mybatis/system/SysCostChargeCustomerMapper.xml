<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.system.mapper.SysCostChargeCustomerMapper">
    <resultMap type="SysCostChargeCustomer" id="SysCostChargeCustomerResult">
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="creditCode"    column="credit_code"    />
        <result property="state"    column="state"    />
        <result property="isVip"    column="is_vip"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysCostChargeCustomerVo">
        select customer_id, customer_name, credit_code, state, is_vip, update_time from sys_cost_charge_customer
    </sql>

    <select id="selectSysCostChargeCustomerList" resultMap="SysCostChargeCustomerResult">
        <include refid="selectSysCostChargeCustomerVo"/>
    </select>

</mapper>