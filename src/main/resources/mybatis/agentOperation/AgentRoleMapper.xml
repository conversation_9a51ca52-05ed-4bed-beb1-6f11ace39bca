<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.agentOperation.mapper.AgentRoleMapper">

    <resultMap type="AgentRole" id="AgentRoleResult">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="menuCheckStrictly" column="menu_check_strictly"/>
        <result property="deptCheckStrictly" column="dept_check_strictly"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="freightForwarderId" column="freight_forwarder_id"/>
        <result property="presetRoleId" column="preset_role_id"/>
    </resultMap>


    <sql id="selectRoleVo">
        SELECT DISTINCT r.role_id,
                        r.role_name,
                        r.role_key,
                        r.role_sort,
                        r.data_scope,
                        r.menu_check_strictly,
                        r.dept_check_strictly,
                        r.status,
                        r.del_flag,
                        r.create_by,
                        r.create_time,
                        r.update_by,
                        r.update_time,
                        r.remark,
                        r.freight_forwarder_id,
                        r.preset_role_id
        FROM agent_role r
                 LEFT JOIN agent_user_role ur ON ur.role_id = r.role_id
                 LEFT JOIN agent_user u ON u.user_id = ur.user_id
    </sql>

    <select id="selectRoleList" parameterType="AgentRole" resultMap="AgentRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0'
        <if test="freightForwarderId != null and freightForwarderId != 0">
            AND r.freight_forwarder_id = #{freightForwarderId}
        </if>
        <if test="roleId != null and roleId != 0">
            AND r.role_id = #{roleId}
        </if>
        <if test="roleName != null and roleName != ''">
            AND r.role_name LIKE concat('%', #{roleName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND r.status = #{status}
        </if>
        <if test="roleKey != null and roleKey != ''">
            AND r.role_key LIKE concat('%', #{roleKey}, '%')
        </if>
        <if test="params != null and params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(r.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params != null and params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(r.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="presetRoleId != null and presetRoleId != 0">
            AND r.preset_role_id = #{presetRoleId}
        </if>
        ORDER BY r.role_sort
    </select>

    <select id="selectRolePermissionByUserId" resultMap="AgentRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0' AND ur.user_id = #{userId} AND u.freight_forwarder_id = #{freightForwarderId}
    </select>

    <select id="selectRoleAll" resultMap="AgentRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0' AND r.freight_forwarder_id = #{freightForwarderId}
    </select>

    <select id="selectRoleIdsByUserId" parameterType="Long" resultType="Long">
        SELECT r.role_id
        FROM agent_role r
                 LEFT JOIN agent_user_role ur ON ur.role_id = r.role_id
                 LEFT JOIN agent_user u ON u.user_id = ur.user_id
        WHERE u.user_id = #{userId}
    </select>

    <select id="selectRoleById" parameterType="Long" resultMap="AgentRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.role_id = #{roleId}
    </select>

    <select id="selectRolesByUserName" parameterType="String" resultMap="AgentRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0' AND u.user_name = #{userName}
    </select>

    <select id="checkRoleNameUnique" parameterType="AgentRole" resultMap="AgentRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.role_name=#{roleName}
        AND r.del_flag = '0'
        <if test="freightForwarderId != null and freightForwarderId != 0">
            AND r.freight_forwarder_id = #{freightForwarderId}
        </if>
        LIMIT 1
    </select>

    <select id="checkRoleKeyUnique" parameterType="AgentRole" resultMap="AgentRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.role_key=#{roleKey}
        AND r.del_flag = '0'
        <if test="freightForwarderId != null and freightForwarderId != 0">
            AND r.freight_forwarder_id = #{freightForwarderId}
        </if>
        LIMIT 1
    </select>

    <insert id="insertRole" parameterType="AgentRole" useGeneratedKeys="true" keyProperty="roleId">
        INSERT INTO agent_role(
        <if test="roleId != null and roleId != 0">role_id,</if>
        <if test="roleName != null and roleName != ''">role_name,</if>
        <if test="roleKey != null and roleKey != ''">role_key,</if>
        <if test="roleSort != null and roleSort != ''">role_sort,</if>
        <if test="dataScope != null and dataScope != ''">data_scope,</if>
        <if test="menuCheckStrictly != null">menu_check_strictly,</if>
        <if test="deptCheckStrictly != null">dept_check_strictly,</if>
        <if test="status != null and status != ''">status,</if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="freightForwarderId != null and freightForwarderId != ''">freight_forwarder_id,</if>
        <if test="presetRoleId != null and presetRoleId != 0">preset_role_id,</if>
        create_time
        ) VALUES (
        <if test="roleId != null and roleId != 0">#{roleId},</if>
        <if test="roleName != null and roleName != ''">#{roleName},</if>
        <if test="roleKey != null and roleKey != ''">#{roleKey},</if>
        <if test="roleSort != null and roleSort != ''">#{roleSort},</if>
        <if test="dataScope != null and dataScope != ''">#{dataScope},</if>
        <if test="menuCheckStrictly != null">#{menuCheckStrictly},</if>
        <if test="deptCheckStrictly != null">#{deptCheckStrictly},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="freightForwarderId != null and freightForwarderId != ''">#{freightForwarderId},</if>
        <if test="presetRoleId != null and presetRoleId != 0">#{presetRoleId},</if>
        sysdate()
        )
    </insert>

    <update id="updateRole" parameterType="AgentRole">
        UPDATE agent_role
        <set>
            <if test="roleName != null and roleName != ''">role_name = #{roleName},</if>
            <if test="roleKey != null and roleKey != ''">role_key = #{roleKey},</if>
            <if test="roleSort != null and roleSort != ''">role_sort = #{roleSort},</if>
            <if test="dataScope != null and dataScope != ''">data_scope = #{dataScope},</if>
            <if test="menuCheckStrictly != null">menu_check_strictly = #{menuCheckStrictly},</if>
            <if test="deptCheckStrictly != null">dept_check_strictly = #{deptCheckStrictly},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="freightForwarderId != null and freightForwarderId != ''">freight_forwarder_id =
                #{freightForwarderId},
            </if>
            <if test="presetRoleId != null and presetRoleId != ''">preset_role_id =
                #{presetRoleId},
            </if>
            update_time = sysdate()
        </set>
        WHERE role_id = #{roleId}
    </update>

    <delete id="deleteRoleById" parameterType="Long">
        UPDATE agent_role
        SET del_flag = '2'
        WHERE role_id = #{roleId}
    </delete>

    <delete id="deleteRoleByIds" parameterType="Long">
        UPDATE agent_role
        SET del_flag = '2'
        WHERE role_id IN
        <foreach collection="array" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

    <select id="selectUserRoleList" resultMap="AgentRoleResult">
        SELECT r.role_id,
               r.role_name,
               r.role_key
        FROM agent_user_role ur
                 LEFT JOIN agent_role r ON ur.role_id = r.role_id
        WHERE r.status = 0
          AND r.del_flag = '0'
          AND ur.user_id = #{userId}
    </select>

    <select id="selectUsersRoleList" resultType="AgentUserRoleInfo">
        SELECT r.role_id AS roleId,
        r.role_name AS roleName,
        r.role_key AS roleKey,
        ur.user_id AS userId
        FROM agent_user_role ur
        LEFT JOIN agent_role r ON ur.role_id = r.role_id
        WHERE r.status = 0
        AND r.del_flag = '0'
        AND ur.user_id IN
        <foreach collection="userIds" item="userId" separator="," open="(" close=")">
            #{userId}
        </foreach>
    </select>
    <select id="selectRoleByPresetId" resultMap="AgentRoleResult">
        SELECT r.role_id,
                        r.role_name,
                        r.role_key,
                        r.role_sort,
                        r.data_scope,
                        r.menu_check_strictly,
                        r.dept_check_strictly,
                        r.status,
                        r.del_flag,
                        r.create_by,
                        r.create_time,
                        r.update_by,
                        r.update_time,
                        r.remark,
                        r.freight_forwarder_id
        FROM agent_role r where r.preset_role_id=#{presetRoleId}
    </select>
    <select id="selectRoleListByAgentRoleReq" resultMap="AgentRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0'
        <if test="freightForwarderId != null and freightForwarderId != 0">
            AND r.freight_forwarder_id = #{freightForwarderId}
        </if>
        <if test="hasAdmin != null and hasAdmin == 1">
            AND r.role_name &lt;&gt; "管理员"
        </if>
        <if test="roleName != null and roleName != ''">
            AND r.role_name like concat('%', #{roleName}, '%')
        </if>
        ORDER BY r.role_sort
    </select>
</mapper>
