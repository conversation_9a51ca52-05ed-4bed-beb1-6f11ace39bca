<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.agentOperation.mapper.AgentUserMapper">

    <resultMap type="AgentUser" id="AgentUserResult">
        <id property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="password" column="password"/>
        <result property="pwd" column="pwd"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="freightForwarderId" column="freight_forwarder_id"/>
        <result property="customerId" column="customer_id"/>
        <result property="defaultProject" column="default_project"/>
    </resultMap>

    <resultMap id="RoleResult" type="AgentRole">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="status" column="role_status"/>
    </resultMap>

    <sql id="selectUserVo">
        SELECT u.user_id,
               u.user_name,
               u.nick_name,
               u.email,
               u.avatar,
               u.phonenumber,
               u.password,
               u.pwd,
               u.sex,
               u.status,
               u.del_flag,
               u.login_ip,
               u.login_date,
               u.create_by,
               u.create_time,
               u.remark,
               u.freight_forwarder_id,
               u.customer_id,
               u.default_project
        FROM agent_user u
    </sql>

    <select id="selectUserList" parameterType="AgentUser" resultMap="AgentUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0'
        <if test="freightForwarderId != null and freightForwarderId != 0">
            AND u.freight_forwarder_id = #{freightForwarderId}
        </if>
        <if test="customerId != null and customerId != 0">
            AND u.customer_id = #{customerId}
        </if>
        <if test="userId != null and userId != 0">
            AND u.user_id = #{userId}
        </if>
        <if test="nickName != null and nickName != ''">
            AND u.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND u.status = #{status}
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <if test="params != null and params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params != null and params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
    </select>

    <select id="selectUserByUserName" parameterType="String" resultMap="AgentUserResult">
        <include refid="selectUserVo"/>
        where u.user_name = #{userName} and u.del_flag = '0' LIMIT 1
    </select>

    <select id="selectUserById" parameterType="Long" resultMap="AgentUserResult">
        <include refid="selectUserVo"/>
        where u.user_id = #{userId}
    </select>

    <select id="checkUserNameUnique" parameterType="String" resultType="int">
        SELECT COUNT(1)
        FROM agent_user
        WHERE user_name = #{userName}
          AND del_flag = '0'
        LIMIT 1
    </select>

    <select id="checkPhoneUnique" parameterType="String" resultMap="AgentUserResult">
        SELECT user_id, phonenumber
        FROM agent_user
        WHERE phonenumber = #{phonenumber}
          AND del_flag = '0'
        LIMIT 1
    </select>

    <select id="checkEmailUnique" parameterType="String" resultMap="AgentUserResult">
        SELECT user_id, email
        FROM agent_user
        WHERE email = #{email}
          AND del_flag = '0'
        LIMIT 1
    </select>
    <select id="selectHasAllProjectsUserByIds" resultType="java.lang.Long">
        select  user_id from agent_user
        where default_project = 1 and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>
    <select id="selectAdminUsersByFreightForwarderId" resultMap="AgentUserResult">
        <include refid="selectUserVo"/>
        left join agent_user_role ur on u.user_id = ur.user_id
        left join agent_role r on r.role_id = ur.role_id
        where u.del_flag = '0'
        <if test="freightForwarderId != null and freightForwarderId != 0">
            and u.freight_forwarder_id = #{freightForwarderId}
        </if>
        and r.role_key = "admin"
        order by u.create_time desc
    </select>

    <insert id="insertUser" parameterType="AgentUser" useGeneratedKeys="true" keyProperty="userId">
        insert into agent_user (
        <if test="userId != null and userId != 0">user_id,</if>
        <if test="userName != null and userName != ''">user_name,</if>
        <if test="nickName != null and nickName != ''">nick_name,</if>
        <if test="email != null and email != ''">email,</if>
        <if test="avatar != null and avatar != ''">avatar,</if>
        <if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
        <if test="sex != null and sex != ''">sex,</if>
        <if test="password != null and password != ''">password,</if>
        <if test="pwd != null and pwd != ''">pwd,</if>
        <if test="status != null and status != ''">status,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="freightForwarderId != null and freightForwarderId != ''">freight_forwarder_id,</if>
        <if test="customerId != null and customerId != ''">customer_id,</if>
        <if test="defaultProject != null ">default_project,</if>
        create_time
        ) values (
        <if test="userId != null and userId != ''">#{userId},</if>
        <if test="userName != null and userName != ''">#{userName},</if>
        <if test="nickName != null and nickName != ''">#{nickName},</if>
        <if test="email != null and email != ''">#{email},</if>
        <if test="avatar != null and avatar != ''">#{avatar},</if>
        <if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
        <if test="sex != null and sex != ''">#{sex},</if>
        <if test="password != null and password != ''">#{password},</if>
        <if test="pwd != null and pwd != ''">#{pwd},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="freightForwarderId != null and freightForwarderId != ''">#{freightForwarderId},</if>
        <if test="customerId != null and customerId != ''">#{customerId},</if>
        <if test="defaultProject != null">#{defaultProject},</if>
        sysdate()
        )
    </insert>

    <update id="updateUser" parameterType="AgentUser">
        update agent_user
        <set>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="email != null ">email = #{email},</if>
            <if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
            <if test="sex != null and sex != ''">sex = #{sex},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="pwd != null and pwd != ''">pwd = #{pwd},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="freightForwarderId != null">freight_forwarder_id = #{freightForwarderId},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="defaultProject != null">default_project = #{defaultProject},</if>
            update_time = sysdate()
        </set>
        where user_id = #{userId}
    </update>

    <update id="updateUserStatus" parameterType="AgentUser">
        UPDATE agent_user
        SET status = #{status}
        WHERE user_id = #{userId}
    </update>

    <update id="updateUserAvatar" parameterType="AgentUser">
        UPDATE agent_user
        SET avatar = #{avatar}
        WHERE user_name = #{userName}
    </update>

    <delete id="deleteUserById" parameterType="Long">
        UPDATE agent_user
        SET del_flag = '2'
        WHERE user_id = #{userId}
    </delete>

    <delete id="deleteUserByIds" parameterType="Long">
        update agent_user set del_flag = '2' where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <update id="resetUserPwd" parameterType="TenantUser">
        update agent_user set password = #{password},pwd = #{pwd} where user_name = #{userName}
    </update>
    <update id="updateAgentUserStatusByUserIds">
        update agent_user set status = 0 where user_id in
        <foreach collection="shipAgentUserIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>

    <select id="queryUser" resultType="clickLoginRes">
        SELECT
        au.user_id AS userId,
        au.user_name AS userName,
        au.nick_name AS nickName,
        au.phonenumber AS phone,
        au.status AS state,
        au.login_date AS date,
        ar.role_id AS roleId,
        ar.role_name AS roleName
        FROM
        agent_user au
        JOIN
        agent_user_role aur ON au.user_id = aur.user_id
        JOIN
        agent_role ar ON aur.role_id = ar.role_id
        WHERE
        au.status = '0' AND
        au.del_flag = '0'
        <if test="customerId != null">AND au.freight_forwarder_id = #{customerId}</if>
        <if test="userName != null and userName != ''">AND au.user_name LIKE '%${userName}%'</if>
        <if test="nickName != null and nickName != ''">AND au.nick_name LIKE '%${nickName}%'</if>
        ORDER BY
        CASE
        WHEN ar.role_name = '管理员' THEN 0
        ELSE 1
        END, ar.role_name
    </select>
    <select id="getEnterpriseInfo" resultType="com.zly.project.tenant.domain.res.EnterpriseInfoRes">
        select ffi.id customerId,ffi.name customerName
        from agent_user au
                 left join freight_forwarder_info ffi on au.freight_forwarder_id = ffi.id
        where au.phonenumber = #{phone}
          and au.status = 0 and ffi.state = 0 and au.del_flag = 0 order by login_date desc
    </select>
    <select id="selectAgentUserList" resultType="com.zly.project.agentOperation.domain.AgentUser">
        select user_id userId,user_name userName,freight_forwarder_id freightForwarderId from agent_user
    </select>
    <select id="selectUserByIds" resultMap="AgentUserResult">
        select  * from agent_user
        where user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>
    <select id="selectAgentUserListByFreightForwarderId" resultMap="AgentUserResult">
        select * from agent_user
        where del_flag = '0' AND freight_forwarder_id = #{freightForwardId}
    </select>
</mapper>
