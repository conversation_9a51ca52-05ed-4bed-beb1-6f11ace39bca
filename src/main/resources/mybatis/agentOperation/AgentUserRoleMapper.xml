<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.agentOperation.mapper.AgentUserRoleMapper">

    <resultMap type="AgentUserRole" id="AgentUserRoleResult">
        <result property="userId" column="user_id"/>
        <result property="roleId" column="role_id"/>
    </resultMap>

    <select id="selectUserRoleIds" resultType="java.lang.Long">
        SELECT role_id
        FROM agent_user_role
        WHERE user_id = #{userId}
    </select>

    <select id="selectRoleRelatedUserIds" resultType="java.lang.Long">
        SELECT user_id
        FROM agent_user_role
        WHERE role_id = #{roleId}
    </select>

    <delete id="deleteUserRoleByUserId" parameterType="Long">
        DELETE
        FROM agent_user_role
        WHERE user_id = #{userId}
    </delete>
    <select id="selectRoleIdsByUserId" resultType="java.lang.Long">
		select role_id
		from agent_user_role
		where user_id = #{userId}
	</select>


    <delete id="deleteUserRoleByRoleId" parameterType="Long">
        DELETE
        FROM agent_user_role
        WHERE role_id = #{roleId}
    </delete>

    <select id="countUserRoleByRoleId" resultType="Integer">
        SELECT COUNT(1)
        FROM agent_user_role
        WHERE role_id = #{roleId}
    </select>
    <select id="selectAdminByUserIds" resultType="java.lang.Long">
        select  aur.user_id from agent_user_role aur
        left join agent_role ar on aur.role_id = ar.role_id
        where aur.user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and ar.role_name = "管理员"
    </select>
    <select id="selectUserRoleByUserIds" resultMap="AgentUserRoleResult">
        select * from agent_user_role where user_id in
        <foreach collection="agentUserIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <delete id="deleteUserRoleByUserIds" parameterType="Long">
        delete from agent_user_role where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <insert id="batchUserRole">
        insert ignore into agent_user_role(user_id, role_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.userId},#{item.roleId})
        </foreach>
    </insert>

    <delete id="deleteUserRoleInfo" parameterType="AgentUserRole">
        DELETE
        FROM agent_user_role
        WHERE user_id = #{userId}
          AND role_id = #{roleId}
    </delete>

    <delete id="deleteUserRoleInfos">
        delete from agent_user_role where role_id=#{roleId} and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

</mapper>
