<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.agentOperation.mapper.AgentRoleMenuMapper">

    <resultMap type="AgentRoleMenu" id="AgentRoleMenuResult">
        <result property="roleId" column="role_id"/>
        <result property="menuId" column="menu_id"/>
    </resultMap>

    <select id="checkMenuExistRole" resultType="Integer">
        SELECT COUNT(1)
        FROM agent_role_menu
        WHERE menu_id = #{menuId}
    </select>

    <delete id="deleteRoleMenuByRoleId" parameterType="Long">
        DELETE
        FROM agent_role_menu
        WHERE role_id = #{roleId}
    </delete>

    <delete id="deleteRoleMenu" parameterType="Long">
        delete from agent_role_menu where role_id in
        <foreach collection="array" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

    <insert id="batchRoleMenu">
        INSERT INTO agent_role_menu(role_id, menu_id) VALUES
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.roleId},#{item.menuId})
        </foreach>
    </insert>

    <select id="selectRoleMenuIds" resultType="java.lang.Long">
        SELECT menu_id
        FROM agent_role_menu
        WHERE role_id = #{roleId}
    </select>

    <select id="selectRoleMenuList" resultMap="com.zly.project.agentOperation.mapper.AgentMenuMapper.AgentMenuResult">
        SELECT m.*
        FROM agent_role_menu rm
        LEFT JOIN agent_menu m ON m.menu_id = rm.menu_id
        WHERE role_id = #{roleId} and parent_id != 0
    </select>

    <select id="selectRoleParentMenuList" resultMap="com.zly.project.agentOperation.mapper.AgentMenuMapper.AgentMenuResult">
        SELECT m.*
        FROM agent_role_menu rm
                 LEFT JOIN agent_menu m ON m.menu_id = rm.menu_id
        WHERE role_id = #{roleId} and parent_id = 0
    </select>

    <select id="selectRoleMenuListAdmin" resultMap="com.zly.project.agentOperation.mapper.AgentMenuMapper.AgentMenuResult">
        SELECT m.*
        FROM agent_role_menu rm
                 LEFT JOIN agent_menu m ON m.menu_id = rm.menu_id
        WHERE parent_id != 0
    </select>

    <select id="selectRoleParentMenuListAdmin" resultMap="com.zly.project.agentOperation.mapper.AgentMenuMapper.AgentMenuResult">
        SELECT m.*
        FROM agent_role_menu rm
                 LEFT JOIN agent_menu m ON m.menu_id = rm.menu_id
        WHERE parent_id = 0
    </select>
    <select id="selectRoleParentMenuByRoleIds"
            resultMap="AgentRoleMenuResult">
        select * from agent_role_menu where role_id in
        <foreach collection="agentRoleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </select>

</mapper>
