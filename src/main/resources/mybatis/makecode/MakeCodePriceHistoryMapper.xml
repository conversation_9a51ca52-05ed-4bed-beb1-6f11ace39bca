<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.makecode.mapper.MakeCodePriceHistoryMapper">
    
    <resultMap type="MakeCodePriceHistory" id="MakeCodePriceHistoryResult">
        <result property="id"    column="id"    />
        <result property="makeCodeId"    column="make_code_id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="oldWaybillPrice"    column="old_waybill_price"    />
        <result property="waybillPrice"    column="waybill_price"    />
        <result property="waybillType"    column="waybill_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectMakeCodePriceHistoryVo">
        select id, make_code_id, customer_id, customer_name, old_waybill_price, waybill_price, waybill_type, create_by, create_time from make_code_price_history
    </sql>

    <select id="selectMakeCodePriceHistoryList" parameterType="MakeCodePriceHistory" resultMap="MakeCodePriceHistoryResult">
        <include refid="selectMakeCodePriceHistoryVo"/>
        <where>  
            <if test="makeCodeId != null "> and make_code_id = #{makeCodeId}</if>
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="oldWaybillPrice != null "> and old_waybill_price = #{oldWaybillPrice}</if>
            <if test="waybillPrice != null "> and waybill_price = #{waybillPrice}</if>
            <if test="waybillType != null "> and waybill_type = #{waybillType}</if>
        </where>
    </select>
    
    <select id="selectMakeCodePriceHistoryById" parameterType="Long" resultMap="MakeCodePriceHistoryResult">
        <include refid="selectMakeCodePriceHistoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMakeCodePriceHistory" parameterType="MakeCodePriceHistory">
        insert into make_code_price_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="makeCodeId != null">make_code_id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="oldWaybillPrice != null">old_waybill_price,</if>
            <if test="waybillPrice != null">waybill_price,</if>
            <if test="waybillType != null">waybill_type,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="makeCodeId != null">#{makeCodeId},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="oldWaybillPrice != null">#{oldWaybillPrice},</if>
            <if test="waybillPrice != null">#{waybillPrice},</if>
            <if test="waybillType != null">#{waybillType},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateMakeCodePriceHistory" parameterType="MakeCodePriceHistory">
        update make_code_price_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="makeCodeId != null">make_code_id = #{makeCodeId},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="oldWaybillPrice != null">old_waybill_price = #{oldWaybillPrice},</if>
            <if test="waybillPrice != null">waybill_price = #{waybillPrice},</if>
            <if test="waybillType != null">waybill_type = #{waybillType},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMakeCodePriceHistoryById" parameterType="Long">
        delete from make_code_price_history where id = #{id}
    </delete>

    <delete id="deleteMakeCodePriceHistoryByIds" parameterType="String">
        delete from make_code_price_history where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>