<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.makecode.mapper.MakeCodeSlaveMapper">
    
    <resultMap type="com.zly.project.makecode.domain.MakeCodeSlave" id="MakeCodeSlaveResult">
        <result property="id"    column="id"    />
        <result property="relationId"    column="relation_id"    />
        <result property="waybillPrice"    column="waybill_price"    />
        <result property="waybillType"    column="waybill_type"    />
        <result property="waybillPaymentType"    column="waybill_payment_type"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="zeroingRule"    column="zeroing_rule"    />
        <result property="payType"    column="pay_type"    />
        <result property="prepayMoney"    column="prepay_money"    />
        <result property="deductMoney"    column="deduct_money"    />
        <result property="state"    column="state"    />
        <result property="effectiveTime"    column="effective_time"    />
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectMakeCodeSlaveVo">
        select id, relation_id, waybill_price, waybill_type, waybill_payment_type, rule_id, zeroing_rule, pay_type, prepay_money, deduct_money, state,effective_time,create_by,
               create_time,
               update_by,
               update_time from make_code_slave
    </sql>

    <select id="selectMakeCodeSlaveList"  resultMap="MakeCodeSlaveResult">
        <include refid="selectMakeCodeSlaveVo"/>
        <where>  
            <if test="relationId != null "> and relation_id = #{relationId}</if>
            <if test="waybillPrice != null "> and waybill_price = #{waybillPrice}</if>
            <if test="waybillType != null "> and waybill_type = #{waybillType}</if>
            <if test="waybillPaymentType != null "> and waybill_payment_type = #{waybillPaymentType}</if>
            <if test="ruleId != null "> and rule_id = #{ruleId}</if>
            <if test="zeroingRule != null "> and zeroing_rule = #{zeroingRule}</if>
            <if test="payType != null "> and pay_type = #{payType}</if>
            <if test="prepayMoney != null "> and prepay_money = #{prepayMoney}</if>
            <if test="deductMoney != null "> and deduct_money = #{deductMoney}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="effectiveTime != null "> and effective_time = #{effectiveTime}</if>
        </where>
    </select>
    
    <select id="selectMakeCodeSlaveById" parameterType="Long" resultMap="MakeCodeSlaveResult">
        <include refid="selectMakeCodeSlaveVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMakeCodeSlave" >
        insert into make_code_slave
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="relationId != null">relation_id,</if>
            <if test="waybillPrice != null">waybill_price,</if>
            <if test="waybillType != null">waybill_type,</if>
            <if test="waybillPaymentType != null">waybill_payment_type,</if>
            <if test="ruleId != null">rule_id,</if>
            <if test="zeroingRule != null">zeroing_rule,</if>
            <if test="payType != null">pay_type,</if>
            <if test="prepayMoney != null">prepay_money,</if>
            <if test="deductMoney != null">deduct_money,</if>
            <if test="state != null">state,</if>
            <if test="effectiveTime != null">effective_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="relationId != null">#{relationId},</if>
            <if test="waybillPrice != null">#{waybillPrice},</if>
            <if test="waybillType != null">#{waybillType},</if>
            <if test="waybillPaymentType != null">#{waybillPaymentType},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="zeroingRule != null">#{zeroingRule},</if>
            <if test="payType != null">#{payType},</if>
            <if test="prepayMoney != null">#{prepayMoney},</if>
            <if test="deductMoney != null">#{deductMoney},</if>
            <if test="state != null">#{state},</if>
            <if test="effectiveTime != null">#{effectiveTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMakeCodeSlave" >
        update make_code_slave
        <trim prefix="SET" suffixOverrides=",">
            <if test="relationId != null">relation_id = #{relationId},</if>
            <if test="waybillPrice != null">waybill_price = #{waybillPrice},</if>
            <if test="waybillType != null">waybill_type = #{waybillType},</if>
            <if test="waybillPaymentType != null">waybill_payment_type = #{waybillPaymentType},</if>
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="zeroingRule != null">zeroing_rule = #{zeroingRule},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="prepayMoney != null">prepay_money = #{prepayMoney},</if>
            <if test="deductMoney != null">deduct_money = #{deductMoney},</if>
            <if test="state != null">state = #{state},</if>
            <if test="effectiveTime != null">effective_time = #{effectiveTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMakeCodeSlaveById" parameterType="Long">
        delete from make_code_slave where id = #{id}
    </delete>

    <delete id="deleteMakeCodeSlaveByIds" parameterType="String">
        delete from make_code_slave where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectMakeCodeSlaveByHour" resultMap="MakeCodeSlaveResult">
        <include refid="selectMakeCodeSlaveVo"/>
        WHERE (
        DATE_FORMAT(effective_time, '%Y-%m-%d %H') = DATE_FORMAT(NOW(), '%Y-%m-%d %H') AND state = 0
        OR
        DATE_FORMAT(effective_time, '%Y-%m-%d %H') = DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 HOUR), '%Y-%m-%d %H') AND state = 0
        )
    </select>
</mapper>