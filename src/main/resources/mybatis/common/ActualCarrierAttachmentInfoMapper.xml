<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.common.mapper.ActualCarrierAttachmentInfoMapper">

    <resultMap type="ActualCarrierAttachmentInfo" id="ActualCarrierAttachmentInfoResult">
        <result property="id" column="id"/>
        <result property="relationId" column="relation_id"/>
        <result property="fileName" column="file_name"/>
        <result property="originalFileName" column="original_file_name"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileType" column="file_type"/>
        <result property="fileUrl" column="file_url"/>
        <result property="keyValue" column="key_value"/>
        <result property="state" column="state"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectActualCarrierAttachmentInfoVo">
        select id,
               relation_id,
               file_name,
               original_file_name,
               file_size,
               file_type,
               file_url,
               key_value,
               state,
               remark,
               create_by,
               create_time,
               update_by,
               update_time
        from actual_carrier_attachment_info
    </sql>

    <select id="selectActualCarrierAttachmentInfoList" parameterType="ActualCarrierAttachmentInfo"
            resultMap="ActualCarrierAttachmentInfoResult">
        <include refid="selectActualCarrierAttachmentInfoVo"/>
        <where>
            <if test="relationId != null ">and relation_id = #{relationId}</if>
            <if test="fileName != null  and fileName != ''">and file_name like concat('%', #{fileName}, '%')</if>
            <if test="originalFileName != null  and originalFileName != ''">and original_file_name like concat('%',
                #{originalFileName}, '%')
            </if>
            <if test="fileSize != null  and fileSize != ''">and file_size = #{fileSize}</if>
            <if test="fileType != null  and fileType != ''">and file_type = #{fileType}</if>
            <if test="fileUrl != null  and fileUrl != ''">and file_url = #{fileUrl}</if>
            <if test="keyValue != null  and keyValue != ''">and key_value = #{keyValue}</if>
            <if test="state != null ">and state = #{state}</if>
        </where>
    </select>

    <select id="selectActualCarrierAttachmentInfoById" parameterType="Long"
            resultMap="ActualCarrierAttachmentInfoResult">
        <include refid="selectActualCarrierAttachmentInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectCarrierAttachmentList" resultMap="ActualCarrierAttachmentInfoResult">
        select id, relation_id, file_name, original_file_name, file_size, file_type, file_url, key_value, state, remark
        from actual_carrier_attachment_info
        <where>
            <if test="relationId != null ">and relation_id = #{relationId}</if>
            <if test="fileName != null  and fileName != ''">
                and file_name in
                <foreach item="item" collection="fileName" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="state != null ">and state = #{state}</if>
        </where>
    </select>
    <select id="selectVehicleFileListByActualCarrierInfoIdAndFileName" resultType="java.lang.Long">
        select relation_id from actual_carrier_attachment_info
        <where>
            <if test="idCardA != null  and idCardA != ''">
                and file_name = #{idCardA}
            </if>
            <if test="basicIntegrityActualCarrierInfoIds != null  and basicIntegrityActualCarrierInfoIds.size() != 0">
                and relation_id in
                <foreach item="item" collection="basicIntegrityActualCarrierInfoIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        </where>
    </select>

    <insert id="insertActualCarrierAttachmentInfo" parameterType="ActualCarrierAttachmentInfo">
        insert into actual_carrier_attachment_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="relationId != null">relation_id,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="originalFileName != null and originalFileName != ''">original_file_name,</if>
            <if test="fileSize != null and fileSize != ''">file_size,</if>
            <if test="fileType != null and fileType != ''">file_type,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="keyValue != null and keyValue != ''">key_value,</if>
            <if test="state != null">state,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="relationId != null">#{relationId},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="originalFileName != null and originalFileName != ''">#{originalFileName},</if>
            <if test="fileSize != null and fileSize != ''">#{fileSize},</if>
            <if test="fileType != null and fileType != ''">#{fileType},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="keyValue != null and keyValue != ''">#{keyValue},</if>
            <if test="state != null">#{state},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>
    <insert id="insertActualCarrierAttachmentInfos">
        insert IGNORE into actual_carrier_attachment_info
        <foreach collection="list" item="item" separator="," index="i">
            <if test="i == 0">
                <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="item.id != null">id,</if>
                    <if test="item.relationId != null">relation_id,</if>
                    <if test="item.fileName != null">file_name,</if>
                    <if test="item.originalFileName != null ">original_file_name,</if>
                    <if test="item.fileSize != null ">file_size,</if>
                    <if test="item.fileType != null ">file_type,</if>
                    <if test="item.fileUrl != null ">file_url,</if>
                    <if test="item.keyValue != null">key_value,</if>
                    <if test="item.state != null">state,</if>
                    <if test="item.remark != null ">remark,</if>
                    <if test="item.createBy != null">create_by,</if>
                    <if test="item.createTime != null">create_time,</if>
                </trim>
            </if>
        </foreach>
        VALUES
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">#{item.id},</if>
                <if test="item.relationId != null">#{item.relationId},</if>
                <if test="item.fileName != null">#{item.fileName},</if>
                <if test="item.originalFileName != null">#{item.originalFileName},</if>
                <if test="item.fileSize != null">#{item.fileSize},</if>
                <if test="item.fileType != null">#{item.fileType},</if>
                <if test="item.fileUrl != null">#{item.fileUrl},</if>
                <if test="item.keyValue != null">#{item.keyValue},</if>
                <if test="item.state != null">#{item.state},</if>
                <if test="item.remark != null">#{item.remark},</if>
                <if test="item.createBy != null">#{item.createBy},</if>
                <if test="item.createTime != null">#{item.createTime},</if>
            </trim>
        </foreach>
    </insert>

    <update id="updateActualCarrierAttachmentInfo" parameterType="ActualCarrierAttachmentInfo">
        update actual_carrier_attachment_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="relationId != null">relation_id = #{relationId},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="originalFileName != null and originalFileName != ''">original_file_name = #{originalFileName},
            </if>
            <if test="fileSize != null and fileSize != ''">file_size = #{fileSize},</if>
            <if test="fileType != null and fileType != ''">file_type = #{fileType},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="keyValue != null and keyValue != ''">key_value = #{keyValue},</if>
            <if test="state != null">state = #{state},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateActualCarrierAttachmentInfoId">
        update actual_carrier_attachment_info
        set relation_id = #{newId}
        where relation_id = #{oldId}
    </update>

    <delete id="deleteActualCarrierAttachmentInfoById" parameterType="Long">
        delete
        from actual_carrier_attachment_info
        where id = #{id}
          and file_name != '人脸识别'
    </delete>

    <delete id="deleteActualCarrierAttachmentInfoByIds" parameterType="String">
        delete from actual_carrier_attachment_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        and file_name != '人脸识别'
    </delete>
    <delete id="deleteActualCarrierAttachmentInfoByCarrierId">
        update actual_carrier_attachment_info
        set state = 1
        where relation_id = #{actualCarrierId}
          and file_name != '人脸识别'
    </delete>

    <select id="queryFileByCarrierIds" resultMap="ActualCarrierAttachmentInfoResult">
        select * from actual_carrier_attachment_info
        where state = 0
        <if test="relationIds != null  and relationIds.size() != 0">
            and relation_id in
            <foreach item="item" collection="relationIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectPayeeInfoPayeeNotAttachmentIssues"
            resultMap="ActualCarrierAttachmentInfoResult">
        select acai.*
        from actual_carrier_attachment_info acai
                 join actual_carrier_info aci on acai.relation_id = aci.id
        where acai.state = 0
          and aci.identity_card = #{identityCard}
          and aci.actual_carrier_name = #{payeeName}
        group by acai.file_name
        order by acai.create_time desc
    </select>
    <select id="selectCarrierAttachmentInfoByIds"
            resultMap="ActualCarrierAttachmentInfoResult">
        <include refid="selectActualCarrierAttachmentInfoVo"/>
        where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="handleFile">
        update actual_carrier_attachment_info
        set state = 1
        where relation_id not in (select id from actual_carrier_info_temp)
    </update>

    <select id="selectIdsByRelationIdOne" resultMap="ActualCarrierAttachmentInfoResult">
        select id,
               relation_id,
               file_name,
               original_file_name,
               file_size,
               file_type,
               file_url,
               key_value,
               state,
               remark
        from actual_carrier_attachment_info
        where relation_id = #{carrierId}
          and state = 0
        group by file_name
    </select>

    <delete id="deleteCarrierIdentityFiles">
        delete from actual_carrier_attachment_info where relation_id = #{carrierId} and
        file_name in
        <foreach item="fileName" collection="fileNames" open="(" separator="," close=")">
            #{fileName}
        </foreach>
    </delete>

    <delete id="logicDeleteCarrierIdentityFiles">
        UPDATE actual_carrier_attachment_info SET state = 1
        where state = 0
        and relation_id in
        <foreach item="carrierId" collection="carrierIds" open="(" separator="," close=")">
            #{carrierId}
        </foreach>
        and file_name in
        <foreach item="fileName" collection="fileNames" open="(" separator="," close=")">
            #{fileName}
        </foreach>
    </delete>

    <select id="queryDriverFileByCarrierIds" resultType="driverAttachmentInfo">
        select id,
        relation_id relationId,
        file_name fileName,
        original_file_name originalFileName,
        file_size fileSize,
        file_type fileType,
        file_url fileUrl,
        key_value keyValue,
        state,
        remark
        from actual_carrier_attachment_info
        where state = 0
        <if test="relationIds != null  and relationIds.size() != 0">
            and relation_id in
            <foreach item="item" collection="relationIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectDriverAttachmentListByIdCard" resultType="com.zly.project.common.domain.DriverAttachmentInfo">
        select acai.id                 as id,
               acai.file_url           as fileUrl,
               acai.file_name          as fileName,
               acai.relation_id        as relationId,
               acai.file_type          as fileType,
               acai.file_size          as fileSize,
               acai.original_file_name as originalFileName,
               acai.key_value          as keyValue,
               acai.create_time        as createTime,
               acai.create_by          as createBy,
               acai.update_time        as updateTime,
               acai.update_by          as updateBy
        from actual_carrier_attachment_info acai
                 join actual_carrier_info aci on aci.id = acai.relation_id
        where acai.state = 0
          and aci.identity_card = #{identityCard}
          and acai.file_name in ('身份证（国徽面）', '身份证（人像面）')
    </select>

    <select id="selectAuxiliaryStaffAttachmentInfoList"
            resultType="com.zly.project.carrier.auxiliary.domain.AuxiliaryStaffAttachmentInfo">
        select acai.id                 as id,
               acai.file_url           as fileUrl,
               acai.file_name          as fileName,
               acai.relation_id        as relationId,
               acai.file_type          as fileType,
               acai.file_size          as fileSize,
               acai.original_file_name as originalFileName,
               acai.key_value          as keyValue,
               acai.create_time        as createTime,
               acai.create_by          as createBy,
               acai.update_time        as updateTime,
               acai.update_by          as updateBy
        from actual_carrier_attachment_info acai
                 join actual_carrier_info aci on aci.id = acai.relation_id
        where acai.state = 0
          AND aci.auxiliary_staff_id = #{auxiliaryStaffId}
          and acai.file_name in ('身份证（国徽面）', '身份证（人像面）')
    </select>
    <select id="selectByCarrierIds" resultMap="ActualCarrierAttachmentInfoResult">
        <include refid="selectActualCarrierAttachmentInfoVo"/>
        where relation_id in
        <foreach item="carrierId" collection="carrierIds" open="(" separator="," close=")">
            #{carrierId}
        </foreach>
    </select>
    <select id="selectJoinCarrierInfoByDriverIds" resultMap="ActualCarrierAttachmentInfoResult">
        select
        attachment.id,
        attachment.file_url,
        attachment.file_name,
        carrier.driver_id as relation_id
        from actual_carrier_attachment_info attachment
        join actual_carrier_info carrier on carrier.id = attachment.relation_id
        where attachment.state = 0
        and attachment.file_name in ('身份证（国徽面）', '身份证（人像面）')
        and carrier.driver_id in
        <foreach item="driverId" collection="driverIds" open="(" separator="," close=")">
            #{driverId}
        </foreach>
    </select>

    <select id="selectJoinCarrierInfoByAuxiliaryIds" resultMap="ActualCarrierAttachmentInfoResult">
        select
        attachment.id,
        attachment.file_url,
        attachment.file_name,
        carrier.auxiliary_staff_id as relation_id
        from actual_carrier_attachment_info attachment
        join actual_carrier_info carrier on carrier.id = attachment.relation_id
        where attachment.state = 0
        and attachment.file_name in ('身份证（国徽面）', '身份证（人像面）')
        and carrier.auxiliary_staff_id in
        <foreach item="auxiliaryStaffId" collection="auxiliaryStaffIds" open="(" separator="," close=")">
            #{auxiliaryStaffId}
        </foreach>

    </select>

    <select id="selectJoinCarrierInfoByCaptainIds" resultMap="ActualCarrierAttachmentInfoResult">
        select
        attachment.id,
        attachment.file_url,
        attachment.file_name,
        carrier.car_captain_id as relation_id
        from actual_carrier_attachment_info attachment
        join actual_carrier_info carrier on carrier.id = attachment.relation_id
        where attachment.state = 0
        and attachment.file_name in ('身份证（国徽面）', '身份证（人像面）')
        and carrier.car_captain_id in
        <foreach item="carCaptainId" collection="carCaptainIds" open="(" separator="," close=")">
            #{carCaptainId}
        </foreach>
    </select>

    <update id="modifyCarrierIdentityFilesState">
        update actual_carrier_attachment_info set state = 1
        where relation_id = #{carrierId} and
        file_name in
        <foreach item="fileName" collection="fileNames" open="(" separator="," close=")">
            #{fileName}
        </foreach>
    </update>

    <update id="deleteCarrierFilesByIds">
        update actual_carrier_attachment_info set state = 1
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteActualCarrierAttachmentInfoByRelationIds">
        delete from actual_carrier_attachment_info where relation_id in
        <foreach item="carrierId" collection="carrierIds" open="(" separator="," close=")">
            #{carrierId}
        </foreach>
    </delete>

    <select id="selectFileNameAndUrlListByRelationId"
            resultMap="ActualCarrierAttachmentInfoResult">
        select file_name,file_url from actual_carrier_attachment_info where relation_id = #{relationId} and state = 0
    </select>
</mapper>
