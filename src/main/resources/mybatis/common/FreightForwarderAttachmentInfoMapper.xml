<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.common.mapper.FreightForwarderAttachmentInfoMapper">

    <resultMap type="FreightForwarderAttachmentInfo" id="FreightForwarderAttachmentInfoResult">
        <result property="id" column="id"/>
        <result property="relationId" column="relation_id"/>
        <result property="fileName" column="file_name"/>
        <result property="originalFileName" column="original_file_name"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileType" column="file_type"/>
        <result property="fileUrl" column="file_url"/>
        <result property="keyValue" column="key_value"/>
        <result property="state" column="state"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectFreightForwarderAttachmentInfoVo">
        select id,
               relation_id,
               file_name,
               original_file_name,
               file_size,
               file_type,
               file_url,
               key_value,
               state,
               remark,
               create_by,
               create_time,
               update_by,
               update_time
        from freight_forwarder_attachment_info
    </sql>

    <select id="selectFreightForwarderAttachmentInfoList" parameterType="FreightForwarderAttachmentInfo"
            resultMap="FreightForwarderAttachmentInfoResult">
        <include refid="selectFreightForwarderAttachmentInfoVo"/>
        <where>
            <if test="relationId != null ">and relation_id = #{relationId}</if>
            <if test="fileName != null  and fileName != ''">and file_name like concat('%', #{fileName}, '%')</if>
            <if test="originalFileName != null  and originalFileName != ''">and original_file_name like concat('%',
                #{originalFileName}, '%')
            </if>
            <if test="fileSize != null  and fileSize != ''">and file_size = #{fileSize}</if>
            <if test="fileType != null  and fileType != ''">and file_type = #{fileType}</if>
            <if test="fileUrl != null  and fileUrl != ''">and file_url = #{fileUrl}</if>
            <if test="keyValue != null  and keyValue != ''">and key_value = #{keyValue}</if>
            <if test="state != null ">and state = #{state}</if>
            <if test="remark != null  and remark != ''">and remark = #{remark}</if>
            <if test="createBy != null  and createBy != ''">and create_by = #{createBy}</if>
            <if test="createTime != null ">and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="updateTime != null ">and update_time = #{updateTime}</if>
        </where>
    </select>

    <select id="selectFreightForwarderAttachmentInfoById" parameterType="Long" resultMap="FreightForwarderAttachmentInfoResult">
        <include refid="selectFreightForwarderAttachmentInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertFreightForwarderAttachmentInfo" parameterType="FreightForwarderAttachmentInfo">
        insert into freight_forwarder_attachment_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="relationId != null">relation_id,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="originalFileName != null and originalFileName != ''">original_file_name,</if>
            <if test="fileSize != null and fileSize != ''">file_size,</if>
            <if test="fileType != null and fileType != ''">file_type,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="keyValue != null and keyValue != ''">key_value,</if>
            <if test="state != null">state,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="relationId != null">#{relationId},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="originalFileName != null and originalFileName != ''">#{originalFileName},</if>
            <if test="fileSize != null and fileSize != ''">#{fileSize},</if>
            <if test="fileType != null and fileType != ''">#{fileType},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="keyValue != null and keyValue != ''">#{keyValue},</if>
            <if test="state != null">#{state},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateFreightForwarderAttachmentInfo" parameterType="FreightForwarderAttachmentInfo">
        update freight_forwarder_attachment_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="relationId != null">relation_id = #{relationId},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="originalFileName != null and originalFileName != ''">original_file_name = #{originalFileName},
            </if>
            <if test="fileSize != null and fileSize != ''">file_size = #{fileSize},</if>
            <if test="fileType != null and fileType != ''">file_type = #{fileType},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="keyValue != null and keyValue != ''">key_value = #{keyValue},</if>
            <if test="state != null">state = #{state},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFreightForwarderAttachmentInfoById" parameterType="Long">
        delete
        from freight_forwarder_attachment_info
        where id = #{id}
    </delete>

    <delete id="deleteFreightForwarderAttachmentInfoByIds" parameterType="String">
        delete from freight_forwarder_attachment_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectFreightForwarderFilesList" resultMap="FreightForwarderAttachmentInfoResult">
        select id, relation_id, file_name, original_file_name, file_size, file_type, file_url, key_value, state, remark
        from freight_forwarder_attachment_info
        <where>
            <if test="relationId != null ">and relation_id = #{relationId}</if>
            <if test="fileName != null  and fileName != ''">
                and file_name in
                <foreach item="item" collection="fileName" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="state != null ">and state = #{state}</if>
        </where>
    </select>

    <select id="selectFileNameAndUrlListByRelationId"
            resultMap="FreightForwarderAttachmentInfoResult">
        select file_name,file_url from freight_forwarder_attachment_info where relation_id = #{relationId} and state = 0
    </select>

</mapper>
