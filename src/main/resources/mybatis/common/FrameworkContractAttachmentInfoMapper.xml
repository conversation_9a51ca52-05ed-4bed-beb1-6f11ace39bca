<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zly.project.common.mapper.FrameworkContractAttachmentInfoMapper">

    <resultMap type="FrameworkContractAttachmentInfo" id="FrameworkContractAttachmentInfoResult">
        <result property="id"    column="id"    />
        <result property="relationId"    column="relation_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="originalFileName"    column="original_file_name"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="keyValue"    column="key_value"    />
        <result property="state"    column="state"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createClient"    column="create_client"    />
        <result property="companyId"    column="company_id"    />
        <result property="isTemp"    column="is_temp"    />
    </resultMap>

    <sql id="selectFrameworkContractAttachmentInfoVo">
        select id, relation_id, file_name, original_file_name, file_size, file_type, file_url, key_value, state
             , remark, create_by, create_time, update_by, update_time, create_client, company_id, is_temp from framework_contract_attachment_info
    </sql>

    <select id="selectFrameworkContractAttachmentInfoList" parameterType="FrameworkContractAttachmentInfo" resultMap="FrameworkContractAttachmentInfoResult">
        <include refid="selectFrameworkContractAttachmentInfoVo"/>
        <where>
            <if test="relationId != null "> and relation_id = #{relationId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="originalFileName != null  and originalFileName != ''"> and original_file_name like concat('%', #{originalFileName}, '%')</if>
            <if test="fileSize != null  and fileSize != ''"> and file_size = #{fileSize}</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="keyValue != null  and keyValue != ''"> and key_value = #{keyValue}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="createClient != null ">and create_client = #{createClient}</if>
            <if test="companyId != null ">and company_id = #{companyId}</if>
            <if test="isTemp != null ">and is_temp = #{isTemp}</if>
        </where>
    </select>

    <select id="selectFrameworkContractAttachmentInfoById" parameterType="Long"
            resultMap="FrameworkContractAttachmentInfoResult">
        <include refid="selectFrameworkContractAttachmentInfoVo"/>
        where id = #{id}
    </select>
    <select id="selectByRelationIds" resultMap="FrameworkContractAttachmentInfoResult">
        <include refid="selectFrameworkContractAttachmentInfoVo"/>
        where relation_id in
        <foreach item="contractId" collection="contractIds" open="(" separator="," close=")">
            #{contractId}
        </foreach>
    </select>

    <insert id="insertFrameworkContractAttachmentInfo" parameterType="FrameworkContractAttachmentInfo">
        insert into framework_contract_attachment_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="relationId != null">relation_id,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="originalFileName != null and originalFileName != ''">original_file_name,</if>
            <if test="fileSize != null and fileSize != ''">file_size,</if>
            <if test="fileType != null and fileType != ''">file_type,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="keyValue != null and keyValue != ''">key_value,</if>
            <if test="state != null">state,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createClient != null">create_client,</if>
            <if test="companyId != null">company_id,</if>
            <if test="isTemp != null">is_temp,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="relationId != null">#{relationId},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="originalFileName != null and originalFileName != ''">#{originalFileName},</if>
            <if test="fileSize != null and fileSize != ''">#{fileSize},</if>
            <if test="fileType != null and fileType != ''">#{fileType},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="keyValue != null and keyValue != ''">#{keyValue},</if>
            <if test="state != null">#{state},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createClient != null">#{createClient},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="isTemp != null">#{isTemp},</if>
        </trim>
    </insert>
    <insert id="insertFrameworkContractAttachmentInfos">
        insert IGNORE into framework_contract_attachment_info
        (id,
        relation_id,
        file_name,
        original_file_name,
        file_size,
        file_type,
        file_url,
        key_value,
        state,
        remark,
        create_by,
        create_time,
        update_by,
        update_time,
        create_client,
        company_id,
        is_temp
        )
        VALUES
        <foreach collection="list" item="item" open="(" close=")" separator="),(">
            #{item.id},
            #{item.relationId},
            #{item.fileName},
            #{item.originalFileName},
            #{item.fileSize},
            #{item.fileType},
            #{item.fileUrl},
            #{item.keyValue},
            #{item.state},
            #{item.remark},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime},
            #{item.createClient},
            #{item.companyId},
            #{item.isTemp}
        </foreach>
    </insert>

    <update id="updateFrameworkContractAttachmentInfo" parameterType="FrameworkContractAttachmentInfo">
        update framework_contract_attachment_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="relationId != null">relation_id = #{relationId},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="originalFileName != null and originalFileName != ''">original_file_name = #{originalFileName},
            </if>
            <if test="fileSize != null and fileSize != ''">file_size = #{fileSize},</if>
            <if test="fileType != null and fileType != ''">file_type = #{fileType},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="keyValue != null and keyValue != ''">key_value = #{keyValue},</if>
            <if test="state != null">state = #{state},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createClient != null">create_client = #{createClient},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="isTemp != null">is_temp = #{isTemp},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFrameworkContractAttachmentInfoById" parameterType="Long">
        delete from framework_contract_attachment_info where id = #{id}
    </delete>

    <delete id="deleteFrameworkContractAttachmentInfoByIds" parameterType="String">
        delete from framework_contract_attachment_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteFrameworkContractAttachmentInfoByRelationId">
        delete from framework_contract_attachment_info where relation_id = #{relationId}
    </delete>

    <select id="selectFileNameAndUrlListByRelationId"
            resultMap="FrameworkContractAttachmentInfoResult">
        select file_name,file_url from framework_contract_attachment_info where relation_id = #{relationId} and state = 0
    </select>
</mapper>
